<template>
    <div class="mod-pay__order" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.orderId" :placeholder="$t('order.orderId')" clearable></el-input>
                    <el-input v-model="state.dataForm.userId" :placeholder="$t('order.userId')" clearable></el-input>
                    <ny-select v-model="state.dataForm.status" dict-type="order_status" style="width: 200px;" :placeholder="$t('order.status')"></ny-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                
                <!-- 订单状态 -->
                <template #status="scope">
                    {{ state.getDictLabel("order_status", scope.row.status) }}
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button v-if="scope.row.status === 0" type="primary" text bg
                        @click="payHandle(scope.row.orderId)">支付宝</el-button>
                    <el-button v-if="scope.row.status === 0" type="primary" text bg
                        @click="weChatHandle(scope.row.orderId)">微信扫码</el-button>
                    <el-button v-if="scope.row.status === 0" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
            
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
        <el-dialog v-model="weChatVisible" title="微信扫码支付" :close-on-click-modal="false" :close-on-press-escape="false"
            :width="230">
            <qrcode-vue :value="codeUrl" :size="200" level="H" />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./order-add-or-update.vue";
import app from "@/constants/app";
import QrcodeVue from 'qrcode.vue'
import baseService from "@/service/baseService";

const view = reactive({
    getDataListURL: "/pay/order/page",
    getDataListIsPage: true,
    deleteURL: "/pay/order",
    deleteIsBatch: true,
    dataForm: {
        orderId: "",
        status: "",
        userId: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "orderId",
        label: "订单ID",
        minWidth: 150
    },
    {
        prop: "productName",
        label: "产品名称",
        minWidth: 120
    },
    {
        prop: "payAmount",
        label: "支付金额",
        minWidth: 120
    },
    {
        prop: "status",
        label: "订单状态",
        minWidth: 120
    },
    {
        prop: "payAt",
        label: "支付时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "createDate",
        label: "下单时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 240
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.orderId = "";
    view.dataForm.status = "";
    view.dataForm.userId = "";
    state.getDataList();
}

const payHandle = (orderId: string) => {
    window.open(`${app.api}/pay/alipay/webPay?orderId=` + orderId);
};

const weChatVisible = ref(false)
const codeUrl = ref('')
const weChatHandle = (orderId: string) => {
    baseService.get(`/pay/wechat/nativePay?orderId=${orderId}`).then(res => {
        codeUrl.value = res.data
        weChatVisible.value = true
    })
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = () => {
    addOrUpdateRef.value.init();
};
</script>
