// v-copy 指令
// 复制某个值至剪贴板
import type { Directive, DirectiveBinding } from "vue";
import useClipboard from 'vue-clipboard3';
const { toClipboard } = useClipboard();
import { ElMessage } from "element-plus";
interface ElType extends HTMLElement {
  copyData: string | number;
}
const copy: Directive = {
  mounted(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
    el.addEventListener("click", handleClick);
  },
  updated(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
  },
  beforeUnmount(el: ElType) {
    el.removeEventListener("click", handleClick);
  }
};

async function handleClick(this: any) {
  try {
      await toClipboard(this.copyData)
      ElMessage.success('复制成功')
  } catch (e:any) {
      ElMessage.warning('复制操作不被支持或失败：', e)
  }
}

export default copy;
