<template>
  <div class="mod-shop__tbpartnerattributemapping">
    <div class="accountTabsConpy">
      <ul class="accountTabsUL" :style="{ height: stateTab.foldTabs ? 'auto' : '49px' }">
        <li :class="state.dataForm.gameCode == item.code ? 'codeActive' : ''" @click="handleNameClick(item)" v-for="(item, index) in stateTab.allGameList" :key="index">
          <span>{{ item.name }}</span>
        </li>
      </ul>
      <div class="icons" v-if="stateTab.moreTabs" @click="stateTab.foldTabs = !stateTab.foldTabs">
        <el-icon v-show="!stateTab.foldTabs"><ArrowDown /></el-icon>
        <el-icon v-show="stateTab.foldTabs"><ArrowUp /></el-icon>
      </div>
    </div>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card" style="margin-bottom: 15px">
      <ny-form-slot>
        <template v-slot:content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.attributeName" placeholder="游戏属性" clearable></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:button>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        <el-button v-if="state.hasPermission('shop:tbpartnerattributemapping:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button @click="SynchronizationFn" type="primary">自动同步</el-button>
        <el-button v-if="state.hasPermission('shop:tbpartnerattributemapping:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <!-- <el-table-column prop="id" label="" header-align="center" align="center"></el-table-column> -->
        <el-table-column prop="gameCode" label="主平台游戏ID" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="attributeId" label="主平台属性ID" header-align="center" align="center" width="120"></el-table-column>
        <!-- <el-table-column prop="attributeCode" label="属性code" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="attributeName" label="主平台属性名" header-align="center" align="center"></el-table-column>
        <!-- <el-table-column prop="partnerId" label="合作商id" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="partnerName" label="合作商名称" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="partnerAttributeId" label="平台对应属性ID" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="partnerAttributeName" label="平台对应属性名称" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="partnerSuperiorName" label="平台对应上级属性名" header-align="center" align="center" width="180"></el-table-column>
        <!-- <el-table-column prop="type" label="类型 1自动 2手动" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="partnerSuperiorId" label="平台对应上级属性ID" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="creator" label="创建者ID" header-align="center" align="center" width="200"> </el-table-column>
        <el-table-column prop="updater" label="更新者ID" header-align="center" align="center" width="200"> </el-table-column>
        <!-- <el-table-column prop="field2" label="备用字段2" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
          <template v-slot="scope">
            <el-button v-if="state.hasPermission('shop:tbpartnerattributemapping:update')" type="primary" text bg @click="editHandle(scope.row)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('shop:tbpartnerattributemapping:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>

    <!-- 同步 -->
    <el-dialog v-model="sync.visible" title="同步" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="sync" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
        <el-form-item label="合作商名称" prop="partnerId">
          <el-select v-model="sync.partnerId" placeholder="请选择">
            <el-option v-for="(item, index) in partneruserListArr" :key="index" :label="item.companyName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="游戏名称" prop="gameId">
          <el-select v-model="sync.gameId" placeholder="请选择">
            <el-option v-for="(item, index) in stateTab.allGameList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button @click="sync.visible = false">{{ $t("cancel") }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
      </template>
    </el-dialog>

    <!-- 修改 -->
    <el-dialog v-model="modify.visible" title="修改" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="modify" @keyup.enter="Modifysave()" label-width="160px" style="width: 90%">
        <el-form-item label="本平台属性" prop="partnerId">
          <span>{{ modify.detailsitem.attributeName }}</span>
        </el-form-item>
        <el-form-item label="外部平台属性" prop="gameId">
          <el-select @visible-change="(visible: boolean) => { visibleChange(visible,modify.detailsitem.partnerAttributeName) }" @change="partnerAttributeNameFn(modify.detailsitem)" v-model="modify.detailsitem.partnerAttributeName" placeholder="请选择关联属性" filterable>
            <el-option v-for="(item, index) in externalAttribute" :key="index" :label="item.attribute" :value="item.attribute" />
          </el-select>
        </el-form-item>
        <el-form-item label="外部平台上级属性" prop="partnerId">
          <span>{{ modify.detailsitem.partnerSuperiorName }}</span>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button @click="modify.visible = false">{{ $t("cancel") }}</el-button>
        <el-button type="primary" @click="Modifysave()">{{ $t("confirm") }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onUnmounted, onMounted } from "vue";
import AddOrUpdate from "./tbpartnerattributemapping-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const view = reactive({
  getDataListURL: "/shop/tbpartnerattributemapping/page",
  getDataListIsPage: true,
  exportURL: "/shop/tbpartnerattributemapping/export",
  deleteURL: "/shop/tbpartnerattributemapping",
  deleteIsBatch: true,
  dataForm: {
    attributeName: "",
    gameCode: null,
    gameId: null
  }
});

const stateTab: {
  gameList: any;
  allGameList: any[];
  moreTabs: boolean;
  foldTabs: boolean;
} = reactive({
  gameList: {},
  allGameList: [],
  moreTabs: false,
  foldTabs: false
});

// 同步变量
const sync: {
  visible: boolean;
  partnerId: string;
  gameId: string;
} = reactive({
  visible: false,
  partnerId: "",
  gameId: ""
});
const partneruserListArr = ref([]); // 合作商列表数组

// 修改变量
const modify: {
  visible: boolean;
  detailsitem: any;
} = reactive({
  visible: false,
  detailsitem: ""
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.attributeName = "";
  state.getDataList();
};

onMounted(() => {
  getGameList();
  getPartneruserList();
});

// 获取合作商列表
const getPartneruserList = () => {
  baseService.get("/partner/tbpartneruser/list").then((res) => {
    partneruserListArr.value = res.data;
  });
};

// 获取游戏列表
const getGameList = () => {
  baseService.get("/shop/tbgame/gameList").then((res) => {
    state.dataForm.gameCode = res.data[0].code;
    state.dataForm.gameId = res.data[0].id;
    sync.gameId = res.data[0].id;
    stateTab.allGameList = res.data;
    handleResize();
    window.addEventListener("resize", handleResize); // 监听窗口大小变化
    state.getDataList();
  });
};
// 计算游戏列表大小
const handleResize = () => {
  const sxWidth2: number = (document.querySelector(".accountTabsConpy") as HTMLElement).offsetWidth;
  let allFontLen = 0;
  stateTab.allGameList.forEach((item: any) => {
    allFontLen += Number(item.name.length * 13 + 48);
  });
  if (Number((sxWidth2 / allFontLen).toFixed(2)) < 1) {
    stateTab.moreTabs = true;
    stateTab.foldTabs = false;
  } else {
    stateTab.moreTabs = false;
    stateTab.foldTabs = true;
  }
};
// 顶部切换游戏名称
const handleNameClick = (item: any) => {
  state.dataForm.gameCode = item.code;
  state.dataForm.gameId = item.id;
  sync.gameId = item.id;
  state.getDataList();
};
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 自动同步
const SynchronizationFn = () => {
  sync.visible = true;
};

// 提交同步
const dataFormSubmitHandle = () => {
  baseService.get("/shop/tbpartnerattributemapping/sync", { gameId: sync.gameId, partnerId: sync.partnerId }).then((res) => {
    ElMessage.success({
      message: "操作成功",
      duration: 500,
      onClose: () => {
        sync.visible = false;
      }
    });
  });
};

// 修改
const editHandle = (item: any) => {
  modify.visible = true;
  modify.detailsitem = item;
};

const externalAttribute = ref([]); // 外部平台属性
// 获取外部平台属性
const visibleChange = (visible: boolean, name: string) => {
  console.log(visible);
  let dataForm = modify.detailsitem;
  if (visible) {
    externalAttribute.value = [];
    baseService.get("/shop/tbpartnerattributemapping/search", { partnerId: dataForm.partnerId, gameId: dataForm.gameId, attribute: name }).then((res) => {
      externalAttribute.value = res.data;
    });
  }
};

// 选择关联平台属性
const partnerAttributeNameFn = (data: any) => {
  let dataForm = modify.detailsitem;
  let itemdata: any = externalAttribute.value.filter((item: any) => {
    if (dataForm.partnerAttributeName == item.attribute) {
      return item;
    }
  });
  dataForm.partnerAttributeId = itemdata[0].attributeId;
  dataForm.partnerAttributeName = itemdata[0].attribute;
  dataForm.partnerSuperiorName = data.partnerSuperiorName;
};

// 修改保存
const Modifysave = () => {
  baseService.post("/shop/tbpartnerattributemapping/update", [modify.detailsitem]).then((res) => {
    ElMessage.success({
      message: "操作成功",
      duration: 500,
      onClose: () => {
        modify.visible = false;
      }
    });
  });
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, state.dataForm.gameId, state.dataForm.gameCode);
  });
};
</script>

<style lang="less" scoped>
.accountTabsConpy {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 4px 4px 0px 0px;
  background-color: #fff;
  // margin-bottom: 15px;

  .accountTabsUL {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0px;
    padding-left: 0px;

    li {
      padding: 0 20px 15px;
      margin: 15px 0 0;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      box-sizing: border-box;
      transition: all 0.1s;
    }

    .codeActive {
      color: var(--el-color-primary);
      border-bottom: 2px solid var(--el-color-primary);
    }
  }

  .icons {
    padding: 0 15px;
    cursor: pointer;

    .el-icon {
      font-size: 18px;
      margin-top: 18px;
      color: #606266 !important;
    }
  }
}
</style>
