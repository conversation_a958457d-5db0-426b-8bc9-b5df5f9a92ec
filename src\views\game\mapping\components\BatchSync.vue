<template>
    <div>
        <el-dialog v-model="visible" title="批量同步" width="800" :close-on-click-modal="false" :close-on-press-escape="false">
            <el-form label-position="top">
                <p class="p-title">选择合作商</p>
                <el-form-item label="">
                    <el-checkbox-group v-model="partnersIds">
                        <el-checkbox class="batch-sync-checkbox" v-for="item in partnersList" :key="item.id" :label="item.companyName" :value="item.id"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <p class="p-title">选择游戏</p>
                <el-form-item label="">
                    <el-checkbox-group v-model="gamesIds">
                        <el-checkbox class="batch-sync-checkbox" v-for="item in gamesList" :key="item.gameId" :label="item.gameName" :value="item.gameId"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
            

            <template #footer>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :loading="btnLoading" @click="submit">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
    import { ref, defineExpose, defineEmits } from 'vue'
    import { ElMessage } from 'element-plus';
    import baseService from "@/service/baseService";

    const visible = ref(false)

    const emit = defineEmits(['change'])

    const init = (list: any) => {
        visible.value = true
        gamesList.value = list
        getList()
    }

    // 游戏列表
    const gamesList = ref([])
    // 合作商列表
    const partnersList = ref([])
    const getList = () => {
        baseService.get("/partner/partner/partnerList").then((res) => {
            if (res.code == 0) {
                partnersList.value = res.data || [];
            }
        });
    }

    const partnersIds = ref([])
    const gamesIds = ref([])

    // 提交
    const btnLoading = ref(false)
    const submit = () => {
        if(!partnersIds.value.length){
            return ElMessage.warning('请选择合作商');
        }
        if(!gamesIds.value.length){
            return ElMessage.warning('请选择游戏');
        }
        console.log(partnersIds.value, gamesIds.value)
        // emit('change', partnersIds.value);
        btnLoading.value = true
        baseService.post("/mapping/sysgameinfomapping/batchSync", {
            type: 4,
            partnerIdList: partnersIds.value,
            gameIdList: gamesIds.value
        }).then((res) => {
            ElMessage.success('同步成功');
            visible.value = false;
            emit('change');
        }).finally(() => {
            btnLoading.value = false
        })
    }

    defineExpose({
        init
    })

</script>

<style lang="scss">
.batch-sync-checkbox{
    width: 20%;
}
</style>