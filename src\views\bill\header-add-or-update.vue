<template>
  <el-dialog v-model="visible" width="480" :title="(!dataForm.id ? $t('add') : $t('update')) + '抬头'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-descriptions title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>抬头类型<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="headerType" label="抬头类型" required>
            <el-radio-group v-model="dataForm.headerType">
              <el-radio :value="1">企业单位</el-radio>
              <el-radio :value="2">个人</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>抬头名称<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="headerName" label="抬头名称" required>
            <el-input v-model="dataForm.headerName" placeholder="请输入抬头名称" />
          </el-form-item>
        </el-descriptions-item>
        <template v-if="dataForm.headerType == 1">
          <el-descriptions-item label-class-name="title" :key='1'>
            <template #label>
              <div>税号<span style="color: red">*</span></div>
            </template>
            <el-form-item prop="taxNumber" label="税号" :key='3'>
              <el-input v-model="dataForm.taxNumber" placeholder="请输入税号" />
            </el-form-item> </el-descriptions-item
        ></template>
        <el-descriptions-item label-class-name="title" :key='2'>
          <template #label>
            <div>邮箱<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="email" label="邮箱" :key='4'>
            <el-input v-model="dataForm.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-descriptions-item>
        <template v-if="dataForm.headerType == 1">
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>注册地址</div>
            </template>
            <el-form-item prop="registerAddress" label="注册地址">
              <el-input v-model="dataForm.registerAddress" placeholder="请输入注册地址" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>注册电话</div>
            </template>
            <el-form-item prop="registerPhone" label="注册电话">
              <el-input v-model="dataForm.registerPhone" placeholder="请输入注册电话" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>开户银行</div>
            </template>
            <el-form-item prop="bankName" label="开户银行">
              <el-input v-model="dataForm.bankName" placeholder="请输入开户银行" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>银行账号</div>
            </template>
            <el-form-item prop="bankAccount" label="银行账号">
              <el-input v-model="dataForm.bankAccount" placeholder="请输入银行账号" />
            </el-form-item>
          </el-descriptions-item>
        </template>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>设为默认抬头</div>
          </template>
          <el-form-item prop="isDefault" label="设为默认抬头">
            <el-switch v-model="dataForm.isDefault" :inactive-value="0" :active-value="1" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  headerType: 1,
  headerName: "",
  taxNumber: "",
  email: "",
  registerAddress: "",
  registerPhone: "",
  bankName: "",
  bankAccount: "",
  isDefault: 0
});

const rules = ref({
  headerType: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  headerName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  taxNumber: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  email: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});

const init = (row?: any) => {
  visible.value = true;
  dataForm.id = "";
  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    headerType: 1,
    headerName: "",
    taxNumber: "",
    email: "",
    registerAddress: "",
    registerPhone: "",
    bankName: "",
    bankAccount: "",
    isDefault: 0
  });
  if (row && row.id) {
    Object.assign(dataForm, row);
  }
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let params = { ...dataForm };
    if (params.headerType == 2) {
      // 个人去除企业字段
      params.registerAddress = "";
      params.registerPhone = "";
      params.taxNumber = "";
      params.bankName = "";
      params.bankAccount = "";
    }
    (!dataForm.id ? baseService.post : baseService.put)("/invoice/header", params).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          dataFormRef.value.resetFields();
          emit("refreshDataList");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
