<template>
  <div class="mod-finance__fund-account">
    <el-card shadow="never" class="rr-view-ctx-card cardTop ny_form_card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.zh" placeholder="请输入账号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.zhlx" placeholder="请选择账户类型">
                <el-option label="支付宝" value="支付宝" />
                <el-option label="银行卡" value="银行卡" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.zhyt" placeholder="请选择账户用途">
                <el-option label="备用金账户" value="备用金账户" />
                <el-option label="店铺支付宝" value="店铺支付宝" />
                <el-option label="收款支付宝" value="收款支付宝" />
                <el-option label="店铺银行卡" value="店铺银行卡" />
                <el-option label="公户银行卡" value="公户银行卡" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.hm" placeholder="请输入户名" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" :offset="18">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <!-- 账户类型标签 -->
      <el-tabs style="margin-top: -8px" v-model="state.dataForm.zhlx" class="ny-tabs" @tab-change="state.getDataList()">
        <el-tab-pane label="全部" name=""></el-tab-pane>
        <el-tab-pane label="财务借款" name="财务借款"></el-tab-pane>
        <el-tab-pane label="经营账户" name="经营账户"></el-tab-pane>
        <el-tab-pane label="资金账户" name="资金账户"></el-tab-pane>
      </el-tabs>

      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button type="primary" v-if="state.hasPermission('fiowable:fdfundaccountledger:save')" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <template #operation="scope">
          <el-button type="primary" link size="small" v-if="state.hasPermission('fiowable:fdfundaccountledger:update')" @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" link size="small" v-if="state.hasPermission('fiowable:fdfundaccountledger:delete')"  @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          <el-button type="info" link size="small" @click="viewDetail(scope.row)">详情</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>

import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted, computed } from "vue";
import AddOrUpdate from "./fund-account-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import type { FormInstance } from "element-plus";
import { ElMessageBox, ElMessage } from "element-plus";
import ExcelImport from "./fund-import.vue";
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "hm",
    label: "户名",
    minWidth: 120
  },
  {
    prop: "zhyt",
    label: "账户用途",
    width: 150
  },
  {
    prop: "zh",
    label: "账号",
    width: 180
  },
  {
    prop: "khyh",
    label: "开户银行",
    width: 150
  },
  {
    prop: "zhlx",
    label: "账户类型",
    width: 100
  },
  {
    prop: "zhfl",
    label: "账户分类",
    width: 100
  },
  {
    prop: "yh",
    label: "余额",
    width: 120
  },
  {
    prop: "zjzhid",
    label: "资金账户id",
    width: 100
  },
  {
    prop: "xgsj",
    label: "最后更新时间",
    width: 160
  },
  {
    prop: "bz",
    label: "备注",
    width: 160
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/fiowable/fdfundaccountledger/page",
  getDataListIsPage: true,
  exportURL: "/fiowable/fdfundaccountledger/export",
  deleteURL: "/fiowable/fdfundaccountledger",
  deleteIsBatch: true,
  dataForm: {
    zh: "",
    zhyt: "",
    zhlx: "",
    hm: "",
    zhfl: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();
const addOrUpdateHandle = (id?: number) => {
  console.log("addOrUpdateHandle", id);
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 重置操作
const getResetting = () => {
  createTimeRange.value = undefined;
  state.dataForm.zh = "";
  state.dataForm.hm = "";
  state.dataForm.zhfl = "";
  state.dataForm.zhlx = "";
  state.dataForm.zhyt = "";
  state.getDataList();
};

// 查看详情
const viewDetail = (row: any) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(row, true); // 第二个参数为 true 表示查看模式
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};
// 导出
const exportHandle = () => {
  baseService.get("/fiowable/fdfundaccountledger/export", view.dataForm).then((res) => {
    if (res) {
      ElMessage.success("导出成功");
      fileExport(res, "资金账户台账");
    }
  });
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.cardTop {
  margin-bottom: 20px;
}

.ny-tabs {
  :deep(.el-tabs__header) {
    .el-tabs__item {
      width: 96px;
      padding: 0;
    }
  }
}

.amount-text {
  color: #e6a23c;
  font-weight: 500;
}
</style>




