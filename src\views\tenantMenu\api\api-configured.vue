<template>
  <el-table :data="gameList" border v-loading="dataListLoading">
    <el-table-column align="center" prop="gameName" label="游戏名称" />
    <el-table-column align="center" label="推送加价倍率" min-width="100">
      <template #header>
        推送加价倍率
        <el-tooltip effect="dark" content="倍率超过100为加价,低于100为打折" placement="top-start">
          <el-icon color="#909399" size="16"><InfoFilled /></el-icon>
        </el-tooltip>
      </template>
      <template #default="{ row }">
        <el-input type="number" :controls="false" :min="0" v-model="row.pushFareIncrease" placeholder="请输入" style="width: 100%" @change="inputChange(row)">
          <template #append>%</template>
        </el-input>
      </template>
    </el-table-column>
    <el-table-column align="center" label="是否推送">
      <template #default="{ row }">
        <!-- 是否开通 0 否 1是 默认0 -->
        <el-switch v-model="row.isPush" :active-value="1" :inactive-value="0" inline-prompt active-text="是" inactive-text="否" @click="inputChange(row)" />
      </template>
    </el-table-column>
    <!-- 空状态 -->
    <template #empty>
      <div style="padding: 68px 0">
        <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
      </div>
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { ref, defineExpose, nextTick } from "vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage } from "element-plus";

const currentId = ref(null);

const init = (id: any) => {
  currentId.value = id;
  getGamesList();
};

// 游戏列表
const gameList = ref([]);
const dataListLoading = ref(false);
const getGamesList = async () => {
  dataListLoading.value = true;
  gameList.value = [];
  try {
    let res = await baseService.get("/partner/us/partnerApiPushIncreaseFares", { apiId: currentId.value });
    dataListLoading.value = false;
    if (res.code == 0) {
      gameList.value = res.data || [];
    }
  } catch (error) {
    dataListLoading.value = false;
  }
};

// 修改配置
const inputChange = async (row: any) => {
  await nextTick();
  try {
    await (!row.id ? baseService.post("/partner/syspartnersapidisposition", { ...row, apiId: currentId.value }) : baseService.put("/partner/syspartnersapidisposition", row));
    ElMessage.success("操作成功");
    getGamesList();
  } catch (error) {
    getGamesList();
  }
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.el-dialog__body {
  padding: 30px;
  height: 600px;
}
</style>
