<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div class="router_info">
        <span class="info_name">{{ currentRoute.meta.title }}</span>
        <span class="info_line">|</span>
        <span class="info_blurb">恒星后台管理系统，让账号交易变的更安全</span>
      </div>

      <div>
        <el-button type="success" @click="automatic">自动推送设置</el-button>
        <el-button v-if="pushStaskInfo && pushStaskInfo.totalTaskNum > 0" type="primary" plain @mouseenter="mouseenterChange(1)" @mouseleave="mouseleaveChange(1)">推送中：{{ pushStaskInfo.syncingTaskNum }}/{{ pushStaskInfo.totalTaskNum }}</el-button>
        <pushStask v-if="pushStaskShow" @mouseenterChange="mouseenterChange(2)" @mouseleaveChange="mouseleaveChange(2)" @refresh=""></pushStask>
      </div>
    </div>
    <div class="mainBox">
      <div class="leftPart">
        <div class="title">选择API</div>
        <el-select filterable v-model="state.dataForm.partnerId" @change="handleselectPartner" clearable style="width: 186px; margin-bottom: 10px">
          <el-option v-for="item in curAllPartnerList" :key="item.id" :label="item.companyName" :value="item.id" />
        </el-select>
        <div class="title">选择游戏</div>
        <div class="scrollWrap lower">
          <ul class="menuUl">
            <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
              <el-tooltip effect="dark" :content="item.gameName" placement="top-start">
                <span>{{ item.gameName }}</span>
              </el-tooltip>
            </li>
          </ul>
        </div>
      </div>
      <!--  -->
      <div class="rightPart">
        <ny-flod-tab class="newTabSty" @change="" :list="statusList" v-model="tabsValue" value="value" label="label"> </ny-flod-tab>
        <!-- 商品推送 -->
        <div v-if="tabsValue == 1" class="TableXScrollSty">
          <ny-table
            noDataType="4"
            ref="nyTableRef"
            cellHeight="ch-56"
            Height="590"
            :state="state"
            :columns="columns"
            @sortableChange="sortableChange"
            @pageSizeChange="state.pageSizeChangeHandle"
            @pageCurrentChange="state.pageCurrentChangeHandle"
            @selectionChange="state.dataListSelectionChangeHandle"
          >
            <template #header>
              <div class="flx-justify-between">
                <ny-button-group :list="groupList" v-model="state.dataForm.recordType" @change="handleStatusClick"></ny-button-group>
              </div>
            </template>
            <template #header-right>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <el-form-item>
                  <el-select v-model="state.dataForm.accountSource" @change="(e:any)=> e=='ORIGINAL_OWNER' ? state.dataForm.tenantCode = '' : ''">
                    <el-option label="平台直售" value="ORIGINAL_OWNER"></el-option>
                    <el-option label="合作商" value="PARTNER_SYNCHRONIZATION"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="state.dataForm.accountSource == 'PARTNER_SYNCHRONIZATION'">
                  <el-select v-model="state.dataForm.tenantCode" placeholder="请选择合作商" @change="state.getDataList()" clearable>
                    <el-option :label="item.tenantName" :value="item.id" v-for="(item, index) in tenantList" :key="index" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="state.dataForm.searchParam" placeholder="请输入商品标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input>
                </el-form-item>
                <el-form-item>
                  <el-select  v-model="state.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                    <el-option label="未推送" value="0"></el-option>
                    <el-option label="推送成功" value="1"></el-option>
                    <el-option label="推送失败" value="2"></el-option>
                  </el-select>
                  <!-- v-if="state.dataForm.recordType == 1" -->
                  <!-- <el-select v-else v-model="state.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                    <el-option label="未下架" value="0"></el-option>
                    <el-option label="下架成功" value="1"></el-option>
                    <el-option label="下架失败" value="2"></el-option>
                  </el-select> -->
                </el-form-item>
                <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
              </el-form>
            </template>
            <template #header-custom>
              <div style="margin-bottom: 12px;">
                <el-button :disabled="state.dataListSelections?.length < 1" type="primary" @click="pushSelect(1)" v-if="state.dataForm.recordType == 1">上架推送</el-button>
              <el-button :disabled="state.dataListSelections?.length < 1" type="warning" @click="pushSelect(2)">下架推送</el-button>
              </div>
              
            </template>
            <template #title="{ row }">
              <div class="shoping">
                <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
                <div class="info">
                  <div class="title mle" v-html="row.title"></div>
                  <div class="sle" style="width: 185px; text-align: left">
                    {{ `${row.gameName} / ${row.serverName || "-"}` }}
                  </div>
                </div>
              </div>
            </template>
            <template #status="{ row }">
              <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
              <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
              <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
              <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
              <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
              <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
              <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
              <el-tag type="warning" v-if="row.status == '8'">重新上架</el-tag>
              <el-tag type="warning" v-if="row.status == '9'">重新待上架</el-tag>
              <el-tag type="warning" v-if="row.status == '10'">出售待审核</el-tag>
            </template>
            <template #channelStatus="{ row }">
              <el-tag type="primary" v-if="row.channelStatus == '1'">上架</el-tag>
              <el-tag type="warning" v-else-if="row.channelStatus == '2'">下架</el-tag>
              <span v-else>-</span>
            </template>
            <template #pushStatus="{ row }">
              <el-tag v-if="row.pushStatus == 0" type="warning">未推送</el-tag>
              <el-tag v-if="row.pushStatus == 1" type="primary">推送成功</el-tag>
              <el-tag v-if="row.pushStatus == 2" type="danger">推送失败</el-tag>
            </template>
            <template #showLog="{ row }">
              <el-button @click="handleOpenLog(row)" type="primary" link>查看</el-button>
            </template>
            <template #pushTime="{ row }">
              <span v-if="row.pushTime">{{ formatTimeStamp(row.pushTime) }}</span>
              <span v-else>-</span>
            </template>
            <template #compensation="{ row }">
              {{ ["不可包赔", "可买包赔", "永久包赔"][+row.compensation] }}
            </template>
            <template #footer>
              <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
                <span class="tableSort">
                  <NyDropdownMenu
                    :isBorder="false"
                    v-model="SummariesParams"
                    :list="[
                      { label: '零售价', value: 1 },
                      { label: '回收价', value: 2 },
                      { label: '渠道价', value: 3 }
                    ]"
                    labelKey="label"
                    valueKey="value"
                    isTop
                  ></NyDropdownMenu>
                </span>
                <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
                <span>合计={{ getSummaries() }}</span>
              </div>
            </template>
          </ny-table>
        </div>
        <!-- 推送记录 -->
        <pushRecords ref="pushRecordsRef" :params="{ gameId: state.dataForm.gameId, partnerId: state.dataForm.partnerId }" v-else></pushRecords>
      </div>
    </div>
    <!-- 查看日志 -->
    <el-dialog v-model="dialogForm.visibleLog" :title="dialogForm.titleLog" width="1200" :footer="null" @close="handleCloseLog">
      <ny-table :state="stateLog" :showColSetting="false" :columns="columnsLog" @pageSizeChange="stateLog.pageSizeChangeHandle" @pageCurrentChange="stateLog.pageCurrentChangeHandle" @selectionChange="stateLog.dataListSelectionChangeHandle">
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
          <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
          <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
          <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
        </template>
        <template #pushStatus="{ row }">
          <el-tag v-if="row.pushStatus == '未推送'" type="warning">未推送</el-tag>
          <el-tag v-if="row.pushStatus == '推送成功'" type="primary">推送成功</el-tag>
          <el-tag v-if="row.pushStatus == '推送失败'" type="danger">推送失败</el-tag>
        </template>
        <template #errorMessage="{ row }">
          <div style="display: flex" v-if="row.errorMessage">
            <div style="width: 240px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ row.errorMessage }}</div>
            <el-button @click="handleOpen('推送信息', row.errorMessage)" type="primary" link>查看</el-button>
          </div>
          <span v-else>-</span>
        </template>
        <template #requestParam="{ row }">
          <div style="display: flex" v-if="row.requestParam">
            <div class="sle">{{ row.requestParam }}</div>
            <el-button @click="handleOpen('请求参数', row.requestParam)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #responseBody="{ row }">
          <div style="display: flex" v-if="row.responseBody">
            <div class="sle">{{ row.responseBody }}</div>
            <el-button @click="handleOpen('返回参数', row.responseBody)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #requestUrl="{ row }">
          <div style="display: flex" v-if="row.requestUrl">
            <div class="sle">{{ row.requestUrl }}</div>
            <el-button @click="handleOpen('请求地址', row.requestUrl)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #pushTime="{ row }">
          {{ formatTimeStamp(+row.pushTime) }}
        </template>
      </ny-table>
    </el-dialog>
    <!-- 查看推送详情 -->
    <el-dialog v-model="dialogForm.visible" :title="dialogForm.title" width="800" :footer="null" @close="handleClose">
      <div style="height: 422px; overflow-y: scroll">
        {{ dialogForm.content || "-" }}
      </div>
    </el-dialog>

    <!-- 自动上架设置 -->
    <automaticShelvingSettings ref="automaticShelvingSettingsRef"></automaticShelvingSettings>
  </el-card>
</template>

<script lang="tsx" setup>
import { reactive, ref, toRefs, defineExpose, onMounted, nextTick, onUnmounted } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { formatTimeStamp } from "@/utils/method";
import { useRouter } from "vue-router";
import automaticShelvingSettings from "./automatic-shelving-settings.vue";
import pushRecords from "./push-records.vue";
import pushStask from "./push-stask.vue";

const tabsValue = ref(1);
const router = useRouter();
const pushRecordsRef = ref();
const { currentRoute } = router;
const groupList = ref([
  { dictLabel: "上架商品", dictValue: "1" },
  { dictLabel: "下架商品", dictValue: "2" },
]);
// NOTE: 只对了合作商的接口
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "accountSourceName",
    label: "商品类型",
    minWidth: 120
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: 120
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: 340
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 120
  },
  {
    prop: "channelStatus",
    label: "渠道状态",
    minWidth: 120,
    headerRender: () => {
      return (
        <div class="flx-center">
          <el-tooltip effect="dark" content="推送至对方平台的推测状态" placement="top-start">
            <div style="margin-top:4px;margin-right:4px;">
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </div>
          </el-tooltip>
          <div>渠道状态</div>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: "商品状态",
    minWidth: 120
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "120"
  },
  {
    prop: "acquisitionPrice",
    label: "回收价(元)",
    width: 136
  },
  {
    prop: "channelPrice",
    label: "渠道价(元)",
    width: 136
  },
  {
    prop: "pushTime",
    label: "最新推送时间",
    minWidth: 180
  },
  {
    prop: "showLog",
    label: "日志",
    minWidth: 120
  }
]);
const columnsLog = reactive([
  {
    prop: "errorMessage",
    label: "推送信息",
    minWidth: 200,
    showOverflowTooltip: false
  },
  {
    prop: "requestParam",
    label: "请求参数",
    minWidth: 120
  },
  {
    prop: "responseBody",
    label: "返回参数",
    minWidth: 120
  },
  {
    prop: "requestUrl",
    label: "请求地址",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 100
  },
  {
    prop: "pushTime",
    label: "推送时间",
    minWidth: 140
  }
]);
const view = reactive({
  getDataListURL: "/apiSync/shopSyncList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    gameId: "",
    searchParam: "",
    pushStatus: "",
    accountSource: "ORIGINAL_OWNER",
    tenantCode: "",
    recordType: "1"
  }
});

const viewLog = reactive({
  createdIsNeed: false,
  getDataListURL: "/apiSync/shopSyncRecordList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    shopId: "",
    recordType: 1
  }
});
const nyTableRef = ref();
const state = reactive({ ...useView(view), ...toRefs(view) });
const stateLog = reactive({ ...useView(viewLog), ...toRefs(viewLog) });
// NOTE: API列表
const curAllPartnerList = ref([]);
// NOTE: 游戏列表
const allSelectArr = ref([]);
// NOTE: 平台合作商列表
const tenantList = ref([]);
// NOTE: 弹窗
const dialogForm = reactive({
  visible: false,
  title: "",
  content: "",
  visibleLog: false,
  titleLog: "查看日志",
  visibleError: false,
  titleError: "推送失败记录"
});
const statusList = ref([
  {
    value: 1,
    label: "商品推送"
  },
  {
    value: 2,
    label: "推送记录"
  }
]);
const handleOpen = (title: any, content: any) => {
  dialogForm.visible = true;
  dialogForm.title = title;
  dialogForm.content = content;
};
const handleClose = () => {
  dialogForm.visible = false;
  dialogForm.content = "";
};
const handleOpenLog = (content: any, partnerId?: any) => {
  dialogForm.visibleLog = true;
  dialogForm.titleLog = "查看日志-" + content.gameName + "-" + content.code;
  stateLog.dataForm.shopId = content.id;
  stateLog.dataForm.partnerId = partnerId ? partnerId : state.dataForm.partnerId;
  stateLog.dataForm.recordType = state.dataForm.recordType;
  stateLog.getDataList();
};
const handleCloseLog = () => {
  dialogForm.visibleLog = false;
  dialogForm.titleLog = "查看日志";
};

// 推送所选
const pushSelect = (type: number) => {
  let ids = state.dataListSelections?.map((ele) => ele.id);
  baseService.post("/apiSync/submitSyncShopTask", { partnerId: state.dataForm.partnerId, shopIdList: ids, type, gameId: state.dataForm.gameId }).then((res) => {
    ElMessage.success("操作成功！");
    state.getDataList();
    if (nyTableRef.value) nyTableRef.value.clearSelection();
    clearTimeout(pushStaskTimer.value);
    pushStaskContent();
  });
};

// 重置
const getResetting = () => {
  view.dataForm.searchParam = "";
  view.dataForm.pushStatus = "";
  view.dataForm.accountSource = "ORIGINAL_OWNER";
  view.dataForm.tenantCode = "";
  state.getDataList();
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  view.dataForm.orderBy = [
    {
      field: "",
      isAsc: false
    }
  ];
  view.dataForm.orderBy[0].field = prop;
  view.dataForm.orderBy[0].isAsc = order == "ascending";
  if (order == null) {
    delete view.dataForm.orderBy;
  }
  state.getDataList();
};

// 获取API数据
const getSelectInfo = () => {
  baseService.get("/partner/partner/partnerList").then((res) => {
    if (res.code == 0) {
      curAllPartnerList.value = res.data || [];
      if (curAllPartnerList.value.length > 0) {
        state.dataForm.partnerId = curAllPartnerList.value[0].id;
        getGameList(state.dataForm.partnerId);
      }
    }
  });
};
// 切换平台获取游戏+更新table数据
const handleselectPartner = (id: any) => {
  state.dataForm.partnerId = id || undefined;
  state.dataForm.gameId = undefined;
  if (nyTableRef.value) nyTableRef.value.clearSelection();
  getGameList(state.dataForm.partnerId);
};
// 获取游戏列表
const getGameList = (partnersid: any) => {
  baseService.get("/partner/partner/partnerMappingGameList", { partnerId: partnersid }).then((res_) => {
    if (res_.code == 0) {
      allSelectArr.value = res_.data;
      if (res_.data.length > 0) {
        // 获取列表
        handleselectGame(res_.data[0].gameId);
        // 更新推送记录
        pushRecordsRef.value.getListData();
      }
    }
  });
};

// 获取合作商列表
const getTenantList = () => {
  baseService.get("/sys/tenant/page", { limit: 9999 }).then((res) => {
    if (res.code == 0) {
      tenantList.value = res.data.list;
    }
  });
};

const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  if (nyTableRef.value) nyTableRef.value.clearSelection();
  state.getDataList();
};

// 自动上架
const automaticShelvingSettingsRef = ref();
const automatic = () => {
  automaticShelvingSettingsRef.value.init(2);
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item?.price) total += item?.price || 0;
    });
  } else if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item?.acquisitionPrice) total += item?.acquisitionPrice || 0;
    });
  } else if (SummariesParams.value == 3) {
    state.dataList.map((item: any) => {
      if (item?.channelPrice) total += item?.channelPrice || 0;
    });
  }
  return total.toFixed(2);
};

const pushStaskShow = ref(false);
const mouseTimer = ref();
// 鼠标移入事件
const mouseenterChange = (num: any) => {
  pushStaskShow.value = true;
  if (num == 1) {
    clearTimeout(pushStaskTimer.value);
    pushStaskContent();
  }
  if (num == 2) {
    clearTimeout(mouseTimer.value);
  }
};

// 鼠标移出事件
const mouseleaveChange = (num: any) => {
  if (num == 1) {
    mouseTimer.value = setTimeout(() => {
      pushStaskShow.value = false;
    }, 2000);
  } else {
    clearTimeout(mouseTimer.value);
    pushStaskShow.value = false;
  }
};

// 获取推送任务数量
const pushStaskInfo = ref("");
const pushStaskTimer = ref();
const pushStaskContent = () => {
  baseService.get("/apiSync/syncProgress").then((res) => {
    if (res.code == 0) {
      pushStaskInfo.value = res.data;
      if (pushStaskInfo.value && pushStaskInfo.value.totalTaskNum > 0) {
        pushStaskTimer.value = setTimeout(() => {
          pushStaskContent();
        }, 3000);
      } else {
        clearTimeout(pushStaskTimer.value);
        pushStaskShow.value = false;
      }
    }
  });
};

const handleStatusClick = (tab: any) =>{
  state.dataForm.recordType = tab;
  state.getDataList();
}

onMounted(() => {
  getSelectInfo();
  getTenantList();
  pushStaskContent();
});
onUnmounted(() => {
  clearTimeout(pushStaskTimer.value);
});
</script>
<style lang="less" scoped>
ul {
  padding-left: 0;
  margin-top: 0;
}
.rr-view-ctx-card {
  // padding: 20px 0px;
}
.mainBox {
  display: flex;

  .leftPart {
    width: 186px;
    margin-right: 12px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .scrollWrap {
      border-radius: 4px;
      border: 1px solid #ebeef5;
      height: calc(100vh - 260px);
      overflow: auto;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .menuUl {
        .menuLi {
          cursor: pointer;
          padding: 20px;
          word-break: keep-all;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: Inter, Inter;
          font-weight: 400;
          font-size: 12px;
          color: #303133;
          line-height: 14px;
          &.active {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
          }
        }
      }

      &.lower {
        height: calc(100vh - 280px);
      }
    }
  }

  .rightPart {
    flex: 1;
    overflow: hidden;
  }
}
.el-tag {
  border: 1px solid;
}
.router_info {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.pushProcess {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;

  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 488px;
}
</style>
