<template>
  <el-drawer v-model="visible" v-loading="loadingdata" title="合同实名" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%" class="ny-drawer">
    <template #title>
      <template v-if="hasSetting">
        <span style="font-weight: bold; font-size: 18px; color: #303133; margin-right: 12px">合同已实名</span>
      </template>
      <template v-else><span style="font-weight: bold; font-size: 18px; color: #303133">合同实名</span></template>
    </template>
    <el-alert style="margin-bottom: 12px" title="输入手机号快速查询是否已实名， 已实名手机号可直接绑定" type="info" show-icon :closable="false" />
    <el-card class="contract-real-name">
      <template v-if="hasSetting">
        <div style="margin-bottom: 24px">
          <span style="font-size: 13px; color: #303133">认证状态：</span>
          <el-text v-if="dataForm.regStatus == '5'" type="success">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
          <el-text v-else-if="dataForm.regStatus == '4'" type="danger">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
          <el-text v-else type="primary">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
          <el-tag v-if="dataForm.regStatus != '5'" type="primary" style="cursor: pointer; float: right" @click="resetInfo"> 重新提交 </el-tag>
        </div>
      </template>
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="卖方身份" prop="bestsignUserType">
              <el-radio-group v-model="dataForm.bestsignUserType" :disabled="hasSetting">
                <el-radio value="1">个人</el-radio>
                <el-radio value="2">企业</el-radio>
              </el-radio-group>
              <el-text type="danger">tip：{{ identity }}手机号未实名需先进行实名验证，请先选择{{ identity }}身份，身份类型提交后不可再变更。</el-text>
            </el-form-item>
          </el-col>

          <template v-if="dataForm.bestsignUserType == 1">
            <el-col :span="12">
              <el-form-item label="卖方姓名" prop="username">
                <el-input v-model="dataForm.username" placeholder="请输入卖方姓名" :disabled="hasSetting"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方手机号" prop="userPhone">
                <el-input v-model="dataForm.userPhone" placeholder="请输入卖方手机号" :disabled="hasSetting">
                  <template #prepend>
                    <el-button @click="dataForm.userPhone = propsData.customerPhone">自动填充</el-button>
                  </template>
                  <template #append>
                    <el-button
                      @click="
                        init({
                          orderId: propsData.orderId,
                          contractRealnamePhone: dataForm.userPhone,
                          customerPhone: propsData.customerPhone
                        })
                      "
                      :icon="Search"
                    />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="卖方身份证号" prop="userIdNo">
                <el-input v-model="dataForm.userIdNo" placeholder="请输入卖方身份证号" :disabled="hasSetting"> </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="身份证正面" prop="idFrontPic">
                <ny-upload v-model:imageUrl="dataForm.idFrontPic" :limit="1" :fileSize="2" accept="image/*" :disabled="hasSetting"></ny-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证反面" prop="idBackPic">
                <ny-upload v-model:imageUrl="dataForm.idBackPic" :limit="1" :fileSize="2" accept="image/*" :disabled="hasSetting"></ny-upload>
              </el-form-item>
            </el-col>
          </template>

          <template v-else>
            <el-col :span="12">
              <el-form-item label="卖方企业名称" prop="enterpriseName">
                <el-input v-model="dataForm.enterpriseName" placeholder="请输入卖方企业名称" :disabled="hasSetting"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方统一社会信用代码" prop="licenseCode">
                <el-input v-model="dataForm.licenseCode" placeholder="请输入卖方统一社会信用代码" :disabled="hasSetting"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方企业法人名称" prop="legalPersonName">
                <el-input v-model="dataForm.legalPersonName" placeholder="请输入卖方企业法人名称" :disabled="hasSetting"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方企业法人身份号" prop="legalPersonIdNo">
                <el-input v-model="dataForm.legalPersonIdNo" placeholder="请输入卖方企业法人身份号" :disabled="hasSetting"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卖方企业法人手机号" prop="legalPersonPhone">
                <el-input v-model="dataForm.legalPersonPhone" placeholder="请输入卖方企业法人手机号" :disabled="hasSetting">
                  <template #prepend>
                    <el-button @click="dataForm.legalPersonPhone = propsData.customerPhone">自动填充</el-button>
                  </template>
                  <template #append>
                    <el-button
                      @click="
                        init({
                          orderId: propsData.orderId,
                          contractRealnamePhone: dataForm.legalPersonPhone,
                          customerPhone: propsData.customerPhone
                        })
                      "
                      :icon="Search"
                    />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="营业执照" prop="licensePic">
                <ny-upload v-model:imageUrl="dataForm.licensePic" :limit="1" :fileSize="5" accept="image/*" :disabled="hasSetting"></ny-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人身份证正面" prop="idFrontPic">
                <ny-upload v-model:imageUrl="dataForm.idFrontPic" :limit="1" :fileSize="5" accept="image/*" :disabled="hasSetting"></ny-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人身份证反面" prop="idBackPic">
                <ny-upload v-model:imageUrl="dataForm.idBackPic" :limit="1" :fileSize="5" accept="image/*" :disabled="hasSetting"></ny-upload>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </el-card>

    <template #footer>
      <el-button v-if="!hasSetting" :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button v-else :loading="btnLoading" @click="init({ orderId: propsData.orderId, customerPhone: propsData.customerPhone })">手动录入</el-button>
      <el-button v-if="!hasSetting" :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
      <el-button v-else :loading="btnLoading" type="primary" @click="bindsubmitHandle()">确认绑定</el-button>
    </template>
  </el-drawer>
</template>  

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits, defineProps } from "vue";
import { ElMessage, ElNotification } from "element-plus";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  type: String
});

const emits = defineEmits(["refresh"]);
const visible = ref(false);
const hasSetting = ref(false);
const loadingdata = ref(true);
const propsData = ref();
const identity = props.type == "sell" ? "买方" : "卖方";
const init = (obj?: any) => {
  dataForm.value = {};
  propsData.value = { ...obj };
  dataForm.value.orderId = obj.orderId;
  dataForm.value.bestsignUserType = "1";
  visible.value = true;
  hasSetting.value = false;
  if (!obj.contractRealnamePhone) return;
  baseService
    .get("/bestsign/getBestsignUserByPhone/" + obj.contractRealnamePhone)
    .then((res) => {
      if (res.code == 0 && res.data) {
        hasSetting.value = true;
        propsData.value.contractRealnamePhone = obj.contractRealnamePhone;
        dataForm.value = res.data;
        dataForm.value.bestsignUserType = res.data.bestsignUserType;
        if (dataForm.value.bestsignUserType == 1) {
          dataForm.value.username = res.data.bestsignUserName;
          dataForm.value.userPhone = res.data.bestsignMobile;
          dataForm.value.userIdNo = res.data.idNo;
        } else {
          dataForm.value.enterpriseName = res.data.bestsignUserName;
          dataForm.value.legalPersonPhone = res.data.bestsignMobile;
        }
        baseService.get("/bestsign/queryRegStatus/" + obj.contractRealnamePhone + "/1").then((res) => {
          dataForm.value.regStatus = res.data?.regStatus;
        });
      } else if (res.code == 0) {
        ElNotification({
          title: "手机号未实名！",
          type: "info"
        });
      }
    })
    .finally(() => {
      loadingdata.value = false;
    });
};
const statusList = ref(["taskId不存在或已过期", "新申请", "申请中", "超时", "申请失败", "成功", "失败重试"]);
const dataForm = ref(<any>{
  bestsignUserType: "1"
});

const rules = reactive({
  bestsignUserType: [{ required: true, message: "请选择卖方身份", trigger: "change" }],
  username: [{ required: true, message: "请输入卖方名称", trigger: "blur" }],
  userIdNo: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
  userPhone: [{ required: true, message: "请输入卖方手机号", trigger: "blur" }],
  legalPersonPhone: [{ required: true, message: "请输入卖方企业法人手机号", trigger: "blur" }],
  legalPersonIdNo: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
  idFrontPic: [{ required: true, message: "请上传身份证正面", trigger: "change" }],
  idBackPic: [{ required: true, message: "请上传身份证反面", trigger: "change" }],
  enterpriseName: [{ required: true, message: "请输入卖方企业名称", trigger: "blur" }],
  licenseCode: [{ required: true, message: "请输入卖方统一社会信用代码", trigger: "blur" }],
  legalPersonName: [{ required: true, message: "请输入卖方企业法人名称", trigger: "blur" }],
  licensePic: [{ required: true, message: "请上传营业执照", trigger: "change" }]
});

// 提交
const btnLoading = ref(false);
const formRef = ref();
const dataFormSubmitHandle = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      dataForm.value.orderType = props.type == "sell" ? 1 : 0;
      baseService
        .post("/bestsign/createBestsignUser", dataForm.value)
        .then((res) => {
          if (res.code == 0) {
            baseService.put(props.type == "sell" ? "/sale/updateRealnamePhone" : "/purchase/updateRealnamePhone", {
              id: propsData.value.orderId,
              contractRealnamePhone: dataForm.value.bestsignUserType == 1 ? dataForm.value.userPhone : dataForm.value.legalPersonPhone
            });
            ElMessage.success("提交成功");
            emits("refresh");
            visible.value = false;
            setTimeout(() => {
              searchRealNameStatus();
            }, 3000);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

// 提交成功之后查询 实名状态
const searchRealNameStatus = async () => {
  let res = await baseService.get("/bestsign/getBestsignUserByPhone/" + dataForm.value.userPhone);
  if (res.data && res.data.regStatus == 5) {
    emits("refresh");
  }
};

// 认证失败重新提交
const resetInfo = () => {
  baseService.get("/bestsign/queryRegStatus/" + propsData.value.contractRealnamePhone + "/1").then((res) => {
    ElMessage.success("提交成功！");
  });
};
const bindsubmitHandle = () => {
  btnLoading.value = true;
  let form = {
    id: propsData.value.orderId,
    contractRealnamePhone: propsData.value.contractRealnamePhone
  };
  baseService
    .put(props.type == "sell" ? "/sale/updateRealnamePhone" : "/purchase/updateRealnamePhone", {
      ...form
    })
    .then((res) => {
      ElMessage.success("提交成功！");
      emits("refresh");
      visible.value = false;
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.make-payment-confirm {
  .image {
    width: 148px;
    height: 148px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e4e7ed;
  }
}
.el-alert {
  padding: 0;
  background: none;
}
</style>