<template>
     <el-dialog
        v-model="visible" 
        :title="dataForm.id ? '编辑收号属性' : '新增收号属性'" 
        :close-on-click-modal="false" 
        :close-on-press-escape="false" 
        width="70%" 
        class="open-paid"
    >
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-width="120px" v-loading="dataLoading">
            <el-form-item label="游戏名称" prop="gameCode">
                <ny-select-search v-model="dataForm.gameCode"
                    labelKey="name"
                    valueKey="code"
                    url="/game/queryUserList"
                    :param="{limit:9999}"
                    placeholder="选择游戏"
                />
            </el-form-item>
            <el-form-item label="属性" prop="name">
                <el-input v-model="dataForm.name" placeholder="属性"></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <ny-radio-group v-model="dataForm.type" dict-type="game_properties_type"></ny-radio-group>
            </el-form-item>
            <el-form-item label="属性明细">
                <el-input 
                    v-model="attributesDetail"
                    :autosize="{ minRows: 2, maxRows: 4 }" 
                    type="textarea" 
                    placeholder="填写属性名，并用逗号隔开自动解析填入下列表格"
                    @input="inputChange"
                ></el-input>
            </el-form-item>
            <el-form-item label="收号属性">
                <el-button type="primary" plain icon="plus" class="mb-20" @click="addProperty">添加属性</el-button>
                <el-table
                    :data="dataForm.listDetails"
                    border
                    style="width: 100%"
                >
                    <el-table-column type="index" width="50" align="center"></el-table-column>
                    <el-table-column prop="name" label="属性明细" align="center">
                        <template #default="{ row }">
                            <el-input v-model="row.name" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="value" label="操作" width="100" align="center">
                        <template #default="{ $index }">
                            <el-button text bg type="danger" @click="propertyList.splice($index, 1)">{{ $t("delete") }}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>

        <template v-slot:footer>
            <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineExpose } from 'vue';
import baseService from "@/service/baseService";

const visible = ref(false);
const dataForm = ref<any>({
    id: '',
    // 游戏名称
    gameCode: '',
    // 属性
    name: '',
    // 类型
    type: '',
    // 属性明细列表
    listDetails: [] as { name: string }[],
    attributeText: [] as string[],
})

const rules = {
    gameCode: [
        { required: true, message: '请选择游戏', trigger: 'change' }
    ],
    name: [
        { required: true, message: '请输入属性', trigger: 'blur' }
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'change' }
    ]
}

const init = (id: number) => {
    visible.value = true;
    if(id){
        getDetail(id);
    }
}

// 获取详情
const dataLoading = ref(false);
const getDetail = async (id: number) => {
    dataLoading.value = true;
    let res = await baseService.get('/api', { id: id });
    dataLoading.value = false;
    if(res.code == 0){
        dataForm.value = res.data;
    }
}

// textarea  属性明细
const attributesDetail = ref('');
const timer = ref();
const inputChange = () => {
    clearTimeout(timer.value)
    timer.value = setTimeout(() => {
        const arr = attributesDetail.value.split(',');
        const list = dataForm.value.listDetails.map((item: any) => {
            return item.name
        })
        arr.map((item: string) => {
            if(!list.includes(item) && item){
                dataForm.value.listDetails.push({ name: item })
            }
        })
    }, 1000)
}

// 属性列表
const propertyList = ref(<any>[]);

// 添加属性
const addProperty = () => {
    dataForm.value.listDetails.push({
        name: ''
    })
}


// 提交
const dataFormRef = ref();
const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        
        dataForm.value.attributeText = dataForm.value.listDetails.map((item: any) => {
            return item.name
        })
        console.log(dataForm.value)
        
    })
}


defineExpose({
    init
})

</script>