<template>
  <el-drawer v-model="drawer" title="筛选属性" size="50%" class="drawer_shop">
    <el-form :model="dataForm" ref="dataFormRef" label-position="top" :validate-on-rule-change="false">
      <div class="drawer_page" v-loading="requestLoading">
        <div class="menu_left">
          <div class="menu_li" :class="{ menu_active: index == menuIndex }" v-for="(item, index) in ['文本属性', '商品属性', '账号属性']" :key="index" @click="scrollTo(index)">{{ item }}</div>
        </div>
        <div class="menu_right">
          <div class="menu_card" :ref="setSectionRef">
            <div class="menu_title">文本属性</div>
            <el-row :gutter="12">
              <el-col :span="6" v-for="(item, index) in dataForm.attributesList.filter((item: any) => (item.type == 3 && item.isSection == 1 && item.isShow == 1))" :key="index">
                <el-form-item :label="item.name">
                  <div class="retail-price">
                    <el-input-number :controls="false" placeholder="最低" style="width: 80px" v-model="item.start"></el-input-number>
                    <span>至</span>
                    <el-input-number :controls="false" placeholder="最高" style="width: 80px" v-model="item.end"></el-input-number>
                  </div>
                  <!-- <div class="flod_tab" v-else>
                    <el-input v-model="item.attributeText" :placeholder="item.name" clearable></el-input>
                  </div> -->
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="menu_card" :ref="setSectionRef">
            <div class="menu_title">商品属性</div>
            <el-row :gutter="12">
              <el-col :span="6" v-for="(item, index) in dataForm.attributesList.filter((item: any) => item.type == 1)" :key="index">
                <el-form-item :label="item.name">
                  <el-select v-model="item.attributeIds" placeholder="请选择" clearable @clear="item.attributeIds = ''">
                    <el-option v-for="it in item.children" :key="it.id" :label="it.name" :value="it.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="menu_card" :ref="setSectionRef">
            <div class="menu_title">账号属性</div>
            <div style="display: flex; flex-direction: column; gap: 12px">
              <template v-for="(item, index) in dataForm.attributesList.filter((item: any) => item.type == 2)" :key="index">
                <div>
                  <div class="menu_header">
                    <div class="name">{{ item.name }}</div>
                    <div class="operate">
                      <el-button type="primary" text v-if="item.attributeIds.length > 0" @click="item.attributeIds = []" class="resetFont">重置</el-button>
                      <el-button v-if="item.children.length > 4" type="primary" plain text @click="item.fold = !item.fold">
                        <span style="margin-right: 6px">{{ item.fold ? "展开列表" : "收起列表" }}</span>
                        <el-icon v-if="item.fold">
                          <ArrowDown />
                        </el-icon>
                        <el-icon v-else>
                          <ArrowUp />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div class="menu_center">
                    <el-checkbox-group v-model="item.attributeIds">
                      <div style="display: flex; flex-wrap: wrap; gap: 12px">
                        <div style="width: 180px" v-for="it in spliceChildren(item)" :key="it.id">
                          <el-checkbox :label="it.name" :value="it.id" v-lazy-render />
                        </div>
                      </div>
                    </el-checkbox-group>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="drawer = false">取消</el-button>
        <el-button type="primary" @click="confirmClick">查询</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
const drawer = ref(false);
const emit = defineEmits(["refreshDataList", "completeInfo"]);

// 瞄点菜单
const menuIndex = ref(0);
const sectionRefs = ref(<any>[]);
// 收集所有内容区域的ref
const setSectionRef = (el) => {
  if (el) {
    sectionRefs.value.push(el);
  }
};

// 滚动到指定区域
const scrollTo = async (index) => {
  menuIndex.value = index;
  await nextTick(); // 等待DOM更新
  sectionRefs.value[index]?.scrollIntoView({
    behavior: "smooth",
    block: "start"
  });
};

const spliceChildren = (item: any) => {
  let list = JSON.parse(JSON.stringify(item.children));
  return !item.fold ? list : list.splice(0, 4);
};

const dataForm = reactive({
  gameId: "",
  attributesList: <any>[]
});

const requestLoading = ref(false); // 详情加载
// 获取游戏属性信息
const getAttribute = () => {
  requestLoading.value = true;
  baseService
    .get("/game/attribute/page", { gameId: dataForm.gameId })
    .then((res) => {
      if (res.data.length == 0) {
        ElMessage({
          type: "warning",
          message: "没有查询到当前游戏属性信息请编辑后在来新增商品！"
        });
      } else {
        dataForm.attributesList = [];
        res.data.map((item: any) => {
          dataForm.attributesList.push({
            typeId: item.id,
            attributeIds: item.type == 2 ? [] : "",
            attributeText: "",
            children: item.children,
            isTitle: item.isTitle,
            type: item.type,
            name: item.name,
            statisticsTag: item.statisticsTag,
            fold: false,
            start: null,
            end: null,
            isSection: item.isSection,
            isShow: item.isShow
          });
        });
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

// 查询
const confirmClick = () => {
  drawer.value = false;
  emit("refreshDataList", dataForm.attributesList);
};

// 重置
const reset = () => {
  dataForm.attributesList = [];
};

const init = (gameId: string) => {
  dataForm.gameId = gameId;
  drawer.value = true;
  if (gameId) {
    if (!dataForm.attributesList.length) {
      getAttribute();
    }
  }
};

// 自定义懒渲染指令
const vLazyRender = {
  mounted(el: any, binding: any) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 这里可以添加动画效果或直接显示内容
            el.style.opacity = 1;
            observer.unobserve(el);
          }
        });
      },
      {
        threshold: 0.1
      }
    );

    observer.observe(el);
    el.style.opacity = 0;
    el.style.transition = "opacity 0.5s ease";
  }
};

watch(
  () => dataForm.gameId,
  (newVal, oldVal) => {
    if (oldVal) {
      getAttribute();
    }
  }
);

defineExpose({
  init,
  reset
});
</script>

<style lang="less" scoped>
.retail-price {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;

  :deep(.el-input-number) {
    width: 120px;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }
}

.drawer_page {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;

  display: flex;
  align-items: flex-start;
  gap: 12px;

  .menu_left {
    width: 104px;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .menu_li {
      padding: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #303133;
      cursor: pointer;
    }

    .menu_active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }

  .menu_right {
    // border: 1px solid red;
    flex: 1;
    height: calc(100vh - 162px);
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: auto;

    .menu_card {
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      background: #f7f8fa;
      padding: 12px 12px 0px 12px;

      .menu_title {
        font-size: 14px;
        font-weight: 700;
        line-height: 12px;
        margin-bottom: 8px;
      }

      .menu_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
          color: #606266;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }

        .operate {
        }
      }

      .menu_center {
        :deep(.el-checkbox__label) {
          width: 160px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
<style lang="less">
.demo-tabs > .el-tabs__header {
  margin: 0 20px 0;
}

.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    padding: 12px;
    background-color: #f0f2f5;
  }
}
</style>
