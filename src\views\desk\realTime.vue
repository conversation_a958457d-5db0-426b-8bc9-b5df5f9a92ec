<template>
  <div class="realtime">
    <div class="above">
      <div class="left">
        <span class="title">{{ props.isDesk ? "实时数据" : "数据概览" }}</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
        <!-- <div style="cursor: pointer" class="toPage" v-if="!props.isDesk" @click="jumpRouter('/desk/index')">
          查看更多<el-icon><ArrowRight /></el-icon>
        </div> -->
      </div>
    </div>
    <div class="count">
      <div class="count_list" :style="props.isDesk ? 'padding: 12px;' : ' padding: 24px;'">
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.visitNums || 0) }}</span>
          <span class="count_item_label">总访问量</span>
        </div>
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.userNums || 0) }}</span>
          <span class="count_item_label">注册用户</span>
        </div>
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.serviceNums || 0) }}</span>
          <span class="count_item_label">客服咨询</span>
        </div>
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.bargainNums || 0) }}</span>
          <span class="count_item_label">商品议价</span>
        </div>
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.gameNums || 0) }}</span>
          <span class="count_item_label">游戏库存</span>
        </div>
        <div class="count_item">
          <span class="count_item_value">{{ formatCurrency(state.chartData.pushNums || 0) }}</span>
          <span class="count_item_label">平台推送</span>
        </div>
      </div>
    </div>
    <div class="data_echarts" :style="props.isDesk ? 'margin-top: 12px;' : ''" ref="divRef">
      <el-row :gutter="12">
        <el-col :span="props.isDesk ? 12 : 24">
          <div class="data_echarts_item" :style="!props.isDesk ? 'margin-top: 24px;' : ''">
            <div class="echarts_item_left">
              <div class="label">交易金额(元)</div>
              <div class="value">{{ formatCurrency(state.chartData.tradeData?.totalAmount || 0) }}</div>
              <div class="compare">
                <span>较昨日</span>
                <el-icon size="16px" color="#F56C6C"> <CaretTop v-if="state.chartData.tradeData?.direction == 'postive'" /><CaretBottom v-else /> </el-icon>
                <span>{{ state.chartData.tradeData?.rate || 0 }}%</span>
              </div>
            </div>
            <div class="echarts_item_right">
              <div style="width: 100%; height: 100%" ref="AccessChartRefs"></div>
            </div>
          </div>
        </el-col>
        <el-col :span="props.isDesk ? 12 : 24">
          <div class="data_echarts_item" :style="!props.isDesk ? 'margin-top: 24px;' : ''">
            <div class="echarts_item_left">
              <div class="label">订单数</div>
              <div class="value" style="color: #39b70d">{{ formatCurrency(state.chartData.orderData?.totalCount || 0) }}</div>
              <div class="compare">
                <span>较昨日</span>
                <el-icon size="16px" color="#39B70D"><CaretTop v-if="state.chartData.orderData?.direction == 'postive'" /><CaretBottom v-else /></el-icon>
                <span>{{ state.chartData.orderData?.rate || 0 }}%</span>
              </div>
            </div>
            <div class="echarts_item_right">
              <div style="width: 100%; height: 100%" ref="orderChartRefs"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { formatDate, formatCurrency } from "@/utils/method";
import { useRouter, useRoute } from "vue-router";
import commonData from "./index.ts";
import * as echarts from "echarts";
const AccessChartRefs = ref(null); // 交易金额折线图
const orderChartRefs = ref(null); // 订单数折线图
const props = defineProps({
  isDesk: {
    type: Boolean,
    default: true
  }
});

const router = useRouter();
const state = reactive({
  charts: [],
  chartData: {}
});
// 处理x轴时间
const handleXTime = (data: any) => {
  let afterArr = data.map((ele: any) => {
    let arr = ele.split("-");
    return arr?.length == 3 ? `${arr[1]}/${arr[2]}` : "";
  });
  return afterArr;
};
// 交易金额折线图
const UserAccessChart = () => {
  const userGrowthChart = echarts.init(AccessChartRefs.value);
  const option = {
    xAxis: {
      type: "category",
      axisLabel: {
        show: true,
        color: "#909399",
        fontSize: 10
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      data: handleXTime(state.chartData.tradeData?.xData) || []
    },
    yAxis: {
      type: "value",
      axisLabel: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    tooltip: {
      trigger: "axis"
    },
    grid: {
      top: "4%",
      left: "0%",
      right: "0%",
      bottom: "26%"
    },
    series: [
      {
        data: state.chartData.tradeData?.yData || [],
        type: "line",
        smooth: true,
        symbol: "none",
        lineStyle: {
          width: 3,
          color: "#3354F4"
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0.5, color: "#BCBCFC" },
            { offset: 0, color: "#BDA1FE" }
          ])
        }
      }
    ]
  };
  userGrowthChart.setOption(option);
  state.charts.push(userGrowthChart);
};
// 订单数折线图
const DataOrderChart = () => {
  const userGrowthChart = echarts.init(orderChartRefs.value);
  const option = {
    xAxis: {
      type: "category",
      axisLabel: {
        show: true,
        color: "#909399",
        fontSize: 10
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      data: handleXTime(state.chartData.orderData?.xData) || []
    },
    yAxis: {
      type: "value",
      axisLabel: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    tooltip: {
      trigger: "axis"
    },
    grid: {
      top: "4%",
      left: "0%",
      right: "0%",
      bottom: "26%"
    },
    series: [
      {
        data: state.chartData.orderData?.yData || [],
        type: "line",
        smooth: true,
        symbol: "none",
        lineStyle: {
          width: 3,
          color: "#39B70D"
        },
        itemStyle: {
          color: "#39B70D"
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0.5, color: "#A8DF95" },
            { offset: 0, color: "#3AB70E" }
          ])
        }
      }
    ]
  };
  userGrowthChart.setOption(option);
  state.charts.push(userGrowthChart);
};
const jumpRouter = (path: any) => {
  router.push(path);
};

onMounted(async () => {
  state.chartData = await commonData.realTimeData();

  if (!state.chartData.tradeData || !state.chartData.tradeData?.xData || state.chartData.tradeData?.xData.length < 1) {
    state.chartData.tradeData.xData = commonData.currentWeekDate();
  }
  if (!state.chartData.orderData || !state.chartData.orderData?.xData || state.chartData.orderData?.xData.length < 1) {
    state.chartData.orderData.xData = commonData.currentWeekDate();
  }
  UserAccessChart();
  DataOrderChart();
});
</script>

<style lang='less' scoped>
.realtime {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.count {
  background: #f8f8f8;
  border-radius: 4px 4px 4px 4px;
  margin-top: 12px;
  .count_list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .count_item {
      flex: 1;
      min-width: 130px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 12px;
      .count_item_value {
        font-weight: bold;
        font-size: 18px;
        color: #000000;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .count_item_label {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: 6px;
      }
    }
  }
}
.data_echarts {
  .data_echarts_item {
    flex: 1;
    display: flex;
    align-items: center;
    .echarts_item_left {
      width: 112px;
      // margin-right: 12px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .label {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .value {
        font-weight: bold;
        font-size: 20px;
        color: var(--el-color-primary);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 4px 0px;
      }
      .compare {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 12px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
      }
    }
    .echarts_item_right {
      // border: 1px solid red;
      flex: 1;
      height: 67px;
    }
  }
}
</style>