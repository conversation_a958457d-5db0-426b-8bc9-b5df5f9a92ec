<template>
  <shop-add-or-update ref="shopAddOrUpdateRef" :title="'完善信息'" source="order" getDetialApi="/purchase/getOrderInfo/" @completeInfo="completeInfoSubmitHandle" :submitLoading="btnLoading">
    <template #header="scope">
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
        <el-descriptions :column="2" border class="descriptions descriptions-label-140">
          <el-descriptions-item>
            <template #label
              ><span>游戏账号<span style="color: red">*</span></span></template
            >
            <el-form-item label="游戏账号" prop="gameAccount">
              <el-input v-model="dataForm.gameAccount" placeholder="游戏账号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>游戏密码<span style="color: red">*</span></span></template
            >
            <el-form-item label="游戏密码" prop="gamePassword">
              <el-input v-model="dataForm.gamePassword" type="password" show-password placeholder="游戏密码"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>买方手机号<span style="color: red">*</span></span></template
            >
            <el-form-item label="买方手机号" prop="buyerPhone">
              <el-input v-model="dataForm.buyerPhone" placeholder="买方手机号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>卖方手机号<span style="color: red">*</span></span></template
            >
            <el-form-item label="卖方手机号" prop="sellerPhone">
              <el-input v-model="dataForm.sellerPhone" placeholder="卖方手机号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>回收价(元)<span style="color: red">*</span></span></template
            >
            <el-form-item label="回收价(元)" prop="acquisitionPrice">
              <el-input v-model="dataForm.acquisitionPrice" placeholder="回收价"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>包赔费(元)</span></template>
            <el-form-item label="包赔费(元)" prop="guaranteeAmount">
              <el-input v-model="dataForm.v" placeholder="包赔费"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <!-- <el-row :gutter="12">
                    <el-col :span="12">
                        <el-form-item label="游戏账号" prop="gameAccount">
                            <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="游戏密码" prop="gamePassword">
                            <el-input v-model="dataForm.gamePassword" type="password" show-password placeholder="请输入游戏密码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="买方手机号" prop="buyerPhone">
                            <el-input v-model="dataForm.buyerPhone" placeholder="请输入买方手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="卖方手机号" prop="sellerPhone">
                            <el-input v-model="dataForm.sellerPhone" placeholder="请输入卖方手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="回收价(元)" prop="acquisitionPrice">
                            <el-input v-model="dataForm.acquisitionPrice" placeholder="请输入回收价"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="包赔费(元)">
                            <el-input v-model="dataForm.guaranteeAmount" placeholder="请输入包赔费"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row> -->
      </el-form>
      <!-- <div class="p-title mt-0">商品描述</div> -->
    </template>
  </shop-add-or-update>
</template>

<script lang="ts" setup>
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import shopAddOrUpdate from "@/views/shop/shop-add-or-update.vue";
import baseService from "@/service/baseService";

const dataForm = reactive(<any>{});

const rules = reactive({
  gameAccount: [{ required: true, message: "请输入游戏账号", trigger: "blur" }],
  gamePassword: [{ required: true, message: "请输入游戏密码", trigger: "blur" }],
  buyerPhone: [
    { required: true, message: "请输入买方手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  sellerPhone: [
    { required: true, message: "请输入卖方手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  acquisitionPrice: [{ required: true, message: "请输入回收价", trigger: "blur" }]
});

const shopAddOrUpdateRef = ref();
const orderParams = ref({});
const init = (data: any) => {
  orderParams.value = { ...data };
  dataForm.orderId = data.id;
  dataForm.orderType = data.orderType;
  dataForm.gameId = data.gameId;
  dataForm.gameAccount = data.gameAccount;
  dataForm.acquisitionPrice = data.amount;
  dataForm.sellerPhone = data.customerPhone;
  dataForm.buyerPhone = data.orderType == 2 ? data.bindingPhone : data.orderInfo?.buyerPhone || data.changeInfo?.ourBindPhone || "";
  dataForm.gamePassword = data.gamePassword ? data.gamePassword : data.changeInfo ? data.changeInfo.ourPassword : "";
  dataForm.guaranteeAmount = data.orderInfo?.guaranteePrice || data.guaranteePrice || data.guaranteeAmount;
  dataForm.server = data.serverId;

  shopAddOrUpdateRef.value.init(data.gameId, data.id);
};

const emit = defineEmits(["refresh"]);

const formRef = ref();
const btnLoading = ref(false);
const completeInfoSubmitHandle = (attributesList: any, params: any) => {
  formRef.value.validate(async (valid: any) => {
    if (!valid) return;
    console.log("===== sdfjlsdfjsldkj ====");
    let simpleOrder = dataForm.orderType == 2;
    delete dataForm.orderType;
    btnLoading.value = true;
    attributesList.map((item: any) => {
      if (item.type == 1) {
        item.attributeIds = item.attributeIds ? [item.attributeIds] : [];
      }
      if (item.type == 2) {
        item.attributeIds = item.attributeIds ? item.attributeIds : [];
      }
      if (item.type == 3) {
        item.attributeIds = [];
      }
      delete item.children;
    });
    baseService
      .post("/purchase/completeInfo", {
        orderId: dataForm.orderId,
        server: params.server || params.district,
        gameAccount: dataForm.gameAccount,
        gameId: dataForm.gameId,
        gamePassword: dataForm.gamePassword,
        buyerPhone: dataForm.buyerPhone,
        sellerPhone: dataForm.sellerPhone,
        acquisitionPrice: dataForm.acquisitionPrice,
        guaranteeAmount: dataForm.guaranteeAmount,
        title: params.title,
        info: params.info,
        attributesList: attributesList,
        log: params.log instanceof Array ? params.log.join(",") : "",
        images: params.imagesList,
        highlights: params.highlights,
        compensation: params.compensation,
        bargain: params.bargain,
        topped: params.topped,
        remark: params.remark
      })
      .then((res) => {
        if (res.code == 0) {
          if (simpleOrder) {
            baseService.get("/purchase/inbound/" + dataForm.orderId).then((res) => {
              if (res.code == 0) {
                ElMessage.success("基本信息提交并入库成功");
                shopAddOrUpdateRef.value.drawerCloseHandle();
                emit("refresh");
              }
            });
            return;
          }
          ElMessage.success("基本信息提交成功！");
          shopAddOrUpdateRef.value.drawerCloseHandle();
          emit("refresh");
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
