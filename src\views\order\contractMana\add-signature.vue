<template>
  <el-dialog class="contractTemplateDialog" v-model="visible" width="480" :title="'上传签章'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 270px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item style="margin-bottom: 8px" prop="stampUrl" label="上传签章">
        <ny-upload-file tip="建议尺寸1:1, png格式, 2M以内" :limit="1" v-model:fileSrc="dataForm.stampUrl" widthUpload="432px" heightUpload="198px" :isOneLine="true" accept="image/*" :iszip="false"></ny-upload-file>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: undefined,
  bestsignUserType: 2,
  stampUrl: undefined
});

const rules = ref({
  stampUrl: [{ required: true, message: "请选择文件", trigger: "change" }]
});

const init = (obj?: any) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  dataForm.id = obj.id;
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService
      .put("/bestsign/updateStampUrl", {
        id: dataForm.id,
        bestsignUserType: 2,
        stampUrl: dataForm.stampUrl
      })
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
  });
};
defineExpose({
  init
});
</script>
  <style lang="scss" >
.contractTemplateDialog {
  padding: 24px;
  .el-dialog__headerbtn {
    transform: translate(-25%, 25%);
  }
  .el-form-item {
    display: block;
  }
}
</style>
  
  