<template>
  <div>
    <!-- 等待轮播图加载结束 -->
    <div v-if="!state.loadingPage" class="home_page">
      <el-row :gutter="12" style="margin-bottom: 12px">
        <el-col :span="17">
          <div class="system_info">
            <el-carousel style="height: 100%">
              <el-carousel-item @click="jumpRouter(item.linkUrl, false)" style="cursor: pointer; height: 100%" v-for="item in bannerData" :key="item">
                <img :src="item.imgUrl" style="width: 100%; height: 100%" alt="" />
              </el-carousel-item>
            </el-carousel>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="home_card">
            <div class="above">
              <div class="left">
                <span class="title">我管理的组织</span>
              </div>
              <div class="right">
                <div class="toPage" @click="jumpRouter(state.shopUrl, false)">
                  进入商城主页<el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
            <div class="my_info">
              <div class="info_top">
                <img src="../assets/images/home_icon1.png" class="info_top_icon" />
                <div class="info_top_cont">
                  <div class="cont_name">恒星游戏</div>
                  <div class="cont_time">
                    您今天已经工作了<span>{{ state.workingTimeFormat }}</span>
                  </div>
                </div>
              </div>
              <div class="info_cont">
                <div class="info_cont_item">
                  <div class="info_cont_item_value">{{ formatCurrency(state.personData_.balance || 0) }}</div>
                  <div class="info_cont_item_label">账户余额(星币)</div>
                </div>
                <div class="info_cont_item">
                  <div class="info_cont_item_value">{{ formatCurrency(state.personData_.consume || 0) }}</div>
                  <div class="info_cont_item_label">总消费(星币)</div>
                </div>
                <div style="margin-left: 8px">
                  <el-button
                    style="width: 80px"
                    type="primary"
                    @click="toBillBalance"
                    >充值</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="17">
          <div class="recommend">
            <div class="recommend_top">
              <div class="recommend_top_content">
                <div class="recommend_left">
                  <span class="title">热门推荐</span>
                </div>
                <div class="recommend_right">
                  <div @click="jumpRouter('/plugin/paid/paidfunction', true)" class="toPage">
                    <span>查看所有功能</span>
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </div>
            </div>
            <div class="recommend_content">
              <div style="display: flex; align-items: center; justify-content: center; width: 100%" v-if="state.apiData_.length == 0">
                <ny-no-data type="3" description="暂无功能" />
              </div>
              <template v-else>
                <div class="recommend_content_item" v-for="item in state.apiData_" :key="item.id">
                  <img :src="item.paidFunctionOptions.logo" class="item_icon" />
                  <div class="item_content">
                    <div class="item_content_label">{{ item.name }}</div>
                    <div class="item_content_info">{{ item.introduce }}</div>
                    <div class="item_content_but">
                      <span class="but_item">{{ ["试用", "按次", "包月", "永久"][+item.paidFunctionOptions.chargingMethod] }}</span>
                      <span class="but_item">全平台</span>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <!-- 平台账单统计 -->
              <div class="bill" style="min-height: 100%">
                <div class="above">
                  <div class="left">
                    <span class="title">平台账单统计</span>
                  </div>
                  <div class="right">
                    <div class="deadline">截止至 {{ state.currentDateTime }}</div>
                    <div style="cursor: pointer" class="toPage" @click="jumpRouter('/bill/specification', true)">
                      查看更多<el-icon><ArrowRight /></el-icon>
                    </div>
                  </div>
                </div>
                <div class="bill_card flx-align-center">
                  <div class="bill_card_item flx-1" style="margin-right: 12px">
                    <div class="above flx-align-center">
                      <el-icon size="15px" color="#F56C6C"><Histogram /></el-icon>
                      <span class="title flx-1">月消费统计(星币)</span>
                      <el-date-picker v-model="consumptionParams.date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="consumption" />
                    </div>
                    <div class="middle" style="margin-bottom: 12px">{{ formatCurrency(consumptionData.amount) }}</div>
                    <!-- <div class="below">
                      <span class="sum_label">总消费：</span>
                      <span class="sum_value" style="color: #f56c6c">{{ formatCurrency(consumptionData.total) }}</span>
                    </div> -->
                  </div>
                  <div class="bill_card_item flx-1">
                    <div class="above flx-align-center">
                      <el-icon size="15px" color="#67C23A"><Histogram /></el-icon>
                      <span class="title flx-1">月充值统计(星币)</span>
                      <el-date-picker v-model="rechargeParams.date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="recharge" />
                    </div>
                    <div class="middle" style="margin-bottom: 12px">{{ formatCurrency(rechargeData.amount) }}</div>
                    <!-- <div class="below">
                      <span class="sum_label">总充值：</span>
                      <span class="sum_value" style="color: #67c23a">{{ formatCurrency(rechargeData.total) }}</span>
                    </div> -->
                  </div>
                </div>
                <div style="display: inline-block">
                  <ny-button-group
                    :list="[
                      { dictLabel: '消费', dictValue: '0' },
                      { dictLabel: '充值', dictValue: '1' }
                    ]"
                    v-model="dataForm_.status"
                    @change="handleClick"
                    style="margin: 8px 0px"
                  ></ny-button-group>
                </div>

                <el-table :data="tableData" style="width: 100%" border :show-overflow-tooltip="true">
                  <el-table-column :prop="dataForm_.status == '0' ? 'remark' : 'orderNum'" :label="dataForm_.status == '0' ? '插件市场' : '订单编号'" align="center" />
                  <el-table-column prop="spendingAmount" :label="dataForm_.status == '0' ? '消费金额(星币)' : '充值金额(星币)'" align="center" />
                  <el-table-column prop="createDate" :label="dataForm_.status == '0' ? '消费时间' : '充值时间'" align="center">
                    <template v-slot="{ row }">
                      {{ formatDate(row.createDate, undefined) }}
                    </template>
                  </el-table-column>
                  <!-- 空状态 -->
                  <template #empty>
                    <div style="padding: 68px 0">
                      <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                    </div>
                  </template>
                </el-table>
              </div>
            </el-col>
            <el-col :span="12">
              <!-- 数据概览 -->
              <realTime :is-desk="false" />
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="7">
          <!-- 平台公告 -->
          <sysMsg />
          <!-- 快捷入口 -->
          <quick :isWrap="true" />
        </el-col>
      </el-row>
      <!-- 充值 -->
      <el-dialog v-model="rechargedrawer" width="480" title="充值" :close-on-click-modal="false" :close-on-press-escape="false" @close="rechargedrawer = false">
        <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>支付宝账号</div> </template>
            <EMAIL>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>支付宝账户主体</div> </template>
            枣庄努运企业管理有限公司
          </el-descriptions-item>
        </el-descriptions>
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top">
          <el-form-item label="支付宝订单号" prop="orderNum">
            <template #label>
              <span style="margin-right: 10px">支付宝订单号</span>
              <el-text type="primary" @click="dataForm.showImagePreview = true">如何查看订单号？</el-text>
            </template>
            <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div style="flex: auto">
            <el-button @click="rechargedrawer = false">取消</el-button>
            <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
          </div>
        </template>
        <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="closePreview" />
      </el-dialog>
    </div>
    <div v-else style="padding: 24px; background: #fff; border-radius: 8px; height: 100%; overflow: hidden">
      <el-skeleton animated style="margin-bottom: 30px">
        <template #template>
          <el-skeleton-item variant="h3" style="width: 15%" />
          <el-skeleton-item variant="h3" style="width: 30%; margin-left: 12px" />
          <div style="margin-top: 30px; display: flex; justify-content: space-between">
            <el-skeleton-item variant="h3" style="width: 40%" />
          </div>
        </template>
      </el-skeleton>
      <el-skeleton animated>
        <template #template>
          <el-skeleton-item style="margin-bottom: 16px" variant="h3" v-for="item in 14" :key="item" />
          <div style="margin-top: 20px; display: flex; justify-content: flex-end">
            <el-skeleton-item variant="h3" style="width: 40%" />
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, ref, watch, watchEffect } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { formatDate, formatCurrency, Local } from "@/utils/method";
import { useSettingStore } from "@/store/setting";
import sysMsg from "@/views/desk/sysMsg.vue";
import quick from "@/views/desk/quick.vue";
import realTime from "@/views/desk/realTime.vue";
import commonData from "@/views/desk/index.ts";
import * as echarts from "echarts";
import { set } from "lodash";
import { useAppStore } from "@/store";
const gameValue = ref(""); // 游戏筛选变量
const publicTime = ref(""); // 时间筛选变量
const AccessChartRefs = ref(null); // 交易金额折线图
const orderChartRefs = ref(null); // 订单数折线图
const rechargedrawer = ref(false); // 充值
const store = useAppStore();
const settingStore = useSettingStore();

const divRef = ref();
const dataForm_ = reactive({
  status: "0"
});
const state: {
  charts: any[];
  apiData_: any[];
  personData_: any;
  remark: string;
  currentDateTime: string;
  workingTimeFormat: string;
  shopUrl: string;
  pauseWork: boolean;
  loadingPage: boolean;
} = reactive({
  charts: [],
  apiData_: [],
  personData_: {},
  currentDateTime: "",
  remark: "dashboard.Loading",
  workingTimeFormat: "",
  shopUrl: "",
  pauseWork: false,
  loadingPage: true
});

const router = useRouter();
const jumpRouter = (path: any, inpage?: Boolean) => {
  if (inpage) {
    router.push(path);
  } else {
    if (path) {
      window.open(path);
    }
  }
};
const tableData = ref([]);
const handleClick = (name: any) => {
  getDataList();
};
const getDataList = () => {
  tableData.value = [];
  baseService
    .get("/wallet/bill/page", {
      type: dataForm_.status,
      limit: 5,
      page: 1
    })
    .then((res) => {
      tableData.value = res.data?.list || {};
    });
};
// 余额表单变量
const dataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: dataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};

const closePreview = () => {
  dataForm.showImagePreview = false;
};
// 总数据 - 消费
const consumptionParams = reactive({
  type: 0,
  date: ""
});
const consumptionData = reactive({
  amount: 0,
  total: 0
});
const consumption = () => {
  baseService.get("/wallet/bill/date", consumptionParams).then((res) => {
    consumptionData.amount = res.data.amount;
    consumptionData.total = res.data.total;
  });
};
// 总数据 - 充值
const rechargeParams = reactive({
  type: 1,
  date: ""
});
const rechargeData = reactive({
  amount: 0,
  total: 0
});
const recharge = () => {
  baseService.get("/wallet/bill/date", rechargeParams).then((res) => {
    rechargeData.amount = res.data.amount;
    rechargeData.total = res.data.total;
  });
};
const bannerData = ref([]);
const getBanner = () => {
  state.loadingPage = true;
  baseService.get("/home/<USER>", consumptionParams).then((res) => {
    bannerData.value = res.data || [];
    state.loadingPage = false;
  });
};
getBanner();

// 统计表报数据
const echartDataList = reactive({
  UserAccess: [0, 796, 723, 634, 300, 612, 749, 774, 790, 565, 77]
});

// 监听div宽度动态修改echarts宽高
const resizeObserver = ref();
watch(divRef, (newBox, oldBox) => {
  if (newBox) {
    resizeObserver.value = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        // 处理宽度变化的逻辑
        state.charts.map((item) => item.resize());
      }
    });

    resizeObserver.value.observe(divRef.value);
  }
});

// 跳转充值页面
const toBillBalance = () => {
	router.push('/bill/specification?open=1')
}

onMounted(async () => {
  state.shopUrl = settingStore.info.pcWebsiteUrl + "/#/home";
  state.currentDateTime = commonData.currentDateTime();
  consumptionParams.date = formatDate_(new Date(), "YYYY-MM");
  rechargeParams.date = formatDate_(new Date(), "YYYY-MM");
  getDataList();
  consumption();
  recharge();
  startWork();
  state.apiData_ = await commonData.apiData();
  state.personData_ = await commonData.personData();
});

onUnmounted(() => {
  // resizeObserver.value.unobserve(divRef.value);
  resizeObserver.value ? resizeObserver.value.disconnect() : "";
});
// 格式化日期
const formatDate_ = (value: any, type: any) => {
  // 计算日期相关值
  let time = typeof value == "number" ? new Date(value) : value;
  let Y = time.getFullYear();
  let M = time.getMonth() + 1;
  let D = time.getDate();
  let h = time.getHours();
  let m = time.getMinutes();
  let s = time.getSeconds();
  let week = time.getDay();

  if (type == undefined) {
    return Y + "-" + (M < 10 ? "0" + M : M) + "-" + (D < 10 ? "0" + D : D) + " " + (h < 10 ? "0" + h : h) + ":" + (m < 10 ? "0" + m : m) + ":" + (s < 10 ? "0" + s : s);
  } else if (type == "week") {
    return week + 1;
  } else if (type == "YYYY-MM-DD") {
    return Y + "-" + (M < 10 ? "0" + M : M) + "-" + (D < 10 ? "0" + D : D);
  } else if (type == "YYYY-MM") {
    return Y + "-" + (M < 10 ? "0" + M : M);
  } else if (type == "YYYY") {
    return Y;
  }
};
// 工作时间

const d = new Date();
let workTimer: number;
const WORKING_TIME = "workingTime";
// 开始工作
const startWork = () => {
  const workingTime = Local.get(WORKING_TIME) || { date: "", startTime: 0, pauseTime: 0, startPauseTime: 0 };
  const currentDate = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate();
  const time = parseInt((new Date().getTime() / 1000).toString());

  if (workingTime.date != currentDate) {
    workingTime.date = currentDate;
    workingTime.startTime = time;
    workingTime.pauseTime = workingTime.startPauseTime = 0;
    Local.set(WORKING_TIME, workingTime);
  }

  let startPauseTime = 0;
  if (workingTime.startPauseTime <= 0) {
    state.pauseWork = false;
    startPauseTime = 0;
  } else {
    state.pauseWork = true;
    startPauseTime = time - workingTime.startPauseTime; // 已暂停时间
  }

  let workingSeconds = time - workingTime.startTime - workingTime.pauseTime - startPauseTime;

  state.workingTimeFormat = formatSeconds(workingSeconds);
  if (!state.pauseWork) {
    workTimer = window.setInterval(() => {
      workingSeconds++;
      state.workingTimeFormat = formatSeconds(workingSeconds);
    }, 1000);
  }
};

const formatSeconds = (seconds: number) => {
  var secondTime = 0; // 秒
  var minuteTime = 0; // 分
  var hourTime = 0; // 小时
  var dayTime = 0; // 天
  var result = "";

  if (seconds < 60) {
    secondTime = seconds;
  } else {
    // 获取分钟，除以60取整数，得到整数分钟
    minuteTime = Math.floor(seconds / 60);
    // 获取秒数，秒数取佘，得到整数秒数
    secondTime = Math.floor(seconds % 60);
    // 如果分钟大于60，将分钟转换成小时
    if (minuteTime >= 60) {
      // 获取小时，获取分钟除以60，得到整数小时
      hourTime = Math.floor(minuteTime / 60);
      // 获取小时后取佘的分，获取分钟除以60取佘的分
      minuteTime = Math.floor(minuteTime % 60);
      if (hourTime >= 24) {
        // 获取天数， 获取小时除以24，得到整数天
        dayTime = Math.floor(hourTime / 24);
        // 获取小时后取余小时，获取分钟除以24取余的分；
        hourTime = Math.floor(hourTime % 24);
      }
    }
  }

  result = hourTime + "小时" + ((minuteTime >= 10 ? minuteTime : "0" + minuteTime) + "分钟" + ((secondTime >= 10 ? secondTime : "0" + secondTime) + "秒"));
  if (dayTime > 0) {
    result = dayTime + "天" + result;
  }
  return result;
};
</script>

<style lang="less" scoped>
.home_page {
  box-sizing: border-box;
  width: 100%;
  padding: 0px 6px 0px 0px;
}
.system_info {
  width: 100%;
  height: 240px;
  // border: 1px solid #4165D7;
  border-radius: 8px;
  overflow: hidden;
}
.home_card {
  width: 100%;
  height: 240px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(325deg, rgba(208, 219, 255, 0.3) 0%, rgba(244, 247, 252, 0.2) 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffffff;
  backdrop-filter: blur(10px);
  // box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  padding: 12px;
}

.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.my_info {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
  flex: 1;
  margin-top: 12px;
  .info_top {
    display: flex;
    align-items: center;
    .info_top_icon {
      width: 56px;
      height: 56px;
      border-radius: 50px;
      border: 1px solid #e4e7ed;
      margin-right: 8px;
    }
    .info_top_cont {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .cont_name {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .cont_time {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 12px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: 4px;
        span {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .info_cont {
    flex: 1;
    display: flex;
    align-items: center;
    margin-top: 14px;
    .info_cont_item {
      margin-right: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .info_cont_item_value {
        font-weight: bold;
        font-size: 18px;
        color: #000000;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .info_cont_item_label {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .info_buttom {
    margin-top: 10px;
    display: flex;
    .el-button {
      flex: 1;
    }
  }
}
.recommend {
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  .recommend_top {
    padding: 12px;
    background: linear-gradient(90deg, var(--el-color-primary) 0%, #ffffff 70%);
    border-radius: 8px 8px 0px 0px;

    .recommend_top_content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 0px 0px 8px 8px;

      .recommend_left {
        .title {
          font-weight: bold;
          font-size: 16px;
          color: #ffffff;
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          padding-left: 8px;
          display: flex;
          align-items: center;
          position: relative;

          &::after {
            content: "";
            width: 2px;
            height: 22px;
            background-color: #ffffff;
            position: absolute;
            top: 0px;
            left: 0px;
          }
        }
      }
      .recommend_right {
        .toPage {
          height: 22px;
          cursor: pointer;
          font-weight: 400;
          font-size: 14px;
          color: var(--el-color-primary);
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          display: flex;
          align-items: center;
        }
        .el-icon {
          margin-left: 4px;
        }
      }
    }
  }
  .recommend_content {
    width: 100%;
    position: relative;
    background-color: #ffffff;
    border-radius: 0px 0px 8px 8px;
    display: flex;
    flex-wrap: wrap;

    &:after {
      content: "";
      display: block;
      position: absolute;
      bottom: 0px;
      width: 100%;
      border: 1px solid #fff;
      border-radius: 8px;
    }
    .recommend_content_item {
      width: 33.3%;
      padding: 24px;
      display: flex;
      align-items: flex-start;
      border-bottom: 1px solid #e4e7ed;
      border-right: 1px solid #e4e7ed;
      .item_icon {
        width: 48px;
        height: 48px;
        margin-right: 10px;
      }
      .item_content {
        .item_content_label {
          font-weight: bold;
          font-size: 16px;
          color: #303133;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .item_content_info {
          font-weight: 400;
          font-size: 14px;
          color: #909399;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin: 10px 0px;
        }
        .item_content_but {
          display: flex;
          align-items: center;
          .but_item {
            background: #f0f2f5;
            border-radius: 2px 2px 2px 2px;
            font-weight: 500;
            font-size: 14px;
            color: #909399;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            padding: 0px 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
          }
        }
      }

      &:nth-child(3n) {
        border-right: 0;
      }
    }
    .item_border {
      border-left: 1px solid #e4e7ed;
      border-right: 1px solid #e4e7ed;
    }
  }
  .recommend_buttom {
    position: relative;
    left: 0px;
    bottom: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0px 0px 8px 8px;
    background-color: #ffffff;
    padding: 12px;
    .recommend_buttom_left {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: #a8abb2;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .recommend_buttom_right {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
.bill {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 12px;
  .bill_card {
    margin-top: 12px;
    .bill_card_item {
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #ebeef5;
      .above {
        padding: 15px 12px;
        .title {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .middle {
        padding: 4px 16px;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .below {
        padding: 4px 16px 12px 16px;
        .sum_label {
          font-weight: 400;
          font-size: 12px;
          color: #606266;
          line-height: 20px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .sum_value {
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
.dataOverview {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 12px;
  .data_list {
    background: #f8f8f8;
    border-radius: 4px 4px 4px 4px;
    padding: 24px 24px 0px 24px;
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    .data_list_item {
      display: flex;
      height: 60px;
      flex-direction: column;
      justify-content: center;
      width: 33.3%;
      margin-bottom: 24px;

      .data_list_item_value {
        font-weight: bold;
        font-size: 18px;
        color: #000000;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .data_list_item_label {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: 6px;
      }
    }
  }
  .data_echarts {
    .data_echarts_item {
      display: flex;
      align-items: center;
      margin-top: 24px;
      .echarts_item_left {
        width: 112px;
        // margin-right: 12px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .label {
          font-weight: 400;
          font-size: 14px;
          color: #909399;
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .value {
          font-weight: bold;
          font-size: 20px;
          color: var(--el-color-primary);
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin: 4px 0px;
        }
        .compare {
          font-weight: 400;
          font-size: 12px;
          color: #909399;
          line-height: 12px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          display: flex;
          align-items: center;
        }
      }
      .echarts_item_right {
        // border: 1px solid red;
        flex: 1;
        height: 67px;
      }
    }
  }
}
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
:deep(.el-carousel__container) {
  height: 100%;
}
:deep(.el-carousel__indicators) {
  right: 0;
  transform: translateX(0);
  text-align: right;
  margin-right: 16px;
  li {
    padding-left: 2px !important;
    padding-right: 2px !important;
    .el-carousel__button {
      width: 8px;
      height: 4px;
      background: var(--el-color-primary-light-9);
      border-radius: 14px;
    }
    &.is-active {
      .el-carousel__button {
        background: var(--el-color-primary);
      }
    }
  }
}
</style>
