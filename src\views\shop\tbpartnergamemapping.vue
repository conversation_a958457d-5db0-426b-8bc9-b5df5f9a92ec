<template>
  <div class="mod-shop__tbpartnergamemapping">
    <div class="accountTabsConpy">
      <ul class="accountTabsUL" :style="{ height: 'auto' }">
        <li :class="state.dataForm.type == item.dictValue ? 'codeActive' : ''" @click="handleNameClick(item)" v-for="(item, index) in state.typeList" :key="index">
          <span>{{ item.dictLabel }}</span>
        </li>
      </ul>
    </div>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card" style="margin-bottom: 15px">
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <el-form-item label="合作商名称">
          <el-input v-model="state.dataForm.partnerName" placeholder="合作商名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="游戏名称">
          <el-input v-model="state.dataForm.gameName" placeholder="游戏名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="区服名称">
          <el-input v-model="state.dataForm.gameAreaName" placeholder="区服名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="state.hasPermission('shop:tbpartnergamemapping:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }} </el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="state.hasPermission('shop:tbpartnergamemapping:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }} </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="partnerName" label="合作商名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameCode" label="游戏编号" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameName" label="游戏名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameAreaCode" label="区服编码" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameAreaName" label="区服名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameAreaPcode" label="上级区服编码" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="gameAreaPname" label="上级区服名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameCode" label="合作商游戏编码" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameName" label="合作商游戏名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameAreaCode" label="合作商区服编码" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameAreaName" label="合作商区服名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameAreaPcode" label="合作商上级区服编码" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="partnerGameAreaPname" label="合作商上级区服名称" header-align="center" align="center" width="170"></el-table-column>
      <el-table-column prop="type" label="映射类型" header-align="center" align="center" width="100">
        <template v-slot="scope">
          {{ state.getDictLabel("partner_game_mapping_type", scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="创建者ID" header-align="center" align="center" width="200"> </el-table-column>
      <el-table-column prop="updater" label="更新者ID" header-align="center" align="center" width="200"> </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('shop:tbpartnergamemapping:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }} </el-button>
          <el-button v-if="state.hasPermission('shop:tbpartnergamemapping:delete')" type="primary" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }} </el-button>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { computed, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./tbpartnergamemapping-add-or-update.vue";
import { useAppStore } from "@/store";
import { getDictDataList } from "@/utils/utils";

const view = reactive({
  getDataListURL: "/shop/tbpartnergamemapping/page",
  getDataListIsPage: true,
  exportURL: "/shop/tbpartnergamemapping/export",
  deleteURL: "/shop/tbpartnergamemapping",
  deleteIsBatch: true,
  dataForm: {
    partnerName: "",
    gameName: "",
    gameAreaName: "",
    type: "1"
  },
  typeList: []
});

const state = reactive({ ...useView(view), ...toRefs(view) });
onMounted(() => {
  const store = useAppStore();
  view.typeList = getDictDataList(store.state.dicts, "partner_game_mapping_type");
});
// 顶部切换类型
const handleNameClick = (item: any) => {
  state.dataForm.type = item.dictValue;
  state.getDataList();
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
<style scoped lang="less">
.accountTabsConpy {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 4px 4px 0px 0px;
  background-color: #fff;
  // margin-bottom: 15px;

  .accountTabsUL {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0px;
    padding-left: 0px;

    li {
      padding: 0 20px 15px;
      margin: 15px 0 0;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      box-sizing: border-box;
      transition: all 0.1s;
    }

    .codeActive {
      color: var(--el-color-primary);
      border-bottom: 2px solid var(--el-color-primary);
    }
  }

  .icons {
    padding: 0 15px;
    cursor: pointer;

    .el-icon {
      font-size: 18px;
      margin-top: 18px;
      color: #606266 !important;
    }
  }
}
</style>
