<template>
    <div class="mod-sys__post">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:post:save')" type="primary" @click="addOrUpdateHandle()">{{
                        $t("add")
                        }}</el-button>
                    <el-button v-if="state.hasPermission('sys:post:delete')" type="danger" @click="state.deleteHandle()">{{
                        $t("deleteBatch") }}</el-button>
                </template>
                <template #header-right>
                    <el-input v-model="state.dataForm.postCode" :placeholder="$t('post.postCode')" clearable></el-input>
                    <el-input v-model="state.dataForm.postName" :placeholder="$t('post.postName')" clearable></el-input>
                    <ny-select v-model="state.dataForm.status" dict-type="post_status" style="width: 200px;" :placeholder="$t('post.status')"></ny-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                
                <!-- 状态 -->
                <template #status="{row}">
                    {{ state.getDictLabel("post_status", row.status) }}
                </template>

                <!-- 操作 -->
                <template #operation="{row}">
                    <el-button v-if="state.hasPermission('sys:post:update')" type="primary" text bg
                        @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:post:delete')" type="danger" text bg
                        @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
            
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./post-add-or-update.vue";

const view = reactive({
    getDataListURL: "/sys/post/page",
    getDataListIsPage: true,
    deleteURL: "/sys/post",
    deleteIsBatch: true,
    dataForm: {
        postCode: "",
        postName: "",
        status: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "postCode",
        label: "岗位编码"
    },
    {
        prop: "postName",
        label: "岗位名称"
    },
    {
        prop: "sort",
        label: "排序"
    },
    {
        prop: "status",
        label: "岗位状态"
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

// 重置操作
const getResetting = () => {
    state.dataForm.postCode = "";
    state.dataForm.postName = "";
    state.dataForm.status = "";
    state.getDataList();
}

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
