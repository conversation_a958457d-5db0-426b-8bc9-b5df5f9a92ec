<template>
  <el-dialog :footer="null" v-model="visible" title="编辑" width="800">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <el-descriptions style="width: 100%; margin-bottom: 50px" border :column="2">
        <el-descriptions-item label="部门" :span="2">
          <el-form-item label="部门" prop="deptId">
            <ny-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="备注">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="请输入备注" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="启用状态">
          <el-form-item label="启用" prop="enableStatus"> <el-switch inline-prompt v-model="dataForm.enableStatus" :active-value="1" :inactive-value="2" active-text="是" inactive-text="否" /> </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-dialog>
</template>  
    
    <script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";

const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);

const rules = {
  value1: [{ required: true, message: "请输入卡板编码", trigger: "blur" }],
  value2: [{ required: true, message: "请选择租户", trigger: "blur" }]
};

const init = (row: any) => {
  visible.value = true;
  dataForm.value = row
  // getTenant()
  // if (id) {
  //   getDetails(id);
  //   return;
  // }
  // 初始化卡位
  // dataForm.value.value5 = [];
  // for (let index = 0; index < 16; index++) {
  //   dataForm.value.value5[index] = undefined;
  // }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get("/mobile/mobileDevice/" + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 租户下拉
const options = ref([]);
const getTenant = () => {
  options.value = [];
  baseService.get("").then((res) => {
    if (res.code === 0) {
      options.value = res.data;
    }
  });
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/mobile/mobileDevice", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
    
  <style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>