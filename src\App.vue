<script lang="ts">
import "@/assets/css/app.less";
import "@/assets/theme/index.less";
import "@/assets/theme/mobile.less";
import FullscreenLayout from "@/layout/fullscreen-layout.vue";
import Layout from "@/layout/index.vue";
import { ElConfigProvider } from "element-plus";
import { defineComponent, onMounted, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { useImStore } from "./store/im";
import app from "./constants/app";
import { EPageLayoutEnum, EThemeColor, EThemeSetting } from "./constants/enum";
import { getLocaleLang, supportLangs } from "./i18n";
import { IObject } from "./types/interface";
import { getThemeConfigCache, setThemeColor, updateTheme } from "./utils/theme";
import baseService from "@/service/baseService";

export default defineComponent({
  name: "App",
  components: { Layout, FullscreenLayout, [ElConfigProvider.name]: ElConfigProvider },
  setup() {
    const store = useAppStore();
    const imStore = useImStore();
    const sttingInfo = useSettingStore();
    const route = useRoute();
    const router = useRouter();
    const { t, locale } = useI18n();
    const state = reactive({
      layout: location.href.includes("pop=true") ? EPageLayoutEnum.fullscreen : EPageLayoutEnum.page
    });
    const onInitLang = (vl: string, oldVl?: string) => {
      sttingInfo.getInfo();

      setTimeout(() => {
        window.document.querySelector("html")?.setAttribute("lang", vl);
        document.title = sttingInfo.info.backendTitle ? sttingInfo.info.backendTitle : t("ui.app.productName");
        // 动态更新标签图标
        let link: any = document.querySelector("link[rel*='icon']") || document.createElement("link");
        link.type = "image/x-icon";
        link.rel = "shortcut icon";
        link.href = sttingInfo.info.backendLogo ? sttingInfo.info.backendLogo : "";
        document.getElementsByTagName("head")[0].appendChild(link);
        // console.log("====== pinia ======", sttingInfo.info.adminName)
      }, 300);

      if (oldVl && route.path !== "/login") {
        store.updateState({ appIsReady: false });
        location.reload();
      }
    };
    onMounted(() => {
      //读取主题色缓存
      const themeCache = getThemeConfigCache();
      const themeColor = themeCache[EThemeSetting.ThemeColor];
      setThemeColor(EThemeColor.ThemeColor, themeColor);
      updateTheme(themeColor);
      onInitLang(getLocaleLang());


      // 监听退出登录
      const logChannel = new BroadcastChannel('doLogout')
      logChannel.onmessage = (e) => {
        if (e.data === 'logOut') {
          router.push('/login');
          setTimeout(() => {
            location.reload();
          }, 200);
        }
      }
    });
    watch(() => locale.value, onInitLang);
    watch(
      () => [route.path, route.query, route.fullPath],
      ([path, query, fullPath]) => {
        store.updateState({ activeTabName: fullPath });
        state.layout = app.fullscreenPages.includes(path as string) || (query as IObject)["pop"] ? EPageLayoutEnum.fullscreen : EPageLayoutEnum.page;
      }
    );
    return {
      store,
      state,
      pageTag: EPageLayoutEnum.page,
      locale: supportLangs[locale.value].el
    };
  }
});
</script>
<template>
  <el-config-provider :locale="locale">
    <div v-if="!store.state.appIsRender" v-loading="true" :element-loading-fullscreen="true" :element-loading-lock="true" style="width: 100vw; height: 100vh; position: absolute; top: 0; left: 0; z-index: 99999; background: #fff"></div>
    <template v-if="store.state.appIsReady">
      <layout v-if="state.layout === pageTag"> </layout>
      <fullscreen-layout v-else></fullscreen-layout>
    </template>
  </el-config-provider>
</template>
<style lang="less">
// 解决缩放导致的tooltip错位
// .el-table {
//   transform: scale(1);
//   transform-origin: 0 0;
// }

ul,
li {
  padding: 0;
  list-style: none;
}
</style>
