<script lang="ts">
import app from "@/constants/app";
import { IObject } from "@/types/interface";
import { EMitt, EThemeSetting } from "@/constants/enum";
import emits from "@/utils/emits";
import { getValueByKeys } from "@/utils/utils";
import { getThemeConfigCacheByKey } from "@/utils/theme";
import { defineComponent, onMounted, reactive, ref, watch } from "vue";
import { useRoute, useRouter, RouteLocationMatched } from "vue-router";
import { useAppStore } from "@/store";
import Tabs from "./tabs.vue";

/**
 * 业务内容视图框架
 */
export default defineComponent({
  name: "View",
  components: { Tabs },
  setup() {
    const store = useAppStore();
    const route = useRoute();
    const router = useRouter();
    const { currentRoute } = router;
    const firstRoute = (router.options.routes[0] || {}) as RouteLocationMatched;
    const home: RouteLocationMatched = firstRoute.children && firstRoute.children.length > 0 ? (firstRoute.children[0] as RouteLocationMatched) : firstRoute;
    const state = reactive({
      openTabsPage: getThemeConfigCacheByKey(EThemeSetting.OpenTabsPage)
    });
    const routerKeys = ref({} as any);
    const breadcrumbs = ref<IObject[]>([]);
    const pathTitle = ref(<any>"");
    const isTitle = ref(false);
    const titleContent = ref("恒星后台管理系统，让账号交易变的更安全");
    emits.on(EMitt.OnSetThemeTabsPage, (vl) => {
      state.openTabsPage = vl;
    });
    emits.on(EMitt.OnReloadTabPage, () => {
      routerKeys.value[route.fullPath] = new Date().getTime();
    });

    // 不显示标题的页面
    const isNoTitle = ["/", "/home", "/desk/index", "/statReport/echarts/chartList", "/shop/partnerPushDetails/index", "/tenantMenu/api/management", "/plugin/paid/paidfunction", "/utilityTools/scriptListing/index"];

    watch(
      () => currentRoute.value,
      () => {
        breadcrumbs.value = currentRoute.value.path !== home.path ? getValueByKeys(currentRoute.value, "meta.matched", []) : [];
        pathTitle.value = route.path == "/user/center" ? (store.state.user.tenantCode == 10000 ? "个人中心" : "商家中心") : currentRoute.value.meta.title;
        isTitle.value = isNoTitle.includes(route.path) ? false : true;
        titleContent.value = currentRoute.value.path === "/tenantMenu/shopCenter/shop/shop" ? "展示商品信息的集合，包括名称、价格、描述等关键属性，方便您的浏览和选择" : "恒星后台管理系统，让账号交易变的更安全";
      },
      { immediate: true }
    );
    onMounted(() => {
      // breadcrumbs.value = currentRoute.value.path !== home.path ? getValueByKeys(currentRoute.value, "meta.matched", []) : [];
      // pathTitle.value = route.path == "/user/center" ? (store.state.user.tenantCode == 10000 ? "个人中心" : "商家中心") : currentRoute.value.meta.title;
      // isTitle.value = route.path == "/" || route.path == "/home" || route.path == "/desk/index" || route.path == "/statReport/echarts/chartList" ? false : true;
      // console.log(currentRoute.value)
    });
    return { state, store, enabledKeepAlive: app.enabledKeepAlive, routerKeys, breadcrumbs, pathTitle, isTitle, titleContent };
  }
});
</script>

<template>
  <tabs v-if="state.openTabsPage" :tabs="store.state.tabs" :activeTabName="store.state.activeTabName"></tabs>
  <div class="rr-view-ctx">
    <div :class="isTitle ? 'XscrollBasePage' : ''">
      <div class="base_view_page">
        <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
        <div :class="isTitle ? 'base_view_card' : ''">
          <div class="router_info" v-if="isTitle">
            <span class="info_name">{{ pathTitle }}</span>
            <span class="info_line">|</span>
            <span class="info_blurb">{{ titleContent }}</span>
          </div>
          <router-view v-slot="{ Component }">
            <keep-alive v-if="enabledKeepAlive" :max="10">
              <component :is="Component" :key="routerKeys[$route.fullPath] || $route.fullPath" />
            </keep-alive>
            <component :is="Component" v-if="!enabledKeepAlive" />
          </router-view>
        </div>

        <!-- </el-card> -->
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.XscrollBasePage {
  background-color: #fff;
  padding-top: 20px;
  border-radius: 8px;
  height: 100%;
  overflow: hidden;
  .router_info {
    padding-top: 0 !important;
  }
}
.base_view_page {
  height: 100%;
  overflow: auto;
  // background-color: #fff;

  // scrollbar-width: none;
  // &::-webkit-scrollbar {
  //   display: none;
  // }

  border-radius: 8px;
  .base_view_card {
    min-height: 100%;
    background-color: #fff;
    border-radius: 8px;
    .router_info {
      display: flex;
      align-items: center;
      padding: 20px 20px 0px 20px;
      .info_name {
        font-weight: bold;
        font-size: 20px;
        color: #303133;
        line-height: 28px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .info_line {
        color: #e4e7ed;
        margin: 0px 12px;
      }
      .info_blurb {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
