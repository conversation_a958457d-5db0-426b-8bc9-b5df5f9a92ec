<template>
  <div class="userCard">
    <div class="welcome">{{ store.state.user.realName }}，{{ timeTipText }}，欢迎回来！</div>
    <div class="info">
      <img src="../../assets/images/home_icon1.png" class="info_icon" />
      <div class="info_top">
        <span class="user_name">管理员</span>
        <span class="user_time"
          >您今天已工作了<el-text type="primary">{{ state.workingTimeFormat }}</el-text></span
        >
      </div>
      <el-button type="primary" plain @click="jumpRouter(state.shopUrl, false)">进入商城主页</el-button>
    </div>
    <div class="info_cont">
      <div class="info_cont_item">
        <div class="info_cont_item_value">{{ formatCurrency(state.personData_.balance || 0) }}</div>
        <div class="info_cont_item_label">账户余额(星币)</div>
      </div>
      <div class="info_cont_item">
        <div class="info_cont_item_value">{{ formatCurrency(state.personData_.consume || 0) }}</div>
        <div class="info_cont_item_label">总消费(星币)</div>
      </div>
      <div style="margin-left: 8px">
        <el-button
          style="width: 80px"
          type="primary"
          @click="toBillBalance"
          >充值</el-button
        >
      </div>
    </div>
    <!-- 充值 -->
    <el-dialog v-model="rechargedrawer" width="480" title="充值" :close-on-click-modal="false" :close-on-press-escape="false" @close="rechargedrawer = false">
      <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label> <div>支付宝账号</div> </template>
          <EMAIL>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label> <div>支付宝账户主体</div> </template>
          枣庄努运企业管理有限公司
        </el-descriptions-item>
      </el-descriptions>
      <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top">
        <el-form-item label="支付宝订单号" prop="orderNum">
          <template #label>
            <span style="margin-right: 10px">支付宝订单号</span>
            <el-text type="primary" @click="dataForm.showImagePreview = true">如何查看订单号？</el-text>
          </template>
          <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="rechargedrawer = false">取消</el-button>
          <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
        </div>
      </template>
      <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="dataForm.showImagePreview=false" />
    </el-dialog>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from "vue";
import commonData from "@/views/desk/index.ts";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { formatDate, formatCurrency, Local } from "@/utils/method";
const router = useRouter();
const store = useAppStore();
const settingStore = useSettingStore();
const jumpRouter = (path: any, inpage?: Boolean) => {
  if (inpage) {
    router.push(path);
  } else {
    window.open(path);
  }
};
const state: {
  charts: any[];
  remark: string;
  personData_: any;
  workingTimeFormat: string;
  pauseWork: boolean;
  shopUrl: string;
} = reactive({
  charts: [],
  remark: "dashboard.Loading",
  workingTimeFormat: "",
  personData_: {},
  pauseWork: false,
  shopUrl: ""
});
const timeTipText = ref();
const timeTip = () => {
  const date = new Date();
  const hour = date.getHours();
  if (hour >= 0 && hour < 12) {
    timeTipText.value = "上午好";
  } else if (hour === 12) {
    timeTipText.value = "中午好";
  } else if (hour > 12 && hour < 18) {
    timeTipText.value = "下午好";
  } else {
    timeTipText.value = "晚上好";
  }
};
timeTip();
// 充值
const rechargedrawer = ref(false);
// 余额表单变量
const dataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: dataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};
// 工作时间

const d = new Date();
let workTimer: number;
const WORKING_TIME = "workingTime";
// 开始工作
const startWork = () => {
  const workingTime = Local.get(WORKING_TIME) || { date: "", startTime: 0, pauseTime: 0, startPauseTime: 0 };
  const currentDate = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate();
  const time = parseInt((new Date().getTime() / 1000).toString());

  if (workingTime.date != currentDate) {
    workingTime.date = currentDate;
    workingTime.startTime = time;
    workingTime.pauseTime = workingTime.startPauseTime = 0;
    Local.set(WORKING_TIME, workingTime);
  }

  let startPauseTime = 0;
  if (workingTime.startPauseTime <= 0) {
    state.pauseWork = false;
    startPauseTime = 0;
  } else {
    state.pauseWork = true;
    startPauseTime = time - workingTime.startPauseTime; // 已暂停时间
  }

  let workingSeconds = time - workingTime.startTime - workingTime.pauseTime - startPauseTime;

  state.workingTimeFormat = formatSeconds(workingSeconds);
  if (!state.pauseWork) {
    workTimer = window.setInterval(() => {
      workingSeconds++;
      state.workingTimeFormat = formatSeconds(workingSeconds);
      timeTip();
    }, 1000);
  }
};

const formatSeconds = (seconds: number) => {
  var secondTime = 0; // 秒
  var minuteTime = 0; // 分
  var hourTime = 0; // 小时
  var dayTime = 0; // 天
  var result = "";

  if (seconds < 60) {
    secondTime = seconds;
  } else {
    // 获取分钟，除以60取整数，得到整数分钟
    minuteTime = Math.floor(seconds / 60);
    // 获取秒数，秒数取佘，得到整数秒数
    secondTime = Math.floor(seconds % 60);
    // 如果分钟大于60，将分钟转换成小时
    if (minuteTime >= 60) {
      // 获取小时，获取分钟除以60，得到整数小时
      hourTime = Math.floor(minuteTime / 60);
      // 获取小时后取佘的分，获取分钟除以60取佘的分
      minuteTime = Math.floor(minuteTime % 60);
      if (hourTime >= 24) {
        // 获取天数， 获取小时除以24，得到整数天
        dayTime = Math.floor(hourTime / 24);
        // 获取小时后取余小时，获取分钟除以24取余的分；
        hourTime = Math.floor(hourTime % 24);
      }
    }
  }

  result = hourTime + "小时" + ((minuteTime >= 10 ? minuteTime : "0" + minuteTime) + "分钟" + ((secondTime >= 10 ? secondTime : "0" + secondTime) + "秒"));
  if (dayTime > 0) {
    result = dayTime + "天" + result;
  }
  return result;
};
onMounted(async () => {
  startWork();
  state.shopUrl = settingStore.info.pcWebsiteUrl + "/#/home";
  state.personData_ = await commonData.personData();
});

// 跳转充值页面
const toBillBalance = () => {
	router.push('/bill/specification?open=1')
}
</script>

<style lang='less' scoped>
.userCard {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  .welcome {
    font-weight: bold;
    font-size: 14px;
    color: var(--el-color-primary);
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info {
    display: flex;
    align-items: center;
    padding: 23px 0px;
    border-bottom: 1px solid #e4e7ed;
    .info_icon {
      width: 56px;
      height: 56px;
      border-radius: 50px;
      border: 1px solid #e4e7ed;
    }
    .info_top {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 8px;
      .user_name {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 4px;
      }
      .user_time {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .info_cont {
    display: flex;
    align-items: center;
    margin-top: 25px;
    margin-bottom: 18px;
    .info_cont_item {
      flex: 1;
      display: flex;
      margin-right: 16px;
      flex-direction: column;
      justify-content: center;
      .info_cont_item_value {
        font-weight: bold;
        font-size: 18px;
        color: #000000;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .info_cont_item_label {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .info_buttom {
    margin-top: 10px;
    display: flex;
    .el-button {
      flex: 1;
    }
  }
}
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
</style>