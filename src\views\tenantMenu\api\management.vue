<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div class="router_info">
        <span class="info_name">{{ currentRoute.meta.title }}</span>
        <span class="info_line">|</span>
        <span class="info_blurb">恒星后台管理系统，让账号交易变的更安全</span>
      </div>
      <div class="pushProcess" @click="refreshSchedule()">
        <el-tooltip
          class="box-item"
          effect="light"
          content="点击获取最新进度"
          placement="top"
        >
        <span
          >推送进度：<span style="color: #f56c6c">{{ pushData.syncingTaskNum || 0 }}</span
          >/{{ pushData.totalTaskNum || 0 }}</span
        >
        </el-tooltip>
        <el-button style="margin-left: 16px" type="warning" @click="handleOpenError">推送失败记录</el-button>
      </div>
    </div>
    <div class="mainBox">
      <div class="leftPart">
        <div class="title">选择合作商</div>
        <el-select v-model="state.dataForm.partnerId" @change="handleselectPartner" clearable style="width: 186px; margin-bottom: 10px">
          <el-option v-for="item in curAllPartnerList" :key="item.id" :label="item.companyName" :value="item.id" />
        </el-select>
        <div class="title">选择游戏</div>
        <div class="scrollWrap lower">
          <ul class="menuUl">
            <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
              <el-tooltip effect="dark" :content="item.gameName" placement="top-start">
                <span>{{ item.gameName }}</span>
              </el-tooltip>
            </li>
          </ul>
        </div>
      </div>
      <div class="rightPart">
        <ny-flod-tab class="newTabSty" @change="state.dataForm.pushStatus ='',state.getDataList()" :list="statusList" v-model="state.dataForm.recordType" value="value" label="label"> </ny-flod-tab>
        <ny-table ref="nyTableRef" :state="state" :columns="columns" @sortableChange="sortableChange" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
          <template #header>
            <el-button :disabled="state.dataListSelections?.length < 1" type="primary" @click="pushSelect()">推送所选</el-button>
          </template>
          <template #header-right>
            <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item>
                <el-input v-model="state.dataForm.searchParam" placeholder="请输入商品标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-select v-if="state.dataForm.recordType == 1" v-model="state.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                  <el-option label="未推送" value="0"></el-option>
                  <el-option label="推送成功" value="1"></el-option>
                  <el-option label="推送失败" value="2"></el-option>
                </el-select>

                <el-select v-else v-model="state.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                  <el-option label="未下架" value="0"></el-option>
                  <el-option label="下架成功" value="1"></el-option>
                  <el-option label="下架失败" value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-form>
          </template>
          <template #status="{ row }">
            <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
            <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
            <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
            <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
            <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
            <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
            <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
          </template>
          <template #pushStatus="{ row }">
            <block v-if="state.dataForm.recordType == 1">
              <el-tag v-if="row.pushStatus == 0" type="warning">未推送</el-tag>
              <el-tag v-if="row.pushStatus == 1" type="primary">推送成功</el-tag>
              <el-tag v-if="row.pushStatus == 2" type="danger">推送失败</el-tag>
            </block>
            <block v-else>
              <el-tag v-if="row.pushStatus == 0" type="warning">未下架</el-tag>
              <el-tag v-if="row.pushStatus == 1" type="primary">下架成功</el-tag>
              <el-tag v-if="row.pushStatus == 2" type="danger">下架失败</el-tag>
            </block>
          </template>
          <template #showLog="{ row }">
            <el-button @click="handleOpenLog(row)" type="primary" link>查看</el-button>
          </template>
          <template #compensation="{ row }">
            {{ ["不可包赔", "可买包赔", "永久包赔"][+row.compensation] }}
          </template>
        </ny-table>
      </div>
    </div>
    <!-- 推送失败记录 -->
    <el-dialog v-model="dialogForm.visibleError" :title="dialogForm.titleError" width="1200" :footer="null" @close="handleCloseError">
      <ny-table :state="stateError" :columns="columns" @sortableChange="sortableChangeError" @pageSizeChange="stateError.pageSizeChangeHandle" @pageCurrentChange="stateError.pageCurrentChangeHandle" @selectionChange="stateError.dataListSelectionChangeHandle">
        <template #header>
          <el-button :disabled="stateError.dataListSelections?.length < 1" type="primary" @click="pushErrorSelect()">重新推送</el-button>
        </template>
        <template #header-right>
          <el-form :inline="true" :model="stateError.dataForm" @keyup.enter="stateError.getDataList()">
            <el-form-item>
              <el-input v-model="stateError.dataForm.searchParam" placeholder="请输入商品标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input>
            </el-form-item>
            <el-button type="primary" @click="stateError.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getErrorResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
          <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
          <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
          <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
        </template>
        <template #pushStatus="{ row }">
          <el-tag v-if="row.pushStatus == '未推送'" type="warning">未推送</el-tag>
          <el-tag v-if="row.pushStatus == '推送成功'" type="primary">推送成功</el-tag>
          <el-tag v-if="row.pushStatus == '推送失败'" type="danger">推送失败</el-tag>
        </template>
        <template #showLog="{ row }">
          <el-button @click="handleOpenLog(row, row.partnerId)" type="primary" link>查看</el-button>
        </template>
        <template #compensation="{ row }">
          {{ ["不可包赔", "可买包赔", "永久包赔"][+row.compensation] }}
        </template>
      </ny-table>
    </el-dialog>
    <!-- 查看日志 -->
    <el-dialog v-model="dialogForm.visibleLog" :title="dialogForm.titleLog" width="1200" :footer="null" @close="handleCloseLog">
      <ny-table :state="stateLog" :showColSetting="false" :columns="columnsLog" @pageSizeChange="stateLog.pageSizeChangeHandle" @pageCurrentChange="stateLog.pageCurrentChangeHandle" @selectionChange="stateLog.dataListSelectionChangeHandle">
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
          <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
          <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
          <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
        </template>
        <template #pushStatus="{ row }">
          <el-tag v-if="row.pushStatus == '未推送'" type="warning">未推送</el-tag>
          <el-tag v-if="row.pushStatus == '推送成功'" type="primary">推送成功</el-tag>
          <el-tag v-if="row.pushStatus == '推送失败'" type="danger">推送失败</el-tag>
        </template>
        <template #errorMessage="{ row }">
          <div style="display: flex" v-if="row.errorMessage">
            <div style="width: 240px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{row.errorMessage}}</div>
            <el-button @click="handleOpen('推送信息', row.errorMessage)" type="primary" link>查看</el-button>
          </div>
          <span v-else>-</span>
        </template>
        <template #requestParam="{ row }">
          <div style="display: flex" v-if="row.requestParam">
            <div class="sle">{{row.requestParam}}</div>
            <el-button @click="handleOpen('请求参数', row.requestParam)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #responseBody="{ row }">
          <div style="display: flex" v-if="row.responseBody">
            <div class="sle">{{row.responseBody}}</div>
            <el-button @click="handleOpen('返回参数', row.responseBody)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #requestUrl="{ row }">
          <div style="display: flex" v-if="row.requestUrl">
            <div class="sle">{{row.requestUrl}}</div>
            <el-button @click="handleOpen('请求地址', row.requestUrl)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #pushTime="{ row }">
          {{ formatTimeStamp(+row.pushTime) }}
        </template>
      </ny-table>
    </el-dialog>
    <!-- 查看推送详情 -->
    <el-dialog v-model="dialogForm.visible" :title="dialogForm.title" width="800" :footer="null" @close="handleClose">
      <div style="height: 422px; overflow-y: scroll">
        {{ dialogForm.content || "-" }}
      </div>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, defineExpose, onMounted } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { formatTimeStamp } from "@/utils/method";
import { useRouter } from "vue-router";
const router = useRouter();
const { currentRoute } = router;
// NOTE: 只对了合作商的接口
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  // {
  //   prop: "partnerName",
  //   label: "合作商名称",
  //   minWidth: 100
  // },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 100
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 120
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: 120
  },
  {
    prop: "accountSourceName",
    label: "商品类型",
    minWidth: 120
  },
  {
    prop: "title",
    label: "商品标题",
    minWidth: 249
  },
  {
    prop: "acquisitionPrice",
    label: "回收价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "120",
    sortable: "custom"
  },
  {
    prop: "status",
    label: "商品状态",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 120
  },
  {
    prop: "showLog",
    label: "查看日志",
    minWidth: 120
  },
  {
    prop: "compensation",
    label: "包赔",
    minWidth: 120
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 180
  }
]);
const columnsLog = reactive([
  {
    prop: "errorMessage",
    label: "推送信息",
    minWidth: 200,
    showOverflowTooltip: false
  },
  {
    prop: "requestParam",
    label: "请求参数",
    minWidth: 120
  },
  {
    prop: "responseBody",
    label: "返回参数",
    minWidth: 120
  },
  {
    prop: "requestUrl",
    label: "请求地址",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 100
  },
  {
    prop: "pushTime",
    label: "推送时间",
    minWidth: 140
  }
]);
const view = reactive({
  getDataListURL: "/apiSync/shopSyncList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    gameId: "",
    searchParam: "",
    pushStatus: "",
    recordType: 1
  }
});
const viewError = reactive({
  createdIsNeed: false,
  getDataListURL: "/apiSync/shopSyncFailRecordList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    gameId: "",
    searchParam: "",
    pushStatus: "",
    recordType: 1
  }
});
const viewLog = reactive({
  createdIsNeed: false,
  getDataListURL: "/apiSync/shopSyncRecordList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    shopId: "",
    recordType: 1
  }
});
const nyTableRef = ref();
const state = reactive({ ...useView(view), ...toRefs(view) });
const stateLog = reactive({ ...useView(viewLog), ...toRefs(viewLog) });
const stateError = reactive({ ...useView(viewError), ...toRefs(viewError) });
// NOTE: 合作商列表
const curAllPartnerList = ref([]);
// NOTE: 游戏列表
const allSelectArr = ref([]);
// NOTE: 弹窗
const dialogForm = reactive({
  visible: false,
  title: "",
  content: "",
  visibleLog: false,
  titleLog: "查看日志",
  visibleError: false,
  titleError: "推送失败记录"
});
const statusList = ref([
  {
    value: 1,
    label: "上架记录"
  },
  {
    value: 2,
    label: "下架记录"
  }
]);
const handleOpen = (title: any, content: any) => {
  dialogForm.visible = true;
  dialogForm.title = title;
  dialogForm.content = content;
};
const handleClose = () => {
  dialogForm.visible = false;
  dialogForm.content = "";
};
const handleOpenLog = (content: any, partnerId?: any) => {
  dialogForm.visibleLog = true;
  dialogForm.titleLog = "查看日志-" + content.gameName + "-" + content.code;
  stateLog.dataForm.shopId = content.id;
  stateLog.dataForm.partnerId = partnerId ? partnerId : state.dataForm.partnerId;
  stateLog.dataForm.recordType = state.dataForm.recordType;
  stateLog.getDataList();
};
const handleCloseLog = () => {
  dialogForm.visibleLog = false;
  dialogForm.titleLog = "查看日志";
};
const handleOpenError = () => {
  dialogForm.visibleError = true;
  stateError.getDataList();
};
const handleCloseError = () => {
  dialogForm.visibleError = false;
};
// 推送所选
const pushSelect = () => {
  let ids = state.dataListSelections?.map((ele) => ele.id);
  baseService.post("/apiSync/submitSyncShopTask", { partnerId: state.dataForm.partnerId, shopIdList: ids, type: state.dataForm.recordType }).then((res) => {
    ElMessage.success("操作成功！");
    state.getDataList();
    refreshSchedule()
    nyTableRef.value.clearSelection()
  });
};
const pushErrorSelect = () => {
  let recordIdList = stateError.dataListSelections?.map((ele) => ele.recordId);
  baseService.post("/apiSync/syncRetry", recordIdList).then((res) => {
    ElMessage.success("操作成功！");
    stateError.getDataList();
    nyTableRef.value.clearSelection()
  });
};
// 重置
const getResetting = () => {
  view.dataForm.searchParam = "";
  view.dataForm.pushStatus = "";
  state.getDataList();
};
const getErrorResetting = () => {
  stateError.dataForm.searchParam = "";
  stateError.getDataList();
};
// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  view.dataForm.orderBy = [
    {
      field: "",
      isAsc: false
    }
  ];
  view.dataForm.orderBy[0].field = prop;
  view.dataForm.orderBy[0].isAsc = order == "ascending";
  if (order == null) {
    delete view.dataForm.orderBy;
  }
  state.getDataList();
};
const sortableChangeError = ({ order, prop }: any) => {
  viewError.dataForm.orderBy = [
    {
      field: "",
      isAsc: false
    }
  ];
  viewError.dataForm.orderBy[0].field = prop;
  viewError.dataForm.orderBy[0].isAsc = order == "ascending";
  if (order == null) {
    delete viewError.dataForm.orderBy;
  }
  stateError.getDataList();
};
// 获取推送进度
const pushData = reactive({
  totalTaskNum: 0, //总任务
  syncingTaskNum: 0 //正在进行中
});
const getProcess = () => {
  refreshSchedule();
};
const refreshSchedule = () =>{
  baseService.get("/apiSync/syncSchedule?type=" + state.dataForm.recordType).then((res) => {
    pushData.totalTaskNum = res.data?.totalTaskNum;
    pushData.syncingTaskNum = res.data?.syncingTaskNum;
  });
}

// 获取合作商数据
const getSelectInfo = () => {
  baseService.get("/partner/partner/partnerList").then((res) => {
    if (res.code == 0) {
      curAllPartnerList.value = res.data || [];
      if (curAllPartnerList.value.length > 0) {
        state.dataForm.partnerId = curAllPartnerList.value[0].id;
        getGameList(state.dataForm.partnerId);
      }
    }
  });
};
// 切换平台获取游戏+更新table数据
const handleselectPartner = (id: any) => {
  state.dataForm.partnerId = id || undefined;
  state.dataForm.gameId = undefined;
  nyTableRef.value.clearSelection()
  getGameList(state.dataForm.partnerId);
};
// 获取游戏列表
const getGameList = (partnersid: any) => {
  baseService.get("/partner/partner/partnerMappingGameList", { partnerId: partnersid }).then((res_) => {
    if (res_.code == 0) {
      allSelectArr.value = res_.data;
      if (res_.data.length > 0) {
        // 获取列表
        handleselectGame(res_.data[0].gameId);
      }
    }
  });
};
const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  nyTableRef.value.clearSelection()
  state.getDataList();
};
onMounted(() => {
  getSelectInfo();
  getProcess();
});
</script>
<style lang="less" scoped>
ul {
  padding-left: 0;
  margin-top: 0;
}
.mainBox {
  display: flex;

  .leftPart {
    width: 186px;
    margin-right: 12px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .scrollWrap {
      border-radius: 4px;
      border: 1px solid #ebeef5;
      height: calc(100vh - 260px);
      overflow: auto;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .menuUl {
        .menuLi {
          cursor: pointer;
          padding: 20px;
          word-break: keep-all;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: Inter, Inter;
          font-weight: 400;
          font-size: 12px;
          color: #303133;
          line-height: 14px;
          &.active {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
          }
        }
      }

      &.lower {
        height: calc(100vh - 330px);
      }
    }
  }

  .rightPart {
    flex: 1;
    overflow: hidden;
  }
}
.el-tag {
  border: 1px solid;
}
.router_info {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.pushProcess {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}
</style>
