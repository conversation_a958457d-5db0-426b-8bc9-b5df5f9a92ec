<template>
  <div class="uploadFileStyle">
    <el-upload
      :class="{ 'hide-upload': previousProps.fileList.length && previousProps.limit == 1 && !alwaysShow }"
      ref="fileUploader"
      class="file-uploader"
      v-model:file-list="previousProps.fileList"
      :limit="previousProps.limit > 1 ? previousProps.limit : ''"
      :accept="previousProps.accept"
      :multiple="previousProps.multiple"
      :action="url"
      :drag="previousProps.isDrag"
      :list-type="listType"
      :show-file-list="previousProps.showFileList"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      :auto-upload="!isContract"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
    >
      <div v-if="previousProps.isSelfSty" class="BtnSty" :style="{ width: widthUpload, height: heightUpload }">
        <el-icon class="el-icon--upload" style="color: var(--el-color-primary)"><upload-filled /></el-icon>
        <div v-if="isOneLine">
          <span style="font-weight: 400; font-size: 14px; color: #909399; line-height: 20px; margin-bottom: 4px; margin-right: 4px">{{ uploadPreText }}</span>
          <span style="color: var(--el-color-primary)">点击上传</span>
        </div>
        <template v-else>
          <span style="font-weight: 400; font-size: 12px; color: #909399; line-height: 20px; margin-bottom: 4px">{{ uploadPreText }}</span>
          <span style="color: var(--el-color-primary)">点击上传</span>
        </template>
      </div>
      <slot v-else name="content"></slot>
      <template #tip>
        <div class="el-upload__tip" v-if="previousProps.tip && previousProps.tip.length > 0">
          <el-text type="warning"><div v-html="previousProps.tip"></div></el-text>
        </div>
      </template>
    </el-upload>
    <!-- <el-dialog v-model="previousProps.dialogVisible">
      <img w-full style="width: 100%" :src="previousProps.dialogImageUrl" alt="Preview Image" />
    </el-dialog> -->
  </div>
</template>
<script lang="ts">
import type { UploadInstance, UploadRawFile } from "element-plus";
import { ref, reactive, computed, defineExpose, defineComponent, watch, PropType, nextTick } from "vue";
import { ElMessage, UploadProps, UploadUserFile, genFileId } from "element-plus";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";

export default defineComponent({
  name: "NyUploadFile",
  emits: ["urlArr", "update:fileSrc", "loadData"],
  props: {
    listType: {
      type: String,
      required: false,
      default: "text"
    },
    tip: {
      type: String,
      required: false,
      default: "建议格式为JPEG、PNG，2M以内"
    },
    uploadPreText: {
      type: String,
      required: false,
      default: "拖动文件到此处或" //将图片拖到此处，或
    },
    isOneLine: {
      type: Boolean,
      required: false,
      default: false
    },
    isSelfSty: {
      type: Boolean,
      required: false,
      default: true
    },
    widthUpload: {
      type: String,
      required: false,
      default: "148px"
    },
    heightUpload: {
      type: String,
      required: false,
      default: "148px"
    },
    fileSrc: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    fileList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 1
    },
    fileSize: {
      type: Number || String,
      default: 20
    },
    accept: {
      type: String,
      default: "*"
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    isFileOnlyUrl: {
      type: Boolean,
      required: false,
      default: true
    },
    isDrag: {
      type: Boolean,
      required: false,
      default: true
    },
    isContract: {
      type: Boolean,
      required: false,
      default: false
    },
    otherUrl: {
      type: String,
      required: false,
      default: ""
    },
    iszip: {
      type: Boolean,
      required: false,
      default: true
    },
    alwaysShow: {
      type: Boolean,
      required: false,
      default: false
    },
    ossUrl: {
      type: String,
      required: false,
      default: "/sys/oss/upload"
    }
  },

  setup(props, { emit }) {
    const previousProps = reactive({
      // 文件地址
      fileSrc: props.fileSrc,
      // 是否支持多选
      multiple: props.multiple,
      // 对话框文件地址
      dialogImageUrl: "",
      // 对话框是否可见
      dialogVisible: props.dialogVisible,
      // 文件列表
      fileList: props.fileList,
      // 限制上传的文件数量
      limit: props.limit,
      // 限制上传的文件大小,传参时以参数为准。不传时默认20M
      fileSize: props.fileSize,
      // 允许上传的文件类型
      accept: props.accept,
      // 是否显示文件列表
      showFileList: props.showFileList,
      // 文件列表的类型  'text' | 'picture' | 'picture-card'
      listType: props.listType,
      //   提示
      tip: props.tip,
      //   按钮操作提示
      uploadPreText: props.uploadPreText,
      // 上传提示信息是否展示一行
      isOneLine: props.isOneLine,
      // 是否自定义组件内容
      isSelfSty: props.isSelfSty,
      // 拖拽
      isDrag: props.isDrag,
      // 宽高
      widthUpload: props.widthUpload,
      heightUpload: props.heightUpload,
      // 文件没有名称只有路径(针对文件回显)
      isFileOnlyUrl: props.isFileOnlyUrl,
      // 是否是合同上传
      isContract: props.isContract,
      otherUrl: props.otherUrl,
      // 文件地址
      ossUrl: props.ossUrl,
      // 是否压缩图片
      iszip: props.iszip,
      // 不隐藏文件-个人中心头像使用
      alwaysShow: props.alwaysShow
    });
    // 文件上传后端地址
    const url = ref();
    url.value = `${app.api}${previousProps.ossUrl}?token=${getToken()}&iszip=${previousProps.iszip}`;

    // 监听文件地址进行列表回显
    watch(
      () => props.fileSrc,
      (newVal) => {
        // console.log("imageUrl更新", newVal)
        if (newVal) {
          previousProps.fileSrc = newVal;
          previousProps.fileList = [];
          previousProps.fileSrc.split(",").forEach((item) => {
            if (previousProps.isFileOnlyUrl) {
              let strArr = item.split("/");
              previousProps.fileList.push({ url: item, name: strArr[strArr.length - 1] });
            } else {
              previousProps.fileList.push(item);
            }
          });
        } else {
          previousProps.fileList = [];
        }
      },
      { deep: true, immediate: true }
    );
    watch(
      () => props.otherUrl,
      (newVal) => {
        if (newVal) {
          url.value = app.api + props.otherUrl + "&token=" + getToken();
          console.log("url更新", url.value);
        }
      },
      { deep: true, immediate: true }
    );
    // 手动上传的方法
    const fileUploader = ref<UploadInstance>();
    const notAutoSubmit = () => {
      nextTick();
      fileUploader.value!.submit();
    };

    // 文件上传成功的函数
    const handleAvatarSuccess = (res: any) => {
      console.log(previousProps.fileList);
      // 接口上传失败处理，上传失败的文件删除
      if (res.code !== 0) {
        ElMessage.warning(res.msg);
        previousProps.fileList = previousProps.fileList.filter((item: any) => !item.response || (item.response && item.code == 0));
        return;
      }
      if (previousProps.limit == 1) {
        previousProps.fileList = [
          {
            url: res.data.src,
            name: res.data.name
          }
        ];
      }
      if (previousProps.isContract) {
        if (res.code == 0) {
          emit("loadData");
        } else {
          ElMessage.warning(res.msg);
          fileUploader.value!.clearFiles();
          emit("update:fileSrc", undefined);
          return;
        }
      }
      const srcArray = previousProps.fileList.map((item: any) => (item.response ? item.response.data.src : item.url));
      console.log(srcArray);
      const fileSrc = srcArray.join(",");
      emit("update:fileSrc", fileSrc);
    };
    // 处理头像上传前的函数
    const beforeAvatarUpload = (file: any, uploadFiles: any) => {
      const fileSize = previousProps.fileSize * 1024 * 1024;
      if (file.size > fileSize) {
        ElMessage.warning(`上传文件大小在${previousProps.fileSize}M内，请重新选择文件上传`);
        return false;
      }
    };
    // 点击文件列表中已上传的文件时的钩子
    const handlePictureCardPreview: UploadProps["onPreview"] = (file: any) => {
      console.log(file.url);
      previousProps.dialogImageUrl = file.url;
      previousProps.dialogVisible = true;
    };
    // 文件列表移除文件时的钩子
    const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
      previousProps.fileList = uploadFiles;
      const srcArray = previousProps.fileList.map((item: any) => item.url);
      console.log("删除文件", srcArray);
      const fileSrc = srcArray.join(",");
      emit("update:fileSrc", fileSrc);
    };
    // 当超出限制时，执行的钩子函数
    const handleExceed: UploadProps["onExceed"] = (files, fileList) => {
      if (previousProps.limit == 1) {
        fileUploader.value!.clearFiles();
        const file = files[0] as UploadRawFile;
        file.uid = genFileId();
        fileUploader.value!.handleStart(file);
      }
      // ElMessage.warning(`当前限制选择${previousProps.limit}个文件`);
    };
    defineExpose({
      notAutoSubmit
    });
    return {
      previousProps,
      url,
      handleAvatarSuccess,
      beforeAvatarUpload,
      handlePictureCardPreview,
      handleRemove,
      handleExceed,
      notAutoSubmit,
      fileUploader
    };
  }
});
</script>

<style lang="scss">
.uploadFileStyle .el-upload-dragger {
  border: 1px dashed var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  box-shadow: 0px 2px 5px 0px rgba(87, 128, 239, 0.1);
  border-radius: 6px 6px 6px 6px;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: fit-content;
  height: fit-content;
  transition: var(--el-transition-duration-fast);
  padding: 0;
}

.file-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.file-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 50px;
  height: 50px;
  text-align: center;
}

.uploadFileStyle {
  width: 100%;

  .BtnSty {
    display: flex;

    flex-direction: column;
    align-items: center;
    justify-content: center;
    // padding: 23px 0;

    .el-icon--upload {
      font-size: 48px;
      margin-bottom: 8px;
    }
  }
  .el-upload__tip {
    .el-text {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
    }
  }

  .file-uploader {
    width: 100%;
    &.hide-upload {
      .el-upload,
      .el-upload__tip {
        display: none;
      }
    }
  }
}
</style>
