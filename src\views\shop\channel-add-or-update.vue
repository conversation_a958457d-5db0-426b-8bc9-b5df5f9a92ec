<template>
  <el-dialog v-model="visible" :title="isDialogAdd ? $t('add') + '渠道' : $t('update') + '渠道'" width="500">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" @keyup.enter="">
      <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
        <el-descriptions-item>
          <template #label><span>渠道类型</span></template>
          <div style="display: flex;align-items: center;gap: 4px;">
            <div>{{ channelTypeName == '1' ? '回收渠道' : '销售渠道' }}</div>
            <div>-</div>
            <el-select v-model="dataForm.classId" style="flex: 1;" @change="getChannelList(dataForm.classId)">
              <el-option :label="item.className" :value="item.id" v-for="(item, index) in menuList" :key="index"></el-option>
            </el-select>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label><span>渠道名称<span style="color: red">*</span></span></template>
          <el-form-item prop="title">
            <div style="width: 100%;display: flex;align-items: center;gap: 8px;cursor: pointer;">
              <el-input v-model="dataForm.title" placeholder="渠道名称" v-if="isEdit" @input="editTitleUpdate"></el-input>
              <el-select v-model="dataForm.id" placeholder="请输入或选择渠道名称" allow-create :filterable="isDialogAdd" clearable @change="selectChange" v-else>
                <el-option :label="item.title" :value="item.id" v-for="(item, index) in classList" :key="index"></el-option>
              </el-select>
              <el-icon size="18" color="var(--el-color-primary)" @click="isEdit = !isEdit" v-if="dataForm.id && !isDialogAdd && !isEdit"><Edit /></el-icon>
              <el-icon size="18" color="var(--el-color-primary)" @click="isEdit = !isEdit" v-if="dataForm.id && !isDialogAdd && isEdit"><Select /></el-icon>
            </div>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label><span>二级渠道</span></template>
          <el-form-item prop="parentName">
            <div style="display: flex; flex-direction: column; gap: 12px; width: 100%">
              <div v-for="(item, index) in dataForm.childList" :key="index" style="display: flex; align-items: center; gap: 5px; width: 100%">
                <el-input v-model="item.title" placeholder="二级渠道"></el-input>
                <div style="width: 50px; display: flex; align-items: center; gap: 5px">
                  <el-icon color="var(--el-color-primary)" size="18" style="cursor: pointer" @click="secondaryAdd"><CirclePlusFilled /></el-icon>
                  <el-icon color="#F53F3F" size="18" v-if="index > 0 || item.title" style="cursor: pointer" @click="secondaryDel(index)"><RemoveFilled /></el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label><span>渠道说明</span></template>
          <el-form-item prop="remark">
            <el-input v-model="dataForm.remark" placeholder="渠道说明"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label><span>是否启用</span></template>
          <el-form-item prop="state">
            <!-- 0：是 1：否 -->
            <el-switch v-model="dataForm.state" :active-value="0" :inactive-value="1" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 弹窗, 新增 / 修改 -->
  <add-or-update ref="addOrUpdateRef" @refreshDataList="gettenantData"></add-or-update>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import useView from "@/hooks/useView";
import AddOrUpdate from "@/views/tenant/tenant-add-or-update.vue";
const { t } = useI18n();

const isEdit = ref(false);
const isDialogAdd = ref(false);
const visible = ref(false); // 对话框显隐
const dataFormRef = ref(); // 表单ref
const channelTypeName = ref("");
const dataForm = reactive({
  // 表单变量
  id: null,
  title: "",
  channelType: "0",
  remark: "",
  state: 0,
  childList: <any>[{ id: "", title: "" }],
  classId: ""
});
const rules = ref({
  // 表单必填项
  title: [{ required: true, message: "请输入渠道名称", trigger: "blur" }]
  // channelType: [{ required: true, message: "请选择渠道类型", trigger: "change" }],
  // remark: [{ required: true, message: "请输入渠道说明", trigger: "blur" }],
  // state: [{ required: true, message: "请选择是否启用", trigger: "change" }]
});

// 表单初始化
const init = (typeName: string, classId: string, id?: any) => {
  visible.value = true;
  isDialogAdd.value = id ? false : true
  dataForm.id = id ? id : null;
  channelTypeName.value = typeName;
  dataForm.channelType = typeName;
  // console.log(typeName,'====== typeName ======');
  dataForm.classId = classId;
  getChannelClass();
  // 获取表单详情
  if (id) {
    getInfo(id);
  }
  getChannelList(classId);
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};
// NOTE: 获取合作商数据、添加合作商数据
const tenantList = ref([]);
const gettenantData = () => {
  baseService.get("/sys/tenant/page", { limit: 9999 }).then((res) => {
    tenantList.value = res.data?.list || [];
  });
};
gettenantData();

// 编辑时修改渠道名称
const editTitleUpdate = () =>{
  classList.value.map((item: any) => {
    if(item.id == dataForm.id){
      item.title = dataForm.title
    }
  });
}

// 获取父级渠道名称
const classList = ref(<any>[]);
const getChannelList = (classId: string) => {
  baseService.get("/channel/channel/list", { classId: classId }).then((res) => {
    if (res.code == 0) {
      classList.value = res.data;
    }
  });
};

// 获取渠道分类
const menuList = ref(<any>[]);
const getChannelClass = () => {
  baseService.get("/channel/channelClass/list", { type: channelTypeName.value }).then((res) => {
    if (res.code == 0) {
      menuList.value = res.data;
    }
  });
};

// 选择回调
const selectChange = (e: any) => {
  const info = classList.value.find((item: any) => item.id == e);
  if (info) {
    dataForm.title = info.title;
    dataForm.id = info.id;
  } else {
    dataForm.title = e;
  }
};

// 新增渠道
const secondaryAdd = () => {
  dataForm.childList.push({ id: "", title: "" });
};
// 删除渠道
const secondaryDel = (index: number) => {
  if(dataForm.childList.length == 1){
    dataForm.childList[0].title = "";
    dataForm.childList[0].id = "";
    return;
  }
  dataForm.childList.splice(index, 1);
};

// 获取详情
const getInfo = (id: number) => {
  baseService.get("/channel/channel/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    if(!res.data.childList.length){
      dataForm.childList.push({ id: "", title: "" });
    }
  });
};

// 表单提交
const btnLoading = ref(false);
const emit = defineEmits(["refreshDataList"]);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    const info = classList.value.find((item: any) => item.id == dataForm.id);
    if (!info) {
      dataForm.id = null;
    }
    // console.log("======= datafrom =====",isDialogAdd, dataForm);
    // return
    btnLoading.value = true;
    (isDialogAdd.value ? baseService.post : baseService.put)("/channel/channel", dataForm)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: flex-start !important;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>
<style lang="less">
.drawer {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.el-select-dropdown__footer {
  border-top: 0;
}
</style>
