<template>
    <el-scrollbar class="center_left">
        <div class="menu_item" :class="{ active: item.id == modelValue }"
            v-for="(item, index) in list" :key="index" @click=" $emit('update:modelValue', item.id); $emit('change', item.id); ">
            <img :src="item.thumbnail" class="gameLogo">
            <div class="gameInfo">
                <span class="title">{{ item.title }}</span>
                <div class="count">商品数量：<span>{{ item.shopQuantity }}</span> </div>
            </div>
        </div>
    </el-scrollbar>
</template>

<script lang='ts' setup>
import { IObject } from "@/types/interface";
import { ref,reactive, PropType } from 'vue';

defineProps({
    modelValue: {
        type: [Number, String] as PropType<number | string>,
        required: true
    },
    list: {
        type: Array as PropType<IObject[]>,
        required: true
    },
})
</script>

<style lang='less' scoped>
.center_left {
    width: 256px;
    .menu_item {
        width: 100%;
        padding: 8px;
        border-radius: 8px;
        display: flex;
        gap: 8px;
        cursor: pointer;

        .gameLogo {
            width: 64px;
            height: 64px;
        }

        .gameInfo {
            height: 64px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;

            .title {
                font-weight: 500;
                font-size: 16px;
                color: #1D2129;
                text-align: center;
            }

            .count {
                font-weight: 400;
                font-size: 14px;
                color: #4E5969;
                text-align: center;

                span {
                    color: var(--el-color-primary);
                }
            }
        }

        &:hover {
            background-color: var(--el-color-primary-light-9);
            :deep(.title){
                color: var(--el-color-primary) !important;
            }
        }
    }

    .active {
        background-color: var(--el-color-primary-light-9);
        :deep(.title){
            color: var(--el-color-primary) !important;
        }
    }
}
</style>