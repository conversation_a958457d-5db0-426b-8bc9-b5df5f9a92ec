<template>
    <el-drawer v-model="visible" title="换绑" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%" class="ny-drawer">

        <el-card>
            <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
                <el-row :gutter="12">
                    <el-col :span="12">
                        <el-form-item label="对方绑定手机号" prop="customerBindPhone">
                            <el-input v-model="dataForm.customerBindPhone" placeholder="请输入对方绑定手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12"></el-col>
                    <el-col :span="12">
                        <el-form-item label="我方绑定手机号" prop="ourBindPhone">
                            <!-- <el-select v-model="dataForm.ourBindPhone" filterable placeholder="请选择手机号（支持搜索）">
                                <el-option label="请选择1" value=""></el-option>
                            </el-select> -->
                            <el-input v-model="dataForm.ourBindPhone" placeholder="请输入我方绑定手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="我方密码" prop="ourPassword">
                            <el-input v-model="dataForm.ourPassword" type="password" show-password placeholder="请输入我方密码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="换绑凭证" prop="changePicture">
                            <ny-upload v-model:imageUrl="dataForm.changePicture" :limit="99" :fileSize="5"
                                accept="image/*"></ny-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input v-model="dataForm.remark" type="textarea" :rows="3" maxlength="255"
                                show-word-limit placeholder="请输入备注"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </el-card>

        <template #footer>
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
        </template>

    </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emits = defineEmits(["refresh"]);
const visible = ref(false);
const init = (id?: number) => {
    visible.value = true;
    dataForm.value.orderId = id;
}

const dataForm = ref(<any>{

});

const rules = reactive({
    customerBindPhone: [
        { required: true, message: '请输入对方绑定手机号', trigger: 'blur' },
        { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    ourBindPhone: [
        { required: true, message: '请输入我方绑定手机号', trigger: 'blur' },
        { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    ourPassword: [{ required: true, message: '请输入我方密码', trigger: 'blur' }],
    changePicture: [{ required: true, message: '请上传换绑凭证', trigger: 'change' }],
})


// 提交
const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
    formRef.value.validate(async (valid: boolean) => {
        if (!valid) return;
        btnLoading.value = true;
        baseService.post("/purchase/completeChangeInfo", dataForm.value).then(res => {
            if(res.code == 0){
                ElMessage.success('提交成功');
                emits("refresh");
                visible.value = false;
            }
        }).finally(() => {
            btnLoading.value = false;
        })
    })
}


defineExpose({
    init
})

</script>