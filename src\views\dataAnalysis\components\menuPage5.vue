<template>
    <div>
        <div class="menu_title">销售排行榜</div>
        <div class="top">
            <el-radio-group v-model="dataForm.type" @change="queryChagne">
                <el-radio value="1">游戏销量</el-radio>
                <el-radio value="2">员工销量</el-radio>
                <!-- <el-radio value="3">渠道销量</el-radio> -->
            </el-radio-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.salesChannelId" :list="salesChannelList" labelKey="channelName" valueKey="channelId"
                    placeholder="销售渠道" clearable></NyDropdownMenu> -->
                <el-date-picker v-model="SalesTime" type="daterange" start-placeholder="开始时间"
                    end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px;margin-left: 12px;" v-if="typeValue == '0' || typeValue == '1'" @change="selectTime"/>
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <div class="card_list" style="margin-top: 12px;">
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>今日销售订单</span>
                        <el-tooltip effect="dark" content="今日销售过的订单数量，不包含【已取消】的数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ saleOrder.todaySalesOrder }}</div>
                <div class="item_footer">
                    <div class="box">总金额(元)：{{ saleOrder.todayTotalAmount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>本月销售订单</span>
                        <el-tooltip effect="dark" content="本月销售过的订单数量，不包含【已取消】的数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ saleOrder.monthSalesOrder }}</div>
                <div class="item_footer">
                    <div class="box">总金额(元)：{{ saleOrder.monthTotalAmount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>员工销量占比</span>
                        <el-tooltip effect="dark" content="按员工维度，显示当前时间下（默认本月）销量最高的前 6 个员工的销量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="EmployeeSalesRef"></div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>游戏销量占比</span>
                        <el-tooltip effect="dark" content="按游戏维度，显示当前时间下（默认本月）销量最高的前 6 个游戏的销量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="GameSalesRef"></div>
                </div>
            </div>
            <!-- <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(232,230,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>渠道销量占比</span>
                        <el-tooltip effect="dark" content="按渠道维度，显示当前时间下（默认本月）销量最高的前 6 个渠道的销量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="ChannelSalesVolumeRef"></div>
                </div>
            </div> -->
        </div>
        <SaleDetail ref="SaleDetailRef"></SaleDetail>
        <div style="display: flex;align-items: center;gap: 12px;">
            <div style="flex: 1;">
                <orderQuantity ref="orderQuantityRef"></orderQuantity>
            </div>
            <div style="flex: 1;">
                <salesVolume ref="salesVolumeRef"></salesVolume>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import baseService from "@/service/baseService";
import * as echarts from "echarts";
import orderQuantity from "./charts/orderQuantity.vue";
import salesVolume from "./charts/salesVolume.vue";
import SaleDetail from "./charts/SaleDetail.vue";

const SalesTime = ref();

const SaleDetailRef = ref();
const orderQuantityRef = ref();
const salesVolumeRef = ref();

const typeValue = ref("0");
const dataForm = ref({
    gameId: "",
    salesChannelId: "",
    startTime: "",
    endTime: "",
    type:"1"
});

const gameList = ref(); // 游戏列表
const salesChannelList = ref(); // 销售渠道列表

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) =>{
    baseService.get("/dataAnalysis/channelSearchList",{channelType}).then(res=>{
        // 销售渠道
        if(channelType == 0){
            salesChannelList.value = res.data;
        }
    })
}

const charts = ref(<any>[]);
const EmployeeSalesRef = ref(null);
// 员工销量占比
const EmployeeSalesChart = () => {
    const userGrowthChart = echarts.init(EmployeeSalesRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 85,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: saleOrder.value.employeeSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const GameSalesRef = ref(null);
// 游戏销量占比
const GameSalesChart = () => {
    const userGrowthChart = echarts.init(GameSalesRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 85,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: saleOrder.value.gameSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const ChannelSalesVolumeRef = ref(null);
// 渠道销量占比
const ChannelSalesVolumeChart = () => {
    const userGrowthChart = echarts.init(ChannelSalesVolumeRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 45,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: saleOrder.value.channelSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

onMounted(() => {
    getGameList();
    getRecyclingChanneList(0);

    getSalesStatistics();
})

const selectTime = () =>{
    dataForm.value.startTime = SalesTime.value ? SalesTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = SalesTime.value ? SalesTime.value[1] + " 23:59:59" : "";
}

// 查询
const queryChagne = () =>{
    getSalesStatistics();
    SaleDetailRef.value.init(dataForm.value);
    orderQuantityRef.value.init(dataForm.value);
    salesVolumeRef.value.init(dataForm.value);
}

// 重置
const resettingChange = () =>{
    dataForm.value.gameId = "";
    dataForm.value.salesChannelId = "";
    dataForm.value.startTime = "";
    dataForm.value.endTime = "";
    SalesTime.value = [];
    getSalesStatistics();
    SaleDetailRef.value.init(dataForm.value);
    orderQuantityRef.value.init(dataForm.value);
    salesVolumeRef.value.init(dataForm.value);
}

// 销售排行榜统计
const saleOrder = ref({
    todaySalesOrder: 0,
    todayTotalAmount: 0,
    monthSalesOrder: 0,
    monthTotalAmount: 0,
    employeeSalesRanking: [],
    gameSalesRanking: [],
    channelSalesRanking: []
});
const getSalesStatistics = () =>{
    baseService.post("/dataAnalysis/salesStatistics",dataForm.value).then(res=>{
        Object.assign(saleOrder.value,res.data);
        charts.value = [];
        EmployeeSalesChart();
        GameSalesChart();
        ChannelSalesVolumeChart();
    })
}

</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card_list {
    display: flex;
    align-items: center;
    gap: 12px;

    .card_list_item {
        flex: 1;
        padding: 16px 20px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;

        .item_header {
            font-weight: bold;
            font-size: 16px;
            color: #1C1C1C;
            line-height: 20px;

            display: flex;
            align-items: center;
            justify-content: space-between;

            .month {
                font-weight: 500;
                font-size: 14px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }

        .item_center {
            font-weight: bold;
            font-size: 24px;
            color: #1C1C1C;
            line-height: 36px;
            margin: 10px 0px;
        }

        .item_footer {
            display: flex;
            align-items: center;

            .box {
                flex: 1;
                font-weight: 400;
                font-size: 13px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }

        .charts {
            width: 100%;
            height: 76px;
            // border: 1px solid red;
        }
    }
}


</style>
