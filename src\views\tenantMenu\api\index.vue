<template>
  <div class="container">
    <el-card shadow="never" class="rr-view-ctx-card">
      <div>
        <ny-flod-tab class="newTabSty" :list="apiList" v-model="state.companyType" value="id" label="companyName" @change="changeTab"></ny-flod-tab>
        <api-configured ref="apiConfiguredRef" :key="apiConfiguredKey"></api-configured>
      </div>
    </el-card>

    <!-- 配置 -->
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, nextTick, computed } from "vue";
import useView from "@/hooks/useView";
import apiConfigured from "./api-configured.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { IObject } from "@/types/interface";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import apiIcon from "@/assets/images/api_icon.png";
import baseService from "@/service/baseService";

const openTime = ref(""); // 开通时间

const store = useAppStore();

const state = reactive({
  companyType: undefined
});
const apiList = ref([]);
const companyTypeList = () => {
  baseService.get("/partner/us/apiList").then((res) => {
    if (res.code == 0) {
      apiList.value = res.data || [];
      state.companyType = res.data[0]?.id;
      if (state.companyType) changeTab();
    }
  });
};

companyTypeList();

// 选中操作
const apiConfiguredRef = ref();
const apiConfiguredKey = ref(0);
const changeTab = () => {
  apiConfiguredKey.value++;
  nextTick(() => {
    apiConfiguredRef.value.init(state.companyType);
  });
};
</script>

<style lang="less" scoped>
.business_card {
  border-radius: 8px;
  margin-bottom: 40px;
  border: 1px solid var(--el-border-color-light);
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;

  &.active {
    background: var(--color-primary-light);
  }

  .c {
    display: flex;
    box-sizing: border-box;
    padding: 15px;
    height: calc(100% - 50px);
    position: relative;
    .logo {
      height: 50px;
      width: 50px;
      margin-right: 15px;
    }
    .det {
      display: flex;
      flex-direction: column;
      flex: 1;
      .tag {
        span {
          margin-right: 10px;
        }
        font-size: 16px;
        margin-bottom: 15px;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .value {
        font-size: 14px;
        margin-bottom: 10px;
      }
      .api-content {
        display: flex;
        align-items: center;
        font-size: 14px;

        .api-content-item {
          flex: 1;
        }
      }
    }
    .set {
      position: absolute;
      right: 15px;
      top: 0px;
      font-size: 18px;
      color: var(--el-color-info);
    }
  }
  .f {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
    height: 30px;
    box-sizing: content-box;
  }
}

.card-list {
  margin-left: -24px;
  display: flex;
  flex-wrap: wrap;

  .image-wrap {
    height: 80px;
    background: #fcfbfb;
    border-radius: 8px;
    overflow: hidden;
  }

  .image {
    width: 100%;
    height: 100%;
  }

  :deep(.card-item-content) {
    padding: 0;
  }

  .card-content {
    height: calc(100% - 24px);
    margin-left: 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    border: 1px solid #e4e7ed;
    transition: all 0.3s;
    font-size: 14px;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
    }

    .info {
      padding: 12px;
    }

    .title-wrap {
      .title {
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
      }
    }

    .price-wrap {
      font-size: 14px;

      .symbol {
        font-size: 12px;
        color: #f44a29;
      }

      .price {
        font-size: 24px;
        line-height: 20px;
        font-weight: bold;
        color: #f44a29;
      }

      .after {
        font-size: 12px;
        color: #606266;
        margin-left: 3px;
      }
    }

    .open-btn {
      background: #ffecbb;
      border: 1px solid #ffdd8c;
      border-radius: 4px;
      width: 104px;
      line-height: 32px;
      text-align: center;
      font-size: 16px;
      color: #5f4139;
      font-weight: bold;

      &.trial {
        background: var(--el-color-danger);
        border: 1px solid var(--el-color-danger);
        color: #fff;
      }

      &.continued {
        background: var(--el-color-success);
        border: 1px solid var(--el-color-success);
        color: #fff;
      }
    }
  }
}
.api_list {
  display: flex;
  flex-wrap: wrap;
  gap: 1%;
  .api_item {
    width: 19.2%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
    margin-bottom: 20px;
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
    }
    .api_item_top {
      padding: 12px;
      background: #f1f6ff;
      background-image: url("../../../assets/images/api_bg.png");
      background-repeat: no-repeat;
      background-position: right 0 bottom 0;
      background-size: 145px 78px;
      .api_name {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .api_tab {
        margin-top: 10px;
        span {
          background: #ffffff;
          padding: 4px;
          border-radius: 2px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        span + span {
          margin-left: 8px;
        }
      }
    }
    .api_item_cont {
      background: #f6f9ff;
      padding: 16px;
      .api_item_cont_item {
        display: flex;
        align-items: center;
        cursor: pointer;
        .label {
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .value {
          flex: 1;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          padding: 5px 8px;
          margin-left: 20px;
          display: flex;
          align-items: center;
          .value_name {
            flex: 1;
            font-weight: 400;
            font-size: 14px;
            color: #303133;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .value_icon {
            font-weight: 400;
            font-size: 14px;
            color: #4165d7;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .switch {
          display: flex;
          align-items: center;
          margin-left: 20px;
        }
      }

      .api_item_cont_item + .api_item_cont_item {
        margin-top: 10px;
      }
    }
    .api_item_but {
      background-color: #fff;
      padding: 16px;
      border-radius: 0px 0px 4px 4px;
      .money {
        font-weight: bold;
        font-size: 20px;
        color: #f44a29;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .price {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 10px;
      }
    }
  }
}
.select_time {
  display: flex;
  flex-direction: column;
  span {
    width: 100%;
    padding: 8px 16px;
    cursor: pointer;
  }
}
</style>
