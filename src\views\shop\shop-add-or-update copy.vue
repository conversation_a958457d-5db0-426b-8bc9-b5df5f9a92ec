<template>
  <el-drawer v-model="visible" size="50%" class="drawer_shop" @close="drawerCloseHandle('close')">
    <template #header>
      <div class="drawer_title">{{ title ? title : !dataForm.id ? $t("add") : $t("update") }}</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <el-tabs v-model="activeValue" class="demo-tabs" v-if="dataForm.status == 2 || dataForm.status == 3">
        <el-tab-pane label="商品信息" name="1"></el-tab-pane>
        <el-tab-pane label="三方对接" name="2"></el-tab-pane>
      </el-tabs>
      <div class="shop_page" v-if="activeValue == '1'">
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top">
          <div class="shop_page_basic" v-show="!pageShow">
            <slot name="header" :data="dataForm">
              <!-- 游戏列表 -->
              <div class="titleSty">商品基本信息</div>
              <div class="games flx">
                <div class="gamesList flx-1" :style="{ height: gameTabShow ? 'auto' : '38px' }">
                  <div :class="item.id == dataForm.gameId ? 'gamesItem active' : 'gamesItem'" v-for="(item, index) in gamesList" :key="index" @click="gamesClick(item.id, item.gcode, item.gtype)">{{ item.title }}</div>
                </div>
                <div class="icons" @click="gameTabShow = !gameTabShow">
                  <el-icon v-show="!gameTabShow">
                    <ArrowDownBold />
                  </el-icon>
                  <el-icon v-show="gameTabShow">
                    <ArrowUpBold />
                  </el-icon>
                </div>
              </div>
            </slot>

            <!-- 商品信息 -->
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="商品标题" prop="title">
                  <el-input v-model="dataForm.title" type="textarea" placeholder="商品标题" :rows="4"></el-input>
                </el-form-item>
              </el-col>

              <!-- 新增商品显示 -->
              <template v-if="source == 'shop' || source == 'tenantShop'">
                <el-col :span="12">
                  <el-form-item label="零售价" prop="price">
                    <el-input-number v-model="dataForm.price" :min="0" :precision="2" :step="1" controls-position="right" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="回收价" prop="acquisitionPrice">
                    <el-input-number v-model="dataForm.acquisitionPrice" :min="0" :precision="2" :step="1" controls-position="right" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="游戏账号" prop="gameAccount">
                    <el-input v-model="dataForm.gameAccount" placeholder="游戏账号" @blur="BlackNumber(1)"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号码" prop="phone">
                    <!-- 租户 手机号 手输 -->
                    <el-input v-if="source == 'tenantShop'" v-model="dataForm.phone" type="text" placeholder="手机号码" maxlength="11" show-word-limit></el-input>
                    <!-- @change="BlackNumber(2)" -->
                    <el-select v-else filterable remote :loading="loading" :remote-method="remoteMethod" clearable v-model="dataForm.phone" placeholder="请输入关键字搜索手机号">
                      <el-option v-for="item in phonesList" :key="item.phone" :label="item.phone" :value="item.phone" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="游戏密码" prop="gamePassword">
                    <el-input v-model="dataForm.gamePassword" placeholder="游戏密码"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="回收人" prop="acquisitionUser">
                    <ny-select-search v-model="dataForm.acquisitionUser" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="回收人" v-if="store.state.isPlatform" />
                    <el-input :placeholder="store.state.user.realName" disabled v-else></el-input>
                  </el-form-item>
                </el-col>
              </template>

              <el-col :span="24">
                <el-form-item label="商品描述" prop="title">
                  <div class="shop_info">
                    <div class="shop_info_left">
                      <el-icon color="#909399">
                        <Warning />
                      </el-icon>
                      <span class="tip">温馨提示：商品详情信息很重要，直接影响商品排名，越详细精准排名越靠前</span>
                    </div>
                    <div class="shop_info_right">
                      <el-text type="warning" style="margin-right: 10px" v-if="!isWrite">未填写</el-text>
                      <el-text type="success" style="margin-right: 10px" v-else>已填写</el-text>
                      <el-button type="primary" plain @click="pageShow = true" :icon="Shop">新增游戏属性</el-button>
                    </div>
                  </div>
                  <el-input v-model="dataForm.info" type="textarea" placeholder="商品描述" :rows="10" show-word-limit></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="商品主图" prop="log">
                  <ny-shop-image-upload v-model="dataForm.log" v-if="!screenshotLoading"></ny-shop-image-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="商品详情图" prop="imagesList">
                  <ny-shop-image-upload v-model="dataForm.imagesList" :limit="100" :multiple="true" v-if="!screenshotLoading"></ny-shop-image-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item prop="highlights">
                  <template #label>
                    <div class="flx-justify-between">
                      <div>商品亮点</div>
                      <div class="flx-align-center">
                        <span>亮点展示示例：</span>
                        <span class="mytag" style="max-width: 400px">
                          <el-image class="topImg" :src="arrowIcon" />
                          {{ dataForm.highlights }}
                        </span>
                      </div>
                    </div>
                  </template>
                  <el-input v-model="dataForm.highlights" type="textarea" :rows="2" placeholder="商品亮点" @change="feedBackInfo" show-word-limit></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item prop="remark">
                  <template #label>
                    <div class="flx-justify-between">
                      <div>备注</div>
                    </div>
                  </template>
                  <el-input v-model="dataForm.remark" type="textarea" :rows="3" placeholder="备注"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <div class="flx-center" style="padding-bottom: 20px;">
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ !dataForm.id ? '确定发布' : '确定修改' }}</el-button>
          </div> -->
          </div>
          <div class="shop_page_basic" style="margin-top: 12px" v-show="!pageShow && source == 'shop'">
            <!-- 新增商品有 -->
            <div class="titleSty">自动降价设置</div>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="自动降价周期" prop="priceReductionCycle">
                  <el-input v-model="dataForm.priceReductionCycle" type="number" placeholder="请输入自动降价周期">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自动降价比例" prop="priceReductionPercentage">
                  <el-input v-model="dataForm.priceReductionPercentage" type="number" placeholder="请输入自动降价比例">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自动降价底价" prop="minimumPrice">
                  <el-input v-model="dataForm.minimumPrice" type="number" placeholder="请输入自动降价底价">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <slot name="info" :data="dataForm"></slot>
          </div>

          <!-- 商品详情 -->
          <div class="shop_page_details" style="margin-top: 12px" v-show="pageShow">
            <el-form :model="dataForm" :rules="rules" ref="dataFormDetailRef" @keyup.enter="dataFormDetailSubmitHandle()" label-position="top">
              <div class="shop_page_details_card" style="margin-bottom: 16px" v-loading="attributeLoading" element-loading-text="属性识别中...">
                <!-- 描述 -->
                <ny-title title="商品描述" style="padding: 0px 0px 12px 0px" />

                <el-row :gutter="12" v-if="campsiteForm.gameName == '王者荣耀'">
                  <el-col :span="12">
                    <el-form-item label="营地号">
                      <el-input v-model="campsiteForm.campsiteId" placeholder="营地号" clearable @blur="campsiteIdFeedback"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item>
                      <template #label>
                        选择角色<el-text type="warning" style="margin-left: 12px"
                          ><el-icon><InfoFilled /></el-icon>先加载营地数据，再选择角色</el-text
                        >
                      </template>
                      <el-select v-model="currentRoleId" placeholder="选择角色" :disabled="gameRoleList.length == 0" clearable @change="campsiteChange">
                        <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="mb-30">
                    <el-button type="primary" :disabled="!campsiteForm.campsiteId" :loading="campsiteButLoad" @click="loadCampsite()">加载营地数据</el-button>
                    <el-button type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" @click="getWzryAttributesList()">自动获取</el-button>
                    <el-button type="info" v-if="unrecognizedList && unrecognizedList.length" @click="showFailureAttribute">查看识别失败属性</el-button>
                    <el-button type="warning" v-if="(gameRoleList && gameRoleList.length) || dataForm.gameCode == 'WWQY'" @click="screenshotHandle" :loading="screenshotLoading"> 一键截图 </el-button>
                  </el-col>
                </el-row>

                <!-- 扫码授权 -->
                <scan-code-auth :code="dataForm.gameCode" :gtype="dataForm.gameType" :unrecognizedList="unrecognizedList" :gameRoleList="gameRoleList" :screenshotLoading="screenshotLoading" @confirm="getRoleInfo" @screenshotHandle="screenshotHandle"></scan-code-auth>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="商品简介" prop="info">
                      <el-input v-model="dataForm.info" type="textarea" placeholder="商品简介" :rows="10"></el-input>
                      <el-text type="warning">tip：估价网站文本内容可在此进行解析。勾选底部属性信息后，会自动更新商品“描述信息”以及“标题”内容；</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item>
                      <el-button type="primary" :icon="Pointer" @click="analysisFn">一键解析</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div class="shop_page_details_card" style="margin-bottom: 16px" v-if="gameRoleList && gameRoleList.length && campsiteForm.gameName != '王者荣耀'">
                <!-- 角色 -->
                <ny-title title="角色" style="padding: 0px 0px 12px 0px" />
                <el-row :gutter="12">
                  <el-col :span="12">
                    <el-form-item label="账号角色">
                      <div class="flx-between w-100">
                        <el-select v-model="currentRoleId" placeholder="账号角色">
                          <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                        </el-select>
                        <el-button class="ml-12" type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" @click="getGameRoleAttributes()">自动获取</el-button>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <div class="shop_page_details_card" style="margin-bottom: 16px">
                <!-- 设置 -->
                <ny-title title="设置" style="padding: 0px 0px 12px 0px" />
                <el-row :gutter="12">
                  <el-col :span="12">
                    <el-form-item label="服务器" prop="gameAre">
                      <el-select v-model="dataForm.gameAre" placeholder="服务器" @change="gameAreChange">
                        <el-option v-for="item in sysgameList" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="系统区服" prop="server" v-if="sysgameChildrenList.length > 0">
                      <el-select v-model="dataForm.server" placeholder="服务器" @change="feedBackInfo()">
                        <el-option v-for="item in sysgameChildrenList" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="包赔标签" prop="compensation">
                      <el-radio-group v-model="dataForm.compensation" size="small">
                        <el-radio value="0" border>不可包赔</el-radio>
                        <el-radio value="1" border>可买包赔</el-radio>
                        <el-radio value="2" border>永久包赔</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="支持议价标签" prop="bargain">
                      <el-radio-group v-model="dataForm.bargain" size="small">
                        <el-radio value="0" border>否</el-radio>
                        <el-radio value="1" border>是</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="顶级账号标签" prop="topped">
                      <el-radio-group v-model="dataForm.topped" size="small">
                        <el-radio value="0" border>否</el-radio>
                        <el-radio value="1" border>是</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="shop_page_details_card">
                <!-- 属性 -->
                <ny-title title="商品属性" style="padding: 0px 0px 12px 0px" />
                <el-row>
                  <el-col :span="24">
                    <div v-for="(item, index) in dataForm.attributesList" :key="index">
                      <el-form-item :label="item.name" v-if="item.type == 1">
                        <div class="flod_tab">
                          <div class="flod_tab_cont">
                            <el-radio-group v-model="item.attributeIds" size="small" @change="feedBackInfo()">
                              <el-radio :value="it.id" border v-for="it in item.children">{{ it.name }}</el-radio>
                            </el-radio-group>
                          </div>
                          <div class="oper" style="width: 60px">
                            <el-text type="primary" v-if="item.attributeIds" @click="item.attributeIds = ''" class="resetFont">重置</el-text>
                          </div>
                        </div>
                      </el-form-item>
                      <el-form-item :label="item.name" v-if="item.type == 2">
                        <div class="flod_tab">
                          <div class="flod_tab_cont" :style="{ height: !item.fold ? 'auto' : '40px' }">
                            <el-checkbox-group v-model="item.attributeIds" @change="feedBackInfo()">
                              <el-checkbox :label="it.name" :value="it.id" v-for="it in spliceChildren(item)" />
                            </el-checkbox-group>
                          </div>
                          <div class="oper">
                            <el-text type="primary" v-if="item.attributeIds.length > 0" @click="item.attributeIds = []" class="resetFont">重置</el-text>
                            <el-button v-if="item.isMult" type="primary" plain size="small" @click="item.fold = !item.fold">{{ item.fold ? "展开列表" : "收起列表" }}</el-button>
                          </div>
                        </div>
                      </el-form-item>
                      <el-form-item :label="item.name" v-if="item.type == 3">
                        <div class="flod_tab">
                          <el-input v-model="item.attributeText" :placeholder="item.name" style="width: 25%" clearable @change="feedBackInfo()"></el-input>
                        </div>
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </div>
        </el-form>
      </div>
      <thirdParty
        ref="thirdPartyRef"
        :dataForm="dataForm"
        @thridChange="
          (e) => {
            thirdType = e;
          }
        "
        v-else
      ></thirdParty>
    </el-scrollbar>

    <template #footer>
      <template v-if="activeValue == '1'">
        <div style="flex: auto" v-show="!pageShow">
          <el-button @click="drawerCloseHandle('close')">取消</el-button>
          <!-- 修改商品 并且已上架 -->
          <template v-if="dataForm.status == 2">
            <el-button v-if="props.source == 'tenantShop'" type="primary" @click="releasedCommand(1)">确定发布</el-button>
            <el-dropdown placement="top" v-else class="ml-8" @command="releasedCommand">
              <el-button type="primary">确定发布</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="2">推送发布</el-dropdown-item>
                  <el-dropdown-item :command="1">正常发布</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>

          <!-- 新增商品 -->
          <el-button v-else-if="source == 'shop' || source == 'tenantShop'" :loading="btnLoading" type="primary" @click="dataFormSubmitHandle('')">{{ !dataForm.id ? "确定发布" : "确定修改" }}</el-button>

          <!-- 商品审核 -->
          <template v-if="source == 'audit'">
            <el-button :loading="btnLoading" type="warning" @click="dataFormSubmitHandle('2')">一键上架</el-button>
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle('1')">提交</el-button>
          </template>

          <!-- 回收订单 完善信息 -->
          <template v-if="source == 'order'">
            <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
          </template>
        </div>
        <div style="flex: auto" v-show="pageShow">
          <el-button @click="pageShow = false">返回</el-button>
          <el-button type="primary" @click="dataFormDetailSubmitHandle()">{{ !dataForm.id ? "确定保存" : "确定修改" }}</el-button>
        </div>
      </template>
      <template v-else>
        <el-radio-group v-model="taskType">
          <el-radio v-for="item in thirdType == '1' ? typeList : typeListAPI" :key="item.typeCode" :value="item.typeCode" :label="item.typeCode">{{ item.typeName }}</el-radio>
        </el-radio-group>
        <el-button style="margin-left: 24px" :loading="replyLoading" @click="pushAllSelect" type="primary">推送所选</el-button>
      </template>
    </template>

    <!-- 属性识别结果 -->
    <attribute-recognition-result ref="attributeRecognitionResultRef" @confirm="autoChecktAttributes" @showFailureAttributeEmit="showFailureAttribute"></attribute-recognition-result>

    <!-- 选择合作商 -->
    <select-partners ref="selectPartnersRef" :key="selectPartnersKey" btnTxt="推送发布" @change="pushSubmit"></select-partners>
  </el-drawer>
  <!-- </el-dialog> -->
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch, defineProps } from "vue";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import arrowIcon from "@/assets/images/tagicon.png";
import ScanCodeAuth from "./components/ScanCodeAuth.vue";
import AttributeRecognitionResult from "./components/AttributeRecognitionResult.vue";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";
import { districtServiceConversionName } from "@/utils/utils";
import SelectPartners from "./components/SelectPartners.vue";
import thirdParty from "./components/thirdParty.vue";
const { t } = useI18n();
const store = useAppStore();
const emit = defineEmits(["refreshDataList", "completeInfo"]);
const activeValue = ref("1");

const props = defineProps({
  getDetialApi: {
    type: String,
    default: "/shop/shop/"
  },
  // 标题
  title: {
    type: String,
    default: ""
  },
  // 来源
  source: {
    type: String,
    default: ""
  },
  accountSource: {
    type: String,
    default: "ORIGINAL_OWNER"
  },
  // loading
  submitLoading: {
    type: Boolean,
    default: false
  }
});

const visible = ref(false); // 对话框显隐
const pageShow = ref(false); // 商品详情显隐
const isWrite = ref(false); // 商品详情是否填写
const dataFormRef = ref(); // 表单ref
const dataFormDetailRef = ref(); // 商品详情表单ref
const dataForm = reactive({
  // 表单变量
  id: null,
  code: "",
  // 游戏id
  gameId: "",
  // 游戏code
  gameCode: "",
  // 游戏类型
  gameType: "",
  highlights: "",
  remark: "",
  title: "",
  price: 0,
  acquisitionPrice: 0,
  phone: "",
  gameAccount: "",
  gamePassword: "",
  log: [],
  imagesList: [],
  info: "",
  gameAre: "",
  server: "",
  compensation: "",
  bargain: "",
  topped: "",
  attributesList: <any>[],
  acquisitionUser: store.state.isPlatform && !store.state.user.superAdmin ? store.state.user.id : "",
  minimumPrice: undefined,
  priceReductionPercentage: undefined,
  priceReductionCycle: undefined,
  status: 1
});

const campsiteForm = reactive({
  gameName: "",
  campsiteId: "",
  roleId: "",
  roleList: <any>[]
});

const rules = ref({
  // 表单必填项
  gameId: [{ required: true, message: "游戏名称不能为空", trigger: "change" }],
  title: [{ required: true, message: "商品标题不能为空", trigger: "blur" }],
  price: [{ required: true, message: "零售价不能为空", trigger: "blur" }],
  acquisitionPrice: [{ required: true, message: "回收价不能为空", trigger: "blur" }],
  gameAccount: [{ required: true, message: "游戏账号不能为空", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      required: true,
      pattern: /^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ],
  log: [{ required: true, message: "游戏主图不能为空", trigger: "change" }],
  imagesList: [{ required: true, message: "详情图片不能为空", trigger: "change" }],
  info: [{ required: true, message: "商品简介不能为空", trigger: "blur" }],
  gameAre: [{ required: true, message: "服务器不能为空", trigger: "change" }],
  server: [{ required: true, message: "系统区服不能为空", trigger: "change" }],
  compensation: [{ required: true, message: "包赔标签不能为空", trigger: "change" }],
  bargain: [{ required: true, message: "支持议价标签不能为空", trigger: "change" }],
  topped: [{ required: true, message: "顶级账号标签不能为空", trigger: "change" }]
});
const gamesList = ref(<any>[]); // 游戏列表
const gameTabShow = ref(false); // 游戏列表显隐
const sysgameList = ref(<any>[]); // 游戏区服信息 - 服务器
const sysgameChildrenList = ref(<any>[]); // 游戏区服信息 - 系统区服

// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
    let game = gamesList.value.find((item) => item.id == dataForm.gameId);
    dataForm.gameCode = game.gcode;
    dataForm.gameType = game.gtype;

    campsiteForm.gameName = game.title;
  });
};
// 获取手机号数据
const loading = ref(false);
const phonesList = ref([]);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    baseService
      .get("/mobile/mobileCard/page", {
        page: 1,
        limit: 9999,
        phone: query
      })
      .then((res) => {
        phonesList.value = res.data.list;
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    phonesList.value = [];
  }
};

// 获取游戏属性信息
const getAttribute = () => {
  requestLoading.value = true;
  gameUserInfo.value = {};
  baseService
    .get("/game/attribute/page", { gameId: dataForm.gameId })
    .then((res) => {
      if (res.data.length == 0) {
        ElMessage({
          type: "warning",
          message: "没有查询到当前游戏属性信息请编辑后在来新增商品！"
        });
      } else {
        dataForm.attributesList = [];
        const sxWidth: number = Number(((document.querySelector(".flod_tab") as HTMLElement).offsetWidth * 0.5).toFixed(2));
        res.data.map((item: any) => {
          let allFontLen = 0;
          item.children.forEach((item: any) => {
            allFontLen += Number(item.name.length * 14 + 52);
          });
          if (Number((sxWidth / allFontLen).toFixed(2)) < 1) {
            dataForm.attributesList.push({
              typeId: item.id,
              attributeIds: item.type == 2 ? [] : "",
              attributeText: "",
              children: item.children,
              isTitle: item.isTitle,
              type: item.type,
              name: item.name,
              isMult: true,
              fold: true
            });
          } else {
            dataForm.attributesList.push({
              typeId: item.id,
              attributeIds: item.type == 2 ? [] : "",
              attributeText: "",
              children: item.children,
              isTitle: item.isTitle,
              type: item.type,
              name: item.name,
              isMult: false,
              fold: false
            });
          }
        });
        nextTick(()=>{
          if (dataForm.id && dataForm.attributesList) {
            getInfo(dataForm.id);
          } else {
            requestLoading.value = false;
          }
        })
        
      }
    })
    .catch((err) => {
      requestLoading.value = false;
    });
};

// 获取游戏区服
const getSysgame = () => {
  baseService.get("/game/sysgame/get/" + dataForm.gameId).then((res) => {
    sysgameList.value = res.data.areaDtoList;
    // console.log(sysgameList.value, res.data);
  })
};

// 游戏切换点击事件
const gamesClick = (id: string, gcode: string, gtype: string) => {
  ElMessageBox.confirm("切换游戏后，将清空已填写商品信息。您确定要切换吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    center: true
  }).then(() => {
    dataFormRef.value.resetFields();
    dataFormDetailRef.value.resetFields();
    dataForm.id = "";
    dataForm.gameId = id;
    dataForm.gameCode = gcode;
    dataForm.gameType = gtype;
    unrecognizedList.value = [];
    recognizedList.value = [];
    gameRoleList.value = [];
    getAttribute();
    getSysgame();
  });
};

// 服务器切换点击事件
const gameAreChange = (value: any) => {
  sysgameChildrenList.value = [];
  dataForm.server = "";
  const are = sysgameList.value.filter((item: any) => item.id == value)[0];
  if (are.children.length > 0) {
    sysgameChildrenList.value = are.children;
  } else {
    dataForm.server = value;
  }
  feedBackInfo();
};

// 一键解析
const analysisFn = (type?: string) => {
  if (!dataForm.info) {
    ElMessage.warning("请输入商品简介");
    return;
  }
  const strToArr = dataForm.info.split("\n");
  const infoArr = arrayToKeyValuePairs(strToArr);

  if (typeof infoArr == "string") {
    return;
  }
  infoArr.forEach((element: any) => {
    if (element.key.indexOf("服务器") > -1) {
      if (!element.value) return;
      const gameAreItem = sysgameList.value.filter((item: any) => item.title == element.value)[0];
      if (gameAreItem.children.length > 0) {
        dataForm.gameAre = gameAreItem.id;
        sysgameChildrenList.value = gameAreItem.children;
      } else {
        dataForm.gameAre = gameAreItem.id;
        dataForm.server = gameAreItem.id;
      }
    } else if (element.key.indexOf("系统区服") > -1) {
      const serverItem = sysgameChildrenList.value.filter((item: any) => item.title == element.value)[0];
      if (serverItem) {
        dataForm.server = serverItem.id;
      }
    } else if (element.key.indexOf("您自己认为是亮点的地方") > -1) {
      dataForm.highlights = element.value;
    } else {
      dataForm.attributesList.forEach((item: any) => {
        if (item.name == element.key) {
          if (item.type == 1) {
            // 单选
            const ids = nameQueryId(element.value, item.children);
            item.attributeIds = ids.join("");
          }
          if (item.type == 2) {
            // 多选
            const ids = nameQueryId(element.value, item.children);
            item.attributeIds = ids;
          }
          if (item.type == 3) {
            // 文本
            item.attributeText = element.value;
          }
        }
      });
    }
  });
  if (type) ElMessage.success("解析成功！");
  feedBackInfo();
};

// 文本内容转换成数组
const arrayToKeyValuePairs = (strArray: any) => {
  try {
    let arr: { key: any; value: any }[] = [];
    strArray.forEach((item: any) => {
      const [key, value] = item.split("：");
      if (value == undefined) {
        throw "解析失败，商品简介文本内容结构错误。请检查后重新解析；";
      }
      arr.push({ key: key, value: value });
    });
    return arr;
  } catch (err: any) {
    ElMessage.warning(err);
    return err;
  }
};

// 属性名称查询属性ID
const nameQueryId = (value: string, data: any[]) => {
  const valToArr = value.split(/,|，/);
  const ids: any = [];
  valToArr.forEach((name: string) => {
    data.forEach((item: any) => {
      if (item.name == name.trim()) {
        ids.push(item.id);
      }
    });
  });
  return ids;
};

// 属性ID查询属性名称
const idQueryName = (value: any, data: any[]) => {
  const names: any = [];
  value.forEach((id: string) => {
    data.forEach((item: any) => {
      if (item.id == id) {
        names.push(item.name);
      }
    });
  });
  return names;
};

// 点击属性回显商品简介
const feedBackInfo = () => {
  const infoArr: any = [];
  const titleArr: any = [];
  // 区服
  const gameAreItem = sysgameList.value.filter((item: any) => item.id == dataForm.gameAre)[0];
  infoArr.push(`服务器：${gameAreItem ? gameAreItem.title : ""}`);
  if (sysgameChildrenList.value.length > 0) {
    const serverItem = sysgameChildrenList.value.filter((item: any) => item.id == dataForm.server)[0];
    infoArr.push(`系统区服：${serverItem ? serverItem.title : ""}`);
  }
  // 属性
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 1) {
      const names = idQueryName([item.attributeIds], item.children).join("");
      textAttributeBackfilling(item.name, names); // 单选属性回显到文本
      infoArr.push(`${item.name}：${names}`);
    }
    if (item.type == 2) {
      const names = idQueryName(item.attributeIds, item.children).join("，");
      checkboxTotalBackfill(item.name, item.attributeIds.length);
      infoArr.push(`${item.name}：${names}`);
    }
    if (item.type == 3) {
      if (!item.name.includes("【文本】")) {
        infoArr.push(`${item.name}：${item.attributeText}`);
      }
    }
  });

  dataForm.info = infoArr.join("\n");

  // 拼接商品标题
  // 获取文本类型属性
  const textArr = dataForm.attributesList.filter((item: any) => item.type == 3 && item.isTitle == 0);
  if (textArr.length > 0) {
    textArr.forEach((item: any) => {
      if (item.attributeText) {
        titleArr.push(`${item.name}${item.attributeText}`);
      }
    });
  }

  // 获取单选类型属性
  const radioArr = dataForm.attributesList.filter((item: any) => item.type == 1 && item.isTitle == 0);
  if (radioArr.length > 0) {
    radioArr.forEach((item: any) => {
      const names = idQueryName([item.attributeIds], item.children).join("");
      if (names) {
        titleArr.push(`【${names}】`);
      }
    });
  }

  // 获取多选类型属性 -- 统计个数
  const checkboxArr = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0);
  if (checkboxArr.length > 0) {
    checkboxArr.forEach((item: any) => {
      if (item.attributeIds.length > 0) {
        titleArr.push(`${item.name}${item.attributeIds.length}`);
      }
    });
  }

  // 获取多选类型属性 -- 详情内容
  const checkboxArrDetails = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0);
  if (checkboxArrDetails.length > 0) {
    checkboxArrDetails.forEach((item: any) => {
      if (item.attributeIds.length > 0) {
        const names = idQueryName(item.attributeIds, item.children).join("，");
        titleArr.push(`${item.name}：${names}；`);
      }
    });
  }

  // 数组最前面添加亮点信息
  if (dataForm.highlights) titleArr.unshift(`【${dataForm.highlights}】`);
  // 有商品编码，进行拼接
  if (dataForm.code) titleArr.unshift(dataForm.code);
  dataForm.title = titleArr.join(" ").slice(0, 1500);
};

// 单选属性回填文本
const textAttributeBackfilling = (label: any, value: any) => {
  // 属性
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 3 && item.name.includes("【文本】")) {
      if (item.name.slice(0, -4) == label) {
        item.attributeText = value;
      }
    }
  });
};

// 多选属性合计回显
const checkboxTotalBackfill = (label: any, value: any) => {
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 3 && item.name.includes("【合计】")) {
      if (item.name.slice(0, -4) == label) {
        item.attributeText = value;
      }
    }
  });
};

// 营地号回显到文本
const campsiteIdFeedback = (event: FocusEvent) => {
  dataForm.attributesList.forEach((item: any) => {
    if ((item.type == 3 && (item.name == "营地号" || item.name == "营地ID" || item.name == "营地Id")) || item.name == "营地id") {
      item.attributeText = campsiteForm.campsiteId;
    }
  });
};

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = (status: any) => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }

    if (!isWrite.value) {
      ElMessage.warning("请完成商品信息的填写！");
      return false;
    }

    // 商品标题长度校验
    // if(dataForm.title.length > (!dataForm.id ? 230 : 240) ){
    //   return ElMessage.warning(`商品标题长度不能超过${!dataForm.id ? 230 : 240}个字符！`);
    // }

    const params = JSON.parse(JSON.stringify(dataForm));
    // 处理单选属性改为数组
    params.attributesList.map((item: any) => {
      if (item.type == 1) {
        item.attributeIds = item.attributeIds ? [item.attributeIds] : [];
      }
      if (item.type == 2) {
        item.attributeIds = item.attributeIds ? item.attributeIds : [];
      }
      if (item.type == 3) {
        item.attributeIds = [];
      }
    });
    // 处理游戏主图改为字符串
    params.log = params.log.join("");
    btnLoading.value = true;

    if (props.source == "audit") {
      params.status = status;
      delete params.id;
      localStorage.shopDraft = "";
      // 审核通过
      await baseService.post("/shop/shopaudit", { id: dataForm.id, status: "1" });

      baseService
        .post("/shop/shopaudit/add", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 1000,
            onClose: () => {
              drawerCloseHandle("");
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });

      return;
    }

    // 新增商品
    dataForm.id ? "" : (params.accountSource = props.accountSource || "ORIGINAL_OWNER");
    localStorage.shopDraft = "";
    (!dataForm.id ? baseService.post : baseService.put)("/shop/shop", params)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 1000,
          onClose: () => {
            emit("refreshDataList", dataForm.id ? "update" : "");
            drawerCloseHandle("");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 回收订单 完善信息提交
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) return false;

    dataFormDetailRef.value.validate((valid: boolean) => {
      if (!valid) {
        return ElMessage.warning("请将商品详情填写完整！");
      }

      if (!isWrite.value) {
        ElMessage.warning("请完成商品信息的填写！");
        return false;
      }

      // 商品标题长度校验
      // if(dataForm.title.length > 230){
      //   return ElMessage.warning("商品标题长度不能超过230个字符！");
      // }

      const params = JSON.parse(JSON.stringify(dataForm));
      params.attributesList = JSON.parse(JSON.stringify(dataForm.attributesList));

      emit("completeInfo", params.attributesList, dataForm);
    });
  });
};

// 商品详情表单校验
const dataFormDetailSubmitHandle = () => {
  dataFormDetailRef.value.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.warning("请将商品详情填写完整！");
      return false;
    }
    isWrite.value = true;
    pageShow.value = false;
  });
};

// 加载营地数据
const campsiteButLoad = ref(false);
const loadCampsite = (roleId?: string) => {
  campsiteButLoad.value = true;
  baseService
    .post("/shop/shop/roleByCampsiteId", { campsiteId: campsiteForm.campsiteId })
    .then((res) => {
      if (res.code == 0) {
        gameRoleList.value = res.data.map((item: any) => {
          return {
            ...item,
            areaName: districtServiceConversionName(dataForm.gameCode, item.areaName)
          };
        });

        // currentRoleId.value = roleId || gameRoleList.value[0].roleId;
        // if (!roleId) campsiteChange(currentRoleId.value);
      }
    })
    .finally(() => {
      campsiteButLoad.value = false;
    });
};

// 王者荣耀游戏数据
const getWzryAttributesList = () => {
  attributeLoading.value = true;
  baseService
    .get("/autoCheck/autoCheck/wzry/getWzryAttrByCampsiteId", {
      campsiteId: campsiteForm.campsiteId,
      roleId: currentRoleId.value
    })
    .then((res: any) => {
      
      unrecognizedList.value = [];
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      recognizedList.value = attrList;
      autoChecktAttributes();
      feedBackInfo();

      autoCheckServers();
    }).finally(()=>{
      attributeLoading.value = false;
    })
};

watch(
  () => dataForm.gameId,
  (newVal) => {
    setTimeout(() => {
      try {
        const gameName = gamesList.value.filter((item: any) => item.id == newVal)[0].title;
        campsiteForm.gameName = gameName;
      } catch (error) {
        console.log(error);
      }
    }, 1000);
  }
);

watch(
  () => props.submitLoading,
  (newVal) => {
    btnLoading.value = newVal;
  }
);

// 选择角色
const campsiteChange = (value: any) => {
  // 角色ID位置
  const roleIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "**********");
  // 营地ID位置
  const campIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "2024112610");
  if (roleIndex < 0) {
    // 营地角色ID
    dataForm.attributesList.push({
      typeId: "**********",
      attributeText: value
    });
    // 营地ID
    dataForm.attributesList.push({
      typeId: "2024112610",
      attributeText: campsiteForm.campsiteId
    });
  } else {
    dataForm.attributesList[roleIndex].attributeText = value;
    dataForm.attributesList[campIndex].attributeText = campsiteForm.campsiteId;
  }
};

// 表单初始化
const init = (gameId: string, id?: any) => {
  visible.value = true;
  dataForm.gameId = gameId;
  dataForm.id = id ? id : null;
  if (props.source == "audit") dataForm.auditId = id;

  getGamesList();
  if (dataForm.gameId) {
    getSysgame();
    getAttribute();
    getShopDraft();
  }

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};

// 是否有暂存的数据
const getShopDraft = () => {
  let data = localStorage.shopDraft ? JSON.parse(localStorage.shopDraft) : null;
  if (data && !dataForm.id && data.source == props.source && (props.source == "shop" || props.source == "tenantShop") && (data.log || (data.imagesList && data.imagesList.length) || data.info || data.title || data.price || data.acquisitionPrice || data.gameAccount)) {
    ElMessageBox.confirm("当前存在未提交的数据，是否继续编辑?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(() => {
        console.log(dataForm, data);
        // 处理主图回显
        data.log = data.log ? [data.log] : [];
        Object.assign(dataForm, data);
        isWrite.value = data.isWrite;
      })
      .catch((err) => {
        console.log(err);
        // catch error
      });
  }
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getInfo = (id: number) => {
  requestLoading.value = true;
  baseService
    .get(props.getDetialApi + id)
    .then((res) => {
      // console.log(res.data)
      if (!res.data) return;

      const data = res.data?.shopAuditDTO || res.data;

      // 处理主图回显
      data.log = data.log ? [data.log] : [];
      // 处理详情图回显
      data.images = data.images ? JSON.parse(data.images) : [];
      dataForm.imagesList = data.images;
      // 处理包赔标签
      data.compensation = data.compensation?.toString();
      // 处理议价标签
      data.bargain = data.bargain?.toString();
      // 处理顶级账号标签回显
      data.topped = data.topped?.toString();
      // 动态商品信息处理
      try {
        data.accountInfo = JSON.parse(data.accountInfo);
        data.basicInfo = JSON.parse(data.basicInfo);
      } catch (error) {}

      // 处理服务器回显
      if (sysgameList.value) {
        const list = sysgameList.value;
        for (let i = 0; i < list.length; i++) {
          if (list[i].id == data.server) {
            dataForm.gameAre = list[i].id;
            break;
          } else {
            for (let j = 0; j < list[i].children.length; j++) {
              if (list[i].children[j].id == data.server) {
                sysgameChildrenList.value = list[i].children;
                dataForm.gameAre = list[i].id;
                dataForm.server = list[i].children[j].id;
                break;
              }
            }
          }
        }
      }

      if (data.attributesList) {
        // 处理属性回显
        data.attributesList.forEach((ele: any) => {
          // 角色ID回显
          if (ele.typeId == "**********") {
            currentRoleId.value = ele.attributeText;
          }

          // 营地ID回显
          if (ele.typeId == "2024112610") {
            campsiteForm.campsiteId = ele.attributeText;
            setTimeout(() => {
              loadCampsite(currentRoleId.value);
            }, 1000);
          }

          // 属性回显
          dataForm.attributesList.forEach((item: any) => {
            if (item.typeId == ele.typeId) {
              if (item.type == 1) {
                item.attributeIds = ele.attributeIds.join("");
              }
              if (item.type == 2) {
                item.attributeIds = ele.attributeIds;
              }
              if (item.type == 3) {
                item.attributeText = ele.attributeText;
              }
              Object.assign(ele, item);
            }
          });
        });
      }

      if (data.attributesList) {
        const attributesList = Object.assign(data.attributesList, dataForm.attributesList);
        data.attributesList = attributesList;
      }
      Object.assign(dataForm, data);
      // console.log(dataForm,'====== dataform =-=====')
      if (props.source == "order") {
        analysisFn("getInfo");
      }

      // 是否填写回显
      if (dataForm.info && (dataForm.compensation || dataForm.compensation === "0")) isWrite.value = true;
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

// 扫码完成之后查询属性
const attributeLoading = ref(false);
// 查询到的 用户信息
const gameUserInfo = ref(<any>{});

// 当前选中的角色
const currentRoleId = ref("");

// 获取角色信息
const gameRoleList = ref([]);
const gameCodeStr = ref("");
const qrcodeAuthType = ref("qq");
const getRoleInfo = async (info: any, gameCode: string, authType: string) => {
  // return getGameRoleAttributes();
  qrcodeAuthType.value = authType;
  unrecognizedList.value = [];

  gameUserInfo.value = info;
  gameCodeStr.value = gameCode;

  // 无畏契约 没有角色列表
  if (gameCode == "WWQY") {
    // console.log(info,'===== 无畏契约扫码回调 ========');
    const roleIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "2025032209");
    if (roleIndex < 0) {
      dataForm.attributesList.push({
        typeId: "2025032209",
        attributeText: info.openId
      });
    } else {
      dataForm.attributesList[roleIndex].attributeText = info.openId;
    }

    // console.log(dataForm.attributesList,'===== 无畏契约扫码更新属性列表 ========');

    // 区服勾选
    if (authType == "qq") {
      let obj = sysgameList.value.find((item: any) => item.title == "QQ");
      dataForm.gameAre = obj.id;
      dataForm.server = obj.id;
    } else {
      let obj = sysgameList.value.find((item: any) => item.title == "微信");
      dataForm.gameAre = obj.id;
      dataForm.server = obj.id;
    }

    return getGameRoleAttributes();
  }

  let res: any = await baseService.post("/autoCheck/autoCheck/getRole", {
    ...gameUserInfo.value,
    // userId: "483673464",
    // token: "CrTV2GkN",
    gameCode: gameCodeStr.value
  });

  if (res.code == 0) {
    if (!res.data || !res.data.length) {
      attributeLoading.value = false;
      return;
    }
    gameRoleList.value = res.data.map((item: any) => {
      return {
        ...item,
        areaName: districtServiceConversionName(dataForm.gameCode, item.areaName, qrcodeAuthType.value)
      };
    });
    currentRoleId.value = gameRoleList.value[0].roleId;
  }
};

// 自动勾选服务器
const autoCheckServers = () => {
  let currentRoleName = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);

  if (!sysgameList.value[0].children || !sysgameList.value[0].children.length) {
    let obj = sysgameList.value.find((item: any) => item.title == currentRoleName.areaName);
    if (!obj) return;
    dataForm.gameAre = obj.id;
    dataForm.server = obj.id;
    return;
  }

  sysgameList.value.map((item: any) => {
    item.children.map((sub: any) => {
      if (sub.title == currentRoleName.areaName) {
        dataForm.gameAre = item.id;
        dataForm.server = sub.id;
        sysgameChildrenList.value = item.children;
      }
    });
  });
};

// 未识别属性列表
const unrecognizedList = ref(<any>[]);
// 已识别属性列表
const recognizedList = ref(<any>[]);

// 根据角色获取 游戏资产属性 自动勾选
const getGameRoleAttributes = async () => {
  attributeLoading.value = true;
  let currentRole: any = "";
  if (currentRoleId.value) currentRole = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);
  if (gameCodeStr.value != "WWQY") autoCheckServers();
  try {
    let res: any = await baseService.post("/autoCheck/autoCheck/getAttr", {
      // userId: "483673464",
      // token: "CrTV2GkN",
      gameCode: gameCodeStr.value,
      roleIdList: [currentRoleId.value],
      ...gameUserInfo.value,
      ...currentRole
    });

    if (res.code == 0) {
      attributeLoading.value = false;
      unrecognizedList.value = [];
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      let cfpcSpecialWeapon = res.data.cfpcSpecialWeapon ? res.data.cfpcSpecialWeapon : [];
      recognizedList.value = [...attrList, ...cfpcSpecialWeapon];
      autoChecktAttributes();
      feedBackInfo();
    }
  } catch (error) {
    attributeLoading.value = false;
  }
};

// 扫码成功 自动勾选商品属性
const autoChecktAttributes = (attributeList?: any) => {
  let arr = attributeList ? [...attributeList, ...recognizedList.value] : recognizedList.value;
  let list = arr.join(",");

  dataForm.attributesList.map((item: any) => {
    if (item.type == 1) {
      // 单选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids.join("");
    }
    if (item.type == 2) {
      // 多选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids;
    }
  });
};

// 关闭时保存为草稿
const drawerCloseHandle = (type?: string) => {
  if (!visible.value) return;
  visible.value = false;
  if (type != "close" || dataForm.id || (props.source != "shop" && props.source != "tenantShop")) return;
  dataForm.isWrite = isWrite.value;
  const params = JSON.parse(JSON.stringify(dataForm));

  // 处理游戏主图改为字符串
  try {
    params.log = params.log.join("");
  } catch (error) {}
  params.source = props.source;

  localStorage.shopDraft = JSON.stringify(params);
};

const attributeRecognitionResultRef = ref();
const showFailureAttribute = () => {
  attributeRecognitionResultRef.value.init(unrecognizedList.value);
};

watch(
  () => unrecognizedList.value,
  (val: any) => {
    if (val && val.length) {
      attributeRecognitionResultRef.value.init(unrecognizedList.value, "first");
    }
  },
  { deep: true }
);

// 一键截图
const screenshotLoading = ref(false);
const screenshotHandle = () => {
  let currentRole: any = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);

  let params = {
    gameCode: dataForm.gameCode,
    roleIdList: [currentRoleId.value],
    ...gameUserInfo.value,
    ...currentRole
  };

  if (dataForm.gameCode == "WZRY") {
    // 营地号
    params.campsiteId = campsiteForm.campsiteId;
    params.otherMap = {
      skin: "",
      realNameStatus: ""
    };

    // 实名情况和星元皮肤 勾选后 需传参   商品主图展示用
    dataForm.attributesList.map((item: any) => {
      if (item.name == "实名情况" && item.attributeIds) {
        let realName = item.children.find((ele: any) => ele.id == item.attributeIds);
        params.otherMap.realNameStatus = realName.name;
      }
      if (item.name == "星元皮肤") {
        params.otherMap.skin = item.attributeIds.join(",");
      }
      if (item.name == "贵族等级" && item.attributeIds) {
        try {
          let vipLevelName = item.children.find((ele: any) => ele.id == item.attributeIds);
          params.otherMap.vipLevel = vipLevelName.name;
        } catch (error) {}
      }
    });
  }

  screenshotLoading.value = true;
  baseService
    .post("/autoCheck/autoCheck/screenshot", params)
    .then((res: any) => {
      setTimeout(() => {
        let { logoImage, detailImage } = res.data;
        dataForm.log = logoImage ? [logoImage] : [];
        dataForm.imagesList = detailImage ? detailImage : [];
      }, 1000);

      ElMessage.success("一键截图成功，系统已自动为您生成商品主图和详情图");
    })
    .finally(() => {
      screenshotLoading.value = false;
    });
};

// 发布
const releasedCommand = (e: number) => {
  if (e == 1) {
    dataForm.release = false;
    dataFormSubmitHandle("");
  } else {
    selectPartnersHandle();
  }
};

// 显示选择合作商
const selectPartnersRef = ref();
const selectPartnersKey = ref(0);
const selectPartnersHandle = async () => {
  selectPartnersKey.value++;
  await nextTick();
  selectPartnersRef.value.init();
};

// 推送发布
const pushSubmit = (ids: any) => {
  dataForm.release = true;
  dataForm.partnerIds = ids;
  dataFormSubmitHandle("");
};

// 黑号信息查询
const BlackNumber = (type: number) => {
  if (type == 1) {
    // 游戏账号
    if (!dataForm.gameAccount) return;
    baseService
      .post("/shop/shop/checkAccountIsBlack", {
        gameAccount: dataForm.gameAccount
      })
      .then((res) => {
        console.log(res);
      })
      .catch(() => {
        ElMessageBox.confirm("当前账号标记为黑号，是否保存该账号?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {})
          .catch(() => {
            dataForm.gameAccount = "";
          });
      });
  }
  if (type == 2) {
    // 手机号码
    if (!dataForm.phone) return;
    baseService
      .post("/shop/shop/checkAccountIsBlack", {
        phone: dataForm.phone
      })
      .then((res) => {})
      .catch(() => {
        ElMessageBox.confirm("当前手机号码标记为黑号，是否保存该号码?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {})
          .catch(() => {
            dataForm.phone = "";
          });
      });
  }
};

const taskType = ref(1);
const typeList = [
  {
    typeCode: 1,
    typeName: "寄售"
  },
  {
    typeCode: 2,
    typeName: "上架"
  },
  {
    typeCode: 3,
    typeName: "擦亮"
  },
  {
    typeCode: 4,
    typeName: "编辑"
  },
  {
    typeCode: 5,
    typeName: "下架"
  }
];

const typeListAPI = [
  {
    typeCode: 1,
    typeName: "上架"
  },
  {
    typeCode: 2,
    typeName: "下架"
  }
];

const thirdType = ref("1");
const thirdPartyRef = ref();
const replyLoading = ref(false);
const pushAllSelect = () => {
  thirdPartyRef.value.pushSubmit(taskType.value);
};

const spliceChildren = (item:any) =>{
  let list = JSON.parse(JSON.stringify(item.children));
  return !item.fold ? list : list.splice(0,10);
}

defineExpose({
  init,
  drawerCloseHandle
});
</script>

<style lang="less" scoped>
.shop_page {
  background-color: #f0f2f5;
  // border-radius: 8px;
  padding: 12px;
}

.games {
  padding: 20px 6px 10px 6px;

  .gamesList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;

    .gamesItem {
      min-width: 120px;
      height: 28px;
      font-size: 12px;
      line-height: 28px;
      background-color: #fff;
      border: 1px solid var(--el-color-primary);
      border-radius: 4px;
      text-align: center;
      margin: 0px 10px 10px 10px;
      cursor: pointer;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    .active {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }
  }

  .icons {
    padding: 6px 10px 10px 10px;

    .el-icon {
      font-size: 16px;
    }
  }
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}

.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}

.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}

.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .flod_tab_cont {
    flex: 1;
    overflow: hidden;

    :deep(.el-radio) {
      margin-right: 20px;
    }

    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}

.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}

.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .shop_info_left {
    display: flex;
    align-items: center;

    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }

  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
</style>
<style lang="less">
.demo-tabs > .el-tabs__header {
  margin: 0 20px 0;
}
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    padding: 0px;
  }
}
</style>
