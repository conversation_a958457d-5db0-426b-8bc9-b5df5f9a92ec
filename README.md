[ main  开发、测试分支 ][开发功能]

# 功能开发测试完成后 去rePro分支合并main
- git checkout rePro
- git pull
- git merge main  
- git push


[ rePro  中间分支][改线上的bug修改， 确认下次更新需要上线且能够完成的新功能]
```
只合并main分支 
若是新功能， 提交时commit加上【新功能】
```
# bug修复提交后 去main分支合并rePro
- git checkout main
- git pull
- git merge rePro  
- git push 


[ pro  正式分支 ][发版]
```
只合并rePro分支
```
# 发布版本前拉代码
- git pull
- git merge rePro  
- git push 


[ contract  合同开发分支 ][暂定，合同开发完再说]
```
最新一次合并 2025年7月16日11:08:07
```

[ wdfDev  进瑞分支 ][开发]
```
最新一次合并 2025年7月16日11:08:07
```

[ jzjDev  蒋分支 ][开发]
```
最新一次合并 2025年7月16日11:08:07
```

### 重要代码提交
[ 【API SO】 ][2025/4/1]
- 目的：多域名重复打包优化
- 实现：通过域名映射对应API, 实现只一次打包
- 备注： 
    1. 配置页面快捷搜素： API Config
    2. 未配置项会获取.env文件API
    3. 开发环境仍通过.env修改配置