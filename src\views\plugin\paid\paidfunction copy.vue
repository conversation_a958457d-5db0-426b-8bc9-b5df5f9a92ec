<template>
  <div class="mod-paid__tbpaidfunction">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '所有功能', value: 0 },
          { label: '已开通功能', value: 1 }
        ]"
        v-model="currentTypeIndex"
        value="value"
        label="label"
        @change="tabsTypeChange"
      ></ny-flod-tab>
      <template v-if="currentTypeIndex == 0">
        <div class="flx-between">
          <ny-button-group :list="chargingMethodList" v-model="state.dataForm.optionType" @change="stateChange"></ny-button-group>

          <ny-form-slot :alignRight="true" class="flx-1">
            <template v-slot:content>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <el-form-item>
                  <el-input v-model="state.dataForm.name" placeholder="功能名称" clearable>
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <search />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <ny-select
                      v-model="state.dataForm.type"
                      dict-type="paid_function_type"
                      placeholder="游戏范围"
                      @change="state.getDataList()"
                    ></ny-select>
                  </el-form-item> -->
              </el-form>
            </template>
            <template v-slot:button>
              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </template>
          </ny-form-slot>
        </div>
        <el-card shadow="never" class="rr-view-ctx-card" v-loading="state.dataListLoading">
          <el-row class="card-list">
            <el-col class="card-item" :xs="24" :sm="12" :md="8" :lg="6" v-for="(item, index) in state.dataList" :key="index">
              <div body-class="card-item-content" class="card-content" @click="viewDetail(item)">
                <div class="image-wrap flx-center">
                  <el-image v-if="item.coverImg" class="image" :src="item.coverImg" fit="cover" />
                </div>
                <div class="info">
                  <div class="flx-align-center title-wrap method-tag">
                    <span class="method" v-if="isConfigureTrial(item)">试用</span>
                    <span class="method-1" v-else-if="item.chargingMethod == 1">按次</span>
                    <span class="method-2" v-else-if="item.chargingMethod == 2">包月</span>
                    <span class="method-3" v-else-if="item.chargingMethod == 3">永久</span>
                    <div class="title">{{ item.name }}</div>
                  </div>
                  <div class="flx-between mt-10">
                    <div class="flx-align-end price-wrap">
                      <span class="symbol" v-if="!isConfigureTrial(item)">￥</span>
                      <span class="price">{{ isConfigureTrial(item) ? "免费" : item.price }}</span>
                      <span class="after" v-if="!isConfigureTrial(item) && (item.chargingMethod == 1 || item.chargingMethod == 2)">起</span>
                    </div>
                    <div class="open-btn" @click.stop="onEditAction(item.id, 'open')">立即开通</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <ny-no-data type="3" v-if="!state.dataList || !state.dataList.length" />

          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle">
          </el-pagination>
        </el-card>
      </template>
      <template v-if="currentTypeIndex == 1">
        <!-- 已开通功能 -->
        <div class="pb-20">
          <ny-table :state="state" :columns="columns" :show-summary="true" :summary-method="getSummaries" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
            <template #header>
              <ny-button-group :list="chargingMethodList" v-model="state.dataForm.chargingMethod" @change="stateChange"></ny-button-group>
            </template>
            <template #header-right>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <el-form-item>
                  <el-input v-model="state.dataForm.name" placeholder="功能名称" clearable>
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <search />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <ny-select
                      v-model="state.dataForm.type"
                      dict-type="paid_function_type"
                      placeholder="游戏范围"
                      @change="state.getDataList()"
                    ></ny-select>
                  </el-form-item> -->
              </el-form>
              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </template>

            <!-- 游戏范围 -->
            <template #type="{ row }">
              <span>全平台</span>
            </template>

            <!-- 已使用 -->
            <template #haveBeenUsed="{ row }">
              <template v-if="row.optionType == 0">
                <el-link @click="viewUseDetail(row)" type="primary" v-if="row!.trialType == '0' "> {{ row.total - row.remainCount }}/{{ row.total || 0 }}{{ row.name.includes('自动上号')?'点':'次'}}</el-link>
                <span v-else> {{ row.usage || 0 }}/{{ row.total || 0 }}月</span>
              </template>
              <el-link @click="viewUseDetail(row)" type="primary" v-if="row.optionType == 1">{{ row.total - row.remainCount }}/{{ row.total || 0 }}{{ row.name.includes('自动上号')?'点':'次'}}</el-link>
              <span v-if="row.optionType == 2">{{ row.usage || 0 }}/{{ row.total || 0 }}月</span>
              <span v-if="row.optionType == 3">永久 </span>
            </template>

            <!-- 开通金额 -->
            <template #payAmount="{ row }">
              <span v-if="row.optionType == 0">免费</span>
              <span v-else>{{ row.payAmount || "-" }}</span>
            </template>

            <!-- 收费方式 -->
            <template #optionType="{ row }">
              <!-- <el-tag v-if="row.optionType == 0" color="#DAFFE6" style="color: #00c568">试用</el-tag> -->
              <el-tag v-if="row.chargingMethod == 1" color="#CDF0FF" style="color: #00a5ef">按次</el-tag>
              <el-tag v-if="row.chargingMethod == 2" type="primary">包月</el-tag>
              <el-tag v-if="row.chargingMethod == 3" color="#DDDCFF" style="color: #5654ff">永久</el-tag>
            </template>

            <!-- 功能状态 -->
            <template #state="{ row }">
              <!-- 试用 -->
              <template v-if="row.optionType == 0">
                <!-- 按次数 -->
                <el-tag v-if="row.trialType == 0 && row.total - row.remainCount >= row.total" type="warning">已用完</el-tag>
                <!-- 按月数 -->
                <el-tag v-else-if="row.trialType == 1 && isExpired(row.expireTime)" type="warning">已到期</el-tag>
                <el-tag v-else color="#DAFFE6" style="color: #00c568">试用中</el-tag>
              </template>
              <!-- 按次数 -->
              <el-tag v-else-if="row.optionType == 1 && row.total - row.remainCount >= row.total" type="warning">已用完</el-tag>
              <!-- 按月数 -->
              <el-tag v-else-if="row.optionType == 2 && isExpired(row.expireTime)" type="danger">已过期</el-tag>
              <el-tag v-else type="primary">使用中</el-tag>
            </template>

            <!-- 操作 -->
            <template #operation="{ row }">
              <el-button type="primary" bg text @click="onEditAction(row.functionId, 'renewal')">续费</el-button>
            </template>
          </ny-table>
        </div>
      </template>
    </el-card>
  </div>

  <!-- 开通 -->
  <opened :key="addKey" ref="openedRef" @refreshDataList="state.query"></opened>

  <!-- 详情 -->
  <detail ref="detailRef" @open="onEditAction"></detail>

  <!-- 使用明细 -->
  <paidUsed ref="usedetailRef"></paidUsed>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { computed, nextTick, reactive, ref, toRefs, watch } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { BigNumber } from "bignumber.js";
import dayjs from "dayjs";
import Opened from "./paidfunction-opened.vue";
import Detail from "./paidfunction-detail.vue";
import paidUsed from "./paidfunction-used.vue";

const store = useAppStore();

const view = reactive({
  getDataListURL: "/paid/function/page",
  getDataListIsPage: true,
  limit: 12,
  dataForm: {
    platform: import.meta.env.VITE_PLATFORM_MODE,
    name: "",
    type: "",
    optionType: "",
    chargingMethod: ""
  },
  createdIsNeed: true
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const currentTypeIndex = ref(0);
const tabsTypeChange = () => {
  state.dataList = [];
  state.dataForm.optionType = "";
  state.dataForm.chargingMethod = "";

  // 已开通功能列表
  view.getDataListURL = currentTypeIndex.value == 0 ? "/paid/function/page" : "paid/function/openedFunctionList";
  console.log(currentTypeIndex.value, view.getDataListURL);
  state.getDataList();
};

// 收费方式
const chargingMethodList = computed(() => {
  let list = getDictDataList(store.state.dicts, "paid_function_charging_method").filter((item) => item.dictValue != 0);
  return [{ dictValue: "", dictLabel: "全部" }, ...list];
});

// 选择收费方式
const stateChange = (value: any) => {
  state.dataForm.chargingMethod = value;
  state.getDataList();
};

// 重置操作
const getResetting = () => {
  state.dataForm.name = "";
  state.dataForm.type = "";
  state.dataForm.optionType = "";
  state.dataForm.chargingMethod = "";
  state.getDataList();
};

// 开通
const addKey = ref(0);
const openedRef = ref();
const onEditAction = (id: any, type?: string) => {
  addKey.value++;
  nextTick(() => {
    openedRef.value.init(id, type);
  });
};

// 查看详情
const detailRef = ref();
const viewDetail = (data: any) => {
  detailRef.value.init(data);
};
// 查看明细
const usedetailRef = ref();
const viewUseDetail = (data: any) => {
  usedetailRef.value.init(data.functionId);
};

// table columns
const columns = reactive([
  {
    label: "功能名称",
    prop: "name",
    minWidth: "190"
  },
  {
    label: "功能介绍",
    prop: "introduce",
    minWidth: "200"
  },
  {
    label: "游戏范围",
    prop: "type",
    minWidth: 106
  },
  {
    label: "开通时间",
    prop: "createDate",
    minWidth: 168
  },
  {
    label: "已使用/开通范围",
    prop: "haveBeenUsed",
    minWidth: 128
  },
  {
    label: "到期时间",
    prop: "expireTime",
    minWidth: 168
  },
  {
    label: "续费时间",
    prop: "renewTime",
    minWidth: 168
  },
  {
    label: "开通金额",
    prop: "payAmount",
    minWidth: 128
  },
  {
    label: "收费方式",
    prop: "optionType",
    minWidth: 110
  },
  {
    label: "功能状态",
    prop: "state",
    minWidth: 110
  },
  {
    label: "操作",
    prop: "operation",
    fixed: "right",
    width: 90
  }
]);

// 合计行计算函数
const getSummaries = (param: any) => {
  if (currentTypeIndex.value == 0) return "";
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    // 第一列 显示文字
    if (index === 0) {
      return (sums[index] = "合计");
    } else if (column.property == "payAmount") {
      let total: any = 0;
      state.dataList.map((item: any) => {
        if (item.payAmount && item.optionType != 0) total += item.payAmount;
        // total = BigNumber(total).plus(BigNumber(item.payAmount));
      });
      return (sums[index] = total.toFixed(2));
    }
  });
  return sums;
};

// 是否到期
const isExpired = (expireTime: any) => {
  return expireTime ? dayjs(expireTime).isBefore(dayjs().format("YYYY-MM-DD HH:mm:ss")) : false;
};

// 是否配置试用
const isConfigureTrial = (item: any) => {
  const hasTrial = item?.paidFunctionOptionsVo?.trialType === 0 || item?.paidFunctionOptionsVo?.trialType === 1 ? true : false;
  return hasTrial && item?.paidFunctionOptionsVo?.trialOption && item.remainCount === null && item.expireTime === null;

  // // 没有试用
  // if(!item?.paidFunctionOptionsVo?.trialOption) return true;
  // // 剩余试用次数
  // if(item?.paidFunctionOptionsVo?.trialOption && item?.paidFunctionOptionsVo?.trialType == 0){
  // 	return item.remainCount <= 0
  // }
  // // 试用过期时间
  // return item.expireTime ? dayjs(item.expireTime).isBefore(dayjs().format("YYYY-MM-DD HH:mm:ss")) : false;
};
</script>

<style lang="less">
.mod-paid__tbpaidfunction {
  .card {
    margin-top: 20px;
  }

  .card-list {
    margin-left: -24px;

    .image-wrap {
      height: 164px;
      background: #ffffff;
      border-radius: 8px;
      overflow: hidden;
    }

    .image {
      width: 100%;
      height: 100%;
    }

    :deep(.card-item-content) {
      padding: 0;
    }

    .card-content {
      margin-left: 24px;
      margin-bottom: 24px;
      border-radius: 8px;
      cursor: pointer;
      position: relative;
      border: 1px solid #e4e7ed;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
      }

      .info {
        padding: 12px;
      }

      .title-wrap {
        .title {
          font-weight: bold;
          font-size: 16px;
          line-height: 22px;
        }
      }

      .price-wrap {
        color: #f44a29;

        .symbol {
          font-size: 12px;
        }

        .price {
          font-size: 24px;
          line-height: 20px;
          font-weight: bold;
        }

        .after {
          font-size: 12px;
          color: #606266;
          margin-left: 3px;
        }
      }

      .open-btn {
        background: #ffecbb;
        border: 1px solid #ffdd8c;
        border-radius: 4px;
        width: 104px;
        line-height: 32px;
        text-align: center;
        font-size: 16px;
        color: #5f4139;
        font-weight: bold;
      }
    }
  }

  .method-tag {
    span {
      display: flex;
      height: 22px;
      line-height: 1;
      align-items: center;
      padding: 0 4px;
      border-radius: 2px;
      margin-right: 4px;
      font-size: 14px;
    }

    .method {
      background: #daffe6;
      color: #00c568;
    }

    .method-1 {
      background: #cdf0ff;
      color: #00a5ef;
    }

    .method-2 {
      background: #daeeff;
      color: #3366ff;
    }

    .method-3 {
      background: #dddcff;
      color: #481dff;
    }
  }
}
</style>
