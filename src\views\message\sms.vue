<template>
    <div class="mod-message__sms">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                    <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <el-select v-model="state.dataForm.platform" :placeholder="$t('sms.platform')" style="width: 200px;" clearable>
                        <el-option :label="$t('sms.platform1')" :value="1"></el-option>
                        <el-option :label="$t('sms.platform2')" :value="2"></el-option>
                        <el-option :label="$t('sms.platform3')" :value="3"></el-option>
                    </el-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                
                <!-- 平台类型 -->
                <template #platform="{row}">
                    <el-tag v-if="row.platform === 1" size="small">{{ $t("sms.platform1") }}</el-tag>
                        <el-tag v-else-if="row.platform === 2" size="small">{{ $t("sms.platform2") }}</el-tag>
                        <el-tag v-else-if="row.platform === 3" size="small">{{ $t("sms.platform3") }}</el-tag>
                </template>

                <!-- 操作 -->
                <template #operation="{row}">
                    <el-button type="primary" text bg @click="sendHandle(row)">{{ $t("sms.send") }}</el-button>
                    <el-button type="warning" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update")
                        }}</el-button>
                    <el-button type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete")
                        }}</el-button>
                </template>
                
            </ny-table>

        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
        <!-- 弹窗, 发送短信 -->
        <send ref="sendRef" @refreshDataList="state.getDataList"></send>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./sms-add-or-update.vue";
import Send from "./sms-send.vue";
import { IObject } from "@/types/interface";

const sendRef = ref();

const view = reactive({
    getDataListURL: "/sys/sms/page",
    getDataListIsPage: true,
    deleteURL: "/sys/sms",
    deleteIsBatch: true,
    dataForm: {
        mobile: "",
        status: null,
        platform: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "smsCode",
        label: "短信编码",
        minWidth: 100
    },
    {
        prop: "platform",
        label: "平台类型",
        minWidth: 100,
    },
    {
        prop: "remark",
        label: "备注",
        minWidth: 120
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 220
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.platform = "";
    state.getDataList();
}

// 发送短信
const sendHandle = (row: IObject) => {
    sendRef.value.dataForm.smsCode = row.smsCode;
    sendRef.value.init();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
