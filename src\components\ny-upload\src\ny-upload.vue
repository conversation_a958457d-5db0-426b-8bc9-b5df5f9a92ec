<template>
  <div class="uploadImgStyle" :class="{ 'hide-upload': previousProps.fileList.length && previousProps.limit == 1 }">
    <el-upload
      class="avatar-uploader"
      v-model:file-list="previousProps.fileList"
      :limit="previousProps.limit > 1 ? previousProps.limit : ''"
      :accept="previousProps.accept"
      :multiple="previousProps.multiple"
      :action="url"
      drag
      list-type="picture-card"
      :show-file-list="previousProps.showFileList"
      :disabled="previousProps.disabled"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      :auto-upload="true"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
    >
      <div class="BtnSty" :style="{ width: widthUpload, height: heightUpload }">
        <el-icon class="el-icon--upload" style="color: var(--el-color-primary)"><upload-filled /></el-icon>
        <span style="font-weight: 400; font-size: 12px; color: #909399; line-height: 20px; margin-bottom: 4px">将图片拖到此处，或</span>
        <span style="color: var(--el-color-primary)">点击上传</span>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          <el-text type="warning">{{ previousProps.tip }}</el-text>
        </div>
      </template>
    </el-upload>
    <el-dialog v-model="previousProps.dialogVisible">
      <img w-full style="width: 100%" :src="previousProps.dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>
<script lang="ts">
import baseService from "@/service/baseService";
import { ref, reactive, computed, defineComponent, watch, PropType } from "vue";
import { ElMessage, UploadProps, UploadUserFile } from "element-plus";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";

export default defineComponent({
  name: "NyUpload",
  emits: ["urlArr", "update:imageUrl", "uploadSuccess"],
  props: {
    tip: {
      type: String,
      required: false,
      default: "建议格式为JPEG、PNG，2M以内"
    },
    widthUpload: {
      type: String,
      required: false,
      default: "148px"
    },
    heightUpload: {
      type: String,
      required: false,
      default: "148px"
    },
    imageUrl: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    fileList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 1
    },
    fileSize: {
      type: Number || String,
      default: 20
    },
    accept: {
      type: String,
      default: "image/*"
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  setup(props, { emit }) {
    const previousProps = reactive({
      // 文件地址
      imageUrl: props.imageUrl,
      // 是否支持多选
      multiple: props.multiple,
      // 对话框文件地址
      dialogImageUrl: "",
      // 对话框是否可见
      dialogVisible: props.dialogVisible,
      // 文件列表
      fileList: props.fileList,
      // 限制上传的文件数量
      limit: props.limit,
      // 限制上传的文件大小,传参时以参数为准。不传时默认20M
      fileSize: props.fileSize,
      // 允许上传的文件类型
      accept: props.accept,
      // 是否显示文件列表
      showFileList: props.showFileList,
      //   提示
      tip: props.tip,
      disabled: props.disabled,
      // 宽高
      widthUpload: props.widthUpload,
      heightUpload: props.heightUpload
    });
    // 监听文件地址进行列表回显
    watch(
      () => props.imageUrl,
      (newVal) => {
        // console.log("imageUrl更新", newVal)
        if (newVal) {
          previousProps.imageUrl = newVal;
          previousProps.fileList = [];
          previousProps.imageUrl.split(",").forEach((item) => {
            previousProps.fileList.push({ url: item });
          });
        } else {
          previousProps.fileList = [];
        }
      },
      { deep: true, immediate: true }
    );

    // 文件上传后端地址
    const url = `${app.api}/sys/oss/upload?token=${getToken()}`;

    // 文件上传成功的函数
    const handleAvatarSuccess = (res: any) => {
      console.log(previousProps.fileList);
      if (previousProps.limit == 1) {
        console.log(res);
        previousProps.fileList = [
          {
            url: res.data.src,
            name: res.data.name
          }
        ];
      }
      const srcArray = previousProps.fileList.map((item: any) => (item.response ? item.response.data.src : item.url));
      console.log(srcArray);
      const imageUrl = srcArray.join(",");
      emit("update:imageUrl", imageUrl);
      emit("uploadSuccess", imageUrl);
    };
    // 处理头像上传前的函数
    const beforeAvatarUpload = (file: any, uploadFiles: any) => {
      if (previousProps.limit == 1) {
        previousProps.fileList = [];
      }
      const fileSize = previousProps.fileSize * 1024 * 1024;
      if (file.size > fileSize) {
        ElMessage.warning(`上传文件大小在${previousProps.fileSize}M内，请重新选择文件上传`);
        return false;
      }
    };
    // 点击文件列表中已上传的文件时的钩子
    const handlePictureCardPreview: UploadProps["onPreview"] = (file: any) => {
      console.log(file.url);
      previousProps.dialogImageUrl = file.url;
      previousProps.dialogVisible = true;
    };
    // 文件列表移除文件时的钩子
    const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
      previousProps.fileList = uploadFiles;
      const srcArray = previousProps.fileList.map((item: any) => item.url);
      console.log("删除文件", srcArray);
      const imageUrl = srcArray.join(",");
      emit("update:imageUrl", imageUrl);
    };
    // 当超出限制时，执行的钩子函数
    const handleExceed: UploadProps["onExceed"] = (files, fileList) => {
      ElMessage.warning(`当前限制选择${previousProps.limit}个文件`);
    };

    return {
      previousProps,
      url,
      handleAvatarSuccess,
      beforeAvatarUpload,
      handlePictureCardPreview,
      handleRemove,
      handleExceed
    };
  }
});
</script>

<style scoped>
/* .avatar-uploader {
    width: 148px;
    height: 148px;
    display: block;
} */

.avatar-uploader .avatar {
  /* width: 148px;
  height: 148px; */
  display: block;
}
</style>

<style lang="scss">
.avatar-uploader .el-upload-dragger {
  border: 1px dashed var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  box-shadow: 0px 2px 5px 0px rgba(87, 128, 239, 0.1);
  border-radius: 6px 6px 6px 6px;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: fit-content;
  height: fit-content;
  transition: var(--el-transition-duration-fast);
  padding: 0;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 50px;
  height: 50px;
  text-align: center;
}

.uploadImgStyle {
  width: 100%;

  &.hide-upload {
    .el-upload,
    .el-upload__tip {
      display: none;
    }
  }

  .BtnSty {
    display: flex;

    flex-direction: column;
    align-items: center;
    justify-content: center;
    // padding: 23px 0;

    .el-icon--upload {
      font-size: 48px;
      margin-bottom: 8px;
    }
  }
  .el-upload__tip {
    .el-text {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
    }
  }

  .avatar-uploader {
    width: 100%;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 148px;
    height: 148px;
  }

  .uploadImgStyle .el-upload-list--picture-card .el-upload-list__item {
    width: 148px;
    height: 148px;
  }

  .avatar-uploader .el-upload {
    // width: 148px;
    // height: 148px;
  }
}
</style>
