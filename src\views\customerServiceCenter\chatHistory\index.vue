<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <template v-for="item in ['PERSON', 'GROUP']" :key="item">
        <ny-table
          cellHeight="ch-56"
          v-if="item == state.dataForm.channelType"
          :state="state"
          :columns="alltableColums[item]"
          @pageSizeChange="state.pageSizeChangeHandle"
          @pageCurrentChange="state.pageCurrentChangeHandle"
          @selectionChange="state.dataListSelectionChangeHandle"
          @sortableChange="sortableChange"
        >
          <template #header>
            <ny-button-group
              label="label"
              value="value"
              :list="stateList"
              v-model="state.dataForm.channelType"
              @change="
                () => {
                  keywords = '';
                  state.page = 1;
                  getResetting();
                }
              "
            ></ny-button-group>
          </template>
          <template #header-right>
            <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item>
                <!-- <el-input style="width: 280px !important" v-model="state.dataForm.groupCode" :placeholder="state.dataForm.channelType == 'PERSON' ? '请输入用户昵称' : '请输入群聊昵称'" clearable :prefix-icon="Search"></el-input> -->
                <el-select style="width: 280px !important" :loading="loading" clearable v-model="keywords" :placeholder="state.dataForm.channelType == 'PERSON' ? '请输入用户昵称' : '请输入群聊昵称'" filterable remote :remote-method="remoteMethod" @change="selectChange">
                  <el-option v-for="item in memberList" :key="item.imUid" :label="item.nickname" :value="item.imUid">
                    <div v-html="item.nickname"></div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-form>
          </template>
          <template #msgTimestamp="{ row }">
            <span>{{ formatTimeStamp(row.msgTimestamp) }}</span>
          </template>
          <!-- 操作 -->
          <template #operation="{ row }">
            <el-button v-if="state.hasPermission('group:sysqqgroup:update')" :underline="false" style="margin-right: 12px" @click="addHandle(row.imChannel)" type="primary" link>查看</el-button>
          </template>
        </ny-table>
      </template>
      <!-- 新增 -->
      <add-or-update ref="addOrUpdateRef" :key="addOrUpdataKey" @refreshDataList="state.getDataList"></add-or-update>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { useImStore } from "@/store/im";
import useView from "@/hooks/useView";
import { Search } from "@element-plus/icons-vue";
import { ref, toRefs, reactive, nextTick } from "vue";
import { searchConversation } from "@/utils/imTool";
// 组件引入
import AddOrUpdate from "./chatModal.vue";
const addOrUpdateRef = ref();
const imStore = useImStore();

const view = reactive({
  getDataListURL: "/im/login/history",
  getDataListIsPage: true,
  getDataListIsPageSize: true,
  deleteURL: "/group/sysqqgroup",
  deleteIsBatch: true,
  listRequestMethod: "post",
  // 传参
  dataForm: {
    channelType: "PERSON" //状态
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const alltableColums = reactive({
  PERSON: [
    {
      prop: "fromUserName",
      label: "发送用户"
    },
    {
      prop: "toUserName",
      label: "接收用户"
    },
    {
      prop: "msgTimestamp",
      label: "最新消息时间",
      sortable: "custom"
    },
    {
      prop: "operation",
      label: "操作",
      width: 140
    }
  ],
  GROUP: [
    // {
    //   prop: "serialNumber",
    //   label: "群聊ID",
    //   minWidth: 336
    // },
    {
      prop: "toUserName",
      label: "群聊名称",
      minWidth: 336
    },
    {
      prop: "msgTimestamp",
      label: "最新消息时间",
      minWidth: 336,
      sortable: "custom"
    },
    {
      prop: "operation",
      label: "操作",
      width: 240
    }
  ]
});
const stateList = ref([
  { value: "PERSON", label: "单聊记录" },
  { value: "GROUP", label: "群聊记录" }
]);

// 操作
const addOrUpdataKey = ref(0);
const addHandle = (id?: number) => {
  addOrUpdataKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
// 重置操作
const getResetting = () => {
  keywords.value = "";
  delete state.dataForm.fromUserId;
  delete state.dataForm.toUserId;
  state.getDataList();
};
// 排序
const sortableChange = ({ order, prop }: any) => {
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 关键词
const keywords = ref("");
const loading = ref(false);
const memberList = ref([]);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    baseService
      .get("/im/login/query", {
        search: query,
        type: state.dataForm.channelType == "PERSON" ? 1 : 2
      })
      .then((res: any) => {
        if (res.data.imGroups && res.data.imGroups.length > 0) {
          res.data.imGroups.map((item: any) => {
            item.nickname = item.groupName;
            item.imUid = item.groupId;
          });
        }
        memberList.value = state.dataForm.channelType == "PERSON" ? res.data.imUsers : res.data.imGroups;
        // 去重
        memberList.value = memberList.value.filter((item: any, index: any, self: any) => {
          return self.findIndex((t: any) => t.imUid === item.imUid) === index;
        });
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    memberList.value = [];
  }
};

// 选择
const selectChange = () => {
  if (state.dataForm.channelType == "PERSON") {
    state.dataForm.fromUserId = keywords.value;
  } else {
    state.dataForm.toUserId = keywords.value;
  }
  if (!keywords.value) {
    delete state.dataForm.fromUserId;
    delete state.dataForm.toUserId;
  }
  state.getDataList();
};
</script>
<style lang="scss" scoped>
.rr-view-ctx-card {
  // padding: 12px 24px;
  margin-top: 12px;
  :deep(.el-card__body) {
    // padding: 0;
  }
  .cards {
    display: flex;
    margin-bottom: 16px;
    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }
      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
:deep(.el-link__inner) {
  width: 36px;
  height: 22px;
  background: #f5f7fa;
  border-radius: 2px 2px 2px 2px;
}
.el-tag {
  border: 1px solid;
}
</style>
