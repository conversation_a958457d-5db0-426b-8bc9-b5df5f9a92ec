<template>
  <div>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-form-slot>
        <template v-slot:content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.name" :placeholder="$t('tenant.name')" clearable></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:button>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <div class="ny-table-button-list">
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" :label="$t('tenant.domain')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="driverClassName" :label="$t('tenant.driver')" header-align="center" align="center">
          <template v-slot="scope">
            {{ state.getDictLabel("tenant_datasource", scope.row.driverClassName) }}
          </template>
        </el-table-column>
        <el-table-column prop="url" label="URL" show-overflow-tooltip header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" :label="$t('tenant.createDate')" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template v-slot="scope">
            <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./tenant-datasource-add-or-update.vue";

const view = reactive({
  getDataListURL: "/sys/tenant/datasource/page",
  getDataListIsPage: true,
  deleteURL: "/sys/tenant/datasource",
  deleteIsBatch: true,
  dataForm: {
    name: ""
  }
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  view.dataForm.name = "";
  state.getDataList();
};
</script>
