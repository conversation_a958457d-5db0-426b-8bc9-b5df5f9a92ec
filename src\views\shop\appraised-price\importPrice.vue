<template>
  <el-dialog v-model="visible" width="480" title="价格表导入" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <div class="warnInfo">
        <div style="display: flex; align-items: flex-start">
          <div style="margin-top: 2px">
            <el-text type="warning" tag="b">
              <el-icon><WarningFilled /></el-icon>
            </el-text>
          </div>
          <div style="margin-left: 4px">
            <el-text type="warning" tag="b">
              <span>导入价格表流程：</span>
            </el-text>
            <br />
            <el-text type="warning" style="font-weight: 300">
              <span>1. 下载“价格表模板”，下载后只可编辑价格，属性名称不可更改 </span>
            </el-text>
            <br />
            <el-text type="warning" style="font-weight: 300">
              <span>2. 完成编辑后再进行导入</span>
            </el-text>
          </div>
        </div>
        <div class="file">
          <div style="display: flex; align-items: center">
            <el-icon size="16"><Document /></el-icon>
            <span style="margin-left: 4px; color: #4e5969; line-height: 22px"> {{ "价格表模板.xlsx" }}</span>
          </div>
          <div style="cursor: pointer" @click="downFile({})">
            <el-icon size="16"><Download /></el-icon>
          </div>
        </div>
      </div>
      <ny-upload-file @loadData="loadData" v-if="visible" ref="fileRef" :isContract="true" :otherUrl="otherUrl" :limit="1" v-model:fileSrc="dataForm.file" widthUpload="446px" heightUpload="185px" tip="" :isOneLine="true" accept=".xlsx"></ny-upload-file>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnloading" type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { fileExport } from "@/utils/utils";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const props = defineProps({
  gameInfo: {}
});
const btnloading = ref(false);
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  file: ""
});

const rules = ref({
  title: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});
const downFile = async (row: any) => {
  baseService.get("/appraise/attribute/exportAllAttr", { gameId: props.gameInfo.id }, { "Content-Type": "multipart/form-data" }).then((res) => {
    if (res) {
      fileExport(res, props.gameInfo.title + "价格表模板");
    }
  });
};
const init = () => {
  visible.value = true;
};
const otherUrl = ref("");
const fileRef = ref();
// 表单提交
const dataFormSubmitHandle = () => {
  btnloading.value = true;
  dataFormRef.value.validate();
  otherUrl.value = `/appraise/attribute/import?gameId=${props.gameInfo.id}`;
  fileRef.value.notAutoSubmit();
};
const loadData = () => {
  btnloading.value = false;
  visible.value = false;
  emit("refreshDataList");
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.warnInfo {
  border-radius: 4px;
  border: 1px solid #f8e3c5;
  background: #fcf6ec;
  padding: 8px;
  margin-bottom: 12px;
}
.file {
  border-radius: 4px;
  background: #ffecce;
  display: flex;
  padding: 2px 8px 2px 4px;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
}
</style>
