<template>
  <el-dialog :footer="null" v-model="visible" title="添加备注" width="800">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <el-descriptions style="width: 100%;" border :column="2">
        <el-descriptions-item :span="2" label="备注">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="请输入需要修改的备注" type="textarea" :rows="10"></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-dialog>
</template>  
      
<script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";

const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);

const rules = {};
const editType = ref(1);
const editUrl = ref('');
const init = (type: number, id?: number) => {
  visible.value = true;
  editType.value = type;
  
  if(type == 1){
    editUrl.value = '/mobile/MobileMutualDialing'
  }else if(type == 2){
    editUrl.value = '/mobile/mobileCheckPhoneBill'
  }else if(type == 3){
    editUrl.value = '/mobile/mobileQBindingQuantity'
  }
  
  if (id) {
    getDetails(id);
    return;
  }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get(`${editUrl.value}/` + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};


const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"](editUrl.value, dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
      
<style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        // align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>