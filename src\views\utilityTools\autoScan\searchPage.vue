<template>
  <div class="searchPage">
    <div class="page_left" v-if="!searchValue">
      <div class="menu">
        <el-scrollbar height="340px" style="padding: 10px 10px 0px 10px">
          <div
            class="menu_item"
            :class="{ active: index == menuindex }"
            v-for="(item, index) in props.attributesList"
            :key="index"
            @click="
              childrenList = item.children;
              menuindex = index;
            "
          >
            {{ item.name }}
          </div>
        </el-scrollbar>
      </div>
      <div class="childrenList">
        <el-scrollbar height="340px" style="padding: 10px 0px">
          <div class="items" v-for="(item, index) in childrenList" :key="index" :class="{ active: props.attributesList[menuindex].attributeIds.includes(item.id) }" @click="clickFn(item)">{{ item.attributeChildName }}</div>
        </el-scrollbar>
      </div>
    </div>
    <div class="page_result" v-else>
      <div
        class="result_header"
        @click="
          searchValue = '';
          emit('searchChange');
        "
      >
        <el-icon size="16"><ArrowLeft /></el-icon>
        <span>返回列表 / </span><span style="color: #303133; margin-left: 8px">搜索结果</span>
      </div>
      <el-scrollbar height="296px" style="padding: 10px 10px 0px 10px">
        <div class="result_content" v-if="searchList.length > 0">
          <div class="items" v-for="(item, index) in searchList" :key="index" @click="resultFn(item)" v-html="item.name"></div>
        </div>
        <el-button type="primary" @click="addFn()" v-if="searchValue && searchValue != '' && searchList.length < 1">新增关键词</el-button>
      </el-scrollbar>
    </div>
    <div class="page_right">
      <div class="page_right_center">
        <div class="empty" v-if="selectNum == 0">请选择或搜索您需要的属性</div>
        <div class="changeInfo" v-else>
          <el-scrollbar height="279px" style="padding: 10px 10px">
            <template v-for="(item, index) in props.attributesList" :key="index">
              <template v-if="item.attributeIds.length">
                <div class="attrName">{{ item.name }}</div>
                <div class="chanageItem">
                  <template v-for="(ele, ind) in item.children" :key="ind">
                    <div class="item" v-if="item.attributeIds.includes(ele.id)">
                      {{ ele.attributeChildName }}
                      <el-icon size="18" @click="cancel(item, ele)"><CircleCloseFilled /></el-icon>
                    </div>
                  </template>
                </div>
              </template>
            </template>
          </el-scrollbar>
        </div>
      </div>
      <div class="page_right_buttom">
        <div class="name">
          已选 <span>{{ selectNum }}</span> 项
          <text style="cursor: pointer; margin-left: 8px" v-if="selectNum" @click="clearClick">清空</text>
        </div>

        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from "vue";
import { CircleCloseFilled, ArrowLeft } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
const emit = defineEmits(["submit", "searchChange"]);
interface Props {
  attributesList: any;
  searchParam: any;
  partnerCode: any;
  gameId: any;
}
const props = withDefaults(defineProps<Props>(), {
  attributesList: [],
  searchParam: "",
  partnerCode: "",
  gameId: ""
});
const menuindex = ref(0); // 选中的菜单id
const childrenList = ref(<any>[]); // 选中的子菜单
const searchValue = ref(""); // 搜索值
watch(
  () => props.attributesList,
  (newVal) => {
    if (newVal.length) {
      computedNum();
      selectedList.value = [];
      props.attributesList.forEach((ele: any) => {
        ele.children.forEach((item: any) => {
          if (ele.attributeIds.includes(item.id)) {
            selectedList.value.push({ ...item, name: item.attributeChildName });
          }
        });
      });
    }
  },
  {
    deep: true
  }
);
watch(
  () => props.searchParam,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        searchValue.value = newVal;
        searchAttributes();
      });
    }
  },
  {
    immediate: true
  }
);

const searchList = ref(<any>[]); // 搜索属性列表
const searchAttributes = () => {
  searchList.value = [];
  baseService
    .get("/scan/autoScanConfig/getChildAttribute", {
      gameId: props.gameId,
      partnerCode: props.partnerCode,
      attributeChildName: searchValue.value
    })
    .then((res: any) => {
      res.data.map((item: any) => {
        searchList.value.push({
          typeId: item.id,
          attributeIds: [],
          attributeText: "",
          name: item.attributeChildName,
          attributeChildName: item.attributeChildName,
          start: "",
          end: ""
        });
      });
    });
};

onMounted(() => {
  if (props.attributesList.length) {
    childrenList.value = props.attributesList[0].children;
  }
});
const selectedList = ref(<any>[]); // 选中数据
const selectNum = ref(0);
const computedNum = () => {
  let num = 0;
  props.attributesList.forEach((item: any) => {
    num += item.attributeIds.length;
  });
  selectNum.value = num;
};

// 选择点击事件
const clickFn = (event: any) => {
  selectedList.value.push({ ...event, nanme: event.attributeChildName });
  const item = props.attributesList[menuindex.value];
  if (item.attributeIds.includes(event.id)) {
    const index = item.attributeIds.indexOf(event.id);
    item.attributeIds.splice(index, 1);
  } else {
    item.attributeIds.push(event.id);
  }
  computedNum();
};

// 取消选择
const cancel = (item: any, event: any) => {
  if (item.attributeIds.includes(event.id)) {
    const index = item.attributeIds.indexOf(event.id);
    item.attributeIds.splice(index, 1);
  } else {
    item.attributeIds.push(event.id);
  }
  selectedList.value.filter((ele) => ele.id != event.id);
  computedNum();
};

// 搜索选择
const resultFn = (event: any) => {
  selectedList.value.push({ ...event, name: event.attributeChildName });
  props.attributesList.forEach((item: any) => {
    item.children.forEach((ele: any) => {
      if (ele.id == event.typeId) {
        console.log(ele.id, event.typeId);
        if (item.attributeIds.includes(event.typeId)) {
          const index = item.attributeIds.indexOf(event.typeId);
          item.attributeIds.splice(index, 1);
        } else {
          item.attributeIds.push(event.typeId);
        }
      }
    });
  });
  searchValue.value = "";
  emit("searchChange");
  computedNum();
};

const addFn = () => {
  emit("submit", { text: searchValue.value, attributesList: selectedList.value });
};

// 清空选择
const clearClick = () => {
  props.attributesList.forEach((item: any) => {
    item.attributeIds = [];
  });
  computedNum();
};

// 确定
const submit = () => {
  emit("submit", { text: null, attributesList: selectedList.value });
  clearClick()
};
</script>

<style lang="scss" scoped>
.searchPage {
  height: 360px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  .page_left {
    flex: 1;
    height: 360px;
    display: flex;
    align-items: center;
    .menu {
      width: 180px;
      height: 360px;
      border-right: 1px solid #e4e7ed;
      .menu_item {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
        padding: 10px;
        border-radius: 8px;
        cursor: pointer;
        margin-bottom: 10px;
        &:hover {
          color: var(--el-color-primary);
          background-color: var(--el-color-primary-light);
        }
      }
      .active {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light);
      }
    }
    .childrenList {
      flex: 1;
      height: 360px;
      .items {
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
        padding: 2px 12px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        display: inline-block;
        cursor: pointer;
        margin-left: 10px;
        margin-bottom: 10px;
        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
      .active {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
  }
  .page_result {
    flex: 1;
    height: 360px;

    padding: 16px;
    .result_header {
      display: flex;
      align-items: center;
      cursor: pointer;
      span {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
      }
    }
    .result_content {
      display: flex;
      flex-wrap: wrap;
      .items {
        padding: 2px 12px;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
      }
    }
  }
  .page_right {
    width: 360px;
    height: 360px;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e4e7ed;
    .page_right_center {
      flex: 1;
      .empty {
        width: 100%;
        height: 100%;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .changeInfo {
        .attrName {
          font-weight: bold;
          font-size: 14px;
          color: #303133;
          line-height: 22px;
        }
        .chanageItem {
          display: flex;
          flex-wrap: wrap;
          .item {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 14px;
            color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
            line-height: 22px;
            padding: 5px 12px;
            border-radius: 50px;
            margin-right: 4px;
            margin-top: 4px;
            .el-icon {
              color: var(--el-color-primary);
              margin-left: 8px;
              cursor: pointer;
            }
          }
        }
      }
    }
    .page_right_buttom {
      display: flex;
      align-items: center;
      padding: 10px;
      border-top: 1px solid #e4e7ed;
      .name {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 20px;
        flex: 1;
        span {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
