<template>
  <div class="mod-sys__user TableXScrollSty">
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
          <el-button v-if="state.hasPermission('sys:user:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
        </template>

        <template #header-right>
          <div style="display: flex">
            <el-input v-model="state.dataForm.username" :placeholder="$t('user.username')" clearable style="width: 220px; margin-right: 12px"></el-input>
            <ny-select v-model="state.dataForm.gender" dict-type="gender" :placeholder="$t('user.gender')" style="width: 220px; margin-right: 12px"></ny-select>
            <ny-dept-tree v-model="state.dataForm.deptId" v-model:deptName="deptName" :placeholder="$t('dept.title')" :query="true" style="width: 220px; margin-right: 12px"></ny-dept-tree>
            <el-button type="primary" @click="state.getDataList()" style="margin-right: 12px">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
          </div>
        </template>

        <!-- 头像 -->
        <template #headUrl="{ row }">
          <el-image v-if="row.headUrl" :src="row.headUrl" :preview-src-list="[row.headUrl]" :preview-teleported="true" style="width: 40px; height: 40px; border-radius: 40px"></el-image>
          <span v-else>-</span>
        </template>

        <!-- 性别 -->
        <template #gender="{ row }">
          {{ state.getDictLabel("gender", row.gender) }}
        </template>

        <!-- 状态 -->
        <template #status="{ row }">
          <el-tag v-if="row.status === 0" size="small" type="danger">{{ $t("user.status0") }}</el-tag>
          <el-tag v-else size="small" type="success">{{ $t("user.status1") }}</el-tag>
        </template>

        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button v-if="state.hasPermission('sys:user:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    <!-- </el-card> -->
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./user-add-or-update.vue";

const deptName = ref();

const view = reactive({
  getDataListURL: "/sys/user/page",
  getDataListIsPage: true,
  deleteURL: "/sys/user",
  deleteIsBatch: true,
  exportURL: "/sys/user/export",
  dataForm: {
    username: "",
    deptId: "",
    postId: "",
    gender: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "username",
    label: "用户名",
    minWidth: 120
  },
  {
    prop: "nickname",
    label: "昵称",
    minWidth: 120
  },
  {
    prop: "realName",
    label: "真实姓名",
    minWidth: 120
  },
  {
    prop: "headUrl",
    label: "头像",
    minWidth: 120
  },
  {
    prop: "deptName",
    label: "所属部门",
    minWidth: 120
  },
  {
    prop: "email",
    label: "邮箱",
    minWidth: 150
  },
  {
    prop: "mobile",
    label: "手机号",
    minWidth: 120
  },
  {
    prop: "gender",
    label: "性别",
    minWidth: 80,
    sortable: true
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 80
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 160,
    sortable: true
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 140
  }
]);

// 重置操作
const getResetting = () => {
  state.dataForm.username = "";
  state.dataForm.deptId = "";
  state.dataForm.postId = "";
  state.dataForm.gender = "";
  deptName.value = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
