<template>
  <el-dialog v-model="visible" :title="getDialogTitle()" :close-on-click-modal="false" :close-on-press-escape="false" width="800px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="100px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="户名" prop="hm">
            <el-input v-model="dataForm.hm" placeholder="请输入户名" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账户类型" prop="zhlx">
            <el-select v-model="dataForm.zhlx" placeholder="请选择账户类型" style="width: 100%" :disabled="isView">
              <el-option label="支付宝" value="支付宝"></el-option>
              <el-option label="银行卡" value="银行卡"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账号" prop="zh">
            <el-input v-model="dataForm.zh" placeholder="请输入账号" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="khyh">
            <el-input v-model="dataForm.khyh" placeholder="请输入开户银行" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="结算往来单位" prop="jswldw">
            <el-input v-model="dataForm.jswldw" placeholder="请输入结算往来单位" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账户用途" prop="zhyt">
            <el-select v-model="dataForm.zhyt" placeholder="请选择账户用途" style="width: 100%" :disabled="isView">
              <el-option label="备用金账户" value="备用金账户" />
              <el-option label="店铺支付宝" value="店铺支付宝" />
              <el-option label="收款支付宝" value="收款支付宝" />
              <el-option label="店铺银行卡" value="店铺银行卡" />
              <el-option label="公户银行卡" value="公户银行卡" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="余额" prop="yh">
            <el-input-number v-model="dataForm.yh" :precision="2" :min="0" placeholder="请输入余额" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="待还款金额" prop="dhkje">
            <el-input-number v-model="dataForm.dhkje" :precision="2" :min="0" placeholder="请输入待还款金额" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="累计借款金额" prop="ljjkje">
            <el-input-number v-model="dataForm.ljjkje" :precision="2" :min="0" placeholder="请输入累计借款金额" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="累计还款金额" prop="ljhkje">
            <el-input-number v-model="dataForm.ljhkje" :precision="2" :min="0" placeholder="请输入累计还款金额" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账户分类" prop="zhfl">
            <el-select v-model="dataForm.zhfl" placeholder="请选择账户分类" style="width: 100%" :disabled="isView">
              <el-option label="全部" value=""></el-option>
              <el-option label="财务借款" value="财务借款"></el-option>
              <el-option label="经营账户" value="经营账户"></el-option>
              <el-option label="资金账户" value="资金账户"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="bz">
            <el-input v-model="dataForm.bz" type="textarea" :rows="3" placeholder="请输入备注" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">{{ isView ? '关闭' : '取消' }}</el-button>
      <el-button v-if="!isView" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const isView = ref(false); // 是否为查看模式

const dataForm = reactive({
  'id': "",
  "hm": "",
  "zhlx": "",
  "zh": "",
  "khyh": "",
  "zhyt": "",
  "yh": 0.0,
  "bz": "",
  "jswldw": "",
  "zjzhid": "",
  "cjry": "",
  "cjsj": "",
  "xgry": "",
  "xgsj": "",
  "sszz": "",
  "ssgw": "",
  "fId": "",
  "fTenantId": "",
  "fFlowId": "",
  "fVersion": 0,
  "fFlowTaskId": "",
  "dhkje": 0.0,
  "ljjkje": 0.0,
  "ljhkje": 0.0,
  "zhfl": "",
  "bdry": ""
});

const rules = ref({
  // accountName: [{ required: true, message: "户名不能为空", trigger: "blur" }],
  // accountType: [{ required: true, message: "账户类型不能为空", trigger: "change" }],
  // accountNumber: [{ required: true, message: "账号不能为空", trigger: "blur" }],
  // openBank: [{ required: true, message: "开户银行不能为空", trigger: "blur" }],
  // accountStatus: [{ required: true, message: "账户状态不能为空", trigger: "change" }]
});

// 获取弹窗标题
const getDialogTitle = () => {
  if (isView.value) return '查看详情';
  return dataForm.id ? '编辑' : '新增';
};

// 修改 init 方法，支持查看模式
const init = (row?: any, viewMode = false) => {
  visible.value = true;
  isView.value = viewMode;

  // 重置表单数据
  Object.assign(dataForm, {
    'id': "",
    "hm": "",
    "zhlx": "",
    "zh": "",
    "khyh": "",
    "zhyt": "",
    "yh": 0.0,
    "bz": "",
    "jswldw": "",
    "zjzhid": "",
    "cjry": "",
    "cjsj": "",
    "xgry": "",
    "xgsj": "",
    "sszz": "",
    "ssgw": "",
    "fId": "",
    "fTenantId": "",
    "fFlowId": "",
    "fVersion": 0,
    "fFlowTaskId": "",
    "dhkje": 0.0,
    "ljjkje": 0.0,
    "ljhkje": 0.0,
    "zhfl": "",
    "bdry": ""
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (row) {
    // 字段映射：row 字段 => dataForm 字段
    dataForm.id = row.id || "";
    dataForm.hm = row.hm || "";
    dataForm.zhlx = row.zhlx || "";
    dataForm.zh = row.zh || "";
    dataForm.khyh = row.khyh || "";
    dataForm.zhyt = row.zhyt || "";
    dataForm.yh = row.yh || 0.0;
    dataForm.bz = row.bz || "";
    dataForm.jswldw = row.jswldw || "";
    dataForm.zjzhid = row.zjzhid || "";
    dataForm.cjry = row.cjry || "";
    dataForm.cjsj = row.cjsj || "";
    dataForm.xgry = row.xgry || "";
    dataForm.xgsj = row.xgsj || "";
    dataForm.sszz = row.sszz || "";
    dataForm.ssgw = row.ssgw || "";
    dataForm.fId = row.fId || "";
    dataForm.fTenantId = row.fTenantId || "";
    dataForm.fFlowId = row.fFlowId || "";
    dataForm.fVersion = row.fVersion || 0;
    dataForm.fFlowTaskId = row.fFlowTaskId || "";
    dataForm.dhkje = row.dhkje || 0.0;
    dataForm.ljjkje = row.ljjkje || 0.0;
    dataForm.ljhkje = row.ljhkje || 0.0;
    dataForm.zhfl = row.zhfl || "";
    dataForm.bdry = row.bdry || "";
  }
};

const dataFormSubmitHandle = () => {
  if (isView.value) return; // 查看模式不允许提交

  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/fiowable/fdfundaccountledger", dataForm).then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>




