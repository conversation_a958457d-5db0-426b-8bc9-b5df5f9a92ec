<template>
  <el-drawer
    v-model="visible"
    :title="dataForm.id ? '编辑' : '新增'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    size="934px">

    <div class="partBg" style="min-height: calc(90vh - 32px)">
      <!-- 收款信息 -->
      <div class="detailcard cardDescriptions">
        <div class="titleSty">收款信息</div>
        <el-form
          :model="dataForm"
          :rules="rules"
          ref="dataFormRef"
          label-width="120px">

          <el-descriptions title="" :column="2" size="default" border>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>单据日期</div>
              </template>
              <el-form-item prop="loanDate">
                <el-date-picker
                  v-model="dataForm.loanDate"
                  type="date"
                  placeholder="请选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>单据编号</div>
              </template>
              <el-form-item prop="orderNumber">
                <el-input v-model="dataForm.orderNumber" placeholder="系统自动生成" :disabled="!!dataForm.id"></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>借款人名</div>
              </template>
              <el-form-item prop="borrower">
                <el-input v-model="dataForm.borrower" placeholder="请输入借款人"></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>借款人类型</div>
              </template>
              <el-form-item prop="borrowerType">
                <el-select v-model="dataForm.borrowerType" placeholder="请选择类型" style="width: 100%">
                  <el-option label="个人" value="personal"></el-option>
                  <el-option label="企业" value="company"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title" :span="2">
              <template #label>
                <div>借款人用途</div>
              </template>
              <el-form-item prop="purpose">
                <el-input v-model="dataForm.purpose" placeholder="请输入借款用途"></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>

      <!-- 基本信息 -->
      <div class="detailcard cardDescriptions">
        <div class="titleSty">基本信息</div>
        <el-form
          :model="dataForm"
          :rules="rules"
          ref="dataFormRef2"
          label-width="120px">

          <el-descriptions title="" :column="3" size="default" border>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>当前余额</div>
              </template>
              <el-form-item prop="loanAmount">
                <el-input-number
                  v-model="dataForm.loanAmount"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  style="width: 100%"
                  placeholder="请输入金额">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>当前金额</div>
              </template>
              <el-form-item prop="currentAmount">
                <el-input-number
                  v-model="dataForm.currentAmount"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  style="width: 100%"
                  placeholder="请输入金额">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>利息金额</div>
              </template>
              <el-form-item prop="interestAmount">
                <el-input-number
                  v-model="dataForm.interestAmount"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  style="width: 100%"
                  placeholder="请输入金额">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>借款金额</div>
              </template>
              <el-form-item prop="totalAmount">
                <el-input-number
                  v-model="dataForm.totalAmount"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  style="width: 100%"
                  placeholder="请输入金额">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label-class-name="title" :span="2">
              <template #label>
                <div>备注</div>
              </template>
              <el-form-item prop="remark">
                <el-input
                  v-model="dataForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注">
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>

      <!-- 转入总金额 -->
      <div class="detailcard">
        <div class="titleSty">转入总金额：{{ formatCurrency(dataForm.totalAmount || 0) }}</div>
      </div>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataFormRef2 = ref();

const dataForm = reactive({
  id: "",
  orderNumber: "",
  borrower: "",
  borrowerType: "",
  purpose: "",
  loanAmount: 0,
  currentAmount: 0,
  interestAmount: 0,
  totalAmount: 0,
  loanDate: "",
  dueDate: "",
  remark: "",
  status: "0"
});

const rules = reactive({
  borrower: [
    { required: true, message: "借款人不能为空", trigger: "blur" }
  ],
  borrowerType: [
    { required: true, message: "借款人类型不能为空", trigger: "change" }
  ],
  loanAmount: [
    { required: true, message: "借款金额不能为空", trigger: "blur" }
  ],
  loanDate: [
    { required: true, message: "借款日期不能为空", trigger: "change" }
  ]
});

const init = (id?: number) => {
  visible.value = true;

  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    orderNumber: "",
    borrower: "",
    borrowerType: "",
    purpose: "",
    loanAmount: 0,
    currentAmount: 0,
    interestAmount: 0,
    totalAmount: 0,
    loanDate: "",
    dueDate: "",
    remark: "",
    status: "0"
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  if (dataFormRef2.value) {
    dataFormRef2.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get(`/finance/loan/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 格式化金额显示
const formatCurrency = (amount: number) => {
  if (!amount) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const dataFormSubmitHandle = () => {
  Promise.all([
    dataFormRef.value?.validate(),
    dataFormRef2.value?.validate()
  ]).then(() => {
    // 计算总金额
    dataForm.totalAmount = dataForm.loanAmount + dataForm.interestAmount;

    const request = !dataForm.id
      ? baseService.post("/finance/loan", dataForm)
      : baseService.put("/finance/loan", dataForm);

    request.then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  }).catch(() => {
    ElMessage.error("请检查表单信息");
  });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.partBg {
  background: #f5f5f5;
  padding: 20px;
}

.detailcard {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;

  .titleSty {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}

:deep(.el-descriptions__body) {
  .el-descriptions__table {
    .el-descriptions__cell {
      .el-descriptions__label {
        width: 144px;
        background: #f5f7fa;
        font-weight: 500;
        color: #606266;
        padding: 12px;
        border: 1px solid #ebeef5;
      }

      .el-descriptions__content {
        padding: 12px;
        border: 1px solid #ebeef5;

        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

:deep(.el-input-group__append) {
  background-color: transparent;
}
</style>

