<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table cellHeight="ch-40" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group
            label="label"
            value="value"
            :list="stateList"
            v-model="state.dataForm.status"
            @change="
              () => {
                state.page = 1;
                state.getDataList();
              }
            "
          ></ny-button-group>
        </template>
        <template #header-custom>
          <div style="margin-bottom: 12px">
            <el-button type="primary" @click="addHandle()" v-if="state.hasPermission('group:sysqqgroup:save')">新增</el-button>
            <el-button :disabled="!state.dataListSelections || !state.dataListSelections.length" type="danger" @click="state.deleteHandle()" v-if="state.hasPermission('group:sysqqgroup:delete')">删除</el-button>
          </div>
        </template>
        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important" v-model="state.dataForm.groupCode" placeholder="请输入QQ群号码" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>
        <!-- 手机号 -->
        <template #groupCode="{ row, $index }">
          <el-text type="primary" style="cursor: pointer">{{ row.groupCode }}</el-text>
        </template>
        <!-- 状态 -->
        <template #status="{ row }">
          <el-switch v-if="state.hasPermission('group:sysqqgroup:update')" active-text="应用中" inactive-text="未启用" v-model="row.status" :active-value="1" :inactive-value="0" :loading="row.loading" @change="(e: any) => {updateStatus(e, row)}"></el-switch>
          <span v-else>{{ row.status == 0 ? "未启用" : "应用中" }}</span>
        </template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-link v-if="state.hasPermission('group:sysqqgroup:update')" :underline="false" style="margin-right: 12px" @click="addHandle(row.id)" type="primary">编辑</el-link>
          <el-link v-if="state.hasPermission('group:sysqqgroup:delete')" :underline="false" @click="state.deleteHandle(row.id)" type="danger">删除</el-link>
        </template>
      </ny-table>

      <!-- 新增 -->
      <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
      <!-- 纠偏 -->
      <correction ref="correctionRef"></correction>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { Edit, Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
// 组件引入
import AddOrUpdate from "./QQ-add-or-update.vue";
const addOrUpdateRef = ref();

const view = reactive({
  getDataListURL: "/group/sysqqgroup/page",
  getDataListIsPage: true,
  deleteURL: "/group/sysqqgroup",
  deleteIsBatch: true,
  // 传参
  dataForm: {
    status: null, //状态
    groupCode: null //手机号
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const tableColums = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "serialNumber",
    label: "编号",
    minWidth: 104
  },
  {
    prop: "groupCode",
    label: "QQ群号码",
    minWidth: 180
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 200
  },
  {
    prop: "groupName",
    label: "群名称",
    minWidth: 280
  },
  {
    prop: "groupUrl",
    label: "入群链接",
    minWidth: 280
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 280
  },
  {
    prop: "operation",
    label: "操作",
    width: 180
  }
]);
const stateList = ref([
  { value: null, label: "全部" },
  { value: 0, label: "未启用" },
  { value: 1, label: "应用中" }
]);

// 操作
const addHandle = (id?: number) => {
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
// 重置操作
const getResetting = () => {
  state.dataForm.groupCode = "";
  state.getDataList();
};
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
// 禁用&启用
const updateStatus = (status: any, row: any) => {
  if (!row.id) return;
  let form = { ...row };
  form.status = status;
  // 处理数据
  row.loading = true;
  baseService
    .put("/group/sysqqgroup", form)
    .then((res) => {
      ElMessage.success({
        message: "修改成功",
        duration: 500
      });
      state.getDataList();
    })
    .finally(() => {
      row.loading = false;
    });
};
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.rr-view-ctx-card {
  padding: 12px 24px;
  :deep(.el-card__body) {
    padding: 0;
  }
  .cards {
    display: flex;
    margin-bottom: 16px;
    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }
      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
:deep(.el-link__inner) {
  width: 36px;
  height: 22px;
  background: #f5f7fa;
  border-radius: 2px 2px 2px 2px;
}
.el-tag {
  border: 1px solid;
}
</style>
