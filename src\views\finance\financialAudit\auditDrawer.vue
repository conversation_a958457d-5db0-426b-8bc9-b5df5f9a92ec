<template>
  <el-drawer class="infoAuditDrawer" v-model="visible" size="944">
    <template #header>
      <div class="drawer_title">{{ operateData.title }}{{ !operateData.isDetail ? "审核" : "详情" }}</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <!-- 基本信息 -->
      <div class="basicInfoSty cardDescriptions" v-if="dataForm.orderType != '8' && dataForm.type != 5 && dataForm.type != 6 && dataForm.type != 7">
        <div class="titleSty">基本信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <!-- 回收放款，售后退款，提现审核 有 -->
          <template v-if="dataForm.type == 1 || dataForm.type == 2 || dataForm.type == 4">
            <el-descriptions-item label-class-name="title">
              <template #label><div>游戏名称</div> </template>
              {{ dataForm.gameName || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label><div>商品编码</div> </template>
              {{ dataForm.shopCode || "-" }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" label-class-name="title">
              <template #label><div>游戏账号</div> </template>
              {{ dataForm.gameAccount || "-" }}
            </el-descriptions-item>
          </template>
          <!-- 提现审核 -->
          <template v-if="dataForm.type == 3">
            <el-descriptions-item label-class-name="title">
              <template #label><div>提现单号</div> </template>
              {{ dataForm.orderCode }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>租户名称</div> </template>
              {{ dataForm.tenantName }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>提现金额(元)</div> </template>
              <span style="font-size: 14px; color: #f44a29; display: flex; align-items: center"><span style="line-height: 16px">￥</span><el-statistic value-style="font-size: 14px; color: #f44a29" :precision="2" :value="dataForm.amount" /></span>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>申请时间</div> </template>
              {{ formatTimeStamp(dataForm.createDate) }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>账户名称</div> </template>
              {{ dataForm.accountName }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>收款账户</div> </template>
              {{ dataForm.account }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>备注</div> </template>
              {{ dataForm.remarks }}
            </el-descriptions-item>
          </template>
          <!-- 其他-->
          <template v-else>
            <el-descriptions-item label-class-name="title">
              <template #label><div>订单号</div> </template>
              {{ detailOrderCode }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>{{ "申请类型" }}</div>
              </template>
              {{ dataForm.type != 2 ? ["", "", "", "", "回收订单", "销售订单", "售后订单", "", "", "其他收支"][+dataForm.orderType] : dataForm.saleOrderType }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>账户名称</div> </template>
              {{ dataForm.accountName }}
            </el-descriptions-item>
            <el-descriptions-item v-if="dataForm.accountType == '3'" label-class-name="title">
              <template #label> <div>开户行名称</div> </template>
              {{ dataForm.accountBank }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>收款账户</div> </template>
              {{ dataForm.account }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>{{ dataForm.type == 1 || dataForm.type == 4 ? "申请金额(元)" : "退款金额(元)" }}</div>
              </template>
              <span style="font-size: 14px; color: #f44a29; display: flex; align-items: center"><span style="line-height: 16px">￥</span><el-statistic value-style="font-size: 14px; color: #f44a29" :precision="2" :value="dataForm.amount" /></span>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>申请时间</div> </template>
              {{ formatTimeStamp(dataForm.createDate) }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>申请人</div> </template>
              {{ dataForm.creatorName }}
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <PartnerOrdersDetails :orderInfo="orderInfo" :title="operateData.title" v-if="dataForm.orderCode && dataForm.orderType == '8'"></PartnerOrdersDetails>
      <!-- 收款审核 且 不是合作商订单 -->
      <template v-if="dataForm.type == 5">
        <afterSaleInDetails v-if="dataForm.orderType == '6'" :orderInfo="orderInfo" />
        <recyleInDetails v-if="dataForm.orderType == '4'" :orderInfo="orderInfo" />
      </template>
      <!-- 其他支出 -->
      <otherOutDetails v-if="dataForm.type == 6" :orderInfo="orderInfo" />
      <!-- 其他收入 -->
      <otherInDetails v-if="dataForm.type == 7" :orderInfo="orderInfo" />
      <!-- 审核相关 -->
      <div class="basicInfoSty" v-if="!operateData.isDetail" style="padding-bottom: 0">
        <div class="titleSty">审核操作</div>
        <div class="shop_page_basic">
          <!-- 商品信息 -->
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top" style="padding: 0px">
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="审核操作" prop="status">
                  <el-radio-group v-model="dataForm.status">
                    <el-radio value="1">审核通过</el-radio>
                    <el-radio value="2">审核拒绝</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="dataForm.status == '2'">
                <el-form-item label="拒绝原因" prop="remarks_">
                  <el-input maxlength="100" show-word-limit type="textarea" v-model="dataForm.remarks_" placeholder="请输入拒绝原因" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="basicInfoSty cardDescriptions" v-if="operateData.isDetail && dataForm.type < 6">
        <div class="titleSty">付款信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item :span="2" label-class-name="title">
            <template #label> <div>审核结果</div> </template>
            {{ ["", "审核通过", "审核拒绝"][+dataForm.status] }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" v-if="dataForm.status == 2" label-class-name="title">
            <template #label> <div>拒绝原因</div> </template>
            {{ dataForm.refuseReason }}
          </el-descriptions-item>
          <template v-if="dataForm.payStatus != 0">
            <el-descriptions-item label-class-name="title">
              <template #label> <div>支付状态</div> </template>
              {{ ["待支付", "已支付", "支付失败"][+dataForm.payStatus] }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>付款方式</div> </template>
              {{ ["", "支付宝", "微信", "银行卡"][+dataForm.payType] }}
            </el-descriptions-item>
            <template v-if="dataForm.payType == 3">
              <el-descriptions-item label-class-name="title">
                <template #label> <div>账号名称</div> </template>
                {{ dataForm.bankAccountName || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title">
                <template #label> <div>开户行名称</div> </template>
                {{ dataForm.bankName || "-" }}
              </el-descriptions-item>
            </template>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>支付账号</div> </template>
              {{ dataForm.payAccount || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label> <div>支付人</div> </template>
              {{ dataForm.payUserName || "-" }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" label-class-name="title" v-if="dataForm.payType == 1">
              <template #label> <div>支付宝订单号</div> </template>
              {{ dataForm.lendingAlipayNo || "-" }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" label-class-name="title">
              <template #label> <div>支付凭证</div> </template>
              <el-image v-if="dataForm.payImage" style="width: 100px" :src="dataForm.payImage" :preview-src-list="[dataForm.payImage]" :preview-teleported="true" fit="cover"></el-image>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <div class="basicInfoSty">
        <div class="titleSty">流程进度</div>
        <el-steps direction="vertical" :active="activeLength">
          <el-step title="申请人">
            <template #description>
              <div class="cardInfo">
                <div>
                  申请人：<span style="color: var(--el-color-primary)">{{ dataForm.creatorName }}</span>
                </div>
                <div>
                  申请时间：
                  <span>{{ formatTimeStamp(dataForm.createDate) }}</span>
                </div>
              </div>
            </template>
          </el-step>
          <el-step :title="activity.name" v-for="(activity, index) in dataForm.tasks" :key="index">
            <template #description>
              <div class="cardInfo">
                <div>
                  审批人：<span style="color: var(--el-color-primary)">{{ getUserName(activity.stages) }}</span>
                </div>
                <div>
                  处理时间：
                  <span>{{ formatTimeStamp(activity.createDate) || "-" }}</span>
                </div>
                <template v-if="+activity.status > 0">
                  <div style="margin-top: 6px">
                    最终审批人：<span>{{ activity.userName || "-" }}</span>
                  </div>
                  <div>
                    审批结果：
                    <span :style="{ color: +activity.status == 1 ? '#67C23A' : '#F56C6C' }">{{ ["", "审核通过", "审核拒绝"][+activity.status] }}</span>
                  </div>
                </template>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </el-scrollbar>
    <template #footer v-if="!operateData.isDetail">
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import arrowIcon from "@/assets/images/tagicon.png";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import PartnerOrdersDetails from "./PartnerOrdersDetails.vue";
import afterSaleInDetails from "./afterSaleInDetails.vue";
import recyleInDetails from "./recyleInDetails.vue";
import otherInDetails from "./otherInDetails.vue";
import otherOutDetails from "./otherOutDetails.vue";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const isWrite = ref(false); // 商品详情是否填写
const dataFormRef = ref(); // 表单ref
const dataForm = reactive({
  status: "",
  remarks: ""
});
const rules = ref({
  // 表单必填项
  status: [{ required: true, message: "请选择审核操作", trigger: "change" }],
  remarks_: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }]
});
const operateData = reactive({
  title: "",
  isDetail: false
});

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  if (operateData.isDetail) {
    visible.value = false;
    return;
  }
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    auditHandle();
  });
};
const getUserName = (arr: any) => {
  arr = arr.filter((ele) => ele.stageName);
  return arr.map((ele) => ele.stageName).join("，");
};
const auditHandle = () => {
  btnLoading.value = true;
  let params = {
    status: dataForm.status,
    remarks: dataForm.remarks_,
    id: dataForm.id
  };
  baseService
    .post("/flowable/financialaudit/audit", params)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
const detailOrderCode = ref();
// 表单初始化
const init = (id?: String | Number, title?: String, isDetail?: String, orderCode?: string) => {
  visible.value = true;
  operateData.title = title || "***申请";
  operateData.isDetail = isDetail || false;
  detailOrderCode.value = orderCode;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  getInfo(id);
};
const activeLength = ref(0);
// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getInfo = (id: Number) => {
  requestLoading.value = true;
  baseService
    .get("/flowable/financialaudit/" + id)
    .then((res) => {
      const data = res.data;
      Object.assign(dataForm, data);
      let index_ = dataForm.tasks.findIndex((ele) => +ele.status == 0);
      activeLength.value = index_ == -1 ? dataForm.tasks.length : index_;
      let obj = dataForm.tasks.find((ele) => ele.status == 2);
      dataForm.refuseReason = obj?.remarks || "";
      if (!operateData.isDetail) {
        dataForm.status = dataForm.status == 0 ? undefined : dataForm.status;
        dataForm.remarks_ = undefined;
      }
      // console.log(dataForm,'====== 表单详情 =======');

      if (dataForm.orderCode && dataForm.orderType == "8") {
        getPartnerSaleOrder();
      }
      // 收款审核&售后订单
      if (dataForm.orderCode && dataForm.orderType == "6" && dataForm.type == 5) {
        getAfterSaleOrder();
      }
      // 收款审核&回收订单
      if (dataForm.orderCode && dataForm.orderType == "5" && dataForm.type == 5) {
        getRecyleOrder();
      }
      // 其他收支审核
      if ((dataForm.orderCode && dataForm.type == 6) || dataForm.type == 7) {
        getOtherOrder();
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

const orderInfo = ref(<any>{});
// 合作商订单详情
const getPartnerSaleOrder = () => {
  orderInfo.value = {};
  requestLoading.value = true;
  baseService
    .get("/partnerSaleOrder/getOrder/" + dataForm.orderCode)
    .then((res) => {
      if (res.code == 0) {
        const params = JSON.parse(JSON.stringify(dataForm));
        orderInfo.value = Object.assign(params, res.data ? res.data : {});
        orderInfo.value.createDate = params.createDate;
        orderInfo.value.creatorName = params.creatorName;
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};
// 售后订单详情
const getAfterSaleOrder = () => {
  // orderInfo.value = dataForm;
  orderInfo.value = {};
  requestLoading.value = true;
  baseService
    .get("/saleAfter/info/" + dataForm.orderCode)
    .then((res) => {
      if (res.code == 0) {
        const params = JSON.parse(JSON.stringify(dataForm));
        orderInfo.value = Object.assign(params, res.data ? res.data : {});
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};
// 回收订单详情
const getRecyleOrder = () => {
  // 待处理TODO 据我所知目前没有适用这个详情的接口
  // 暂时处理
  orderInfo.value = dataForm;
  console.log(orderInfo.value);
  return;
  orderInfo.value = {};
  baseService.get("" + dataForm.orderCode).then((res) => {
    if (res.code == 0) {
      orderInfo.value = res.data ? res.data.data : {};
      console.log(orderInfo.value);
    }
  });
};
// 其他收支详情
const getOtherOrder = () => {
  orderInfo.value = dataForm;
  // console.log(orderInfo.value);
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}
.shop_page {
  padding: 12px;
}
.games {
  padding: 20px 6px 10px 6px;
  .gamesList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    .gamesItem {
      min-width: 120px;
      height: 28px;
      font-size: 12px;
      line-height: 28px;
      background-color: #fff;
      border: 1px solid var(--el-color-primary);
      border-radius: 4px;
      text-align: center;
      margin: 0px 10px 10px 10px;
      cursor: pointer;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .active {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }
  }
  .icons {
    padding: 6px 10px 10px 10px;
    .el-icon {
      font-size: 16px;
    }
  }
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
}
.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}
.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;
  .flod_tab_cont {
    flex: 1;
    overflow: hidden;
    :deep(.el-radio) {
      margin-right: 20px;
    }
    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}
.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}
.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  .shop_info_left {
    display: flex;
    align-items: center;
    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="less">
.infoAuditDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-textarea__inner {
    min-height: 80px !important;
  }

  .el-step__head {
    &.is-process,
    &.is-finish {
      .el-step__icon {
        background: var(--el-color-primary);
        color: #fff;
        border-color: #fff;
        .el-step__icon-inner {
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 14px;
        }
      }
    }
    .el-step__icon {
      background: #f0f2f5;
      color: #909399;
      border-color: #fff;
      .el-step__icon-inner {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 14px;
      }
    }

    .el-step__line {
      width: 1px;
      .el-step__line-inner {
        border: 1px solid #c0c4cc;
        border-width: 0px !important;
      }
    }
  }
  .el-step__main .el-step__title {
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .cardInfo {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #ebeef5;
    padding: 16px 12px;
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 16px;
    margin-bottom: 10px;
    > div {
      margin-bottom: 2px;
    }
  }
}
</style>
