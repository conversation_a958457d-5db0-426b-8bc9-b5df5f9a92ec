<template>
    <div style="width: 100%; height: 100%" ref="PreviewChartRefs"></div>
</template>

<script lang='ts' setup>
import { ref,reactive, onMounted } from 'vue';
import * as echarts from "echarts";
interface Props {
    chartInfo: any
}
const props = withDefaults(defineProps<Props>(), {
    chartInfo:''
});

const PreviewChartRefs = ref(null); // 图表预览
const charts = ref(<any>[]);

// 配置图表
const PreviewChart = (optionValue:any) =>{
    const userGrowthChart = echarts.init(PreviewChartRefs.value);

    const option = optionValue;
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * (option.index == 2 ? 9 : 18)
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * (option.index == 2 ? 9 : 18)
     
        // sliderZoom._displayables.handleLabels[0].x = option.dataZoom.start < 10 ? leftP : 0
        // sliderZoom._displayables.handleLabels[1].x = option.dataZoom.start > 90 ? rightP : 0
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
}

onMounted(()=>{
    PreviewChart(props.chartInfo)
})

</script>

<style lang='less' scoped>

</style>