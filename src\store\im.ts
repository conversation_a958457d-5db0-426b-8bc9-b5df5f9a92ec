import { defineStore } from "pinia";
import { IObject } from "@/types/interface";
import { reacquireToken } from "@/utils/imTool";
import { getOnlineStatus, showNotificationReminders } from "@/utils/imTool";
import { browserTabTitle } from "@/utils/notification";
import { useSettingStore } from './setting';
import baseService from "@/service/baseService";
import * as RongIMLib from "@rongcloud/imlib-next";

const Events = RongIMLib.Events

export const useImStore = defineStore("useImStore", {
    state: () => ({
        
        appKey: "8luwapkv87evl",   // 生产：pgyu6atqpb23u 开发：cpj2xarlcimyn
        token: "",

        // 系统消息 targetId == sys_user_1
        systemMessageTargetId: 'sys_user_1',

        // 当前用户 imUid
        imUid: "",

        // 当前用户角色
        imRole: "",

        // 当前用户在线状态
        isOnline: true,
        
        // 当前是否有未读消息
        hasUnread: false,

        // 收到新消息
        receivedNewsMessge: {},  

        // 收到已读消息
        receivedReadMessage: {},

        // 群聊 收到群消息回执请求  收到该请求 需要 发送群消息回执响应
        groupMessageReceiptRequest: {},

        // 群聊收到已读消息
        groupReceivedReadMessage: {},
        
        // 显示自动回复设置弹窗
        showAutoReplyDialog: false,
        
        // 显示当前联系人信息
        showContactInfo: false,
        currentContactInfo: {},

        // 当前会话列表类型
        currentConversationType: '',

        // 当前会话信息
        currentConversation: {},

        // 更新会话列表
        updateConversationListKey: 0,

        // 会话列表
        imConversationList: [],

        // 免打扰会话
        imDoNotDisturbList: [],

        // 当前页面是否可见
        visibilityState: false,

        // 订单买家id
        orderBuyerId: '',

        // 当前订单详情
        orderInfo: '',

        orderTransactionProgress: [],

        // 已读同步消息
        readSysMessage: {},

        // 显示消息通知
        showMessageNotification: false,
        messageNotificationInfo: {},
        // 收到系统消息
        systemMessage: {},
        // 系统消息列表 主页用
        systemMessageList: [],
        // 消息通知 未读消息数量
        unreadMessageCount: 0,
        
    } as IObject),
    actions: {
        
        // 初始化 IMLib
        async imInit() {
            // 先移除监听
            RongIMLib.removeEventListeners(Events.MESSAGES)
            RongIMLib.removeEventListeners(Events.CONNECTED)
            RongIMLib.removeEventListeners(Events.DISCONNECT)
            RongIMLib.removeEventListeners(Events.SUSPEND)
            RongIMLib.removeEventListeners(Events.CONNECTING)
            RongIMLib.removeEventListeners(Events.READ_RECEIPT_RECEIVED)
            RongIMLib.removeEventListeners(Events.MESSAGE_RECEIPT_REQUEST)
            RongIMLib.removeEventListeners(Events.MESSAGE_RECEIPT_RESPONSE)
            RongIMLib.removeEventListeners(Events.GROUP_OPERATION)


            let res = await baseService.post("/im/login/token");
            if(res.code) return;
            this.token = res.data.token;
            this.imUid = res.data.imUserId;
            this.imRole = res.data.role;

            RongIMLib.init({ appkey: this.appKey });

            // 开始进行 IM 连接之前，需要对 IMLib 连接相关的事件添加监听，以便于在连接状态发生变化时给出相应的应对。
            RongIMLib.addEventListener(Events.CONNECTED, () => {
                console.log('连接成功');
            });
            RongIMLib.addEventListener(Events.DISCONNECT, (code) => {
                console.log('连接已断开，错误码：', code);
            });
            RongIMLib.addEventListener(Events.SUSPEND, (code) => {
                console.log('连接中断，等待自动重连恢复，错误码：', code);
            });
            RongIMLib.addEventListener(Events.CONNECTING, () => {
                console.log('连接中');
            });


            // 连接聊天服务器
            RongIMLib.connect(this.token).then(async (res) => {
                switch (res.code) {
                    // 连接成功
                    case RongIMLib.ErrorCode.SUCCESS:
                        console.log('用户连接成功');

                        // 获取会话列表
                        this.setUpdateConversationListKey();

                        // 接收消息
                        this.receiveMessages();  

                        // 查询当前用户在线状态
                        getOnlineStatus(this.imUid).then((res: any) => {
                            this.isOnline = res;
                        })
                        
                        break;

                    // token 无效；AppKey 和 Token 不匹配；Token 过期 
                    case RongIMLib.ErrorCode.RC_CONN_TOKEN_INCORRECT:
                    case RongIMLib.ErrorCode.RC_CONN_TOKEN_EXPIRED:
                        console.log('token 无效');
                        reacquireToken();
                        break;
                    default:
                        console.log('连接失败');
                        break;
                }
            })


            document.addEventListener("visibilitychange",  () => {
                if (document.visibilityState == "hidden") {
                    this.visibilityState = false;
               }else if (document.visibilityState == "visible") {
                    browserTabTitle.clear();
                    this.visibilityState = true;
                    document.title = useSettingStore().info.backendTitle ? useSettingStore().info.backendTitle : document.title;
               }
           })
        },

        // 接收消息
        receiveMessages() {
            // @@会话类型 conversationType
            // 1: 单聊
            // 3: 群聊
            // 4: 聊天室
            // 5: 客服会话
            // 6: 系统消息
            

            // @@消息类型  messageType

            // 用户内容类消息
            // 文本消息	RC:TxtMsg
            // 图片消息	RC:ImgMsg
            // GIF 图片消息	RC:GIFMsg
            // 图文消息	RC:ImgTextMsg
            // 文件消息	RC:FileMsg
            // 小视频消息	RC:SightMsg

            // 通知类消息
            // 提示条通知消息	RC:InfoNtf
            // 群组通知消息	RC:GrpNtf

            // 状态类消息及属性
            // 正在输入状态消息	RC:TypSts


            // 信令类消息
            // 单聊已读回执消息	RC:ReadNtf
            // 群聊已读回执请求消息	RC:RRReqMsg
            // 群聊已读回执响应消息	RC:RRRspMsg
            // 多端已读状态同步消息	RC:SRSMsg

            // @@消息方向
            // messageDirection
            //  1: 发送，2: 接收。

            // @@是否为@消息
            // isMentioned

            // receivedStatus == 1 已读  READ 
            // receivedStatus == 0 未读  UNREAD 


            // @@ 消息发送时间
            // sentTime
            
            const listener = (evt: any) => {
                let sysnMessageList = evt.messages.filter((item: any) => item.messageType == 'RC:SRSMsg');
                let message = evt.messages.filter((item: any) => item.messageType != 'RC:SRSMsg');
                console.log("收到消息", evt.messages);
                
                // 只显示最新10条消息
                message = message.slice(-10);
                let list = [...message, ...sysnMessageList]
                imMessagefun(list, 0);
            };

            
            const imMessagefun = (message: any, i: number) => {
                if(i >= message.length) return;
                setTimeout(() => {
                    // 多端已读状态同步消息
                    if(message[i].messageType == 'RC:SRSMsg'){
                        this.readSysMessage = message[i]
                        return
                    }

                    // 是否为系统消息
                    if(message[i].targetId == this.systemMessageTargetId) {
                        this.systemMessage = message[i];
                    }else{
                        this.receivedNewsMessge = message[i];
                    }
                    console.log('收到消息', message[i].content, i)
                    showNotificationReminders(message[i], this.visibilityState);
                    i = i + 1;
                    imMessagefun(message, i);
                }, 100);
            };



            RongIMLib.addEventListener(Events.MESSAGES, listener)


            // 设置已读消息回执监听器
            interface ReadReceiptReceivedEvent {
                conversation: any;
                messageUId?: string;
                sentTime?: number;
                senderUserId?: string | number;
                receivedUserId?: string | number;
                messageUIdList?: string[];
            }
            const onReadReceiptReceived = ({ conversation, messageUId, sentTime }: ReadReceiptReceivedEvent) => {
                this.receivedReadMessage = { conversation, messageUId };
            }
            RongIMLib.addEventListener(Events.READ_RECEIPT_RECEIVED, onReadReceiptReceived);

             // 群聊  监听消息回执请求事件
             const onMessageReceiptRequest = ({conversation, messageUId, senderUserId}: ReadReceiptReceivedEvent) => {
                // conversation 群组会话
                // messageUId 需要回执的消息UId
                // senderUserId 消息发送者Id
                this.groupMessageReceiptRequest = {conversation, messageUId, senderUserId};
                console.log(conversation, messageUId, senderUserId)
            }
            RongIMLib.addEventListener(
                Events.MESSAGE_RECEIPT_REQUEST,
                onMessageReceiptRequest
            )

            // 群聊 监听消息回执响应事件
            const onMessageReceiptResponse = ({conversation, receivedUserId, messageUIdList}: ReadReceiptReceivedEvent) => {
                // conversation 群组会话
                // receivedUserId 为消息接收者，即谁响应了之前发送的消息回执请求
                // messageUIdList 为 消息接收者已查看了的消息UId列表
                console.log(conversation, receivedUserId, messageUIdList)
                this.groupReceivedReadMessage = {conversation, receivedUserId, messageUIdList};
            }
            RongIMLib.addEventListener(
                Events.MESSAGE_RECEIPT_RESPONSE,
                onMessageReceiptResponse
            )

            // 群组操作变更 @@@
            RongIMLib.addEventListener(Events.GROUP_OPERATION, (data) => {
                // data 为 IGroupOperationInfo 类型，通过 operation 来判断操作类型
                console.log('群组操作变更', data);
            });
        },

        // 订阅在线状态  @@@
        subscribeOnlineStatus(userId?: string[]) {
            // 订阅订阅用户userId列表，也是单聊的 targetId (一次最多订阅 200 个)。
            const userIds: any = userId
            // 设置订阅时间，取值范围为[60,2592000]（单位:秒）。
            const expiry = 180000;
            // 指定订阅类型为用户在线状态。
            const type = RongIMLib.SubscribeType.ONLINE_STATUS

            RongIMLib.subscribeUserStatus(userIds, type, expiry).then((res) => {
                if (res.code === RongIMLib.ErrorCode.SUCCESS) {
                    console.log('订阅成功')
                } else if (res.code === RongIMLib.ErrorCode.RC_BESUBSCRIBED_USERIDS_COUNT_EXCEED_LIMIT) {
                    console.log('用户被订阅达到上限', res.code, res.data)
                } else {
                    console.log('订阅失败', res.code, res.msg)
                }
            })

            /**
              * 所订阅的用户的状态变更时会收到此通知。
              * 订阅过期后，融云 SDK不会主动通知您，请您自行关注过期时间。
              */
            RongIMLib.addEventListener(Events.SUBSCRIBED_USER_STATUS_CHANGE, (event) => {
                // 5.10.1 版本开始需根据数据中的 subscribeType 来判断是用户资料还是在线状态。
                // event[0].details[0].eventValue == 0 表示离线  1 表示在线
                console.log('被订阅者状态变更', event)
            })
        },

        // 取消订阅 @@@
        unsubscribeOnlineStatus(userIds: string[]) {
            // 取消订阅类型
            const type = RongIMLib.SubscribeType.ONLINE_STATUS

            RongIMLib.unSubscribeUserStatus(userIds, type).then((res) => {
                if (res.code === RongIMLib.ErrorCode.SUCCESS) {
                    console.log('取消订阅成功')
                } else {
                    console.log('取消订阅失败', res.code, res.msg)
                }
            })
        },

        // 更新会话列表key
        setUpdateConversationListKey(){
            this.updateConversationListKey += 1;
            console.log('更新会话列表key', this.updateConversationListKey)
        }
    }
});

