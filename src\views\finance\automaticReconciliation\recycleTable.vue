<template>
  <div class="container sell-order-wrap TableXScrollSty">
    <ny-table
      :showColSetting="false"
      noDataType="1"
      cellHeight="ch-96"
      class="nyTableSearchFormFitable"
      :state="state"
      :columns="props.pParams.billType == '1' ? columns : columns2"
      @pageSizeChange="state.pageSizeChangeHandle"
      @pageCurrentChange="state.pageCurrentChangeHandle"
      @selectionChange="state.dataListSelectionChangeHandle"
      @sortableChange="sortableChange"
      :key="tabPageKey"
    >
      <template #header> <el-button type="primary" @click="exportHandle">导出</el-button> </template>
      <!-- 游戏账号 -->
      <template #gameAccount="{ row }">
        <el-text type="primary" text class="pointer" @click="logHandle(row.orderId, 'detail', row)">{{ row.state == "待收购" ? row.campNo || "查看属性详情" : row.gameAccount }}</el-text>
      </template>

      <!-- 状态 -->
      <template #reconciliationStatus="{ row }">
        <el-tag v-if="row.reconciliationStatus == '1'" type="success">对账成功</el-tag>
        <el-tag v-if="row.reconciliationStatus == '2'" type="danger">异常订单</el-tag>
      </template>
      <!-- 实收金额 -->
      <template #receivedAmount="{ row }">
        <el-text type="danger" text v-if="+row.receivedAmount == 0">{{ row.receivedAmount }}</el-text>
        <span v-else>{{ row.receivedAmount }}</span>
      </template>
      <template #operation="{ row }">
        <el-button link type="primary" v-if="row.reconciliationStatus == '2'" @click="handleError(row)">处理</el-button>
        <span v-else>-</span>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort">
            <NyDropdownMenu
              :isBorder="false"
              v-model="SummariesParams"
              :list="[
                { label: '实收金额', value: 1 },
                { label: '应收金额', value: 2 }
              ]"
              labelKey="label"
              valueKey="value"
              isTop
            ></NyDropdownMenu>
          </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
    <!-- 操作日志 -->
    <operation-log ref="operationLogRef"></operation-log>
    <!-- 简易回收的编辑 -->
    <editSimpleOrder ref="editSimpleOrderRef"></editSimpleOrder>
    <!-- 处理异常 -->
    <errorOrder ref="errorOrderRef" :key="errorOrderKey" @refresh="state.getDataList()" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import OperationLog from "@/views/order/acquisition/components/OperationLog.vue";
import editSimpleOrder from "@/views/order/acquisition/components/editSimpleOrder.vue";
import errorOrder from "./errorOrder.vue";

// 收入
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNo",
    label: "订单编号",
    minWidth: 180
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 180
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 180
  },
  {
    prop: "recyclingChannel",
    label: "回收渠道",
    minWidth: 180
  },
  {
    prop: "receivableAmount",
    label: "应收金额(元)",
    minWidth: 180
  },
  {
    prop: "receivedAmount",
    label: "实收金额(元)",
    width: 180
  },
  {
    prop: "recyclingPrice",
    label: "回收价(元)",
    minWidth: 180,
    sortable: "custom"
  },
  {
    prop: "recycler",
    label: "回收人",
    width: 180
  },
  {
    prop: "createDate",
    label: "对账时间",
    width: 190
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    fixed: "right",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 180
  }
]);
// 支出
const columns2 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNo",
    label: "订单编号",
    minWidth: 180
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 180
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 180
  },
  {
    prop: "recyclingChannel",
    label: "回收渠道",
    minWidth: 180
  },
  {
    prop: "payableAmount",
    label: "应付金额(元)",
    minWidth: 180
  },
  {
    prop: "paidAmount",
    label: "实付金额(元)",
    width: 180
  },
  {
    prop: "recyclingPrice",
    label: "回收价(元)",
    minWidth: 180,
    sortable: "custom"
  },
  {
    prop: "recycler",
    label: "回收人",
    width: 180
  },
  {
    prop: "createDate",
    label: "对账时间",
    width: 190
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    fixed: "right",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 180
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/autoreconciliation/page",
  getDataListIsPage: true,
  exportURL: "/automaticReconciliation/autoreconciliation/export",
  dataForm: {
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const props = defineProps({
  pParams: {}
});
const tabPageKey = ref(0);
watch(
  () => props.pParams?.billType || props.pParams?.type,
  () => {
    state.dataForm = Object.assign(state.dataForm, props.pParams);
    tabPageKey.value++;
  },
  {
    immediate: true,
    deep: true
  }
);
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleUpDateData = (dataForm:any) => {
  state.dataForm = Object.assign(state.dataForm, dataForm);
  state.getDataList();
};
// 异常处理
const errorOrderRef = ref();
const errorOrderKey = ref(0);
const handleError = (row: any) => {
  errorOrderKey.value++;
  nextTick(() => {
    errorOrderRef.value.init({ ...row },props.pParams.billType);
  });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/autoreconciliation/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `回收订单`);
  });
};

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();
// 日志
const operationLogRef = ref();
const logHandle = (id: number, type: string, row: any, mark = false) => {
  if (row.orderType == 2 && type == "detail") {
    // 简易流程
    editSimpleOrderHandle({ ...row, isCheck: true });
    return;
  }
  baseService.get("/purchase/page",{ page:1, limit: 10, id: row.orderId }).then(res=>{
    if(res.code == 0){
      operationLogRef.value.init(id, mark, type, res.data.list[0]);
    }
  })
};
// 编辑、查看简易流程信息
const editSimpleOrderRef = ref();
const editSimpleOrderHandle = async (row: any) => {
  await nextTick();
   baseService.get("/purchase/page",{ page:1, limit: 10, id: row.orderId }).then(res=>{
    if(res.code == 0){
      editSimpleOrderRef.value.init(res.data.list[0], gamesList.value);
    }
  })
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item.receivableAmount) if (item.receivableAmount) total = total + (item.receivableAmount || 0);
    });
  } else if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item.receivedAmount) if (item.receivedAmount) total = total + (item.receivedAmount || 0);
    });
  }
  return total.toFixed(2);
};
defineExpose({
  handleUpDateData
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
