<template>
  <div class="TableXScrollSty">
    <el-card :shadow="false"  class="rr-view-ctx-card">
      <ny-flod-tab
        :list="[
          { label: '合作商管理', value: '1' },
          { label: '合作商配置', value: '2' },
          { label: '合作商审核', value: '3' }
        ]"
        v-model="state.tabType"
        value="value"
        label="label"
      ></ny-flod-tab>
      <tenant v-if="state.tabType == '1'"></tenant>
      <tenantRole v-if="state.tabType == '2'"></tenantRole>
      <tenantAudit v-if="state.tabType == '3'"></tenantAudit>
    </el-card>
  </div>
  </template>
  <script lang="ts" setup>
  import { nextTick, reactive, ref, toRefs, watch } from "vue";
  import tenant from "./tenant.vue";
  import tenantRole from "./tenant-role.vue";
  import tenantAudit from "./tenant-audit.vue"; 
  
  
  const state = reactive({
    tabType: "1"
  });
  </script>
  