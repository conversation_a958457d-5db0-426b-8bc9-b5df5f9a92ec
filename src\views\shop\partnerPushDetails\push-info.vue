<template>
  <div style="padding: 12px">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="button-group">
        <div
          class="button-item"
          v-for="(item, index) in stateList"
          :key="index"
          :class="{ active: currentTableIndex == item.value }"
          @click="
            () => {
              currentTableIndex = +item.value;
              getData();
            }
          "
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="setUptable">
      <el-table border ref="tableRef" @select="onSelect" @select-all="onSelectAll" cell-class-name="ch-56" :data="state.dataList" style="width: 100%;" max-height="550">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column property="accountSourceName" label="商品类型" align="center" width="100"/>
        <el-table-column property="code" label="商品编码" align="center" width="100"/>
        <el-table-column show-overflow-tooltip property="shopName" label="商品信息" align="center" width="340">
          <template #default="scope">
              <div class="shoping">
                <el-image style="height: 68px; width: 120px" :src="scope.row.log" :preview-src-list="[scope.row.log]" preview-teleported fit="cover" />
                <div class="info">
                  <div class="title mle" v-html="scope.row.title"></div>
                  <div class="sle" style="width: 185px; text-align: left">
                    {{ `${scope.row.gameName} / ${scope.row.serverName}` }}
                  </div>
                </div>
              </div>
            </template>
        </el-table-column>
        <el-table-column property="gameAccount" label="游戏账号" align="center" width="140"/>
        <el-table-column property="pushStatus" label="推送状态" align="center" width="100">
          <template #default="scope">
            <!-- 状态 -->
            <el-tag :type="scope.row.pushStatus == 2 ? 'danger' : 'success'">{{ scope.row.pushStatusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="status" label="商品状态" align="center" width="140">
          <template #default="{ row }">
            <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
            <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
            <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
            <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
            <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
            <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
            <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
            <el-tag type="warning" v-if="row.status == '8'">重新上架</el-tag>
            <el-tag type="warning" v-if="row.status == '9'">重新待上架</el-tag>
            <el-tag type="warning" v-if="row.status == '10'">出售待审核</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="price" label="零售价(元)" align="center" width="100"/>
        <el-table-column property="channelPrice" label="渠道价(元)" align="center" width="100"/>
        <el-table-column property="pushTime" label="推送时间" align="center" width="160">
          <template #default="{ row }">
            {{ formatTimeStamp(row.pushTime) }}
          </template>
        </el-table-column>
        <el-table-column property="showLog" label="日志" align="center">
          <template #default="{ row }">
            <el-button @click="handleOpenLog(row)" type="primary" link>查看</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <div class="flx-between">
        <div class="pt-15">
          <slot name="footer"></slot>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px;">
            <span style="font-weight: bold; color: #1d2129">零售价</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </div>
        <el-pagination
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100, 500, 1000]"
          :page-size="queryParams.size"
          :total="state.count"
          layout="total, sizes, prev, pager, next, jumper"
          :hide-on-single-page="false"
          @size-change="TableSizeChangeFn"
          @current-change="TableCurrentChangeFn"
        ></el-pagination>
      </div>
    </div>

    <!-- 查看日志 -->
    <el-dialog v-model="dialogForm.visibleLog" :title="dialogForm.titleLog" width="1200" :footer="null" @close="handleCloseLog">
      <ny-table :state="stateLog" :showColSetting="false" :columns="columnsLog" @pageSizeChange="stateLog.pageSizeChangeHandle" @pageCurrentChange="stateLog.pageCurrentChangeHandle" @selectionChange="stateLog.dataListSelectionChangeHandle">
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
          <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
          <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
          <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
        </template>
        <template #pushStatus="{ row }">
          <el-tag v-if="row.pushStatus == '未推送'" type="warning">未推送</el-tag>
          <el-tag v-if="row.pushStatus == '推送成功'" type="primary">推送成功</el-tag>
          <el-tag v-if="row.pushStatus == '推送失败'" type="danger">推送失败</el-tag>
        </template>
        <template #errorMessage="{ row }">
          <div style="display: flex" v-if="row.errorMessage">
            <div style="width: 240px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ row.errorMessage }}</div>
            <el-button @click="handleOpen('推送信息', row.errorMessage)" type="primary" link>查看</el-button>
          </div>
          <span v-else>-</span>
        </template>
        <template #requestParam="{ row }">
          <div style="display: flex" v-if="row.requestParam">
            <div class="sle">{{ row.requestParam }}</div>
            <el-button @click="handleOpen('请求参数', row.requestParam)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #responseBody="{ row }">
          <div style="display: flex" v-if="row.responseBody">
            <div class="sle">{{ row.responseBody }}</div>
            <el-button @click="handleOpen('返回参数', row.responseBody)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #requestUrl="{ row }">
          <div style="display: flex" v-if="row.requestUrl">
            <div class="sle">{{ row.requestUrl }}</div>
            <el-button @click="handleOpen('请求地址', row.requestUrl)" type="primary" link>查看</el-button>
          </div>
        </template>
        <template #pushTime="{ row }">
          {{ formatTimeStamp(row.pushTime) }}
        </template>
      </ny-table>
    </el-dialog>
    <!-- 查看推送详情 -->
    <el-dialog v-model="dialogForm.visible" :title="dialogForm.title" width="800" :footer="null" @close="handleClose">
      <div style="height: 422px; overflow-y: scroll">
        {{ dialogForm.content || "-" }}
      </div>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup>
import { nextTick, onUnmounted, reactive, ref, toRefs, watch, inject } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { TableInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { formatTimeStamp } from "@/utils/method";

const startPushStaskContent = inject("startPushStaskContent");
const { t } = useI18n();
const emit = defineEmits(["refreshDataList","pushChange"]);
const tableRef = ref<TableInstance>();
const replyLoading = ref(false); // 推送
const currentTableIndex = ref(0); //当前类型
const props = defineProps({
  propData: {
    type: Object,
    required: true
  }
});
const state = reactive({
  pushParentRecordId: "",
  selectionList: [],
  dataList: [],
  count: 0,
  pushForm: <any>{}
});
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  size: 10
});
// 状态
const stateList = [
  { label: "全部推送", value: 0 },
  { label: "推送成功", value: 1 },
  { label: "推送失败", value: 2 }
];


// 选择列表项
const getData = () => {
  let pushStatus_ = currentTableIndex.value;
  if (currentTableIndex.value == 0) {
    pushStatus_ = null;
  }
  requestLoading.value = true;
  baseService
    .post("/apiSync/shopSyncParentRecordList", {
      pushParentRecordId: state.pushForm.id,
      partnerId: state.pushForm.partnerId,
      gameId: state.pushForm.gameId,
      pushStatus: pushStatus_,
      ...queryParams
    })
    .then((res: any) => {
      if (res.code == 0) {
        requestLoading.value = false;
        state.dataList = res.data.list || [];
        state.count = res.data.total;
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

const onSelect = (arr: any) => {
  state.selectionList = arr;
};
const onSelectAll = (arr: any) => {
  state.selectionList = arr;
};
// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.size = val;
  queryParams.page = 1;
  getData();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getData();
};
// 表单初始化
const init = (data?: any) => {
  state.pushForm = data;
  getData();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.price) total += item?.price || 0;
  });
  return total.toFixed(2);
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const columnsLog = reactive([
  {
    prop: "errorMessage",
    label: "推送信息",
    minWidth: 200,
    showOverflowTooltip: false
  },
  {
    prop: "requestParam",
    label: "请求参数",
    minWidth: 120
  },
  {
    prop: "responseBody",
    label: "返回参数",
    minWidth: 120
  },
  {
    prop: "requestUrl",
    label: "请求地址",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 100
  },
  {
    prop: "pushTime",
    label: "推送时间",
    minWidth: 140
  }
]);
// NOTE: 弹窗
const dialogForm = reactive({
  visible: false,
  title: "",
  content: "",
  visibleLog: false,
  titleLog: "查看日志",
  visibleError: false,
  titleError: "推送失败记录"
});
const viewLog = reactive({
  createdIsNeed: false,
  getDataListURL: "/apiSync/shopSyncRecordList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    shopId: "",
    recordType: 1
  }
});
const stateLog = reactive({ ...useView(viewLog), ...toRefs(viewLog) });
const handleOpenLog = (content: any, partnerId?: any) => {
  dialogForm.visibleLog = true;
  dialogForm.titleLog = "查看日志-" + content.gameName + "-" + content.code;
  stateLog.dataForm.shopId = content.id;
  stateLog.dataForm.partnerId = partnerId ? partnerId : state.pushForm.partnerId;
  stateLog.dataForm.recordType = state.pushForm.pushType;
  stateLog.getDataList();
};
const handleCloseLog = () => {
  dialogForm.visibleLog = false;
  dialogForm.titleLog = "查看日志";
};
const handleOpen = (title: any, content: any) => {
  dialogForm.visible = true;
  dialogForm.title = title;
  dialogForm.content = content;
};
watch(
  () => props.propData,
  () => {
    init(props.propData);
  },
  { immediate: true, deep: true }
);
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.button-group {
  width: fit-content;
  background: #f2f2f2;
  border-radius: 6px;
  margin-bottom: 12px;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
.setUptable {
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .title {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid #4165d7;
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}

.shoping {
    display: flex;
    align-items: center;
    cursor: pointer;

    .el-image {
      border-radius: 4px;
      margin-right: 8px;
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .title {
        color: var(--el-color-primary);
        white-space: pre-wrap;
        text-align: left;
      }
    }
  }
</style>
<style lang="scss">
.infoDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
