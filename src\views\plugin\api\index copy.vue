<template>
  <div class="container">
    <el-card shadow="never" class="rr-view-ctx-card">
      <div>
        <ny-flod-tab :list="companyTypeList" v-model="state.dataForm.companyType" value="dictValue" label="dictLabel" @change="state.getDataList()"></ny-flod-tab>
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px">
          <el-text class="mx-1" type="danger">提示：如需开通API，请联系恒星技术团队，进行配置；</el-text>
          <div class="flx-justify-end">
            <el-select v-model="state.dataForm.apiType" placeholder="API类型" style="width: 200px; margin-right: 12px" clearable>
              <el-option label="仅接收" value="0" />
              <el-option label="仅推送" value="1" />
              <el-option label="互推" value="2" />
              <el-option label="未对接" value="3" />
            </el-select>
            <el-input v-model="state.dataForm.companyName" placeholder="平台名称" style="width: 200px; margin-right: 12px" clearable></el-input>
            <el-select v-model="state.dataForm.openState" placeholder="请选择是否开通" style="width: 200px; margin-right: 12px" clearable>
              <el-option v-for="(item, index) in openStateList" :label="item.label" :value="item.value" :key="index" />
            </el-select>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
          </div>
        </div>
      </div>
      <div v-loading="state.dataListLoading">
        <ny-no-data type="3" v-if="!state.dataList || !state.dataList.length" />
        <!-- 第三方 -->
        <div class="api_list" v-else>
          <div class="api_item" v-for="(item, index) in state.dataList">
            <div class="api_item_top">
              <span class="api_name">{{ item.companyName }}</span>
              <div class="api_tab">
                <template v-if="item.mainPartnerSetting">
                  <span v-if="item.mainPartnerSetting.apiType == 0">仅接收</span>
                  <span v-else-if="item.mainPartnerSetting.apiType == 1">仅推送</span>
                  <span v-else-if="item.mainPartnerSetting.apiType == 2">互推</span>
                  <span v-else-if="item.mainPartnerSetting.apiType == 3">未对接</span>
                </template>
                <span v-else>未对接</span>
                <span>{{ item.companyType == 1 ? "恒星平台" : "第三方商户平台" }}</span>
              </div>
            </div>
            <div class="api_item_cont">
              <div class="api_item_cont_item" @click.stop="checkboxFn(item)">
                <div class="label">配置倍率</div>
                <div class="value">
                  <span class="value_name">默认</span>
                  <span class="value_icon">查看</span>
                </div>
              </div>
              <div class="api_item_cont_item" v-if="item.mainPartnerSetting && item.mainPartnerSetting.apiType != 0">
                <div class="label">开通时间</div>
                <div class="value" v-if="item.mainPartnerOpening && item.mainPartnerOpening.createDate && (item.openState == 1 || item.openState == 3 || item.openState == 4)">
                  <span class="value_name">
                    {{ item.mainPartnerOpening.createDate ? item.mainPartnerOpening.createDate.split(" ")[0] : "-" }}
                    ~
                    {{ item.mainPartnerOpening.expirationTime ? formatTimeStamp(item.mainPartnerOpening.expirationTime).split(" ")[0] : "-" }}
                  </span>
                </div>
                <el-select v-else-if="(item.openState == 2 || item.openState == 3 || item.openState == 4) && item.trialMonth == 0" v-model="item.mainPartnerOpening.openMonth" placeholder="选择时间" style="flex: 1; margin-left: 20px">
                  <el-option :label="`${it == 12 ? it + '个月(永久)' : it + '个月'}`" :value="it" v-for="(it, ind) in item.expireMonth.split(',')" :key="ind" />
                </el-select>
                <div class="value" v-else-if="item.openState == 5">
                  <span class="value_name">永久</span>
                </div>
                <div class="value" v-else-if="(item.openState == 0 || item.openState == null) && item.mainPartnerSetting.apiType != null">
                  <span class="value_name">试用</span>
                </div>
                <div class="value" v-else-if="item.openState == 2">
                  <span class="value_name">已到期</span>
                </div>
                <div class="value" v-else-if="item.mainPartnerSetting.apiType == null">
                  <span class="value_name">未对接</span>
                </div>
              </div>
              <div class="api_item_cont_item" v-if="item.mainPartnerOpening && item.openState != 5 && item.mainPartnerSetting && item.mainPartnerSetting.apiType != 0 && item.companyType != 1">
                <div class="label">自动续费</div>
                <div class="switch">
                  <div @click.stop="">
                    <el-switch v-model="item.mainPartnerOpening.isContinuity" :disabled="item.mainPartnerSetting.apiType == null" inline-prompt active-text="是" inactive-text="否" @click="automaticReneChange(item)" />
                  </div>
                  <el-tooltip effect="dark" content="自动续费开启后，系统将在服务有效期最后一天，续费1个月" placement="right-start">
                    <el-icon color="#A8ABB2" size="16" style="margin-left: 8px"><WarningFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <div style="flex: 1"></div>
            <div class="api_item_but">
              <!-- <div class="money" v-if="item.openState == 0 || item.openState == null">免费</div> -->
              <template v-if="item.openState != 5 && item.mainPartnerSetting && item.mainPartnerSetting.apiType != 0 && item.companyType != 1">
                <div class="money">{{ item.openState == 1 || item.openState == 2 ? item.renew : item.feeStandards }}<span style="font-size: 12px">星币</span></div>
                <div class="price">
                  {{ `续费价格：${item.renew}星币 ` }}
                  <!-- <span v-if="item.openState == 1 || item.openState == 3">{{ `约${calculatePrice(item)}元/月` }}</span> -->
                </div>
              </template>

              <template v-if="item.mainPartnerSetting && item.mainPartnerSetting.apiType != null">
                <template v-if="item.companyType == 1">
                  <el-button plain style="width: 100%; color: #00c568; border: 1px solid #00c568" v-if="item.openState == 5" :icon="SuccessFilled">已开通</el-button>
                  <el-button type="primary" style="width: 100%" v-else @click.stop="openedSelf(item)">立即开通</el-button>
                </template>
                <!-- 需要收费 0  1 -->
                <template v-else-if="item.apiFree == 1">
                  <el-button type="primary" style="width: 100%" v-if="item.openState == 0 || item.openState == null" @click.stop="openedFree(item)">免费使用</el-button>
                  <el-button plain style="width: 100%; color: #00c568; border: 1px solid #00c568" v-else :icon="SuccessFilled">已开通</el-button>
                </template>
                <template v-else>
                  <el-button type="primary" style="width: 100%" v-if="(item.openState == 3 || item.openState == 4 || item.companyType == 1) && item.trialMonth == 0" @click.stop="opened(item, 1)">立即开通</el-button>
                  <el-button type="primary" style="width: 100%" v-else-if="(item.openState == 0 || item.openState == null) && item.trialMonth > 0" @click.stop="opened(item, 0)">立即试用</el-button>
                  <el-button plain style="width: 100%; color: #00c568; border: 1px solid #00c568" v-else-if="item.openState == 5" :icon="SuccessFilled">已开通</el-button>
                  <el-button type="info" style="width: 100%; background: #e6e8eb; color: #a8abb2; border: 1px solid #e6e8eb" v-else-if="item.mainPartnerSetting.apiType == 0">仅接收</el-button>
                  <el-button plain style="width: 100%; color: var(--el-color-primary); border: 1px solid var(--el-color-primary)" v-else @click.stop="opened(item, 2)" :icon="InfoFilled">立即续费</el-button>
                </template>
              </template>
              <template v-else>
                <el-button type="info" style="width: 100%; background: #e6e8eb; color: #a8abb2; border: 1px solid #e6e8eb">暂未对接</el-button>
              </template>
            </div>
          </div>
        </div>
        <!-- 开通状态 0:未开通 1:已开通 2:已过期 3:试用中 4:试用过期 5:永久” -->
        <!-- 平台直接显示开通  永久开通不扣费 -->
      </div>

      <el-pagination
        :current-page="state.page"
        :page-sizes="[10, 20, 50, 100, 500, 1000]"
        :page-size="state.limit"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        :hide-on-single-page="true"
        @size-change="state.pageSizeChangeHandle"
        @current-change="state.pageCurrentChangeHandle"
      >
      </el-pagination>
    </el-card>

    <!-- 配置 -->
    <api-configured ref="apiConfiguredRef" :key="apiConfiguredKey"></api-configured>

    <!-- 开通 -->
    <api-opened ref="apiOpenedRef" :key="apiOpenedKey" @refresh="state.query()"></api-opened>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, nextTick, computed } from "vue";
import useView from "@/hooks/useView";
import apiConfigured from "./api-configured.vue";
import apiOpened from "./api-opened.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { IObject } from "@/types/interface";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import { SuccessFilled, InfoFilled } from "@element-plus/icons-vue";
import apiIcon from "@/assets/images/api_icon.png";
import baseService from "@/service/baseService";
import { formatTimeStamp } from "@/utils/method";

const openTime = ref(""); // 开通时间

const store = useAppStore();
const companyTypeList = computed(() => {
  return getDictDataList(store.state.dicts, "partner_company_type");
});

const view = reactive({
  getDataListURL: "/partner/partner/page",
  getDataListIsPage: true,
  dataForm: {
    // 平台类型 1：平台商户 2：外部商户
    companyType: "2",
    companyName: "",
    openState: "",
    apiType: null
  }
});

const state = reactive(<IObject>{ ...useView(view), ...toRefs(view) });

// 开通状态 0:未开通 1:已开通 2:已过期 3:试用中 4:试用过期 5:永久
const openStateList = [
  { label: "未开通", value: 0 },
  { label: "已开通", value: 1 },
  { label: "已过期", value: 2 },
  { label: "试用中", value: 3 },
  { label: "试用过期", value: 4 },
  { label: "永久", value: 5 }
];

// 重置
const getResetting = () => {
  state.dataForm.companyName = "";
  state.dataForm.openState = "";
  state.dataForm.apiType = null;
  state.getDataList();
};

// 选中操作
const apiConfiguredRef = ref();
const apiConfiguredKey = ref(0);
const checkboxFn = (item: any) => {
  apiConfiguredKey.value++;
  nextTick(() => {
    apiConfiguredRef.value.init(item);
  });
};
// 免费开通
const openedFree = async (row: any) => {
  try {
    let data = {
      id: row.id
    };
    const res = await baseService.post("/partner/partner/openDockingPort", data);

    if (res.code == 0) {
      ElMessage.success("开通成功");
      state.getDataList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};
// 开通
const openedSelf = async (row: any) => {
  try {
    let data = {
      id: row.id,
      months: 12,
      companyType: 1,
      openType: 1
    };
    const res = await baseService.post("/partner/partner/openDockingPort", data);

    if (res.code == 0) {
      ElMessage.success("开通成功");
      state.getDataList();
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {}
};
const apiOpenedRef = ref();
const apiOpenedKey = ref(0);
const opened = (row: any, type) => {
  // if(row.companyType == 1) {
  //     return confirmOpen(row);
  // }
  console.log(type);

  apiConfiguredKey.value++;
  nextTick(() => {
    apiOpenedRef.value.init(row, type);
  });
};

// 自动续费
const automaticReneChange = (row: any) => {
  if (!row.mainPartnerSetting || row.mainPartnerSetting.apiType == null) return;
  // const params = {
  //   id: row.id,
  //   isContinuity: row.isContinuity
  // };
  baseService
    .get("/partner/partner/auto/" + row.id)
    .then((res) => {
      ElMessage.success("设置成功");
    })
    .catch((err) => {
      row.mainPartnerOpening.isContinuity = false;
    });
};

const confirmOpen = async (data: object) => {
  await useHandleData("/paid/tbpaidfunction/open", data, "是否确认开通", "post");
  state.getDataList();
};

// 计算价格
const calculatePrice = (row: any) => {
  const openTime = row.openTime;
  const expirationTime = row.expirationTime;
  const month = getMonthsBetweenDates(openTime, expirationTime);

  const moeny = (row.feeStandards * 1 + row.renew * (month - 1)) / month;
  console.log("=== 开通了几个月:" + month, "===== 平均金额:" + moeny);
  return moeny.toFixed(2);
};

// 计算两个日期之间的月数
const getMonthsBetweenDates = (data1: string, data2: string) => {
  const date1 = new Date(data1);
  const date2 = new Date(data2);
  var months;
  months = (date2.getFullYear() - date1.getFullYear()) * 12;
  months -= date1.getMonth() + 1;
  months += date2.getMonth() + 1;

  console.log(months);
  return months;
};
</script>

<style lang="less" scoped>
.business_card {
  border-radius: 8px;
  margin-bottom: 40px;
  border: 1px solid var(--el-border-color-light);
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;

  &.active {
    background: #E8F3FF;
  }

  .c {
    display: flex;
    box-sizing: border-box;
    padding: 15px;
    height: calc(100% - 50px);
    position: relative;
    .logo {
      height: 50px;
      width: 50px;
      margin-right: 15px;
    }
    .det {
      display: flex;
      flex-direction: column;
      flex: 1;
      .tag {
        span {
          margin-right: 10px;
        }
        font-size: 16px;
        margin-bottom: 15px;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .value {
        font-size: 14px;
        margin-bottom: 10px;
      }
      .api-content {
        display: flex;
        align-items: center;
        font-size: 14px;

        .api-content-item {
          flex: 1;
        }
      }
    }
    .set {
      position: absolute;
      right: 15px;
      top: 0px;
      font-size: 18px;
      color: var(--el-color-info);
    }
  }
  .f {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
    height: 30px;
    box-sizing: content-box;
  }
}

.card-list {
  margin-left: -24px;
  display: flex;
  flex-wrap: wrap;

  .image-wrap {
    height: 80px;
    background: #fcfbfb;
    border-radius: 8px;
    overflow: hidden;
  }

  .image {
    width: 100%;
    height: 100%;
  }

  :deep(.card-item-content) {
    padding: 0;
  }

  .card-content {
    height: calc(100% - 24px);
    margin-left: 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    border: 1px solid #e4e7ed;
    transition: all 0.3s;
    font-size: 14px;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
    }

    .info {
      padding: 12px;
    }

    .title-wrap {
      .title {
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
      }
    }

    .price-wrap {
      font-size: 14px;

      .symbol {
        font-size: 12px;
        color: #f44a29;
      }

      .price {
        font-size: 24px;
        line-height: 20px;
        font-weight: bold;
        color: #f44a29;
      }

      .after {
        font-size: 12px;
        color: #606266;
        margin-left: 3px;
      }
    }

    .open-btn {
      background: #ffecbb;
      border: 1px solid #ffdd8c;
      border-radius: 4px;
      width: 104px;
      line-height: 32px;
      text-align: center;
      font-size: 16px;
      color: #5f4139;
      font-weight: bold;

      &.trial {
        background: var(--el-color-danger);
        border: 1px solid var(--el-color-danger);
        color: #fff;
      }

      &.continued {
        background: var(--el-color-success);
        border: 1px solid var(--el-color-success);
        color: #fff;
      }
    }
  }
}
.api_list {
  display: flex;
  flex-wrap: wrap;
  gap: 1%;
  .api_item {
    width: 19.2%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
    }
    .api_item_top {
      padding: 12px;
      background: #f1f6ff;
      background-image: url("../../../assets/images/api_bg.png");
      background-repeat: no-repeat;
      background-position: right 0 bottom 0;
      background-size: 145px 78px;
      .api_name {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .api_tab {
        margin-top: 10px;
        span {
          background: #ffffff;
          padding: 4px;
          border-radius: 2px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        span + span {
          margin-left: 8px;
        }
      }
    }
    .api_item_cont {
      background: #f6f9ff;
      padding: 16px;
      .api_item_cont_item {
        display: flex;
        align-items: center;
        cursor: pointer;
        .label {
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .value {
          flex: 1;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          padding: 5px 8px;
          margin-left: 20px;
          display: flex;
          align-items: center;
          .value_name {
            flex: 1;
            font-weight: 400;
            font-size: 14px;
            color: #303133;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .value_icon {
            font-weight: 400;
            font-size: 14px;
            color: #165DFF;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .switch {
          display: flex;
          align-items: center;
          margin-left: 20px;
        }
      }

      .api_item_cont_item + .api_item_cont_item {
        margin-top: 10px;
      }
    }
    .api_item_but {
      background-color: #fff;
      padding: 16px;
      border-radius: 0px 0px 4px 4px;
      .money {
        font-weight: bold;
        font-size: 20px;
        color: #f44a29;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .price {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 10px;
      }
    }
  }
}
.select_time {
  display: flex;
  flex-direction: column;
  span {
    width: 100%;
    padding: 8px 16px;
    cursor: pointer;
  }
}
</style>
