<template>
  <el-dialog v-model="rechargedrawer" width="480" title="充值" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="rechargedrawer = false">
    <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>支付宝账号</div> </template>
        <EMAIL>
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>支付宝账户主体</div> </template>
        枣庄努运企业管理有限公司
      </el-descriptions-item>
    </el-descriptions>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top">
      <el-form-item label="支付宝订单号" prop="orderNum">
        <template #label>
          <span style="margin-right: 10px">支付宝订单号</span>
          <el-text type="primary" @click="dataForm.showImagePreview = true">如何查看订单号？</el-text>
        </template>
        <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="rechargedrawer = false">取消</el-button>
        <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
      </div>
    </template>
    <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="dataForm.showImagePreview = false" />
  </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { formatDate, formatCurrency, Local } from "@/utils/method";
const store = useAppStore();
// 充值
const rechargedrawer = ref(false);
// 余额表单变量
const dataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: dataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};
const init = () => {
  rechargedrawer.value = true;
  dataForm.orderNum = "";
};
defineExpose({ init });
</script>
<style scoped lang='scss'>
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
</style>