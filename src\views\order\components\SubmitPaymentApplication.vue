<template>
  <operation-log ref="operationRef" :show="show" @close="show = false" title="提交打款申请" confirmText="确认提交" type="primary" @confirm="confirm">
    <template #default>
      <div class="text">
        <p>您即将向财务系统提交一笔打款申请。请仔细核对以下信息以确保准确无误：</p>
        <ul>
          <li><b>收款方信息：</b>请确认收款人名称、账号及开户行是否正确无误。</li>
          <li><b>打款金额：</b>请核查打款金额是否与订单相符。</li>
        </ul>
        <p>在您提交申请后，财务系统将对您的申请进行审核。审核通过后，方可进行打款确认操作。</p>
      </div>

      <el-form :model="dataForm" label-position="top" :rules="rules" ref="formRef">
        <template v-if="!hide">
          <el-form-item label="收款方账户类型" prop="accountType">
            <el-radio-group v-model="dataForm.accountType">
              <el-radio label="3">银行卡</el-radio>
              <el-radio label="1">支付宝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="账号名称" prop="accountName">
            <el-input v-model="dataForm.accountName" placeholder="请输入账号名称"></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model="dataForm.account" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="开户行名称" prop="accountBank" v-if="dataForm.accountType === '3'">
            <el-input v-model="dataForm.accountBank" placeholder="请输入开户行名称"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="dataForm.remark" type="textarea" :rows="3" maxlength="255" show-word-limit placeholder="请输入备注"></el-input>
          </el-form-item>
        </template>
      </el-form>
    </template>
    <template #footer>
      <el-button :loading="btnLoading" @click="show = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="confirm">确认提交</el-button>
    </template>
  </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose, reactive, defineEmits } from "vue";
import OperationLog from "./SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emits = defineEmits(["refresh"]);
const show = ref(false);

// afterSales 售后订单
// sell  销售订单
// acquisition  回收订单
const currentType = ref("");
const hide = ref(false);

const init = (id: number, type: string, hideForm = false, tenantCode: any, shopCode: any) => {
  hide.value = hideForm;
  currentType.value = type;
  dataForm.value.orderId = id;
  dataForm.value.shopCode = shopCode;
  show.value = true;
  if (currentType.value == "acquisition" || !tenantCode) return;
  baseService.get("/wallet/tenantaccount/getTenantAccountByDefault", { tenantCode }).then((res) => {
    if (res.code == 0) {
      dataForm.value.accountType = res.data?.type;
      dataForm.value.accountName = res.data?.name;
      dataForm.value.account = res.data?.account;
    }
  });
};

const dataForm = ref(<any>{
  accountType: "3"
});

const rules = reactive({
  accountType: [{ required: true, message: "请选择收款方账户类型", trigger: "blur" }],
  accountName: [{ required: true, message: "请输入账号名称", trigger: "blur" }],
  account: [{ required: true, message: "请输入账号", trigger: "blur" }],
  accountBank: [{ required: true, message: "请输入开户行名称", trigger: "blur" }]
});

// 确认
const formRef = ref();
const btnLoading = ref(false);
const confirm = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      let requestApi = currentType.value == "acquisition" ? "/purchase/applyPay" : currentType.value == "sell" ? "/sale/applyPay" : "/saleAfter/applyPay";
      baseService
        .post(requestApi, dataForm.value)
        .then((res) => {
          if (res.code == 0) {
            ElMessage.success("提交成功");
            show.value = false;
            emits("refresh");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>