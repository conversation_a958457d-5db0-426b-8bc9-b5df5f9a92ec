<template>
    <div class="conversation-list-wrap flx-column">
        <div class="search-wrap flx-center">
            <el-input class="search-input" placeholder="搜索" clearable v-model="keywords" @change="searchChangeHandle()"></el-input>

            <!-- 平台用户才显示创建群组 -->
            <div v-if="appStore.state.isPlatform" class="right-add flx-center" @click="createGroupChatsHandle">
                <el-icon>
                    <Plus />
                </el-icon>
            </div>
        </div>
        <div class="nav-warp flx-center" v-show="imStore.currentConversationType != 'search'">
            <div 
                class="nav-item" 
                v-for="(item, index) in conversationListType" 
                :key="index"
                :class="{ active: item.value == imStore.currentConversationType }"
                @click="conversationTabsChange(item.value)"
            >   
                {{ item.name }}
            </div>
        </div>

        <el-scrollbar class="flx-1 conversation-list">
            <ul v-show="imStore.currentConversationType != 'contact' && imStore.currentConversationType != 'platform' && imStore.currentConversationType != 'search'">
                <li 
                    v-for="item in conversationListComputed" 
                    :key="item.id" 
                    @click="conversationClickHandle(item)"
                    @contextmenu="onContextMenu($event, item)"
                >
                    <conversation-item v-if="!imStore.currentConversationType || imStore.currentConversationType == item.conversationType" :item="item" :showIsTop="true"></conversation-item>
                </li>

                <no-data v-if="!conversationListComputed || !conversationListComputed.length"></no-data>
            </ul>
            

            <!-- 联系人 -->
            <div v-show="imStore.currentConversationType == 'contact'">
                <contact-tree :data="treeData" v-if="appStore.state.isPlatform"></contact-tree>
            </div>

            <!-- 平台客服 -->
            <div v-if="imStore.currentConversationType == 'platform'">
                <platform-customer-service :data="treeData" v-if="!appStore.state.isPlatform"></platform-customer-service>
            </div>


            <!-- 搜索结果 -->
            <div class="search-result" v-if="imStore.currentConversationType == 'search'">
               
                <div class="search-result-list">
                    <div class="list-type">单聊</div>
                    <template v-if="searchConversationList && searchConversationList.length">
                        <ul :class="{ active: singleShowAll }">
                            <li v-for="item in searchConversationList" :key="item.id" @click="searchConversationClickHandle(item, 'singleChatSearch')">
                                <conversation-search-item :item="item" conversationType="1"></conversation-search-item>
                            </li>
                        </ul>
                        <div class="show-all-text" v-if="searchConversationList.length>4" @click="singleShowAll = !singleShowAll">
                            {{ singleShowAll ? '收起' : `显示全部（${searchConversationList.length}）` }}
                        </div>
                    </template>
                    <no-data v-else></no-data>
                </div>
                <div class="line"></div>
                <div class="search-result-list">
                    <div class="list-type">交易群</div>
                    <template v-if="searchGroupConversationList && searchGroupConversationList.length">
                        <ul :class="{ active: groupShowAll }">
                            <li v-for="item in searchGroupConversationList" :key="item.id" @click="searchConversationClickHandle(item)">
                                <conversation-item :item="item" :showIsTop="false"></conversation-item>
                            </li>
                        </ul>
                        <div class="show-all-text" v-if="searchGroupConversationList.length > 4" @click="groupShowAll = !groupShowAll">
                            {{ groupShowAll ? '收起' : `显示全部（${searchGroupConversationList.length}）` }}
                        </div>
                    </template>
                    <no-data v-else></no-data>
                </div>

            </div>
            
            
        </el-scrollbar>

        
        <!-- 创建群聊 -->
        <select-member title="创建群聊" ref="createGroupChatsRef" :key="createGroupChatsKey"></select-member>
        

    </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, watch, defineExpose, computed } from 'vue'
import { getConversationList, setConversationTop,setConversationDisturb, clearUnreadCount, sendReadReceiptMessage, getGroupDetail, getGroupMemberList, searchConversation, getConversation, getUserInfo } from '@/utils/imTool';
import { useAppStore } from '@/store/index';
import { useImStore } from '@/store/im';
import { useRoute, useRouter } from 'vue-router';
import ConversationItem from './ConversationItem.vue';
import ConversationSearchItem from './ConversationSearchItem.vue';
import ContactTree from './ContactTree.vue';
import PlatformCustomerService from './PlatformCustomerService.vue';
import SelectMember from './SelectMember.vue';
import NoData from './NoData.vue';
import ContextMenu from '@imengyu/vue3-context-menu'
import baseService from '@/service/baseService';

const imStore = useImStore();
const appStore = useAppStore();
const route = useRoute();
const router = useRouter();


// 会话类型
const conversationListType = computed(() => {
    let list = [
        { name: '全部咨询', value: '' },
        { name: '单聊', value: '1' },
        { name: '交易群', value: '3' },
    ]

    // 平台用户才显示联系人列表
    if(appStore.state.isPlatform){
        list.push({ name: '联系人', value: 'contact' })
    }else{
        // 租户显示平台客服列表
        list.push({ name: '平台客服', value: 'platform' })
    }

    return list;
})


// 会话列表
const conversationListComputed = computed(() => {
    return conversationList.value.filter((ele: any) => ele.targetId != imStore.systemMessageTargetId);
})


const conversationTabsChange = (val: string) => {
    imStore.currentConversationType = val;
    if(val == ''){
        conversationList.value = conversationListCopy;
    }
    if(val == 'single'){
        conversationList.value = conversationListCopy.filter((item: any) => !item.type);
    }
    if(val == 'group'){
        conversationList.value = conversationListCopy.filter((item: any) => item.type == 'group');
    }
}


// 搜索
const keywords = ref('');
// 单聊显示全部
const singleShowAll = ref(false);
// 交易群显示全部
const groupShowAll = ref(false);
// 单聊筛选结果
const searchConversationList = ref([]);
// 群聊筛选结果
const searchGroupConversationList = ref([]);
const searchChangeHandle = () => {
    singleShowAll.value = false;
    groupShowAll.value = false;
    searchGroupConversationList.value = [];
    searchConversationList.value = [];
    if(keywords.value){
        imStore.currentConversationType = 'search';

        conversationListCopy.map((item: any) => {
            if(item.sessionData && item.sessionData.groupName && item.sessionData.groupName.indexOf(keywords.value) > -1){
                item.conversationType == '1' ? '' : searchGroupConversationList.value.push(item);
            }
        })

        searchConversation(imStore.imUid, keywords.value).then((res: any) => {
            searchConversationList.value = res.imUsers;
            // searchGroupConversationList.value = res.imGroups;
        })

    }else{
        imStore.currentConversationType = '';
    }
}

// 会话列表
const conversationList = ref(<any>[]);

let  conversationListCopy: any = [];

// 会话列表 loading
const listLoading = ref(false);


const getList = async () => {
    if(listLoading.value) return;
    listLoading.value = true;
    let list: any = await getConversationList();
    listLoading.value = false;
    conversationList.value = list;
    conversationListCopy = JSON.parse(JSON.stringify(list));
    imStore.imConversationList = list;
    let imDoNotDisturbList = list.filter((item: any) => item.notificationLevel == 5);
    imStore.imDoNotDisturbList = imDoNotDisturbList.map(item => item.targetId);

    
    // 根据路由会话id 跳转对应的会话
    if(route.query.targetId){
        imStore.showContactInfo = false;
        imStore.currentConversationType = '';
        
        let findCoversation = conversationList.value.find((item: any) => item.targetId == route.query.targetId);
        conversationList.value.map((item: any) => {
            if(item.targetId == route.query.targetId){
                // imStore.currentConversation = item;
                conversationClickHandle(item);
            }
        })

        // 过滤系统通知消息
        if(route.query.targetId == imStore.systemMessageTargetId) return;


        // 会话列表有该会话直接打开
        if(findCoversation){
            conversationClickHandle(findCoversation);
        }else{
            // 没有就  创建一条新会话
            getUserInfo(route.query.targetId).then((res: any) => {
                searchConversationClickHandle(res, 'singleChatSearch');
            })
        }
        router.push('/im')
    }
}


// 联系人列表
const treeData = ref([]);

const getTreeData = () => {
    baseService.get('/sys/dept/all/list').then((res: any) => {
        treeData.value = res.data;
    })
}
getTreeData();


// 显示创建群聊
const createGroupChatsKey = ref(0);
const createGroupChatsRef = ref();  
const createGroupChatsHandle = async () => {
    createGroupChatsKey.value++;
    await nextTick();
    createGroupChatsRef.value.init('create');
}

// 点击会话列表
const conversationClickHandle = (data: any) => {
    imStore.showContactInfo = false;
    imStore.currentConversation = data;
    // startPrivateConversation(imStore.imUid, data.targetId);

    getGroupMemberList(data.targetId)

    clearUnreadCount(data.targetId, data.conversationType).then(res => {
        conversationList.value.map((item: any) => {
            if(item.targetId == data.targetId){
                item.unreadMessageCount = 0;
            }
        })
        nextTick(() => {
            conversationListCopy = JSON.parse(JSON.stringify(conversationList.value));
        })
    })
}

// 点击筛选会话列表
const searchConversationClickHandle = async (data: any, type?: string) => {
    if(type == 'singleChatSearch'){
        let res:any = await getConversation(data.imUid);
        data = {
            sessionData: data,
            ...res
        }
    }

    imStore.showContactInfo = false;
    imStore.currentConversation = data;
    imStore.currentConversationType = '';
    keywords.value = '';
}


// 收到新消息
watch(() => imStore.receivedNewsMessge, (newVal, oldVal) => {
    if(newVal.sentTime == oldVal.sentTime) return;
   
    // 是否新会话
    let newSession = true;
    
    conversationList.value.map((item: any) => {
        if(item.targetId == newVal.targetId){
            newSession = false;
            item.latestMessage = newVal;
            item.sentTime = newVal.sentTime;
            if(newVal.targetId != imStore.currentConversation.targetId && newVal.senderUserId != imStore.imUid) item.unreadMessageCount = item.unreadMessageCount ? item.unreadMessageCount + 1 : 1;
            if(newVal.content?.msgType == 'CU:flow') imStore.currentConversation.sentTime = newVal.sentTime;
            // console.log('是否新会话', newVal)
            updateSessionOrder();
        }
    })

    // 有新会话  刷新会话列表
    if(newSession){
        setTimeout(() => {
            getList();
        }, 600);
    }else if(newVal.content?.msgType == 'CU:flow' && newVal.targetId != imStore.currentConversation.targetId){
        try {
            if(newVal.conversationType != 3 || !newVal.targetId) return;
            getGroupDetail(newVal.targetId).then((res: any) => {
                let index = conversationList.value.findIndex((item: any) => item.targetId == newVal.targetId);
                conversationList.value[index].sessionData = res;
            })
        } catch (error) {
            
        }
    }

}, { deep: true })


// 订单进度更新
watch(() => imStore.currentConversation?.sessionData?.orderFlow, (newVal, oldVal) => {
    if(newVal != oldVal){
        let index = conversationList.value.findIndex((item: any) => item.targetId == imStore.currentConversation.targetId);
        conversationList.value[index].sessionData.orderFlow = newVal;
    }
}, { deep: true })


// 更新会话列表
watch(() => imStore.updateConversationListKey, (newVal) => {
    if(newVal) getList();
}, {  deep: true, immediate: true })


// 当前会话是否是新会话
watch(() => imStore.currentConversation, (newVal, oldVal) => {
    if(newVal.targetId == oldVal.targetId) return;
    let index = conversationList.value.findIndex((item: any) => item.targetId == newVal.targetId)
    if(index == -1){
        if(!newVal.latestMessage || !newVal.latestMessage.sentTime) {
            newVal.latestMessage = {
                sentTime: new Date().getTime()
            }
        }
        updateSessionOrder([newVal]);
    }
})

// 从通知消息跳转过来的
watch(() => route.query.targetId, (newVal) => {
    if(newVal && conversationList && conversationList.value.length){
        getList();
    }
})


// 收到多端同步已读消息
watch(() => imStore.readSysMessage, (newVal) => {
    conversationClearUnreadCount(newVal.targetId);
})



// 收到消息更新会话顺序
const updateSessionOrder = (conversation:any = []) => {
    // 置顶会话列表
    const topSessionList = conversationList.value.filter((item: any) => item.isTop);
    // 其他
    const otherSessionList = conversationList.value.filter((item: any) => !item.isTop);

    otherSessionList.sort((a: any, b: any) => {
        return (b.latestMessage ? b.latestMessage.sentTime : b.sentTime) - (a.latestMessage ? a.latestMessage.sentTime : a.sentTime);
    })

    try {
        conversationList.value = [...topSessionList, ...conversation, ...otherSessionList];
        imStore.imConversationList = conversationList.value;
        conversationListCopy = JSON.parse(JSON.stringify(conversationList.value));
    } catch (error) {
        
    }
}


// 发送消息 已读等 更新会话
const updateConversation = (data: any) => {
    if(data.type == 'conversationClearUnreadCount'){
        conversationClearUnreadCount();
    }

    // 我发出的消息
    if(data.type == 'sendMessage'){
        let index = conversationList.value.findIndex((item: any) => item.targetId == data.targetId);
        conversationList.value[index] = {
            ...conversationList.value[index],
            latestMessage: data.content,
            sentTime: data.content.sentTime
        }
        nextTick(() => {
            updateSessionOrder();
        })
    }
}

// 清除当前会话的未读消息
const conversationClearUnreadCount = (id?: string) => {
    let targetId = id ? id : imStore.currentConversation.targetId;
    conversationList.value.map((item: any) => {
        if(item.targetId == targetId){
            item.unreadMessageCount = 0;
        }
    })
}


// 右键菜单
const onContextMenu = (e : MouseEvent, data: any) => {
  //prevent the browser's default menu
  e.preventDefault();
  //show your menu
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    minWidth: 58,
    items: [
      { 
        label: "置顶", 
        hidden: data.isTop,
        onClick: () => {
            setConversationTop(data, true).then(() => {
                getList()
            })
        }
      },
      { 
        label: "取消置顶", 
        hidden: !data.isTop,
        onClick: () => {
            setConversationTop(data, false).then(() => {
                getList()
            })
        }
      },

      { 
        label: "开启免打扰", 
        hidden: data.notificationLevel == 5 || data.conversationType == 1,
        onClick: () => {
            setConversationDisturb(data.targetId, 5).then(() => {
                getList()
            })
        },
      },
      { 
        label: "取消免打扰", 
        hidden: data.notificationLevel == 0 || data.conversationType == 1,
        onClick: () => {
            setConversationDisturb(data.targetId, 0).then(() => {
                getList()
            })
        },
      },
      { 
        label: "标记未读", 
        hidden: !data.unreadMessageCount,
        onClick: () => {
            if(data.conversationType == 1){
                sendReadReceiptMessage(data.targetId, data.latestMessage.messageUId, data.latestMessage.sentTime);
            }
            clearUnreadCount(data.targetId, data.conversationType).then(res => {
                conversationList.value.map((item: any) => {
                    if(item.targetId == data.targetId){
                        item.unreadMessageCount = 0;
                    }
                })
            })
        },
      },
    ]
  }); 
}

defineExpose({
    updateConversation
})


</script>

<style lang="scss" scoped>
.conversation-list-wrap {
    height: 100%;
    width: 320px;
    background: #F2F4F7;

    .search-wrap {
        padding: 16px 16px 12px 16px;
        background: #F2F4F7;

        .search-input {
            flex: 1;
            background: #fff;
            height: 32px;
            border-radius: 40px;

            :deep(.el-input__wrapper) {
                border: 0;
                box-shadow: none;
                border-radius: 40px;
            }
        }

        .right-add {
            margin-left: 8px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border-radius: 40px;
            background: #fff;
            cursor: pointer;

            .el-icon {
                color: #909399;
            }
        }
    }

    .nav-warp {
        padding: 0 16px 16px 16px;

        .nav-item {
            flex: 1;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 40px;
            background: #fff;
            margin-right: 8px;
            color: #606266;
            cursor: pointer;

            &:last-child {
                margin-right: 0;
            }

            &.active,
            &:hover {
                background: var(--el-color-primary);
                color: #fff;
            }
        }
    }

    .conversation-list {
        overflow-y: auto;

        :deep(.el-scrollbar__bar){
            display: none;
        }

        ul{
            padding: 0;
        }
        li{
            list-style: none;
        }

        .search-result{
            width: 100%;
            height: 100%;

            .list-type{
                font-size: 13px;
                line-height: 22px;
                padding: 16px 16px 0 16px;
            }

            .show-all-text{
                padding: 0 16px 16px 16px;
                cursor: pointer;
            }
            
            .line{
                height: 8px;
                background: #fff;
            }
            
            ul{
                max-height: 410px;
                overflow: hidden;

                &.active{
                    max-height: inherit;
                    height: auto;
                }
            }
        }
    }
}
</style>

<style lang="scss">
    .mx-context-menu{
        width: 98px;
        border-radius: 4px;
        padding: 4px;

        .mx-context-menu-item{
            height: 30px;
            padding: 0 8px;
            border-radius: 4px;
            color: #171A1D;
            font-size: 14px;
            cursor: pointer;

            &:hover{
                background: #F2F3F5;
            }
        }

        .mx-context-menu-item .mx-icon-placeholder{
            display: none;
        }
        .mx-context-menu-item .label{
            padding-inline-end: 0;
        }
    }
</style>