<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-flod-tab
        :list="[
          { label: '文章管理', value: '0' },
          { label: '轮播图管理', value: '1' }
        ]"
        v-model="currentTypeIndex"
        value="value"
        label="label"
        @change="tabsTypeChange"
      >
      </ny-flod-tab>

      <!-- 文章管理 -->
      <article-list v-if="currentTypeIndex == 0"></article-list>

      <!-- 轮播图管理 -->
      <banner-list v-if="currentTypeIndex == 1"></banner-list>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import BannerList from "./bannerList.vue";
import ArticleList from "./articleList.vue";

const currentTypeIndex = ref(0);

const tabsTypeChange = async () => {};
</script>
