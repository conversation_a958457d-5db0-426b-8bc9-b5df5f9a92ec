<template>
  <div style="width: 100%">
    <div class="title">短信接收记录</div>
    <div class="TableXScrollSty">
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <div style="display: flex; gap: 8px">
            <!-- <el-button type="primary" @click="handleAutoRefresh">开启自动刷新</el-button> -->
            <el-button color="#409EFF" style="color: #fff" @click="deviceDetailHandle(1)">发送短信</el-button>
            <el-button type="warning" @click="deviceDetailHandle(2)">问题反馈</el-button>
            <el-button type="success" @click="numberIssueHandle">问题记录</el-button>
          </div>
        </template>
        <template #header-right>
          <div style="display: flex; gap: 8px">
            <el-input :prefix-icon="Search" style="width: 240px" v-model="state.dataForm.info" placeholder="请输入手机号/内容" clearable></el-input>
            <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            <el-button @click="filterHandle">高级筛选</el-button>
          </div>
        </template>
        <template #createDate="{ row }">
          <span>{{ row.createDate ? formatTimeStamp(row.createDate) : "-" }}</span>
        </template>
      </ny-table>
    </div>

    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
    <!-- 问题列表 -->
    <numberIssue ref="numberIssueRef" />
    <!-- 操作 -->
    <smsoperate ref="deviceDetailRef" @refreshDataList="state.getDataList" />
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import filterCom from "./filter.vue";
import smsoperate from "./sms-operate.vue";
import numberIssue from "./number-issue.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatTimeStamp } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileReceiveMessage/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  // {
  //   prop: "id",
  //   label: "ID",
  //   minWidth: 80
  // },
  {
    prop: "sendPhone",
    label: "发送手机号",
    minWidth: 136
  },
  {
    prop: "receivePhone",
    label: "接收手机号",
    minWidth: 136
  },
  {
    prop: "type",
    label: "类型",
    minWidth: 136
  },
  {
    prop: "verificationCode",
    label: "验证码",
    minWidth: 80
  },
  {
    prop: "content",
    label: "内容",
    minWidth: 470
  },
  {
    prop: "deptName",
    label: "部门",
    minWidth: 80
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 180
  }
]);
// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm = { info: "" };
  createDate.value = [];
  filterRef.value.reset();
  state.page = 1;
  state.getDataList();
};
const getParamsInfo = (params: any) => {
  delete params.createDate;
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

// 自动刷新
const handleAutoRefresh = () => {};

const deviceDetailRef = ref();
const deviceDetailHandle = (type?: any) => {
  nextTick(() => {
    deviceDetailRef.value.init(type);
  });
};
const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(3);
  });
};
const numberIssueRef = ref();
const numberIssueHandle = () => {
  nextTick(() => {
    numberIssueRef.value.init(3);
  });
};
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 503px;
}
</style>
