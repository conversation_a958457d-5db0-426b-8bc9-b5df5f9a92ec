<template>
    <div class="contact-tree-wrap">
        <div class="contact-tree-group" v-for="(item, index) in data" :key="index">
            <div class="group-title" @click="item.fold = !item.fold">
                <el-icon class="arrow" size="16" :class="{ fold: item.fold }"><CaretRight /></el-icon>
                <div class="text-primary icon">
                    <svg width="20" height="18" fill="currentColor" viewBox="0 0 20 18" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.4142 2H19C19.5523 2 20 2.44772 20 3V17C20 17.5523 19.5523 18 19 18H1C0.44772 18 0 17.5523 0 17V1C0 0.44772 0.44772 0 1 0H8.4142L10.4142 2Z"/>
                    </svg>
                </div>

                <div class="ml-10 title">{{ item.name }}</div>
            </div>
            <div class="member-list" :class="{ fold: item.fold }">
                <div v-if="item.children && item.children.length">
                    <contact-tree :data="item.children" :layer="layer + 1"></contact-tree>
                </div>
            
                <div 
                    class="member-item flx-justify-between" 
                    :class="{ active: currentActive == user.id, hide: user.id == appStore.state.user.id }"
                    v-for="(user, uIndex) in item.users"
                    :key="uIndex"
                    @click="memberItemHandle(user, item.name)"
                >
                    <div class="member flx">
                        <el-image :src="user.headUrl || sttingInfo.info.backendLogo" class="avatar"></el-image>
                        <div class="name">{{ user.nickname || user.realName }}</div>
                    </div>
                    <div class="message" @click="initiatedCOnversationHandle(user)">
                        <el-icon size="16"><ChatDotRound /></el-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';
import { CaretRight, ChatDotRound } from '@element-plus/icons-vue';
import { useImStore } from '@/store/im';
import { useAppStore } from '@/store';
import { useSettingStore } from '@/store/setting';
import { getConversation, startPrivateConversation } from '@/utils/imTool';

const imStore = useImStore();
const appStore = useAppStore();
const sttingInfo = useSettingStore();

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    layer: {
        type: Number,
        default: 1
    }
})
    
const paddingL = ref(props.layer * 16 + 'px');

const memberPaddingL = ref(props.layer * 16 + 36 + 'px');

// 当前已选定的联系人
const currentActive = ref("");

// 点击联系人查看详情
const memberItemHandle = (user: any, deptName: string) => {
    currentActive.value = user.id;
    imStore.showContactInfo = true;
    user.deptName = deptName;
    imStore.currentContactInfo = user;
}

// 发起会话
const initiatedCOnversationHandle = (user: any) => {
    if(!user.imUid) return;
    getConversation(user.imUid).then((res: any) => {
        res.sessionData = user;
        imStore.currentConversation = res;
        imStore.showContactInfo = false;
        imStore.currentConversationType = '';

        
        // startPrivateConversation(imStore.imUid, data.imUid);
    })
}


</script>

<style lang="scss" scoped> 
    .contact-tree-wrap{
        .group{
            &-title{
                display: flex;
                align-items: center;
                height: 44px;
                line-height: 44px;
                cursor: pointer;
                padding-left: v-bind(paddingL);

                .icon{
                    line-height: 1;
                }

                .arrow{
                    margin-right: 12px;
                    margin-left: 10px;
                    transition: all .3s;

                    &.fold{
                        transform: rotate(90deg);
                    }
                }
            }
        }
        
        .member-list{
            height: 0;
            opacity: 0;
            transition: all .3s;
            overflow: hidden;

            &.fold{
                height: auto;
                opacity: 1;
            }
            
            .member-item{
                display: flex;
                align-items: center;
                height: 44px;
                line-height: 44px;
                cursor: pointer;
                padding-left: v-bind(memberPaddingL);
                padding-right: 10px;

                &.hide{
                    display: none;
                }
                
                &:hover{
                    background: #E6E6E6;
                    color: var(--el-color-primary);

                    .message{
                        display: inline-block;
                    }
                }

                &.active{
                    background: var(--el-color-primary);
                    color: #fff;
                    .message{
                        display: inline-block;
                    }
                }

                .member{
                    height: 44px;
                    align-items: center;
                }

                .avatar{
                    width: 24px;
                    height: 24px;
                    border-radius: 4px;
                    background: #fff;
                }

                .name{
                    margin-left: 10px;
                }

                .message{
                    display: none;
                }
            }
        }
    }
</style>