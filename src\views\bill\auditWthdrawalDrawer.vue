<template>
  <el-drawer class="infoAuditDrawer" v-model="visible" size="944" :footer="null">
    <template #header>
      <div class="drawer_title">提现详情</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="basicInfoSty">
        <div class="titleSty">提现信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>申请人</div>
            </template>
            {{ dataForm.applicant }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>提现金额(元)</div>
            </template>
            {{ dataForm.amount }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>账户名称</div> </template>
            {{ dataForm.recipient }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>收款账户</div> </template>
            {{ dataForm.accountType == 1 ? "支付宝" : "银行卡" }} - {{ dataForm.account }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>申请时间</div> </template>
            {{ formatTimeStamp(dataForm.createDate) }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>状态</div> </template>
            <el-tag type="primary" v-if="dataForm.state == 0">待审核</el-tag>
            <el-tag v-else-if="dataForm.state == 2" style="margin-right: 8px" type="danger">已拒绝</el-tag>
            <el-tag type="success" v-else-if="dataForm.state == 1">审核通过</el-tag>
            <el-tag type="success" v-else-if="dataForm.state == 3">已付款</el-tag>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label-class-name="title">
            <template #label> <div>备注</div> </template>
            {{ dataForm.remark }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label-class-name="title" v-if="dataForm.payUrl">
            <template #label> <div>支付凭证</div> </template>
            <el-image v-if="dataForm.payUrl" style="width: 100px" :src="dataForm.payUrl" :preview-src-list="[dataForm.payUrl]" :preview-teleported="true" fit="cover"></el-image>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty">
        <div class="titleSty">审核流程</div>
        <el-steps direction="vertical" :active="dataForm.state == 0 ? 0 : 1">
          <el-step title="提现申请">
            <template #description>
              <div class="cardInfo">
                <div>
                  申请人：<span style="color: var(--el-color-primary)">{{ dataForm.applicant }}</span>
                </div>
                <div>
                  申请时间：
                  <span>{{ formatTimeStamp(dataForm.createDate) }}</span>
                </div>
              </div>
            </template>
          </el-step>
          <el-step title="财务审核">
            <template #description>
              <div class="cardInfo">
                <div>
                  审批人：<span style="color: var(--el-color-primary)">{{ dataForm.reviewer || "-" }}</span>
                </div>
                <div>
                  处理时间：
                  <span>{{ formatTimeStamp(dataForm.approvalTime) || "-" }}</span>
                </div>
                <template v-if="+dataForm.state > 0">
                  <div>
                    审批结果：
                    <span :style="{ color: +dataForm.state != 2 ? '#67C23A' : '#F56C6C' }">{{ dataForm.state != 2 ? "审核通过" : "审核拒绝" }}</span>
                  </div>
                </template>
                <template v-if="+dataForm.state == 2">
                  <div class="mt-8">
                    拒绝原因：
                    <span>{{ dataForm.approvalOpinion }}</span>
                  </div>
                </template>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </el-scrollbar>
  </el-drawer>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { formatTimeStamp } from "@/utils/method";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const dataFormRef = ref(); // 表单ref
const dataForm = ref(<any>{});

// 表单初始化
const init = (row: any) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  Object.assign(dataForm.value, row);
};
// 获取表单详情信息
const requestLoading = ref(false); // 详情加载

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}
.shop_page {
  padding: 12px;
}
.games {
  padding: 20px 6px 10px 6px;
  .gamesList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    .gamesItem {
      min-width: 120px;
      height: 28px;
      font-size: 12px;
      line-height: 28px;
      background-color: #fff;
      border: 1px solid var(--el-color-primary);
      border-radius: 4px;
      text-align: center;
      margin: 0px 10px 10px 10px;
      cursor: pointer;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .active {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }
  }
  .icons {
    padding: 6px 10px 10px 10px;
    .el-icon {
      font-size: 16px;
    }
  }
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
}
.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}
.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;
  .flod_tab_cont {
    flex: 1;
    overflow: hidden;
    :deep(.el-radio) {
      margin-right: 20px;
    }
    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}
.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}
.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  .shop_info_left {
    display: flex;
    align-items: center;
    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="less">
.infoAuditDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-textarea__inner {
    min-height: 80px !important;
  }

  .el-step__head {
    &.is-process,
    &.is-finish {
      .el-step__icon {
        background: var(--el-color-primary);
        color: #fff;
        border-color: #fff;
        .el-step__icon-inner {
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 14px;
        }
      }
    }
    .el-step__icon {
      background: #f0f2f5;
      color: #909399;
      border-color: #fff;
      .el-step__icon-inner {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 14px;
      }
    }

    .el-step__line {
      width: 1px;
      .el-step__line-inner {
        border: 1px solid #c0c4cc;
        border-width: 0px !important;
      }
    }
  }
  .el-step__main .el-step__title {
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .cardInfo {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #ebeef5;
    padding: 16px 12px;
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 16px;
    margin-bottom: 10px;
    > div {
      margin-bottom: 2px;
    }
  }
}
</style>
