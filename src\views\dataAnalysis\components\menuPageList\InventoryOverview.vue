<template>
    <div class="analysis_page">
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 20px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">库存周转</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="analysis_type">
                        <div class="analysis_type_item" :class="{ active: dataForm.type == item.value }"
                            v-for="(item, index) in dateTypeList" :key="index" @click="dateTypeChange(item)">{{
                                item.label }}</div>
                    </div>
                    <!-- <department v-model="dataForm.deptId" v-model:deptName="dataForm.deptName"></department> -->
                </div>
            </div>
            <div style="padding: 0px 20px 20px 20px;">
                <div class="list">
                    <div class="litItem">
                        <div class="header">时间段</div>
                        <div class="line">回收数据
                            <el-tooltip effect="dark" :content="valueOrTip('回收数据')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                        <div class="line">销售数据
                            <el-tooltip effect="dark" :content="valueOrTip('销售数据')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                        <div class="line">存量增幅
                            <el-tooltip effect="dark" :content="valueOrTip('存量增幅')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                        <div class="line">库存翻新率
                            <el-tooltip effect="dark" :content="valueOrTip('库存翻新率')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="litItem" v-for="(item, index) in tableData" :key="index">
                        <div class="header" style="font-weight: bold;color: #4165D7;background: #e5e9f7">{{ item.timeSlot }}</div>
                        <div class="line" style="background: #eef1f8;">￥{{ item.recoveryAmount }}/{{ item.recyclingSingleNumber }}单</div>
                        <div class="line" style="background: #eef1f8;">￥{{ item.salesAmount }}/{{ item.salesOrderNumber }}单</div>
                        <div class="line" style="background: #eef1f8;">{{ item.quantityOfStockIncrease }}({{ item.proportionOfStockGrowthRate || '-' }}%)</div>
                        <div class="line" style="background: #eef1f8;">{{ item.inventoryRefurbishmentTimes }}</div>
                    </div>
                </div>

            </div>
        </div>

        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 20px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">库存滞销</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <!-- <department v-model="dataForm.deptId" v-model:deptName="dataForm.deptName"></department> -->
                </div>
            </div>
            <div style="padding: 0px 20px 20px 20px;">
                <div class="list">
                    <div class="litItem">
                        <div class="header">时间段</div>
                        <div class="line">待售库存
                            <el-tooltip effect="dark" :content="valueOrTip('待售库存')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                        <div class="line">数量占比
                            <el-tooltip effect="dark" :content="valueOrTip('数量占比')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                        <div class="line">资金占比
                            <el-tooltip effect="dark" :content="valueOrTip('资金占比')" placement="top-end">
                                <img src="/src/assets/icons/svg/question-line.svg"
                                    style="width: 14px;height: 14px;margin-left: 4px;">
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="litItem" v-for="(item, index) in tableData2" :key="index">
                        <div class="header" style="font-weight: bold;color: #F77234;background: #f7ebe6;">{{ item.timeSlot }}</div>
                        <div class="line" style="background: #f7f1f0;">{{ item.inventory }}个</div>
                        <div class="line" style="background: #f7f1f0;">{{ item.proportion }}%</div>
                        <div class="line" style="background: #f7f1f0;">{{ item.capitalProportion }}%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue';
import department from './department.vue';
import baseService from '@/service/baseService';
const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    purchaseEmployeeId: "",
    type: "1"
});

const dateTypeList = [
    { label: "日", value: "1" },
    { label: "月", value: "2" }
];

const dateTypeChange = (e: any) => {
    dataForm.value.type = e.value;
    getInventoryTurnover();
};
const tableData = ref(<any>[])
const tableData2 = ref(<any>[])

const valueOrTip = (e: any) => {
    if (e == "回收数据") {
        return "已提交的回收数据（不包含已取消的回收订单）"
    }
    if (e == "销售数据") {
        return "已提交的销售数据（不包含已取消的回收订单）"
    }
    if (e == "存量增幅") {
        return "待售库存涨幅数量与百分比"
    }
    if (e == "库存翻新率") {
        return "推算出这段时间内，商品平均翻新次数"
    }
    if (e == "资金周转率") {
        return "推算出这段时间内，库存占用的资金周转次数"
    }
    if (e == "待售库存") {
        return "取指定时间段末尾时间点，待售库存根据不同的入库时间段，计算对应的库存数量"
    }
    if (e == "数量占比") {
        return "不同滞销时长的待售库存数量，去除以【待售库存总数】"
    }
    if (e == "资金占比") {
        return "不同滞销时长的待售库存回收价总金额，去除以【待售库存总回收价】"
    }
}

onMounted(() => {
    getInventoryTurnover();
    getInventoryStagnation();
})

// 库存周转
const getInventoryTurnover = () => {
    baseService.post("/dataAnalysis/inventoryTurnover", dataForm.value).then(res => {
        tableData.value = res.data;
    })
}

// 库存滞销
const getInventoryStagnation = () => {
    
    baseService.post("/dataAnalysis/inventoryStagnation", dataForm.value).then(res => {
        tableData2.value = res.data;
    })
}


const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getInventoryTurnover();
    getInventoryStagnation();
};

defineExpose({
    init
});

</script>

<style lang="less" scoped>
.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
                    color: #1d2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165d7;
                        border-color: #4165d7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #00b42a;
                        border-color: #00b42a;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ed1;
                        border-color: #722ed1;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4e5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.analysis_type {
    display: flex;
    align-items: center;
    gap: 10px;
}

.analysis_type_item {
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 22px;
    padding: 3px 12px;
    background: #ffffff;
    border-radius: 24px;
    border: 1px solid #d4d7de;
    cursor: pointer;
}

.active {
    background: #4165d7;
    color: #ffffff;
    border: 1px solid #4165d7;
}

.analysis_table {
    width: 100%;

    :deep(th .cell) {
        background: none !important;
    }

    :deep(th:nth-child(n+2):nth-child(-n+4)) {
        background-color: rgba(65, 101, 215, 0.1) !important;

        .cell {
            color: #4165D7;
        }
    }

    :deep(td:nth-child(n+2):nth-child(-n+4)) {
        background-color: rgba(65, 101, 215, 0.05) !important;
    }
}

.analysis_table2 {
    width: 100%;

    :deep(th .cell) {
        background: none !important;
    }

    :deep(th:nth-child(n+2):nth-child(-n+4)) {
        background-color: rgba(247, 114, 52, 0.1) !important;

        .cell {
            color: #F77234;
        }
    }

    :deep(td:nth-child(n+2):nth-child(-n+4)) {
        background-color: rgba(247, 114, 52, 0.05) !important;
    }
}

.list {
    border: 1px solid #EBEEF5;
    display: flex;
    gap: 1px;

    .litItem {
        flex: 1;
        background-color: #EBEEF5;
        display: flex;
        flex-direction: column;
        gap: 1px;

        .header {
            height: 40px;
            background: #F5F7FA;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 14px;
            color: #303133;
        }

        .line {
            height: 64px;
            background-color: #FFF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 14px;
            color: #303133;
        }
    }
}
</style>
