<template>
  <!-- 出售订单 -->
  <div class="container sell-order-wrap">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab class="newTabSty" :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <ny-table :state="state" :columns="columns" :show-summary="true" :summary-method="getSummaries" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.state" @change="state.getDataList"></ny-button-group>
        </template>

        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important" v-model="state.dataForm.keywords" placeholder="请输入订单编号/商品编码/游戏账号" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
            </el-form-item>
            <el-form-item>
              <el-select v-model="state.dataForm.saleType" clearable placeholder="请选择出售类型" @change="state.getDataList">
                <el-option v-for="(item, index) in saleTypeList" :label="item.label" :value="item.value" :key="index"></el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>

        <!-- 游戏账号 -->
        <template #gameAccount="{ row }">
          <el-text type="primary" text class="pointer" @click="operationLogHandle(row, false, 'detail')">{{ row.gameAccount }}</el-text>
        </template>

        <!-- 合同 -->
        <template #contract="{ row }">
          <span v-loading="row.downLoading" style="display: flex; justify-content: center; align-items: center">
            {{ row.contractState }}
            <el-icon style="color: var(--el-color-primary); cursor: pointer; margin-left: 4px" v-if="row.contractState == '合同已锁定'" @click="downPdf(row)"><Management /></el-icon> </span
        ></template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == '待支付'" type="warning">待支付</el-tag>
          <el-tag v-if="row.state == '已支付'" type="primary">已支付</el-tag>
          <el-tag v-if="row.state == '交易成功'" type="success">交易成功</el-tag>
          <el-tag v-if="row.state == '已取消'" type="info">已取消</el-tag>
          <el-tag v-if="row.state == '标记售后'" type="danger">标记售后</el-tag>
        </template>

        <template #operation="{ row }">
          <operation-btns
            :key="Math.random()"
            :max="4"
            :buttons="[
              {
                text: '详情',
                type: 'primary',
                click: () => showDetail(row),
                isShow: true
              }
            ]"
          />
        </template>
      </ny-table>
    </el-card>

    <detail ref="detailRef"></detail>
    <!-- 操作日志 -->
    <operation-log ref="operationLogRef" :key="operationLogKey" @refresh="state.getDataList()"></operation-log>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { BigNumber } from "bignumber.js";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { useAppStore } from "@/store";
import OperationLog from "./components/OperationLog.vue";
import detail from "./components/detail.vue";
import OperationBtns from "@/components/ny-table/src/components/OperationBtns.vue";
const store = useAppStore();
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "sn",
    label: "订单编号",
    minWidth: 190
  },
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: 102
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 200
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 184
  },
  {
    prop: "purchaseAmount",
    label: "回收价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "contract",
    label: "合同",
    width: 120
  },
  {
    prop: "saleAmount",
    label: "售价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "guaranteePrice",
    label: "包赔费(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "dealAmount",
    label: "成交价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "createDate",
    label: "创建时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "payTime",
    label: "支付时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "state",
    label: "状态",
    width: 136,
    fixed: "right"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 280
  }
]);

const view = reactive({
  getDataListURL: "/sale/page",
  getDataListIsPage: true,
  createdIsNeed: false,
  exportURL: "/sale/export",
  dataForm: {
    gameId: "",
    state: "",
    keywords: "",
    saleType: "",
    startTime: "",
    endTime: "",
    order: "",
    orderField: "",
    tenantCode: "",
    acquisitionType: 2
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.saleType = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  { label: "已支付", value: "PAID" },
  { label: "已取消", value: "CANCELED" },
  { label: "交易成功", value: "DEAL_SUCCESS" }
  // { label: "标记售后", value: "SALE_AFTER_SERVICE" },
];

// 出售类型
const saleTypeList = [
  { label: "合作商同步", value: "PARTNER_SYNCHRONIZATION" },
  { label: "平台直售", value: "ORIGINAL_OWNER" },
  { label: "平台代售", value: "PLATFORM_CONSIGNMENT" }
];

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 合计行计算函数
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      return (sums[index] = "合计");
    } else if (column.property == "saleAmount") {
      let total: any = 0;
      state.dataList.map((item: any) => {
        if (item.saleAmount) total = total + (item.saleAmount || 0);
      });
      return (sums[index] = total);
    } else if (column.property == "dealAmount") {
      let total: any = 0;
      state.dataList.map((item: any) => {
        if (item.dealAmount) total = (+total + (+item.dealAmount || 0)).toFixed(2);
      });
      return (sums[index] = total);
    }
  });
  return sums;
};
// 下载合同
const downPdf = async (obj: any) => {
  obj.downLoading = true;
  let res = await baseService.get("/bestsign/downloadContract/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  obj.downLoading = false;
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);

// 详情
const detailRef = ref();
const showDetail = (id: number) => {
  detailRef.value.init(id);
};

// 操作日志
const operationLogRef = ref();
const operationLogKey = ref(0);
const operationLogHandle = async (row: any, marker?: boolean, type?: string) => {
  operationLogKey.value++;
  await nextTick();
  operationLogRef.value.init(row, marker, type);
};
onMounted(() => {
  state.dataForm.tenantCode = store.state.user.tenantCode;
  state.getDataList();
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.el-tag {
  border: 1px solid;
}
</style>
