<template>
  <div class="ny-table">
    <div class="table-header clearfix">
      <div class="header-button-lf">
        <el-button v-if="refresh" type="info" icon="Refresh" @click="refreshHandle"></el-button>
        <slot name="header"></slot>
      </div>
      <div class="header-button-ri">
        <slot name="header-right"></slot>
        <el-popover v-if="showColSetting" placement="left-start" :width="400" popper-class="header-popover" trigger="click">
          <template #reference>
            <el-button plain>
              <el-icon size="16">
                <Setting />
              </el-icon>
              <!-- <span>设置</span> -->
            </el-button>
          </template>
          <el-card>
            <template #header>
              <div class="card-header">
                <span>自定义列</span>
              </div>
            </template>
            <el-row :id="uuid + '-col-setting'">
              <template v-for="(item, index) in colSettingList" :key="index">
                <el-col :span="12" v-show="!columnTypes.includes(item.type) && item.prop !== 'operation'">
                  <el-checkbox v-model="item.isShow" :label="item.label" @change="(e:any) => colSettingChangeHandle(e, item.prop)" class="move" />
                </el-col>
              </template>
            </el-row>
          </el-card>
        </el-popover>
      </div>
    </div>

    <!-- 表格筛选模块下的插槽 -->
    <slot name="header-custom"></slot>

    <div class="table-body" id="table-body" :style="isDrag ? 'cursor: grab;' : ''">
      <div class="tableTop"></div>
      <el-table
        v-loading="state.dataListLoading"
        v-bind="$attrs"
        :id="uuid"
        :data="state.dataList"
        element-loading-background="#fff"
        border
        :showOverflowTooltip="true"
        style="width: 100%"
        :height="Height"
        class="draggable-table"
        :tree-props="{ children: treeChildren, hasChildren: hasChildren }"
        :row-key="rowKey"
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="sortChange"
        ref="tableRef"
        :cell-class-name="cellHeight"
        :row-style="rowStyle"
        @mousedown="mouseDownHandler"
        @mouseup="mouseUpHandler"
        @mousemove="mouseMoveHandler"
      >
        <!-- 默认插槽 -->
        <slot />
        <template v-for="item in tableColumns" :key="item">
          <!-- selection || radio || index || expand || sort -->
          <el-table-column v-if="item && item.type && columnTypes.includes(item.type)" v-bind="item" :align="item.align ?? 'center'" :reserve-selection="item.type == 'selection'" :selectable="selectable">
            <template #default="scope">
              <!-- expand -->
              <template v-if="item.type == 'expand'">
                <component :is="item.render" v-bind="scope" v-if="item.render" />
                <slot v-else :name="item.type" v-bind="scope" />
              </template>
              <!-- radio -->
              <el-radio v-if="item.type == 'radio'" v-model="radio" :label="scope.row[rowKey]">
                <i></i>
              </el-radio>
              <!-- sort -->
              <el-button bg text v-if="item.type == 'sort'" class="move">
                <el-icon> <Rank /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <!-- other -->
          <table-column v-else :column="item">
            <template v-for="slot in Object.keys($slots)" #[slot]="scope">
              <slot :name="slot" v-bind="scope" />
            </template>
          </table-column>
        </template>
        <!-- 插入表格最后一行之后的插槽 -->
        <template #append>
          <slot name="append" />
        </template>

        <!-- 空状态 -->
        <template #empty>
          <slot name="empty">
            <div style="padding: 68px 0">
              <img v-if="props.noDataType == 1" style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              <img v-else-if="props.noDataType == 2" style="width: 170px" src="@/components/ny-table/src/components/noMessage.png" alt="" />
              <img v-else-if="props.noDataType == 3" style="width: 170px" src="@/components/ny-table/src/components/noOrder.png" alt="" />
              <img v-else-if="props.noDataType == 4" style="width: 170px" src="@/components/ny-table/src/components/noShop.png" alt="" />
              <img v-else style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </slot>
        </template>
      </el-table>
    </div>

    <div class="flx-justify-between NYpagination" :style="`width: ${tableWidth}px;`">
      <div>
        <slot name="footer"></slot>
      </div>
      <el-pagination
        v-if="pagination"
        :current-page="state.page"
        :page-sizes="[10, 20, 50, 100, 500, 1000]"
        :page-size="state.limit"
        :total="state.total"
        background
        :pager-count="5"
        layout="total, sizes, prev, pager, next, jumper"
        :hide-on-single-page="true"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle"
      ></el-pagination>
    </div>
  </div>
</template>
<script lang="ts" setup name="NyTable">
import { defineEmits, defineProps, ref, watch, onMounted, nextTick, defineExpose, onUnmounted } from "vue";
import TableColumn from "./components/TableColumn.vue";
import { generateUUID } from "@/utils/utils";
import { localGet, localSet } from "@/utils/cache";
import { useRoute } from "vue-router";
import Sortable from "sortablejs";

interface ProTableProps {
  state?: any;
  // 列配置项  ==> 必传
  columns: any;
  // 是否需要刷新按钮 ==> 非必传（默认为false）
  refresh?: boolean;
  // 是否需要分页组件 ==> 非必传（默认为true）
  pagination?: boolean;
  // 是否显示设置自定义列 ==> 非必传（默认为true）
  showColSetting?: boolean;
  // 行数据的 Key，用来优化 Table 的渲染，当表格数据多选时，所指定的 id ==> 非必传（默认为 id）
  rowKey?: string;
  // 当前页面路由 该值不能重复 ==> 非必传 (默认为当前页路由)
  routePath?: string;
  // 树形表格配置项 当 row 中包含 treeChildren 字段时，被视为树形数据 ==> 非必传 (默认为children)
  treeChildren?: string;
  //  row 中的hasChildren字段来指定哪些行是包含子节点  ==> 非必传 (默认为hasChildren)
  hasChildren?: string;
  // 是否开启水平拖拽
  isDrag?: boolean;
  // 复选框方法
  selectable?: Function;
  // ch-96 ch-80 ch-56 ch-40
  cellHeight?: string;
  // 表格高度
  Height?: string;
  // 空状态回显样式
  noDataType?: string | number;
}

const emits = defineEmits(["refresh", "pageSizeChange", "pageCurrentChange", "selectionChange", "dragSort", "sortableChange"]);
const route = useRoute();
const currentRoutePath = ref(route.path);

const props = withDefaults(defineProps<ProTableProps>(), {
  state: {},
  columns: () => [],
  refresh: false,
  pagination: true,
  showColSetting: true,
  rowKey: "id",
  routePath: "",
  treeChildren: "children",
  hasChildren: "hasChildren",
  isDrag: false,
  cellHeight: "ch-96",
  // 1 结果 2 消息 3 订单 4 商品
  noDataType: "1"
});
const state = ref(<any>props.state);

// 生成组件唯一id
const uuid = ref("id-" + generateUUID());

// 接收 columns 并设置为响应式
const tableColumns = ref(<any>[]);

// column 列类型
const columnTypes = ["selection", "radio", "index", "expand", "sort"];
// 单选值
const radio = ref("");

let colSettingList: any = ref([]);

// 存储当前表格隐藏列
const colSettingChangeHandle = (e: any, prop: any) => {
  let data = localGet("colSetting") || {};
  if (!data[currentRoutePath.value]) {
    data[currentRoutePath.value] = colSettingList.value;
  }
  let index = data[currentRoutePath.value].findIndex((item: any) => item.prop == prop);
  data[currentRoutePath.value][index].isShow = e;
  localSet("colSetting", data);
  columnsInit();
};

// 表格列初始化 从localStorage获取当前表格需要隐藏的列prop   根据isShow 状态显示(默认true)
const columnsInit = (type?: string) => {
  currentRoutePath.value = props.routePath || route.path;
  let colSetting: any = [];
  let list = localGet("colSetting")?.[currentRoutePath.value] || [];
  let data = localGet("colSetting") || {};

  let tableProps = tableColumns.value.map((item: any) => {
    return item.prop;
  });

  for (let i = 0; i < list.length; i++) {
    if (!tableProps.includes(list[i].prop)) {
      delete data[currentRoutePath.value];
      localSet("colSetting", data);
      list = tableColumns.value;
      break;
    }
  }

  // 表格列数量 和 本地存储表格列数量 不一致 清除本地存储
  if (list && list.length != tableColumns.value.length) {
    tableColumns.value.map((item: any) => {
      list.map((item: any) => {
        if (item.prop == item.prop) {
          item.isShow = item.isShow;
        }
      });
    });

    // delete data[currentRoutePath.value];
    localSet("colSetting", data);
  }

  if (list && list.length) {
    colSetting = list;
  }

  nextTick(() => {
    if (colSetting && colSetting.length) {
      // colSettingList.value = colSetting;
      tableColumns.value = colSetting;
    }
    if (type) {
      tableColumns.value.map((item: any, index: number) => {
        item.isShow = item.isShow ?? true;
      });
      if (props.showColSetting) colSettingList.value = tableColumns.value;
    }
  });
};

// 刷新
const refreshHandle = () => {
  emits("refresh");
};

// 分页
const pageSizeChangeHandle = (val: number) => {
  emits("pageSizeChange", val);
  nextTick(() => {
    tableRef.value.setScrollTop(0);
  });
};

const pageCurrentChangeHandle = (val: number) => {
  emits("pageCurrentChange", val);
  nextTick(() => {
    tableRef.value.setScrollTop(0);
  });
};

// 多选
const dataListSelectionChangeHandle = (list: any[]) => {
  emits("selectionChange", list);
};

// 清空多选
const clearSelection = () => {
  tableRef.value.clearSelection();
};

// 表格拖拽排序
const dragSort = () => {
  const tbody = document.querySelector(`#${uuid.value} tbody`) as HTMLElement;
  Sortable.create(tbody, {
    handle: ".move",
    animation: 300,
    onEnd({ newIndex, oldIndex }) {
      // const [removedItem] = state.value.dataList.splice(oldIndex!, 1);
      // state.value.dataList.splice(newIndex!, 0, removedItem);
      emits("dragSort", { newIndex, oldIndex });
    }
  });
};

// 表格列排序拖拽
const tableColDragSort = () => {
  const tbody = document.querySelector(`#${uuid.value}-col-setting`) as HTMLElement;
  Sortable.create(tbody, {
    handle: ".move",
    animation: 300,
    onEnd({ newIndex, oldIndex }) {
      let data = localGet("colSetting") || {};
      let list = data[currentRoutePath.value] || JSON.parse(JSON.stringify(colSettingList.value));
      const [removedItem] = list.splice(oldIndex!, 1);
      list.splice(newIndex!, 0, removedItem);

      if (!data[currentRoutePath.value]) data[currentRoutePath.value] = [];
      data[currentRoutePath.value] = list;
      localSet("colSetting", data);

      // 重新初始化表格数据
      columnsInit();
    }
  });
};

// 点击表头进行排序
const sortChange = (column: any) => {
  if (column) {
    emits("sortableChange", {
      order: column.order,
      prop: column.prop
    });
  }
};

let headFixed = ref(false);
let tableHeight = ref(<any>"");

// 监听 columns 配置
watch(
  () => props.columns,
  (newVal) => {
    // 检查 columns 配置是否发生变化
    if (newVal.length != tableColumns.value.length) {
      // 监听 columns 配置，重新初始化表格数据
      tableColumns.value = props.columns;
      columnsInit("init");
    }
  },
  { deep: true, immediate: true }
);
const tableWidth = ref(0);
onMounted(() => {
  dragSort();
  if (props.showColSetting) tableColDragSort();
  tableWidth.value = document.getElementById(uuid.value).offsetWidth;
  window.addEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById(uuid.value).offsetWidth;
    });
  }); // 监听窗口大小变化
});

onUnmounted(() => {
  window.removeEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById(uuid.value).offsetWidth;
    });
  });
});

// 列表长表头拖拽优化
const tableRef = ref();
const mouseFlag = ref(false);
const mouseOffset = ref(0);
const mouseDownHandler = (e: any) => {
  if (!props.isDrag) {
    return false;
  }
  mouseOffset.value = e.clientX;
  mouseFlag.value = true;
};
const mouseUpHandler = () => {
  if (!props.isDrag) {
    return false;
  }
  mouseFlag.value = false;
};
const mouseMoveHandler = (e: any) => {
  if (!props.isDrag) {
    return false;
  }
  let divData = tableRef.value.scrollBarRef.wrapRef;
  if (mouseFlag.value) {
    divData.scrollLeft -= -mouseOffset.value + (mouseOffset.value = e.clientX);
  }
};
const selectedAll = () => {
  tableRef.value.toggleAllSelection();
};
const rowStyle = ({ row }: { row: any }) => {
  if (row.isWarning) {
    return { backgroundColor: "#ffcccc" };
  }
  return {};
};
defineExpose({
  clearSelection,
  selectedAll
});
</script>

<script lang="ts">
export default {
  name: "NyTable"
};
</script>

<style lang="less">
// 表格 header 样式
.table-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  .header-button-lf,
  .header-button-ri {
    display: flex;
    align-items: center;
    gap: 12px 12px;

    .el-button:not(.el-input .el-button) {
      margin-left: 0;
    }

    .el-form-item {
      margin-bottom: 0;
    }

    .el-input--suffix {
      width: 200px !important;
    }
  }

  .header-button-lf {
    flex: 1;
  }

  .header-button-ri {
    justify-content: flex-end;
  }
}

.header-popover {
  padding: 0 !important;
  border: none !important;
  overflow-x: hidden !important;
}

.table-body {
  .move,
  .move i {
    cursor: move;
  }

  // &.head-fixed{
  //     .el-table__header{
  //         position: fixed;
  //         z-index: 10;
  //         top: 86px;
  //         transition: top 0.3s;
  //         width: 735px;
  //     }
  // }
}
</style>
