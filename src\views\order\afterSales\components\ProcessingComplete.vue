<template>
  <el-drawer v-model="visible" :footer="null" :title="'售后订单处理日志'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%" class="ny-drawer">
    <div class="order-processing-form">
      <el-card v-loading="dataLoading">
        <div class="p-title mt-0">基本信息</div>
        <el-descriptions class="descriptions-label-140" :column="2" border>
          <el-descriptions-item label="订单号">{{ resData.sn }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ resData.shopCode }}</el-descriptions-item>
          <el-descriptions-item label="游戏名称">{{ resData.gameName }}</el-descriptions-item>
          <el-descriptions-item label="游戏账号">{{ resData.gameAccount }}</el-descriptions-item>
          <el-descriptions-item label="回收价(元)">￥{{ resData.purchaseAmount }}</el-descriptions-item>
          <el-descriptions-item label="成交价(元)"
            ><el-text type="danger">￥{{ resData.dealAmount }}</el-text></el-descriptions-item
          >
          <el-descriptions-item label="包赔费">￥{{ resData.fee || "-" }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ resData.createDate }}</el-descriptions-item>
          <el-descriptions-item label="打款时间">{{ formatTimeStamp(resData.payTime) }}</el-descriptions-item>
          <el-descriptions-item label="回收成功时间">{{ resData.dealDate }}</el-descriptions-item>
          <el-descriptions-item label="出售类型">{{ resData.saleType }}</el-descriptions-item>
          <el-descriptions-item label="回收人" :span="2">{{ resData.acquisitionName }}</el-descriptions-item>
          <!-- <el-descriptions-item label="卖方手机号" :span="2">{{ resData.sellerPhone }}</el-descriptions-item> -->
          <el-descriptions-item label="游戏密码" :span="2">
            <div class="flx-justify-between" v-if="resData.gamePassword">
              <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="售后类型">{{ resData.saleAfterType }}</el-descriptions-item>
          <el-descriptions-item label="账号来源">{{ resData.orderSource == "出售订单" ? "销售订单" : resData.orderSource }}</el-descriptions-item>
          <el-descriptions-item v-if="resData.saleAfterType == '其他'" :span="2" label="其他原因备注">{{ resData.saleAfterRemark || "-" }}</el-descriptions-item>
          <el-descriptions-item label="问题截图" :span="2">
            <el-image class="srceenshot" style="width: 54px; height: 54px" :src="resData.saleAfterPics" :preview-src-list="[resData.saleAfterPics]"></el-image>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="mt-12" v-if="resData.salesAfterType != null && showPart.showSale">
        <div class="p-title mt-0">卖家售后处理</div>
        <el-descriptions class="descriptions-label-140" :column="2" border>
          <el-descriptions-item label="售后结果">{{ resData.salesAfterType }}</el-descriptions-item>
          <template v-if="resData.salesAfterType && resData.salesAfterType.includes('赔付')">
            <el-descriptions-item label="退款金额(元)">{{ resData.salesAfterRefund }}</el-descriptions-item>
            <el-descriptions-item label="收款账户" :span="2">{{ resData.salesAfterAccountName || "-" }}</el-descriptions-item>
          </template>
          <el-descriptions-item label="售后处理记录" :span="2">
            <el-image v-for="item in resData.salesAfterPics" :key="item" class="srceenshot" style="width: 54px; height: 54px; margin-right: 4px" :src="item" :preview-src-list="resData.salesAfterPics"></el-image>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ resData.salesAfterRemark || "-" }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <!-- 卖家通过+买家已处理 -->
      <template v-if="showPart.showBuy && resData.salesAfterAudit == 1 && resData.buyersType != null">
        <!-- 进入买家处理时显示, 财务确认时不显示 -->
        <el-card class="mt-12">
          <div class="p-title mt-0">买家售后处理</div>
          <el-descriptions class="descriptions-label-140" :column="2" border>
            <el-descriptions-item label="售后结果">{{ resData.buyersType }}</el-descriptions-item>
            <template v-if="resData.buyersType && resData.buyersType.includes('赔付')">
              <el-descriptions-item label="退款金额(元)">{{ resData.buyersRefund }}</el-descriptions-item>
              <el-descriptions-item label="账号名称" :span="2">{{ resData.buyersAccountName || "-" }}</el-descriptions-item>
            </template>
            <el-descriptions-item label="售后处理记录" :span="2">
              <el-image v-for="item in resData.buyersPics" :key="item" class="srceenshot" style="width: 54px; height: 54px; margin-right: 4px" :src="item" :preview-src-list="resData.buyersPics"></el-image>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ resData.buyersRemark || "-" }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </template>
      <!-- 流程进度  -->
      <el-card class="mt-12" v-if="processingData.length > 0 && showPart.showFlow">
        <div class="basicInfoSty">
          <div class="titleSty">流程进度</div>
          <el-scrollbar max-height="600px">
            <el-steps direction="vertical" :active="payStatus == 2 ? activeLength + 1 : activeLength">
              <template :key="index_" v-for="(activity, index_) in processingData">
                <el-step title="申请人" :key="index_" v-if="activity.taskIndex == 1">
                  <template #description>
                    <div class="cardInfo">
                      <div>
                        申请人：<span style="color: var(--el-color-primary)">{{ activity.creatorName }}</span>
                      </div>
                      <div>
                        申请时间：
                        <span>{{ formatTimeStamp(activity.createDate) }}</span>
                      </div>
                    </div>
                  </template>
                </el-step>
                <el-step v-else :title="activity.name">
                  <template #description>
                    <div class="cardInfo">
                      <div>
                        审批人：<span style="color: var(--el-color-primary)">{{ getUserName(activity.stages) }}</span>
                      </div>
                      <div>
                        处理时间：
                        <span>{{ formatTimeStamp(activity.createDate) || "-" }}</span>
                      </div>
                      <template v-if="+activity.status > 0">
                        <div style="margin-top: 6px">
                          最终审批人：<span>{{ activity.userName || "-" }}</span>
                        </div>
                        <div>
                          审批结果：
                          <span :style="{ color: +activity.status == 1 ? '#67C23A' : '#F56C6C' }">{{ ["", "审核通过", "审核拒绝"][+activity.status] }}</span>
                        </div>
                        <div v-if="activity.status == 2">
                          拒绝原因：<span>{{ activity.remarks }}</span>
                        </div>
                      </template>
                    </div>
                  </template>
                </el-step>
              </template>
              <el-step v-if="payStatus != 0" title="支付状态">
                <template #description>
                  <div class="cardInfo">
                    <div :style="{ color: +payStatus == 2 ? '#67C23A' : '#F56C6C' }">{{ ["", "未付款", "已付款"][+payStatus] }}</div>
                  </div>
                </template>
              </el-step>
            </el-steps>
          </el-scrollbar>
        </div>
      </el-card>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { useAppStore } from "@/store";
const store = useAppStore();
const emits = defineEmits(["refresh"]);

const visible = ref(false);

// 售后结果
const afterSalesResults = [
  { label: "赔付(全额退款)", value: "RETURN_ALL" },
  { label: "赔付(部分退款)", value: "RETURN_PART" },
  { label: "还号", value: "RETURN_ACCOUNT" },
  { label: "换号", value: "EXCHANGE_ACCOUNT" },
  { label: "退号", value: "GIVE_BACK" },
  { label: "不予处理", value: "REJECT" },
  { label: "其他", value: "OTHER" }
];
const orderId = ref(0);
const showPart = reactive({
  showBuy: true,
  showFlow: true,
  showSale: true
});
const resData = ref(<any>{});
const dataLoading = ref(false);

// 是否显示游戏密码
const isShowGamePassword = ref(false);

const init = (data?: any, showFlow = true, showBuy = true, showSale = true) => {
  resData.value = {};
  orderId.value = data.id;
  visible.value = true;
  showPart.showFlow = showFlow;
  showPart.showSale = showSale;
  showPart.showBuy = showBuy;
  dataLoading.value = true;
  baseService
    .get("/saleAfter/info/" + data.id)
    .then((res) => {
      resData.value = res?.data;
      getHandleDetail();
    })
    .finally(() => {
      dataLoading.value = false;
    });
};
// 0 不需要显示，1 显示待付款，2 显示已付款
const payStatus = ref(0);
// 流程进度
const processingData = ref(<any>{});
const activeLength = ref(0);
const getHandleDetail = () => {
  processingData.value = [];
  activeLength.value = 0;

  baseService
    .get("/order/sysorderdisposedatainfo/info", { orderId: orderId.value, orderDisposeType: "SAO" })
    .then((res) => {
      resData.value = Object.assign(resData.value, res.data?.data);
      resData.value.buyersPics = resData.value.buyersPics ? resData.value.buyersPics.split(",") : [];
      resData.value.salesAfterPics = resData.value.salesAfterPics ? resData.value.salesAfterPics.split(",") : [];
      // 处理流程数据
      let arr = <any>[];
      let flowArr = [];
      // 不显示买家时，流程只显示卖家
      if (!showPart.showBuy) {
        if (res.data?.sellAuditData && res.data?.sellAuditData.length > 0) {
          flowArr = res.data?.sellAuditData;
        }
        // 进入买家审批只显示买家
      } else if (res.data?.buyersAuditData && res.data?.buyersAuditData.length > 0) {
        flowArr = res.data?.buyersAuditData;
        payStatus.value = 1;
        // 未进入买家处理是显示卖家
      } else if (resData.value.buyersType == null && res.data?.sellAuditData && res.data?.sellAuditData.length > 0) {
        flowArr = res.data?.sellAuditData;
      }
      if (flowArr.length > 0) {
        flowArr.forEach((ele: any, index: number) => {
          ele.tasks = [
            {
              createDate: ele.createDate,
              creatorName: ele.creatorName,
              taskIndex: 1
            },
            ...ele.tasks
          ];
          arr = [...arr, ...ele.tasks];
          // 退款已付款 流程最后一步
          if (flowArr.some((f: any) => f.type == 2 && f.payStatus == 1)) {
            payStatus.value = 2;
          }
          ele.tasks?.forEach((ele_: any) => {
            if (index != flowArr.length - 1 || (index == flowArr.length - 1 && +ele_.status > 0)) {
              activeLength.value++;
            }
          });
        });
      }
      processingData.value = arr;
      dataLoading.value = false;
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

const getUserName = (arr: any) => {
  return arr?.map((ele) => ele.stageName).join("，");
};

defineExpose({
  init
});
</script>

<style lang="scss">
.el-descriptions__content {
  display: flex;
  align-items: center;
}
.order-processing-form {
  .screenshots {
    width: 96px;
    height: 96px;
    border-radius: 4px;
  }

  .ny-steps {
    .el-step__icon {
      background: #f0f2f5;
      border-color: #f0f2f5;
      color: #909399;
    }

    .el-step__head.is-finish .el-step__icon {
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }

    .el-step__title {
      font-size: 14px;
    }

    .step-desc-card {
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #ebeef5;
      padding: 12px;
      margin: 4px 0 10px 0;
      font-size: 12px;
      line-height: 16px;
      color: #606266;

      .text {
        padding: 2px 0;

        .el-text {
          font-size: 12px;
        }
      }
    }
  }
}
.loadingSty {
  .el-loading-spinner .circular {
    display: none;
  }
}
.basicInfoSty {
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}

.el-step__head {
  &.is-process,
  &.is-finish {
    .el-step__icon {
      background: var(--el-color-primary);
      color: #fff;
      border-color: #fff;
      .el-step__icon-inner {
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 14px;
      }
    }
  }
  .el-step__icon {
    background: #f0f2f5;
    color: #909399;
    border-color: #fff;
    .el-step__icon-inner {
      font-weight: 400;
      font-size: 12px;
      color: #909399;
      line-height: 14px;
    }
  }

  .el-step__line {
    width: 1px;
    .el-step__line-inner {
      border: 1px solid #c0c4cc;
      border-width: 0px !important;
    }
  }
}
.el-step__main .el-step__title {
  font-family: OPPOSans, OPPOSans;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 24px;
}
.cardInfo {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ebeef5;
  padding: 16px 12px;
  font-family: OPPOSans, OPPOSans;
  font-weight: 400;
  font-size: 12px;
  color: #606266;
  line-height: 16px;
  margin-bottom: 10px;
  > div {
    margin-bottom: 2px;
  }
}
</style>
