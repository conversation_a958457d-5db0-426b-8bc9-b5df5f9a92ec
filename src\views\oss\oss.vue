<template>
    <div class="mod-oss__oss" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="primary" @click="configHandle()">{{ $t("oss.config") }}</el-button>
                    <el-button type="primary" @click="uploadHandle()">{{ $t("oss.upload") }}</el-button>
                    <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>

        </el-card>
        <!-- 弹窗, 云存储配置 -->
        <config v-if="state.configVisible" ref="configRef"></config>
        <!-- 弹窗, 上传文件 -->
        <upload v-if="state.uploadVisible" ref="uploadRef" @refreshDataList="state.getDataList"></upload>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs } from "vue";
import Config from "./oss-config.vue";
import Upload from "./oss-upload.vue";

const configRef = ref();
const uploadRef = ref();

const view = reactive({
    getDataListURL: "/sys/oss/page",
    getDataListIsPage: true,
    deleteURL: "/sys/oss",
    deleteIsBatch: true,
    dataForm: {},
    configVisible: false,
    uploadVisible: false
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        prop: "url",
        label: "URL地址",
        minWidth: 400
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 100
    }
])

// 云存储配置
const configHandle = () => {
    state.configVisible = true;
    nextTick(() => {
        configRef.value.init();
    });
};

// 上传文件
const uploadHandle = () => {
    state.uploadVisible = true;
    nextTick(() => {
        uploadRef.value.init();
    });
};
</script>
