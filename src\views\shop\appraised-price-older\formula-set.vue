<!-- 公式设定 -->
<template>
  <ny-table
    :state="state"
    @pageSizeChange="state.pageSizeChangeHandle"
    @pageCurrentChange="state.pageCurrentChangeHandle"
    :columns="columns"
  >
    <template #header>
      <!-- v-if="state.hasPermission('customized:appraisedPrice:save')" -->
      <el-button type="primary" @click="addOrUpdateHandle()"> 新增 </el-button>
    </template>

    <!-- 系统区服 -->
    <template #gameAreaName="{ row }">
      {{ row.gameAreaName ? row.gameAreaName : "全部区服" }}
    </template>

    <!-- 条件 -->
    <template #settingBos="{ row }">
        {{ joinConditions(row.settingVoList) }}
    </template>

    <!-- 公式 -->
    <template #showFormula="{ row }">
      <p v-html="row.showFormula"></p>
    </template>

    <!-- 操作 -->
    <template #operation="{ row }">
      <el-button
        text
        bg
        type="primary"
        @click="addOrUpdateHandle(row)"
        >{{ $t("update") }}</el-button
      >
      <el-button
        text
        bg
        type="danger"
        @click="state.deleteHandle(row.id)"
        >{{ $t("delete") }}</el-button
      >
    </template>
  </ny-table>

  <!-- 新增 -->
  <formula-set-add-or-update
    ref="formulaSetAddOrUpdateRef"
    :key="formulaSetAddOrUpdateKey"
    @refresh="state.getDataList()"
  ></formula-set-add-or-update>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, nextTick } from "vue";
import useView from "@/hooks/useView";
import formulaSetAddOrUpdate from "./formula-set-add-or-update.vue";
import { IObject } from "@/types/interface";

const visible = ref(false);

const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 160,
  },
  {
    prop: "gameAreaName",
    label: "系统区服",
    minWidth: 150,
  },
  {
    prop: "settingBos",
    label: "条件",
    minWidth: 200,
  },
  {
    prop: "formulaContent",
    label: "公式",
    minWidth: 200,
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 160,
  },
]);

const view = reactive({
  getDataListURL: "/appraise/formula/page",
  getDataListIsPage: true,
  deleteURL: "/appraise/formula/delete",
  deleteIsBatch: true,
  dataForm: {},
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const init = () => {
  visible.value = true;
  state.query();
};

// 新增
const formulaSetAddOrUpdateRef = ref();
const formulaSetAddOrUpdateKey = ref(0);
const addOrUpdateHandle = (row?: object) => {
  formulaSetAddOrUpdateKey.value++;
  nextTick(() => {
    formulaSetAddOrUpdateRef.value.init(row);
  });
};

// 条件转成字符串 逗号分隔
const joinConditions = (data: any) => {
  let arr: IObject = [];
  data.map((item: any) => {
    arr.push(item.settingContent);
  })
  console.log(arr)
  return arr.join(',');
}

defineExpose({
  init,
});
</script>
