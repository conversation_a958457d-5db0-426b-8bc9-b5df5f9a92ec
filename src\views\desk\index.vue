<template>
  <div class="desk_page">
    <div style="display: flex; flex-wrap: nowrap;">
      <div style="margin-right: 12px; flex: 1">
        <!-- 实时数据 -->
        <realTime />
        <!-- 快捷入口 -->
        <quick style="height: 138px;" :isWrap="false" />
        <!-- 商城概览 -->
        <shoppingMall />
        <el-row :gutter="12">
          <el-col :span="12">
            <!-- 分销商统计 -->
            <distributor />
          </el-col>
          <el-col :span="12">
            <!-- 供货商统计 -->
            <supplier />
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col style="display: flex; flex-direction: column;" :span="12">
            <!-- 热门搜索 -->
            <popular style="flex: 1;" />
          </el-col>
          <el-col style="display: flex; flex-direction: column;" :span="12">
            <!-- 账单统计 -->
            <billStatistics style="flex: 1;" />
          </el-col>
        </el-row>
      </div>
      <div style="width: 426px;">
        <!-- 用户卡片 -->
        <userCard />
        <!-- 待办事项 -->
        <todoList style="height: 138px;" />
        <!-- kpi -->
        <kpiTable />
        <!-- 系统消息 -->
        <sysMsg />
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive } from "vue";
import realTime from "./realTime.vue";
import quick from "./quick.vue";
import shoppingMall from "./shoppingMall.vue";
import distributor from "./distributor.vue";
import supplier from "./supplier.vue";
import popular from "./popular.vue";
import billStatistics from "./billStatistics.vue";
import userCard from "./userCard.vue";
import todoList from "./todoList.vue";
import kpiTable from "./kpiTable.vue";
import sysMsg from "./sysMsg.vue";
</script>

<style lang='less' scoped>
.desk_page {
  box-sizing: border-box;
  width: 100%;
  padding: 0px 6px 0px 0px;
}
.card {
}
</style>