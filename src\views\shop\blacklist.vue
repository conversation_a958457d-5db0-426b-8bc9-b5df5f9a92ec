<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '平台', value: '0' },
          { label: '全网', value: '1' }
        ]"
        v-model="view.dataForm.isAll"
        value="value"
        label="label"
        @change="handleClick"
      ></ny-flod-tab>
      <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <el-button type="primary" v-if="state.hasPermission('blacklist:blacklist:save')" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <!-- <el-button type="warning" @click="">{{ $t("excel.import") }}</el-button> -->
          <el-button type="warning" v-if="state.hasPermission('blacklist:blacklist:export')" @click="state.exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="danger" v-if="state.hasPermission('blacklist:blacklist:delete')" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
        </template>
        <template #header-right>
          <el-input v-model="view.dataForm.name" placeholder="请输入姓名" :prefix-icon="Search" style="width: 220px" />
          <el-input v-model="view.dataForm.account" placeholder="请输入游戏账号" :prefix-icon="Search" style="width: 220px" />
          <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" />
          <el-button type="primary" @click="queryFn">查询</el-button>
          <el-button @click="resetFn">重置</el-button>
        </template>

        <!-- 身份证号 -->
        <template #idCard="{ row }">
          <span>{{ row.idCard ? maskedIdNumber(row.idCard) : "" }}</span>
        </template>

        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #updateDate="{ row }">
          <span>{{ formatTimeStamp(row.updateDate) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button v-if="state.hasPermission('blacklist:blacklist:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('blacklist:blacklist:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    </el-card>
  </div>
  <!-- 弹窗, 新增 / 修改 -->
  <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./blacklist-add-or-update.vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { Search } from "@element-plus/icons-vue";
import { getDictDataList, maskedIdNumber } from "@/utils/utils";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
import { useI18n } from "vue-i18n";
const store = useAppStore();
const { t } = useI18n();

const timeInterval = ref(); // 时间区间

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "account",
    label: "游戏账号",
    minWidth: "170",
    align: "center"
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "name",
    label: "姓名",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "idCard",
    label: "身份证号",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "platform",
    label: "平台名称",
    minWidth: "150",
    align: "center"
  },
  {
    prop: "remarks",
    label: "拉黑原因",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "160",
    align: "center",
    sortable: "custom"
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: "160",
    align: "center",
    sortable: "custom"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "140"
  }
]);

const view = reactive({
  getDataListURL: "/blacklist/blacklist/page",
  getDataListIsPage: true,
  exportURL: "/blacklist/blacklist/export",
  deleteURL: "/blacklist/blacklist",
  deleteIsBatch: true,
  dataForm: {
    gameName: null, // 游戏名称
    orderField: "", // 排序字段
    order: "", // 排序方式
    isAll: "0", // 是否全平台
    name: "", // 姓名
    account: "", // 账号
    start: "", // 开始时间
    end: "" // 结束时间
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  if (order == "ascending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "asc";
  }
  if (order == "descending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "desc";
  }
  if (order == null) {
    view.dataForm.orderField = "";
    view.dataForm.order = "";
  }
  state.getDataList();
};

// 页签点击事件
const handleClick = (name: any) => {
  state.getDataList();
};

// 查询
const queryFn = () => {
  view.dataForm.start = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.end = timeInterval.value ? timeInterval.value[1] : "";
  state.getDataList();
};
// 重置
const resetFn = () => {
  view.dataForm.name = "";
  view.dataForm.account = "";
  view.dataForm.start = "";
  view.dataForm.end = "";
  timeInterval.value = "";
  state.getDataList();
};

onMounted(() => {});
</script>

<style lang="less" scoped></style>
