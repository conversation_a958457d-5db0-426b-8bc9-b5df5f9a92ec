import { defineStore } from "pinia";
import baseService from "@/service/baseService";
export const useSettingStore = defineStore("useSettingStore", {
    state: () => ({
        info: {
            //平台名称
            seoPlatformName: "",
            //SEO标题
            seoTitle: "",
            //SEO关键词
            seoKeywords: "",
            //SEO描述
            seoDescription: "",
            //ICO图标
            seoIco: "",
            //PC端网址
            pcWebsiteUrl: "",
            //PC端LOGO
            pcLogo: "",
            //标识语
            pcTips: "",
            //ICP备案
            pcIcp: "",
            //公网安备案号
            pcPoliceIcp: "",
            //底部信息
            pcFooterInfo: "",
            //百度统计
            pcBaiduStatiscs: "",
            //H5端网址
            mobileWebsiteUrl: "",
            //移动端LOGO
            mobileLogo: "",
            //APP下载地址
            mobileDownloadUrl: "",
            //APP版本号
            mobileVersion: "",
            //更新说明
            mobileUpdateDescription: "",
            //后台名称
            backendTitle: "",
            //后台LOGO
            backendLogo: "",
            //员工默认头像
            backendDefaultAvatar: "",
            //用户默认头像
            userDefaultAvatar: "",
            //商品出售费率
            shopSaleRate: "",
            //出售商品需要实名认证
            shopRealnameSale: 0,
            //购买商品需要实名认证
            shopRealnameBuy: 0,
            // 买家出价限制
            bargainRateLimit: "",
            // 买家商品议价次数
            bargainCountLimit: "",
            // 买家议价总次数
            bargainTotalLimit: "",
            // 议价有效时间
            bargainTimeLimit: "",
            // 商品擦亮提示
            shopRehotTips: "",
            // 商品一天擦亮次数
            shopRehotCount: "",
            // 用户协议
            userAgreement: "",
            // 隐私政策
            privateAgreement: "",
            // 买家须知
            buyerAgreement: "",
            // 购买须知
            sellerAgreement: "",
            // 外链地址
            websiteUrl: "",
        }
    }),
    actions: {
        async getInfo() {
            await baseService.get("/global/setting/get").then(res => {
                Object.assign(this.info, res.data);
            })
        }

    }
});