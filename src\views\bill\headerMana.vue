<template>
  <el-drawer class="descriptionsDrawer" v-model="visible" :footer="null" :title="'抬头管理'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="934px">
    <div class="partBg" style="height: calc(100vh - 52px)">
      <div class="detailcard">
        <ny-table :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
          <template #header>
            <el-button type="primary" @click="addOrUpdateHandle()">新增抬头</el-button>
          </template>
          <template #headerType="{ row }">
            {{ row.headerType == 1 ? "企业单位" : row.headerType == 2 ? "个人" : "-" }}
          </template>
          <template #taxNumber="{ row }">
            {{ row.taxNumber || "-" }}
          </template>
          <template #isDefault="{ row }">
            <el-switch v-model="row.isDefault" :inactive-value="0" :active-value="1" @click="changeData(row)" />
          </template>
          <!-- 操作 -->
          <template #operation="{ row }">
            <el-link type="primary" @click="addOrUpdateHandle(row)">{{ "编辑" }}</el-link>
            <el-link type="danger" @click="state.deleteHandle(row.id)" style="margin-left: 12px">删除</el-link>
          </template>
        </ny-table>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, reactive, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./header-add-or-update.vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const visible = ref(false);
const view = reactive({
  getDataListURL: "/invoice/header/page",
  getDataListIsPage: true,
  deleteURL: "/invoice/header",
  deleteIsBatch: true
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// 单行数据
const addOrUpdateRef = ref();
const addOrUpdateHandle = (row?: any) => {
  addOrUpdateRef.value.init(row);
};

// 表格配置项
const columns = reactive([
  {
    prop: "headerName",
    label: "抬头名称",
    minWidth: 140
  },
  {
    prop: "headerType",
    label: "抬头类型",
    minWidth: 120
  },
  {
    prop: "taxNumber",
    label: "税号",
    width: 300
  },
  {
    prop: "isDefault",
    label: "默认抬头",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    width: 140
  }
]);
const init = (id?: number) => {
  visible.value = true;
  state.getDataList();
};
const btnLoading = ref(false);
const changeData = (row: any) => {
  baseService.put("/invoice/header", row).then(() => {
    ElMessage.success({
      message: '修改成功！',
      duration: 500,
      onClose: () => {
        state.getDataList();
      }
    });
  });
};
defineExpose({
  init
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
</style>
