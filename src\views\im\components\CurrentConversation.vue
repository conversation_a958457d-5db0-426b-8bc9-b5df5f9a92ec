<template>
  <div class="ny-im-conversation flx-1">
    <div class="conversation-content flx-column flx-1">
      <div class="header flx-justify-between">
        <!-- 单聊 -->
        <div v-if="imStore.currentConversation.conversationType == '1'" class="user flx-center" @click="avatarClickHandle()">
          <im-avatar class="avatar" :user="imStore.currentConversation.sessionData"></im-avatar>
          <div class="nickname flx-align-center">
            {{ imStore.currentConversation.sessionData ? imStore.currentConversation.sessionData.nickname : "-" }}
            <template v-if="imStore.currentConversation.sessionData.imRole">
              <el-tag v-if="imStore.currentConversation.sessionData.imRole != 0" :type="imStore.currentConversation.sessionData.imRole == 1 ? 'primary' : 'warning'" size="small" class="ml-8">
                {{ imStore.currentConversation.sessionData.imRole == 1 ? "客服" : "认证商户" }}
              </el-tag>
            </template>
          </div>
        </div>

        <!-- 群组 -->
        <div v-if="imStore.currentConversation.conversationType == '3'" class="user flx-center" @click="groupManageHandle()">
          <group-avatar :info="imStore.currentConversation.sessionData"></group-avatar>
          <div class="nickname">{{ imStore.currentConversation.sessionData ? imStore.currentConversation.sessionData.groupName : "-" }}</div>
        </div>

        <div class="right-btn flx-center text-primar">
          <!-- <img src="../../../assets/images/im_business_card.png" />
                    查看名片 -->
        </div>
      </div>

      <el-scrollbar class="msg-list-wrap" ref="scrollbarRef" @scroll="handleReachTop">
        <div class="msg-list">
          <!-- messageDirection 消息方向： 1: 发送，2: 接收。  -->
          <div class="msg-item" v-for="(item, index) in msgList" :key="index" :class="{ my: item.messageDirection == 1 }">
            <div class="msg-time" v-if="!item.hideTime">
              <div class="time">{{ timeStringAutoShort(item.sentTime, true) }}</div>
            </div>

            <!-- 创建群聊  更新群聊  订单流程更新-->
            <div v-if="item.messageType == 'RC:IWNormalMsg' && (item.content.msgType == 'CU:createGroup' || item.content.msgType == 'CU:uploadGroup' || item.content.msgType == 'CU:flow')" class="create-group">{{ item.content.msgFields }}</div>

            <div v-else class="msg-wrap">
              <!-- 群聊成员头像 -->
              <div v-if="imStore.currentConversation.conversationType == 3 && item.messageDirection == 2" @click="avatarClickHandle(getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId))">
                <im-avatar class="avatar" :user="getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId)"></im-avatar>
              </div>

              <!-- 单聊 -->
              <div v-else-if="item.messageDirection == 2" @click="avatarClickHandle()">
                <im-avatar class="avatar" :user="imStore.currentConversation.sessionData"></im-avatar>
              </div>
              <!-- 自己 -->
              <im-avatar v-if="item.messageDirection == 1" class="avatar" :user="userStore.state.user"></im-avatar>

              <div class="msg-content">
                <!-- 群聊成员昵称 -->
                <div class="nickname" v-if="imStore.currentConversation.conversationType == 3 && item.messageDirection == 2">
                  <span>
                    <span v-if="getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId).imRole != 1">
                      {{ imStore.currentConversation.sessionData.type == 4 ? (item.senderUserId.includes(imStore.orderBuyerId) ? "买家-" : "卖家-") : !item.senderUserId.includes(imStore.orderBuyerId) ? "买家-" : "卖家-" }}
                    </span>
                    {{ getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId).nickname }}
                    <el-tag v-if="getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId).imRole != 0" :type="getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId).imRole == 1 ? 'primary' : 'warning'" size="small">{{
                      getUserInfoByImUid(imStore.currentConversation.sessionData.userVos, item.senderUserId).imRole == 1 ? "客服" : "认证商户"
                    }}</el-tag>
                  </span>
                </div>

                <!-- 文本 -->
                <div class="msg-text" v-if="item.messageType == 'RC:TxtMsg'">
                  <a v-if="item.content.extra && item.content.extra.type == 'link'" :href="item.content.content" target="_blank">{{ item.content.content }}</a>
                  <span v-else v-html="item.content.content"></span>
                </div>

                <!-- 订单流程 -->
                <div class="msg-text order-flow" v-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:orderFlow'">
                  <div class="flow-title">{{ item.content.msgFields.name }}</div>
                  <div>{{ item.content.msgFields.content }}</div>

                  <div class="confirmed-btn" v-if="!userStore.state.isPlatform && imStore.orderTransactionProgress && imStore.orderTransactionProgress['sort-' + item.content.msgFields.sort] && !imStore.orderTransactionProgress['sort-' + item.content.msgFields.sort].includes(imStore.imUid)">
                    <!-- 交易流程确认 -->

                    <el-button v-if="item.content.msgFields.sort == 1" type="primary" @click="flowConfirmHandle(item.content.msgFields.sort)"> 确认 </el-button>
                    <!-- 卖家提供账密 -->
                    <el-button v-if="item.content.msgFields.sort == 2 && isSeller(item) && !userStore.state.isPlatform" type="primary" @click="provideInformation(item.content.msgFields.sort)"> 提供资料 </el-button>

                    <!-- 买家验号确认 -->
                    <el-button v-if="item.content.msgFields.sort == 3 && !isSeller(item)" type="primary" @click="flowConfirmHandle(item.content.msgFields.sort)"> 我已确认 </el-button>

                    <!-- 卖家提供二次实名确认 -->
                    <el-button v-if="item.content.msgFields.sort == 4 && isSeller(item)" type="primary" @click="flowConfirmHandle(item.content.msgFields.sort)"> 我已截图 </el-button>
                    <!-- 买家换绑完成确认 -->
                    <el-button v-if="item.content.msgFields.sort == 5 && !isSeller(item)" type="primary" @click="flowConfirmHandle(item.content.msgFields.sort)"> 全部换绑完成 </el-button>
                  </div>
                </div>

                <!-- 图片 -->
                <div class="msg-file" v-else-if="item.messageType == 'RC:ImgMsg'">
                  <el-image class="image" :src="base64Prefix(item.content, 'image')" :preview-src-list="[base64Prefix(item.content, 'image')]" />
                </div>

                <!-- 视频 -->
                <div class="msg-file" v-else-if="item.messageType == 'RC:FileMsg'">
                  <video :src="item.content.fileUrl" :id="item.messageUId"></video>
                  <div class="video-mask flx-center" @click="playVideo(item.content.fileUrl)">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </div>

                <!-- 商品 -->
                <div class="shop" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:shop'" @click="goToShop(item.content.msgFields)">
                  <div class="title">
                    <span class="text">商品编号：{{ item.content.msgFields.code }}</span>
                    <el-text type="primary" class="pointer" @click.stop="" v-copy="item.content.msgFields.code">复制</el-text>
                  </div>
                  <div class="shop-info flx">
                    <img class="shop-img" :src="item.content.msgFields.log" />
                    <div class="shop-info-right">
                      <div class="mle shop-title">{{ item.content.msgFields.title }}</div>
                      <div class="price-wrap flx-justify-between">
                        <div class="price">￥{{ item.content.msgFields.price }}</div>
                        <el-tag type="primary" size="small">{{ item.content.msgFields.compensation == 0 ? "不可包赔" : item.content.compensation == 1 ? "可买包赔" : "永久包赔" }}</el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 图片 -->
                <div class="shop" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:image'">
                  <el-image class="image" :src="item.content.msgFields.url" :preview-src-list="item.content.msgFields.url" />
                </div>

                <!-- 订单 -->
                <div class="shop order" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:order'">
                  <div class="title flx">
                    <span class="text sle flx-1">订单编号：{{ item.content.msgFields.sn || item.content.msgFields.saleOrder.sn }}</span>
                    <el-text type="primary" class="pointer" v-copy="item.content.msgFields.sn || item.content.msgFields.saleOrder.sn">复制</el-text>
                  </div>
                  <div class="shop-info flx" @click="clickOrderHandler(item.content)">
                    <img v-if="item.content.msgFields.shopVo && item.content.msgFields.shopVo.log" class="shop-img" :src="item.content.msgFields.shopVo.log" />
                    <div class="shop-info-right">
                      <div class="mle shop-title" v-if="item.content.msgFields.shopVo && item.content.msgFields.shopVo.title">{{ item.content.msgFields.shopVo.title }}</div>
                      <div class="price-wrap flx-justify-between">
                        <div class="price">￥{{ item.content.msgFields.dealAmount }}</div>
                        <el-tag v-if="item.content.msgFields.shopVo" type="primary" size="small">{{ item.content.msgFields.shopVo.compensation == 0 ? "不可包赔" : item.content.msgFields.shopVo.compensation == 1 ? "可买包赔" : "永久包赔" }}</el-tag>
                      </div>
                      <div class="order-bottom">
                        <div class="order-text">
                          <div class="text">创建时间：</div>
                          <div class="text-success">{{ item.content.msgFields.createDate }}</div>
                        </div>
                        <div class="order-text">
                          <div class="text">游戏名称：</div>
                          <div class="text-success">{{ item.content.msgFields.gameName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 收集包赔消息 -->
                <a class="shop prohibited order" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:collect'" :href="item.content.msgFields.content" target="_blank" :class="{ lifted: isSeller(item) && !userStore.state.isPlatform }">
                  <div class="title flx">{{ imStore.currentConversation.conversationType == 3 ? "请卖家填写" : "" }}收集包赔信息</div>
                  <div class="desc mle">{{ item.content.msgFields.content }}</div>
                </a>

                <!-- 签署合同消息 -->
                <a class="shop prohibited order" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:contract'" :href="item.content.msgFields.content" target="_blank" :class="{ lifted: isSeller(item) && !userStore.state.isPlatform }">
                  <div class="title flx">{{ imStore.currentConversation.conversationType == 3 ? "请卖家填写" : "" }}签署合同</div>
                  <div class="desc mle">{{ item.content.msgFields.content }}</div>
                </a>

                <!-- 入群邀请消息 -->
                <a class="shop lifted order" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:qq'" :href="item.content.msgFields.content" target="_blank">
                  <div class="title flx">QQ入群邀请</div>
                  <div class="desc mle">{{ item.content.msgFields.content }}</div>
                </a>

                <!-- 单聊 -->
                <div class="read" v-if="item.messageDirection == 1 && imStore.currentConversation.conversationType == 1">
                  <span v-if="firstUnreadMessageTime && firstUnreadMessageTime >= item.sentTime">已读</span>
                  <el-text class="msg-read" v-else>未读</el-text>
                </div>

                <!-- 群聊 -->
                <div class="read" v-if="item.messageDirection == 1 && imStore.currentConversation.conversationType == 3 && imStore.currentConversation.sessionData.userVos && item.messageType != 'CU:orderFlow'">
                  <el-text class="msg-read pointer" v-if="!item.readReceiptInfo || (item.readReceiptInfo.readerList && !item.readReceiptInfo.readerList.length)" @click="recipientsHandle([], imStore.currentConversation.sessionData.userVos)"> 全部未读 </el-text>
                  <div v-else-if="item.readReceiptInfo.readerList.length == imStore.currentConversation.sessionData.userVos.length - 1">全部已读</div>
                  <div v-else class="read">
                    {{ item.readReceiptInfo.readerList.length }}人已读
                    <el-text class="msg-read pointer" @click="recipientsHandle(item.readReceiptInfo.readerList, imStore.currentConversation.sessionData.userVos)">{{ imStore.currentConversation.sessionData.userVos.length - item.readReceiptInfo.readerList.length - 1 }}人未读</el-text>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>

      <div class="input-box flx-column">
        <div class="input-top-tool">
          <!-- 禁言中 群主不禁言 -->
          <template v-if="imStore.currentConversation.sessionData && imStore.currentConversation.sessionData.stat == 1 && groupOwner != imStore.imUid">
            <div class="icon emoji"></div>
            <div class="icon image"></div>
            <div class="icon file"></div>
          </template>

          <template v-else>
            <div class="icon emoji" @click="showEmoji = !showEmoji"></div>
            <div class="icon image">
              <div class="upload">
                <file-upload accept="image/*" ref="imgUploadRef" @uploadSuccess="imageUploadSuccess" @beforeUpload="beforeUploadHandle"></file-upload>
              </div>
            </div>
            <div class="icon file">
              <div class="upload">
                <file-upload accept=".mp4" ref="fileUploadRef" @uploadSuccess="fileUploadSuccess" @beforeUpload="beforeUploadHandle"></file-upload>
              </div>
            </div>
          </template>

          <!-- imStore.currentConversation.sessionData.imRole != 1 客服和客服之间会话 不显示交易相关按钮 -->
          <!-- 租户 只能创建回收订单 -->
          <div v-if="!userStore.state.isPlatform && imStore.currentConversation.conversationType == 1 && imStore.currentConversation.sessionData.imRole != 1" class="tool-btn create-order" @click="createPurchaseOrderHandle">创建回收订单</div>

          <el-dropdown v-if="userStore.state.isPlatform && imStore.currentConversation.conversationType == 1 && imStore.currentConversation.sessionData.imRole != 1" placement="top" @command="createOrder">
            <div class="tool-btn create-order">创建订单</div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="purchase">创建回收订单</el-dropdown-item>
                <el-dropdown-item command="sale">创建销售订单</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 4回收群  1交易群 -->
          <template v-if="imStore.currentConversation.sessionData.imRole != 1">
            <!-- <div v-if="userStore.state.isPlatform" class="tool-btn send-contract" @click="signContractHandle">发送合同</div> -->
            <div v-if="userStore.state.isPlatform" class="tool-btn collected" @click="collectHandle">收集包赔</div>

            <el-dropdown placement="top" @command="shareQQ" v-if="userStore.state.isPlatform">
              <div class="tool-btn qq">发送QQ群链接</div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="item in groupList" :key="item.id" :command="item.groupUrl">{{ item.groupName }}({{ item.groupCode }})</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </div>

        <div class="input-textarea">
          <div class="gag" v-if="imStore.currentConversation.sessionData && imStore.currentConversation.sessionData.stat == 1 && groupOwner != imStore.imUid">全员禁言中，请联系客服</div>

          <at v-else :members="members" v-model:value="inputContent" name-key="nickname">
            <template #item="{ item }">
              <div class="at-item" @click="atClickHandle(item)">
                <el-image class="at-item-img" :src="item.headUrl"></el-image>
                <div class="at-item-name">{{ item.nickname }}</div>
              </div>
            </template>
            <div id="chatContenteditable" ref="textarea" :contenteditable="true" class="msg-input" @blur="blurEvent()" @keydown.enter="keyDown"></div>
          </at>
          <!-- <at-ta :members="members">
                        <textarea v-model="inputContent"></textarea>
                    </at-ta> -->

          <!-- <el-input 
                        v-else
                        v-model="inputContent"
                        type="textarea" 
                        maxlength="1000" 
                        show-word-limit 
                        placeholder="请输入内容"
                        @blur="blurEvent"
                        @keydown.enter.native="keyDown"
                    ></el-input>  -->
        </div>
        <div class="send-btn">
          <span class="text">Enter 发送，Ctrl+Enter换行</span>
          <el-button type="primary" :disabled="imStore.currentConversation.sessionData && imStore.currentConversation.sessionData.stat == 1 && groupOwner != imStore.imUid" @click="sendHandle('TextMessage', inputContent, imStore.currentConversation.targetId)"> 发送 </el-button>
        </div>

        <!-- emoji -->
        <emoji :showEmoji="showEmoji" @selectEmoji="selectEmoji"></emoji>
      </div>
    </div>

    <!-- 群订单 -->
    <right-order v-if="imStore.currentConversation.sessionData && (imStore.currentConversation.sessionData.type == '1' || imStore.currentConversation.sessionData.type == '4')"></right-order>

    <!-- 单聊订单 -->
    <single-chat-order v-if="showSingleChatOrder" ref="singleChatOrderRef"></single-chat-order>

    <!-- 名片 -->
    <business-card ref="businessCardRef"></business-card>

    <!-- 消息接收人列表 -->
    <message-recipient-list ref="messageRecipientListRef"></message-recipient-list>

    <!-- 群聊管理 -->
    <group-management v-if="showGroupManage" ref="groupManagementRef" @close="showGroupManage = false"></group-management>

    <!-- 创建回收订单 -->
    <create-purchase-order ref="createPurchaseOrderRef" :key="createPurchaseOrderKey" @sendOrder="sendOrder"></create-purchase-order>

    <!-- 创建销售订单 -->
    <create-sale-order ref="createSaleOrderRef" :key="createSaleOrderKey" @sendOrder="sendOrder"></create-sale-order>

    <!-- 交易账密提交 -->
    <account-password-form ref="accountPasswordFormRef" :key="accountPasswordFormKey" @accountPasswordSubmit="accountPasswordSubmit"></account-password-form>

    <!-- 视频预览 -->
    <el-dialog width="50%" v-model="showVideoPreview" @close="closeVideoPreview">
      <video style="width: 100%" :src="videoSrc" controls ref="videoRef"></video>
    </el-dialog>

    <!-- 签署合同 -->
    <sign-contract ref="signContractRef" @refreshdata="sendContractLink"></sign-contract>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch, defineEmits, reactive, onMounted, computed } from "vue";
import { ElMessage, ElScrollbar } from "element-plus";
import { VideoPlay } from "@element-plus/icons-vue";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { checkPermission } from "@/utils/utils";
import {
  getHistoryMessages,
  sendReadReceiptMessage,
  clearUnreadCount,
  sendMessage,
  timeStringAutoShort,
  getFirstUnreadMessage,
  getConversationReadMessageTime,
  setConversationReadMessageTime,
  sendReadReceiptResponse,
  getUserInfoByImUid,
  getGroupDetail,
  timeDiff,
  getGroupInfo,
  base64Prefix,
  sendReadReceiptRequest
} from "@/utils/imTool";
import BusinessCard from "./BusinessCard.vue";
import MessageRecipientList from "./MessageRecipientList.vue";
import RightOrder from "./RightOrder.vue";
import SingleChatOrder from "./SingleChatOrder.vue";
import GroupManagement from "./GroupManagement.vue";
import CreatePurchaseOrder from "./CreatePurchaseOrder.vue";
import CreateSaleOrder from "./CreateSaleOrder.vue";
import Emoji from "./Emoji.vue";
import FileUpload from "./FileUpload.vue";
import GroupAvatar from "./GroupAvatar.vue";
import GroupAt from "./GroupAt.vue";
import AccountPasswordForm from "./AccountPasswordForm.vue";
import SignContract from "@/views/order/components/SignContract.vue";
import dayjs from "dayjs";
import baseService from "@/service/baseService";
import At from "vue-at"; // for content-editable
import AtTa from "vue-at/dist/vue-at-textarea";
import axios from "axios";
import ImAvatar from "./ImAvatar.vue";

var sel, range;
var textContent;

const userStore = useAppStore();
const imStore = useImStore();
const settingStore = useSettingStore();
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const emits = defineEmits(["updateConversation"]);
const imgUploadRef = ref();
const fileUploadRef = ref();
onMounted(() => {
  document.getElementById("chatContenteditable").addEventListener("paste", pasteFn);
});

const pasteFn = (e) => {
  if (e.clipboardData.files && e.clipboardData.files.length) {
    return;
  }
  // 先阻止默认的复制事件
  e.preventDefault();
  // 获取复制板的内容
  const paste = (e.clipboardData || window.clipboardData).getData("text/plain");
  // // 新建元素标签
  var newNode = document.createElement("span");
  newNode.innerHTML = paste;
  // 获取当前光标位置，插入元素
  window.getSelection().getRangeAt(0).insertNode(newNode);
};

// 输入框内容
const inputContent = ref("");

// 群成员
const members = ref([]);

// 消息列表
const msgList = ref(<any>[]);

// 单聊销售订单详情
const showSingleChatOrder = ref(false);

// 获取消息列表
const msgHasMore = ref(true);
// 第一条未读消息时间
const firstUnreadMessageTime = ref(0);
const getMessages = (timestamp = 0) => {
  getHistoryMessages(imStore.currentConversation.targetId, timestamp, imStore.currentConversation.conversationType).then((res: any) => {
    let { list, hasMore } = res;
    // 是否还有分页消息
    msgHasMore.value = hasMore;

    if (!list || !list.length) return;
    msgList.value = timeDiff([...list, ...msgList.value]);

    console.log(msgList.value, "消息列表");

    // 第一次获取消息列表 滚动到底部
    if (!timestamp) {
      let lastMessage = list[list.length - 1];
      scrollBarToBottom();
      readMessageHandle(lastMessage);
      sendReadReceiptResponse(imStore.currentConversation.targetId);
      firstUnreadMessageTime.value = getConversationReadMessageTime(imStore.currentConversation.targetId) || lastMessage.sentTime;
      setConversationReadMessageTime(imStore.currentConversation.targetId, firstUnreadMessageTime.value);
      getFirstUnreadMessage(imStore.currentConversation.targetId, imStore.currentConversation.conversationType).then((res: any) => {
        if (res && res.sentTime) {
          firstUnreadMessageTime.value = res.sentTime;
          setConversationReadMessageTime(imStore.currentConversation.targetId, firstUnreadMessageTime.value);
          console.log(res, "获取到未读消息");
        }
      });

      initiateAReadReceiptRequest(lastMessage, list);
    } else {
      scrollToSpecifiedPosition(list.length - 1);
    }
  });
};

// 发起已读回执请求
// 交易群  && 最后一条消息 是服务器自动发送了自定义消息  && 当前人发送的 && 消息已读人员列表为空
const initiateAReadReceiptRequest = (lastMessage: any, list: any) => {
  if (lastMessage.conversationType != 3) return;
  list.map((item: any) => {
    if ((item.content.msgType === "CU:flow" || item.content.msgType === "CU:orderFlow") && !item.readReceiptInfo?.readerList?.length && item.senderUserId == imStore.imUid) {
      sendReadReceiptRequest(lastMessage.targetId, item.messageUId);
    }
  });
};

// 滚动条滚到最底部
const scrollBarToBottom = () => {
  nextTick(() => {
    scrollbarRef.value!.setScrollTop(scrollbarRef.value!.wrapRef.scrollHeight);
    setTimeout(() => {
      scrollbarRef.value!.setScrollTop(scrollbarRef.value!.wrapRef.scrollHeight);
    }, 100);
  });
};

// 滚动到指定位置
const scrollToSpecifiedPosition = (index: number) => {
  nextTick(() => {
    let offsetTop = document.getElementsByClassName("msg-item")[index].offsetTop;
    scrollbarRef.value!.setScrollTop(offsetTop);
  });
};

// 滚动到顶部
const handleReachTop = (e) => {
  if (e.scrollTop == 0 && msgHasMore.value && msgList.value.length) {
    getMessages(msgList.value[0].sentTime);
  }
};

// 发送已读消息
const readMessageHandle = (msg: any) => {
  if (msg.senderUserId == imStore.imUid) return;
  let { messageUId, sentTime } = msg;
  // 单聊
  if (imStore.currentConversation.conversationType == 1) {
    sendReadReceiptMessage(imStore.currentConversation.targetId, messageUId, sentTime);
  } else if (imStore.currentConversation.conversationType == 3) {
    // 群聊
    // sendReadReceiptResponse(imStore.currentConversation.targetId);
  }
  // clearUnreadCount(imStore.currentConversation.targetId, imStore.currentConversation.conversationType).then(res => {
  //     emits('updateConversation', { type: 'conversationClearUnreadCount' });
  // })
};

// 点击头像 查看名片
const businessCardRef = ref();
const avatarClickHandle = (info?: any) => {
  businessCardRef.value.init(info || imStore.currentConversation.sessionData);
};
// 点击群头像 查看群管理
const showGroupManage = ref(false);
const groupManagementRef = ref();
const groupManageHandle = async () => {
  showGroupManage.value = true;
  await nextTick();
  groupManagementRef.value.init(imStore.currentConversation);
};

// 点击 未读人数 查看接收人列表
const messageRecipientListRef = ref();
const recipientsHandle = (readerList: any, userVos: any) => {
  messageRecipientListRef.value.init(readerList, userVos);
};

// 创建回收订单
const createPurchaseOrderKey = ref(0);
const createPurchaseOrderRef = ref();
const createPurchaseOrderHandle = async () => {
  createPurchaseOrderKey.value++;
  await nextTick();
  createPurchaseOrderRef.value.init(imStore.currentConversation);
};

// 创建销售订单
const createSaleOrderRef = ref();
const createSaleOrderKey = ref(0);
const createSaleOrderHandle = async () => {
  createSaleOrderKey.value++;
  await nextTick();
  createSaleOrderRef.value.init(msgList.value, imStore.currentConversation.sessionData);
};

// 创建订单
const createOrder = (type: string) => {
  // 回收订单
  if (type == "purchase") {
    createPurchaseOrderHandle();
  } else {
    // 销售订单
    createSaleOrderHandle();
  }
};

// 是否显示emoji
const showEmoji = ref(false);
// 选择emoji
const selectEmoji = (val: any) => {
  showEmoji.value = false;

  if (inputSelectionStart.value == -1) {
    return (inputContent.value += val);
  }
  let index = inputSelectionStart.value;
  let str = inputContent.value;
  inputContent.value = str.slice(0, index) + val + str.slice(index);
  inputSelectionStart.value = -1;
};

const inputSelectionStart = ref(-1);
const blurEvent = () => {
  sel = window.getSelection();
  inputSelectionStart.value = sel.getRangeAt(0).startOffset;
};

// 图片上传成功
const imageUploadSuccess = (val: any) => {
  sendHandle("ImageMessage", val.src, val.targetId);
};

// 视频上传成功
const fileUploadSuccess = (val: any) => {
  sendHandle("FileMessage", val.src, val.targetId);
};

const beforeUploadHandle = () => {
  nextTick(() => {
    imgUploadRef.value.initeTarget(imStore.currentConversation.targetId);
    fileUploadRef.value.initeTarget(imStore.currentConversation.targetId);
  });
};

// 入群链接
const shareQQ = (val: any) => {
  if (typeof val != "string") {
    ElMessage.warning("请先配置入群邀请链接！");
    return;
  }
  sendHandle("qqMessage", val, imStore.currentConversation.targetId);
};

// 发送收集包赔链接
const collectHandle = () => {
  // 群聊
  if (imStore.currentConversation.conversationType == "3") {
    // type 4回收群  1交易群
    let orderType = imStore.currentConversation.sessionData.type;

    // collectType：0回收订单   1销售订单；
    sendHandle("CollectMessage", `${settingStore.info.websiteUrl}compensation?userId=&orderId=${imStore.orderInfo.id}&collectType=${orderType == 4 ? 0 : 1}`, imStore.currentConversation.targetId);
    return;
  }

  // 单聊
  let orderId = "";
  let orderType = "";
  msgList.value.map((item) => {
    if (item.content.msgType == "CU:order") {
      orderId = item.content.msgFields.id;
      orderType = item.content.msgFields.orderType;
    }
  });
  sendHandle("CollectMessage", `${settingStore.info.websiteUrl}compensation?userId=&orderId=${orderId}&collectType=${orderType == "purchase" ? 0 : 1}`, imStore.currentConversation.targetId);
};

// 发送合同链接
const sendContractLink = (link: string) => {
  sendHandle("ContractMessage", link, imStore.currentConversation.targetId);
};

// 输入框回车
const keyDown = (e: any) => {
  sel = window.getSelection();
  inputSelectionStart.value = sel.getRangeAt(0).startOffset;
  if (e.ctrlKey && e.keyCode == 13) {
    //用户点击了ctrl+enter触发
    inputContent.value += "<br/><br/>";
    nextTick(() => {
      textareaRange();
    });
  } else {
    sendHandle("TextMessage", inputContent.value, imStore.currentConversation.targetId);
    e.preventDefault();
  }
};

//换行并重新定位光标位置
const textarea = ref();
const textareaRange = () => {
  var o = document.getElementById("chatContenteditable").lastChild;
  var textbox = document.getElementById("chatContenteditable");
  var sel = window.getSelection();
  var range = document.createRange();
  range.selectNodeContents(textbox);
  range.collapse(false);
  range.setEndAfter(o); //
  range.setStartAfter(o); //
  sel.removeAllRanges();
  sel.addRange(range);
};

// 发送消息
const sendHandle = (msgType = "TextMessage", content = "", targetId: string, type?: string) => {
  let str = content ? JSON.parse(JSON.stringify(content)) : "";
  // str = str.replace(/[\r\n]/g,"").replace(/^\s*|\s*$/g, '');

  let imgReg = /<img.*?(?:>|\/>)/gi; //匹配图片中的img标签
  let srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i; // 匹配图片中的src
  let arr = str.match(imgReg); //筛选出所有的img
  let srcArr = [];
  if (arr && arr.length) {
    for (let i = 0; i < arr.length; i++) {
      let src = arr[i].match(srcReg);
      // 获取图片地址
      srcArr.push(src[1]);
      base64ToImg(src[1]);
    }
  }

  //js字符替换将<br>替换成/n
  str = str.replace(/<br\s*\/?>/g, "\n");

  // 过滤html标签和空格
  str = str.replace(/<\/?.+?\/?>/g, "").replace(/&nbsp;/gi, "");
  if (!str || !str.replace(/[\r\n]/g, "")) return;

  // 群聊@人员
  let atMembers = Object.keys(atMemberList.value);
  let atMemberIds: any = [];
  atMembers.map((item) => {
    if (inputContent.value.indexOf(item) > -1) {
      atMemberIds.push(atMemberList.value[item]);
    }
  });

  let mentionedInfo = {};
  if (atMemberIds.length) {
    mentionedInfo = {
      type: 2,
      userIdList: atMemberIds,
      mentionedContent: ""
    };
  }

  imMessageSend(msgType, str, mentionedInfo, type, targetId);
};

const imMessageSend = (msgType: string, str: string, mentionedInfo: any, type?: string, targetId: string) => {
  sendMessage({
    conversationType: imStore.currentConversation.conversationType,
    msgType: msgType,
    targetId: targetId,
    content: str,
    extra: { type },
    mentionedInfo: mentionedInfo
  }).then((res: any) => {
    if (targetId == imStore.currentConversation.targetId) {
      msgList.value.push(res);
    }
    msgList.value = timeDiff(msgList.value);
    scrollBarToBottom();
    inputContent.value = "";
    atMemberList.value = {};
    emits("updateConversation", {
      type: "sendMessage",
      content: res,
      targetId: targetId
    });
  });
};

// 消息中有图片 base64上传之后 在发送图片消息
const base64ToImg = async (base64: string) => {
  const blob = base64ToFile(base64, "截图-" + new Date().getTime() + ".png");
  baseService.post("/sys/oss/upload", { file: blob }, { "Content-Type": "multipart/form-data" }).then((res) => {
    if (res.code == 0) {
      sendHandle("ImageMessage", res.data.src, imStore.currentConversation.targetId);
    }
  });
};

// base64 转 Blob
const base64ToFile = (base64: string, fileName: string) => {
  let arr = base64.split(","),
    type = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type });
};

// 发送订单消息
const sendOrder = (content: any) => {
  imMessageSend("OrderMessage", content, {}, "", imStore.currentConversation.targetId);
};

// 播放视频
const videoSrc = ref("");
const showVideoPreview = ref(false);
const videoRef = ref();
const playVideo = async (src: string) => {
  videoSrc.value = src;
  showVideoPreview.value = true;
  await nextTick();
  videoRef.value.play();
};

const closeVideoPreview = () => {
  videoSrc.value = "";
  showVideoPreview.value = false;
};

// 流程确认
const flowConfirmHandle = (sort: number) => {
  baseService
    .post("/api/im/flow/submit", {
      imUid: imStore.imUid,
      groupId: imStore.currentConversation.targetId,
      sort: sort
    })
    .then((res) => {
      if (res.code == 0) {
        imStore.orderTransactionProgress["sort-" + sort] = [imStore.imUid];
        ElMessage.success("确认成功");
      }
    });
};

// 点击@人员
const atMemberList = ref(<any>{});
const atClickHandle = (item: any) => {
  atMemberList.value[`@${item.nickname}`] = item.imUid;
};

// 账密表单
const accountPasswordFormRef = ref();
const accountPasswordFormKey = ref(0);
const provideInformation = async () => {
  accountPasswordFormKey.value++;
  await nextTick();
  accountPasswordFormRef.value.init();
};

// 发送账密消息
const accountPasswordSubmit = (data: any) => {
  let content = `游戏账号： ${data.gameAccount}\n游戏密码： ${data.gamePassword}\n手机号： ${data.phone}`;
  sendHandle("TextMessage", content, imStore.currentConversation.targetId);
  setTimeout(() => {
    flowConfirmHandle(2);
  }, 500);
};
// 获取Q群
const groupList = ref();
const getQ = () => {
  baseService.get("/group/sysqqgroup/page", { limit: 9999, page: 1 }).then((res) => {
    if (imStore.currentConversation.conversationType != 1) {
      baseService.get("/api/im/query/orderFlow?groupId=" + imStore.currentConversation.targetId).then((res_) => {
        groupList.value = res.data.list;
        groupList.value = groupList.value.filter((ele) => {
          if (res_.data.purchaseVo && ele.gameName == res_.data.purchaseVo.gameName) {
            return ele;
          } else if (res_.data.game && ele.gameName == res_.data.game.title) {
            return ele;
          }
        });
      });
    } else {
      groupList.value = res.data.list;
    }
  });
};
// 群主id
const groupOwner = ref("");

// 当前会话
watch(
  () => imStore.currentConversation,
  (newVal, oldVal) => {
    getQ();
    if (imStore.currentConversation && imStore.currentConversation.targetId && newVal.targetId != oldVal?.targetId) {
      showSingleChatOrder.value = false;
      msgList.value = [];
      getMessages();
      inputContent.value = "";
      atMemberList.value = {};
      members.value = [];

      if (imStore.currentConversation.conversationType == 3 && imStore.currentConversation.sessionData) {
        // 获取群资料
        try {
          getGroupDetail(imStore.currentConversation.targetId).then((res: any) => {
            if (imStore.currentConversation.conversationType == 3 && imStore.currentConversation.targetId == res.groupId) {
              imStore.currentConversation.sessionData = res;
              members.value = res.userVos;
            }
          });
        } catch (error) {}

        // 获取群详情 群主

        getGroupInfo(imStore.currentConversation.targetId).then((res) => {
          groupOwner.value = res.data[0].ownerId;
        });
      }
    }
  },
  { deep: true, immediate: true }
);

// 收到新消息
watch(
  () => imStore.receivedNewsMessge,
  (newVal) => {
    if (newVal.targetId != imStore.currentConversation.targetId) return;
    msgList.value.push(newVal);
    msgList.value = timeDiff(msgList.value);
    readMessageHandle(newVal);
    scrollBarToBottom();

    // 更新群信息
    if (newVal.content.msgType == "CU:uploadGroup") {
      getGroupDetail(imStore.currentConversation.targetId).then((res: any) => {
        imStore.currentConversation.sessionData = res;
      });
    }

    // 更新群信息, 创建群信息, 订单流程消息, 更新订单状态信息
    let messageTypeList = ["CU:uploadGroup", "CU:createGroup", "CU:flow", "CU:orderFlow"];
    if (messageTypeList.includes(newVal.content?.msgType)) {
      clearUnreadCount(newVal.targetId, imStore.currentConversation.conversationType);
    }
  }
);

// 收到已读消息
watch(
  () => imStore.receivedReadMessage,
  (newVal) => {
    if (newVal.conversation.targetId != imStore.currentConversation.targetId) return;
    setConversationReadMessageTime(imStore.currentConversation.targetId, msgList.value[msgList.value.length - 1].sentTime);
    firstUnreadMessageTime.value = msgList.value[msgList.value.length - 1].sentTime;
  }
);

// 群聊 收到群消息回执请求  收到该请求 需要 发送群消息回执响应
watch(
  () => imStore.groupMessageReceiptRequest,
  (newVal) => {
    if (newVal.conversation.targetId != imStore.currentConversation.targetId) return;
    sendReadReceiptResponse(imStore.currentConversation.targetId);
  }
);

// 群聊收到已读消息
watch(
  () => imStore.groupReceivedReadMessage,
  (newVal) => {
    if (newVal.conversation.targetId != imStore.currentConversation.targetId) return;
    // console.log(newVal, '收到群消息回执响应')
    msgList.value.map((item: any) => {
      if (!item.readReceiptInfo) item.readReceiptInfo = { readerList: [newVal.receivedUserId] };
      item.readReceiptInfo.readerList = [...new Set([...item.readReceiptInfo.readerList, newVal.receivedUserId])];
    });
  }
);

// 单聊消息订单详情
const singleChatOrderRef = ref();

// 点击订单
const clickOrderHandler = async (content: any) => {
  // 单聊 销售订单
  if (content.orderType == "sales") {
    showSingleChatOrder.value = true;
    await nextTick();
    singleChatOrderRef.value.init(content);
  }
};

// 签署合同
const signContractRef = ref();
const signContractHandle = async () => {
  let phone = "";
  let orderId = "";
  let orderType = "";
  if (imStore.currentConversation.conversationType == 3) {
    if (!imStore.orderInfo.id) return;
    let sellersPhone = "";
    imStore.currentConversation.sessionData.userVos.map((item: any) => {
      if (item.id == imStore.orderBuyerId && item.imRole != 1) {
        sellersPhone = item.mobile;
      }
    });

    phone = imStore.orderInfo.contractRealnamePhone || imStore.orderInfo.customerPhone || sellersPhone;
    orderId = imStore.orderInfo.id;
    // type 4回收群  1交易群
    orderType = imStore.currentConversation.sessionData.type;
  } else {
    let order = msgList.value.filter((item: any) => item.content.msgType == "CU:order");
    if (!order || !order.length) return;

    phone = imStore.currentConversation.sessionData.mobile;
    orderId = order[order.length - 1].content.msgFields.id;
    orderType = order[order.length - 1].content.msgFields.orderType == "purchase" ? "4" : "1";
  }
  signContractRef.value.init(
    {
      contractType: orderType == "4" ? "PURCHASE_CONTRACT" : "SALE_CONTRACT",
      orderId: orderId,
      contractRealnamePhone: phone
    },
    "im"
  );
};

// 跳转到商城商品详情页
const goToShop = ({ id, gameId }) => {
  window.open(`${settingStore.info.pcWebsiteUrl}/#/goodsDetails?id=${id}&gameId=${gameId}`, "_blank");
};

// 是否卖家
const isSeller = computed(() => {
  return (item: any) => {
    console.log(imStore.currentConversation.sessionData.type, imStore.imUid, imStore.orderBuyerId);
    return imStore.currentConversation.sessionData.type == 4 ? (imStore.imUid.includes(imStore.orderBuyerId) ? false : true) : !imStore.imUid.includes(imStore.orderBuyerId) ? false : true;
  };
});
</script>

<style lang="scss">
.ny-im-conversation {
  background: #fff;
  display: flex;
  overflow: hidden;

  .conversation-content {
    position: relative;
    overflow: hidden;
  }

  .header {
    height: 92px;
    padding: 0 16px;
    border-bottom: 1px solid #e4e7ed;

    .user {
      cursor: pointer;
      .avatar {
        width: 42px;
        height: 42px;
        border-radius: 4px;
      }

      .nickname {
        font-size: 20px;
        color: #171a1d;
        padding-left: 8px;
      }
    }

    .right-btn {
      font-size: 16px;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
        margin-right: 2px;
      }
    }
  }

  .msg-list-wrap {
    flex: 1;
    overflow-y: auto;

    .msg-list {
      padding: 16px;

      .msg-item {
        width: 100%;
        margin-bottom: 14px;
        display: inline-block;

        .msg-time {
          text-align: center;
          margin-bottom: 14px;
          .time {
            display: inline-block;
            background: #e6e8eb;
            height: 26px;
            line-height: 26px;
            border-radius: 2px;
            padding: 0 4px;
            color: #909399;
          }
        }

        .create-group {
          text-align: center;
          color: #909399;
          font-size: 12px;
        }

        .msg-content {
          float: left;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .nickname {
            display: flex;
            align-items: center;
            padding-bottom: 4px;
          }

          .el-tag {
            margin-left: 4px;
          }
        }

        .avatar {
          width: 36px;
          height: 36px;
          border-radius: 4px;
          float: left;
          margin: 0 12px 0 0;
          cursor: pointer;
        }

        .msg-text {
          float: left;
          padding: 9px 12px;
          line-height: 24px;
          background: #f2f4f7;
          font-size: 16px;
          color: #171a1d;
          max-width: 560px;
          border-radius: 1px 10px 10px 10px;
          overflow: hidden;

          .confirmed-btn {
            text-align: right;

            .el-button {
              margin-top: 8px;
            }
          }

          span {
            white-space: pre-wrap;
            word-wrap: break-word;
            // word-break: break-all;
            // word-wrap: break-word;
          }

          :deep(a),
          a {
            color: var(--el-color-primary);
            cursor: pointer;
          }
        }

        .msg-file {
          max-width: 560px;
          border-radius: 1px 10px 10px 10px;
          overflow: hidden;
          position: relative;

          video,
          .image {
            width: auto;
            max-width: 560px;
          }

          .video-mask {
            color: #fff;
            font-size: 50px;
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background: rgba($color: #000000, $alpha: 0);
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              transform: scale(1.2);
            }
          }
        }

        .shop {
          width: 306px;
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          cursor: pointer;
          color: #000;

          &.prohibited {
            pointer-events: none;
            user-select: none; /* 禁止选择文本 */
            -webkit-user-select: none; /* Safari 和 Chrome 的兼容性 */
            -moz-user-select: none; /* Firefox 的兼容性 */
            -ms-user-select: none; /* IE 和 Edge 的兼容性 */
          }

          &.lifted {
            pointer-events: all;
            user-select: auto;
          }

          .title {
            line-height: 24px;

            .text {
              padding-right: 8px;
            }
          }

          .desc {
            color: #999;
            font-size: 12px;
            padding-top: 8px;
            word-break: break-all;
          }

          .shop-info {
            padding-top: 16px;

            .shop-img {
              width: 80px;
              height: 80px;
              border-radius: 4px;
              margin-right: 10px;
            }

            .shop-info-right {
              flex: 1;

              .shop-title {
                line-height: 24px;
                height: 48px;
              }

              .price-wrap {
                padding-top: 10px;
                height: 20px;
                .price {
                  color: #f44a29;
                }
              }

              .order-bottom {
                .order-text {
                  padding-top: 10px;
                }

                .text {
                  font-size: 12px;
                  color: #909399;
                  padding-bottom: 4px;
                }
              }
            }
          }
        }

        .msg-read {
          color: var(--el-color-primary);
          // border: 1px solid #E4E7ED;
          // border-radius: 4px;
        }

        .read {
          color: #909399;
          text-align: right;
          vertical-align: middle;
        }

        &.my {
          .avatar {
            float: right;
            margin: 0 0 0 12px;
          }

          .msg-content {
            float: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }

          .msg-text {
            background: var(--el-color-primary);
            color: #fff;
            border-radius: 10px 1px 10px 10px;
            a {
              color: #fff;
            }
          }
        }
      }
    }
  }

  .input-box {
    height: 240px;
    padding: 16px;
    border-top: 1px solid #e4e7ed;
    position: relative;

    .input-top-tool {
      display: flex;
      align-items: center;
      cursor: pointer;

      .icon {
        width: 32px;
        height: 32px;
        margin-right: 16px;
        background: url("../../../assets/images/im_emoji.png") center no-repeat;
        background-size: 26px;
        overflow: hidden;

        &.image {
          background: url("../../../assets/images/im_uplod_img.png") center no-repeat;
          background-size: 26px;
        }

        &.file {
          background: url("../../../assets/images/im_upload_file.png") center no-repeat;
          background-size: 26px;
        }

        .upload {
          opacity: 0;
        }
      }

      .tool-btn {
        display: flex;
        align-items: center;
        border-radius: 4px;
        border: 1px solid #cdd0d6;
        color: #171a1d;
        padding: 0 4px;
        font-size: 16px;
        cursor: pointer;
        height: 32px;
        line-height: 32px;
        margin-right: 16px;

        &::before {
          content: "";
          display: inline-block;
          width: 24px;
          height: 24px;
          margin-right: 4px;
        }

        &.create-order::before {
          content: "";
          background: url("../../../assets/images/im_create_order.png") center no-repeat;
          background-size: 20px;
        }

        &.send-contract::before {
          content: "";
          background: url("../../../assets/images/im_send_contract.png") center no-repeat;
          background-size: 20px;
        }

        &.collected::before {
          background: url("../../../assets/images/im_collected.png") center no-repeat;
          background-size: 20px;
        }
        &.qq::before {
          background: url("../../../assets/images/qq_icon.png") center no-repeat;
          background-size: 20px;
        }
      }
    }

    .input-textarea {
      flex: 1;
      padding: 10px 0;
      // overflow: hidden;

      .gag {
        font-size: 16px;
        color: #a8abb2;
        text-align: center;
      }

      .atwho-wrap {
        height: 100%;
      }

      .msg-input {
        box-shadow: none;
        resize: none;
        height: 123px;
        font-size: 16px;
        outline: none;
        overflow-y: auto;
        white-space: pre-wrap;
        // -webkit-user-modify: read-write-plaintext-only;

        :deep(img) {
          max-height: 120px;
          max-width: 120px;
        }

        &:empty::before {
          content: attr(placeholder);
        }
      }

      :deep(.el-textarea__inner) {
        box-shadow: none;
        resize: none;
        height: calc(100% - 20px);
        font-size: 16px;
      }

      .el-textarea {
        height: 100%;
      }
    }
    .send-btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      span.text {
        color: #a8abb2;
        margin-right: 12px;
      }

      .el-button {
        width: 96px;
      }
    }
  }

  .atwho-cur {
    background: var(--el-color-primary);
  }

  .atwho-li {
    padding: 5px 10px;
    box-sizing: content-box;
    cursor: pointer;
  }

  .at-item {
    padding: 10px 0 !important;
    display: flex;
    align-items: center;

    .at-item-img {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      margin-right: 8px;

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
