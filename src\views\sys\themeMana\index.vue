<template>
  <div class="sys__theme">
    <el-card shadow="never" class="rr-view-ctx-card" v-loading="state.dataListLoading">
      <ny-button-group style="width: fit-content" :list="groupList" v-model="currentTypeIndex" @change="handleStatusClick"></ny-button-group>
      <el-row :gutter="24" style="margin-top: 12px">
        <el-col style="margin-bottom: 12px" :span="8" v-if="state.page == 1 && !state.dataListLoading">
          <div class="themeItem">
            <el-image class="img" :src="defaultImg" fit="cover" :preview-src-list="[defaultImg]" />
            <div class="content">
              <div class="title">
                <span>默认主题</span>
                <span class="tag">永久</span>
              </div>
              <div class="tip">采用简洁的设计风格，帮助顾客快速定位商品类别</div>
              <div class="bottom">
                <span v-if="currentTypeIndex != '1'" class="price">免费</span>
                <span v-else>永久</span>
                <!-- 没有其他主题使用时 -->
                <el-button v-if="!activeId || activeId == '0'" style="width: 104px" type="primary">{{ "使用中" }}</el-button>
                <el-button v-else @click.stop="useTheme({ id: 0, functionId: 0, name: '默认主题' })" style="width: 104px" type="primary">立即使用</el-button>
              </div>
            </div>
          </div>
        </el-col>
        <!-- 全部 -->
        <template v-if="currentTypeIndex != '1'">
          <el-col style="margin-bottom: 12px" :span="8" v-for="item in state.dataList" :key="item.id">
            <div class="themeItem">
              <el-image class="img" :src="item.paidFunctionOptions.coverImg" fit="cover" :preview-src-list="item.paidFunctionOptions.infoImgs ? item.paidFunctionOptions.infoImgs : []" />
              <div class="content">
                <div class="title">
                  <span>{{ item.name }}</span>
                  <span :class="item.paidFunctionOptions.price == 0 ? 'tag ' : ' tag monthTag'">{{ item.paidFunctionOptions.price == 0 ? "永久" : "包月" }}</span>
                </div>
                <div class="tip">{{ item.introduce }}</div>
                <div class="bottom">
                  <!-- 全部中显示价格 -->
                  <span class="price"><span v-if="item.paidFunctionOptions.price !== 0" style="font-size: 12px"></span>{{ item.paidFunctionOptions.price === 0 ? "免费" : item.paidFunctionOptions.price }}星币</span>
                  <!-- functionPartner 在使用期中 -->
                  <!-- 使用中 -->
                  <el-button v-if="activeId == item.id" style="width: 104px" type="primary">{{ "使用中" }}</el-button>
                  <template v-else>
                    <!-- 已过期续费 -->
                    <el-button v-if="item.functionPartner && isExpired(item.functionPartner.expireTime)" style="width: 104px" @click.stop="onEditAction(item.id, 'open')" type="primary">{{ "立即续费" }}</el-button>
                    <template v-else>
                      <!-- 免费且未开通。 付费、购买且未开通 -->
                      <el-button @click.stop="useTheme(item)" v-if="(item.paidFunctionOptions.price == 0 || (item.paidFunctionOptions.price != 0 && item.functionPartner)) && activeId != item.id" style="width: 104px" type="primary">{{ "立即使用" }}</el-button>
                      <!-- 不在已开通中、付费且未购买 -->
                      <el-button v-if="item.paidFunctionOptions.price != 0 && !item.functionPartner" @click.stop="onEditAction(item.id, 'open')" style="width: 104px" type="primary">{{ "立即购买" }}</el-button>
                    </template>
                  </template>
                </div>
              </div>
            </div>
          </el-col>
        </template>
        <!-- 开通 -->
        <template v-else>
          <el-col style="margin-bottom: 12px" :span="8" v-for="item in state.dataList" :key="item.id">
            <div class="themeItem">
              <el-image class="img" :src="item.coverImg" fit="cover" :preview-src-list="item.infoImgs ? item.infoImgs : []" />
              <div class="content">
                <div class="title">
                  <span>{{ item.name }}</span>
                  <span :class="item.price == 0 ? 'tag ' : ' tag monthTag'">{{ item.price == 0 ? "永久" : "包月" }}</span>
                </div>
                <div class="tip">{{ item.introduce }}</div>
                <div class="bottom">
                  <!--  已开通中显示到期时间 -->
                  <span>{{ item.price === 0 ? "永久" : `${item.expireTime}到期 ` }}</span>
                  <el-button v-if="isExpired(item.expireTime)" style="width: 104px" @click.stop="onEditAction(item.functionId, 'open')" type="primary">{{ "立即续费" }}</el-button>
                  <template v-else>
                    <el-button v-if="activeId == item.functionId" style="width: 104px" type="primary">{{ "使用中" }}</el-button>
                    <el-button v-else style="width: 104px" type="primary" @click.stop="useTheme(item)">{{ "立即使用" }}</el-button>
                  </template>
                </div>
              </div>
            </div>
          </el-col>
        </template>
      </el-row>
      <!-- <el-pagination :current-page="state.page" :page-size="state.limit" :total="state.total" layout="total, prev, next" :hide-on-single-page="true" @size-change="state.pageSizeChangeHandle" @current-change="pageCurrentChangeHandle"> </el-pagination> -->
      <!-- 开通 -->
      <opened :key="addKey" ref="openedRef" @refreshDataList="state.query"></opened>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, nextTick } from "vue";
import baseService from "@/service/baseService";
import opened from "@/views/plugin/paid/paidfunction-opened.vue";
import { ElNotification, ElMessage, ElMessageBox } from "element-plus";
import defaultImg from "@/assets/images/default.png";
import dayjs from "dayjs";

const view = reactive({
  getDataListURL: "/paid/function/page",
  getDataListIsPage: true,
  limit: 9999,
  dataForm: {
    platform: import.meta.env.VITE_PLATFORM_MODE,
    type: 3
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const groupList = reactive([
  { dictLabel: "全部主题", dictValue: "" },
  { dictLabel: "已开通主题", dictValue: "1" }
]);
const activeId = ref("");
const currentTypeIndex = ref("");
const handleStatusClick = () => {
  state.dataList = [];
  // 已开通功能列表
  view.getDataListURL = currentTypeIndex.value == "" ? "/paid/function/page" : "/paid/function/openedFunctionList";
  if (currentTypeIndex.value == "1") {
    state.dataForm.type = 3;
  } else {
    state.dataForm.type = 3;
  }
  state.getDataList();
};
// 开通
const addKey = ref(0);
const openedRef = ref();
const onEditAction = (id: any, type?: string) => {
  console.log(id, type, "=== sdjflsdjfl =====");

  addKey.value++;
  nextTick(() => {
    openedRef.value.init(id, type);
  });
};
// const pageCurrentChangeHandle = (val: number) => {
//   state.page = val;
//   state.page > 1 ? (state.limit = 6) : (state.limit = 5);
//   state.query();
// };

// 是否到期
const isExpired = (expireTime: any) => {
  return expireTime ? dayjs(expireTime).isBefore(dayjs().format("YYYY-MM-DD HH:mm:ss")) : false;
};
const useTheme = (row?: any) => {
  baseService.post("/skin/sysskin/useImmediately", { functionId: currentTypeIndex.value === "1" ? row.functionId : row.id, functionName: row.name }).then((res) => {
    ElMessage.success("配置成功！");
    state.query();
    useStatus();
  });
};
const useStatus = () => {
  baseService.get("/skin/sysskin/use").then((res) => {
    if (res.code == 0) {
      activeId.value = res.data?.functionId || "";
    }
  });
};
useStatus();
</script>
<style lang="less" scoped>
.sys__theme {
  padding: 12px 0px;
  .themeItem {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    overflow: hidden;
    border: 1px solid #ebeef5;

    .img {
      width: 100%;
      height: 314px;
    }

    .content {
      padding: 20px;

      .title {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 22px;
        margin-bottom: 4px;
      }
      .tag {
        background: #dddcff;
        color: #5654ff;
        border-radius: 2px 2px 2px 2px;
        padding: 0 4px;
        margin-left: 4px;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;

        &.monthTag {
          color: #3366ff;
          background: #daeeff;
        }
      }
      .tip {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 16px;
        margin-bottom: 10px;
      }
      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .price {
          font-weight: bold;
          font-size: 16px;
          color: #f44a29;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
