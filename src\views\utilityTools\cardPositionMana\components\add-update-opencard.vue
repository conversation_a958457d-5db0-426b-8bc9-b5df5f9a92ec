<template>
  <el-drawer v-model="visible" :title="dataForm.id ? '编辑开卡人' : '新增开卡人'" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="944px" class="ny-drawer article-add-or-update">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <div class="card">
        <div class="titleSty">基本信息</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item class-name="noneSelfRight" label="姓名">
            <template #label>
              <span>姓名<span style="color: red">*</span></span>
            </template>
            <el-form-item label="姓名" prop="userName">
              <el-input v-model="dataForm.userName" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="供卡人类型">
            <template #label>
              <span>供卡人类型<span style="color: red">*</span></span>
            </template>
            <el-form-item label="供卡人类型" prop="provideUserType">
              <el-select v-model="dataForm.provideUserType" placeholder="请选择供卡人类型">
                <el-option label="自管理" value="自管理" />
                <el-option label="合作卡主" value="合作卡主" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            <template #label>
              <span>联系方式<span style="color: red">*</span></span>
            </template>
            <el-form-item label="联系方式" prop="contactInformation">
              <el-input v-model="dataForm.contactInformation" placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="微信">
            <el-form-item label="微信" prop="wx">
              <el-input v-model="dataForm.wx" placeholder="请输入微信"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            <el-form-item label="负责人" prop="personInCharge">
              <el-input v-model="dataForm.personInCharge" placeholder="请输入负责人"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="支付账号">
            <el-form-item label="支付账号" prop="alipayAccount">
              <el-input v-model="dataForm.alipayAccount" placeholder="请输入支付账号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="与负责人关系">
            <el-form-item label="与负责人关系" prop="relationship">
              <el-input v-model="dataForm.relationship" placeholder="请输入与负责人关系"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="供卡人状态">
            <el-form-item label="供卡人状态" prop="status">
              <el-select v-model="dataForm.status" placeholder="请选择供卡人状态">
                <el-option label="已注销" :value="0" />
                <el-option label="正常" :value="1" />
                <el-option label="意向注销" :value="2" />
                <el-option label="急需注销" :value="3" />
                <el-option label="失联" :value="4" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="办卡数">
            <el-form-item label="办卡数" prop="cardQuantity">
              <el-input v-model="dataForm.cardQuantity" placeholder="请输入办卡数"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="单卡分成(%)">
            <el-form-item label="单卡分成(%)" prop="singleDivideInto">
              <el-input v-model="dataForm.singleDivideInto" placeholder="请输入单卡分成"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="合计分成(元)">
            <el-form-item label="合计分成(元)" prop="sumDivideInto">
              <el-input v-model="dataForm.sumDivideInto" placeholder="请输入合计分成"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"></el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="card">
        <div class="titleSty">身份信息</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item label="身份证号">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="dataForm.idCard" placeholder="请输入身份证号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="是否是公司员工">
            <el-form-item label="是否是公司员工" prop="companyStaff"> <el-switch inline-prompt v-model="dataForm.companyStaff" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" /> </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="身份证正面">
            <el-form-item label="身份证正面" prop="idCardFront"> <ny-upload v-model:imageUrl="dataForm.idCardFront" :limit="1" :fileSize="5" accept="image/*"></ny-upload> </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="身份证反面">
            <el-form-item label="身份证反面" prop="idCardBack"> <ny-upload v-model:imageUrl="dataForm.idCardBack" :limit="1" :fileSize="5" accept="image/*"></ny-upload></el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="合同" :span="2">
            <el-form-item label="合同" prop="contract">
              <ny-upload-file ossUrl="/sys/oss/upload" :isSelfSty="false" :isDrag="false" :isSimple="true" tip="" v-model:fileSrc="dataForm.contract">
                <template #content>
                  <el-button>上传文件</el-button>
                </template>
              </ny-upload-file>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="承诺利润">
            <el-form-item label="承诺利润" prop="promiseProfit">
              <el-input v-model="dataForm.promiseProfit" placeholder="请输入承诺利润"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-form>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-drawer>
</template>  
      
  <script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { Calendar } from "@element-plus/icons-vue";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";
import { isCardNo } from "@/utils/method";

const { t } = useI18n();
const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);

const validatePhone = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (!/^1[0-9]{10}$/.test(value)) {
    return callback(new Error(t("validate.format", { attr: "手机号码" })));
  }
  callback();
};

// 自定义身份证校验规则
const idCardValidator = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入正确身份证号"));
  } else {
    if (!isCardNo(value)) {
      callback(new Error("身份证号格式错误"));
    } else {
      callback();
    }
  }
};

const rules = {
  userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  provideUserType: [{ required: true, message: "请选择开卡人类型", trigger: "change" }],
  contactInformation: [
    { required: true, message: "请输入联系方式", trigger: "blur" },
    { validator: validatePhone, trigger: "blur" }
  ],
  // idCard: [
  //   { required: true, message: "请输入姓名", trigger: "blur" },
  //   { validator: idCardValidator, trigger: "blur" }
  // ]
};

const init = (id?: number) => {
  visible.value = true;
  // getTenant()

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  if (id) {
    getDetails(id);
  }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get("/mobile/mobileCardIssuer/" + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 租户下拉
const options = ref([]);
const getTenant = () => {
  options.value = [];
  baseService.get("").then((res) => {
    if (res.code === 0) {
      options.value = res.data;
    }
  });
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/mobile/mobileCardIssuer", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
      
    <style lang="scss" scoped>
.card {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }
  :deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;
    tbody {
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;
        .el-descriptions__label {
          display: flex;
          align-items: center;
          font-weight: normal;
          width: 144px;
        }
        .el-descriptions__content {
          display: flex;
          align-items: center;
          min-height: 48px;
          flex: 1;
          > div {
            width: 100%;
          }
          .el-form-item__label {
            display: none;
          }
          .el-form-item {
            margin-bottom: 0;
          }
        }
        .noneSelfRight {
          border-right: 0 !important;
        }
        .noneSelfLeft {
          border-left: 0 !important;
        }
        .noneSelfLabel {
          background: none;
          border-left: 0 !important;
          border-right: 0 !important;
        }
      }
    }
  }
}
</style>