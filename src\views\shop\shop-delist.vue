<template>
  <div>
    <el-drawer v-model="visibleSale" append-to-body title="商品售出确认" :close-on-click-modal="true" :close-on-press-escape="false" class="ny-drawer" size="944">
      <el-form ref="delistFormRef" :model="delistForm" :rules="delistRules" label-position="top" status-icon v-if="delistForm.sold">
        <el-card class="cardDescriptions" style="padding: 0">
          <div class="titleSty">商品出售信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label><span>游戏名称</span></template>
              {{ rows.gameName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>游戏账号</span></template>
              {{ rows.gameAccount }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>商品编码</span></template>
              {{ rows.code }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>零售价(元)</span></template>
              {{ rows.price.toFixed(2) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>出售人<span style="color: red">*</span></span></template
              >
              <el-form-item label="出售人" prop="transactionUser">
                <ny-select-search v-model="delistForm.transactionUser" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="出售人" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>出售渠道<span style="color: red">*</span></span></template
              >
              <el-form-item label="出售渠道" prop="channelId">
                <el-cascader style="width: 100%" :show-all-levels="false" clearable v-model="delistForm.channelId" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择出售渠道" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>买方手机号<span style="color: red">*</span></span></template
              >
              <el-form-item label="买方手机号" prop="buyerPhone">
                <el-input v-model="delistForm.buyerPhone" placeholder="买方手机号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label style="display: flex; align-items: center; justify-content: flex-start">
                成交价(元)<span style="color: red">*</span>
                <el-tooltip effect="dark" content="成交价 = 零售价 + 包赔费" placement="top-end">
                  <img src="/src/assets/icons/svg/question-line.svg" style="width: 14px; height: 14px; margin-left: 4px" />
                </el-tooltip>
              </template>
              <el-form-item label="成交价(元)" prop="transactionPrice" class="tipLabel">
                <el-input type="number" v-model="delistForm.transactionPrice" @input="countInterestRate" @blur="countInterestRate" placeholder="成交价" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>包赔费</span></template
              >
              <el-form-item label="包赔费(元)" prop="guaranteeAmount">
                <el-input type="number" v-model="delistForm.guaranteeAmount" placeholder="包赔费" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item v-if="delistForm.accountSourceName == '平台直售'">
              <template #label
                ><span>是否分期</span></template
              >
              <el-form-item label="是否分期" prop="byStages">
                <el-switch v-model="delistForm.byStages" :active-value="1" :inactive-value="0" inline-prompt active-text="是" inactive-text="否" @change="byStagesChange"/>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel" v-else></el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card class="cardDescriptions" style="padding: 0; margin-top: 12px" v-if="delistForm.byStages == 1">
          <div class="titleSty">分期信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label><span>首付款(元)<span style="color: red">*</span></span></template>
              <el-form-item label="首付款" prop="downPayments">
                <el-input v-model="delistForm.downPayments" placeholder="首付款" @blur="countInterestRate"/>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>分期金额(元)</span></template>
              <el-input v-model="interestRateForm.installmentAmount" disabled/>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>分期期数(个月)<span style="color: red">*</span></span></template>
              <el-form-item label="分期期数" prop="byStagesPeriods">
                <el-input-number v-model="delistForm.byStagesPeriods" :min="1" controls-position="right" placeholder="分期期数" style="width: 100%;" @change="countInterestRate" @blur="countInterestRate"/>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>费率<span style="color: red">*</span></span></template>
              <el-form-item label="费率" prop="byStagesRate">
                <el-input-number v-model="delistForm.byStagesRate" :min="0" :precision="2" :step="0.1" controls-position="right" placeholder="费率" style="width: 100%;" @change="countInterestRate" @blur="countInterestRate"/>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>每月应还金额(元)</span></template>
              <el-input v-model="interestRateForm.payableAmount" disabled/>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>手续费总额(元)</span></template>
              <el-input v-model="interestRateForm.total" disabled/>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>还款日<span style="color: red">*</span></span></template>
              <el-form-item label="还款日" prop="repaymentDay">
                <div class="selectDate">
                  <div class="label">每月</div>
                  <el-select v-model="delistForm.repaymentDay" placeholder="请选择日期" style="width: 205px">
                    <el-option
                      v-for="index in 31"
                      :key="index"
                      :label="index + '日'"
                      :value="index"
                    />
                  </el-select>
                </div>
                
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>三方订单编号</span></template>
              <el-form-item label="三方订单编号" prop="orderCode">
                <el-input v-model="delistForm.orderCode" placeholder="请输入三方订单编号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>付款方式</span></template>
              <el-form-item label="付款方式" prop="parType">
                <el-select v-model="delistForm.parType" placeholder="请输入付款方式">
                  <el-option
                    v-for="item in [
                      { value: 1, name: '支付宝账号' },
                      { value: 2, name: '微信' },
                      { value: 3, name: '银行卡' },
                      { value: 4, name: '淘宝' }
                    ]"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item class="descriptions" v-if="delistForm.parType == '1'">
              <template #label><span>支付宝订单号</span></template>
              <el-form-item label="支付宝订单号" prop="alipayOrderNo">
                <el-input v-model="delistForm.alipayOrderNo" placeholder="请输入支付宝订单号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel" v-else></el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>付款凭证</span></template>
              <el-form-item label="付款凭证" prop="payCredentials">
                <ny-upload :limit="1" v-model:imageUrl="delistForm.payCredentials" tip=" " accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card class="cardDescriptions" style="padding: 0; margin-top: 12px" v-else>
          <div class="titleSty">付款信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>三方订单编号</span></template>
              <el-form-item label="三方订单编号" prop="orderCode">
                <el-input v-model="delistForm.orderCode" placeholder="请输入三方订单编号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>付款方式</span></template>
              <el-form-item label="付款方式" prop="parType">
                <el-select v-model="delistForm.parType" placeholder="请输入付款方式">
                  <el-option
                    v-for="item in [
                      { value: 1, name: '支付宝账号' },
                      { value: 2, name: '微信' },
                      { value: 3, name: '银行卡' }
                    ]"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item class="descriptions" v-if="delistForm.parType == '1'">
              <template #label><span>支付宝订单号</span></template>
              <el-form-item label="支付宝订单号" prop="alipayOrderNo">
                <el-input v-model="delistForm.alipayOrderNo" placeholder="请输入支付宝订单号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel" v-else></el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>付款凭证</span></template>
              <el-form-item label="付款凭证" prop="payCredentials">
                <ny-upload :limit="1" v-model:imageUrl="delistForm.payCredentials" tip=" " accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-form>

      <template v-slot:footer>
        <el-button :loading="btnLoading" @click="visibleSale = false">{{ $t("cancel") }}</el-button>
        <el-button :loading="btnLoading" type="primary" @click="SubmitEvent()">确定售出</el-button>
      </template>
    </el-drawer>
    <el-dialog v-model="visible" :title="'下架'" :close-on-click-modal="false" :close-on-press-escape="false" width="30%">
      <el-form ref="delistFormRef" :model="delistForm" :rules="delistRules" label-width="120px" status-icon>
        <el-row>
          <el-col :span="22">
            <el-form-item label="下架原因：" prop="types">
              <el-select v-model="delistForm.types" placeholder="请选择下架原因" @change="delistForm.remark = ''">
                <el-option label="用户下架" value="用户下架" />
                <el-option label="平台下架" value="平台下架" />
                <el-option label="账号问题" value="账号问题" />
                <el-option label="重新整理" value="重新整理" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="原因" prop="remark" v-if="delistForm.types == '账号问题'">
              <el-input v-model="delistForm.remark" placeholder="请输入原因" />
            </el-form-item>
            <el-form-item label="备注" prop="remark" v-if="delistForm.types == '其他'">
              <el-input v-model="delistForm.remark" placeholder="请输入下架原因" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template v-slot:footer>
        <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
        <el-button :loading="btnLoading" type="primary" @click="SubmitEvent()">{{ $t("confirm") }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { formatCurrency } from "@/utils/method";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const visibleSale = ref(false); // 对话框显隐

const delistFormRef = ref();
const delistForm = reactive({
  types: "",
  remark: "",
  status: 2,
  id: null,
  ids: false,
  sold: "",
  transactionPrice: "",
  transactionUser: "", // 出售人
  channelId: "", // 出售渠道
  buyerPhone: "", //买方手机号
  guaranteeAmount: "", // 包赔费用
  orderCode: "", // 第三方订单编号
  payCredentials: "", // 第三方订单编号
  parType: "",
  alipayOrderNo: "",
  byStages: 0,
  repaymentDay: "",
  byStagesPeriods: 1, // 分期期数
  accountSourceName:"", // 平台类型
  downPayments: null, // 首付
  byStagesRate: null, // 分期费率
});

const rows = ref(<any>"");
const validateRules = (rule: any, value: any, callback: any) => {
  if (value === rows.value.phone) {
    callback(new Error("买方手机号与卖方不能一致"));
  } else {
    callback();
  }
};
const delistRules = ref({
  types: [{ required: true, message: "请选择下架原因", trigger: "change" }],
  remark: [{ required: true, message: "请输入下架原因" }],
  transactionPrice: [{ required: true, message: "请输入成交价" }],
  transactionUser: [{ required: true, message: "请选择出售人", trigger: "change" }],
  channelId: [{ required: true, message: "请选择出售渠道", trigger: "change" }],
  payCredentials: [{ required: false, message: "请选择付款凭证", trigger: "change" }],
  buyerPhone: [
    { required: true, message: "请输入买方手机号", trigger: "blur" },
    { pattern: /^1[3456789]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" },
    { validator: validateRules, trigger: "blur" }
  ],
  guaranteeAmount: [{ required: false, message: "请输入包赔费用", trigger: "blur"  }],
  downPayments: [{ required: true, message: "请输入首付款", trigger: "blur"  }],
  byStagesRate: [{ required: true, message: "请输入费率", trigger: "blur"  }],
  byStagesPeriods: [{ required: true, message: "请选择分期期数", trigger: "change" }],
  repaymentDay: [{ required: true, message: "请选择还款日", trigger: "change" }],
});

// 弹窗初始化
const init = (id: any, sold?: string, row?: any) => {
  console.log(row, "====sdfsdjfdslkfj");
  rows.value = row;
  if (sold) {
    visibleSale.value = true;
  } else {
    visible.value = true;
  }

  delistForm.ids = false;
  delistForm.id = id;
  delistForm.accountSourceName = row.accountSourceName
  delistForm.sold = sold ? sold : "";
  getSelectData();
  getChannelTree();
  getConfig();
  // 重置表单数据
  if (delistFormRef.value) {
    delistFormRef.value.resetFields();
  }
};
const initMore = (ids: any) => {
  // 批量下架
  visible.value = true;
  delistForm.id = ids;
  delistForm.ids = true;
  delistForm.sold = "";
  getSelectData();
  getChannelTree();
  // 重置表单数据
  if (delistFormRef.value) {
    delistFormRef.value.resetFields();
  }
};

// 获取出售渠道
const ChannelTreeList = ref(<any>[]);
const getChannelTree = () => {
  baseService.get("/channel/channel/getChannelTree", { type: "0" }).then((res) => {
    res.data.map((item: any) => {
      if (item.children.length == 0) {
        item.disabled = true;
      }
    });
    ChannelTreeList.value = res.data;
    console.log(ChannelTreeList.value, "=====ChannelTreeList.value=======");
  });
};

// 提交
const btnLoading = ref(false);
const SubmitEvent = () => {
  delistFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (delistForm.sold) {
      const params = {
        id: delistForm.id,
        status: 4,
        transactionPrice: +delistForm.transactionPrice,
        transactionUser: delistForm.transactionUser,
        channelId: delistForm.channelId[delistForm.channelId.length - 1],
        buyerPhone: delistForm.buyerPhone,
        guaranteeAmount: delistForm.guaranteeAmount ? delistForm.guaranteeAmount : 0,
        // 第三方订单编号
        orderCode: delistForm.orderCode,
        // 支付凭证
        payCredentials: delistForm.payCredentials,
        // 创建订单，商品列表点击出售时传true
        createOrder: true,
        // 支付宝订单号
        alipayOrderNo: delistForm.alipayOrderNo,
        parType: delistForm.parType,
        // 是否分期
        byStages: delistForm.byStages,
        // 分期期数
        byStagesPeriods: delistForm.byStagesPeriods,
        // 还款日
        repaymentDay: delistForm.repaymentDay,
        // 首付
        downPayments: delistForm.downPayments,
        // 分期费率
        byStagesRate: delistForm.byStagesRate
      };
      btnLoading.value = true;
      baseService
        .post("/shop/shop/status", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500,
            onClose: () => {
              visibleSale.value = false;
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      const params = {
        status: 3,
        delistingRemark: delistForm.types
      };
      if (delistForm.ids) {
        params.ids = delistForm.id;
      } else {
        params.id = delistForm.id;
      }
      if (delistForm.types == "账号问题" || delistForm.types == "其他") {
        params.delistingRemark = delistForm.remark;
      }
      btnLoading.value = true;
      baseService
        .post("/shop/shop/status", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const tenantList = ref(<any>[]);
const getSelectData = () => {
  let data = <any>[];
  let tenantData = <any>[];
  baseService.get("/channel/channel/page", { limit: 9999 }).then((res) => {
    (res.data?.list || []).forEach((element: any) => {
      if (element.channelType == 3) {
        tenantData.push({
          ...element,
          value: element.id,
          label: element.title
        });
      } else if (element.channelType == 0) {
        data.push({
          ...element,
          value: element.id,
          label: element.title
        });
      }
    });
    if (tenantData.length > 0) {
      data.unshift({
        label: "合作商出售",
        value: 3,
        children: [...tenantData]
      });
    }
    tenantList.value = data;
  });
};

// 是否分期开关
const byStagesChange = (value:any) =>{
  if(value == 1 && !configInfo.value){
    delistForm.byStages = 0;
    ElMessage.warning("请前往分期订单，进行分期设置！")
    return
  }
}

// 获取分期配置信息
const configInfo = ref();
const getConfig = () =>{
  baseService.get('/sale/saleStagesConfig/getConfig').then(res=>{
    if(res.code == 0){
      configInfo.value = res.data;
      countInterestRate();
    }
  })
}

// 根据分析配置信息计算相关利率
const interestRateForm = reactive({
  downPayment: '', // 首付
  installmentAmount: '',
  rate: '',
  payableAmount: '',
  total: '',
})
const countInterestRate = () =>{
  interestRateForm.installmentAmount =  (Number(delistForm.transactionPrice) - delistForm.downPayments).toFixed(2)
  interestRateForm.payableAmount = ((Number(interestRateForm.installmentAmount) / Number(delistForm.byStagesPeriods)) + (Number(interestRateForm.installmentAmount) * (delistForm.byStagesRate / 100))).toFixed(2);
  interestRateForm.total = (Number(interestRateForm.installmentAmount) * (delistForm.byStagesRate / 100) * Number(delistForm.byStagesPeriods)).toFixed(2);
}

defineExpose({
  init,
  initMore
});
</script>

<style lang="less" scoped>
.selectDate{
  display: flex;
  align-items: center;
  .label{
    padding: 4px 16px;
    border: 1px solid #DCDFE6;
    border-right: none;
    border-radius: 4px 0px 0px 4px;
    background-color: #F5F7FA;
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    color: #606266;
  }
  :deep(.el-select__wrapper){
    border-radius:0px 4px 4px 0px;
  }
}
.tipLabel {
  :deep(.el-form-item__label) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
