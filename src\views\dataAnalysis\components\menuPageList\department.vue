<template>
    <div>
        <div class="analysis_type_item" style="margin-left: 10px;" @click="deptDialog">
            <span>{{ props.deptName ? props.deptName : "设置对比数据" }}</span>
            <el-icon color="#D4D7DE" size="16" @click.stop="clearChange" v-if="props.deptName" style="margin-left: 8px;"><CircleCloseFilled /></el-icon>
        </div>
        <el-dialog v-model="visibleDept" width="30%" :modal="false" :title="placeholder" :close-on-click-modal="false"
            :close-on-press-escape="false">
            <el-form size="small" :inline="true">
                <el-form-item :label="$t('keyword')">
                    <el-input v-model="filterText" :input-style="{ width: '150px' }" placeholder="请输入部门名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="default">{{ $t("query") }}</el-button>
                </el-form-item>
            </el-form>
            <el-tree class="filter-tree" :data="deptList" :default-expanded-keys="expandedKeys"
                :props="{ label: 'name', children: 'children' }" :expand-on-click-node="false"
                :filter-node-method="filterNode" :highlight-current="true" node-key="id" ref="treeRef"> </el-tree>
            <template v-slot:footer>
                <el-button type="default" @click="cancelHandle()">{{ $t("cancel") }}</el-button>
                <el-button v-if="query" type="info" @click="clearHandle()">{{ $t("clear") }}</el-button>
                <el-button type="primary" @click="commitHandle()">{{ $t("confirm") }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from "vue";
import { IObject } from "@/types/interface";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const filterText = ref("");
const visibleDept = ref(false);
const deptList = ref<any[]>([]);
const showDeptName = ref("");
const expandedKeys = ref<any[]>([]);
const treeRef = ref();

const props = defineProps({
    modelValue: String,
    deptName: String,
    query: Boolean,
    placeholder: String
});
watch(
    () => filterText.value,
    (val) => {
        treeRef.value.filter(val);
    }
);

const deptDialog = () => {
    expandedKeys.value = [];
    visibleDept.value = true;
    getDeptList(props.modelValue);
};

const filterNode = (value: string, data: IObject) => {
    if (!value) return true;
    return data.name.indexOf(value) !== -1;
};

const getDeptList = (id?: string) => {
    return baseService.get("/sys/dept/list").then((res) => {
        deptList.value = res.data;
        nextTick(() => {
            if (id) {
                treeRef.value.setCurrentKey(id);
                expandedKeys.value = [id];
            }
        });
    });
};

const cancelHandle = () => {
    visibleDept.value = false;
    deptList.value = [];
    filterText.value = "";
};

const emit = defineEmits(["update:modelValue", "update:deptName", "changeDeptId"]);

const clearHandle = () => {
    emit("update:modelValue", "");
    emit("update:deptName", "");
    showDeptName.value = "";
    visibleDept.value = false;
    deptList.value = [];
    filterText.value = "";
};

const commitHandle = () => {
    const node = treeRef.value.getCurrentNode();
    if (!node) {
        ElMessage.error(t("dept.chooseerror"));
        return;
    }
    emit("update:modelValue", node.id);
    emit("update:deptName", node.name);
    emit("changeDeptId", node.id);
    showDeptName.value = node.name;
    visibleDept.value = false;
    deptList.value = [];
    filterText.value = "";
};

const clearChange = () => {
    emit("update:modelValue", "");
    emit("update:deptName", "");
}
</script>
<style lang="less" scoped>
.analysis_type_item {
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    line-height: 22px;
    padding: 3px 12px;
    background: #FFFFFF;
    border-radius: 24px;
    border: 1px solid #D4D7DE;
    cursor: pointer;
    display: flex;
    align-items: center;
}
</style>