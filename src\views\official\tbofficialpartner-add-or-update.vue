<template>
    <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
            <el-row :gutter="24">
                <el-col :span="24">
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="dataForm.name" placeholder="合作伙伴名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="合作伙伴logo" prop="logo">
                        <ny-upload v-model:imageUrl="dataForm.logo" :limit="1" ></ny-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="排序" prop="sort">
                        <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template v-slot:footer>
            <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  //id
  id:"",
  //合作伙伴名称
  name: "",
  //合作伙伴logo
  logo: "",
  //排序
  sort: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
      dataFormRef.value.resetFields();
  }

  if (id) {
      getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/official/tbofficialpartner/" + id).then((res) => {
      Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
          return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/official/tbofficialpartner", dataForm).then((res) => {
          ElMessage.success({
              message: t("prompt.success"),
              duration: 500,
              onClose: () => {
                  visible.value = false;
                  emit("refreshDataList");
              }
          });
      });
  });
};
const logoUrlAttr=(url:any)=>{
    dataForm.logo=url[0].response.data.src
}
defineExpose({
  init
});
</script>

