<template>
  <el-drawer class="descriptionsDrawer" v-model="visible" :footer="null" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="934px">
    <template #title>
      <div>
        <span style="font-size: 20px; margin-right: 10px; color: #303133">{{ propsData.title }}</span>
        <el-text type="primary" style="font-size: 16px; font-weight: 400">
          <el-icon style="margin-right: 2px"><WarningFilled /></el-icon>仅支持申请普通增值税发票</el-text
        >
      </div>
    </template>
    <div class="partBg" style="height: calc(100vh - 52px)">
      <div v-for="(item, index) in typeList" :key="item">
        <div v-if="item === 1" class="detailcard cardDescriptions">
          <div class="titleSty flx-justify-between">
            <span>{{ "充值订单明细" }} </span>
          </div>
          <el-table :data="propsData.purchases" :show-summary="true" :summary-method="getSummaries" max-height="450">
            <el-table-column prop="remark" label="开票类型" align="center" width="385" />
            <el-table-column prop="createName" align="center" width="140" label="支付人" />
            <el-table-column prop="createDate" align="center" width="200" label="充值时间">
              <template #default="{ row }">
                <span>{{ formatTimeStamp(row.createDate) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="spendingAmount" align="center" label="充值金额(元)" width="150" />
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
        </div>
        <div v-if="item === 2" class="detailcard cardDescriptions">
          <div class="titleSty">{{ "开票信息" }}</div>
          <el-form style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="submitForm()" label-width="120px">
            <el-descriptions title="" :column="2" size="default" border v-if="!dataForm.id">
              <el-descriptions-item label-class-name="title" :span="dataForm.headerType == '1' ? 1 : 2">
                <template #label>
                  <div>发票类型<span style="color: red">*</span></div>
                </template>
                普通增值税发票(电子)
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1" :key="1">
                <template #label key="20">
                  <div>抬头类型</div>
                </template>
                {{ dataForm.headerType == 1 ? "企业单位" : dataForm.headerType == 2 ? "个人" : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1" :key="2">
                <template #label key="21">
                  <div>
                    选择抬头<span style="color: red">*{{ dataForm.headerType == 1 ? "" : "" }}</span>
                  </div>
                </template>
                <el-form-item prop="headerName" label="选择抬头" required :key="10">
                  <el-select v-model="dataForm.hid" placeholder="请选择抬头" @change="handleHeadData">
                    <el-option v-for="item in headerList" :key="item.id" :label="item.headerName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
              <template v-if="dataForm.headerType == 1">
                <el-descriptions-item label-class-name="title" :span="1" :key="3">
                  <template #label>
                    <div>税号{{ dataForm.headerType == 1 ? "" : "" }}<span style="color: red">*</span></div>
                  </template>
                  <el-form-item prop="taxNumber" label="税号" :key="11">
                    <el-input v-model="dataForm.taxNumber" placeholder="请输入税号" disabled />
                  </el-form-item> </el-descriptions-item
              ></template>
              <el-descriptions-item label-class-name="title" :span="1" :key="4">
                <template #label>
                  <div>开票金额(元)<span style="color: red">*</span></div>
                </template>
                <el-form-item prop="invoiceAmount" label="开票金额(元)">
                  <el-input v-model="dataForm.invoiceAmount" placeholder="请输入开票金额(元)" disabled />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1">
                <template #label>
                  <div>发送至邮箱<span style="color: red">*</span></div>
                </template>
                <el-form-item prop="email" label="发送至邮箱">
                  <el-input v-model="dataForm.email" placeholder="发送至邮箱" disabled />
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions title="" :column="2" size="default" border v-else>
              <el-descriptions-item label-class-name="title" :span="dataForm.headerType == '1' ? 1 : 2">
                <template #label>
                  <div>发票类型</div>
                </template>
                普通增值税发票(电子)
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1">
                <template #label>
                  <div>开票金额(元)</div>
                </template>
                <span>{{ dataForm.invoiceAmount }}</span>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1">
                <template #label>
                  <div>抬头名称</div>
                </template>
                <span>{{ dataForm.headerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" v-if="dataForm.headerType == 1" :span="1">
                <template #label>
                  <div>税号</div>
                </template>
                <span>{{ dataForm.taxNumber }}</span>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1">
                <template #label>
                  <div>抬头类型</div>
                </template>
                {{ dataForm.headerType == 1 ? "企业单位" : dataForm.headerType == 2 ? "个人" : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="1">
                <template #label>
                  <div>接收邮箱</div>
                </template>
                <span>{{ dataForm.email }}</span>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title" :span="2">
                <template #label>
                  <div>开票状态</div>
                </template>
                <el-tag :type="dataForm.applyStatus == 1 ? 'warning' : 'success'">{{ dataForm.applyStatus == 1 ? "开票中" : "已开票" }}</el-tag>
                <el-text v-if="dataForm.applyStatus == 2" style="margin-left: 10px" type="info"
                  ><el-icon style="margin-right: 4px"><WarningFilled /></el-icon>请去接收邮箱下载发票</el-text
                >
              </el-descriptions-item>
            </el-descriptions>
          </el-form>
        </div>
        <div v-if="index === 0" style="margin-bottom: 12px"></div>
      </div>
    </div>
    <template v-slot:footer v-if="!dataForm.id">
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :disabled="propsData.purchases.length < 1" :loading="btnLoading" type="primary" @click="submitForm()">提交</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, reactive, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import { formatTimeStamp, formatCurrency } from "@/utils/method";
import { ElMessage, ElNotification } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: null,
  orderIds: "",
  invoiceType: 1,
  headerType: undefined,
  headerName: "",
  taxNumber: "",
  email: "",
  registerAddress: "",
  invoiceAmount: 0,
  hid: "" //非数据库字段
});
const rules = ref({
  headerName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  invoiceAmount: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  taxNumber: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});
const typeList = ref([1, 2]);
const propsData = ref({
  purchases: [],
  apply: true
});

const headerList = ref(<any>[]);
const getHeaderList = () => {
  baseService.get("/invoice/header/list").then((res) => {
    headerList.value = res.data;
  });
};
const handleHeadData = () => {
  let obj = headerList.value.find((ele) => ele.id == dataForm.hid);
  dataForm.headerType = obj.headerType;
  dataForm.headerName = obj.headerName;
  dataForm.taxNumber = obj.taxNumber;
  dataForm.email = obj.email;
  dataForm.registerAddress = obj.registerAddress;
};
const init = (row?: any) => {
  visible.value = true;
  propsData.value = row;
  Object.assign(dataForm, {
    id: null,
    orderIds: "",
    invoiceType: 1,
    headerType: undefined,
    headerName: "",
    taxNumber: "",
    email: "",
    registerAddress: "",
    invoiceAmount: 0,
    hid: "" //非数据库字段
  });
  getHeaderList();
  // 页面排序
  typeList.value = row.apply ? [1, 2] : [2, 1];
  // 数据处理
  if (propsData.value.apply) {
    propsData.value.purchases = propsData.value.purchases.filter((ele: any) => !ele.applyStatus || ele.applyStatus == 0);
    let amountTotal = 0;
    propsData.value.purchases.forEach((ele: any) => {
      amountTotal = amountTotal + +ele.spendingAmount;
    });
    dataForm.orderIds = propsData.value.purchases.map((ele: any) => ele.id).join(",");
    dataForm.invoiceAmount = amountTotal;
    ElNotification({
      title: "提示",
      message: "已过滤出未开发票数据",
      type: "info"
    });
    return;
  } else {
    getInfo(row);
  }
};
const getInfo = (row: any) => {
  baseService.get("/invoice/apply/" + row.id).then((res) => {
    Object.assign(dataForm, res.data);
    propsData.value.purchases = res.data.purchases;
  });
};
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value[0].validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    let params = { ...dataForm };
    delete params.hid;
    baseService[dataForm.id ? "put" : "post"]("/invoice/apply", params)
      .then((res) => {
        if (res.code === 0) {
          emit("refreshDataList");
          visible.value = false;
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
// 合计行计算函数
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    // 第一列 显示文字
    if (index === 0) {
      return (sums[index] = "合计:");
    } else if (column.property == "spendingAmount") {
      let total: any = 0;
      propsData.value.purchases.map((item: any) => {
        if (item.spendingAmount) total = total + +item.spendingAmount;
      });
      total = formatCurrency(+total.toFixed(2));
      return (sums[index] = total + "");
    }
  });
  return sums;
};
defineExpose({
  init
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
tfoot {
  tr td:last-child .cell {
    color: red;
    font-weight: bold;
  }
}
</style>
