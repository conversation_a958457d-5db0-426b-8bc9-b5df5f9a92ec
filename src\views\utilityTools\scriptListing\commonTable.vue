<template>
  <div class="ba-table-box" style="background-color: var(--ba-bg-color-overlay); border-radius: 0">
    <template v-if="currentTableIndex == 4">
      <div>
        <div style="margin-bottom: 6px">
          <template v-if="+currentTableIndex > 1">
            <div class="flx-justify-between">
              <div>
                <el-button type="primary" :loading="autoAsyncScriptLoading" @click="autoAsyncScript">自动同步</el-button>
                <template v-if="+currentTableIndex == 4">
                  <el-button type="warning" @click="importShow = true">导入</el-button>
                  <el-button type="primary" :disabled="state.selectionList?.length < 1" @click="exportHandle">导出</el-button>
                </template>
                <el-button @click="delListHandle" :disabled="state.selectionList?.length < 1" type="danger">删除</el-button>
              </div>
              <div v-if="+currentTableIndex == 2 || +currentTableIndex == 3 || +currentTableIndex == 4">
                <el-select style="width: 200px; margin-right: 12px" @change="TableCurrentChangeFn(1)" v-model="state.dataForm.outPname" clearable filterable v-if="+currentTableIndex == 4">
                  <el-option v-for="(item, index) in attributeList" :key="index" :label="item.name + (item.require ? ' (必填)' : '')" :value="item.name" />
                </el-select>
                <el-input :prefix-icon="Search" style="width: 280px !important" v-model="queryParams.attributeName" placeholder="请输入" clearable> </el-input>
                <el-button type="primary ml-12" @click="TableCurrentChangeFn(1)">{{ $t("query") }}</el-button>
                <el-button class="ml-8" @click="(queryParams.outName = ''), TableCurrentChangeFn(1)">{{ $t("resetting") }}</el-button>
              </div>
            </div>
          </template>
        </div>
        <div class="setUptable">
          <el-table border @select="onSelect" @select-all="onSelectAll" @sort-change="sortChange" :data="state.dataList">
            <el-table-column show-overflow-tooltip v-for="(cItem, cindex) in tableColums[currentTableIndex]" :key="cindex" :prop="cItem.prop" align="center" :sortable="cItem.sortable" :type="cItem.type" :min-width="cItem.minWidth" :label="cItem.label">
              <template #default="scope">
                <template v-if="cItem.prop == 'sysName'">
                  {{ settingStore.info.seoPlatformName }}
                </template>
                <template v-if="cItem.prop == 'fieldFormat'">
                  {{ scope.row.fieldFormat ? ["", "单选", "多选", "文本"][+scope.row.fieldFormat] : "-" }}
                </template>
                <!-- 平台属性 -->
                <template v-if="cItem.prop == 'fixedValue'">
                  <el-select
                    v-if="scope.row.fieldFormat == '1' || scope.row.fieldFormat == '2'"
                    @change="changeGameselect(scope.row, scope.$index)"
                    @close="changeGameselect(scope.row, scope.$index)"
                    v-model="scope.row.fixedValue"
                    :multiple="scope.row.fieldFormat == '2'"
                    filterable
                    clearable
                    placeholder="请选择对应平台属性"
                  >
                    <el-option v-for="item in scope.row.filedSelect" :key="item.code + ''" :label="item.value" :value="item.code + ''"></el-option>
                  </el-select>
                  <el-input v-if="scope.row.fieldFormat == '3'" @change="changeGameselect(scope.row, scope.$index)" v-model="scope.row.fixedValue" placeholder="请输入"></el-input>
                </template>
                <!-- 是否必填、 -->
                <template v-if="cItem.prop == 'isRequired'">
                  {{ scope.row.isRequired ? "是" : "否" }}
                </template>
                <!-- 任务类型 -->
                <template v-if="cItem.prop == 'taskType'">
                  {{ getTaskText(scope.row.taskType) }}
                </template>
                <!-- 状态 -->
                <template v-if="cItem.prop == 'pushStatus'">
                  <el-tag v-if="scope.row.pushStatus == 2" type="warning">推送中</el-tag>
                  <el-tag v-if="scope.row.pushStatus == 3" type="success">推送完成</el-tag>
                  <el-tag v-if="scope.row.pushStatus == 1" type="primary">未开始</el-tag>
                </template>
                <template v-if="cItem.prop == 'pushSuccessCount'">
                  {{ scope.row.totalPushCount > 0 ? (scope.row.pushSuccessCount || 0) + "/" + scope.row.totalPushCount : "-" }}
                </template>
                <template v-if="cItem.prop == 'pushStartTime'">
                  {{ timeFormat(scope.row.pushStartTime) }}
                </template>
                <template v-if="cItem.prop == 'platformId'">
                  <el-select @change="changemappingSelect(scope.row)" remote :remote-method="(query: any)=>{remoteMethod(query, scope.row)}" v-model="scope.row.platformName" filterable clearable placeholder="请选择对应平台属性">
                    <el-option v-for="item in platformList" :key="item.platformId + ''" :label="item.platformName" :value="item.platformName + ''"></el-option>
                  </el-select>
                </template>
                <template v-if="cItem.prop == 'defaultThis'">
                  <el-switch v-model="scope.row.defaultThis" :active-value="true" :inactive-value="false" :loading="scope.row.loading" @click="switchChange(scope.row)" />
                </template>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
        </div>
      </div>
    </template>
    
    <el-pagination
      v-if="!tableLoading"
      :current-page="queryParams.page"
      :page-sizes="[10, 20, 50, 100, 500, 1000]"
      :page-size="queryParams.limit"
      :total="state.count"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="TableSizeChangeFn"
      @current-change="TableCurrentChangeFn"
    ></el-pagination>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importShow" class="ba-upload-preview" title="" @close="closeMoneyPreview" width="35%">
      <template #header>导入文件</template>
      <div>
        <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs">
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              importShow = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { timeFormat, fileExport } from "@/utils/utils";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";
// 组件引入
import baseService from "@/service/baseService";
import { useSettingStore } from "@/store/setting";

const emit = defineEmits(["refreshDataList", "pushChange"]);

const settingStore = useSettingStore();
const pushInfoRef = ref();

const { t } = useI18n();

const dataObj: {
  gameList: any[];
  curAllPartnerList: any[];
} = reactive({
  gameList: [],
  curAllPartnerList: []
});
const collapseValue = ref(-1);
const state = reactive({
  // 传参
  dataForm: {
    partnerId: "0", //平台
    gameId: "0", // 游戏
    outPname: "",
    scriptUserId: "" // 店铺Id
  },
  sortForm: {
    order: null,
    column: null
  },
  selectionList: [],
  dataList: <any>[],
  count: 0
});

const platformList = ref(<any>[]);
const tableLoading = ref(false);
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  limit: 10
});
const currentTableIndex = ref(2); //当前table序号

// 表格配置项
const tableColums = reactive({
  0: [
    {
      type: "selection",
      minWidth: 50
    },
    {
      prop: "partnerName",
      label: "合作商",
      minWidth: 160
    },
    {
      prop: "gameName",
      label: "游戏名称",
      minWidth: 160
    },
    {
      prop: "***",
      label: "合作商属性类型",
      minWidth: 160
    },
    {
      prop: "fieldName",
      label: "字段名称",
      minWidth: 160
    },
    {
      prop: "fieldFormat",
      label: "字段格式",
      minWidth: 160
    },
    {
      prop: "fieldProperty",
      label: "字段属性",
      minWidth: 160
    },
    {
      prop: "isRequired",
      label: "是否必填",
      minWidth: 160
    },
    {
      prop: "fixedValue",
      label: "平台属性",
      minWidth: 160
    }
  ],
  1: [
    {
      type: "selection",
      minWidth: 50
    },
    {
      prop: "gameName",
      label: "游戏名称",
      minWidth: 190
    },
    {
      prop: "totalPushCount",
      label: "推送商品总数",
      minWidth: 130
    },
    {
      prop: "nowDayPushNum",
      label: "今日推送商品数量",
      minWidth: 184
    },
    {
      prop: "pushingNum",
      label: "推送执行中商品数量",
      minWidth: 152
    },
    {
      prop: "taskType",
      label: "任务类型",
      minWidth: 104
    },
    {
      prop: "intervalTime",
      label: "间隔时间",
      minWidth: 136
    },
    // {
    //   prop: "dailyLimit",
    //   label: "每日上限",
    //   minWidth: 136,
    // },
    {
      prop: "pushStartTime",
      label: "推送时间",
      minWidth: 190,
      sortable: true
    },
    {
      prop: "pushStatus",
      label: "状态",
      minWidth: 136
    },
    {
      prop: "pushSuccessCount",
      label: "推送成功数量",
      minWidth: 136
    },
    {
      prop: "operate",
      label: "操作",
      minWidth: 136
    }
  ],
  2: [
    {
      type: "selection",
      minWidth: 30
    },
    {
      prop: "partnerName",
      label: "合作商名称"
    },
    {
      prop: "outName",
      label: "合作商游戏名称"
    },
    {
      prop: "sysName",
      label: "当前平台"
    },
    {
      prop: "platformId",
      label: "当前平台游戏名称"
    }
  ],
  3: [
    {
      type: "selection",
      minWidth: 50
    },
    {
      prop: "sysName",
      label: "当前平台",
      minWidth: 190
    },
    {
      prop: "gameName",
      label: "当前平台游戏名称",
      minWidth: 190
    },
    {
      prop: "outName",
      label: "合作商区服名称",
      minWidth: 190
    },
    {
      prop: "partnerName",
      label: "合作商名称",
      minWidth: 190
    },
    {
      prop: "partnerGameName",
      label: "合作商游戏名称",
      minWidth: 190
    },
    {
      prop: "outPname",
      label: "合作商上级区服名称",
      minWidth: 190
    },
    {
      prop: "platformId",
      label: "当前平台区服名称",
      minWidth: 190
    }
  ],
  4: [
    {
      type: "selection",
      minWidth: 50
    },
    {
      prop: "partnerName",
      label: "合作商名称",
      minWidth: 150
    },
    {
      prop: "gameName",
      label: "合作商游戏名称",
      minWidth: 150
    },
    {
      prop: "outPname",
      label: "合作商上级名称",
      minWidth: 150
    },
    {
      prop: "outName",
      label: "合作商游戏属性名称",
      minWidth: 190
    },
    {
      prop: "sysName",
      label: "当前平台",
      minWidth: 150
    },
    {
      prop: "platformPname",
      label: "平台游戏上级名称",
      minWidth: 190
    },
    {
      prop: "platformId",
      label: "平台属性",
      minWidth: 190
    },
    {
      prop: "defaultThis",
      label: "是否默认",
      minWidth: 100
    }
  ]
});
// 父组件改变参数
const changeDataForm = (partnerId: any, gameId: any, type: any, changePartner = false) => {
  platformList.value = [];
  state.selectionList = [];
  state.dataList = [];
  currentTableIndex.value = +type;
  state.dataForm.gameId = gameId;
  state.dataForm.partnerId = partnerId;
  state.dataForm.outPname = "";
  queryParams.outName = "";
  if (type == 0) {
    getAllPartnerList();
  }
  
  getAttributeData();
  setTimeout(() => {
    TableCurrentChangeFn(1);
  }, 800);
};
// 列表下拉选择
const changeGameselect = (data: any, index: Number) => {
  if (data.fixedValue == undefined) {
    data.fixedValue = "";
  }
  let form = { ...data };
  if (form.fieldFormat == "2") {
    form.fixedValue = form.fixedValue.join(",");
  }
  baseService.put("/script/sysscriptfield", { ...form }).then((res) => {
    if (res.code == 0) {
      ElMessage.success("修改成功");
      getListData();
    }
  });
};
// 映射下拉
const changemappingSelect = (data: any) => {
  let form = { ...data };
  let itemData: any = form.platformName ? platformList.value.find((item: any) => item.platformName == form.platformName) : { id: "" };
  console.log(form, itemData);
  baseService
    .put("/mapping/sysgameinfomapping", {
      gameId: form.gameId,
      id: form.id,
      platformId: itemData.platformId
    })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("修改成功");
        getListData();
      }
    });
};
// 自动同步脚本
const autoAsyncScriptLoading = ref(false);
const autoAsyncScript = () => {
  let type = [8, 9, 5, 6, 7][+currentTableIndex.value];
  autoAsyncScriptLoading.value = true;
  ElMessage.success("数据正在同步中，请稍后查看！");
  baseService
    .post("/mapping/sysgameinfomapping/sync", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      type: type
    })
    .then((res) => {
      if (res.code == 0) {
        // ElMessage.success("正在同步中,请稍后刷新查看！");
        ElMessage.success("自动同步成功！");
        TableCurrentChangeFn(1);
        getAttributeData();
      }
    })
    .finally(() => {
      autoAsyncScriptLoading.value = false;
    });
};

// 导入
const importShow = ref(false);
const fileList = ref([] as IObject);

// 关闭对话框
const closeMoneyPreview = () => {
  importShow.value = false;
};
// -----------------------数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};
// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: any) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 文件上传   确认导入按钮
const uploadExcel = async (file: any) => {
  let type = [8, 9, 5, 6, 7][+currentTableIndex.value];
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  const url = `/mapping/sysgameinfomapping/import?type=${type}&gameId=${state.dataForm.gameId}&partnerId=${state.dataForm.partnerId}`;
  await baseService
    .post(url, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res: any) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
        getListData();
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      fileList.value = [];
      importShow.value = false;
      getListData();
    });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  params.page = -1;
  params.type = [8, 9, 5, 6, 7][+currentTableIndex.value];
  baseService.get("/mapping/sysgameinfomapping/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `官方属性映射`);
  });
};
// 搜索关联平台属性
const remoteMethod = (query: string, row: any) => {
  let type = [8, 9, 5, 6, 7][+currentTableIndex.value];
  if (query) {
    baseService
      .post("/mapping/sysgameinfomapping/getPlatformInfo", {
        mappingId: row.id,
        searchParam: query == null ? "" : query
      })
      .then((res) => {
        platformList.value = res.data || [];
      });
  }
};
// 获取列表信息&切换游戏
const getListData = () => {
  tableLoading.value = true;
  state.dataList = [];

  if (currentTableIndex.value == 1) {
    // // 推送记录
    // baseService
    //   .get("/script/sysscriptpushtask/page", {
    //     partnerId: state.dataForm.partnerId,
    //     gameId: state.dataForm.gameId,
    //     scriptUserId: state.dataForm.scriptUserId,
    //     ...queryParams,
    //     ...state.sortForm
    //   })
    //   .then((res_) => {
    //     if (res_.code == 0) {
    //       state.dataList = res_.data.list || [];
    //       collapseValue.value = 0;
    //       tableLoading.value = false;
    //       state.count = res_.data.total;
    //     }
    //   })
    //   .catch((err) => {
    //     tableLoading.value = false;
    //     state.count = 0;
    //   });
  } else {
    let type = [8, 9, 5, 6, 7][+currentTableIndex.value];
    baseService
      .get("/mapping/sysgameinfomapping/page", {
        partnerId: state.dataForm.partnerId,
        gameId: state.dataForm.gameId,
        outPname: state.dataForm.outPname,
        type: type,
        ...queryParams
      })
      .then((res_) => {
        if (res_.code == 0) {
          state.dataList = res_.data.list || [];
          tableLoading.value = false;
          state.count = res_.data.total;
        }
      })
      .catch((err) => {
        tableLoading.value = false;
        state.count = 0;
      });
  }
};

const delListHandle = () => {
  let selectedData = [...state.selectionList];
  let ids = selectedData.map((ele) => ele.id);
  let url = +currentTableIndex.value > 1 ? "/mapping/sysgameinfomapping" : "/script/sysscriptfield";
  baseService.delete(url, ids).then((res) => {
    if (res.code == 0) {
      ElMessage.success("删除成功");
      TableCurrentChangeFn(1);
    }
  });
};

// 回显处理
const getTaskText = (type: number) => {
  let arr = ["寄售", "上架", "擦亮", "编辑", "下架"];
  return arr[type - 1] || "-";
};
// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  getListData();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getListData();
};
// 选择列表项
const onSelect = (arr: any) => {
  state.selectionList = arr;
};
const onSelectAll = (arr: any) => {
  state.selectionList = arr;
};
const sortChange = ({ order, prop }: any) => {
  queryParams.page = 1;
  state.sortForm.order = order;
  state.sortForm.column = prop;
  getListData();
};
// ---------------------- 组件调佣

const getAllPartnerList = () => {
  baseService.get("/script/sysscriptpartnerinfo/allList").then((res) => {
    if (res.code == 0) {
      dataObj.curAllPartnerList = res.data || [];
    }
  });
};

const attributeList = ref();
const getAttributeData = () => {
  attributeList.value = [];
  baseService.get("/mapping/sysgameinfomapping/getPartnerAttrClass", { limit: 9999, partnerId: state.dataForm.partnerId, gameId: state.dataForm.gameId, type: 7 }).then((res) => {
    attributeList.value = res.data || [];
  });
};

// 是否默认开关
const switchChange = (row: any) => {
  if (row.id) {
    row.loading = true;
    baseService
      .put("/mapping/sysgameinfomapping/setDefault", { id: row.id })
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
        getListData();
      })
      .finally(() => {
        row.loading = false;
      });
  }
};



onMounted(() => {});
onUnmounted(() => {});
defineExpose({ changeDataForm });
</script>
<style lang="scss" scoped>
.default-main {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.accountTabsscript {
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
}
.setUptable {
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.el-tag {
  border: 1px solid;
}
.primaryDisabled {
  background-color: var(--el-button-disabled-bg-color);
  border-color: var(--el-button-disabled-bg-color);
}
.el-button:focus-visible {
  outline: none !important;
}
:deep(.el-dropdown-menu__item) {
  justify-content: center;
}

</style>
