<template>
    <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-form-item label="视频标题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="视频标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="视频地址" prop="url">
                        <ny-upload-video></ny-upload-video>
                        <!-- <ny-upload v-model="dataForm.url" v-model:headUrl="dataForm.url"></ny-upload> -->
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="视频封面" prop="cover">
                        <ny-upload v-model:imageUrl="dataForm.cover" :limit="1"></ny-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="视频类型" prop="type">
                        <ny-select v-model="dataForm.type" dict-type="official_video_type" placeholder="视频类型"></ny-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="排序" prop="sort">
                        <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template v-slot:footer>
            <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  //id
  id:"",
  //视频标题
  title: "",
  //视频地址
  url: "",
  //视频封面
  cover: "",
  //视频类型 1：推荐视频 【字典：official_video_type】
  type: "",
  //排序
  sort: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
      dataFormRef.value.resetFields();
  }

  if (id) {
      getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/official/tbofficialvideo/" + id).then((res) => {
      Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
          return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/official/tbofficialvideo", dataForm).then((res) => {
          ElMessage.success({
              message: t("prompt.success"),
              duration: 500,
              onClose: () => {
                  visible.value = false;
                  emit("refreshDataList");
              }
          });
      });
  });
};
const imageUploadEmit=(url:any)=>{
  console.log(url);
  dataForm.cover=url[0].response.data.src
}
defineExpose({
  init
});
</script>
