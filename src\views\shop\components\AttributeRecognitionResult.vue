<template>
    <el-dialog v-model="visible" title="属性识别结果" width="800">
        <el-alert title="以下属性识别未成功，请手动进行属性勾选" type="warning" :closable="false" />

        <el-checkbox-group v-model="checkedList">
            <div class="attribute-list" v-for="(item, index) in resData" :key="index">
                <div class="attribute-name">{{ item.className || '普通' }}</div>
                <div class="attribute-list-content">
                    <el-checkbox v-for="(sub, sindex) in item.officialAttrName" :value="sub" :key="index">{{ sub }}</el-checkbox>
                </div>
            </div>
        </el-checkbox-group>


        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="attributeHandle">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineEmits } from 'vue';

    const visible = ref(false);
    const emit = defineEmits(['confirm']);

    const checkedList = ref<string[]>([]);

    const resData = ref(<any>[]);
    const init = (list: any, type?: string) => {
        if(type == 'first') checkedList.value = [];

        resData.value = list;
        visible.value = true;
    }

    const attributeHandle = () => {
        visible.value = false;
        emit('confirm', checkedList.value);
    }

    defineExpose({
        init
    })

</script>

<style lang="scss" scoped>
    .attribute-list{
        margin-top: 12px;
        .attribute-name{
            font-size: 14px;
            line-height: 22px;
            padding-bottom: 2px;
        }

        .el-checkbox {
            height: 32px;
            line-height: 32px;
        }
    }
</style>