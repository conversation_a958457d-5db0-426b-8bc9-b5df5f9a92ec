<template>
  <el-drawer v-model="visible" :title="(dataForm.id && !checkInfo ? '编辑' : checkInfo ? '' : '新增') + '其他支出' + (checkInfo ? '详情' : '')" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="994" class="ny-drawer">
    <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
      <el-card class="cardDescriptions" style="padding: 0" v-loading="DetailLoading">
        <div class="titleSty">基本信息</div>
        <el-descriptions :column="2" border class="descriptions">
          <el-descriptions-item>
            <template #label
              ><span>费用名称<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="费用名称" prop="name">
              <span v-if="checkInfo">
                {{ dataForm.name }}
              </span>
              <el-input v-else v-model="dataForm.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>支出金额(元)<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="支出金额" prop="amount">
              <span v-if="checkInfo">
                {{ dataForm.amount }}
              </span>
              <el-input v-else v-model="dataForm.amount" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>支付方式<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="支付方式" prop="payType">
              <span v-if="checkInfo">
                {{ ["支付宝转账", "微信", "银行卡"][dataForm.payType - 1] }}
              </span>
              <el-select v-else v-model="dataForm.payType" clearable placeholder="请选择支付方式">
                <el-option v-for="(item, index) in ['支付宝转账', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>关联商品</span></template>
            <el-form-item label="关联商品" prop="shopCode">
              <span v-if="checkInfo">
                {{ dataForm.shopCode }}
              </span>
              <el-select v-else :loading="loading" clearable v-model="dataForm.shopCode" placeholder="请输入商品编码" filterable remote :remote-method="remoteMethod">
                <el-option v-for="item in shopList" :key="item.shopId" :label="item.shopCode" :value="item.shopCode"> </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item class-name="noneSelfRight" v-show="dataForm.payType == '1'" :span="2">
            <template #label><span>支付宝订单号</span></template>
            <el-form-item label="支付宝订单号" prop="alipayOrderNo">
              <span v-if="checkInfo">
                {{ dataForm.alipayOrderNo }}
              </span>
              <el-input v-else v-model="dataForm.alipayOrderNo" placeholder="请输入支付宝订单号" style="width: 39%"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template #label><span>备注</span></template>
            <el-form-item label="备注" prop="remark">
              <div v-if="checkInfo">
                {{ dataForm.remark || "-" }}
              </div>
              <el-input v-else type="textarea" v-model="dataForm.remark" placeholder="请输入备注" :rows="5"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>开销凭证</span></template>
            <el-form-item label="开销凭证">
              <div v-if="checkInfo">
                <span v-if="!dataForm.voucher || dataForm.voucher.length < 1">-</span>
                <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                  <el-image style="width: 100px" :src="item" v-for="item in dataForm.voucher.split(',')" alt="" :preview-src-list="dataForm.voucher.split(',')" />
                </div>
              </div>
              <ny-upload v-else v-model:imageUrl="dataForm.voucher" :limit="3" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>发票</span></template>
            <el-form-item label="发票">
              <div v-if="checkInfo">
                <span v-if="!dataForm.invoice || dataForm.invoice.length < 1">-</span>
                <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                  <el-image style="width: 100px" :src="item" v-for="item in dataForm.invoice.split(',')" alt="" :preview-src-list="dataForm.invoice.split(',')" />
                </div>
              </div>
              <ny-upload v-else v-model:imageUrl="dataForm.invoice" :limit="3" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="cardDescriptions" style="padding: 0; margin-top: 12px">
        <div class="titleSty">收款信息</div>
        <el-descriptions :column="2" border class="descriptions">
          <el-descriptions-item>
            <template #label
              ><span>账户类型<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="账户类型" prop="accountType">
              <span v-if="checkInfo">
                {{ ["支付宝", "微信", "银行卡"][dataForm.accountType - 1] }}
              </span>
              <el-select v-else v-model="dataForm.accountType" clearable placeholder="请选择账户类型">
                <el-option v-for="(item, index) in ['支付宝', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>收款账号<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="收款账号" prop="accountNumber">
              <span v-if="checkInfo">
                {{ dataForm.accountNumber || "-" }}
              </span>
              <el-input v-else v-model="dataForm.accountNumber" placeholder="请输入收款账号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>{{ dataForm.accountType != "3" ? "收款账户" : "姓名" }}<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="姓名" prop="accountName">
              <span v-if="checkInfo">
                {{ dataForm.accountName || "-" }}
              </span>
              <el-input v-else v-model="dataForm.accountName" :placeholder="`请输入${dataForm.accountType != '3' ? '收款账户' : '姓名'}`"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="dataForm.accountType == '3'">
            <template #label><span>开户银行</span></template>
            <el-form-item label="开户银行" prop="bankName">
              <span v-if="checkInfo"> {{ dataForm.bankName || "-" }} </span>
              <el-input v-else v-model="dataForm.bankName" placeholder="请输入开户银行"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-form>

    <template #footer>
      <template v-if="!checkInfo && !dataForm.checkCollect">
        <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
      </template>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
const emits = defineEmits(["refresh", "completeInfo"]);
const visible = ref(false);
const checkInfo = ref(false);
const DetailLoading = ref(false);
const init = (billType: any, row?: any) => {
  visible.value = true;
  dataForm.value.billType = billType;
  if (row && row.id) {
    // dataForm.value = Object.assign({}, row);
    DetailLoading.value = true;
    baseService
      .get("/automaticReconciliation/othercost/" + row.id)
      .then((res) => {
        dataForm.value = res.data;
      })
      .finally(() => {
        DetailLoading.value = false;
      });
    checkInfo.value = row.isCheck;
    delete dataForm.value.isCheck;
  }
};

const dataForm = ref(<any>{});

const rules = reactive({
  name: [{ required: true, message: "请输入费用名称", trigger: "change" }],
  amount: [{ required: true, message: "请输入支出金额", trigger: "change" }],
  payType: [{ required: true, message: "请选择支付方式", trigger: "change" }],
  // alipayOrderNo: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }],
  accountType: [{ required: true, message: "请选择账户类型", trigger: "change" }],
  accountNumber: [{ required: true, message: "请输入收款账号", trigger: "blur" }],
  accountName: [{ required: true, message: "请输入", trigger: "blur" }]
});
const loading = ref(false);
const shopList = ref([]);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    baseService
      .get("/automaticReconciliation/othercost/getShopByCode", { code: query })
      .then((res) => {
        shopList.value = res.data;
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    shopList.value = [];
  }
};
// 提交
const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let params = JSON.parse(JSON.stringify(dataForm.value));
    (!params.id ? baseService.post : baseService.put)("/automaticReconciliation/othercost", params)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          emits("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
:deep(.el-form-item) {
  &.is-error {
    .el-input__inner {
      &::placeholder {
        color: var(--el-color-danger);
      }
    }
    .el-select__placeholder {
      color: var(--el-color-danger);
    }
    .el-form-item__error {
      display: none !important;
      opacity: 0 !important;
    }
  }
  &.is-success {
    .el-form-item__error {
      transition: none !important;
      opacity: 0 !important;
    }
  }
}
</style>
