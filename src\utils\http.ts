import app from "@/constants/app";
import { CacheTenantCode } from "@/constants/cacheKey";
import { getLocaleLang } from "@/i18n";
import { IHttpResponse, IObject } from "@/types/interface";
import router from "@/router";
import axios, { AxiosRequestConfig } from "axios";
import qs from "qs";
import { getCache, getToken } from "./cache";
import { getValueByKeys } from "./utils";
import { ElMessage } from "element-plus";
import { ref } from "vue";
// 存储已显示的错误信息
const shownErrors = ref(new Set());
const http = axios.create({
  baseURL: app.api,
  timeout: app.requestTimeout
});

http.interceptors.request.use(
  function (config: any) {
    removePending(config); // 删除重复请求
    addPending(config); // 存储请求信息
    config.headers["X-Requested-With"] = "XMLHttpRequest";
    config.headers["Request-Start"] = new Date().getTime();
    config.headers["Accept-Language"] = getLocaleLang();
    config.headers["agent"] = "web";

    // 一键截图 上传图片  超时时间
    if (
      config.url == "/autoCheck/autoCheck/screenshot" ||
      config.url == "/sys/oss/uploadList" ||
      config.url == "/scan/autoScanLog/task" ||
      config.url == "/autoCheck/autoCheck/getAttr"
    ) {
      config.timeout = 120000;
    }
    if (app.tenantMode === "code") {
      config.headers["tenantCode"] = getCache(CacheTenantCode, { isParse: false }) || "";
    }
    const token = getToken();
    if (token) {
      config.headers["token"] = token;
    }
    if (config.method?.toUpperCase() === "GET") {
      config.params = { ...config.params, _t: new Date().getTime() };
    }
    if (Object.values(config.headers).includes("application/x-www-form-urlencoded")) {
      config.data = qs.stringify(config.data);
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);
http.interceptors.response.use(
  (response) => {
    removePending(response.config);
    // 响应成功
    if (response.data.code === 0 || response.data.type == "application/vnd.ms-excel") {
      return response;
    }

     // 错误提示
     if (response.data.msg && !response.config.url?.includes("im/login/token")) {
      if (!shownErrors.value.has(response.data.msg)) {
        // 显示错误提示（这里以Element Plus为例）
        if(response.data.msg == '请先设置部门负责人!'){
          ElMessage.warning(response.data.msg);
        }else{
          ElMessage.error(response.data.msg);
        }
        
        shownErrors.value.add(response.data.msg);
        
        // 可选：一段时间后从缓存中移除，允许再次显示相同错误
        setTimeout(() => {
          shownErrors.value.delete(response.data.msg);
        }, 1000); // 5秒后清除
      }
    }

    if (response.data.code === 401) {
      //自定义业务状态码
      redirectLogin();
    }

    return Promise.reject(new Error(response.data.msg || "Error"));
  },
  (error) => {
    error.config && removePending(error.config);
    console.log();
    
    const status = getValueByKeys(error, "response.status", 500);
    const httpCodeLabel: IObject<string> = {
      400: "请求参数错误",
      401: "未授权，请登录",
      403: "拒绝访问",
      404: `请求地址出错: ${getValueByKeys(error, "response.config.url", "")}`,
      408: "请求超时",
      500: "API接口报500错误",
      501: "服务未实现",
      502: "网关错误",
      503: "服务不可用",
      504: "网关超时",
      505: "HTTP版本不受支持"
    };
    if (error && error.response) {
      console.error("请求错误", error.response.data);
    }
    if (status === 401) {
      redirectLogin();
    }
    return Promise.reject(new Error(httpCodeLabel[status] || "接口错误"));
  }
);

const redirectLogin = () => {
  router.replace("/login");
  return;
};

const pendingMap = new Map();

/**
 * 删除重复的请求
 */
function removePending(config: AxiosRequestConfig) {
  const pendingKey = getPendingKey(config);
  if (pendingMap.has(pendingKey)) {
    const cancelToken = pendingMap.get(pendingKey);
    cancelToken(pendingKey);
    pendingMap.delete(pendingKey);
  }
}

/**
 * 生成每个请求的唯一key
 */
function getPendingKey(config: AxiosRequestConfig) {
  let { data } = config;
  const { url, method, params, headers } = config;
  if (typeof data === "string") data = JSON.parse(data); // response里面返回的config.data是个字符串对象
  return [
    url,
    method,
    headers && (headers as any).token ? (headers as any).token : "",
    headers && (headers as any)["ba-user-token"] ? (headers as any)["ba-user-token"] : "",
    JSON.stringify(params),
    JSON.stringify(data)
  ].join("&");
}

/**
 * 储存每个请求的唯一cancel回调, 以此为标识
 */
function addPending(config: AxiosRequestConfig) {
  const pendingKey = getPendingKey(config);
  config.cancelToken =
    config.cancelToken ||
    new axios.CancelToken((cancel) => {
      if (!pendingMap.has(pendingKey)) {
        pendingMap.set(pendingKey, cancel);
      }
    });
}

export default (o: AxiosRequestConfig): Promise<IHttpResponse> => {
  return new Promise((resolve, reject) => {
    http(o)
      .then((res) => {
        return resolve(res.data);
      })
      .catch(reject);
  });
};
