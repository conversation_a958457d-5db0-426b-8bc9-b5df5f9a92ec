<template>
  <el-dialog width="800px" v-model="dialogVisible" class="sys-member-group-dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="create-group-header">
        <p class="dialog-title">选择部门负责人</p>
      </div>
    </template>

    <div class="sys-member-group-dialog-content flx">
      <div class="left-container">
        <div class="search-container">
          <el-input v-model="searchKeywords" placeholder="请输入姓名进行搜索" :prefix-icon="Search" clearable @input="inputChange(false)" @keyup.enter="inputChange(true)" />
        </div>

        <!-- 选择成员 -->
        <div class="select-member card">
          <div class="card-title flx-justify-between">
            选择成员
            <div class="right-btn" v-if="!searchKeywords">
              <div class="btn" v-for="(item, index) in ['角色', '部门']" :key="index" :class="{ active: currentType == index }" @click="typeTabsChange(index)">
                {{ item }}
              </div>
            </div>
          </div>

          <div class="member-content flx">
            <el-scrollbar class="role" v-if="!searchKeywords">
              <!-- 角色 -->
              <template v-if="currentType == 0 && roles.length > 0">
                <div class="role-item" v-for="(item, index) in roles" :key="index" :class="{ active: index == currentRoleIndex }" @click="roleSelectChange(index)">
                  {{ item.name }}
                </div>
              </template>
              <div v-else-if="roles.length < 1" style="height: 100px; line-height: 100px; text-align: center; color: gray">暂无数据</div>

              <!-- 部门 -->
              <template v-else>
                <el-tree :expand-on-click-node="false" :data="deptTree" :props="{ label: 'name' }" default-expand-all @node-click="handleNodeClick"></el-tree>
              </template>
            </el-scrollbar>

            <div style="flex: 1; height: 340px">
              <div v-if="memberList.length < 1" style="height: 100px; line-height: 100px; text-align: center; color: gray">暂无数据</div>
              <el-scrollbar v-else class="select-member-list" style="margin-top: 8px; margin-left: 0">
                <el-radio-group v-model="checkedMember" @change="handdleChange">
                  <el-radio v-for="(member, index) in memberList" :key="index" :label="member.realName" :value="member.id">
                    <div class="member-item flx-align-center">
                      <img class="avatar" v-if="member.headUrl" :src="member.headUrl" />
                      <img v-else-if="member.gender == 1" src="https://oss.nyyyds.com/upload/20250710/2033ce6ac6fb44849a62b354be9caa59.webp" class="avatar" />
                      <img v-else src="https://oss.nyyyds.com/upload/20250710/c3d902a47bb74d4a882e447bb3b26c05.webp" class="avatar" />
                      <span class="sle">{{ member.realName || "-" }}</span>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>

      <div class="right-container flx-column">
        <div class="selected flx-justify-between">
          <div class="selected-count">
            已选 (<span class="count">{{ checkedMember ? 1 : 0 }}</span
            ><span>/1</span>)
          </div>
          <div class="clear pointer text-primary" @click="emptied">清空</div>
        </div>

        <el-scrollbar class="select-member-list">
          <div class="flx member-item-wrap">
            <div class="member-item" v-for="(item, index) in checkedMemberList" :key="index">
              {{ item.realName }}
              <el-icon style="cursor: pointer" @click="delMember(index)"><Close /></el-icon>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div class="flx-justify-end create-group-footer">
      <el-button :loading="btnLoading" @click="handleCancel">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineProps, computed } from "vue";
import { Search, Close } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store/index";
import { useImStore } from "@/store/im";
import { useSettingStore } from "@/store/setting";
const emits = defineEmits(["checkedMember"]);
defineProps({
  title: {
    type: String,
    default: "选择人员"
  }
});

const dialogVisible = ref(false);
const checkedMemberList = ref([]);
const memberList = ref([]);

const init = (backdata?: any) => {
  searchKeywords.value = undefined;
  dialogVisible.value = true;
  memberList.value = [];
  checkedMember.value = backdata.id || undefined;
  checkedMemberList.value = [];
  baseService
    .get("/sys/user/page", {
      limit: 9999,
      page: 1
    })
    .then((res) => {
      memberList.value = res.data.list || [];
      if (checkedMember.value) {
        handdleChange();
      }
    });
  getRoles();
};

// 按昵称搜索
const searchKeywords = ref("");
const searchLoading = ref(false);
const inputChange = (loading: boolean) => {
  searchLoading.value = loading;
  memberList.value = [];
  baseService
    .get("/sys/user/page", {
      limit: 9999,
      page: 1,
      realName: searchKeywords.value
    })
    .then((res) => {
      memberList.value = res.data.list || [];
    });
};

// 选择成员 筛选类型
const currentType = ref(0);
const typeTabsChange = (index: number) => {
  currentType.value = index;
  if (currentType.value == 0) {
    roleSelectChange(0);
  } else {
    getdepts();
  }
};

// 角色列表
const currentRoleIndex = ref();
const roles = ref(<any>[]);
const dataLoading = ref(false);

const getRoles = () => {
  dataLoading.value = true;
  memberList.value = [];
  baseService
    .get("/sys/role/all/list")
    .then((res: any) => {
      roles.value = res.data;
    })
    .finally(() => {
      dataLoading.value = false;
    });
};
const roleSelectChange = (index: number) => {
  currentRoleIndex.value = index;
  memberList.value = roles.value[index].users || [];
};

// 部门列表
const deptTree = ref([]);
const getdepts = () => {
  baseService.get("/sys/dept/list").then((res: any) => {
    deptTree.value = res.data;
  });
};
const handleNodeClick = (node: any) => {
  memberList.value = [];
  baseService
    .get("/sys/user/page", {
      limit: 9999,
      page: 1
    })
    .then((res) => {
      memberList.value = res.data.list || [];
    });
};

// 已选中的人员
const checkedMember = ref();
const handdleChange = () => {
  console.log(checkedMember.value);

  checkedMemberList.value = memberList.value.filter((ele) => ele.id == checkedMember.value);
};
// 清空
const emptied = () => {
  checkedMember.value = "";
};
// 删除已选
const delMember = (index) => {
  checkedMember.value = "";
  checkedMemberList.value = [];
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

// 提交
const btnLoading = ref(false);
const formRef = ref();
const handleSubmit = () => {
  if (!checkedMemberList.value || !checkedMemberList.value.length) {
    return ElMessage.warning("请选择");
  }
  dialogVisible.value = false;
  emits("commitHandle", checkedMemberList.value[0]);
};

defineExpose({
  init
});
</script>

<style lang="scss">
.sys-member-group-dialog {
  padding: 0;

  .el-dialog__header {
    padding-right: 0;
  }
}
.create-group-header {
  height: 56px;
  line-height: 56px;
  box-shadow: inset 0px -1px 0px 0px #f0f0f0;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: bold;
  padding-right: 0;
}

.sys-member-group-dialog-content {
  padding: 16px;
  padding-top: 0;
  .left-container {
    width: 475px;
    padding-right: 16px;
    border-right: 1px solid rgba(186, 191, 197, 0.4);

    .card {
      margin-top: 8px;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid rgba(186, 191, 197, 0.4);

      .member-item {
        height: 32px;
        line-height: 32px;

        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 4px;
        }

        span {
          min-width: calc(100% - 35px);
          margin-left: 12px;
        }
      }

      &.recent-contacts {
        height: 118px;

        .contacts-list {
          display: flex;
          flex-wrap: wrap;

          .checkbox {
            margin-top: 8px;
            width: 25%;
            margin-right: 0;

            .el-checkbox__label {
              width: calc(100% - 14px);
            }
          }
        }
      }

      &.select-member {
        height: 409px;

        .right-btn {
          display: flex;
          background: #f5f6f7;
          border: 1px solid rgba(186, 191, 197, 0.4);
          border-radius: 4px;

          .btn {
            line-height: 24px;
            width: 60px;
            text-align: center;
            color: rgba(0, 0, 0, 0.6);
            cursor: pointer;

            &.active {
              color: var(--el-color-primary);
              background: #fff;
              border-radius: 4px;
            }
          }
        }

        .member-content {
          padding-top: 8px;

          .role {
            width: 220px;
            height: 340px;
            margin-right: 10px;

            .role-item {
              height: 36px;
              line-height: 36px;
              padding-left: 16px;
              cursor: pointer;

              &.active,
              &:hover {
                background: #f5f6f7;
              }
            }
          }

          .el-tree-node__content {
            height: 36px;
            line-height: 36px;
          }

          .select-member-list {
            flex: 1;
            height: 320px;
            overflow-x: hidden;

            .checkbox {
              width: 100%;
              margin-bottom: 8px;
            }

            .el-radio {
              height: 36px;
              width: 100%;
              padding-right: 0;
              margin-right: 0;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }

  .right-container {
    flex: 1;
    margin-left: 16px;
    height: 440px;

    .selected {
      height: 36px;
      color: rgba(0, 0, 0, 0.6);
      border-bottom: 1px solid rgba(186, 191, 197, 0.4);

      .count {
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.8);
      }
    }

    .el-radio {
      margin-right: 8px !important;
      padding-right: 8px !important;
    }

    .select-member-list {
      height: 290px;

      .member-item-wrap {
        flex-wrap: wrap;

        .member-item {
          display: flex;
          align-items: center;
          margin: 8px 8px 0 0;
          background: #f5f6f7;
          border-radius: 4px;
          height: 30px;
          color: rgba(0, 0, 0, 0.6);
          padding: 0 8px;

          .el-icon {
            margin-left: 5px;
          }
        }
      }
    }

    .el-form-item:last-child {
      margin-bottom: 0;
    }
  }
}

.create-group-footer {
  padding: 0 16px 16px 0;
}
</style>
