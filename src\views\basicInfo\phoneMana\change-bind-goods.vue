<template>
  <el-dialog v-model="visible" width="500" title="更换绑定商品" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 188px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item style="margin-bottom: 8px" prop="shopId">
        <template #label>
          <div style="display: flex; justify-content: space-between; width: 100%">
            <span style="color: #606266">选择商品</span>
          </div>
        </template>
        <el-select :loading="loading" clearable v-model="dataForm.shopId" placeholder="请输入关键字搜索商品" filterable remote :remote-method="remoteMethod">
          <el-option style="width: 468px" v-for="item in shopList" :key="item.id" :label="item.title1" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

let dataForm = reactive({
  shopId: <any>[]
});

const rules = ref({
  shopId: [{ required: true, message: "请选择商品", trigger: "blur" }]
});

const init = (obj: any) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  
  obj.id = obj.phoneGameId;
  dataForm = Object.assign(dataForm, obj);
  dataForm.shopId = undefined;
};

const loading = ref(false);
const shopList = ref([]);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    baseService
      .post("/shop/shop/search", {
        page: 1,
        limit: 9999,
        size: 9999,
        queryType: "1",
        gameId: "",
        search: query,
        statusStr: "1,2,3",
        start: "",
        end: ""
      })
      .then((res) => {
        shopList.value = res.data?.list;
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    shopList.value = [];
  }
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let form = { ...dataForm };
    let obj = shopList.value.find((ele) => ele.id == dataForm.shopId);
    form.shopName = obj.title1;
    baseService.put("/phone/sysphonegame", form).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: block;
  :deep(.el-form-item__label) {
    width: 100%;
    padding-right: 0;
  }
}
</style>
