<template>
  <div class="container bargain-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '收款审核', value: '5' },
          { label: '放款审核', value: '1' },
          { label: '退款审核', value: '2' },
          { label: '其他收支', value: '6' },
          { label: '提现审核', value: '3' }
        ]"
        v-model="tabsTypeValue"
        value="value"
        label="label"
        @change="tabsTypeChange"
      ></ny-flod-tab>

      <!-- 数据统计 -->
      <ul class="headCardUl" v-if="tabsTypeValue == '1' || tabsTypeValue == '2'">
        <li class="headCardLi" v-for="(cardItem, index) in cardInfo" :key="index" :style="cardItem.bg">
          <!-- 头部 -->
          <!-- 去支付点击切换tab到已通过，走今日的，支付状态为待支付的，已通过的筛选 -->
          <div class="head flex">
            <div class="flex">
              <span>{{ cardItem.top.label }}</span>
            </div>
            <div>
              <template v-if="cardItem.top.hasButton">
                <el-button style="margin-left: 12px" @click="btnItem.handle" :type="btnItem.type" v-for="(btnItem, btnI) in cardItem.top.bottomList" :key="btnI">{{ btnItem.text }}</el-button>
              </template>
              <div class="stat_top_time" v-else>
                <el-date-picker :clearable="false" type="date" :value-format="'YYYY-MM-DD'" :format="'MM/DD'" style="width: 70px" v-model="cardItem.top.dateValue" @change="topDateChange($event, index)" />
              </div>
            </div>
          </div>
          <!-- 数值 -->
          <div class="middle flex">
            <div style="width: 50%" v-for="(midelItem, mI) in cardItem.middle" :key="mI">
              <el-statistic :value-style="{ color: midelItem.color || '#303133' }" :value="midelItem.data" />
            </div>
          </div>
          <!-- 尾部 -->
          <div class="bottom flex">
            <template v-if="cardItem.bottom.hasBottom && (state.dataForm.type != '5' || index != 1)">
              <div style="display: flex; align-items: center">
                <span>{{ cardItem.bottom.tip }}</span>
                <el-statistic :value-style="'font-weight: 400;font-size: 12px;height: 20px;display: -webkit-box;color:' + cardItem.bottom.color" :value="cardItem.bottom.data" />
              </div>

              <el-link v-if="cardItem.bottom.label" :type="cardItem.bottom.type" :underline="false" @click="cardItem.bottom.handle"
                >{{ cardItem.bottom.label }}<el-icon size="15"><ArrowRight /></el-icon
              ></el-link>
            </template>
          </div>
        </li>
      </ul>
      <auditStat
        ref="auditStatRef"
        @Examine="
          state.dataForm.status = 0;
          state.getDataList();
        "
        v-if="tabsTypeValue == '5'"
      />
      
      <!-- 其他收支统计 -->
      <!-- <otherCard ref="otherCardRef"
        @Examine="ExamineChange"
        v-if="tabsTypeValue == '6'"
      /> -->

      <!-- 除了其他收之外的表格 -->
      <template v-if="state.dataForm.type != '6'">
        <template v-for="tItem in [1, 2, 3, 4, 5]">
          <ny-table
            noDataType="3"
            cellHeight="ch-96"
            v-if="state.dataForm.type == tItem"
            :key="tItem"
            :state="state"
            :columns="tableColums[+state.dataForm.type]"
            :routePath="'/finance/financialAudit/index' + tItem"
            @pageSizeChange="state.pageSizeChangeHandle"
            @pageCurrentChange="state.pageCurrentChangeHandle"
            @selectionChange="state.dataListSelectionChangeHandle"
            @sortableChange="sortableChange"
          >
            <template #header>
              <ny-button-group
                label="label"
                value="value"
                :list="stateList"
                v-model="state.dataForm.status"
                @change="
                  () => {
                    state.getDataList();
                  }
                "
                v-if="tabsTypeValue != '1'"
              ></ny-button-group>
              <ny-button-group label="label" value="value" :list="lendingList" v-model="lendingValue" @change="lendingChange" v-else></ny-button-group>
            </template>

            <template #header-right>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <div style="display: flex; gap: 8px">
                  <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
                  <el-input :prefix-icon="Search" style="width: 240px !important" v-model="state.dataForm.keywords" placeholder="请输入订单号/商品编号" clearable> </el-input>
                  <el-select v-if="state.dataForm.type != '5'" v-model="state.dataForm.payStatus" placeholder="支付状态" clearable style="width: 160px !important">
                    <el-option v-for="(item, index) in payStateList" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                  <el-select v-if="state.dataForm.type == '1' || state.dataForm.type == '4'" v-model="state.dataForm.status" placeholder="审核状态" clearable style="width: 160px !important">
                    <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                  <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                  <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </div>
              </el-form>
            </template>

            <!-- 订单编号 -->
            <!-- 1,4   放款和销售-->
            <template #orderSn="{ row }">
              <div class="linkSty">
                <el-link :underline="false" v-copy="row.orderSn"
                  >{{ row.orderSn }}<el-icon class="copyIcon"><DocumentCopy /></el-icon>
                </el-link>
              </div>
            </template>
            <!-- 2,3 退款和提现 -->
            <template #orderCode="{ row }">
              <div class="linkSty">
                <el-link :underline="false" v-copy="row.orderCode"
                  >{{ row.orderCode }}<el-icon class="copyIcon"><DocumentCopy /></el-icon>
                </el-link>
              </div>
            </template>
            <!-- 5 线下收款 -->
            <template #orderCode2="{ row }">
              <div class="linkSty">
                <!-- <el-link @click="orderCheckHandle({ ...row }, false, false, false)" :underline="false" type="primary">{{ row.orderCode }} </el-link> -->
                <el-link :underline="false">
                  {{ row.orderCode }}
                  <el-icon v-copy="row.orderCode" class="copyIcon"><DocumentCopy /></el-icon>
                </el-link>
              </div>
            </template>
            <template #contractId="{ row }">
              <el-button v-if="row.contractId" link type="primary" @click="previewHandle(row.contractId)">查看</el-button>
              <span v-else>-</span>
            </template>
            <template #orderType="{ row }">
              <span v-if="row.orderType == 4">回收订单</span>
              <span v-if="row.orderType == 5">销售订单</span>
              <span v-if="row.orderType == 6">售后订单</span>
              <span v-if="row.orderType == 8">合作商订单</span>
            </template>
            <!-- 商品标题 -->
            <template #shopTitle="{ row }">
              <div class="shoping">
                <el-image style="height: 68px; width: 120px" :src="row.img" :preview-src-list="[row.img]" preview-teleported fit="cover" />
                <div class="info">
                  <div class="title mle" v-html="row.shopTitle" @click="jumpGoodsDetails(row)"></div>
                  <div class="sle" style="width: 185px; text-align: left">
                    {{ `${row.gameName || "-"} / ${row.serverName || "-"}` }}
                  </div>
                  <div>
                    <span>{{ state.getDictLabel("shop_compensation", row.compensation) }}</span>
                  </div>
                </div>
              </div>
            </template>
            <!-- 支付状态 -->
            <template #payStatus="{ row }">
              <template v-if="row.status == 1">
                <el-tag v-if="row.payStatus == 1" type="success">已支付</el-tag>
                <el-tag v-else-if="row.payStatus == 2" type="danger">支付失败</el-tag>
                <el-tag v-else-if="row.payStatus == 0" type="warning">待支付</el-tag>
              </template>
            </template>
            <!-- 状态 -->
            <template #status="{ row }">
              <el-tag v-if="row.status == 1" type="success">已通过</el-tag>
              <el-tag v-else-if="row.status == 0" type="warning">待审核</el-tag>
              <el-tag v-else-if="row.status == 2" type="danger">已拒绝</el-tag>
            </template>
            <!-- 线下收款确认状态 -->
            <template #status2="{ row }">
              <el-tag v-if="row.status == 1" type="success">已确认</el-tag>
              <el-tag v-else-if="row.status == 0" type="warning">待审核</el-tag>
              <el-tag v-else-if="row.status == 2" type="danger">已拒绝</el-tag>
            </template>

            <template #operate="{ row }">
              <el-button v-if="row.status == 0 && state.hasPermission('finance:financialAudit:Audit')" type="warning" text bg @click="auditDrawerRefHandle(row, false)"> 审核 </el-button>
              <el-button v-if="row.status == 1 && row.payStatus == 0" type="warning" text bg @click="payWayRefHandle(row.id)"> 付款 </el-button>
              <el-button v-if="row.status != 0" type="primary" text bg @click="auditDrawerRefHandle(row, true)"> 详情 </el-button>
              <!-- <el-button type="info" text bg @click="state.deleteHandle(row.id)"> 日志 </el-button> -->
            </template>
            <!-- 线下收款确认操作 -->
            <template #operate2="{ row }">
              <el-button v-if="row.status == 0 && state.hasPermission('finance:financialAudit:Audit')" type="warning" text bg @click="auditDrawerRefHandle(row, false)"> 审核 </el-button>
              <el-button v-if="row.status != 0 && row.orderType == 6" type="primary" text bg @click="orderCheckHandle({ ...row }, true, false, true)"> 详情 </el-button>
              <el-button v-if="row.status != 0 && row.orderType == 4" type="primary" text bg @click="auditDrawerRefHandle(row, true)"> 详情 </el-button>
            </template>
            <template #createDate="{ row }">
              <span>{{ formatTimeStamp(row.createDate) }}</span>
            </template>
            <template #footer>
              <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
                <span style="font-weight: bold; color: #1d2129">{{ ["", "申请金额", "退款金额", "提现金额", "申请金额", "应收金额"][tItem] }}</span>
                <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
                <span>合计={{ getSummaries() }}</span>
              </div>
            </template>
          </ny-table>
        </template>
      </template>
      <!-- 其他收支 -->
      <template v-if="state.dataForm.type == '6' || state.dataForm.type == '7'">
        <template v-for="tItem in [0, 1]">
          <ny-table
            noDataType="3"
            cellHeight="ch-56"
            v-if="otherTypeVlaue == tItem"
            :key="tItem"
            :state="state"
            :columns="tableColumsOther[+otherTypeVlaue]"
            :routePath="'/finance/financialAudit/index' + tItem"
            @pageSizeChange="state.pageSizeChangeHandle"
            @pageCurrentChange="state.pageCurrentChangeHandle"
            @selectionChange="state.dataListSelectionChangeHandle"
            @sortableChange="sortableChange"
          >
            <template #header>
              <ny-button-group label="label" value="value" :list="stateListOther" v-model="otherTypeVlaue" @change="otherTypeChange"></ny-button-group>
            </template>

            <template #header-right>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <div style="display: flex; gap: 8px">
                  <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
                  <el-input :prefix-icon="Search" style="width: 240px !important" v-model="state.dataForm.keywords" placeholder="请输入订单号/商品编号" clearable> </el-input>
                  <el-select v-model="state.dataForm.status" placeholder="审核状态" clearable style="width: 160px !important">
                    <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                  <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                  <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
                </div>
              </el-form>
            </template>

            <!-- 关联商品 -->
            <template #shopCode="{ row }">
              <el-text type="primary" text class="pointer" @click="jumpGoodsDetails(row)">{{ row.shopCode }}</el-text>
            </template>

            <!-- 开销凭证 -->
            <template #voucher="{ row }">
              <span v-if="!row.voucher || row.voucher.length < 1">-</span>
              <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px" v-else>
                <el-image style="width: 64px; height: 64px" :src="item" v-for="item in row.voucher.split(',')" alt="" preview-teleported :preview-src-list="row.voucher.split(',')" />
              </div>
            </template>

            <!-- 发票 -->
            <template #invoice="{ row }">
              <span v-if="!row.invoice || row.invoice.length < 1">-</span>
              <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px" v-else>
                <el-image style="width: 64px; height: 64px" :src="item" v-for="item in row.invoice.split(',')" alt="" preview-teleported :preview-src-list="row.invoice.split(',')" />
              </div>
            </template>

            <!-- 收款信息 -->
            <template #checkView="{ row }">
              <el-text type="primary" text class="pointer" @click="checkDetail(row)">{{ row.checkView || "-" }}</el-text>
            </template>

            <!-- 收款账号 -->
            <template #account="{ row }">
              <div v-if="row.account" style="display: flex; align-items: center; justify-content: center; gap: 8px">
                <span>{{ row.account }}</span>
                <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M21.9224 15.3576C18.0929 14.2048 15.8667 13.5176 15.244 13.296C15.812 12.32 16.268 11.2 16.564 9.976H13.3V8.872H17.3V8.192H13.3V6.344H11.764C11.484 6.344 11.452 6.592 11.452 6.592V8.184H7.7V8.864H11.452V9.968H8.38V10.584H14.604C14.38 11.36 14.076 12.096 13.716 12.76C12.308 12.296 11.524 11.976 9.804 11.816C6.548 11.504 5.796 13.296 5.676 14.392C5.5 16.064 6.98 17.424 9.188 17.424C11.396 17.424 12.868 16.4 14.268 14.704C15.4346 15.2619 17.6059 16.2293 20.7819 17.6062C18.9835 20.2577 15.9452 22 12.5 22C6.97715 22 2.5 17.5228 2.5 12C2.5 6.47715 6.97715 2 12.5 2C18.0228 2 22.5 6.47715 22.5 12C22.5 13.1778 22.2964 14.3081 21.9224 15.3576ZM8.932 16.368C6.596 16.368 6.228 14.888 6.348 14.272C6.468 13.656 7.148 12.856 8.452 12.856C9.948 12.856 11.284 13.24 12.892 14.016C11.756 15.496 10.372 16.368 8.932 16.368Z"
                    fill="#165DFF"
                  />
                </svg>
              </div>
            </template>

            <!-- 支付方式 -->
            <template #payType="{ row }">
              <span>{{ ["支付宝", "微信", "银行卡"][row.payType - 1] }}</span>
            </template>

            <!-- 是否需要发票 -->
            <template #whetherHasInvoice="{ row }">
              {{ row.whetherHasInvoice ? "是" : "否" }}
            </template>

            <template #status="{ row }">
              <el-tag v-if="row.status == 1" type="success">已通过</el-tag>
              <el-tag v-else-if="row.status == 0" type="warning">待审核</el-tag>
              <el-tag v-else-if="row.status == 2" type="danger">已拒绝</el-tag>
            </template>

            <template #operate="{ row }">
              <el-button v-if="row.status == 0" type="warning" text bg @click="auditDrawerRefHandle(row, false)"> 审核 </el-button>
              <el-button v-if="row.status != 0" type="primary" text bg @click="auditDrawerRefHandle(row, true)"> 详情 </el-button>
              <!-- <el-button type="info" text bg @click="state.deleteHandle(row.id)"> 日志 </el-button> -->
            </template>

            <template #createDate="{ row }">
              <span>{{ formatTimeStamp(row.createDate) }}</span>
            </template>
            <template #footer>
              <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
                <span style="font-weight: bold; color: #1d2129">{{ ["支出金额", "收入金额"][+otherTypeVlaue] }}</span>
                <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
                <span>合计={{ getSummaries() }}</span>
              </div>
            </template>
          </ny-table>
        </template>
      </template>
      <!-- 审核 -->
      <auditDrawer @refreshDataList="refreshDataList" ref="auditDrawerRef"></auditDrawer>
      <!-- 付款 -->
      <payWay @refreshDataList="refreshDataList" ref="payWayRef"></payWay>
    </el-card>
    <el-dialog v-model="dialogForm.visible" width="500">
      <template #header>
        <span style="font-weight: bold; font-size: 18px; color: #303133"
          ><el-text type="primary"
            ><el-icon><InfoFilled /></el-icon
          ></el-text>
          线下收款确认</span
        >
      </template>
      <div style="margin-top: 8px">请确认财务系统是否已成功接收该笔款项。</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="
              dialogForm.visible = false;
              dialogForm.id = undefined;
            "
            >取消</el-button
          >
          <el-button type="primary" @click="submitHandle"> 确认收款 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
    <!-- 收款信息 -->
    <detail ref="detailRef" :key="detailKey" />
    <!-- 售后订单处理 -->
    <ProcessingComplete ref="orderProcessingRef" @refresh="state.getDataList()"></ProcessingComplete>
  </div>
</template>

<script lang="ts" setup>
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, DocumentDelete, DocumentCopy } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import { BigNumber } from "bignumber.js";
import auditDrawer from "./auditDrawer.vue";
import payWay from "./payWay.vue";
import auditStat from "./auditStat.vue";
import ShopInfo from "@/views/shop/shop-info.vue";
import detail from "../otherIandE/detail.vue";
import ProcessingComplete from "@/views/order/afterSales/components/ProcessingComplete.vue";
import otherCard from "./otherCard.vue";
const auditDrawerRef = ref();
const otherCardRef = ref();
const payWayRef = ref();
const tabsTypeValue = ref("5");
const otherTypeVlaue = ref(0); // 其他收支
const lendingValue = ref("1"); // 放款审核

// 表格配置项
const tableColums = {
  1: [
    {
      prop: "orderSn",
      label: "订单号",
      minWidth: 220
    },
    {
      prop: "orderType",
      label: "申请类型",
      minWidth: 152
    },
    {
      prop: "shopCode",
      label: "商品编码",
      width: 120
    },
    {
      prop: "gameAccount",
      label: "游戏账号",
      width: 120
    },
    {
      prop: "amount",
      label: "申请金额(元)",
      width: 136,
      sortable: "custom"
    },
    {
      prop: "accountName",
      label: "账户名称",
      minWidth: 184
    },
    {
      prop: "account",
      label: "收款账户",
      minWidth: 184
    },
    {
      prop: "contractId",
      label: "合同",
      width: 136
    },
    {
      prop: "creatorName",
      label: "申请人",
      minWidth: 104
    },
    {
      prop: "requestDeptName",
      label: "所属部门",
      minWidth: 104
    },
    {
      prop: "createDate",
      label: "申请时间",
      minWidth: 180
    },
    {
      prop: "remarks",
      label: "申请备注",
      width: 180
    },
    {
      prop: "status",
      label: "审核状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "payStatus",
      label: "支付状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "operate",
      label: "操作",
      width: 136,
      fixed: "right"
    }
  ],
  2: [
    {
      prop: "orderSn",
      label: "订单号",
      minWidth: 220
    },
    {
      prop: "saleOrderType",
      label: "申请类型",
      minWidth: 152
    },
    {
      prop: "shopCode",
      label: "商品编码",
      width: 136
    },
    {
      prop: "shopTitle",
      label: "商品信息",
      width: 340
    },
    {
      prop: "gameAccount",
      label: "游戏账号",
      width: 136
    },
    {
      prop: "amount",
      label: "退款金额(元)",
      width: 136,
      sortable: "custom"
    },
    {
      prop: "accountName",
      label: "账户名称",
      minWidth: 184
    },
    {
      prop: "account",
      label: "收款账户",
      minWidth: 184
    },
    {
      prop: "creatorName",
      label: "申请人",
      minWidth: 104
    },
    {
      prop: "requestDeptName",
      label: "所属部门",
      minWidth: 104
    },
    {
      prop: "createDate",
      label: "申请时间",
      minWidth: 180
    },
    // {
    //   prop: "contractId",
    //   label: "合同",
    //   width: 136
    // },
    {
      prop: "remarks",
      label: "申请备注",
      width: 136
    },
    {
      prop: "status",
      label: "审核状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "payStatus",
      label: "支付状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "operate",
      label: "操作",
      width: 136,
      fixed: "right"
    }
  ],
  3: [
    {
      prop: "orderCode",
      label: "提现单号",
      minWidth: 220
    },
    {
      prop: "tenantName",
      label: "合作商名称",
      minWidth: 184
    },
    {
      prop: "account",
      label: "收款账户",
      minWidth: 136
    },
    {
      prop: "createDate",
      label: "申请时间",
      minWidth: 180
    },
    {
      prop: "amount",
      label: "提现金额(元)",
      minWidth: 184,
      sortable: "custom"
    },
    {
      prop: "realAmount",
      label: "支付金额(元)",
      minWidth: 184,
      sortable: "custom"
    },
    {
      prop: "remarks",
      label: "备注",
      width: 136
    },
    {
      prop: "status",
      label: "状态",
      width: 136
    },
    {
      prop: "payStatus",
      label: "支付状态",
      width: 136
    },
    {
      prop: "operate",
      label: "操作",
      width: 136,
      fixed: "right"
    }
  ],
  4: [
    {
      prop: "orderSn",
      label: "订单号",
      minWidth: 220
    },
    {
      prop: "shopCode",
      label: "商品编码",
      width: 136
    },
    {
      prop: "shopTitle",
      label: "商品信息",
      width: 340
    },
    {
      prop: "gameAccount",
      label: "游戏账号",
      width: 136
      // sortable: "custom"
    },
    {
      prop: "amount",
      label: "申请金额(元)",
      width: 136,
      sortable: "custom"
    },
    {
      prop: "accountName",
      label: "账户名称",
      minWidth: 184
    },
    {
      prop: "account",
      label: "收款账户",
      minWidth: 184
    },
    {
      prop: "contractId",
      label: "合同",
      width: 136
    },
    {
      prop: "creatorName",
      label: "申请人",
      minWidth: 104
    },
    {
      prop: "requestDeptName",
      label: "所属部门",
      minWidth: 104
    },
    {
      prop: "createDate",
      label: "申请时间",
      minWidth: 180
    },
    {
      prop: "remarks",
      label: "申请备注",
      width: 136
    },
    {
      prop: "status",
      label: "审核状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "payStatus",
      label: "支付状态",
      width: 136,
      fixed: "right"
    },
    {
      prop: "operate",
      label: "操作",
      width: 136,
      fixed: "right"
    }
  ],
  5: [
    {
      prop: "orderType",
      label: "订单来源",
      minWidth: 152
    },
    {
      prop: "orderSn",
      label: "订单号",
      minWidth: 180
    },
    {
      prop: "shopTitle",
      label: "商品信息",
      width: 340
    },
    {
      prop: "creatorName",
      label: "提交人",
      minWidth: 104
    },
    {
      prop: "amount",
      label: "应收金额(元)",
      width: 136,
      sortable: "custom"
    },
    {
      prop: "createDate",
      label: "提交时间",
      minWidth: 136
    },
    {
      prop: "status2",
      label: "审核状态",
      width: 136
    },
    {
      prop: "accountName",
      label: "支付方式",
      minWidth: 136
    },
    // {
    //   prop: "account",
    //   label: "收款账号",
    //   minWidth: 184
    // },
    {
      prop: "operate2",
      label: "操作",
      width: 136
    }
  ]
};
const tableColumsOther = reactive({
  0: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "name",
      label: "费用名称",
      minWidth: 160
    },
    {
      prop: "amount",
      label: "支出金额(元)",
      minWidth: 160
    },
    {
      prop: "payType",
      label: "支付方式",
      minWidth: 160
    },
    {
      prop: "requestDeptName",
      label: "申请部门",
      minWidth: 160
    },
    {
      prop: "creatorName",
      label: "提交人",
      minWidth: 160
    },
    {
      prop: "createDate",
      label: "提交时间",
      minWidth: 160
    },
    {
      prop: "voucher",
      label: "开销凭证",
      minWidth: 160
    },
    {
      prop: "invoice",
      label: "发票",
      minWidth: 160
    },
    {
      prop: "shopCode",
      label: "关联商品",
      width: 160
    },
    {
      prop: "checkView",
      label: "收款信息",
      minWidth: 160
    },
    {
      prop: "remarks",
      label: "备注",
      width: 200
    },
    {
      prop: "status",
      label: "审核状态",
      width: 120,
      fixed: "right"
    },
    {
      prop: "operate",
      label: "操作",
      fixed: "right",
      width: 160
    }
  ],
  1: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "name",
      label: "费用名称",
      minWidth: 160
    },
    {
      prop: "amount",
      label: "收入金额(元)",
      minWidth: 160
    },
    {
      prop: "requestDeptName",
      label: "申请部门",
      minWidth: 160
    },
    {
      prop: "creatorName",
      label: "提交人",
      minWidth: 160
    },
    {
      prop: "createDate",
      label: "提交时间",
      minWidth: 160
    },
    {
      prop: "voucher",
      label: "收入凭证",
      minWidth: 160
    },
    {
      prop: "whetherHasInvoice",
      label: "是否需要发票",
      minWidth: 160
    },
    {
      prop: "shopCode",
      label: "关联商品",
      width: 160
    },
    {
      prop: "account",
      label: "收款账号",
      minWidth: 160
    },
    {
      prop: "remarks",
      label: "备注",
      width: 200
    },
    {
      prop: "status",
      label: "审核状态",
      width: 120,
      fixed: "right"
    },
    {
      prop: "operate",
      label: "操作",
      fixed: "right",
      width: 160
    }
  ]
});
const cardInfo = reactive([
  {
    top: {
      img: "",
      label: "今日待审核",
      hasButton: false,
      bottomList: [],
      hasSearch: true,
      width: "112px",
      dateValue: undefined
    },
    middle: [
      {
        tip: "",
        data: "0"
      }
    ],
    bottom: {
      hasBottom: true,
      label: "去审核",
      type: "warning",
      img: "",
      tip: "所有待审核：",
      data: "0",
      color: "#e6a23c",
      handle: () => {
        state.dataForm.status = 0;
        state.dataForm.payStatus = undefined;
        state.page = 1;
        state.getDataList();
      } //待审核tab
    },
    bg: "background: linear-gradient(160deg, rgba(254, 241, 230, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff"
  },
  {
    top: {
      img: "",
      label: "今日审核通过",
      hasButton: false,
      bottomList: [],
      hasSearch: true,
      dateValue: undefined,
      width: "112px"
    },
    middle: [
      {
        tip: "",
        data: "0"
      }
    ],
    bottom: {
      hasBottom: true,
      label: "去支付",
      type: "success",
      img: "",
      tip: "待支付：",
      data: "0",
      color: "#67c23a",
      handle: () => {
        state.dataForm.status = undefined;
        state.dataForm.payStatus = 0;
        state.page = 1;
        state.getDataList();
      } //已通过下今天待支付的数据筛选
    },
    bg: "background: linear-gradient(160deg, rgba(230, 254, 234, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff"
  },
  {
    top: {
      img: "",
      label: "今日审核拒绝",
      hasButton: false,
      bottomList: [],
      hasSearch: true,
      dateValue: undefined,
      width: "112px"
    },
    middle: [
      {
        tip: "",
        data: "0",
        color: "#f53f3f"
      }
    ],
    bottom: {
      hasBottom: true,
      label: "",
      img: "",
      tip: "拒绝金额：",
      data: "0",
      color: "#f53f3f"
    },
    bg: "background: linear-gradient(160deg, rgba(255, 226, 226, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff"
  }
]);

const view = reactive({
  getDataListURL: "/flowable/financialaudit/page",
  getDataListIsPage: true,
  deleteURL: "/flowable/financialaudit",
  deleteIsBatch: true,
  dataForm: {
    keywords: "",
    payStatus: "",
    start: "",
    end: "",
    type: "5"
  },
  dataList: []
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const dialogForm = reactive({
  visible: false,
  id: undefined
});
// 重置操作
const getResetting = () => {
  state.dataForm.payStatus = "";
  state.dataForm.start = "";
  state.dataForm.end = "";
  state.dataForm.keywords = "";
  state.dataForm.status = null;
  createDate.value = [];
  state.page = 1;
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: null },
  { label: "待审核", value: 0 },
  { label: "已通过", value: 1 },
  { label: "已拒绝", value: 2 }
];
// 其他收支
const stateListOther = [
  { label: "其他支出", value: 0 },
  { label: "其他收入", value: 1 }
];
// 放款审核
const lendingList = [
  { label: "回收放款", value: "1" },
  { label: "销售放款", value: "4" }
];
const payStateList = [
  { label: "待支付", value: 0 },
  { label: "已支付", value: 1 },
  { label: "支付失败", value: 2 }
];

const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  if (row.orderType == 8) return;
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};
// 收款信息
const detailRef = ref();
const detailKey = ref(0);
const checkDetail = (row: any) => {
  detailKey.value++;
  nextTick(() => {
    detailRef.value.init({ ...row });
  });
};

const defaultDate = ref();
// 合同预览
const previewHandle = async (id: any) => {
  let res = await baseService.get("/bestsign/previewContract/" + id);
  window.open(res?.data);
};
// 切换Tab
const tabsTypeChange = async () => {
  state.page = 1;
  state.dataForm.type = tabsTypeValue.value;
  state.dataForm.status = undefined;
  otherTypeVlaue.value = 0;
  lendingValue.value = "1";
  state.getDataList();
  if(tabsTypeValue.value == '1' || tabsTypeValue.value == '2'){
    for (let index = 1; index < 4; index++) {
      getStatistics(+index, defaultDate.value);
    }
  }
  if(tabsTypeValue.value == '6' && otherCardRef.value){
    otherCardRef.value.getAll();
  }
};

// 其他支出tabs切换
const otherTypeChange = () => {
  state.page = 1;
  state.dataForm.type = otherTypeVlaue.value == 0 ? "6" : "7";
  state.getDataList();
  for (let index = 1; index < 4; index++) {
    getStatistics(+index, defaultDate.value);
  }
};

// 放款审核tabs切换
const lendingChange = () => {
  state.page = 1;
  state.dataForm.type = lendingValue.value;
  state.getDataList();
  for (let index = 1; index < 4; index++) {
    getStatistics(+index, defaultDate.value);
  }
};

// 处理售后订单
const orderProcessingRef = ref();
const orderCheckHandle = async (row: any, showFlow?: any, showBuy?: any, showSale?: any) => {
  await nextTick();
  row.id = row.orderCode;
  orderProcessingRef.value.init(row, showFlow, showBuy, showSale);
};

// 更新收款审核数据
const auditStatRef = ref();
const changeStatData = () => {
  nextTick(() => {
    auditStatRef.value.getAll();
  });
};

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 顶部数据查询
const getStatistics = (queryType: Number | String, date?: any) => {
  baseService
    .get("/flowable/financialaudit/report", {
      queryType,
      date,
      type: state.dataForm.type
    })
    .then((res) => {
      if (queryType == 3) {
        cardInfo[+queryType - 1].middle[0].data = res.data?.count || 0;
        cardInfo[+queryType - 1].bottom.data = res.data?.totalAmount || 0;
      } else {
        cardInfo[+queryType - 1].middle[0].data = res.data?.count || 0;
        cardInfo[+queryType - 1].bottom.data = res.data?.total || 0;
      }
    })
    .catch((res) => {
      cardInfo[+queryType - 1].middle[0].data = "0";
      cardInfo[+queryType - 1].bottom.data = "0";
    });
};
const topDateChange = (event: any, i: Number) => {
  getStatistics(+i + 1, event);
};
const refreshDataList = () => {
  state.getDataList();
  // 更新数据统计
  if (state.dataForm.type == "5") {
    changeStatData();
  } else {
    for (let index = 1; index < 4; index++) {
      getStatistics(+index, defaultDate.value);
    }
  }
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.amount) {
      total = BigNumber(total).plus(BigNumber(item.amount));
    }
  });

  return total.toFixed(2);
};
const auditDrawerRefHandle = async (obj: any, isDetail_: any) => {
  await nextTick();
  let title = ["", "放款", "退款", "提现", "销售放款", "线下收款确认", "其他收支"][+tabsTypeValue.value];
  if (title == "线下收款确认") {
    title = obj.orderType == 8 ? "合作商订单到账" : obj.orderType == 6 ? "售后收到赔付到账" : obj.orderType == 4 ? "合作商回收线下收款" : "线下收款确认";
  }
  if (title == "其他收支") {
    title = otherTypeVlaue.value == 0 ? "其他支出" : "其他收入";
  }
  let isDetail = isDetail_;
  auditDrawerRef.value.init(obj.id, title, isDetail, state.dataForm.type == "1" || state.dataForm.type == "4" ? obj.orderSn : obj.orderCode);
};
const payWayRefHandle = async (obj: any) => {
  await nextTick();
  payWayRef.value.init(obj);
};
const submitHandle = () => {
  baseService.get("/bestsign/confirmContract/" + dialogForm.id).then((res) => {
    ElMessage.success("签署成功！");
  });
};

const ExamineChange = (type:string) =>{
  otherTypeVlaue.value = type == '6' ? 0 : 1;
  state.dataForm.type = type
  state.dataForm.status = 0;
  state.getDataList();
}

onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  let month = now.getMonth() + 1; // 月份从0开始，需要加1
  let day = now.getDate();
  month = +month > 9 ? month : "0" + month;
  day = +day > 9 ? day : "0" + day;
  defaultDate.value = year + "-" + month + "-" + day;
  for (let index = 1; index < 4; index++) {
    getStatistics(+index, defaultDate.value);
    cardInfo[index - 1].top.dateValue = defaultDate.value;
  }
});
</script>

<style lang="scss" scoped>
.headCardUl,
.headCardLi {
  padding: 0;
  list-style: none;
}
.flex {
  display: flex;
  align-items: cenetr;
}

.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}

.headCardUl {
  display: flex;
  align-items: cenetr;
  flex-wrap: nowrap;
  gap: 12px;

  .headCardLi {
    width: 387px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    padding: 12px;

    &:last-child {
      margin-right: 0;
    }

    .head {
      justify-content: space-between;
      font-weight: 500;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
    }

    .middle {
      justify-content: space-between;
      span {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      :deep(.el-statistic__content) {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bottom {
      margin-top: 16px;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      justify-content: space-between;
      :deep(.el-link__inner) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 14px;
        height: 11px;
        margin-left: 5px;
      }
    }
  }
}
.linkSty {
  display: flex;
  align-items: center;
  text-align: center;
  width: fit-content;
  margin: auto;
  .copyIcon {
    display: none;
    width: 1px;
  }
  &:hover {
    .copyIcon {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
.el-tag {
  border: 1px solid;
}
:deep(.el-table__footer) {
  font-weight: bold;
  tfoot tr td:first-child {
    font-weight: normal;
    text-align: left;
  }
}
.stat_top_time {
  :deep(.el-text) {
    cursor: pointer;
  }
  :deep(.ny_dropdown_menu) {
    padding: 0;
  }
  :deep(.clickValue),
  :deep(.placeholder) {
    line-height: normal;
    cursor: pointer;
  }

  :deep(.el-date-editor) {
    line-height: 20px;
    height: 20px;
    .el-input__prefix {
      position: absolute;
      right: 4px;

      .el-input__prefix-inner {
        color: #303133;

        .el-input__icon {
          margin-right: 0;
        }
      }
    }
    .el-input__wrapper {
      box-shadow: none;
      background-color: transparent;

      .el-input__inner {
        cursor: pointer;
      }
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
