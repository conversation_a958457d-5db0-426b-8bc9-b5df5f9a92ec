<template>
    <div class="button-group">
        <template class="visibleButtons" v-for="(button, index) in visibleButtons" :key="index">
            <el-button link :type="button.type" :icon="button.icon" @click="button.click" v-if="button.isShow">
                {{ button.text }}
            </el-button>
        </template>

        <el-dropdown trigger="click"  v-if="showMore">
            <el-button type="primary" class="pl-8" link>
                <el-icon class="text-primary">
                    <MoreFilled />
                </el-icon>
            </el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item v-for="(button, index) in hiddenButtons" :key="index" @click="button.click">
                        <span>
                            {{ button.text }}
                        </span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
</template>
<script lang="ts" setup>
import { defineProps, computed, ref } from "vue";
import { MoreFilled } from "@element-plus/icons-vue";

interface Item {
    text: string;
    icon?: string;
    type?: string;
    isShow: boolean;
    click: () => void;
}

const props = defineProps({
    // 操作按钮  ==> 必传
    buttons: {
        type: Array as () => Item[],
        default: () => [],
    },

    // 最多显示几个按钮， 其他按钮显示在更多里面  ==> 非必传（默认3个）
    max: {
        type: Number,
        default: 3,
    },
})

const isShowButtons = () => {
    return props.buttons?.filter((item: any) => {
        return item.isShow;
    });
};
let resultBtn = ref(isShowButtons());
const visibleButtons = computed(() => {
    if (resultBtn.value.length < props.max) {
        return resultBtn.value;
    } else {
        return resultBtn.value.slice(0, props.max);
    }
});
const hiddenButtons = computed(() => {
    if (resultBtn.value.length <= props.max) {
        return [];
    } else {
        return resultBtn.value.slice(props.max);
    }
});
const showMore = computed(() => {
    return resultBtn.value.length > props.max;
});
</script>

<style lang="scss" scoped>
.button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}
</style>




