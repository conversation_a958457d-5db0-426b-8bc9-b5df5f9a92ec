<template>
    <div>
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header-right>
                    <el-input v-model="state.dataForm.outTradeNo" :placeholder="$t('order.outTradeNo')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                <!-- 订单金额 -->
                <template #total="{row}">
                    {{ row.total / 100 }} <el-tag>元</el-tag>
                </template>

            </ny-table>
            
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, toRefs } from "vue";

const view = reactive({
    getDataListURL: "/pay/wechatNotifyLog/page",
    getDataListIsPage: true,
    deleteIsBatch: true,
    dataForm: {
        outTradeNo: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        prop: "outTradeNo",
        label: "订单ID",
        minWidth: 120
    },
    {
        prop: "total",
        label: "订单金额",
        minWidth: 120,
    },
    {
        prop: "tradeState",
        label: "交易状态",
        minWidth: 120
    },
    {
        prop: "transactionId",
        label: "微信订单号",
        minWidth: 150
    },
    {
        prop: "tradeType",
        label: "交易类型",
        minWidth: 120
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: 150
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.outTradeNo = "";
    state.getDataList();
}
</script>
