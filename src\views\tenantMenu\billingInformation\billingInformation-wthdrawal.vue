<template>
  <el-dialog v-model="visible" width="480" :title="'余额提现'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <div class="tipinfo">
      当前可提现金额：<span style="color: #e6a23c; display: flex">￥<el-statistic :value="detailInfo.amountTotal" :precision="2" /></span>
    </div>
    <div class="tipinfo" style="margin-bottom: 12px">
      今日可提现次数：<span style="color: #e6a23c">{{ detailInfo.count == null ? "无限" : detailInfo.count }}次</span>
    </div>

    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-suffix="：">
      <el-form-item prop="amount" label="" required>
        <el-input :max="+detailInfo.amountTotal" v-model="dataForm.amount" @blur="changeInput" :placeholder="'请输入 ' + inputPlacehoder" type="number" controls-position="right">
          <template #prepend>提现金额</template>
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <div v-if="defaultSet.charge && dataForm.amount" style="font-size: 12px; margin-left: 96px">
        <span>提现手续费：</span><span style="color: #e6a23c">{{ defaultSet.charge }}%，{{ (+defaultSet.charge * dataForm.amount) / 100 }}元</span>
      </div>
      <el-form-item prop="accountId" label="选择账户" required>
        <el-select v-model="dataForm.accountId" placeholder="选择账户" clearable>
          <el-option v-for="(item, index) in selectData" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="remark" label="备注">
        <el-input v-model="dataForm.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "提交审核" }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive, toRefs } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";

const store = useAppStore();

const { t } = useI18n();
const emit = defineEmits(["close", "refreshDataList"]);
const inputPlacehoder = ref("金额");
const visible = ref(false);
const validateAmount = () => {
  if (+dataForm.value.amount < +defaultSet.value.minAmount && defaultSet.value.minAmount != 0) {
    ElMessage.warning("提现最小金额" + defaultSet.value.minAmount + "元");
    return false;
  }
  if (+dataForm.value.amount > +defaultSet.value.maxAmount && defaultSet.value.maxAmount != 0) {
    ElMessage.warning("提现最大金额" + defaultSet.value.maxAmount + "元");
    return false;
  }
  return true;
};
const rules = {
  amount: [{ required: false, message: "请输入提现金额", trigger: "blur" }],
  remark: [{ required: false, message: "请输入备注", trigger: "change" }],
  accountId: [{ required: true, message: "请选择选择账户", trigger: "change" }]
};
const dataFormRef = ref();
const dataForm = ref(<any>{
  amount: undefined,
  remark: undefined,
  accountId: undefined
});
// 详情id
const detailInfo = ref({
  count: 0,
  amountTotal: 0
});
let defaultSet = ref({});
const init = (info: any) => {
  visible.value = true;
  detailInfo.value = info;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    dataForm.value.amount = undefined;
  }
  baseService.get("/flowable/withdraw").then((res) => {
    inputPlacehoder.value = "金额";
    defaultSet.value = res.data || {};
    if (defaultSet.value.minAmount != 0) {
      inputPlacehoder.value = defaultSet.value.minAmount + "元 ≤ " + inputPlacehoder.value;
    }
    if (defaultSet.value.maxAmount != 0) {
      inputPlacehoder.value = inputPlacehoder.value + " ≤ " + defaultSet.value.maxAmount + "元";
    }
  });
  getInfo();
};

const selectData = ref([]);
// 获取详情
const getInfo = async () => {
  let res = await baseService.get("/wallet/tenantaccount/page", { limit: 9999, tenantCode: store.state.user.tenantCode });
  selectData.value = res.data.list;
};
const changeInput = () => {
  if (dataForm.value.amount < 0) {
    dataForm.value.amount = 0;
  } else {
    dataForm.value.amount = dataForm.value.amount;
  }
};

// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = async () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid && validateAmount()) {
      replyLoading.value = true;
      baseService
        .get("/finance/tenantfinance/withdrawal", dataForm.value)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              replyLoading.value = false;
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          replyLoading.value = false;
        });
    }
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emit("close");
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
  display: flex;
}
:deep(.el-form-item--default) {
  margin-bottom: 12px;
  display: block;
}
.fontSty {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.el-statistic__content) {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #e6a23c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
