<template>
    <div class="push_stask_page" @mouseenter="mouseenterChange" @mouseleave="mouseleaveChange">
        <el-scrollbar max-height="268px">
            <div style="padding: 0px 8px; display: flex;flex-direction: column;gap: 8px;">
                <div class="stask_item" v-for="(item, index) in queryTheList" :key="index">
                    <div class="progress_bar">
                        <div class="line" v-if="item.pushShopSuccessCount > 0" :style="`width: ${(item.pushShopSuccessCount / item.pushShopCount) * 100}%`">
                            <div id="container-stars">
                                <div id="stars"></div>
                            </div>
                            <div id="glow">
                                <div class="circle"></div>
                                <div class="circle"></div>
                            </div>
                        </div>
                        <div class="barCenter">
                            <div class="barCenter_item" style="width: 90px">
                                <div class="label">合作商</div>
                                <div class="value">{{ item.partnerName }}</div>
                            </div>
                            <div class="barCenter_item" style="flex: 1">
                                <div class="label">执行时间</div>
                                <div class="value">{{ item.executeTime }}</div>
                            </div>
                            <div class="barCenter_item">
                                <div class="label">推送商品</div>
                                <div class="value" style="color: var(--color-primary)">{{ item.pushShopSuccessCount }}/
                                    {{
                                        item.pushShopCount }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="status_bar">
                        <el-text type="info" v-if="item.taskStatus == 1">已暂停</el-text>
                        <el-text type="success" v-else>执行中</el-text>
                        <el-text style="color: #1d2129; flex: 1">{{ item.scriptUserName }}</el-text>
                        <div class="but" v-if="item.needVerifyCode" @click="verificationCodeChange(item.sendPhoneCodeParam)">
                            <el-icon color="#F53F3F" size="14">
                                <Warning />
                            </el-icon>
                            <el-text style="color: #f53f3f">发送验证码</el-text>
                        </div>
                        <div class="switch" v-else>
                            <div class="but" v-if="item.taskStatus == 2" @click="switchChange(item, 1)">
                                <el-icon color="#86909C" size="14">
                                    <VideoPause />
                                </el-icon>
                                <el-text type="info">暂停</el-text>
                            </div>
                            <div class="but" v-else @click="switchChange(item, 2)">
                                <el-icon color="var(--color-primary)" size="14">
                                    <VideoPlay />
                                </el-icon>
                                <el-text type="primary">启动</el-text>
                            </div>
                        </div>
                        <div class="but" @click="deleteChange(item)">
                            <el-icon color="#F53F3F" size="14">
                                <Delete />
                            </el-icon>
                            <el-text style="color: #f53f3f">删除</el-text>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>
    <!-- 同步上架短信验证码 -->
    <SendVerificationCode ref="SendVerificationCodeRef"></SendVerificationCode>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import SendVerificationCode from "./SendVerificationCode.vue";
const emit = defineEmits(["mouseenterChange","mouseleaveChange","refresh"]);

// 定时器
const timer = ref();
// 任务列表
const queryTheList = ref(<any>[]);
const getQueryList = () => {
    baseService.post("/script/sysscriptpushtask/queryTheListOfTasksInExecution").then((res) => {
        if (res.code == 0) {
            queryTheList.value = res.data;
        }
    });

    timer.value = setTimeout(() => {
        getQueryList();
    }, 5000);
};

// 开关事件
const switchChange = (row: any, state: number) => {
    baseService
        .post("/script/sysscriptpushtask/pauseOrStartTask", {
            taskId: row.taskId,
            state
        })
        .then((res) => {
            if (res.code == 0) {
                ElMessage.success(`${state == 1 ? "推送任务已暂停" : "推送任务已开启"}`);
                clearTimeout(timer.value);
                getQueryList();
                emit('refresh');
            }
        });
};

// 删除事件
const deleteChange = (row: any) => {
    ElMessageBox.confirm("只删除本次推送任务，不影响已推送成功的商品;", "提示", {
        confirmButtonText: "确认删除",
        cancelButtonText: "取消",
        type: "warning"
    })
        .then(() => {
            baseService
                .post("/script/sysscriptpushtask/deleteTask", {
                    taskId: row.taskId
                })
                .then((res) => {
                    if (res.code == 0) {
                        ElMessage.success("推送任务删除成功");
                        clearTimeout(timer.value);
                        getQueryList();
                        emit('refresh');
                    }
                });
        })
        .catch(() => { });
};

// 验证码弹窗
const SendVerificationCodeRef = ref();
const verificationCodeChange = (VerificationInfo: any) => {
    SendVerificationCodeRef.value.init(VerificationInfo);
};

// 鼠标移入
const mouseenterChange = () =>{
    emit("mouseenterChange")
}

// 鼠标移出
const mouseleaveChange = () =>{
    emit('mouseleaveChange')
}

onMounted(() => {
    getQueryList();
});
onUnmounted(() => {
    clearTimeout(timer.value);
});
</script>

<style lang="less" scoped>
.push_stask_page {
    background-color: #fff;
    border-radius: 8px;
    width: 400px;
    padding: 8px 0px;
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 150px;
    right: 36px;
    z-index: 999;

    .stask_item {
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        padding: 4px;

        .progress_bar {
            width: 100%;
            height: 50px;
            padding: 8px;
            border: 1px solid var(--color-primary-light);
            border-radius: 4px;
            display: flex;
            align-items: center;
            position: relative;

            .line {
                height: 48px;
                border-radius: 4px;
                position: absolute;
                left: 0;
                top: 0;
                background-size: 300% 300%;
                cursor: pointer;
                backdrop-filter: blur(1rem);
                transition: 0.5s;
                animation: gradient_301 5s ease infinite;
                background-image: linear-gradient(var(--el-color-primary-light-8), var(--el-color-primary-light-8));
                background-origin: border-box;
                background-clip: content-box, border-box;
            }

            .barCenter {
                position: absolute;
                left: 0;
                top: 0;
                display: flex;
                align-items: center;
                width: 100%;
                height: 50px;
                padding: 8px;

                .barCenter_item {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .label {
                        color: #86909c;
                        font-size: 11px;
                        font-weight: 400;
                        line-height: 11px;
                    }

                    .value {
                        color: #303133;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 18px;
                    }
                }
            }
        }

        .status_bar {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 0px 8px;
            margin-top: 4px;

            .switch {
                display: flex;
                align-items: center;
            }

            .but {
                display: flex;
                align-items: center;
                gap: 2px;
                cursor: pointer;
            }
        }
    }
}

// 粒子动画
#container-stars {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: 0.5s;
    backdrop-filter: blur(1rem);
    border-radius: 5rem;
}

strong {
    z-index: 2;
    font-family: "Avalors Personal Use";
    font-size: 12px;
    letter-spacing: 5px;
    color: #ffffff;
    text-shadow: 0 0 4px white;
}

#glow {
    position: absolute;
    display: flex;
    width: 12rem;
}

.circle {
    width: 100%;
    height: 30px;
    filter: blur(2rem);
    animation: pulse_3011 4s infinite;
    z-index: -1;
}

// .circle:nth-of-type(1) {
//   background: rgba(254, 83, 186, 0.636);
// }

// .circle:nth-of-type(2) {
//   background: rgba(142, 81, 234, 0.704);
// }

// .btn:hover #container-stars {
//   z-index: 1;
//   background-color: #212121;
// }

// .btn:hover {
//   transform: scale(1.1);
// }

#stars {
    position: relative;
    background: transparent;
    width: 200rem;
    height: 200rem;
}

#stars::after {
    content: "";
    position: absolute;
    top: -10rem;
    left: -100rem;
    width: 100%;
    height: 100%;
    animation: animStarRotate 90s linear infinite;
}

#stars::after {
    background-image: radial-gradient(#ffffff 1px, transparent 1%);
    background-size: 50px 50px;
}

#stars::before {
    content: "";
    position: absolute;
    top: 0;
    left: -50%;
    width: 170%;
    height: 500%;
    animation: animStar 60s linear infinite;
}

#stars::before {
    background-image: radial-gradient(#ffffff 1px, transparent 1%);
    background-size: 50px 50px;
    opacity: 0.5;
}

@keyframes animStar {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-135rem);
    }
}

@keyframes animStarRotate {
    from {
        transform: rotate(360deg);
    }

    to {
        transform: rotate(0);
    }
}

@keyframes gradient_301 {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes pulse_3011 {
    0% {
        transform: scale(0.75);
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
    }

    100% {
        transform: scale(0.75);
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}
</style>
