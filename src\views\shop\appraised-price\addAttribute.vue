<template>
  <el-dialog v-model="visible" width="480" :title="(!dataForm.id ? $t('add') : $t('update')) + '属性'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-descriptions title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>游戏名称</div>
          </template>
          {{ props.gameInfo?.title }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>选择属性<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="pid" label="选择属性" required>
            <el-select v-model="dataForm.pid" placeholder="请选择属性" clearable @change="currentChildrenAtttibute">
              <el-option v-for="(item, index) in props.attributeList" :key="index" :label="item.name" :value="item.typeId" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>属性明细<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="attributeId" label="属性明细" required>
            <el-select :filter-method="dataFilter" v-model="dataForm.attributeId" placeholder="请搜索属性明细" filterable clearable>
              <el-option v-for="(item, index) in childrenAtttibute" :style="{ display: item.hide ? 'none' : 'block' }" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>单属性价格(元)<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="singlePrice" label="单属性价格(元)" required>
            <el-input-number class="leftIpt" style="width: 100%" v-model="dataForm.singlePrice" :controls="false" placeholder="请输入单属性价格" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>多属性价格(元)<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="multiPrice" label="多属性价格(元)" required>
            <el-input-number class="leftIpt" style="width: 100%" v-model="dataForm.multiPrice" :controls="false" placeholder="请输入多属性价格" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnloading" type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const props = defineProps({
  gameInfo: {},
  attributeList: []
});
const btnloading = ref(false);
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  gameId: "",
  type: "",
  pid: "",
  attributeId: "",
  singlePrice: undefined,
  multiPrice: undefined
});

const rules = ref({
  gameId: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  pid: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  attributeId: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  singlePrice: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  multiPrice: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});

const init = (row?: any) => {
  visible.value = true;
  Object.assign(dataForm, {
    gameId: "",
    type: "",
    pid: "",
    attributeId: "",
    singlePrice: undefined,
    multiPrice: undefined
  });
  // 重置表单数据
  dataFormRef.value && dataFormRef.value.resetFields();
  if (row && row.id) {
    Object.assign(dataForm, row);
  }
};
const childrenAtttibute = ref();
const currentChildrenAtttibute = () => {
  if (dataForm.pid) {
    let obj = props.attributeList.find((ele) => ele.typeId == dataForm.pid);
    dataForm.type = obj.type;
    childrenAtttibute.value = obj.children || [];
    dataForm.attributeId = "";
    return;
  }
  childrenAtttibute.value = [];
  dataForm.type = "";
  dataForm.attributeId = "";
};
const dataFilter = (str: any) => {
  childrenAtttibute.value.map((ele) => {
    if (ele.name.includes(str) || !str) {
      ele.hide = false;
    } else {
      ele.hide = true;
    }
    return ele;
  });
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnloading.value = true;
    dataForm.gameId = props.gameInfo.id;
    (!dataForm.id ? baseService.post : baseService.put)("/appraise/attribute/save", dataForm).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
      btnloading.value = false;
    });
  });
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.leftIpt {
  :deep(.el-input) {
    .el-input__inner {
      text-align: left !important;
    }
  }
}
</style>
