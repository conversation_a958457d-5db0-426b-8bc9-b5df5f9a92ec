<template>
  <!-- 回收订单 -->
  <div class="container acquisition-order TableXScrollSty">
    <div style="margin: 0px 12px -12px">
      <ny-flod-tab
        :list="[
          { label: '平台账号池', value: 0 },
          // { label: '公共账号池', value: 1 }
        ]"
        value="value"
        label="label"
        @change="getGamesList()"
        v-model="state.dataForm.accountPoolType"
      ></ny-flod-tab>
    </div>
    <div class="appraise_center">
      <div>
        <div class="center_left">
          <div class="menu_item" :class="{ active: item.gameId == state.dataForm.gameId }" v-for="(item, index) in gamesList" :key="index" @click="state.dataForm.gameId = item.gameId">
            <img :src="item.thumbnail" class="gameLogo" />
            <div class="gameInfo">
              <span class="title">{{ item.title }}</span>
              <div class="count">
                商品数量：<span>{{ item.clueNum }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center_right">
        <ny-table cellHeight="ch-56" noDataType="3" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
          <template #header-right>
            <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item>
                <ny-select style="width: 200px" v-model="state.dataForm.orderSource" dict-type="order_source" placeholder="请选择订单来源" @change="state.getDataList()"></ny-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker style="width: 316px !important" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
              </el-form-item>
              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-form>
          </template>

          <!-- 回收人 -->
          <template #realName="{ row }">
            {{ row.orderType == 2 ? row.realName1 : row.realName }}
          </template>
          <!-- 回收类型 -->
          <template #acquisitionType="{ row }">
            {{ row.acquisitionType == 2 ? "合作商回收" : "平台回收" }}
          </template>

          <!-- 游戏账号 -->
          <template #gameAccount="{ row }">
            <el-text type="primary" text class="pointer" @click="logHandle(row)">{{ row.state == "待收购" ? row.campNo || "查看属性详情" : row.gameAccount }}</el-text>
          </template>

          <template #operation="{ row }">
            <operation-btns
              :key="Math.random()"
              :max="4"
              :buttons="[
                {
                  text: '创建回收订单',
                  type: 'primary',
                  click: () => addHandle('create', row),
                  // 待回收 已取消  并且没有订单号
                  isShow: state.hasPermission('purchase:purchaseorder:addOrder')
                }
              ]"
            />
          </template>
        </ny-table>
      </div>
    </div>

    <!-- 操作日志 -->
    <operation-log ref="operationLogRef" :key="operationLogKey"></operation-log>

    <!-- 新增回收订单 -->
    <add-order ref="addOrderRef" :key="addOrderKey" @refresh="handleRefresh()" @completeInfo="completeInfoHandle"></add-order>

    <!-- 完善信息 -->
    <complete-info ref="completeInfoRef" :key="completeInfoKey" @refresh="handleRefresh()"></complete-info>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { Search, Management } from "@element-plus/icons-vue";
import { BigNumber } from "bignumber.js";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import OperationLog from "./OperationLog.vue";
import AddOrder from "../acquisition/components/AddOrder.vue";
import CompleteInfo from "../acquisition/components/CompleteInfo.vue";
import OperationBtns from "@/components/ny-table/src/components/OperationBtns.vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 表格配置项
const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 120
  },
  {
    prop: "gameAccount",
    label: "游戏账号/营地号",
    minWidth: 130
  },
  {
    prop: "orderSourceLabel",
    label: "订单来源",
    minWidth: 120
  },
  {
    prop: "submitter",
    label: "提交人",
    minWidth: 120
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 130
  },
  {
    prop: "operation",
    label: "操作",
    minWidth: 130
  }
]);

const view = reactive({
  getDataListURL: "/accountResourcesPool/accountresourcespool/page",
  getDataListIsPage: true,
  dataForm: {
    gameId: "",
    accountPoolType: 0,
    orderSource: "",
    startTime: "",
    endTime: ""
  }
});

const state = reactive({
  ...useView(view),
  ...toRefs(view)
});

// 重置操作
const getResetting = () => {
  state.dataForm.orderSource = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  createDate.value = [];
  state.getDataList();
};

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/accountResourcesPool/accountresourcespool/getaccountListGames/0").then((res) => {
    gamesList.value = [...res.data].map((ele: any) => {
      return { ...ele, id: ele.gameId };
    });
    state.dataForm.gameId = gamesList.value.length > 0 && gamesList.value[0].gameId;
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);

// 新增回收订单
const addOrderRef = ref();
const addOrderKey = ref(0);
const currentResource = ref({} as any);
const addHandle = async (type?: string, row?: any) => {
  currentResource.value = JSON.parse(JSON.stringify(row));
  delete row.id;
  addOrderKey.value++;
  await nextTick();
  addOrderRef.value.init(type, gamesList.value, row, state.dataForm.gameId);
};
// 完善信息
const completeInfoRef = ref();
const completeInfoKey = ref(0);
const completeInfoHandle = async (row: any) => {
  completeInfoKey.value++;
  await nextTick();
  completeInfoRef.value.init(row);
};
// 日志
const operationLogRef = ref();
const operationLogKey = ref(0);
const logHandle = (row: any) => {
  operationLogKey.value++;
  nextTick(() => {
    operationLogRef.value.init({ ...row });
  });
};

const handleRefresh = () => {
  baseService.delete("/accountResourcesPool/accountresourcespool", [currentResource.value.id]).then((res) => {
    state.getDataList();
    getGamesList();
    currentResource.value = {};
  });
};
</script>

<style lang="scss" scoped>
.acquisition-order {
  .contract-icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.appraise_center {
  // height: calc(100vh - 238px);
  // border: 1px solid red;
  padding: 12px;
  display: flex;
  gap: 12px;

  .center_left {
    width: 256px;
    max-height: calc(100vh - 230px);
    overflow: scroll;
    position: sticky;
    top: 0;

    .menu_item {
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      display: flex;
      gap: 8px;
      cursor: pointer;

      .gameLogo {
        width: 64px;
        height: 64px;
      }

      .gameInfo {
        height: 64px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .title {
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          text-align: center;
        }

        .count {
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          text-align: center;

          span {
            color: var(--el-color-primary);
          }
        }
      }

      &:hover {
        background-color: var(--el-color-primary-light-9);

        :deep(.title) {
          color: var(--el-color-primary) !important;
        }
      }
    }

    .active {
      background-color: var(--el-color-primary-light-9);

      :deep(.title) {
        color: var(--el-color-primary) !important;
      }
    }
  }

  .center_right {
    flex: 1;
    // border: 1px solid blue;
  }
}
</style>
