<template>
  <!-- 出售订单 -->
  <div class="container sell-order-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <ny-table
        noDataType="3"
        cellHeight="ch-96"
        class="nyTableSearchFormFitable"
        :state="state"
        :columns="columns"
        @pageSizeChange="state.pageSizeChangeHandle"
        @pageCurrentChange="state.pageCurrentChangeHandle"
        @selectionChange="state.dataListSelectionChangeHandle"
        @sortableChange="sortableChange"
      >
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.state" @change="state.getDataList"></ny-button-group>
        </template>
        <template #header-right>
          <el-form class="nyTableSearchForm" :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-select v-model="state.dataForm.saleType" clearable placeholder="请选择出售类型" @change="state.getDataList">
                <el-option v-for="(item, index) in saleTypeList" :label="item.label" :value="item.value" :key="index"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="state.dataForm.keywords" placeholder="商品编码/游戏账号/自编码/应急手机号" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>
        <template #header-custom>
          <div style="margin-bottom: 12px">
            <el-button type="primary" @click="AddSaleOrderHandle()">新增合作商订单</el-button>
          </div>
        </template>
        <template #title="{ row }">
          <div class="shoping">
            <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
            <div class="info">
              <div class="title mle" v-html="row.shopTitle" @click="toDetails(row)"></div>
              <div class="sle">
                {{ `${row.gameName || "-"} / ${row.serverName || "-"}` }}
              </div>
            </div>
          </div>
        </template>
        <!-- 利润头部 -->
        <template #profitHeader>
          <el-tooltip effect="dark" content="利润=成交价-回收价-包赔费-手续费" placement="top">
            <div class="flx-center">
              <div>利润(元)</div>
              <el-icon size="16" style="margin-left: 4px">
                <Warning />
              </el-icon>
            </div>
          </el-tooltip>
        </template>

        <!-- 游戏账号 -->
        <template #gameAccount="{ row }">
          <el-text type="primary" text class="pointer" @click="operationLogHandle(row, false, '详情')" v-if="row.saleType != '合作商销售'">{{ row.gameAccount }}</el-text>
          <span v-else>{{ row.gameAccount }}</span>
        </template>

        <!-- 收款凭证 -->
        <template #payCredentials="{ row }">
          <el-image :preview-teleported="true" style="width: 40px" v-if="row.payCredentials" fit="cover" :src="row.payCredentials" :preview-src-list="[row.payCredentials]"></el-image>
          <span v-else>-</span>
        </template>

        <!-- 合同状态 -->
        <template #contractState="{ row }">
          <template v-if="row.contractState == '未签署'">
            <div>
              <el-tag type="danger"> {{ row.contractState }} </el-tag>
            </div>
            <div v-if="row.guaranteeIds != null && row.state == '已支付' && (row.contractState == '未签署' || !row.contractState) && (row.saleType == '平台代售' || row.saleType == '合作商')">
              <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="signContractHandle(row)">生成合同</el-link>
            </div>
          </template>
          <template v-if="row.contractState == '卖方已签署'">
            <div>
              <el-tag type="warning">
                {{ row.contractState }}
                <el-icon @click="downPdf(row)"><Management /></el-icon>
              </el-tag>
            </div>
            <div>
              <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="submitsignContractHandle(row)">确认签署</el-link>
            </div>
          </template>
          <template v-if="row.contractState == '合同已锁定'">
            <div>
              <el-tag type="success">
                <div style="display: flex; gap: 4px; align-items: center">
                  <span>{{ row.contractState }}</span>
                  <el-icon @click="downPdf(row)"><Management /></el-icon>
                </div>
              </el-tag>
            </div>
            <div>
              <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="downPdf2(row)">下载证据报告</el-link>
            </div>
          </template>
        </template>

        <!-- 手续费 -->
        <template #fee="{ row }">
          {{ row.saleType == "平台直售" ? "-" : row.fee }}
        </template>

        <!-- 包赔信息 -->
        <template #guaranteeInformation="{ row }">
          <el-text class="pointer" type="primary" text v-if="state.hasPermission('purchase:purchaseorder:getGuaranteeInfoCollect')" @click="guaranteeInfoHandle(row.id)">查看</el-text>
        </template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == '待支付'" type="danger">待支付</el-tag>
          <el-tag v-if="row.state == '已支付'" type="primary">已支付</el-tag>
          <el-tag v-if="row.state == '交易成功'" type="success">交易成功</el-tag>
          <el-tag v-if="row.state == '已取消'" type="info">已取消</el-tag>
          <el-tag v-if="row.state == '标记售后'" type="warning">标记售后</el-tag>
          <el-tag v-if="row.state == '售出待审核'" type="warning">售出待审核</el-tag>
          <el-tag v-if="row.state == '售出审核拒绝'" type="danger">售出审核拒绝</el-tag>
          <el-tag v-if="row.state == '提交打款已拒绝'" type="danger">提交打款已拒绝</el-tag>
          <div>
            <el-text type="info" size="small" v-if="row.statusRemark == '待打款'">(待打款)</el-text>
            <el-text type="info" size="small" v-if="row.statusRemark == '打款已提交'">(打款已提交)</el-text>
            <el-text type="info" size="small" v-if="row.statusRemark == '已打款'">(已打款)</el-text>
            <el-text type="info" size="small" v-if="row.state == '售出审核拒绝'">{{ row.statusRemark }}</el-text>
            <el-text type="info" size="small" v-if="row.state == '提交打款已拒绝'">{{ row.statusRemark }}</el-text>
            <el-text type="info" size="small" v-if="row.expireState == 1 && row.state == '标记售后'">（已过期）</el-text>
          </div>
        </template>

        <template #operation="{ row }">
          <operation-btns
            :key="Math.random()"
            :max="4"
            :buttons="[
              // 待支付  商品列表售出未上传付款凭证；
              {
                text: '收款确认',
                type: 'primary',
                click: () => acknowledgementHandle(row, true),
                isShow: row.state == '待支付' && row.sellWay === '0'
              },
              // 商城过来的
              {
                text: '收款确认',
                type: 'primary',
                click: () => acknowledgementHandle(row),
                // 创建入口  createGateway 0 前端上传  1后端创建
                isShow: row.state == '待支付' && row.createGateway == 1 && row.sellWay != '0'
              },
              // 已支付 sellType 平台自营 平台代售 合作商家  只有平台代售才显示提交打款  销售订单 平台直售的 状态应该是交易成功, 不应该是已支付
              {
                text: '提交打款',
                type: 'success',
                click: () => submitPaymentApplicationHandle(row.id, false, row.tenantCode, row.shopCode),
                isShow: ((row.guaranteeIds != null && row.contractState == '合同已锁定') || row.guaranteeIds == null) && row.state == '已支付' && (row.saleType == '平台代售' || row.saleType == '合作商') && !row.financeAuditId && state.hasPermission('sale:saleorder:applyPay')
              },
              {
                text: '打款已拒绝',
                type: 'warning',
                click: () => viewReviewDetails(row),
                // auditEntity.status  拒绝状态
                isShow: row.state == '已支付' && row.auditEntity && row.auditEntity.status == 2 && state.hasPermission('purchase:purchaseorder:confirmPayment')
              },
              // 已支付  financeAuditId null 提交打款  有值 打款完成
              {
                text: '打款信息',
                type: 'success',
                click: () => makePaymentConfirmationHandle(row.id),
                isShow: row.state == '交易成功' && (row.financeAuditId || row.saleType == '平台直售') && row.contractState == '合同已锁定' && state.hasPermission('sale:saleorder:confirmPayment')
              },
              {
                text: '二次回收',
                type: 'primary',
                click: () => handleRecycle(row),
                isShow: row.state == '交易成功' && row.applyRecyclingStatus == '0' && row.saleType != '合作商销售'
              },
              {
                text: '审核',
                type: 'primary',
                click: () => shopAuditHandle(row.id),
                isShow: row.isAudit == '1'
              },
              // 已支付  交易成功
              {
                text: '标记售后',
                type: 'warning',
                click: () => operationLogHandle(row, true, '订单标记售后'),
                isShow: (row.state == '已支付' || row.state == '交易成功') && row.saleType != '合作商销售'
              },
              {
                text: '重新标记',
                type: 'warning',
                click: () => operationLogHandle(row, true, '订单标记售后'),
                isShow: row.expireState == 1 && row.state == '标记售后'
              },
              {
                text: '提交打款',
                type: 'success',
                click: () => PaymentApplicationHandle(row.id),
                isShow: row.statusRemark == '待打款' || row.state == '提交打款已拒绝'
              },
              // 订单提交审核拒绝可重新编辑
              {
                text: '编辑',
                type: 'primary',
                click: () => AddSaleOrderHandle(row.id),
                isShow: row.state == '售出审核拒绝' && row.saleType == '合作商销售'
              },
              {
                text: '日志',
                type: 'info',
                click: () => operationLogHandle(row, false, '操作日志'),
                isShow: state.hasPermission('sale:saleorder:orderLog')
              },
              // 已支付
              {
                text: '取消',
                type: 'danger',
                click: () => orderCancelHandle(row),
                isShow: row.state == '待支付' && state.hasPermission('sale:saleorder:cancel') && row.sellWay != '0'
              },
              {
                text: '包赔确认',
                type: 'warning',
                click: () => guaranteeInfoHandle(row.id),
                // 待换绑, 已收集过包赔信息 还没确认
                isShow: row.guaranteeIds != null && row.baopei && !row.guaranteeIds && row.state != '交易成功'
              }
            ]"
          />
        </template>

        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span style="font-weight: bold; color: #1d2129">零售价</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>

    <!-- 查看商品详情 -->
    <ShopInfo ref="infoRef"></ShopInfo>

    <!-- 签署合同 -->
    <sign-contract @refreshdata="state.getDataList()" ref="signContractRef"></sign-contract>

    <!-- 确认签署 -->
    <submitSign @refreshdata="state.getDataList()" ref="submitSignRef"></submitSign>

    <!-- 提交打款申请 -->
    <submit-payment-application ref="submitPaymentApplicationRef" :key="submitPaymentApplicationKey" @refresh="state.getDataList()"></submit-payment-application>

    <!-- 打款信息 -->
    <make-payment-confirmation ref="makePaymentConfirmationRef" @refresh="state.getDataList()"></make-payment-confirmation>

    <!-- 订单取消确认 -->
    <order-cancel ref="orderCancelRef" @refresh="state.getDataList()"></order-cancel>

    <!-- 删除订单 -->
    <order-delete ref="orderDeleteRef" :key="orderDeleteKey" @refresh="state.getDataList()"></order-delete>

    <!-- 操作日志 -->
    <operation-log ref="operationLogRef" :key="operationLogKey" @refresh="state.getDataList()"></operation-log>

    <!-- 收款确认 -->
    <acknowledgement ref="acknowledgementRef" :key="acknowledgementKey" @refresh="state.getDataList()"></acknowledgement>

    <!-- 提交打款拒绝  查看拒绝原因 -->
    <audit-detail ref="auditDetailRef" :key="auditDetailKey" @resubmitPayment="(id: any) => paymentSubmitHandle(id)"></audit-detail>

    <!-- 包赔材料 -->
    <guaranteed-materials ref="guaranteedMaterialsRef" :key="guaranteedMaterialsKey" @refresh="state.getDataList()"></guaranteed-materials>

    <!-- 新增合作商销售订单 -->
    <AddSaleOrder ref="AddSaleOrderRef" @refresh="state.getDataList()"></AddSaleOrder>

    <!-- 合作商销售订单详情 -->
    <AddSaleOrderDetails ref="AddSaleOrderDetailsRef"></AddSaleOrderDetails>

    <!-- 合作商订单提交打款 -->
    <PaymentApplication ref="PaymentApplicationRef" @refresh="state.getDataList()"></PaymentApplication>

    <!-- 售出审核 -->
    <shopAudit ref="shopAuditRef" :key="shopAuditKey" @refreshDataList="state.getDataList()" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { BigNumber } from "bignumber.js";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import SignContract from "../components/SignContract.vue";
import ContractRealName from "../components/ContractRealName.vue";
import SubmitPaymentApplication from "../components/SubmitPaymentApplication.vue";
import MakePaymentConfirmation from "../components/MakePaymentConfirmation.vue";
import OrderCancel from "../components/OrderCancel.vue";
import OrderDelete from "../components/OrderDelete.vue";
import OperationLog from "./components/OperationLog.vue";
import submitSign from "../components/submitSign.vue";
import OperationBtns from "@/components/ny-table/src/components/OperationBtns.vue";
import Acknowledgement from "./components/Acknowledgement.vue";
import AuditDetail from "../acquisition/components/AuditDetail.vue";
import GuaranteedMaterials from "./components/GuaranteedMaterials.vue";
import ShopInfo from "@/views/shop/shop-info.vue";
import AddSaleOrder from "./components/AddSaleOrder.vue";
import PaymentApplication from "./components/PaymentApplication.vue";
import AddSaleOrderDetails from "./components/AddSaleOrderDetails.vue";
import shopAudit from "./components/shopAudit.vue";
import { useAppStore } from "@/store";
const store = useAppStore();
const infoRef = ref(); // 查看商品详情
const AddSaleOrderDetailsRef = ref(); // 合作商销售订单详情
// 表格配置项
const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 120
  },
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: 120
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 120
  },
  {
    prop: "state",
    label: "订单状态",
    width: 136
  },
  {
    prop: "dealAmount",
    label: "成交价(元)",
    width: 136
  },
  {
    prop: "saleType",
    label: "销售类型",
    width: 136
  },
  {
    prop: "channelName",
    label: "销售渠道",
    width: 136
  },
  {
    prop: "payTime",
    label: "支付时间",
    width: 190
  },
  {
    prop: "saleUserName",
    label: "销售人",
    width: 112
  },
  {
    prop: "saleAmount",
    label: "零售价(元)",
    width: 136
  },
  {
    prop: "purchaseAmount",
    label: "回收价(元)",
    width: 136
  },
  {
    prop: "guaranteePrice",
    label: "包赔费(元)",
    width: 136
  },
  {
    prop: "fee",
    label: "手续费(元)",
    width: 136
  },
  {
    prop: "profit",
    label: "利润(元)",
    width: 136
  },
  {
    prop: "guaranteeInformation",
    label: "包赔信息",
    width: 88
  },
  {
    prop: "contractState",
    label: "合同状态",
    width: 126
  },
  {
    prop: "firstListingTime",
    label: "首次上架时间",
    width: 190
  },
  {
    prop: "thirdPartyOrderCode",
    label: "三方订单号",
    width: 112
  },
  {
    prop: "payCredentials",
    label: "收款凭证",
    width: 112
  },
  {
    prop: "ownCoding",
    label: "自编码",
    width: 120
  },
  {
    prop: "emergencyPhone",
    label: "应急手机号",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 200
  }
]);

const view = reactive({
  getDataListURL: "/sale/page",
  getDataListIsPage: true,
  exportURL: "/sale/export",
  dataForm: {
    gameId: "",
    state: "",
    keywords: "",
    saleType: "",
    startTime: "",
    endTime: "",
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.saleType = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  { label: "待支付", value: "WAI_FOR_PAY" },
  { label: "已支付", value: "PAID" },
  { label: "已取消", value: "CANCELED" },
  { label: "交易成功", value: "DEAL_SUCCESS" },
  { label: "标记售后", value: "SALE_AFTER_SERVICE" },
  { label: "待审核", value: "SOLD_PENDING_APPROVAL" }
];

// 出售类型
const saleTypeList = [
  { label: "合作商同步", value: "PARTNER_SYNCHRONIZATION" },
  { label: "平台直售", value: "ORIGINAL_OWNER" },
  { label: "平台代售", value: "PLATFORM_CONSIGNMENT" }
];

// 点击标题查看详情
const toDetails = (row: any) => {
  nextTick(() => {
    if (row.saleType != "合作商销售") {
      infoRef.value.init(row);
    } else {
      AddSaleOrderDetailsRef.value.init(row.id);
    }
  });
};

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.saleAmount) total = total + (item.saleAmount || 0);
  });

  return total.toFixed(2);
};

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);

// 签署合同
const signContractRef = ref();
const signContractHandle = async (obj: any) => {
  obj.contractType = "SALE_CONTRACT";
  signContractRef.value.init(obj);
};
const submitSignRef = ref();
const submitsignContractHandle = async (obj: any) => {
  await nextTick();
  submitSignRef.value.init(obj);
};
// 下载合同
const downPdf = async (obj: any) => {
  let res = await baseService.get("/bestsign/downloadContract/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
const downPdf2 = async (obj: any) => {
  let res = await baseService.get("/bestsign/downloadReport/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle + "上上签证据报告"; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};

// 提交打款
const submitPaymentApplicationRef = ref();
const submitPaymentApplicationKey = ref(0);
const submitPaymentApplicationHandle = async (id: number, hideForm?: boolean, tenantCode: any, shopCode: any) => {
  submitPaymentApplicationKey.value++;
  await nextTick();
  submitPaymentApplicationRef.value.init(id, "sell", hideForm, tenantCode, shopCode);
};

// 打款信息
const makePaymentConfirmationRef = ref();
const makePaymentConfirmationHandle = (id: number) => {
  makePaymentConfirmationRef.value.init(id, "sell");
};

// 订单取消确认
const orderCancelRef = ref();
const orderCancelHandle = (row: any) => {
  orderCancelRef.value.init(row, "sell");
};

// 删除订单
const orderDeleteRef = ref();
const orderDeleteKey = ref(0);
const orderDeleteHandle = async (id?: number) => {
  orderDeleteKey.value++;
  await nextTick();
  let ids: any[] = [];
  let arr = state.dataListSelections.map((item) => item.id);

  if (!id && state.dataListSelections && state.dataListSelections.length) {
    state.dataList?.map((item) => {
      console.log(state.dataListSelections, item.id, item.state == "已取消");
      if (arr.includes(item.id) && item.state == "已取消") {
        ids.push(item.id);
      }
    });
  }
  if (!id && !ids.length) {
    return ElMessage.error("请选择已取消状态的订单进行删除!");
  }

  if (!id && !ids.length) return;
  orderDeleteRef.value.init(id ? [id] : ids, "sell");
};

// 操作日志
const operationLogRef = ref();
const operationLogKey = ref(0);
const operationLogHandle = async (row: any, marker?: boolean, type?: string) => {
  console.log(row);
  operationLogKey.value++;
  await nextTick();
  operationLogRef.value.init(row, marker, type);
};
// 二次回收
const handleRecycle = (data: any) => {
  ElMessageBox.confirm("是否申请将商品二次回收？", "申请回收", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService
      .post("accountResourcesPool/accountresourcespool", {
        gameAccount: data.gameAccount,
        orderSource: "1",
        orderSourceLabel: "销售订单-二次回收",
        submitter: store.state.user.realName,
        submitterId: store.state.user.id,
        accountPoolType: "0",
        gameId: data.gameId,
        gameName: data.gameName,
        orderId: data.id
      })
      .then((res) => {
        ElMessage.success("已回收至账号资源池!");
        state.query();
      });
  });
};

// 收款确认
const acknowledgementRef = ref();
const acknowledgementKey = ref(0);
const acknowledgementHandle = async (row: any, shop = false) => {
  acknowledgementKey.value++;
  await nextTick();
  acknowledgementRef.value.init(row, shop);
};

// 查看审核详情
const auditDetailRef = ref();
const auditDetailKey = ref(0);
const viewReviewDetails = async (row: any) => {
  auditDetailKey.value++;
  await nextTick();
  auditDetailRef.value.init(row);
};

// 包赔材料
const guaranteedMaterialsRef = ref();
const guaranteedMaterialsKey = ref(0);
const guaranteeInfoHandle = async (id: number) => {
  guaranteedMaterialsKey.value++;
  await nextTick();
  guaranteedMaterialsRef.value.init(id);
};

// 新增合作商销售订单
const AddSaleOrderRef = ref();
const AddSaleOrderKey = ref(0);
const AddSaleOrderHandle = async (id?: any) => {
  AddSaleOrderKey.value++;
  await nextTick();
  AddSaleOrderRef.value.init(id);
};

// 合作商提交打款申请
const PaymentApplicationRef = ref();
const PaymentApplicationKey = ref(0);
const PaymentApplicationHandle = async (id: any) => {
  PaymentApplicationKey.value++;
  await nextTick();
  PaymentApplicationRef.value.init(id);
};

// 商品出售审核
const shopAuditRef = ref();
const shopAuditKey = ref(0);
const shopAuditHandle = async (id: any) => {
  shopAuditKey.value++;
  await nextTick();
  shopAuditRef.value.init(id);
};
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
