<template>
    <div class="ny-im-wrap">
        <top-header></top-header>
        
        <div class="im-container">
            <!-- 会话列表 -->
            <conversation-list ref="conversationListRef"></conversation-list>

            <!-- 当前会话 -->
            <current-conversation v-if="!showContactInfo && imStore.currentConversation && imStore.currentConversation.targetId" @updateConversation="updateConversation"></current-conversation>

            <!-- 未打开会话 -->
            <div v-else-if="!showContactInfo" class="no-conversation">
                <img :src="sttingInfo.info.backendLogo" />   
            </div>

            <!-- 联系人信息 -->
            <contact-info class="flx-1" v-else="showContactInfo"></contact-info>

        </div>

        <!-- 自动回复设置 -->
        <automatic-reply-setting :visible="showAutoReplySetting"></automatic-reply-setting>
        
    </div>
</template>

<script lang="ts" setup>
    import { ref, onMounted, computed } from 'vue'
    import { useImStore } from '@/store/im';
    import { useSettingStore } from '@/store/setting';
    import TopHeader from './components/TopHeader.vue';
    import ConversationList from './components/ConversationList.vue';
    import CurrentConversation from './components/CurrentConversation.vue';
    import ContactInfo from './components/ContactInfo.vue';
    import AutomaticReplySetting from './components/AutomaticReplySetting.vue';

    const imStore = useImStore();
    const sttingInfo = useSettingStore();


    // 是否显示自动回复设置窗口
    const showAutoReplySetting = computed(() => {
        return imStore.showAutoReplyDialog;
    })

    // 是否显示联系人信息
    const showContactInfo = computed(() => {
        return imStore.showContactInfo;
    })

    // 清除当前会话的未读数
    const conversationListRef = ref();
    const updateConversation = (data: any) => {
        conversationListRef.value.updateConversation(data);
    }

    
</script>

<style lang="scss" scoped>
    @import './im.scss';
</style>