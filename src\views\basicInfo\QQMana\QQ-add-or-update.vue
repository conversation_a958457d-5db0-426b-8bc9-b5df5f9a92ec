<template>
  <el-dialog v-model="visible" width="500" :title="(!dataForm.id ? $t('add') : $t('update')) + 'QQ群'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 270px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item style="margin-bottom: 8px" prop="gameId" label="选择游戏">
        <el-select clearable v-model="dataForm.gameId" placeholder="请选择游戏">
          <el-option v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="groupCode" label="QQ群号码">
        <el-input clearable v-model="dataForm.groupCode" placeholder="请输入QQ群号码"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="groupName" label="QQ群名称">
        <el-input clearable v-model="dataForm.groupName" placeholder="请输入QQ群名称"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="groupUrl" label="入群链接">
        <el-input clearable v-model="dataForm.groupUrl" placeholder="请输入入群链接"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const gameList = ref([]);
const dataForm = reactive({
  gameId: undefined,
  gameName: undefined,
  groupCode: undefined,
  groupUrl: undefined,
  groupName: undefined
});
const validateCode = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (!/^[1-9][0-9]{4,}$/.test(value)) {
    return callback(new Error(t("validate.format", { attr: "QQ号码" })));
  }
  callback();
};
const rules = ref({
  groupCode: [
    { required: true, message: "请输入QQ群号码", trigger: "blur" },
    { validator: validateCode, trigger: "blur" }
  ],
  groupName: [{ required: true, message: "请输入QQ群名称", trigger: "blur" }],
  groupUrl: [{ required: true, message: "请输入入群链接", trigger: "blur" }],
  gameId: [{ required: true, message: "请选择游戏", trigger: "change" }]
});

const init = (id?: number) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  delete dataForm.id;
  if (id) {
    dataForm.id = id;
    getInfo(id);
  }
};

// 获取游戏下拉
const getgameList = () => {
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    gameList.value = res.data;
  });
};
getgameList();
// 获取信息
const getInfo = (id: number) => {
  baseService.get("/group/sysqqgroup/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let gameObj = gameList.value.find((ele: any) => ele.id == dataForm.gameId);
    dataForm.gameName = gameObj.title;
    (!dataForm.id ? baseService.post : baseService.put)("/group/sysqqgroup", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: block;
}
</style>
