<template>
  <el-dialog v-model="visible" title="导航常用功能管理" :close-on-click-modal="false" :close-on-press-escape="false" width="680">
    <div style="display: flex">
      <div class="dataArea">
        <div v-for="item in allAuthMenu" :key="item.path">
          <div class="metatitle">{{ item.meta?.title }}</div>
          <div class="chooseDiv">
            <el-checkbox-group @change="handleCheckData" v-model="item.checkPath" :max="10">
              <el-checkbox :disabled="backDisabled(subItem.meta?.id)" style="width: 26%" v-for="subItem in item.children" :label="subItem.meta?.title" :value="subItem.meta?.id" />
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div class="checkArea">
        <div style="display: flex; align-items: center; justify-content: space-between">
          <span style="color: #1d2129">已添加{{ checkMenu.length }}个</span>
          <el-button link type="primary" @click="cleanAll">清空</el-button>
        </div>
        <div style="color: #86909c; font-size: 11px; margin: 5px 0 10px 0">最多可选10个，支持拖拽排序</div>
        <div>
          <div class="pathCard" v-for="(item, index) in checkMenu" :key="item.id" draggable="true" @dragstart="handleDragStart($event, index)" @dragend="handleDragEnd" @dragover="handleDragOver" @drop="handleDrop($event, index)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M2.90663 8.50012L3.56313 9.14062C3.68813 9.28662 3.72713 9.45062 3.68013 9.63262C3.63313 9.81462 3.51846 9.92929 3.33612 9.97662C3.15379 10.024 2.99496 9.98496 2.85962 9.85962L1.35963 8.35962C1.25529 8.25529 1.20312 8.13546 1.20312 8.00012C1.20312 7.86479 1.25529 7.74496 1.35963 7.64062L2.85962 6.14062C2.99496 6.01562 3.15379 5.97662 3.33612 6.02362C3.51846 6.07062 3.63313 6.18529 3.68013 6.36762C3.72713 6.54996 3.68813 6.71396 3.56313 6.85962L2.90663 7.50012H7.45362V2.90612L6.81313 3.56262C6.67779 3.69796 6.51379 3.74229 6.32113 3.69562C6.12846 3.64896 6.00863 3.53179 5.96163 3.34413C5.91463 3.15646 5.96413 2.99496 6.11013 2.85962L7.61013 1.35963C7.70379 1.25529 7.81846 1.20312 7.95412 1.20312C8.08979 1.20312 8.20963 1.25529 8.31363 1.35963L9.81363 2.85962C9.90729 2.96396 9.95162 3.07863 9.94662 3.20363C9.94162 3.32863 9.88946 3.44063 9.79012 3.53963C9.69079 3.63863 9.57879 3.69079 9.45413 3.69613C9.32946 3.70146 9.21479 3.65713 9.11012 3.56313L8.45362 2.92262V7.50063H13.0006L12.3601 6.86013C12.2558 6.75579 12.2036 6.63596 12.2036 6.50063C12.2036 6.36529 12.2531 6.24813 12.3521 6.14913C12.4511 6.05013 12.5683 6.00063 12.7036 6.00063C12.839 6.00063 12.9588 6.04746 13.0631 6.14112L14.5631 7.64112C14.6568 7.74546 14.7036 7.86529 14.7036 8.00062C14.7036 8.13596 14.6568 8.25579 14.5631 8.36012L13.0631 9.86012C12.9588 9.95379 12.839 10.0006 12.7036 10.0006C12.5683 10.0006 12.4511 9.95113 12.3521 9.85213C12.2531 9.75313 12.2036 9.63596 12.2036 9.50062C12.2036 9.36529 12.2558 9.24546 12.3601 9.14112L13.0006 8.50062H8.45362V13.0786L9.11012 12.4381C9.24546 12.3028 9.40429 12.2611 9.58663 12.3131C9.76896 12.3651 9.88612 12.4823 9.93812 12.6646C9.99012 12.847 9.94846 13.0058 9.81312 13.1411L8.31312 14.6411C8.20879 14.7455 8.08896 14.7976 7.95362 14.7976C7.81829 14.7976 7.70362 14.7455 7.60962 14.6411L6.10962 13.1411C6.00529 13.0475 5.95312 12.9328 5.95312 12.7971C5.95312 12.6615 6.00262 12.5443 6.10162 12.4456C6.20062 12.347 6.31779 12.2975 6.45312 12.2971C6.58846 12.2968 6.70829 12.3436 6.81262 12.4376L7.45312 13.0941V8.50012H2.90612H2.90663Z"
                fill="#86909C"
              />
            </svg>
            <span style="color: #1d2129">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="submitLoading" @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const checkMenu = ref(<any>[]); // 已选菜单
const allAuthMenu = ref(<any>[]); // 所有菜单
const dragIndex = ref<number | null>(null); // 当前拖拽的索引

const init = (menu: any, allMenu: any) => {
  checkMenu.value = [];
  allAuthMenu.value = [];
  // init
  visible.value = true;
  checkMenu.value = menu;
  allAuthMenu.value = allMenu.map((ele: any) => {
    ele.checkPath = [];
    return ele;
  });
  // 回显勾选
  menu.forEach((cItem: any) => {
    allAuthMenu.value.forEach((ele: any) => {
      if (ele.children && ele.children.length > 0 && ele.children.some((e: any) => e.meta?.id == cItem.id)) {
        ele.checkPath.push(cItem.id);
      }
    });
  });
};

// 勾选数据回显
const handleCheckData = () => {
  let checkArr = <any>[];
  allAuthMenu.value.forEach((ele: any) => {
    if (ele.checkPath.length > 0) {
      let arr_ = ele.children.filter((e: any) => ele.checkPath.includes(e.meta?.id));
      arr_.map((aItem: any) => {
        let order = checkMenu.value.findIndex((menuItem: any) => menuItem.id === aItem.meta?.id);
        aItem.name = aItem.meta?.title;
        aItem.id = aItem.meta?.id;
        aItem.order = order == -1 ? 99 : order; // 按顺序回显， 新增排在后面
        return aItem;
      });
      checkArr.push(...arr_);
    }
  });
  if (checkArr.length > 10) {
    ElMessage.warning("最多可选10个！");
    return;
  }
  checkMenu.value = checkArr.sort((a, b) => a.order - b.order);
};
// 达到十个后禁用未选项
const backDisabled = (id: any) => {
  if (checkMenu.value && checkMenu.value.length == 10) {
    if (checkMenu.value.some((ele) => ele.id == id)) {
      return false;
    }
    return true;
  }
  return false;
};
// 清空
const cleanAll = () => {
  checkMenu.value = [];
  allAuthMenu.value.forEach((ele: any) => {
    ele.checkPath = [];
  });
};

// 拖拽开始
const handleDragStart = (e: DragEvent, index: number) => {
  dragIndex.value = index;
  e.dataTransfer?.setData("text/plain", String(index));
};

// 拖拽结束
const handleDragEnd = () => {
  dragIndex.value = null;
};

// 拖拽进入
const handleDragOver = (e: DragEvent) => {
  e.preventDefault(); // 必须阻止默认行为，才能触发 drop 事件
};

// 放置
const handleDrop = (e: DragEvent, index: number) => {
  e.preventDefault();
  const fromIndex = dragIndex.value;
  if (fromIndex === null || fromIndex === index) return;

  // 交换元素位置
  const movedItem = checkMenu.value.splice(fromIndex, 1)[0];
  checkMenu.value.splice(index, 0, movedItem);
};

// 文件上传   确认导入按钮
const submitLoading = ref(false);
const handleSubmit = () => {
  submitLoading.value = true;
  let params = <any>[];
  checkMenu.value.forEach((ele: any, index: any) => {
    params.push({
      menuId: ele.id,
      sort: index + 1
    });
  });
  baseService
    .post("/sys/commonFunctions/batchSave", params)
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("设置成功！");
        emit("refreshDataList");
      } else {
        ElMessage.error("设置失败！");
      }
    })
    .finally(() => {
      submitLoading.value = false;
      visible.value = false;
    });
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.dataArea {
  width: 436px;
  max-height: 518px;
  overflow-y: scroll;
  .metatitle {
    padding: 4px 8px;
    background-color: #f2f3f5;
    color: #606266;
    line-height: 22px;
  }
  :deep(.el-checkbox__inner) {
    border-radius: 4px !important;
  }
  :deep(.el-checkbox__label) {
    color: #606266 !important;
    font-weight: 400;
  }
}
.checkArea {
  width: 200px;
  margin-left: 12px;
  padding: 4px 8px;
  max-height: 518px;
  background-color: #f2f3f5;

  .pathCard {
    margin-bottom: 10px;
    padding: 0 8px;
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 8px;
    height: 32px;
    cursor: pointer;
  }
}
</style>
