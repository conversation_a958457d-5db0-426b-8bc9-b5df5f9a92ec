<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-tabs v-model="dataForm.type" class="demo-tabs" @tab-change="handleNameClick">
      <el-tab-pane :label="item.dictLabel" :name="item.dictValue" v-for="(item, index) in state.typeList" :key="index"></el-tab-pane>
    </el-tabs>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="合作商" prop="partnerUserId">
            <ny-select-search v-model="dataForm.partnerUserId" labelKey="companyName" valueKey="userId" url="/partner/tbpartneruser/page" :param="{ limit: 9999 }" placeholder="请选择商户" @change="handleChangePartnerUserId" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-button type="primary" @click="handleSync" :loading="state.syncLoading">自动同步</el-button>
        </el-col>
        <el-col :span="12">
          <el-form-item label="游戏" prop="gameId" v-if="dataForm.type == 2">
            <ny-select-search v-model="dataForm.gameId" labelKey="name" valueKey="id" url="/shop/tbgame/page" :param="{ limit: 9999 }" placeholder="请选择游戏" @change="handleChangeGameId" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="游戏映射">
            <el-table v-loading="table.dataListLoading" :data="table.dataList" style="width: 100%">
              <el-table-column prop="partnerName" label="合作商名称" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column v-if="dataForm.type == 1" prop="gameCode" label="游戏编号" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column prop="gameName" label="游戏名称" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column v-if="dataForm.type == 2" prop="gameAreaName" label="区服名称" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column v-if="dataForm.type == 1" prop="partnerGameCode" label="合作商游戏编码" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column v-if="dataForm.type == 1" prop="partnerGameName" label="合作商游戏名称" header-align="center" align="center" width="150">
                <template #default="{ row, $index }">
                  <el-select @focus="gameselectFocus(row)" @change="changeGameselect(row, $index)" v-model="row.partnerGameId" filterable clearable>
                    <el-option v-for="item in state.PartnersGameList" :key="item.partnerUserId" :label="item.partnerGameName" :value="item.partnerGameId"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column v-if="dataForm.type == 2" prop="partnerGameName" label="合作商游戏名称" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column v-if="dataForm.type == 2" prop="partnerGameAreaName" label="合作商区服名称" header-align="center" align="center" width="150">
                <template #default="{ row, $index }">
                  <el-select @focus="PartnersSelectFocus(row)" @change="changePartnersSelect(row, $index)" v-model="row.partnerGameAreaId" filterable clearable>
                    <el-option v-for="item in state.PartnersList" :key="item.partnerGameAreaId" :label="item.partnerGameAreaName" :value="item.partnerGameAreaId"> </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column v-if="dataForm.type == 2" prop="partnerGameAreaPname" label="合作商上级区服名称" header-align="center" align="center" width="150"></el-table-column>
              <!-- 空状态 -->
              <template #empty>
                <div style="padding: 68px 0">
                  <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                </div>
              </template>
            </el-table>
            <el-pagination :current-page="table.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="table.limit" :total="table.total" layout="total, sizes, prev, pager, next, jumper" @size-change="table.pageSizeChangeHandle" @current-change="table.pageCurrentChangeHandle"></el-pagination>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRefs } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
import { getDictDataList } from "@/utils/utils";
import useView from "@/hooks/useView";

const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const view = reactive({
  getDataListURL: "/shop/tbpartnergamemapping/page",
  getDataListIsPage: true,
  exportURL: "/shop/tbpartnergamemapping/export",
  deleteURL: "/shop/tbpartnergamemapping",
  deleteIsBatch: true,
  dataForm: {
    partnerName: "",
    gameName: "",
    gameAreaName: "",
    type: "1",
    partnerUserId: "",
    gameId: ""
  },
  typeList: [],
  createdIsNeed: false
});

const table = reactive({ ...useView(view), ...toRefs(view) });

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  //id
  id: "",
  //合作商id
  partnerUserId: "",
  //合作商名称
  partnerName: "",
  //游戏id
  gameId: "",
  //游戏编号
  gameCode: "",
  //游戏名称
  gameName: "",
  //区服id
  gameAreaId: "",
  //区服编码
  gameAreaCode: "",
  //区服名称
  gameAreaName: "",
  //上级区服id
  gameAreaPid: "",
  //上级区服编码
  gameAreaPcode: "",
  //上级区服名称
  gameAreaPname: "",
  //合作商游戏id
  partnerGameId: "",
  //合作商游戏编码
  partnerGameCode: "",
  //合作商游戏名称
  partnerGameName: "",
  //合作商区服id
  partnerGameAreaId: "",
  //合作商区服编码
  partnerGameAreaCode: "",
  //合作商区服名称
  partnerGameAreaName: "",
  //合作商上级区服id
  partnerGameAreaPid: "",
  //合作商上级区服编码
  partnerGameAreaPcode: "",
  //合作商上级区服名称
  partnerGameAreaPname: "",
  //映射类型 1：游戏 2：区服 【字典：partner_game_mapping_type】
  type: "1"
});

const state: {
  typeList: any[];
  dataList: any[];
  dataListLoading: boolean;
  syncLoading: boolean;
  PartnersGameList: any[];
  PartnersList: any[];
} = reactive({
  typeList: [],
  dataList: [],
  dataListLoading: false,
  syncLoading: false,
  PartnersGameList: [],
  PartnersList: []
});

const rules = ref({});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

onMounted(() => {
  const store = useAppStore();
  state.typeList = getDictDataList(store.state.dicts, "partner_game_mapping_type");
});

// 下拉框模糊搜索
// const selectgame=(data:any)=>{
//   console.log(data,'模糊搜索');

// }
// 区服选中下拉框
const PartnersSelectFocus = (row: any) => {
  // if (condition) {
  // }
};
// 游戏选中下拉框
const gameselectFocus = (row: any) => {
  console.log("单元格数据", row);
  if (dataForm.partnerUserId == "") {
    getPartnersGameInfo(row.partnerUserId);
  }
};
// 改变选中的值
const changeGameselect = (row: any, index: any) => {
  table.dataList[index].partnerGameCode = row.partnerGameId;
  console.log(row, index);
  let params = {
    id: row.id,
    partnerUserId: row.partnerUserId,
    type: row.type,
    partnerGameId: row.partnerGameId
  };
  baseService.post("/shop/tbpartnergamemapping/updateGameMapping", params).then((res) => {});
};

// 改变区服选中的下拉框值
const changePartnersSelect = (row: any, index: any) => {
  console.log(row);

  table.dataList[index].partnerGameAreaCode = row.partnerGameAreaId;
  let params = {
    id: row.id,
    partnerUserId: row.partnerUserId,
    type: row.type,
    partnerGameId: row.partnerGameId,
    partnerGameAreaId: row.partnerGameAreaId
  };
  baseService.post("/shop/tbpartnergamemapping/updateGameMapping", params).then((res) => {
    if (res.code == 0) {
      // table.getDataList();
      console.log(table.page);

      table.pageCurrentChangeHandle(table.page || 1);
    }
  });
};

// 查询合作商游戏信息
const getPartnersGameInfo = (partnerId: any) => {
  baseService.get("/shop/tbpartnergamemapping/getPartnerGameInfo?partnerUserId=" + partnerId).then((res) => {
    console.log(res);
    state.PartnersGameList = res.data;
  });
};
// 查询合作商区服信息
const getPartnersInfo = (partnerUserId: any, gameId: any) => {
  let params = {
    partnerUserId,
    gameId
  };
  baseService.post("/shop/tbpartnergamemapping/getPartnerGameAreaInfo", params).then((res) => {
    console.log("合作商区服信息", res);

    state.PartnersList = res.data;
  });
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbpartnergamemapping/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};
// 顶部切换类型
const handleNameClick = () => {
  console.log(dataForm.type);
  table.dataForm.type = dataForm.type;
  table.dataList = [];
  if (dataForm.type == "1") {
    if (dataForm.partnerUserId != "") {
      table.getDataList();
    }
  } else {
    if (dataForm.partnerUserId != "" && dataForm.gameId != "") {
      table.getDataList();
    }
  }
};
const handleSync = () => {
  state.syncLoading = true;
  baseService
    .post("/shop/tbpartnergamemapping/sync", { partnerId: dataForm.partnerUserId, type: dataForm.type, gameId: dataForm.gameId })
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success")
      });
      table.getDataList();
      state.syncLoading = false;
    })
    .catch((error) => {
      state.syncLoading = false;
    });
};
const handleChangePartnerUserId = () => {
  table.dataForm.partnerUserId = dataForm.partnerUserId;
  table.getDataList();
  if (dataForm.partnerUserId != "" && dataForm.type == "1") {
    getPartnersGameInfo(dataForm.partnerUserId);
  }
  if (dataForm.partnerUserId != "" && dataForm.gameId != "" && dataForm.type == "2") {
    getPartnersInfo(dataForm.partnerUserId, dataForm.gameId);
  }
};

const handleChangeGameId = () => {
  table.dataForm.gameId = dataForm.gameId;
  table.getDataList();
  if (dataForm.partnerUserId != "" && dataForm.gameId != "") {
    getPartnersInfo(dataForm.partnerUserId, dataForm.gameId);
  }
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/shop/tbpartnergamemapping", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
