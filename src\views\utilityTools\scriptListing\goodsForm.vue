<template>
<el-drawer v-model="visible" size="40%" class="drawer_shop">
    <template #header>
        <div class="drawer_title">商品信息</div>
    </template>
    <el-scrollbar v-loading="retrieveLoading">
        <div class="page">
            <div class="page_basic">
                <el-form :model="dataForm" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top">
                    <template v-for="(item,index) in dataForm.list" :key="index">
                        <el-form-item 
                            :prop="`list.` + index + `.value`"
                            :label="item.name" 
                            :rules="[
                                {
                                    required: item.formRule.require,
                                    message: `${(item.type == '1' || item.type == '2') ? '请输入' + item.name : '请选择' + item.name}`,
                                    trigger: `${(item.type == '1' || item.type == '2') ? 'blur' : 'change'}`,
                                },
                            ]"
                            v-if="item.name == '营地id' && gameName == '王者荣耀'"
                        >
                        <div style="display: flex;align-items: center;width: 100%;">
                            <el-input v-model="item.value" placeholder="请输入" style="flex: 1;margin-right: 12px;"></el-input>
                            <el-button type="primary" @click="getFormDataByCampsiteId(item.value)">获取营地数据</el-button>
                        </div>
                            
                        </el-form-item>
                        <el-form-item 
                            :prop="`list.` + index + `.value`"
                            :label="item.name" 
                            :rules="[
                                {
                                    required: item.formRule.require,
                                    message: `${(item.type == '1' || item.type == '2') ? '请输入' + item.name : '请选择' + item.name}`,
                                    trigger: `${(item.type == '1' || item.type == '2') ? 'blur' : 'change'}`,
                                },
                            ]"
                            v-else
                        >
                            <el-input v-model="item.value" placeholder="请输入" v-if="item.type == 1"></el-input>
                            <el-input v-model="item.value" type="textarea" :rows="2" placeholder="请输入" v-if="item.type == 2"></el-input>
                            <el-radio-group v-model="item.value" size="small" v-if="item.type == 3">
                                <el-radio :value="it.id" border v-for="it in item.formSelect">{{ it.name }}</el-radio>
                            </el-radio-group>
                            <el-checkbox-group v-model="item.value" v-if="item.type == 4">
                                <el-checkbox :label="it.name" :value="it.id" v-for="it in item.formSelect" />
                            </el-checkbox-group>
                            <ny-shop-image-upload v-model="item.value" :limit="100" :multiple="true" v-if="item.type == 5"></ny-shop-image-upload>
                        </el-form-item>
                    </template>
                </el-form>
            </div>
        </div>
    </el-scrollbar>
    <template v-slot:footer>
        <el-button :loading="retrieveLoading" :icon="Refresh" @click="retrieve()">重新获取</el-button>
        <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
</el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { scrollToFirstError } from "@/utils/method";
import { ElMessage } from "element-plus";
import { Delete, Edit, Search, Share, Refresh } from '@element-plus/icons-vue'
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

let dataForm = ref(<any>{});

const params = ref(<any>"");
const gameName = ref("");

const init = (row: any, name:any) => {
    visible.value = true;
    params.value = row;
    gameName.value = name;
    // 重置表单数据
    if (dataFormRef.value) {
        dataFormRef.value.resetFields();
    }
    getInfo();
};

// 获取信息
const retrieveLoading = ref(false);
const getInfo = () => {
    retrieveLoading.value = true;
    baseService.post(`/script/sysscriptpushtaskdetails/pullPartnerShopForm`,params.value).then((res) => {
        res.data.map((item:any) => {
            if(item.type == 4){
                item.value = item.value ? item.value.split(',') : []
            }else if(item.type == 5){
                item.value = item.value ? item.value.split(',') : []
            }
        });
        dataForm.value.list = res.data
    }).finally(()=>{
        retrieveLoading.value = false;
    })
};

// 重新获取
const retrieve = () =>{
    params.value.refresh = true;
    getInfo();
}

// 表单提交
const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            // 未填写的内容滚动到当前位置
            scrollToFirstError(dataFormRef.value);
            return false;
        }
        const param = JSON.parse(JSON.stringify(dataForm.value.list));
        param.map((item:any) => {
            if(item.type == 4){
                item.value = item.value && item.value.length ? item.value.join(',') : ''
            }else if(item.type == 5){
                item.value = item.value && item.value.length ? item.value.join(',') : ''
            }
        });
        baseService.post("/script/sysscriptpushtaskdetails/savePartnerShopForm", {
            partnerId:params.value.partnerId,
            gameId:params.value.gameId,
            scriptUserId:params.value.scriptUserId,
            shopId:params.value.shopId,
            formData: param}).then(() => {
            ElMessage.success({
                message: t("prompt.success"),
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        });
    });
};

// 获取营地Id
const getFormDataByCampsiteId = (campsiteId:any) =>{
    retrieveLoading.value = true;
    baseService.post("/script/sysscriptpushtaskdetails/getFormDataByCampsiteId",{
        partnerId:params.value.partnerId,
        gameId:params.value.gameId,
        scriptUserId:params.value.scriptUserId,
        shopId:params.value.shopId,
        campsiteId: campsiteId
    }).then(res=>{
        res.data.map((item:any) => {
            if(item.type == 4){
                item.value = item.value ? item.value.split(',') : []
            }else if(item.type == 5){
                item.value = item.value ? item.value.split(',') : []
            }
        });
        dataForm.value.list = res.data
    }).finally(()=>{
        retrieveLoading.value = false;
    })
}

defineExpose({
    init
});
</script>
<style lang="less" scoped>
    .page {
        background-color: #f0f2f5;
        // border-radius: 8px;
        padding: 12px;
    }
    .page_basic {
        background-color: #fff;
        border-radius: 8px;
        padding: 12px;
    }
</style>