<template>
  <div style="padding: 12px">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="mb-12">
        <el-button type="primary" :loading="replyLoading" @click="getExport">推送所选</el-button>
        <!-- <el-button v-show="props.propData.partnerName == '盼之'" style="margin-left: 8px" @click="pregetExport">短信验证码校验</el-button> -->
      </div>
      <div class="button-group">
        <div
          class="button-item"
          v-for="(item, index) in stateList"
          :key="index"
          :class="{ active: currentTableIndex == item.value }"
          @click="
            () => {
              currentTableIndex = +item.value;
              getData();
            }
          "
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="setUptable">
      <el-table border ref="tableRef" @select="onSelect" @select-all="onSelectAll" cell-class-name="ch-56" :data="state.dataList" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column property="shopCode" label="商品编码" align="center" width="100"/>
        <el-table-column show-overflow-tooltip property="shopName" label="商品信息" align="center" width="340">
          <template #default="scope">
              <div class="shoping">
                <el-image style="height: 68px; width: 120px" :src="scope.row.shopLog" :preview-src-list="[scope.row.shopLog]" preview-teleported fit="cover" />
                <div class="info">
                  <div class="title mle" v-html="scope.row.shopTitle"></div>
                  <div class="sle" style="width: 185px; text-align: left">
                    {{ `${scope.row.gameName} / ${scope.row.areaName}` }}
                  </div>
                </div>
              </div>
            </template>
        </el-table-column>
        <el-table-column property="gameAccount" label="游戏账号" align="center" width="140"/>
        <el-table-column property="pushStatus" label="推送状态" align="center">
          <template #default="scope">
            <!-- 状态 -->
            <el-tag :type="scope.row.pushStatus == 2 ? 'danger' : 'success'">{{ scope.row.pushStatusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="price" label="零售价(元)" align="center"/>
        <el-table-column property="remark" label="商品状态" align="center" width="140">
          <template #default="{ row }">
            <el-tag type="warning" v-if="row.shopStatus == '1'">待上架</el-tag>
            <el-tag type="primary" v-if="row.shopStatus == '2'">已上架</el-tag>
            <el-tag type="danger" v-if="row.shopStatus == '3'">已下架</el-tag>
            <el-tag type="success" v-if="row.shopStatus == '4'">已出售</el-tag>
            <el-tag type="warning" v-if="row.shopStatus == '5'">问题账号</el-tag>
            <el-tag type="danger" v-if="row.shopStatus == '6'">作废账号</el-tag>
            <el-tag type="success" v-if="row.shopStatus == '7'">交易中</el-tag>
            <el-tag type="info" v-if="row.shopStatus == '8'">重新上架</el-tag>
            <el-tag type="warning" v-if="row.shopStatus == '9'">重新待上架</el-tag>
            <el-tag type="primary" v-if="row.shopStatus == '10'">出售待审核</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="pushTime" label="推送时间" align="center" width="160"/>
        <el-table-column :hide-after="0" property="whetherScanQrCode" label="操作" align="center" width="160" fixed="right">
          <template #default="scope">
            <el-button v-if="scope.row.typeName == '推送失败'" type="primary" text bg @click="getAgainExport(scope.row)">重新推送</el-button>
            <div v-else>-</div>
            <!-- <el-popover placement="right" :width="165" trigger="click">
              <template #reference>
                <el-link @click="showCode(scope.row, scope.$index)" :type="!scope.row.whetherScanQrCode ? 'info' : 'primary'">{{ !scope.row.whetherScanQrCode ? "" : "查看二维码" }}</el-link>
              </template>
              <el-image v-loading="!scope.row.codeUrl" :src="scope.row.codeUrl" style="height: 140px; width: 140px" frameborder="0">
                <template #error>
                  <div class="image-slot">
                    <el-icon><icon-picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </el-popover> -->
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <div class="flx-between">
        <div class="pt-15">
          <slot name="footer"></slot>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px;">
            <span style="font-weight: bold; color: #1d2129">零售价</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </div>
        <el-pagination
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100, 500, 1000]"
          :page-size="queryParams.pageSize"
          :total="state.count"
          layout="total, sizes, prev, pager, next, jumper"
          :hide-on-single-page="false"
          @size-change="TableSizeChangeFn"
          @current-change="TableCurrentChangeFn"
        ></el-pagination>
      </div>
    </div>

    <pushValite ref="pushValiteRef"></pushValite>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onUnmounted, reactive, ref, toRefs, watch, inject } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { TableInstance } from "element-plus";
import pushValite from "./pushValite.vue";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";

const startPushStaskContent = inject("startPushStaskContent");
const { t } = useI18n();
const emit = defineEmits(["refreshDataList","pushChange"]);
const tableRef = ref<TableInstance>();
const replyLoading = ref(false); // 推送
const currentTableIndex = ref(0); //当前类型
const props = defineProps({
  propData: {
    type: Object,
    required: true
  }
});
const state = reactive({
  currowId: "",
  selectionList: [],
  dataList: [],
  count: 0,
  pushForm: {
    gameId: "",
    partnerId: "",
    shopIdList: [],
    taskId: "",
    taskType: ""
  }
});
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  size: 10
});
// 状态
const stateList = [
  { label: "全部推送", value: 0 },
  { label: "推送成功", value: 1 },
  { label: "推送失败", value: 2 }
];
const pushValiteRef = ref();
// 总表单提交
const btnLoading = ref(false);
// 推送
const pregetExport = async () => {
  await nextTick();
  pushValiteRef.value.init(state.pushForm.partnerId);
};
const getExport = () => {
  let form = {
    gameId: "",
    partnerId: "",
    shopIdList: [],
    taskId: "",
    taskType: ""
  };
  form.gameId = state.pushForm.gameId;
  form.partnerId = state.pushForm.partnerId;
  form.taskType = state.pushForm.taskType;
  form.taskId = state.pushForm.id;
  form.shopIdList = state.selectionList.map((ele: any) => ele.shopId);
  replyLoading.value = true;
  baseService
    .post("/script/sysscriptpushtask/submitTask", { ...form })
    .then((res) => {
      if (res.code == 0) {
        replyLoading.value = false;
        ElMessage.success("推送成功");
        emit("pushChange");
        startPushStaskContent();
      }
    })
    .finally(() => {
      replyLoading.value = false;
    });
};

// 重新推送
const getAgainExport = (row: any) => {
  let form = {
    gameId: "",
    partnerId: "",
    shopIdList: [],
    taskId: "",
    taskType: ""
  };
  form.gameId = state.pushForm.gameId;
  form.partnerId = state.pushForm.partnerId;
  form.taskType = state.pushForm.taskType;
  form.taskId = state.pushForm.id;
  form.shopIdList = [row.shopId];
  replyLoading.value = true;
  baseService
    .post("/script/sysscriptpushtask/submitTask", { ...form })
    .then((res) => {
      if (res.code == 0) {
        replyLoading.value = false;
        ElMessage.success("推送成功");
      }
    })
    .finally(() => {
      replyLoading.value = false;
    });
};

// 选择列表项
const getData = () => {
  let pushStatus_ = currentTableIndex.value;
  if (currentTableIndex.value == 0) {
    pushStatus_ = null;
  }
  requestLoading.value = true;
  baseService
    .post("/script/sysscriptpushtaskdetails/detail", {
      id: state.currowId,
      pushStatus: pushStatus_,
      ...queryParams
    })
    .then((res: any) => {
      if (res.code == 0) {
        requestLoading.value = false;
        state.dataList = res.data.list || [];
        state.count = res.data.total;
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};
const codeResultForm = reactive({
  qrsig: undefined,
  taskId: undefined,
  taskDetailId: undefined
});
const timer = ref();
const showCode = (obj?: any, index: any) => {
  if (!obj.whetherScanQrCode) return;
  state.dataList[index].loadingUrl = true;
  baseService
    .post("/script/sysscriptpushtask/getQrCode", {
      taskDetailId: obj.taskDetailId
    })
    .then((res) => {
      state.dataList[index].codeUrl = "data:image/png;base64," + res.data.image;
      state.dataList[index].loadingUrl = false;
      codeResultForm.qrsig = res.data.qrsig;
      codeResultForm.taskId = res.data.taskId;
      codeResultForm.taskDetailId = obj.taskDetailId;
      timer.value = setInterval(() => {
        callReturnCode();
      }, 5000);
    })
    .catch((err) => {
      state.dataList[index].codeUrl = null;
    });
};
const callReturnCode = () => {
  baseService.post("/script/sysscriptpushtask/getQrCodeResult", { ...codeResultForm }).then((res) => {
    if (res?.code == -2) {
      // 循环
    } else if (res?.code == "0") {
      if (timer.value) {
        clearInterval(timer.value);
      }
      ElMessage.success("扫码成功！");
    } else if (res?.code == "-1") {
      if (timer.value) {
        clearInterval(timer.value);
      }
      ElMessage.warning(res?.msg);
    }
  });
};
const onSelect = (arr: any) => {
  state.selectionList = arr;
};
const onSelectAll = (arr: any) => {
  state.selectionList = arr;
};
const clearSelected = (rows?: any, ignoreSelectable?: boolean) => {
  if (rows) {
    rows.forEach((row: any) => {
      tableRef.value!.toggleRowSelection(row, undefined, ignoreSelectable);
    });
  } else {
    tableRef.value!.clearSelection();
    state.selectionList = [];
  }
};
// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.pageSize = val;
  queryParams.page = 1;
  getData();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getData();
};
// 表单初始化
const init = (data?: any) => {
  state.currowId = data.id;
  state.pushForm = data;
  getData();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.price) total += item?.price || 0;
  });
  return total.toFixed(2);
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
watch(
  () => props.propData,
  () => {
    init(props.propData);
  },
  { immediate: true, deep: true }
);
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.button-group {
  width: fit-content;
  background: #f2f2f2;
  border-radius: 6px;
  margin-bottom: 12px;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
.setUptable {
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .title {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid #4165d7;
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}

.shoping {
    display: flex;
    align-items: center;
    cursor: pointer;

    .el-image {
      border-radius: 4px;
      margin-right: 8px;
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .title {
        color: var(--el-color-primary);
        white-space: pre-wrap;
        text-align: left;
      }
    }
  }
</style>
<style lang="scss">
.infoDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
