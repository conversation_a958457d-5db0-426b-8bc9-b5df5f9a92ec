<template>
  <div class="sxPage" ref="xsPageRef">
    <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
      <el-descriptions-item>
        <template #label
          ><span>账号<span style="color: red">*</span></span></template
        >
        <el-input v-model="dataForm.user" placeholder="账号" style="width: 100%" @input="inputChange('user', dataForm.user)"></el-input>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label
          ><span>密码<span style="color: red">*</span></span></template
        >
        <el-input v-model="dataForm.pwd" placeholder="密码" style="width: 100%" @input="inputChange('pwd', dataForm.pwd)"></el-input>
      </el-descriptions-item>
      <!-- <el-descriptions-item v-if="resultsShow && !props.taskId">
                <template #label><span>系统<span style="color: red;">*</span></span></template>
                <el-select v-model="dataForm.xiaosuanSystemType" placeholder="系统" style="width: 100%;" clearable>
                    <el-option
                        v-for="item in systemList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-descriptions-item> -->
    </el-descriptions>
    <div class="flx-align-center" v-if="!resultsShow">
      <el-button type="primary" @click="submit">登录账号</el-button>
      <div class="recognition-fail">
        <el-icon color="#FF7D00"><WarningFilled /></el-icon>
        <text>先登录，再获取</text>
      </div>
    </div>
    <div v-else class="flx-align-center">
      <el-button type="primary" v-if="!props.taskId" :loading="oneClickAccessLoading" @click="oneClickAccess">一键获取</el-button>
      <div class="flx-align-center">
        <el-button type="info" :loading="oplogLoading" @click="getOplog" v-if="props.taskId">操作日志</el-button>
        <el-button type="warning" :loading="taskResultLoading" @click="getTaskResult" v-if="props.taskId">查询结果</el-button>
        <div class="recognition-fail" v-if="taskResultMessage">
          <el-icon color="#FF7D00"><WarningFilled /></el-icon>
          <text>{{ taskResultMessage }}</text>
        </div>
      </div>
    </div>

    <!-- 小算接口页面 -->
    <iframe id="iframe" :key="iframeKey" ref="xsIframe" src="https://rpa-sdk.51bees.com/verifyUser.html" style="position: fixed; top: 0; left: 0; width: 100vh; height: 100vh; display: none" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>

    <!-- 输入验证码 -->
    <el-dialog v-model="dialogVisible" title="自动上号验证码" width="480" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="1">
        <el-descriptions-item label="游戏名称">{{ props.gameName }}</el-descriptions-item>
        <el-descriptions-item label="游戏账号">{{ dataForm.user }}</el-descriptions-item>
        <el-descriptions-item label="验证码">
          <el-input type="number" v-model="dataForm.verifyCode" placeholder="验证码" style="width: 100%" clearable></el-input>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button type="primary" @click="dialogSubmit">立即验证</el-button>
      </template>
    </el-dialog>

    <!-- 查看操作日志 -->

    <el-dialog v-model="oplogShow" title="操作日志" width="1200">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="taskId" label="任务ID" align="center" width="200" />
        <el-table-column prop="type" label="类型" align="center" width="120" />
        <el-table-column prop="direction" label="方向" align="center" width="100">
          <template #default="{ row }">
            <span v-if="row.direction == 1">发送</span>
            <span v-if="row.direction == 2">接收</span>
          </template>
        </el-table-column>
        <el-table-column prop="resultCode" label="结果编码" align="center" width="120" />
        <el-table-column prop="resultMsg" label="结果信息" align="center" width="120" />
        <el-table-column prop="resultBody" label="结果体" align="center" show-overflow-tooltip />
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <template #footer>
        <el-button type="primary" @click="oplogShow = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 加载 -->
    <div class="loadingPage" v-if="xiaosuanLoading">
      <div class="loader">
        <label>数据加载中</label>
        <div class="loaderBar"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, onUnmounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
const emit = defineEmits(["taskId", "updateInput", "refreshInfo", "xsVerification", "oneClickAccess"]);

const props = defineProps({
  gameCode: String,
  gameId: String,
  gameName: String,
  taskId: String,
  userName: String,
  passWord: String,
  xsGameInfo: <any>{},
  serverId: String
});

watch(
  () => props.userName,
  (newVal) => {
    dataForm.user = props.userName;
  }
);
watch(
  () => props.passWord,
  (newVal) => {
    dataForm.pwd = props.passWord;
  }
);
watch(
  () => props.taskId,
  (newVal) => {
    if (props.taskId) {
      resultsShow.value = true;
      // getOplog();
    }
  }
);

const xsPageRef = ref();
const styleXs = ref("");
const iframeKey = ref(0);

onMounted(() => {
  // styleXs.value = `width: ${xsPageRef.value.offsetWidth}px; height: ${xsPageRef.value.offsetHeight}px;`;
  dataForm.user = props.userName;
  dataForm.pwd = props.passWord;
  if (props.taskId) {
    resultsShow.value = true;
  }
});

// 查询日志
const oplogShow = ref(false);
const oplogLoading = ref(false);
const tableData = ref([]);
const getOplog = () => {
  oplogLoading.value = true;
  baseService
    .get("/xiaosuan/TaskLog/page", { taskId: props.taskId })
    .then((res) => {
      if (res.code == 0) {
        tableData.value = res.data.list;
        oplogShow.value = true;
      }
    })
    .finally(() => {
      oplogLoading.value = false;
    });
};

const inputChange = (type: string, value: string) => {
  emit("updateInput", { [type]: value });
};

const taskResultMessage = ref("");
const taskResultLoading = ref(false);
const getTaskResult = () => {
  taskResultLoading.value = true;
  baseService
    .get("/xiaosuan/task/getTaskResult/" + props.taskId)
    .then((res) => {
      if (res.code == 0) {
        taskResultMessage.value = res.msg;
        if (res.msg == "任务执行成功!") {
          emit("refreshInfo");
        }
      }
    })
    .finally(() => {
      taskResultLoading.value = false;
    });
};

const dialogVisible = ref(false);
const resultsShow = ref(false);

const systemList = [
  { label: "ios", value: "0" },
  { label: "android", value: "1" },
  { label: "pc", value: "2" }
];

const dataForm = reactive({
  user: "",
  pwd: "",
  verifyCode: "",
  xiaosuanSystemType: ""
});

const submit = () => {
  if (!dataForm.user) {
    ElMessage.warning("请输入账号");
    return;
  }
  if (!dataForm.pwd) {
    ElMessage.warning("请输入密码");
    return;
  }
  nextTick(() => {
    getOnLoad();
  });
};


const xiaosuanLoading = ref(false);

// 开始验号
const xsIframe = ref();
const gamePlatformList = reactive([
  {gameCode:'CYHXSY',gamePlatform:'qq'},
  {gameCode:'SMZHSY',gamePlatform:'qq'},
  {gameCode:'SJZXD',gamePlatform:'qq'},
  {gameCode:'YJWJ',gamePlatform:'netease_dashen'},
  {gameCode:'QQDZZ',gamePlatform:'giant'},
])
const getOnLoad = (type?: any) => {
  const frame = xsIframe.value.contentWindow;
  // 自动调用必须放在onload中,通过事件调用则不用
  const data = {
    user: dataForm.user,
    pwd: dataForm.pwd,
    gameId: props.xsGameInfo.outId,
    gamePlatform: gamePlatformList.find((item:any)=> item.gameCode == props.gameCode)?.gamePlatform,
    actionType: "login",
    channel: "yxf_client"
  };
  console.log(data, "====== 登录提交 ======");
  frame.postMessage(data, "*"); //开始验号
  xiaosuanLoading.value = true;
  getResultMonitoring();
};

// 结果监听
const getResultMonitoring = () => {
  console.log("进来了开始执行监听了");
  window.addEventListener("message", MonitoringFunction);
};

// 监听方法
const MonitoringFunction = (e: any) => {
  xiaosuanLoading.value = false;
  let data = e.data; //返回结果
  let origin = e.origin; //返回的域名
  console.log(data, "======= 监听回调 =======");

  if (origin == "https://rpa-sdk.51bees.com" && !!data.actionType) {
    if (data.actionType == "data") {
      //data 返回的数据 结果数据
      if (data.data.code == 3001) {
        // 验号成功
        ElMessage.success("验号成功");
        resultsShow.value = true;
        emit("xsVerification", true);
        window.removeEventListener("message", MonitoringFunction);
      } else {
        ElMessage.warning(data.data.message);
        xiaosuanLoading.value = false;
        iframeKey.value++;//更新iframe DOM重新验号
      }
    } else if (data.actionType == "VerifyCode") {
      //通知获取短信显示
      dialogVisible.value = true;
      const actionData = JSON.parse(data.data);
      if (actionData.number == 2) {
        ElMessage.warning("验证码错误");
      }
      window.removeEventListener("message", MonitoringFunction);
    } else if (data.actionType == "slider") {
      //qq滑块显示
    } else if (data.actionType == "sliderEnd") {
      //qq滑块结束
    } else if (data.actionType == "submitHide") {
      //提交隐藏
    } else if (data.actionType == "closeSubmitHide") {
      //关闭隐藏
    }
  }
};

// 短信验证码 - 立即提交
const dialogSubmit = () => {
  if (!dataForm.verifyCode) {
    ElMessage.warning("请输入验证码");
    return;
  }
  const frame = xsIframe.value.contentWindow;
  // 自动调用必须放在onload中,通过事件调用则不用
  const data = {
    user: dataForm.user,
    pwd: dataForm.pwd,
    gameId: props.xsGameInfo.outId,
    gamePlatform: "qq",
    actionType: "verifyCode",
    verifyCode: dataForm.verifyCode,
    channel: "yxf_client"
  };
  console.log(data, "====== 验证码提交");
  frame.postMessage(data, "*"); //开始验号
  xiaosuanLoading.value = true;
  dialogVisible.value = false;
  getResultMonitoring();
};

// 一键获取
const oneClickAccessLoading = ref(false);
const oneClickAccess = () => {
  if (!dataForm.user) {
    ElMessage.warning("请输入账号");
    return;
  }
  if (!dataForm.pwd) {
    ElMessage.warning("请输入密码");
    return;
  }
  // if(!dataForm.xiaosuanSystemType){
  //     ElMessage.warning('请选择系统');
  //     return
  // }
  if (!props.serverId) {
    ElMessage.warning("请选择系统区服");
    return;
  }

  if (xiaosuanLoading.value) {
    return;
  }
  xiaosuanLoading.value = true;
  emit("oneClickAccess", true);
  baseService
    .post("/xiaosuan/task/startActionTask", {
      gameId: props.gameId,
      userName: dataForm.user,
      password: dataForm.pwd,
      serverId: props.serverId
      // xiaosuanSystemType: dataForm.xiaosuanSystemType
    })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("任务执行成功请稍后查看结果");
        emit("taskId", res.data.id);
      }
    })
    .finally(() => {
      xiaosuanLoading.value = false;
      emit("oneClickAccess", false);
    });
};

onUnmounted(() => {
  window.removeEventListener("message", MonitoringFunction);
});

defineExpose({
  oneClickAccess
});
</script>

<style lang="less" scoped>
.sxPage {
  position: relative;
  .loadingPage {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    /* From Uiverse.io by TemplateVentures */
    .loader {
      width: 350px;
      height: 140px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
    }

    label {
      color: #0031f2;
      font-size: 18px;
      animation: bit 0.6s alternate infinite;
    }

    @keyframes bit {
      from {
        opacity: 0.3;
      }
      to {
        opacity: 1;
      }
    }
    /* From Uiverse.io by dylanharriscameron */
    .loaderBar {
      width: calc(160px / 0.707);
      height: 10px;
      background: #f9f9f9;
      border-radius: 10px;
      border: 1px solid #006dfe;
      position: relative;
      overflow: hidden;
    }

    .loaderBar::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 5px;
      background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
      animation: fillProgress 10s ease-in-out infinite, lightEffect 1s infinite linear;
      animation-fill-mode: forwards;
    }

    @keyframes fillProgress {
      0% {
        width: 0;
      }

      33% {
        width: 33.333%;
      }

      66% {
        width: 66.67%;
      }

      100% {
        width: 100%;
      }
    }

    @keyframes lightEffect {
      0%,
      20%,
      40%,
      60%,
      80%,
      100% {
        background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
      }

      10%,
      30%,
      50%,
      70%,
      90% {
        background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px, rgba(255, 255, 255, 0.3) 0 40px) right/200% 100%;
      }
    }
  }
}
.recognition-fail {
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  margin-left: 12px;

  text {
    font-weight: 400;
    font-size: 13px;
    line-height: 22px;
    color: #ff7d00;
    margin-left: 4px;
  }
}
</style>
