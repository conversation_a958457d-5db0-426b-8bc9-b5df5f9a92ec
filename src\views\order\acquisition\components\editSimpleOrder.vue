<template>
  <el-drawer v-model="visible" :title="'简易流程回收订单'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="994" class="ny-drawer">
    <el-card class="cardDescriptions" style="padding: 0">
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
        <template v-if="!dataForm.checkCollect">
          <div class="titleSty">回收信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label
                ><span>游戏名称<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="游戏名称" prop="gameId">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.gameName }}
                </span>
                <el-select v-else v-model="dataForm.gameId" filterable placeholder="请选择游戏名">
                  <el-option v-for="(item, index) in gamesList" :key="index" :label="item.title" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收渠道<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="回收渠道" prop="channelId">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.channelName }}
                </span>
                <el-cascader v-else style="width: 100%" :show-all-levels="false" clearable v-model="selectedOptions" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择出售渠道" @change="handleChange" />
                <!-- <ny-select-search v-else v-model="dataForm.channelId" labelKey="title" valueKey="id" url="/channel/channel/page" :param="{ limit: 9999, state: 0, channelType: 1 }" placeholder="请选择回收渠道" @changeReturnEntity="getChannelName" /> -->
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>游戏账号<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="游戏账号" prop="gameAccount">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.gameAccount }}
                </span>
                <el-input v-else v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <span>游戏密码<span v-if="!checkInfo" style="color: red">*</span></span>
              </template>
              <el-form-item label="游戏密码" prop="gamePassword">
                <div v-if="dataForm.state == '入库且已打款' || checkInfo">
                  <div style="display: flex; align-items: center; justify-content: space-between; width: 280px">
                    <span>{{ isShowGamePassword ? dataForm.gamePassword : "******" }}</span>
                    <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                      <View v-if="!isShowGamePassword" />
                      <Hide v-if="isShowGamePassword" />
                    </el-icon>
                  </div>
                </div>
                <el-input v-else v-model="dataForm.gamePassword" type="password" show-password placeholder="请输入游戏密码"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收人<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="回收人" prop="recycleUser">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.realName1 }}
                </span>
                <ny-select-search v-else v-model="dataForm.recycleUser" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择回收人" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>绑定手机号码<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="绑定手机号码" prop="bindingPhone">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.bindingPhone }}
                </span>
                <el-input v-else v-model="dataForm.bindingPhone" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收价(元)<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="回收价(元)" prop="recyclePrice">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ dataForm.recyclePrice }}
                </span>
                <el-input v-else v-model="dataForm.recyclePrice" placeholder="请输入回收价"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>支付方式<span v-if="!checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="支付方式" prop="payWay">
                <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                  {{ ["支付宝", "微信", "银行卡"][dataForm.payWay - 1] }}
                </span>
                <el-select v-else v-model="dataForm.payWay" clearable placeholder="请选择支付方式">
                  <el-option v-for="(item, index) in ['支付宝', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <template v-if="dataForm.payWay == 1">
              <el-descriptions-item class-name="noneSelfRight">
                <template #label><span>支付宝订单号</span></template>
                <el-form-item label="支付宝订单号" prop="alipayOrderNo">
                  <div v-if="checkInfo">
                    {{ dataForm.alipayOrderNo || "-" }}
                  </div>
                  <el-input v-else v-model="dataForm.alipayOrderNo" placeholder="请输入支付宝订单号" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
            </template>

            <!-- 未上传付款截图则状态为已换绑 else 已付款 -->
            <el-descriptions-item>
              <template #label
                ><span>付款截图<span v-if="(dataForm.state == '未入库已打款' || dataForm.state == '入库且已打款') && !checkInfo" style="color: red">*</span></span></template
              >
              <el-form-item label="付款截图" :prop="dataForm.state == '未入库已打款' || dataForm.state == '入库且已打款' ? 'payImg' : ''">
                <div v-if="checkInfo">
                  <span v-if="!dataForm.payImg || dataForm.payImg.length < 1">-</span>
                  <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                    <el-image style="width: 100px" :src="item" v-for="item in dataForm.payImg.split(',')" alt="" :preview-src-list="dataForm.payImg.split(',')" />
                  </div>
                </div>
                <ny-upload v-else v-model:imageUrl="dataForm.payImg" :limit="3" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>二次实名截图</span></template>
              <el-form-item label="二次实名截图" prop="realNameImg">
                <div v-if="checkInfo">
                  <span v-if="!dataForm.realNameImg || dataForm.realNameImg.length < 1">-</span>
                  <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                    <el-image style="width: 100px" :src="item" v-for="item in dataForm.realNameImg.split(',')" alt="" :preview-src-list="dataForm.realNameImg.split(',')" />
                  </div>
                </div>
                <ny-upload v-model:imageUrl="dataForm.realNameImg" v-else :limit="3" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template #label><span>换绑凭证</span></template>
              <el-form-item label="换绑凭证" prop="changeBindingVoucher">
                <div v-if="checkInfo">
                  <span v-if="!dataForm.changeBindingVoucher || dataForm.changeBindingVoucher.length < 1">-</span>
                  <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                    <el-image style="width: 100px" :src="item" v-for="item in dataForm.changeBindingVoucher.split(',')" alt="" :preview-src-list="dataForm.changeBindingVoucher.split(',')" />
                  </div>
                </div>
                <ny-upload v-model:imageUrl="dataForm.changeBindingVoucher" v-else :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>自编码</span></template
              >
              <el-form-item label="自编码" prop="ownCoding">
                <div v-if="checkInfo">
                  {{ dataForm.ownCoding || "-" }}
                </div>
                <el-input v-else v-model="dataForm.ownCoding" placeholder="自编码"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>应急手机号</span></template
              >
              <el-form-item label="应急手机号" prop="emergencyPhone">
                <div v-if="checkInfo">
                  {{ dataForm.emergencyPhone || "-" }}
                </div>
                <el-input v-else v-model="dataForm.emergencyPhone" placeholder="应急手机号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>合同签署</span></template>
              <el-form-item label="合同签署" prop="contractSign">
                <div v-if="checkInfo">
                  {{ ["未签署", "已签署"][+dataForm.contractSign] }}
                </div>
                <el-select v-else v-model="dataForm.contractSign" clearable placeholder="请选择合同签署">
                  <el-option v-for="(item, index) in ['未签署', '已签署']" :label="item" :value="index" :key="index"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>合同附件</span></template>
              <el-form-item label="合同附件" prop="contractAttachment">
                <div v-if="checkInfo && !dataForm.contractAttachment">-</div>
                <ny-upload-file v-else style="margin-bottom: -10px" tip="" v-model:fileSrc="dataForm.contractAttachment" :isDrag="false" :limit="1" :fileSize="5" :isSelfSty="false">
                  <template #content>
                    <el-button :icon="Plus">上传</el-button>
                  </template>
                </ny-upload-file>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template #label><span>备注</span></template>
              <el-form-item label="备注" prop="remark">
                <div v-if="checkInfo">
                  {{ dataForm.remark || "-" }}
                </div>
                <el-input v-else type="textarea" v-model="dataForm.remark" placeholder="请输入备注" :rows="5"></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <div class="titleSty" style="margin-top: 10px">包赔信息</div>
        <el-descriptions :column="2" border class="descriptions">
          <el-descriptions-item>
            <template #label
              ><span>卖方姓名<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="卖方姓名" prop="saleUserName">
              <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                {{ dataForm.saleUserName || "-" }}
              </span>
              <el-input v-else v-model="dataForm.saleUserName" placeholder="请输入卖方姓名"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label
              ><span>卖方手机号<span v-if="!checkInfo" style="color: red">*</span></span></template
            >
            <el-form-item label="卖方手机号" prop="customerPhone">
              <span v-if="dataForm.state == '入库且已打款' || checkInfo">
                {{ dataForm.customerPhone || "-" }}
              </span>
              <el-input v-else v-model="dataForm.customerPhone" placeholder="请输入卖方手机号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>卖方身份证</span></template>
            <el-form-item label="卖方身份证" prop="sellerIdCard">
              <span v-if="checkInfo">
                {{ dataForm.sellerIdCard || "-" }}
              </span>
              <el-input v-else v-model="dataForm.sellerIdCard" placeholder="请输入卖方身份证"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>微信号</span></template>
            <el-form-item label="微信号" prop="wxNo">
              <span v-if="checkInfo"> {{ dataForm.wxNo || "-" }} </span>
              <el-input v-else v-model="dataForm.wxNo" placeholder="请输入微信号"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>所在城市</span></template>
            <el-form-item label="所在城市" prop="city">
              <span v-if="checkInfo">{{ dataForm.city ? getCodeToText(dataForm.city) : "" }}</span>
              <el-cascader v-else style="width: 100%" v-model="dataForm.city" :options="regionData" placeholder="请选择所在城市" clearable @change="handleCityChange" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>详细地址</span></template>
            <el-form-item label="详细地址" prop="detailedAddress">
              <span v-if="checkInfo">{{ dataForm.detailedAddress || "-" }}</span>
              <el-input v-else v-model="dataForm.detailedAddress" placeholder="请输入详细地址" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>身份证正面</span></template>
            <el-form-item label="身份证正面" prop="idCardFront">
              <div v-if="checkInfo">
                <span v-if="!dataForm.idCardFront || dataForm.idCardFront.length < 1">-</span>
                <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                  <el-image style="width: 100px" :src="item" v-for="item in dataForm.idCardFront.split(',')" alt="" :preview-src-list="dataForm.idCardFront.split(',')" />
                </div>
              </div>
              <ny-upload v-model:imageUrl="dataForm.idCardFront" v-else :limit="3" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>身份证反面</span></template>
            <el-form-item label="身份证反面" prop="idCardBack">
              <div v-if="checkInfo">
                <span v-if="!dataForm.idCardBack || dataForm.idCardBack.length < 1">-</span>
                <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
                  <el-image style="width: 100px" :src="item" v-for="item in dataForm.idCardBack.split(',')" alt="" :preview-src-list="dataForm.idCardBack.split(',')" />
                </div>
              </div>
              <ny-upload v-model:imageUrl="dataForm.idCardBack" v-else :limit="3" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>紧急联系人1</span></template>
            <el-form-item label="紧急联系人1" prop="emergencyContacts1Type">
              <div v-if="checkInfo">
                {{ ["-", "父亲", "母亲"][+dataForm.emergencyContacts1Type] }}
              </div>
              <el-radio-group v-else v-model="dataForm.emergencyContacts1Type">
                <el-radio :value="1">父亲</el-radio>
                <el-radio :value="2">母亲</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>紧急联系人1名称</span></template>

            <el-form-item label="紧急联系人1名称" prop="emergencyContacts1Name">
              <div v-if="checkInfo">
                {{ dataForm.emergencyContacts1Name || "-" }}
              </div>
              <el-input v-else v-model="dataForm.emergencyContacts1Name" placeholder="请输入紧急联系人1名称" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item class-name="noneSelfRight">
            <template #label><span>紧急联系人1手机</span></template>
            <el-form-item label="紧急联系人1手机" prop="emergencyContacts1Phone">
              <div v-if="checkInfo">
                {{ dataForm.emergencyContacts1Phone || "-" }}
              </div>
              <el-input v-else v-model="dataForm.emergencyContacts1Phone" placeholder="请输入紧急联系人1手机" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
          <el-descriptions-item class-name="noneSelfRight">
            <template #label><span>紧急联系人2</span></template>
            <el-form-item label="紧急联系人2" prop="emergencyContacts2Type">
              <div v-if="checkInfo">
                {{ ["-", "父亲", "母亲"][+dataForm.emergencyContacts2Type] }}
              </div>
              <el-radio-group v-else v-model="dataForm.emergencyContacts2Type">
                <el-radio :value="1">父亲</el-radio>
                <el-radio :value="2">母亲</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>紧急联系人2名称</span></template>
            <el-form-item label="紧急联系人2名称" prop="emergencyContacts2Name">
              <div v-if="checkInfo">
                {{ dataForm.emergencyContacts2Name || "-" }}
              </div>
              <el-input v-else v-model="dataForm.emergencyContacts2Name" placeholder="请输入紧急联系人2名称" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item class-name="noneSelfRight">
            <template #label><span>紧急联系人2手机</span></template>
            <el-form-item label="紧急联系人2手机" prop="emergencyContacts2Phone">
              <div v-if="checkInfo">
                {{ dataForm.emergencyContacts2Phone || "-" }}
              </div>
              <el-input v-else v-model="dataForm.emergencyContacts2Phone" placeholder="请输入紧急联系人2手机" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </el-card>

    <template #footer>
      <template v-if="!checkInfo && !dataForm.checkCollect">
        <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
        <el-button v-if="dataForm.state != '入库且已打款'" plain :loading="btnLoading" type="primary" @click="submitForm(true)">保存并完善信息</el-button>
        <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
      </template>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { Plus } from "@element-plus/icons-vue";
import { useAppStore } from "@/store";
import { regionData, codeToText } from "element-china-area-data";
import { id } from "element-plus/es/locale";
const store = useAppStore();
const emits = defineEmits(["refresh", "completeInfo"]);
const visible = ref(false);
const isShowGamePassword = ref(false);
const gamesList = ref(<any>[]);
const checkInfo = ref(false);
const init = (row?: any, list?: any) => {
  visible.value = true;
  getChannelId(row.channelId);
  getChannelTree();
  if (row && row.id) {
    dataForm.value = Object.assign({}, row);
    console.log(row, "====== row ========");

    checkInfo.value = row.isCheck;
    delete dataForm.value.isCheck;
    dataForm.value.city = dataForm.value.city && dataForm.value.city.length > 0 ? dataForm.value.city.split(",") : [];
  }
  gamesList.value = list?.filter((item) => item.id);
};

const dataForm = ref(<any>{
  contractSign: 0
});

const rules = reactive({
  channelId: [{ required: true, message: "请选择回收渠道", trigger: "change" }],
  gameId: [{ required: true, message: "请输入游戏名称", trigger: "change" }],
  district: [{ required: true, message: "请选择大区", trigger: "change" }],
  gameAccount: [{ required: true, message: "请输入游戏账号", trigger: "blur" }],
  gamePassword: [{ required: true, message: "请输入游戏密码", trigger: "blur" }],
  purchasePrice: [{ required: true, message: "请输入回收价格", trigger: "blur" }],
  recycleUser: [{ required: true, message: "请选择回收人", trigger: "change" }],
  bindingPhone: [
    { required: true, message: "请输入绑定手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  recyclePrice: [{ required: true, message: "请输入回收价", trigger: "blur" }],
  payWay: [{ required: true, message: "请选择支付方式", trigger: "change" }],
  saleUserName: [{ required: true, message: "请输入卖方姓名", trigger: "blur" }],
  payImg: [{ required: true, message: "请上传付款凭证", trigger: "blur" }],
  customerPhone: [
    { required: true, message: "请输入卖方手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  emergencyPhone: [
    { required: false, message: "请输入应急手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ]
});
// 处理城市选择变化
const handleCityChange = (value: any) => {
  console.log("选择的城市：", value); // value 是一个数组，例如 ['省', '市', '区']
};
// 回收渠道名称
const getChannelName = (data: any) => {
  dataForm.value.channelName = data.title;
};
const getCodeToText = (codeArray: any) => {
  if (null === codeArray) {
    codeArray = codeArray.split(",");
  }

  let area = "";
  switch (codeArray.length) {
    case 1:
      area += codeToText[codeArray[0]];
      break;
    case 2:
      area += codeToText[codeArray[0]] + "/" + codeToText[codeArray[1]];
      break;
    case 3:
      area += codeToText[codeArray[0]] + "/" + codeToText[codeArray[1]] + "/" + codeToText[codeArray[2]];
      break;
    default:
      break;
  }
  return area;
};
// 提交
const formRef = ref();
const btnLoading = ref(false);
const submitForm = (compete?: any) => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    if (compete && !dataForm.value.payImg) {
      ElMessage.warning("完善信息需先上传付款截图!");
      return;
    }
    btnLoading.value = true;
    let params = JSON.parse(JSON.stringify(dataForm.value));
    // params.gameName = gamesList.value.find((item: any) => item.id == params.gameId)?.title;
    // if (params.server) {
    //   params.serverId = params.server.split("-")[0];
    //   params.serverName = params.district.split("-")[1] + "-" + params.server.split("-")[1];
    // } else {
    //   params.serverId = params.district.split("-")[0];
    //   params.serverName = params.district.split("-")[1];
    // }
    // delete params.server;
    // delete params.district;

    // 简易回收
    // params.orderType = 2;
    delete params.checkCollect;
    params.state = dataForm.value.payImg && dataForm.value.state != "入库且已打款" ? "NOT_INBOUND_BUT_REMIT" : params.state;
    params.city = params.city.join(",");

    baseService
      .put("/purchase/updateSimpleOrder", params)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          emits("refresh");
          visible.value = false;
          params.amount = params.recyclePrice;
          compete ? emits("completeInfo", params) : "";
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 获取出售渠道
const ChannelTreeList = ref(<any>[]);
const getChannelTree = () => {
  baseService.get("/channel/channel/getChannelTree", { type: "1" }).then((res) => {
    res.data.map((item: any) => {
      if (item.children.length == 0) {
        item.disabled = true;
      }
    });
    ChannelTreeList.value = res.data;
  });
};
const selectedOptions = ref(<any>[]);
const handleChange = (selectedData: any) => {
  if (selectedData && selectedData.length > 0) {
    dataForm.value.channelId = selectedData[selectedData.length - 1];
    baseService.get("/channel/channel/" + dataForm.value.channelId).then((res) => {
      if (res.data) {
        dataForm.value.channelName = res.data.title;
      }
    });
  } else {
    dataForm.value.channelId = "";
    dataForm.value.channelName = "";
  }
};

// 查询出售渠道详细信息
const getChannelId = (id: any) => {
  baseService.get("/channel/channel/" + id).then((res) => {
    if (res.data) {
      selectedOptions.value = res.data.pid ? [res.data.classId, res.data.pid, res.data.id] : [res.data.classId, res.data.id];
    }
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
.ny-button-group {
  background: #f2f2f2;
  border-radius: 6px;
  margin-right: 10px;
  width: fit-content;
  margin: auto;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
:deep(.el-form-item) {
  &.is-error {
    .el-input__inner {
      &::placeholder {
        color: var(--el-color-danger);
      }
    }
    .el-select__placeholder {
      color: var(--el-color-danger);
    }
    .el-form-item__error {
      display: none !important;
      opacity: 0 !important;
    }
  }
  &.is-success {
    .el-form-item__error {
      transition: none !important;
      opacity: 0 !important;
    }
  }
}
</style>
