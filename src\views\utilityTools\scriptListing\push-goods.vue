<template>
  <div style="padding: 12px">
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px">
      <div class="flx-align-center">
        <el-button style="margin-right: 24px" :loading="replyLoading" @click="pushAllSelect" type="primary">推送所选</el-button>
        <el-radio-group v-model="state.pushForm.taskType">
          <el-radio v-for="item in state.typeList" :key="item.typeCode" :value="item.typeCode" :label="item.typeCode">{{ item.typeName }}</el-radio>
        </el-radio-group>
      </div>
      <div style="display: flex; align-items: center">
        <div style="display: flex;align-items: center;gap: 12px;margin-right: 12px;">
          <el-input v-model="state.dataForm.shopSearchParam" placeholder="请输入商品编码/商品标题/游戏账号" :prefix-icon="Search" style="width: 280px" clearable></el-input>
          <el-cascader style="width: 160px;" :show-all-levels="false" clearable v-model="state.dataForm.server" :options="sysgameList" :props="{ label: 'title', value: 'id' }" placeholder="请选择区服"/>
          <el-select clearable style="width: 160px;" v-model="state.dataForm.pushStatus" placeholder="请选择推送状态">
            <el-option label="推送成功" value="1" />
            <el-option label="推送失败" value="2" />
          </el-select>
        </div>
        <el-button type="primary" @click="queryFn">查询</el-button>
        <el-button @click="resetFn">重置</el-button>
      </div>
    </div>
    <div class="setUptable TableXScrollSty">
      <el-table border height="calc(100vh - 436px)" ref="tableRef" id="pushGoods-uuid" @select="onSelect" @select-all="onSelectAll" @sort-change="sortableChange" :data="state.dataList" v-loading="dataListLoading" style="width: 100%">
        <el-table-column show-overflow-tooltip v-for="(cItem, cindex) in columns" :key="cindex" :prop="cItem.prop" align="center" :sortable="cItem.sortable" :fixed="cItem.fixed" :type="cItem.type" :minWidth="cItem.minWidth" :label="cItem.label">
          <template #default="scope">
            <!-- 商品信息 -->
            <template v-if="cItem.prop == 'shopTitle'">
              <div class="shoping">
                <el-image style="height: 68px; width: 120px" :src="scope.row.log" :preview-src-list="[scope.row.log]" preview-teleported fit="cover" />
                <div class="info">
                  <div class="title mle" v-html="scope.row.shopTitle"></div>
                  <div class="sle" style="width: 185px; text-align: left">
                    {{ `${scope.row.gameName} / ${scope.row.areaName}` }}
                  </div>
                </div>
              </div>
            </template>
            <!-- 状态 -->
            <template v-if="cItem.prop == 'pushStatus'">
              <el-tag type="warning" v-if="scope.row.pushStatus == '0'">未推送</el-tag>
              <el-tag type="success" v-if="scope.row.pushStatus == '1'">推送成功</el-tag>
              <el-tag type="danger" v-if="scope.row.pushStatus == '2'">推送失败</el-tag>
              <el-tag type="primary" v-if="scope.row.pushStatus == '3'">推送中</el-tag>
            </template>
            <!-- 审核状态 -->
            <template v-if="cItem.prop == 'outsideStatusCodeName'">
              <el-tooltip
                effect="dark"
                :content="`更新时间：${timeFormat(scope.row.outsideStatueUpdateTime)}`"
                placement="top"
              >{{ scope.row.outsideStatusCodeName }}</el-tooltip>
            </template>
            <!-- 推送信息 -->
            <template v-if="cItem.prop == 'pushMessage'">
              {{ scope.row.pushMessage || "-" }}
            </template>
            <!-- 查看日志 -->
            <template v-if="cItem.prop == 'checkLog'">
              <el-button type="primary" link @click="handleOpenLog(scope.row)">查看</el-button>
            </template>
            <!-- 商品状态 -->
            <template v-if="cItem.prop == 'status'">
              <el-tag type="warning" v-if="+scope.row.status < 4">{{ shopStatus[+scope.row.status] }}</el-tag>
              <el-tag type="success" v-else-if="+scope.row.status == 4">{{ shopStatus[+scope.row.status] }}</el-tag>
              <el-tag type="danger" v-else>{{ shopStatus[+scope.row.status] }}</el-tag>
            </template>
            <template v-if="cItem.prop == 'createTime'">
              {{ timeFormat(scope.row.createTime) }}
            </template>
            <!-- <template v-if="cItem.prop == 'formStatus'">
              <template v-if="state.dataForm.partnerName == '交易猫'">
                <el-button link type="warning" v-if="scope.row.formStatus == 0" @click="openGoodsForm(scope.row)">未拉取</el-button>
                <el-button link type="success" v-if="scope.row.formStatus == 1" @click="openGoodsForm(scope.row)">已通过</el-button>
                <el-button link type="danger" v-if="scope.row.formStatus == 2" @click="openGoodsForm(scope.row)">未通过</el-button>
              </template>
              <template v-else>-</template>           
            </template> -->
            <template v-if="cItem.prop == 'scan'">
              <el-popover @after-leave="clearTimers" v-if="!scope.row.whetherScan && scope.row.whetherCanScan" placement="left" :width="165" trigger="click" v-model:visible="scope.row.visible" @hide="clearTimers" popper-style="overflow: hidden;">
                <template #reference>
                  <el-button @click="getScanCode({ ...scope.row }, scope.$index)" link type="primary">扫码</el-button>
                </template>
                <el-image v-loading="scope.row.loading" :src="scope.row.codeUrl" style="height: 140px; width: 140px" frameborder="0">
                  <template #error>
                    <div class="image-slot">
                      <el-icon><icon-picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </el-popover>
              <span v-else-if="scope.row.whetherScan && scope.row.whetherCanScan">已扫码</span>
              <el-button v-loading="scope.row.loading" link type="primary" v-if="scope.row.authorizationStatus && scope.row.authorizationStatus != 0" @click="getScanAuthCode({ ...scope.row }, scope.$index)">去授权</el-button>
              <span style="color: #909399" v-if="!scope.row.whetherCanScan && (scope.row.authorizationStatus == 0 || !scope.row.authorizationStatus)">暂无操作</span>
            </template>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components/noShop.png" alt="" />
          </div>
        </template>
      </el-table>
    </div>
    <div class="flx-justify-between NYpagination" :style="`width: ${tableWidth}px;`">
      <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
        <span style="font-weight: bold; color: #1d2129">零售价</span>
        <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
        <span>合计={{ getSummaries() }}</span>
      </div>
      <el-pagination
        :hide-on-single-page="false"
        background
        :page-sizes="[10, 20, 50, 100, 500, 1000]"
        layout="sizes,total, ->, prev, pager, next, jumper"
        :total="state.count"
        :current-page="queryParams.page"
        :default-page-size="queryParams.size"
        @size-change="TableSizeChangeFn"
        @current-change="TableCurrentChangeFn"
      />
    </div>
  </div>

  <!-- 商品表单 -->
  <goodsForm ref="goodsFormRef" @refreshDataList="getDataList"></goodsForm>

  <!-- 扫码成功选择角色 -->
  <el-dialog v-model="roleShow" title="选择角色" width="500">
    <el-select v-model="roleId" placeholder="请选择角色" @change="roleChange">
      <el-option v-for="item in roleCheckList" :key="item.scene" :label="item.areaName" :value="item.scene" />
    </el-select>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="roleShow = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="roleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 查看日志 -->
  <el-dialog v-model="dialogForm.visibleLog" :title="dialogForm.titleLog" width="1200" :footer="null" @close="handleCloseLog">
    <ny-table :state="stateLog" :showColSetting="false" :columns="columnsLog" @pageSizeChange="stateLog.pageSizeChangeHandle" @pageCurrentChange="stateLog.pageCurrentChangeHandle" @selectionChange="stateLog.dataListSelectionChangeHandle">
      <template #status="{ row }">
        <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
        <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
        <el-tag type="danger" v-if="row.status == '3'">已下架</el-tag>
        <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
        <el-tag type="warning" v-if="row.status == '5'">问题账号</el-tag>
        <el-tag type="danger" v-if="row.status == '6'">作废账号</el-tag>
        <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
      </template>
      <template #pushStatus="{ row }">
        <el-tag v-if="row.pushStatus == '未推送'" type="warning">未推送</el-tag>
        <el-tag v-if="row.pushStatus == '推送成功'" type="primary">推送成功</el-tag>
        <el-tag v-if="row.pushStatus == '推送失败'" type="danger">推送失败</el-tag>
      </template>
      <template #errorMessage="{ row }">
        <div style="display: flex" v-if="row.message">
          <div style="width: 240px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ row.message }}</div>
          <el-button @click="handleOpen('推送信息', row.message)" type="primary" link>查看</el-button>
        </div>
        <span v-else>-</span>
      </template>
      <template #requestParam="{ row }">
        <div style="display: flex" v-if="row.pushParam">
          <div class="sle">{{ row.pushParam }}</div>
          <el-button @click="handleOpen('请求参数', row.pushParam)" type="primary" link>查看</el-button>
        </div>
      </template>
      <template #responseBody="{ row }">
        <div style="display: flex" v-if="row.pushResponse">
          <div class="sle">{{ row.pushResponse }}</div>
          <el-button @click="handleOpen('返回参数', row.pushResponse)" type="primary" link>查看</el-button>
        </div>
      </template>
      <template #requestUrl="{ row }">
        <div style="display: flex" v-if="row.requestUrl">
          <div class="sle">{{ row.requestUrl }}</div>
          <el-button @click="handleOpen('请求地址', row.requestUrl)" type="primary" link>查看</el-button>
        </div>
      </template>
      <template #pushTime="{ row }">
        {{ formatTimeStamp(+row.pushTime) }}
      </template>
    </ny-table>
  </el-dialog>
  <!-- 查看推送详情 -->
  <el-dialog v-model="dialogForm.visible" :title="dialogForm.title" width="800" :footer="null" @close="handleClose">
    <div style="height: 422px; overflow-y: scroll">
      {{ dialogForm.content || "-" }}
    </div>
  </el-dialog>
  <!-- 账号授权扫码 -->
  <el-dialog v-model="dialogForm.authVisible" title="账号授权" width="360" :footer="null" @close="handleAuthClose">
    <div v-if="scanAuthForm.authType != 3 && !dialogForm.authloading" style="width: 100%; background: #f2f3f5; padding: 10px 24px; display: flex; border-radius: 7px; align-items: center">
      <img style="width: 30px; height: 30px; border-radius: 4px" :src="scanAuthForm.account.gameImage" />
      <div style="margin-left: 8px">
        <div>{{ scanAuthForm.account.gameName }}|{{ scanAuthForm.account.server }}</div>
        <div style="color: #86909c; font-size: 13px">{{ scanAuthForm.account.account }}</div>
      </div>
    </div>
    <div style="display: flex; align-items: center; justify-content: center; padding: 20px 0">
      <el-image v-loading="dialogForm.authloading" :src="dialogForm.antuCodeUrl" style="height: 240px; width: 240px" frameborder="0">
        <template #error>
          <div class="image-slot">
            <el-icon><icon-picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
    <div v-if="scanAuthForm.authType != 3" style="color: #86909c; font-size: 13px; text-align: center">请打开【QQ】扫码完成授权</div>
  </el-dialog>

  <!-- 小算授权 -->
  <XsObtain ref="XsObtainRef" @xsVerification="xsVerification" />
  <!-- 查看商品详情 -->
  <shopInfo ref="infoRef"></shopInfo>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted, onUnmounted, inject } from "vue";
import { Search, Timer } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import useView from "@/hooks/useView";
import { timeFormat } from "@/utils/utils";
import { useI18n } from "vue-i18n";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import goodsForm from "./goodsForm.vue";
import XsObtain from "./xs-obtain.vue";
import shopInfo from "@/views/shop/shop-info.vue";

const startPushStaskContent = inject("startPushStaskContent");
const XsObtainRef = ref();
const props = defineProps({
  partnerName: <any>""
});
// NOTE: 弹窗
const dialogForm = reactive({
  visible: false,
  title: "",
  content: "",
  visibleLog: false,
  titleLog: "查看日志",
  visibleError: false,
  titleError: "推送失败记录",
  authVisible: false,
  authloading: false,
  antuCodeUrl: ""
});
const { t } = useI18n();
const visible = ref(false);
const replyLoading = ref(false);
const shopStatus = ref(["", "待上架", "已上架", "已下架", "已出售", "问题账号", "作废账号"]);
const state = reactive({
  selectionList: [],
  dataList: [],
  count: 0,
  partnerGamesList: [],
  typeList: [],
  dataForm: {
    gameId: "", // 游戏ID
    order: "", // 排序字段
    partnerId: "", // 平台ID
    partnerName: "", // 平台名称
    shopSearchParam: "", //输入框
    pushStatus: "", //推送状态
    server: "", // 区服Id
  },
  pushForm: {
    taskType: undefined
  },
  sortForm: {
    column: null,
    isAsc: null
  }
});
watch(
  () => props.partnerName,
  () => {
    state.dataForm.partnerName = props.partnerName;
  },
  {
    immediate: true
  }
);
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  size: 10
});
// 表格设置
const columns = reactive([
  {
    type: "selection",
    minWidth: 50
  },
  {
    prop: "shopCode",
    label: "商品编号",
    minWidth: "120"
  },
  {
    prop: "shopTitle",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: "150"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "120",
    sortable: "custom"
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: "112"
  },
  {
    prop: "outsideStatusCodeName",
    label: "审核状态",
    minWidth: "112"
  },
  {
    prop: "checkLog",
    label: "查看日志",
    minWidth: "100"
  },
  {
    prop: "pushMessage",
    label: "推送信息",
    minWidth: "170"
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "152"
  },
  {
    prop: "createTime",
    label: "创建时间",
    minWidth: "170"
  },
  {
    prop: "scan",
    label: "操作",
    minWidth: "160",
    fixed: "right"
  }
]);
const columnsLog = reactive([
  {
    prop: "errorMessage",
    label: "推送信息",
    minWidth: 200,
    showOverflowTooltip: false
  },
  {
    prop: "requestParam",
    label: "请求参数",
    minWidth: 120
  },
  {
    prop: "responseBody",
    label: "返回参数",
    minWidth: 120
  },
  {
    prop: "pushStatus",
    label: "推送状态",
    minWidth: 100
  },
  {
    prop: "pushTime",
    label: "推送时间",
    minWidth: 140
  }
]);
const viewLog = reactive({
  createdIsNeed: false,
  getDataListURL: "/script/sysscriptpushtaskdetails/pushShopRecordList",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    partnerId: "",
    shopId: "",
    scriptUserId: ""
  }
});
const stateLog = reactive({ ...useView(viewLog), ...toRefs(viewLog) });
const handleOpen = (title: any, content: any) => {
  dialogForm.visible = true;
  dialogForm.title = title;
  dialogForm.content = content;
};
const handleClose = () => {
  dialogForm.visible = false;
  dialogForm.content = "";
};
const handleOpenLog = (content: any) => {
  dialogForm.visibleLog = true;
  dialogForm.titleLog = "查看日志";
  stateLog.dataForm.shopId = content.shopId;
  stateLog.dataForm.partnerId = state.dataForm.partnerId;
  stateLog.dataForm.scriptUserId = scriptUserId.value;
  stateLog.getDataList();
};
const handleCloseLog = () => {
  dialogForm.visibleLog = false;
  dialogForm.titleLog = "查看日志";
};

// 表单初始化
const scriptUserId = ref("");
// const gameName = ref("");
const init = async (id: any, partnerId: any, gameId: any) => {
  closeDialog();
  visible.value = true;
  scriptUserId.value = id;
  state.dataForm.partnerId = partnerId;
  state.dataForm.gameId = gameId;
  // let res1 = await baseService.get("/script/sysscriptpartnerinfo/selectGameList", { partnerId: state.dataForm.partnerId });
  // if (res1.code == 0) {
  //   if (res1.data.length > 0) {
  //     state.partnerGamesList = res1.data || [];
  //     let obj = res1.data.find((item: any) => item.gameId == gameId);
  //     gameName.value = obj ? obj.gameName : "";
  //   }
  // }
  state.typeList = [];
  state.pushForm.taskType = undefined;
  // 更新类型
  let res2 = await baseService.get("/script/sysscriptpushtask/getTaskType", { partnerId: state.dataForm.partnerId });
  if (res2.code == 0) {
    if (res2.data.length > 0) {
      state.typeList = res2.data;
      state.pushForm.taskType = res2.data[0].typeCode;
    }
  }
  getDataList();
  if(state.dataForm.gameId){
    getSysgame();
  }
  
};
// 推送
const pushAllSelect = () => {
  let form = {
    gameId: "",
    partnerId: "",
    shopIdList: [],
    scriptUserId: scriptUserId.value
  };
  form.gameId = state.dataForm.gameId;
  form.partnerId = state.dataForm.partnerId;
  form.shopIdList = state.selectionList.map((ele: any) => ele.shopId);
  replyLoading.value = true;
  baseService
    .post("/script/sysscriptpushtask/submitTask", { ...state.pushForm, ...form })
    .then((res) => {
      if (res.code == 0) {
        replyLoading.value = false;
        ElMessage.success("推送成功");
        getDataList();
        startPushStaskContent();
      }
    })
    .finally(() => {
      replyLoading.value = false;
    });
};
// 选择列表项
const onSelect = (arr: any) => {
  state.selectionList = arr;
};
const onSelectAll = (arr: any) => {
  state.selectionList = arr;
};
// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.size = val;
  queryParams.page = 1;
  getDataList();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getDataList();
};
// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  queryParams.page = 1;
  state.sortForm.isAsc = order === "ascending";
  state.sortForm.column = prop;
  getDataList();
};

// 查询
const queryFn = () => {
  refreshFn();
};
// 重置
const resetFn = () => {
  state.dataForm.shopSearchParam = "";
  state.dataForm.pushStatus = "";
  state.dataForm.server = "";
  refreshFn();
};
// 刷新页面
const refreshFn = () => {
  queryParams.page = 1;
  state.dataForm.server = Array.isArray(state.dataForm.server) ? state.dataForm.server[state.dataForm.server.length - 1] : state.dataForm.server;
  getDataList();
};
const closeDialog = () => {
  state.dataForm.partnerId = undefined;
  state.dataForm.gameId = undefined;
  state.dataForm.shopSearchParam = "";
  state.dataForm.pushStatus = "";
  state.selectionList = [];
  state.dataList = [];
  clearTimers();
  visible.value = false;
  replyLoading.value = false;
};
// 获取列表数据
const dataListLoading = ref(false);
const getDataList = () => {
  dataListLoading.value = true;
  baseService
    .post("/script/sysscriptpushtaskdetails/pushShopList", {
      ...state.dataForm,
      ...queryParams,
      ...state.sortForm,
      scriptUserId: scriptUserId.value
    })
    .then((res_) => {
      if (res_.code == 0) {
        state.dataList = res_.data.list.map((ele) => {
          if (ele.fieldFormatName == "多选") {
            ele.fixedValue = ele.fixedValue.split(",");
          }
          return ele;
        });

        state.count = res_.data.total;
      }
    }).finally(()=>{
      dataListLoading.value = false;
    })
};

// ===================== 扫码 ======================
const scanForm = reactive({
  index: "",
  shopId: "",
  outtimer: null,
  resulttime: null,
  success: false
});
const getScanCode = (row: any, index: any) => {
  scanForm.shopId = row.shopId;
  scanForm.success = false;
  hideCode();
  state.dataList[index].loading = true;
  if (scanForm.outtimer) clearInterval(scanForm.outtimer);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxQRCode", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      shopId: scanForm.shopId
    })
    .then((res) => {
      if (res.code == 0) {
        scanForm.index = index;
        state.dataList[scanForm.index].codeUrl = "data:image/png;base64," + res.data.url;
        state.dataList[index].loading = false;
        getScanresult();
        scanForm.outtimer = setInterval(() => {
          if (!scanForm.success) {
            // 1分钟更新二维码
            console.log("更新");
            getScanCode(row, scanForm.index);
          } else {
            clearInterval(scanForm.outtimer);
          }
        }, 60000);
      }
    });
};
const getScanresult = () => {
  if (scanForm.resulttime) clearInterval(scanForm.resulttime);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxQRCodeResult", {
      partnerId: state.dataForm.partnerId,
      shopId: scanForm.shopId
    })
    .then((res) => {
      if (res.code == 0) {
        scanForm.resulttime = setInterval(() => {
          if (res.data.status == 1) {
            // 继续扫码
            console.log("继续扫码");
            getScanresult();
          } else {
            // 扫码成功
            state.dataList[scanForm.index].codeUrl = null;
            state.dataList[scanForm.index].visible = false;
            getDataList();
            scanForm.success = false;
            ElMessage.success("扫码成功!");
            clearInterval(scanForm.resulttime);

            roleCheckList.value = res.data.roleList;
            // 有角色的情况下弹出角色选择框
            if (roleCheckList.value.length) {
              roleShow.value = true;
            }
          }
        }, 5000);
      }
    });
};
// ============================= 授权扫码 ====================
const scanAuthForm = reactive({
  index: "",
  shopId: "",
  qrsig: "",
  taskId: "",
  account: {},
  outtimer: null,
  resulttime: null,
  success: false,
  authType: null
});
const getScanAuthCode = (row: any, index: any) => {
  scanAuthForm.shopId = row.shopId;
  scanAuthForm.success = false;
  scanAuthForm.authType = row.authorizationStatus;
  if (row.authorizationStatus == 2) {
    // 任务授权
    getTaskAuth(row, index);
    return;
  }
  if (row.authorizationStatus == 3) {
    // 授权3
    getCodeTaskAuth(row, index);

    return;
  }
  // 二维码授权
  dialogForm.authVisible = true;
  dialogForm.authloading = true;
  if (scanAuthForm.outtimer) clearInterval(scanAuthForm.outtimer);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxAuthQrCode", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      shopId: scanAuthForm.shopId,
      scriptUserId: scriptUserId.value
    })
    .then((res) => {
      if (res.code == 0) {
        scanAuthForm.index = index;
        dialogForm.antuCodeUrl = "data:image/png;base64," + res.data.url;
        scanAuthForm.taskId = res.data.taskId;
        scanAuthForm.qrsig = res.data.qrsig;
        scanAuthForm.account = res.data.account;
        dialogForm.authloading = false;
        getAuthScanresult();
        scanAuthForm.outtimer = setInterval(() => {
          if (!scanAuthForm.success) {
            // 1分钟更新二维码
            console.log("更新");
            getScanAuthCode(row, scanAuthForm.index);
          } else {
            console.log("陈宫");

            clearInterval(scanAuthForm.outtimer);
          }
        }, 60000);
      }
    });
};
const getAuthScanresult = () => {
  if (scanAuthForm.resulttime) clearInterval(scanAuthForm.resulttime);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxAuthQrCodeResult", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      shopId: scanAuthForm.shopId,
      scriptUserId: scriptUserId.value,
      qrsig: scanAuthForm.qrsig,
      taskId: scanAuthForm.taskId
    })
    .then((res) => {
      if (res.code == 0) {
        scanAuthForm.resulttime = setInterval(() => {
          if (res.data.code == -2) {
            // 继续扫码
            console.log("继续扫码");
            getAuthScanresult();
          } else {
            // 扫码成功
            dialogForm.antuCodeUrl = null;
            dialogForm.authVisible = false;
            getDataList();
            scanAuthForm.taskId = "";
            scanAuthForm.qrsig = "";
            scanAuthForm.account = {};
            scanAuthForm.success = false;
            ElMessage.success("授权成功！");
            clearInterval(scanAuthForm.resulttime);
          }
        }, 5000);
      }
    });
};
const handleAuthClose = () => {
  dialogForm.antuCodeUrl = "";
  dialogForm.authVisible = false;
  scanAuthForm.success = false;
  scanAuthForm.index = "";
  scanAuthForm.shopId = "";
  scanAuthForm.qrsig = "";
  scanAuthForm.taskId = "";
  scanAuthForm.account = {};
  clearTimers();
};
// 小算授权=======================
const getTaskAuth = (row: any, index: any) => {
  baseService
    .post("/script/sysscriptpushtaskdetails/pxAuthTaskStart", {
      partnerId: state.dataForm.partnerId,
      shopId: scanAuthForm.shopId,
      scriptUserId: scriptUserId.value,
      gameId: state.dataForm.gameId
    })
    .then((res) => {
      if (res.code == 0) {
        nextTick(() => {
          XsObtainRef.value.submit({ ...res.data, tpartnerId: state.dataForm.partnerId, tshopId: scanAuthForm.shopId, tscriptUserId: scriptUserId.value, tgameId: state.dataForm.gameId });
        });
      }
    });
};

const xsVerification = (result: boolean) => {
  if (result) {
    baseService
      .post("/script/sysscriptpushtaskdetails/pxAuthTaskEnd", {
        partnerId: state.dataForm.partnerId,
        shopId: scanAuthForm.shopId,
        scriptUserId: scriptUserId.value,
        gameId: state.dataForm.gameId
      })
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("授权成功！");
          getDataList();
        }
      });
  }
};

// 授权3===========================
const getCodeTaskAuth = (row: any, index: any) => {
  scanAuthForm.shopId = row.shopId;
  scanAuthForm.success = false;
  // 二维码授权
  dialogForm.authVisible = true;
  dialogForm.authloading = true;
  if (scanAuthForm.outtimer) clearInterval(scanAuthForm.outtimer);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxAuthTaskQrCodeStart", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      shopId: scanAuthForm.shopId,
      scriptUserId: scriptUserId.value
    })
    .then((res) => {
      if (res.code == 0) {
        getAuthScanresult3();
      }
    });
};
const getAuthScanresult3 = () => {
  if (scanAuthForm.resulttime) clearInterval(scanAuthForm.resulttime);
  baseService
    .post("/script/sysscriptpushtaskdetails/pxAuthTaskQrCodeStatus", {
      gameId: state.dataForm.gameId,
      partnerId: state.dataForm.partnerId,
      shopId: scanAuthForm.shopId,
      scriptUserId: scriptUserId.value
    })
    .then((res) => {
      if (res.code == 0) {
        scanAuthForm.resulttime = setInterval(() => {
          if (res.data.code == -2) {
            // 继续扫码
            getAuthScanresult3();
          } else if (res.data.code == 0 && res.data.qrCode && res.data.qrCode.length > 0) {
            dialogForm.antuCodeUrl = res.data.qrCode;
            dialogForm.authloading = false;
            getAuthScanresult3();
          } else {
            // 扫码成功
            dialogForm.antuCodeUrl = null;
            dialogForm.authVisible = false;
            getDataList();
            scanAuthForm.taskId = "";
            scanAuthForm.qrsig = "";
            scanAuthForm.account = {};
            scanAuthForm.success = false;
            ElMessage.success("授权成功！");
            clearInterval(scanAuthForm.resulttime);
          }
        }, 5000);
      }
    });
};

// 选择角色
const roleShow = ref(false);
const roleCheckList = ref(<any>[]);
const roleId = ref();
const roleInfo = ref(<any>{});
// 选择回调
const roleChange = () => {
  roleInfo.value = roleCheckList.value.find((item: any) => item.scene == roleId.value);
  console.log(roleInfo.value);
};
// 确认角色
const submitLoading = ref(false);
const roleSubmit = () => {
  if (!roleId) {
    ElMessage.warning("请选择用户角色");
    return;
  }
  submitLoading.value = true;
  baseService
    .post("/script/sysscriptpushtaskdetails/submitQRCodeScanningRoleSelection", {
      shopId: scanForm.shopId,
      userId: roleInfo.value.userId,
      wt: roleInfo.value.wt,
      openId: roleInfo.value.openId,
      token: roleInfo.value.token,
      isAuth: roleInfo.value.isAuth,
      scene: roleInfo.value.scene
    })
    .then((res) => {
      if (res.code == 0) {
        getDataList();
        roleShow.value = false;
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

const hideCode = () => {
  state.dataList.forEach((ele) => {
    ele.codeUrl = null;
    ele.loading = false;
  });
};
const clearTimers = () => {
  if (scanForm.outtimer) clearInterval(scanForm.outtimer);
  if (scanForm.resulttime) clearInterval(scanForm.resulttime);
  if (scanAuthForm.outtimer) clearInterval(scanAuthForm.outtimer);
  if (scanAuthForm.resulttime) clearInterval(scanAuthForm.resulttime);
};

const goodsFormRef = ref();
// const openGoodsForm = async (row: any) => {
//   await nextTick();
//   const params = {
//     partnerId: state.dataForm.partnerId,
//     gameId: state.dataForm.gameId,
//     scriptUserId: scriptUserId.value,
//     refresh: false,
//     shopId: row.shopId
//   };
//   goodsFormRef.value.init(params, gameName.value);
// };

// 获取游戏区服
const sysgameList = ref(<any>[])
const getSysgame = () => {
  baseService.get("/game/sysgame/get/" + state.dataForm.gameId).then((res) => {
    sysgameList.value = res.data.areaDtoList;
  });
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.price) total += Number(item?.price) || 0;
  });
  return total.toFixed(2);
};

const tableWidth = ref(0);
onMounted(() => {
  tableWidth.value = document.getElementById("pushGoods-uuid").offsetWidth;
  window.addEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById("pushGoods-uuid").offsetWidth;
    });
  }); // 监听窗口大小变化
});
onUnmounted(() => {
  clearTimers();
  window.removeEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById("pushGoods-uuid").offsetWidth;
    });
  });
});
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.setUptable {
  width: 100%;
  margin-bottom: 16px;
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
  .shoping {
    display: flex;
    align-items: center;
    cursor: pointer;

    .el-image {
      border-radius: 4px;
      margin-right: 8px;
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .title {
        color: var(--el-color-primary);
        white-space: pre-wrap;
        text-align: left;
      }
    }
  }
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 506px;
  bottom: 100px;
}
</style>
<style lang="scss">
.pushGooddialog {
  .el-dialog__header {
    padding-bottom: 16px;
  }
  .el-dialog__title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
  }
  .el-dialog__body {
    padding-top: 0;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
