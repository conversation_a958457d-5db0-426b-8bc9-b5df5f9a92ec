<template>
    <div>
        <div class="menu_title">财务指标追踪</div>
        <div class="top">
            <ny-button-group :list="[
                { dictLabel: '财务概况', dictValue: '0' },
                { dictLabel: '结算明细', dictValue: '1' },
                { dictLabel: '库存资产', dictValue: '2' },
            ]" v-model="typeValue" @change=""></ny-button-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId" :list="recyclingChanneList" labelKey="channelName"
                    valueKey="channelId"
                    placeholder="回收渠道" clearable></NyDropdownMenu> -->
                <NyDropdownMenu v-model="dataForm.employeeId" :list="employeeList" labelKey="employeeName"
                    valueKey="employeeId"
                    placeholder="选择员工" clearable></NyDropdownMenu>
                <el-date-picker v-model="indexTime" type="daterange" start-placeholder="开始时间"
                    end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px;margin-left: 12px;" 
                    v-if="typeValue == '0' || typeValue == '1'"
                    @change = "indexTimeChange"
                />
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <!-- 财务概况 -->
        <FinancialOverview ref="FinancialOverviewRef" v-if="typeValue == '0'"></FinancialOverview>
        <!-- 结算明细 -->
        <SettlementDetails ref="SettlementDetailsRef" v-if="typeValue == '1'"></SettlementDetails>
        <!-- 库存资产 -->
        <InventoryAssets ref="InventoryAssetsRef" v-if="typeValue == '2'"></InventoryAssets>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import FinancialOverview from "./menuPageList/FinancialOverview.vue";
import SettlementDetails from "./menuPageList/SettlementDetails.vue";
import InventoryAssets from "./menuPageList/InventoryAssets.vue";
import baseService from "@/service/baseService";

const typeValue = ref("0");
const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    employeeId: "",
    startTime: "",
    endTime: ""
});

const indexTime = ref();

const gameList = ref(); // 游戏列表
const recyclingChanneList = ref(); // 回收渠道列表
const employeeList = ref(); // 选择员工列表

const FinancialOverviewRef = ref();
const SettlementDetailsRef = ref();
const InventoryAssetsRef = ref();

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) => {
    baseService.get("/dataAnalysis/channelSearchList", { channelType }).then((res) => {
        recyclingChanneList.value = res.data;
    });
};

// 员工列表
const getEmployeeList = () => {
    baseService.get("/dataAnalysis/employeeSearchList").then((res) => {
        employeeList.value = res.data;
    });
};

// 选择时间
const indexTimeChange = () =>{
    dataForm.value.startTime = indexTime.value ? indexTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = indexTime.value ? indexTime.value[1] + " 23:59:59" : "";
}

// 查询
const queryChagne = () =>{
    if(FinancialOverviewRef.value){
        FinancialOverviewRef.value.init(dataForm.value);
    }
    if(SettlementDetailsRef.value){
        SettlementDetailsRef.value.init(dataForm.value);
    }
    if(InventoryAssetsRef.value){
        InventoryAssetsRef.value.init(dataForm.value);
    }
}

// 重置
const resettingChange = () =>{
    dataForm.value.gameId = "";
    dataForm.value.employeeId = "";
    dataForm.value.recyclingChannelId ="";
    dataForm.value.startTime = "";
    dataForm.value.endTime = "";
    indexTime.value = [];
    if(FinancialOverviewRef.value){
        FinancialOverviewRef.value.init(dataForm.value);
    }
    if(SettlementDetailsRef.value){
        SettlementDetailsRef.value.init(dataForm.value);
    }
    if(InventoryAssetsRef.value){
        InventoryAssetsRef.value.init(dataForm.value);
    }
}

onMounted(() => {
    getGameList();
    getRecyclingChanneList(1);
    getEmployeeList();
});
</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}


</style>
