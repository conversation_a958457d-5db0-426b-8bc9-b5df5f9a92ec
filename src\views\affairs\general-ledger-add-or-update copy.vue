<template>
  <el-dialog v-model="visible" :title="dataForm.id ? '编辑应收总账' : '新增应收总账'" :close-on-click-modal="false" :close-on-press-escape="false" width="800px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="dataForm.customerName" placeholder="请输入客户名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账户类型" prop="accountType">
            <ny-select v-model="dataForm.accountType" dict-type="account_type" placeholder="请选择账户类型" style="width: 100%"></ny-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="单据编号" prop="orderNumber">
            <el-input v-model="dataForm.orderNumber" placeholder="请输入单据编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="openBank">
            <el-input v-model="dataForm.openBank" placeholder="请输入开户银行"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账户用途" prop="accountPurpose">
            <el-input v-model="dataForm.accountPurpose" placeholder="请输入账户用途"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人员" prop="accountHolder">
            <el-input v-model="dataForm.accountHolder" placeholder="请输入创建人员"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="应收金额" prop="receivableAmount">
            <el-input-number
              v-model="dataForm.receivableAmount"
              :precision="2"
              :min="0"
              placeholder="请输入应收金额"
              style="width: 100%">
              <template #append>元</template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到期日期" prop="dueDate">
            <el-date-picker
              v-model="dataForm.dueDate"
              type="date"
              placeholder="请选择到期日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <ny-select v-model="dataForm.status" dict-type="receivable_status" placeholder="请选择状态" style="width: 100%"></ny-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="逾期标记" prop="isOverdue">
            <el-switch
              v-model="dataForm.isOverdue"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="dataForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  customerName: "",
  accountType: "",
  orderNumber: "",
  openBank: "",
  accountPurpose: "",
  accountHolder: "",
  receivableAmount: 0,
  receivedAmount: 0,
  remainingAmount: 0,
  dueDate: "",
  status: "0",
  isOverdue: 0,
  remark: ""
});

const rules = ref({
  customerName: [{ required: true, message: "客户名称不能为空", trigger: "blur" }],
  accountType: [{ required: true, message: "账户类型不能为空", trigger: "change" }],
  orderNumber: [{ required: true, message: "单据编号不能为空", trigger: "blur" }],
  receivableAmount: [{ required: true, message: "应收金额不能为空", trigger: "blur" }],
  dueDate: [{ required: true, message: "到期日期不能为空", trigger: "change" }],
  status: [{ required: true, message: "状态不能为空", trigger: "change" }]
});

const init = (id?: number) => {
  visible.value = true;

  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    customerName: "",
    accountType: "",
    orderNumber: "",
    openBank: "",
    accountPurpose: "",
    accountHolder: "",
    receivableAmount: 0,
    receivedAmount: 0,
    remainingAmount: 0,
    dueDate: "",
    status: "0",
    isOverdue: 0,
    remark: ""
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get(`/finance/receivable/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    // 计算剩余金额
    dataForm.remainingAmount = dataForm.receivableAmount - dataForm.receivedAmount;

    const request = !dataForm.id ?
      baseService.post("/finance/receivable", dataForm) :
      baseService.put("/finance/receivable", dataForm);

    request.then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
  width: 100%;
}
</style>
