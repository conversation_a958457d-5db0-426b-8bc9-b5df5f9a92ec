<template>
    <el-drawer v-model="visible"  size="50%" class="drawer_shop">
        <template #header>
            <div class="drawer_title">商品信息审核详情</div>
        </template>
        <el-scrollbar v-loading="requestLoading">
            <div class="shopPaudit_page">
                <div class="shopPaudit_card">
                    <ny-title title="基本信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                        <el-descriptions-item>
                            <template #label>商品编码</template>
                            {{ dataForm.code }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>游戏名称</template>
                            {{ dataForm.gameName }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>商品标题</template>
                            {{ dataForm.title }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>零售价</template>
                            <el-text type="danger">{{ dataForm.price }}</el-text>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>商品描述</template>
                            <span style="white-space: pre-wrap;">{{ dataForm.info }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>游戏主图</template>
                            <el-image 
                                style="height: 100px" 
                                :src="dataForm.log" 
                                :preview-src-list="[dataForm.log]" 
                                preview-teleported
                                v-if="dataForm.log"
                            />
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>详情图片</template>
                            <el-image 
                                style="height: 100px" v-for="(item,index) in dataForm.images" 
                                :src="item" 
                                :preview-src-list="dataForm.images" 
                                :initial-index="index" 
                                preview-teleported
                            />
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>商品亮点</template>
                            {{ dataForm.highlights }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card" style="margin: 12px 0px;">
                    <ny-title title="用户信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>用户ID</template>
                            {{ dataForm.creator }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>用户名</template>
                            {{ dataForm.nickname }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系手机</template>
                            {{ dataForm.phone }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系微信</template>
                            {{ dataForm.wx }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card" style="margin: 12px 0px;">
                    <ny-title title="自动降价" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>自动降价周期(天)</template>
                            {{ dataForm.priceReductionCycle || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>自动降价比例(%)</template>
                            {{ dataForm.priceReductionPercentage || '-'  }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>自动降价底价(元)</template>
                            {{ dataForm.minimumPrice || '-'  }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card">
                    <ny-title title="商品审核" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>审核结果</template>
                            <span v-if="dataForm.status == '0'">待审核</span>
                                <span v-if="dataForm.status == '1'">审核通过</span>
                                <span v-if="dataForm.status == '2'">审核拒绝</span>
                                <span v-if="dataForm.status == '3'">已完善</span>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>审核时间</template>
                            <span>{{ formatTimeStamp(dataForm.auditTime) }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item v-if="dataForm.status == '2'">
                            <template #label>拒绝原因</template>
                            {{ dataForm.remark }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>审核人</template>
                            {{ dataForm.realName }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
        </el-scrollbar>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue'
const visible = ref(false);  // 对话框显隐
import baseService from "@/service/baseService";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";

const dataForm = reactive({  // 表单变量
    id: null,
    code: '',
    gameId: '',
    gameName: '',
    gameCode: '',
    highlights: '',
    title: '',
    price: 0,
    acquisitionPrice: 0,
    phone: '',
    gameAccount: '',
    gamePassword:'',
    log: [],
    images: [],
    info:'',
    gameAre: '',
    server: '',
    serverName: '',
    compensation: '',
    bargain: '',
    topped: '',
    creator: '',
    nickname: '',
    wx: '',
    status: '',
    auditTime: 0,
    auditUserId: '',
    remark: '',
    realName: '',
});


const requestLoading = ref(false); // 详情加载

// 表单初始化
const init = (id: any) => {
    visible.value = true;
    dataForm.id = id;
    getInfo();
};

const getInfo = () =>{
    requestLoading.value = true
    baseService.get("/shop/shopaudit/" + dataForm.id).then((res) => {
        if(res.data.shopDTO){
            res.data.shopDTO.images = res.data.shopDTO.images ? JSON.parse(res.data.shopDTO.images) : []
            Object.assign(dataForm,res.data.shopDTO);
            
            dataForm.nickname = res.data.shopAuditDTO.nickname;
            dataForm.wx = res.data.shopAuditDTO.wx;
            dataForm.status = res.data.shopAuditDTO.status;
            dataForm.auditTime = res.data.shopAuditDTO.auditTime;
            dataForm.remark = res.data.shopAuditDTO.remark;
            dataForm.realName = res.data.shopAuditDTO.realName;
        }else{
            res.data.shopAuditDTO.images = res.data.shopAuditDTO.images ? JSON.parse(res.data.shopAuditDTO.images) : []
            Object.assign(dataForm,res.data.shopAuditDTO);
        }
        
    }).finally(()=>{
        requestLoading.value = false
    })
}


defineExpose({
    init
});
</script>

<style lang='less' scoped>
.shopPaudit_page{
    background-color: #F0F2F5;
    padding: 12px;
    .shopPaudit_card{
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        padding: 12px;
    }
}
.proportion{
    :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content){
        width: 220px;
    }
}
</style>
<style lang='less'>
.drawer_shop{
    .el-drawer__header{
        margin-bottom: 0px;
    }
    .drawer_title{
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .el-drawer__body{
        padding: 0px;
    }
}
</style>