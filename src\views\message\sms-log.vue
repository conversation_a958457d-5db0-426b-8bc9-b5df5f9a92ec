<template>
    <div class="mod-demo__syssmslog">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:smslog:all')" type="danger" @click="state.deleteHandle()">{{
                        $t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.smsCode" :placeholder="$t('sms.smsCode')" clearable></el-input>
                    <el-input v-model="state.dataForm.mobile" :placeholder="$t('sms.mobile')" clearable></el-input>
                    <el-select v-model="state.dataForm.status" :placeholder="$t('sms.status')" style="width: 200px;" clearable>
                        <el-option :label="$t('sms.status1')" :value="1"></el-option>
                        <el-option :label="$t('sms.status0')" :value="0"></el-option>
                    </el-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                
                <!-- 状态 -->
                <template #status="{row}">
                    <el-tag v-if="row.status === 1" size="small">{{ $t("sms.status1") }}</el-tag>
                    <el-tag v-else size="small" type="danger">{{ $t("sms.status0") }}</el-tag>
                </template>

                <!-- 操作 -->
                <template #operation="{row}">
                    <el-button v-if="state.hasPermission('sys:smslog:all')" type="danger" text bg
                            @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, toRefs } from "vue";

const view = reactive({
    getDataListURL: "/sys/smslog/page",
    getDataListIsPage: true,
    deleteURL: "/sys/smslog",
    deleteIsBatch: true,
    dataForm: {
        mobile: "",
        status: "",
        smsCode: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "smsCode",
        label: "短信编码",
        minWidth: 120
    },
    {
        prop: "mobile",
        label: "手机号",
        minWidth: 120
    },
    {
        prop: "params1",
        label: "参数1",
        minWidth: 100
    },
    {
        prop: "params2",
        label: "参数2",
        minWidth: 100
    },
    {
        prop: "params3",
        label: "参数3",
        minWidth: 100
    },
    {
        prop: "status",
        label: "状态",
        minWidth: 100
    },
    {
        prop: "createDate",
        label: "发送时间",
        minWidth: 150,
        sortabel: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 100
    }
])
// 重置操作
const getResetting = () => {
    view.dataForm.mobile = "";
    view.dataForm.status = "";
    view.dataForm.smsCode = "";
    state.getDataList();
}
</script>
