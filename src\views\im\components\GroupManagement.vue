<template>
    <div class="group-management-box">
        <div class="mask" @click="close"></div>
        <div class="group-management" @click.stop="" v-if="groupInfo.sessionData">
            <div class="title flx-justify-between">
                <span>群聊管理</span>
                <el-icon class="close" @click="close"><Close /></el-icon>
            </div>
            
            <div class="group-info flx">
                <group-avatar :info="groupInfo.sessionData"></group-avatar>
                <div class="info">
                    <div class="name sle">{{ groupInfo.sessionData.groupName }}</div>
                    <!-- 群类型 1交易群，2中介群3普通群 -->
                    <el-tag type="primary" size="small">{{ groupInfo.sessionData.type == '1' ? '交易群' : groupInfo.sessionData.type == '2' ? '中介群' : '普通群 '  }}</el-tag>
                </div>
            </div>

            <div class="group-member">
                <div class="member-total flx-justify-between">
                    <span class="text">群成员{{ groupInfo.sessionData.userVos.length }}人</span>

                    <!-- 只用平台角色才可以拉人 -->
                    <img 
                        v-if="imStore.imRole == 1"
                        class="add" 
                        src="../../../assets/images/im_add_member.png" 
                        @click="addMember"
                    >
                </div>
                <div class="member-list">
                    <div class="member-item flx-justify-between" v-for="(item, index) in groupInfo.sessionData.userVos" :key="index">
                        <div class="left flx-center">
                            <im-avatar class="avatar" :user="item"></im-avatar>
                            <span class="name">
                                <span v-if="item.imRole != 1 && imStore.currentConversation.sessionData.type == 4">
                                    <!-- // type 4回收群  1交易群 -->
                                    {{ imStore.orderBuyerId == item.id ? '买家-' : '卖家-' }}
                                </span>
                                <span v-if="item.imRole != 1 && imStore.currentConversation.sessionData.type == 1">
                                    <!-- // type 4回收群  1交易群 -->
                                    {{ imStore.orderBuyerId != item.id ? '买家-' : '卖家-' }}
                                </span>
                                {{ item.nickname }}
                                <el-tag v-if="item.imRole!=0" :type="item.imRole == 1 ? 'primary' : 'warning'" size="small">{{ item.imRole == 1 ? '客服' : '认证商户' }}</el-tag>
                            </span>
                            <el-tag type="warning" v-if="item.imUid == groupOwner">群主</el-tag>
                        </div>
                        <el-icon 
                            v-if="item.imUid != imStore.imUid" 
                            @click="initiatedCOnversationHandle(item)">
                            <ChatDotRound />
                        </el-icon>
                    </div>
                </div>
            </div>

            <div class="group-setting">
                <!-- 只有群主可以禁言 -->
                <div class="setting-item" v-if="groupOwner == imStore.imUid">
                    <div class="label">全员禁言</div>
                    <div class="value">
                        <el-switch v-model="groupInfo.gag" @change="groupBanChange"></el-switch>
                    </div>
                </div>
                <div class="setting-item">
                    <div class="label">群置顶</div>
                    <div class="value">
                        <el-switch v-model="groupInfo.isTop" @change="groupIsTopChange"></el-switch>
                    </div>
                </div>
                <div class="setting-item">
                    <div class="label">消息免打扰</div>
                    <div class="value">
                        <el-switch v-model="groupInfo.notificationLevel" @change="groupNotificationLevelChange"></el-switch>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加成员 -->
        <select-member title="添加成员" ref="createGroupChatsRef" :key="createGroupChatsKey"></select-member>
        
    </div>
</template>

<script lang="ts" setup>
    import { ref, defineEmits, defineExpose, nextTick } from 'vue';
    import { setConversationTop, setConversationDisturb, getConversation, getGroupInfo } from '@/utils/imTool';
    import { ChatDotRound, Close } from '@element-plus/icons-vue';
    import { useImStore } from '@/store/im';
    import { useAppStore } from '@/store';
    import { ElMessage } from 'element-plus';

    import SelectMember from './SelectMember.vue';
    import GroupAvatar from './GroupAvatar.vue';
    import ImAvatar from './ImAvatar.vue';
    import baseService from '@/service/baseService';

    const imStore = useImStore();
    const userStore = useAppStore();
    const emit = defineEmits(['close']);

    const switchValue = ref(false);
    const groupInfo = ref(<any>{
        groupType: '',
        groupMember: ''
    });

    // 群主
    const groupOwner = ref('');

    const init = async (info: any) => {
        groupInfo.value = {
            ...info,
            ...groupInfo.value,
            // 是否禁言
            gag: info.sessionData.stat ? true : false,
            notificationLevel: info.notificationLevel == 5 ? true : false
        }

        let res = await getGroupInfo(info.targetId)
        groupOwner.value = res.data[0].ownerId;
    }

    // 选择成员
    const createGroupChatsRef = ref();
    const createGroupChatsKey = ref(0);
    const addMember = async () => {
        createGroupChatsKey.value++;
        await nextTick();
        createGroupChatsRef.value.init('add');
    }


    // 修改群置顶
    const groupIsTopChange = () => {
        setConversationTop(groupInfo.value, groupInfo.value.isTop).then(() => {
            imStore.setUpdateConversationListKey();
            ElMessage.success('操作成功！');
        })
    }

    // 修改群 免打扰状态
    const groupNotificationLevelChange = () => {
        setConversationDisturb(groupInfo.value.targetId, groupInfo.value.notificationLevel ? 5 : 0).then(() => {
            imStore.setUpdateConversationListKey();
            ElMessage.success('操作成功！');
        })
    }

    // 群禁言 0 表示为未禁言、1 表示为禁言。
    const groupBanChange = () => {
        baseService.get(`/im/login/group/prohibition?groupId=${groupInfo.value.targetId}&status=${groupInfo.value.gag ? 1 : 0}`).then(res => {
            if(res.code == 0){
                ElMessage.success('操作成功！');
                imStore.currentConversation.sessionData.stat = groupInfo.value.gag ? 1 : 0;
            }
        })
    }

    
    // 发起会话
    const initiatedCOnversationHandle = (user: any) => {
        if(!user.imUid) return;
        getConversation(user.imUid).then((res: any) => {
            res.sessionData = user;
            imStore.currentConversation = res;
            imStore.showContactInfo = false;
            imStore.currentConversationType = '';
            close();
            
            // startPrivateConversation(imStore.imUid, data.imUid);
        })
    }


    const close = () => {
        emit('close');
    }

    defineExpose({
        init
    })
    
</script>

<style lang="scss" scoped>
    .group-management-box{
        position: absolute;
        // right: 348px;
        right: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        overflow: hidden
    }
    .mask{
        position: absolute;
        right: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
    .group-management{
        position: absolute;
        right: 0;
        top: 0;
        width: 376px;
        height: 100%;
        background: #FFFFFF;
        box-shadow: -4px 0px 8px 0px rgba(0,0,0,0.15);
        animation: setFromRight 0.3s ease-in-out;

        @keyframes setFromRight {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(0);
            }
        }

        .title{
            height: 56px;
            padding: 0 16px;
            box-shadow: inset 0px -1px 0px 0px #F0F0F0;
            span{
                font-size: 16px;
                color: rgba(0,0,0,0.85);
                font-weight: bold;
            }

            .close{
                cursor: pointer;
                font-size: 16px;
            }
        }

        .group-info{
            padding: 16px 16px 12px 16px;
            border-bottom: 1px solid rgba(99,106,116,0.15);

            .group-img{
                width: 42px;
                height: 42px;
            }

            .info{
                margin-left: 8px;
                
                .name{
                    font-size: 16px;
                    font-weight: bold;
                    color: rgba(0,0,0,0.8);
                    line-height: 24px;
                }
            }
        }

        .group-member{
            padding: 12px 16px 16px 16px;
            
            .member-total{
                height: 22px;

                .text{
                    font-weight: 500;
                    color: #171A1D;
                }

                .add{
                    cursor: pointer;
                }
            }

            .member-item{
                padding-top: 10px;

                .left{
                    cursor: pointer;
                }

                .avatar{
                    width: 28px;
                    height: 28px;
                    border-radius: 4px;
                }

                .name{
                    padding-left: 8px;
                }

                .el-tag{
                    height: 14px;
                    line-height: 14px;
                    padding: 0 4px;
                    margin-left: 4px;
                }

                .el-icon{
                    font-size: 20px;
                    color: #909399;
                    font-weight: bold;
                    cursor: pointer;
                }
            }
        }

        .group-setting{
            border-top: 8px solid #F2F3F5;
            padding: 0 16px;

            .setting-item{
                height: 56px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #171A1D;
                border-bottom: 1px solid rgba(99,106,116,0.15);
            }
        }
    }
</style>