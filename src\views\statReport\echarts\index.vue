<template>
    <el-dialog v-model="visible"  title="选择统计图类型" width="814">
        <div class="content">
            <div class="title">选择一个组件，开始搭建你的数据看板</div>
            <div class="introduce">快速插入柱状图、折线图、饼图组件类型，直观展示数据，让业务动态一目了然</div>
            <div class="list">
                <div class="items" @click="chartChange('option1')">
                    <img src="/src/assets/images/stat1.png" alt="">
                    <span class="name">柱状图</span>
                    <div class="but">立即创建<el-icon><Right /></el-icon></div>
                </div>
                <div class="items" @click="chartChange('option3')">
                    <img src="/src/assets/images/stat3.png" alt="">
                    <span class="name">折线图</span>
                    <div class="but">立即创建<el-icon><Right /></el-icon></div>
                </div>
                <div class="items" @click="chartChange('option5')">
                    <img src="/src/assets/images/stat5.png" alt="">
                    <span class="name">饼图</span>
                    <div class="but">立即创建<el-icon><Right /></el-icon></div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
const emit = defineEmits(["refresh"]);
const visible = ref(false);

const init = (id?: number) => {
  visible.value = true;
  
};

// 图表选择
const chartChange = (value:any) =>{
    visible.value = false;
    emit('refresh',value);
}


defineExpose({
  init
});

</script>

<style lang='less' scoped>
.content{
    padding-top: 32px;
    .title{
        font-weight: 500;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: center;
    }
    .introduce{
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 26px;
        text-align: center;
    }
    .list{
        display: flex;
        justify-content: center;
        margin-top: 24px;
        margin-bottom: 120px;
        .items{
            width: 143px;
            height: 167px;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #E4E7ED;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            transition: all 0.3s;
            cursor: pointer;
            &:hover {
                transform: translateY(-8px);
                box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);

                .name{
                    display: none;
                }
                .but{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s;
                }
            }
            img{
                width: 72px;
                height: 72px;
                margin-bottom: 10px;
            }
            .name{
                font-weight: 400;
                font-size: 14px;
                color: #606266;
                line-height: 26px;
                display: block;
            }
            .but{
                width: 92px;
                height: 24px;
                background: rgba(65,101,215,0.1);
                border-radius: 300px 300px 300px 300px;
                border: 1px solid #4165D7;
                font-weight: 500;
                font-size: 12px;
                color: #4165D7;
                line-height: 20px;
                display: none;
                .el-icon{
                    margin-left: 10px;
                }
            }
            
        }
        .items + .items{
            margin-left: 12px;
        }
    }
}
</style>