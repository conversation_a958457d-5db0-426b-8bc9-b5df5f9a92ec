<template>
  <div>
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
    <ny-table :showColSetting="false" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <el-button type="primary" :loading="syncLoading" @click="syncHandle">自动同步</el-button>
        <el-button v-if="state.hasPermission('game:sysgame:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </template>

      <!-- 当前平台 -->
      <template #currentPlatformName> {{ settingStore.info.seoPlatformName }} </template>

      <!-- 合作商区服名称 -->
      <template #platformName="{ row, $index }">
        <el-select :remote-method="(query: any)=>{remoteMethod(query, row)}" @change="partnerAttributeNameFn(row)" v-model="row.platformName" clearable remote reserve-keyword filterable>
          <el-option v-for="(item, index) in externalAttribute" :key="index" :label="item.platformName" :value="item.platformName" />
        </el-select>
      </template>
    </ny-table>
    <!-- </el-card> -->
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, defineExpose } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { useSettingStore } from "@/store/setting";

const settingStore = useSettingStore();

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },

  {
    prop: "outName",
    label: "合作商区服名称",
    minWidth: 120
  },
  {
    prop: "partnerName",
    label: "合作商名称",
    minWidth: 120
  },
  {
    prop: "partnerGameName",
    label: "合作商游戏名称",
    minWidth: 120
  },
  {
    prop: "outPname",
    label: "合作商上级区服名称",
    minWidth: 120
  },
  {
    prop: "currentPlatformName",
    label: "当前平台",
    minWidth: 100
  },
  {
    prop: "gameName",
    label: "当前平台游戏名称",
    minWidth: 120
  },
  {
    prop: "similar",
    label: "预览",
    minWidth: 120
  },
  {
    prop: "platformName",
    label: "当前平台区服名称",
    minWidth: 120
  }
]);

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/mapping/sysgameinfomapping/page",
  getDataListIsPage: true,
  deleteURL: "/mapping/sysgameinfomapping",
  deleteIsBatch: true,
  dataForm: {
    type: 3,
    partnerId: "",
    gameId: "",
    attribute: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const getList = (params: IObject) => {
  state.dataForm = {
    ...state.dataForm,
    ...params
  };
  state.getDataList();
};

// 自动同步
const syncLoading = ref(false);
const syncHandle = () => {
  syncLoading.value = true;
  baseService
    .post("/mapping/sysgameinfomapping/sync", {
      type: 3,
      partnerId: state.dataForm.partnerId,
      gameId: state.dataForm.gameId
    })
    .then((res) => {
      ElMessage.success("同步成功！");
      getList({});
    })
    .finally(() => {
      syncLoading.value = false;
    });
};

// 区服列表
const externalAttribute = ref(<any>[]); // 平台属性
// 搜索关联平台属性
const remoteMethod = (query: string, row: any) => {
  externalAttribute.value = [];
  if (query) {
    baseService
      .post("/mapping/sysgameinfomapping/getPlatformInfo", {
        mappingId: row.id,
        searchParam: query == null ? "" : query
      })
      .then((res) => {
        externalAttribute.value = res.data;
      });
  }
};

// 选择关联平台属性
const partnerAttributeNameFn = (row: any) => {
  let data = row;
  let itemdata: any = externalAttribute.value.find((item: any) => item.platformName == row.platformName);
  data.platformId = itemdata ? itemdata.platformId : "";
  baseService
    .put("/mapping/sysgameinfomapping", {
      id: row.id,
      platformId: data.platformId
    })
    .then((res) => {
      ElMessage.success("提交成功");
      state.query();
    });
};
const changeDataForm = (partners: any, gameCode?: any) => {
  state.dataForm.partnerId = partners;
  state.dataForm.gameId = gameCode;
  getList({});
};

defineExpose({
  getList,
  changeDataForm
});
</script>