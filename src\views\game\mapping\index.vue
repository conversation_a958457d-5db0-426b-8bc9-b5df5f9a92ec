<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <ny-flod-tab class="newTabSty" :list="gamesList" v-model="tabActiveId" value="value" label="label"> </ny-flod-tab>
    <div class="mainBox">
      <div class="leftPart">
        <div class="title" v-if="tabActiveId != 4">选择合作商</div>
        <div class="scrollWrap" v-if="tabActiveId == 1">
          <ul class="menuUl">
            <li :class="'menuLi ' + (dataForm.partners == item.id ? 'active' : '')" v-for="(item, itemIndex) in state.curAllPartnerList" :key="itemIndex" @click="handleselectPartner(item.id)" :index="itemIndex">
              <span>{{ item.companyName }}</span>
            </li>
          </ul>
        </div>
        <template v-else>
          <el-select filterable v-if="tabActiveId != 4" v-model="dataForm.partners" @change="handleselectPartner" clearable style="width: 186px; margin-bottom: 10px">
            <el-option v-for="item in state.curAllPartnerList" :key="item.id" :label="item.companyName" :value="item.id" />
          </el-select>
          <div class="title">选择游戏</div>
          <div class="scrollWrap lower">
            <ul class="menuUl">
              <li :class="'menuLi ' + (dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in state.allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
                <el-tooltip effect="dark" :content="item.gameName" placement="top-start">
                  <span>{{ item.gameName }}</span>
                </el-tooltip>
              </li>
            </ul>
          </div>
        </template>
      </div>
      <div class="rightPart">
        <!-- 游戏 -->
        <!-- <mapping-game v-if="tabActiveId == 1" ref="mappingRef"></mapping-game> -->
        <!-- 区服 -->
        <!-- <mapping-district-server v-if="tabActiveId == 2" ref="mappingRef"></mapping-district-server> -->
        <!-- 筛选属性映射 -->
        <mapping-attribute v-if="tabActiveId == 3" ref="mappingRef" :gamesList="state.allSelectArr"></mapping-attribute>
        <!-- 官方属性 -->
        <mapping-official v-if="tabActiveId == 4" ref="mappingRef"></mapping-official>
      </div>
    </div>
  </el-card>
</template>
  
  <script lang="ts" setup>
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";

import MappingGame from "./mapping-game.vue";
import MappingDistrictServer from "./mapping-district-server.vue";
import MappingAttribute from "./mapping-attribute.vue";
import MappingOfficial from "./mapping-official.vue";
import baseService from "@/service/baseService";

const tabActiveId = ref(3);
const gamesList = ref([
  // {
  //   value: 1,
  //   label: "游戏映射"
  // },
  // {
  //   value: 2,
  //   label: "区服映射"
  // },
  {
    value: 3,
    label: "筛选属性映射"
  },
  {
    value: 4,
    label: "官方属性映射"
  }
]);
const state = reactive({
  curAllPartnerList: [],
  allSelectArr: []
});
const dataForm = ref({
  // 合作商
  partners: "",
  // 游戏
  gameId: "",
  // 属性
  attribute: ""
});

const mappingRef = ref();

const handleselectGame = (id: any) => {
  dataForm.value.gameId = id;
  mappingRef.value.changeDataForm(dataForm.value.partners, dataForm.value.gameId);
};
// 切换平台获取游戏+更新table数据
const handleselectPartner = (id: any) => {
  dataForm.value.partners = id || undefined;
  dataForm.value.gameId = undefined;
  getGameList(id);
};
const getGameList = (partnersid: any) => {
  baseService
    .get(tabActiveId.value == 4 ? "/mapping/sysgameinfomapping/gameListUnofficial" : "/partner/partner/gameListUnofficial", { partnerId: partnersid || undefined })
    .then((res_) => {
      if (res_.code == 0) {
        state.allSelectArr = res_.data;
        if (res_.data.length > -1) {
          dataForm.value.gameId = res_.data[0].gameId;
          // 获取列表
        }
      }
    })
    .finally(() => {
      mappingRef.value.changeDataForm(dataForm.value.partners, dataForm.value.gameId);
    });
};
// 获取合作商数据
const getSelectInfo = () => {
  baseService.get(tabActiveId.value == 4 ? "/script/sysscriptpartnerinfo/allList" : "/partner/partner/partnerList").then((res) => {
    if (res.code == 0) {
      state.curAllPartnerList = res.data || [];
      if (state.curAllPartnerList.length > 0) {
        dataForm.value.partners = state.curAllPartnerList[0].id;
        getGameList(dataForm.value.partners);
      }
    }
  });
};
getSelectInfo();

watch(
  () => tabActiveId.value,
  () => {
    if (tabActiveId.value == 4) {
      getGameList(null);
    } else {
      getGameList(dataForm.value.partners);
    }
  }
);
onMounted(() => {});
onUnmounted(() => {});
</script>
  <style lang="scss" scoped>
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rr-view-ctx-card {
  // padding: 12px 24px;
  min-height: calc(100vh - 154px);

  :deep(.el-card__body) {
    // padding: 0;
  }

  .cards {
    display: flex;
    margin-bottom: 16px;

    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }

      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .mainBox {
    display: flex;

    .leftPart {
      width: 186px;
      margin-right: 12px;

      .title {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 22px;
        margin-bottom: 2px;
      }

      display: flex;
      flex-direction: column;

      .scrollWrap {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        height: calc(100vh - 260px);
        overflow: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .menuUl {
          .menuLi {
            cursor: pointer;
            padding: 20px;
            word-break: keep-all;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Inter, Inter;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            &.active {
              background-color: var(--color-primary-light);
              color: var(--color-primary);
            }
          }
        }

        &.lower {
          height: calc(100vh - 330px);
        }
      }
    }

    .rightPart {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
  