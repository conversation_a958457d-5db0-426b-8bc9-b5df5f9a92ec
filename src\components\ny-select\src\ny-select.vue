<template>
  <el-select v-model="value" @change="$emit('update:modelValue', $event);selectChange($event)" :placeholder="placeholder" clearable @clear="value='',$emit('update:modelValue', '')">
    <el-option :label="data.dictLabel" v-for="data in dataList" :key="data.dictValue" :value="data.dictValue">{{ data.dictLabel }}</el-option>
  </el-select>
</template>
<script lang="ts">
import { computed, defineComponent } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
export default defineComponent({ 
  name: "NySelect",
  props: {
    modelValue: [Number, String],
    dictType: String,
    placeholder: String,
    filterValue: String
  },
  emits: ['update:id', 'change'],
  setup(props, { emit }) {
    const store = useAppStore();
    const dataList = getDictDataList(store.state.dicts, props.dictType).filter(item => item.dictValue != props.filterValue);

    const selectChange = (e: any) => {
      let id = dataList.find(item => item.dictValue === e)?.id
      emit('update:id', id);
      emit('change', id);
    }

    return {
      value: computed(() => `${props.modelValue || ""}`),
      dataList,
      selectChange
    };
  },
  
});
</script>
