<template>
  <el-dialog v-model="visible" title="商品推送" :close-on-click-modal="false" :close-on-press-escape="false" width="700">
    <el-form label-width="100" label-suffix="：">
      <el-form-item label="目标合作商">
        <el-select v-model="dataForm.selectTargetPartnerId" placeholder="请选择目标合作商">
          <el-option v-for="item in partnerList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择游戏">
        <el-select v-model="dataForm.gameCode" placeholder="请选择游戏">
          <el-option v-for="item in gameList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%" @selection-change="handleSortChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="companyType" align="center" label="平台类型">
        <template #default="{ row }">
          {{ getDictLabel(store.state.dicts, "api_platform_type", row.companyType) }}
        </template>
      </el-table-column>
      <el-table-column prop="companyName" align="center" label="商户名"></el-table-column>
      <el-table-column prop="shopCount" align="center" label="商品数量"></el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" :disabled="!selected.length" type="primary" @click="handleSubmit">确定推送</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits } from "vue";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
import { getDictLabel } from "@/utils/utils";
import { useAppStore } from "@/store";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);
const store = useAppStore();
const visible = ref(false);

const dataForm = ref({
  // 目标合作商
  selectTargetPartnerId: "",
  // 游戏编码
  gameCode: ""
});

// 合作商列表
const partnerList = ref<IObject[]>([]);

// 游戏列表
const gameList = ref<IObject[]>([]);

// 表格数据
const tableData = ref<IObject[]>([]);

const init = () => {
  visible.value = true;
};

const selected = ref([]);
const handleSortChange = (val: any) => {
  selected.value = val;
};

// 推送
const btnLoading = ref(false);
const handleSubmit = async () => {
  btnLoading.value = true;
  try {
    const res = await baseService.post("/api/partnergame/push", dataForm.value);
    btnLoading.value = false;
    if (res.code === 200) {
      visible.value = false;
      ElMessage.success("推送成功");
      emits("refresh");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    btnLoading.value = false;
  }
};

defineExpose({
  init
});
</script>
