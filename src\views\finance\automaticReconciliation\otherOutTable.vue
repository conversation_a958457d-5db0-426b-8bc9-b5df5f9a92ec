<template>
  <div class="container sell-order-wrap TableXScrollSty">
    <ny-table
      :showColSetting="false"
      noDataType="1"
      cellHeight="ch-96"
      class="nyTableSearchFormFitable"
      :state="state"
      :columns="columns"
      @pageSizeChange="state.pageSizeChangeHandle"
      @pageCurrentChange="state.pageCurrentChangeHandle"
      @selectionChange="state.dataListSelectionChangeHandle"
      @sortableChange="sortableChange"
    >
      <template #header> <el-button type="primary" @click="exportHandle">导出</el-button> </template>

      <template #payType="{ row }">
        {{ ['支付宝', '微信', '银行卡'][row.payType - 1] }}
      </template>

      <!-- 关联商品 -->
      <template #shopCode="{ row }">
        <el-text type="primary" text class="pointer" @click="jumpGoodsDetails(row)">{{ row.shopCode }}</el-text>
      </template>

      <!-- 实收金额 -->
      <template #paidAmount="{ row }">
        <el-text type="danger" text v-if="+row.paidAmount == 0">{{ row.paidAmount }}</el-text>
        <span v-else>{{ row.paidAmount }}</span>
      </template>

      <!-- 状态 -->
      <template #reconciliationStatus="{ row }">
        <el-tag v-if="row.reconciliationStatus == '1'" type="success">对账成功</el-tag>
        <el-tag v-if="row.reconciliationStatus == '2'" type="danger">异常订单</el-tag>
      </template>

      <!-- 收款信息 -->
      <template #infoDetails="{ row }">
        <el-text type="primary" text class="pointer" @click="checkDetail(row)">查看</el-text>
      </template>

      <template #operation="{ row }">
        <el-button link type="primary" v-if="row.reconciliationStatus == '2'" @click="handleError(row)">处理</el-button>
        <span v-else>-</span>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort">
            <NyDropdownMenu
              :isBorder="false"
              v-model="SummariesParams"
              :list="[
                { label: '实付金额', value: 1 },
                { label: '支出金额', value: 2 }
              ]"
              labelKey="label"
              valueKey="value"
              isTop
            ></NyDropdownMenu>
          </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
    <!-- 收款信息 -->
    <detail ref="detailRef" :key="detailKey" />
    <!-- 处理异常 -->
    <errorOrder ref="errorOrderRef" :key="errorOrderKey" @refresh="state.getDataList()" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import ShopInfo from "@/views/shop/shop-info.vue";
import errorOrder from "./errorOrder.vue";
import detail from '../otherIandE/detail.vue'
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "feeName",
    label: "费用名称",
    minWidth: 160
  },
  {
    prop: "payableAmount",
    label: "支出金额(元)",
    minWidth: 160
  },
  {
    prop: "paidAmount",
    label: "实付金额(元)",
    minWidth: 160
  },
  {
    prop: "payType",
    label: "支付方式",
    minWidth: 160
  },
  {
    prop: "submitter",
    label: "提交人",
    minWidth: 160
  },
  {
    prop: "createDate",
    label: "对账时间",
    minWidth: 160
  },
  {
    prop: "shopCode",
    label: "关联商品",
    width: 160
  },
  {
    prop: "infoDetails",
    label: "收款信息",
    minWidth: 160
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    width: 160
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/autoreconciliation/page",
  getDataListIsPage: true,
  exportURL: "/automaticReconciliation/autoreconciliation/export",
  dataForm: {
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const props = defineProps({
  pParams: {}
});
watch(
  () => props.pParams?.billType || props.pParams?.type,
  () => {
    state.dataForm = Object.assign(state.dataForm, props.pParams);
  },
  {
    immediate: true,
    deep: true
  }
);
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleUpDateData = (dataForm:any) => {
  state.dataForm = Object.assign(state.dataForm, dataForm);
  state.getDataList();
};
// 异常处理
const errorOrderRef = ref();
const errorOrderKey = ref(0);
const handleError = (row: any) => {
  errorOrderKey.value++;
  nextTick(() => {
    errorOrderRef.value.init({ ...row },props.pParams.billType);
  });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/autoreconciliation/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `其他支出`);
  });
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};

// 收款信息
const detailRef = ref();
const detailKey = ref(0);
const checkDetail = (row: any) => {
  detailKey.value++;
  nextTick(() => {
    detailRef.value.init({ ...row });
  });
};
// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item.payableAmount) if (item.payableAmount) total = total + (item.payableAmount || 0);
    });
  } else if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item.paidAmount) if (item.paidAmount) total = total + (item.paidAmount || 0);
    });
  }
  return total.toFixed(2);
};
defineExpose({
  handleUpDateData
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
