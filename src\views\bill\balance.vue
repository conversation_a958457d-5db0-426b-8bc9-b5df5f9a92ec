<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <div class="wallet_card">
      <div class="above flx-align-center">
        <el-icon size="15px" color="#4165D7"><WalletFilled /></el-icon>
        <span class="flx-1">账户余额</span>
        <div>
          <!-- <el-button>提现</el-button> -->
          <el-button
            type="primary"
            @click="
              recharge = true;
              dataForm.orderNum = '';
            "
            >充值</el-button
          >
        </div>
      </div>
      <div class="middle flx-align-center">
        <div class="middle_item">
          <span class="middle_item_label">当前余额(元)</span>
          <span class="middle_item_value">{{ formatCurrency(balance) }}</span>
        </div>
        <!-- <div class="middle_item">
                    <span class="middle_item_label">可提现金额(元)</span>
                    <span class="middle_item_value">{{ formatCurrency(300) }}</span>
                </div> -->
      </div>
      <!-- <div class="below flx-align-center" @click="manage = true">
                <span class="below_label">账户管理</span>
                <el-icon size="15px" color="#4165D7"><Postcard /></el-icon>
            </div> -->
    </div>
    <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
            <el-tab-pane label="提现明细" name="提现明细"></el-tab-pane>
        </el-tabs>
        <ny-table 
            :columns="columns"
        >
            <template #header>
                <el-button v-if="state.hasPermission('shop:shop:export')" type="primary" @click="state.exportHandle()">{{ $t("export") }}</el-button>
            </template>
            <template #header-right>
                <el-date-picker
                    v-model="publicTime"
                    type="daterange"
                    range-separator="到"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    unlink-panels
                    style="width: 220px;"
                />
            </template>
        </ny-table> -->
  </el-card>
  <!-- 账户管理 -->
  <el-drawer v-model="manage" size="40%" class="manage">
    <template #header>
      <div class="manage_title">账户管理</div>
    </template>
    <div>
      <el-button type="primary" @click="addFn">添加</el-button>
      <el-table :data="tableData" border style="width: 100%; margin-top: 20px">
        <el-table-column prop="name" label="账户名称" align="center" min-width="90">
          <template #default="scope">
            <el-input v-model="scope.row.name" placeholder="请输入账户名称" v-if="scope.row.isEdit" />
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="支付宝账号" align="center" min-width="100">
          <template #default="scope">
            <el-input v-model="scope.row.account" placeholder="请输入支付宝账号" v-if="scope.row.isEdit" />
            <span v-else>{{ scope.row.account }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="160">
          <template #default="scope">
            <el-input v-model="scope.row.remark" placeholder="请输入备注" v-if="scope.row.isEdit" />
            <span v-else>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="80">
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="row.isEdit = !row.isEdit"> {{ row.isEdit ? "保存" : "编辑" }}</el-button>
            <el-button type="danger" link @click="deleteFn($index)">删除</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
    </div>
  </el-drawer>
  <!-- 充值 -->
  <el-drawer v-model="recharge" size="40%" class="manage">
    <template #header>
      <div class="manage_title">充值</div>
    </template>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" label-width="120px">
      <el-form-item label="支付宝账号">
        <div class="recharge_value"><EMAIL></div>
      </el-form-item>
      <el-form-item label="支付宝账户主体">
        <div class="recharge_value">枣庄努运企业管理有限公司</div>
      </el-form-item>
      <el-form-item label="支付宝订单号" prop="orderNum">
        <template #label>
          <span style="margin-right: 10px">支付宝订单号</span>
          <el-text type="primary" @click="dataForm.showImagePreview = true">如何查看订单号？</el-text>
        </template>
        <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号" style="width: 60%"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="recharge = false">取消</el-button>
        <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
      </div>
    </template>
    <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="closePreview" />
  </el-drawer>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { useAppStore } from "@/store";
const store = useAppStore();
const activeName = ref("提现明细");
const publicTime = ref("");
const balance = ref(0); // 账户余额
const manage = ref(false); // 账户管理
const recharge = ref(false); // 充值
const tableData = ref([
  {
    name: "刘能",
    account: "***********",
    remark: "备注备注，不备了",
    isEdit: false
  },
  {
    name: "赵四",
    account: "***********",
    remark: "备注备注，不备了",
    isEdit: false
  },
  {
    name: "谢广坤",
    account: "***********",
    remark: "备注备注，不备了",
    isEdit: false
  }
]);

const dataForm = reactive({
  // 表单变量
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNum",
    label: "订单编号",
    minWidth: "200"
  },
  {
    prop: "targetType",
    label: "定制商城",
    minWidth: "200"
  },
  {
    prop: "xf",
    label: "消费金额",
    minWidth: "200"
  },
  {
    prop: "userName",
    label: "支付人",
    minWidth: "120"
  },
  {
    prop: "createTime",
    label: "消费时间",
    minWidth: "200"
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "400"
  }
]);

const view = reactive({
  getDataListURL: "",
  getDataListIsPage: true,
  exportURL: "",
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 账户管理 - 新增
const addFn = () => {
  tableData.value.push({
    name: "",
    account: "",
    remark: "",
    isEdit: true
  });
};
// 账户管理 - 删除
const deleteFn = (index: number) => {
  tableData.value.splice(index, 1);
};

// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: dataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        recharge.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};
const closePreview = () => {
  dataForm.showImagePreview = false;
};

// 金额格式化
const formatCurrency = (number: number) => {
  if (isNaN(number) || number === null) return "";
  const numStr = number.toString();
  const decimalIndex = numStr.indexOf(".");
  const integerNum = decimalIndex >= 0 ? numStr.substring(0, decimalIndex) : numStr;
  const integerStr = integerNum.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return decimalIndex >= 0 ? integerStr + numStr.substring(decimalIndex) : integerStr;
};

onMounted(() => {
  baseService.get("/wallet/bill/balance").then((res) => {
    balance.value = res.data;
  });
});
</script>

<style lang="less" scoped>
.wallet_card {
  width: 484px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ebeef5;
  .above {
    padding: 12px;
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .middle {
    padding: 4px 16px 12px 16px;
    .middle_item {
      width: 225px;
      height: 43px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .middle_item_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .middle_item_value {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .below {
    cursor: pointer;
    padding: 4px 16px 12px 16px;
    .below_label {
      font-weight: 400;
      font-size: 12px;
      color: #4165d7;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-right: 5px;
    }
  }
}

.manage_title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.recharge_value {
  font-weight: 400;
  font-size: 13px;
  color: #303133;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 8px;
}
</style>
<style lang="less">
.manage {
  .el-drawer__header {
    margin-bottom: 0px;
  }
}
</style>
