<template>
    <el-dialog width="480px" v-model="dialogVisible" class="im-custom-dialog message-recipient-dialog" @close="close" :modal="false">
        <template #header="{ close, titleId, titleClass }">
            <div class="im-custom-dialog-header">
                <p class="dialog-title">消息接收人列表</p>
            </div>
        </template>
        
        <div class="message-recipient-dialog-content flx">
            <div class="list-item">
                <div class="text">{{ readPerson.length }}人已读</div>
                <div class="member-item" v-for="(item, index) in readPerson" :key="index">
                    <el-image class="avatar" :src="item.headUrl"></el-image>
                    <span class="nickname">{{ item.nickname }}</span>
                </div>
            </div>
            
            <div class="list-item">
                <div class="text">{{ unreadPerson.length }}人未读</div>
                <div class="member-item" v-for="(item, index) in unreadPerson" :key="index">
                    <el-image class="avatar" :src="item.headUrl"></el-image>
                    <span class="nickname">{{ item.nickname }}</span>
                </div>
            </div>
        </div>
        
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose } from 'vue';
    import { useImStore } from '@/store/im';

    const imStore = useImStore();
    
    const dialogVisible = ref(false);

    // 已读人员
    const readPerson = ref(<any>[]);
    // 全部人员
    const allPersonnel = ref([]);
    // 未读人员
    const unreadPerson = ref(<any>[]);

    const init = (readerList: any, userVos: any) => {
        dialogVisible.value = true;

        readPerson.value = userVos.filter((item: any) => readerList.includes(item.imUid));

        unreadPerson.value = userVos.filter((item: any) => !readerList.includes(item.imUid) && item.imUid != imStore.imUid);
    }

    const close = () => {
        
    }

    defineExpose({
        init
    })
</script>

<style lang="scss">

    .message-recipient-dialog-content{
        height: 272px;
        padding-bottom: 16px;
        
        .list-item{
            flex: 1;
            margin: 0 16px;

            .text{
                font-size: 16px;
                color: rgba(0,0,0,0.85);
                line-height: 24px;
            }

            .member-item{
                display: flex;
                align-items: center;
                padding-top: 16px;
                
                .avatar{
                    width: 24px;
                    height: 24px;
                    border-radius: 4px;
                }

                .nickname{
                    padding-left: 8px;
                }
            }
        }
    }

</style>