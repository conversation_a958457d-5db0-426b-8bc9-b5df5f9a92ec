<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        :list="[
          { label: '排查结果', value: 1 },
          { label: '执行记录', value: 2 }
        ]"
        value="value"
        label="label"
        v-model="currentTab"
      ></ny-flod-tab>
      <template v-if="currentTab == 1">
        <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
          <template #header>
            <ny-button-group :list="groupList" v-model="view.dataForm.type" @change="handleStatusClick"></ny-button-group>
          </template>
          <template #header-right>
            <div>
              <el-input v-model="state.dataForm.keywords" placeholder="请输入游戏标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input>
              <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px; margin-left: 12px" />
            </div>
            <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
            <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
          </template>
          <template #header-custom>
            <el-button style="margin-bottom: 12px" type="primary" @click="setHandle()">规则配置</el-button>
            <el-button style="margin-bottom: 12px" type="warning" @click="setAsyncHandle()">数据同步</el-button>
          </template>
          <template #selectTime="{ row }">
            <span>{{ formatTimeStamp(row.selectTime) }}</span>
          </template>
          <template #type="{ row }">
            <el-tag type="warning" v-if="row.type == '-1'">查询失败</el-tag>
            <el-tag type="primary" v-if="row.type == '0'">正常</el-tag>
            <el-tag type="danger" v-if="row.type == '1'">被找回</el-tag>
            <el-tag type="warning" v-if="row.type == '2'">被转手</el-tag>
            <el-tag type="info" v-if="row.type == '3'">掉绑</el-tag>
            <el-tag color="#F5E8FF" style="color: #8d4eda; border: 1px solid #8d4eda" v-if="row.type == '4'">账号错误</el-tag>
          </template>
          <template #video="{ row }">
            <el-button @click="showVideo(row)" link type="primary">查看录像</el-button>
          </template>
          <template #img="{ row }">
            <el-image v-if="row.img" :src="row.img" :preview-teleported="true" :preview-src-list="[row.img]" />
          </template>
          <template #shopTitle="{ row }">
            <el-link class="shopTitleSty" type="primary" :underline="false" @click="toDetails(row)">{{ row.shopTitle }}</el-link>
          </template>
          <template #operation="{ row }">
            <template v-if="row.type == '1'">
              <el-button v-if="true" type="primary" text bg @click="toExamine(row, false)">处理</el-button>
              <span v-else>售后处理中</span>
            </template>
            <el-button type="primary" text bg v-else @click="toExamine(row, true)">详情</el-button>
          </template>
        </ny-table></template
      >
      <record v-else />
    </el-card>
    <el-dialog title="录像" width="72%" append-to-body v-model="dialogVisible" @close="closeVideo">
      <video width="100%" :autoplay="false" :src="playvideo" controls id="video"></video>
    </el-dialog>
    <!-- 详情 -->
    <info ref="infoRef"></info>
    <!-- 设置 -->
    <setModal ref="setRef" @refreshDataList="state.getDataList"></setModal>
    <!-- 商品审核 -->
    <shopMarkSales ref="shopMarkSalesRef" @refreshDataList="state.getDataList"></shopMarkSales>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import Info from "./shop-info.vue";
import setModal from "./setModal.vue";
import shopMarkSales from "./shop-markSales.vue";
import record from "./record.vue";
const infoRef = ref(); // 查看商品详情
const setRef = ref(); // 查找回设置
const shopMarkSalesRef = ref(); // 处理
const timeInterval = ref(); // 时间区间
const view = reactive({
  getDataListURL: "/shop/sysaccountfound/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    orderField: "create_date", // 排序字段
    order: "desc", // 排序方式
    type: undefined, // 状态
    keywords: undefined, // 搜索
    startTime: undefined, // 开始时间
    endTime: undefined // 结束时间
  }
});
const currentTab = ref(1);
const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格设置
const columns = reactive([
  {
    prop: "shopCode",
    label: "问题商品编码",
    minWidth: "120"
  },
  {
    prop: "shopTitle",
    label: "问题商品名称",
    minWidth: "400"
  },
  {
    prop: "account",
    label: "游戏账号",
    minWidth: "140"
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: "120"
  },
  {
    prop: "selectTime",
    label: "查询时间",
    minWidth: "200"
  },
  {
    prop: "type",
    label: "账号状态",
    minWidth: "120"
  },
  {
    prop: "officialPhoneNumber",
    label: "官方手机号",
    minWidth: "120"
  },
  // {
  //   prop: "video",
  //   label: "录像",
  //   minWidth: "140"
  // },
  // {
  //   prop: "img",
  //   label: "截图",
  //   minWidth: "140"
  // },
  {
    prop: "operation",
    label: "处理",
    minWidth: "100"
  }
]);
// 状态
const groupList = ref([
  { dictLabel: "全部", dictValue: undefined },
  { dictLabel: "被找回", dictValue: "1" },
  { dictLabel: "被转手", dictValue: "2" },
  { dictLabel: "掉绑", dictValue: "3" }
]);
// 切换状态
const handleStatusClick = (tab: any) => {
  view.dataForm.status = tab;
  state.getDataList();
};
// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  if (order == "ascending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "asc";
  }
  if (order == "descending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "desc";
  }
  if (order == null) {
    view.dataForm.orderField = "";
    view.dataForm.order = "";
  }
  state.getDataList();
};
// 查询
const queryFn = () => {
  view.dataForm.startTime = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.endTime = timeInterval.value ? timeInterval.value[1] : "";
  state.getDataList();
};
// 重置
const resetFn = () => {
  view.dataForm.keywords = "";
  view.dataForm.startTime = "";
  view.dataForm.endTime = "";
  timeInterval.value = "";
  state.getDataList();
};
//  ---------- 录像
const dialogVisible = ref(false);
const playvideo = ref("");
const showVideo = (row: any) => {
  playvideo.value = row.video;
  dialogVisible.value = true;
};
const closeVideo = () => {
  dialogVisible.value = false;
  playvideo.value = "";
};

// ----------商品处理
const toExamine = (row: any, isdetail?: boolean) => {
  nextTick(() => {
    shopMarkSalesRef.value.init(row, isdetail);
  });
};
// ----------查找回设置
const setHandle = () => {
  nextTick(() => {
    setRef.value.init();
  });
};
// --------数据同步
const setAsyncHandle = () => {
  baseService.post("/shop/sysaccountfound/synchronousShop").then((res) => {
    ElMessage.success("数据同步成功！");
    state.getDataList();
  });
};
// -------------点击标题查看详情
const toDetails = (row: any) => {
  nextTick(() => {
    infoRef.value.init(row);
  });
};
onMounted(() => {});
</script>

<style lang="less" scoped>
:deep(.shopTitleSty) {
  .el-link__inner {
    width: inherit;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    display: block;
    padding-right: 20px;
  }
  width: inherit;
}
.el-tag {
  border: 1px solid;
  &.noBorder {
    border: 1px dotted;
  }
}
</style>
