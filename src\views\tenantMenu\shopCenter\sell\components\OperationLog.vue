<template>
  <el-drawer v-model="visible" :title="markerAfterSale ? '订单标记售后' : '操作日志'" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%" class="ny-drawer article-add-or-update">
    <el-card class="article-add-or-update-form" v-loading="dataLoading">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions class="descriptions-label-140" :column="1" border>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏大区">{{ resData.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item v-if="!showLog" label="买方手机号">{{ resData.tbUserPhone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="账号价格">
          <el-text type="danger" v-if="typeof resData.saleAmount == 'number'">￥{{ resData.saleAmount }}</el-text>
          <el-text type="danger" v-else>-</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="包赔费">￥{{ resData.fee || "-" }}</el-descriptions-item>
        <template v-if="!showLog">
          <el-descriptions-item label="创建时间">{{ formatTimeStamp(resData.createDate) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ formatTimeStamp(resData.payTime) || "-" }}</el-descriptions-item>
          <!-- <el-descriptions-item label="回收成功时间">{{ resData.dealDate || "-" }}</el-descriptions-item> -->
          <el-descriptions-item label="回收人">{{ resData.acquisitionName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="卖方手机号">{{ resData.saleUserPhone || "-" }}</el-descriptions-item>
          <el-descriptions-item label="游戏密码">
            <div class="flx-justify-between" v-if="resData.gamePassword">
              <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </el-card>

    <el-card class="mt-12" v-if="!markerAfterSale && showLog && state.hasPermission('purchase:purchaseorder:logPage')">
      <div class="p-title mt-0">操作日志</div>
      <ny-table :state="state" :columns="columns" :pagination="false" :showColSetting="false"> </ny-table>
    </el-card>

    <el-card class="mt-12" v-if="markerAfterSale">
      <div class="p-title mt-0">标记售后</div>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="售后类型" prop="saleAfterType">
              <ny-select v-model="dataForm.saleAfterType" dict-type="after_sales_type" placeholder="请选择售后类型"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他原因备注" prop="saleAfterRemark" v-if="dataForm.saleAfterType == 'OTHER'">
              <el-input v-model="dataForm.saleAfterRemark" placeholder="请输入其他原因备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请选择处理人" prop="userId">
              <ny-select-search v-model="dataForm.userId" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择处理人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问题截图" prop="saleAfterPics">
              <ny-upload v-model:imageUrl="dataForm.saleAfterPics" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template #footer v-if="markerAfterSale">
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-drawer>
</template>  

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { formatTimeStamp } from "@/utils/method";

const emit = defineEmits(["refresh"]);

const view = reactive({
  getDataListURL: "/sale/optionalLog/",
  createdIsNeed: false,
  getDataListIsPage: true,
  dataForm: {
    orderId: "",
    type: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    prop: "optionalUserName",
    label: "操作人"
  },
  {
    prop: "optionalTitle",
    label: "操作行为"
  },
  {
    prop: "createDate",
    label: "操作时间"
  }
]);

const visible = ref(false);

// 是否订单标记售后
const markerAfterSale = ref(false);

// 是否显示日志
const showLog = ref(false);

const orderId = ref("");
const init = (data: any, marker?: boolean, type?: string) => {
  visible.value = true;
  showLog.value = type == "log" ? true : false;
  markerAfterSale.value = marker ? true : false;
  orderId.value = data.id;
  view.getDataListURL = view.getDataListURL + data.id;
  view.dataForm.orderId = data.id;

  dataForm.value.saleOrderId = data.id;

  resData.value = data;

  // getDetails();
  state.getDataList();
};

const resData = ref(<any>{
  orderInfo: {},
  changeInfo: {}
});
const dataLoading = ref(false);
const getDetails = () => {
  dataLoading.value = true;
  baseService
    .get("/sale/orderLog/" + orderId.value)
    .then((res) => {
      if (res.code === 0 && res.data) {
        resData.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 是否显示游戏密码
const isShowGamePassword = ref(false);

// 标记售后
const dataForm = ref(<any>{
  userId: "",
  saleAfterPics: ""
});

const rules = reactive({
  saleAfterType: [{ required: true, message: "请选择售后类型", trigger: "change" }],
  userId: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
  saleAfterPics: [{ required: true, message: "请上传问题截图", trigger: "change" }]
});

const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let data = JSON.parse(JSON.stringify(dataForm.value));
    data.saleAfterPics = data.saleAfterPics.split(",");
    baseService
      .post("/saleAfter/createSaleAfterOrder", data)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          emit("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>