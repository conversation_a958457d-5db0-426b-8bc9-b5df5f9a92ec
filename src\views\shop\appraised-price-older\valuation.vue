<template>
  <div class="flx">
    <div class="flx-1">
      <div class="p-title">估价网站</div>
      <div class="flx-align-center pb-8">
        <el-input :value="link" disabled class="copy-input-width"></el-input>
        <el-button type="primary" v-copy="link">复制</el-button>
        <el-button type="primary" plain @click="jump">跳转</el-button>
      </div>
      <div class="p-title">估价报告</div>
      <div class="flx-align-center pb-8">
        <el-input :value="link2" disabled class="copy-input-width"></el-input>
        <el-button type="primary" v-copy="link2">复制</el-button>
        <el-button type="primary" plain @click="jump2">跳转</el-button>
      </div>

      <div class="p-title">填写回收信息（粘贴信息，进行一键估价）</div>

      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef"label-suffix="：">
        <el-form-item label="回收信息" prop="appraisedContent">
          <!-- <wangEditor
            v-model="dataForm.appraisedContent"
            placeholder="请粘贴回收信息，进行一键估价"
          ></wangEditor> -->
          <el-input
            v-model="dataForm.appraisedContent"
            type="textarea"
            placeholder="请粘贴回收信息，进行一键估价"
            :rows="10"
          ></el-input>
        </el-form-item>

        <div class="p-title">选择公式</div>

        <el-form-item label="游戏" prop="gameId">
          <el-select clearable v-model="dataForm.gameId" placeholder="请选择游戏" @change="changeGame">
            <el-option v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="公式" prop="formulaId">
          <el-select clearable v-model="dataForm.formulaId" placeholder="选择公式">
            <el-option v-for="item in currentGameFormula" :key="item.id" :label="item.formulaContent" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="">
          <el-button :loading="btnLoading" @click="clickValuation" type="primary">一键估价</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="right-price">
      <div class="p-title">估算价格</div>
      <div class="appraised-price flx-column flx-center" v-loading="btnLoading">
        <el-empty v-if="!valuation" :image-size="120" description="暂未进行估价"></el-empty>
        <div class="price-result" v-else>
          <div class="text">您的游戏账号估价结果为：</div>
          <div class="price-wrap flx-align-end flx-justify-center">
            <span class="symbol">￥</span>
            <span class="price">{{ valuation }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import baseService from "@/service/baseService";
import { useRouter } from "vue-router";
import { useSettingStore } from "@/store/setting";
import { IObject } from "@/types/interface";
import { generateUUID } from "@/utils/utils";

const settingStore = useSettingStore();

const dataForm = ref({
  // 游戏id
  gameId: "",
  // 回收信息
  appraisedContent: "",
  // 估算价格
  price: 0,
  // 条件
  preconditionsFormula: "",
  // 公式id
  formulaId: "",
});

const rules = ref({
  appraisedContent: [{ required: true, message: "回收信息不能为空", trigger: "blur" }],
  gameId: [{ required: true, message: "请选择游戏", trigger: "change" }],
  formulaId: [{ required: true, message: "请选择公式", trigger: "change" }],
});

// 获取游戏下拉
const gameList = ref(<IObject>[]);
const getgameList = () => {
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    gameList.value = res.data;
    currentGameFormula.value = formulaListAll.value.filter((item: any) => item.gameId == dataForm.value.gameId);
  });
};


// 获取公式列表
const formulaListAll = ref(<IObject>[]);
const currentGameFormula = ref(<IObject>[]);
const getFormulaList = () => {
  baseService.get("/appraise/formula/page", { limit: 999 }).then((res) => {
    formulaListAll.value = res.data.list;

    getgameList();
  });
};
getFormulaList();

// 选择游戏
const changeGame = () => {
  dataForm.value.formulaId = "";
  currentGameFormula.value = formulaListAll.value.filter((item: any) => item.gameId == dataForm.value.gameId);
}


// 估价网站
const link = `${settingStore.info.websiteUrl}recycleView?ny=${generateUUID()}`;
const link2 = `${settingStore.info.mobileWebsiteUrl}#/pages/appraisedPrice/WZRY/index`;

// 公式设定
const formulaSetKey = ref(0);
const formulaSetRef = ref();

// 一键估价
const btnLoading = ref(false);
const valuation = ref("");
const formRef = ref();
const clickValuation = () => {

  formRef.value.validate(async (valid: any) => {
    if (!valid) return;
      let appraisedContent = dataForm.value.appraisedContent.split(/[(\r\n)\r\n]+/);
      
      let content: any = [];
      appraisedContent.map((item, index) => {
        let val = item.split('：');
        if (val[1]) { content.push({ [JSON.stringify(val[0])]: val[1] }) }
      })
    
      btnLoading.value = true;
      try {
        let res = await baseService.post("/appraise/formula/appraise", { content: content, formulaId: dataForm.value.formulaId })
        valuation.value = res.data.toString();
        btnLoading.value = false;
      } catch (error) {
        btnLoading.value = false;
      }
  });
}

// 跳转估价网站
const jump = () => {
  window.open(link, "_blank");
};
// 跳转估价报告
const jump2 = () => {
  window.open(link2, "_blank");
};
</script>

<style lang="scss" scoped>
.copy-input-width {
  width: 60%;
  margin-right: 8px;
}
.right-price {
  width: 390px;
  padding-left: 12px;
  margin-left: 12px;
  border-left: 1px solid #e4e7ed;

  .appraised-price {
    width: 100%;
    height: 240px;
    background: #fff url("@/assets/images/appraised_price_bg.png") top no-repeat;
    background-size: 100%;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .price-result {
      .text {
        line-height: 22px;
        font-size: 14px;
        color: #606266;
      }

      .price-wrap {
        padding-top: 20px;
        color: #f44a29;

        .symbol {
          font-size: 20px;
        }

        .price {
          font-size: 32px;
          line-height: 32px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
