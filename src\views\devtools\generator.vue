<template>
  <div class="mod-generator">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-form-slot>
        <template v-slot:content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.tableName" placeholder="表名"></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:button>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="primary" @click="importHandle()">导入数据库表</el-button>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="tableName" label="表名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="tableComment" label="表说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="className" label="类名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="400">
          <template v-slot="scope">
            <el-button type="primary" link @click="handlePreview(scope.row.id)">预览</el-button>
            <el-button type="primary" text bg @click="editTableHandle(scope.row.id)">编辑</el-button>
            <el-button type="primary" text bg @click="generatorCodeHandle(scope.row.id)">生成代码</el-button>
            <el-button type="primary" text bg @click="generatorMenuHandle(scope.row)">创建菜单</el-button>
            <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <import ref="importRef" @refreshDataList="state.getDataList"></import>
    <edit-table ref="editTableRef" @refreshDataList="state.getDataList"></edit-table>
    <generator-code ref="generatorCodeRef" @refreshDataList="state.getDataList"></generator-code>
    <generator-menu ref="generatorMenuRef" @refreshDataList="state.getDataList"></generator-menu>
    <el-dialog :title="preview.title" v-model="preview.open" width="80%" top="5vh" append-to-body class="scrollbar">
      <el-tabs v-model="activeTabName">
        <el-tab-pane
          v-for="(value, key) in preview.data"
          :label="key"
          :name="key"
          :key="value"
        >
          <el-link :underline="false" icon="DocumentCopy" v-copyText="value" @click="copyValue(value)"
                   style="float:right">&nbsp;复制
          </el-link>
          <pre>{{ value }}</pre>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import Import from "./generator-import.vue";
import EditTable from "./generator-edittable.vue";
import GeneratorCode from "./generator-code.vue";
import GeneratorMenu from "./generator-menu.vue";
import { IObject } from "@/types/interface";
import baseService from "@/service/baseService";
import {Message} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";

// const { proxy } = getCurrentInstance();
const preview= reactive({
  open: false,
  title: "代码预览",
  data: {},
});
let activeTabName = ref('');

const view = reactive({
  getDataListURL: "/devtools/table/page",
  getDataListIsPage: true,
  deleteURL: "/devtools/table",
  deleteIsBatch: true,
  importVisible: false,
  editTableVisible: false,
  generatorCodeVisible: false,
  generatorMenuVisible: false,
  dataForm: {
    tableName: ""
  }
});

const dataForm = reactive({
  id: "",
  baseclassId: "",
  backendPath: "",
  frontendPath: "",
  packageName: "",
  email: "",
  author: "",
  version: "",
  subModuleName: "",
  moduleName: "",
  className: "",
  tableComment: "",
  tableName: ""
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  view.dataForm.tableName = "";
  state.getDataList();
}

const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};
const copyValue = (value: string) => {
  const textarea = document.createElement('textarea');
  textarea.value = value;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  document.body.removeChild(textarea);
};
const handlePreview = (id:any)=>{
  baseService.get("/devtools/table/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    baseService.post("/devtools/preview", dataForm).then((res1) => {
      preview.data=res1.data
      activeTabName = ref(Object.keys(preview.data)[0]);
      preview.open=true
    })
  });
}
const editTableRef = ref();
const editTableHandle = (id: string) => {
  editTableRef.value.init(id);
};

const generatorCodeRef = ref();
const generatorCodeHandle = (id: string) => {
  generatorCodeRef.value.init(id);
};

const generatorMenuRef = ref();
const generatorMenuHandle = (row: IObject) => {
  generatorMenuRef.value.dataForm.moduleName = row.moduleName;
  generatorMenuRef.value.dataForm.className = row.className;
  generatorMenuRef.value.init();
};
</script>
