<template>
  <el-drawer v-model="visible" size="768" class="drawer">
    <template #header>
      <div class="drawer_title">{{ !dataForm.id ? $t("add") : $t("update") }}审核流程</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="shop_page">
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="流程名称" prop="name">
                <el-input v-model="dataForm.name" placeholder="请输入流程名称" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="流程类型" prop="type">
                <el-select v-model="dataForm.type" placeholder="流程类型">
                  <el-option
                    v-for="item in [
                      { title: '放款', id: '1' },
                      { title: '退款', id: '2' },
                      { title: '提现', id: '3' },
                      { title: '销售放款', id: '4' },
                      { title: '线下收款确认', id: '5' },
                      { title: '其他收支', id: '9' }
                    ]"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="阶段负责人" prop="stage0link">
                <el-checkbox-group v-model="dataForm.stage0link">
                  <el-checkbox v-for="(ele, eleIndex) in selectListObj.roleList" :key="eleIndex" :label="ele.name" :value="ele.id" />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="阶段审批设置" prop="stage">
                <el-card shadow="never" v-for="(ele, eleIndex) in dataForm.stage" :key="eleIndex">
                  <div class="cardTitle">
                    <div style="display: flex; align-items: center">
                      <div class="tag">{{ eleIndex + 1 }}</div>
                      <el-input style="width: 157px; height: 24px; margin-left: 10px; background: #ffffff" v-model="ele.name" placeholder="请输入阶段审批名称"></el-input>
                    </div>
                    <div>
                      <el-button style="width: 14px; height: 14px" @click="addArrHandle" :icon="Plus" type="primary" circle />
                      <el-button v-if="eleIndex != 0" style="margin-left: 12px; width: 14px; height: 14px" type="danger" @click="delArrHandle(eleIndex)" :icon="Delete" circle />
                    </div>
                  </div>
                  <div class="cardContent">
                    <div style="margin-bottom: 8px">
                      <span>阶段审批人类型</span>
                      <el-radio-group v-model="ele.type" @change="ele.link = undefined">
                        <el-radio value="1">默认审批人</el-radio>
                        <el-radio value="2">指定审批人</el-radio>
                      </el-radio-group>
                    </div>
                    <div v-if="ele.type == 1">
                      <span>阶段审批人</span>
                      <el-checkbox-group v-model="ele.link">
                        <el-checkbox v-for="(ele, eleIndex) in selectListObj.roleList" :key="eleIndex" :label="ele.name" :value="ele.id" />
                      </el-checkbox-group>
                    </div>
                    <div v-else>
                      <span>选择审批人</span>
                      <div style="display: flex; align-items: center">
                        <el-select multiple style="width: 358px" v-model="ele.link" :placeholder="ele.userList && ele.userList.length > 0 ? '请选择审批人' : '请先选择部门'">
                          <el-option v-for="item_ in ele.userList" :key="item_.id" :label="item_.name" :value="item_.linkId" />
                        </el-select>
                        <ny-dept-tree
                          @changeDeptId="
                            (id?:any) => {
                              changeDeptId(id, eleIndex);
                            }
                          "
                          placeholder="选择部门"
                          :query="true"
                          style="width: 220px; margin-right: 12px"
                        ></ny-dept-tree>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="进入阶段是否提醒" prop="isRemind">
                <el-radio-group v-model="dataForm.isRemind">
                  <el-radio :value="1">是</el-radio>
                  <el-radio :value="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="要求填写字段" prop="fields">
                <el-checkbox-group v-model="dataForm.fields">
                  <el-checkbox label="提现金额" :value="1" />
                  <el-checkbox label="提现收款账户" :value="2" />
                  <el-checkbox label="原因" :value="3" />
                  <el-checkbox label="备注" :value="4" />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-scrollbar>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const btnLoading = ref(false);
const requestLoading = ref(false); // 详情加载
const dataFormRef = ref();
const selectListObj = reactive({
  roleList: [],
  userList: []
});
const dataForm = reactive({
  type: undefined,
  name: undefined,
  stage0link: undefined,
  fields: undefined,
  isRemind: 1,
  stage: <any>[],
  status: 0 //默认禁用
});

// 自定义验证
const validatorData = (frule: any, value: any, callback: any) => {
  let errLength = 0;
  dataForm.stage.forEach((ele: any) => {
    if (!ele.name || !ele.link || (ele.name && ele.name.length < 0) || (ele.link && ele.link.length < 1)) {
      errLength++;
    }
  });
  if (errLength > 0) {
    callback(new Error("请完善审批设置信息"));
  } else {
    callback();
  }
};
const rules = {
  name: [{ required: true, message: "请输入流程名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择流程类型", trigger: "change" }],
  stage0link: [{ required: true, message: "请选择阶段负责人", trigger: "change" }],
  stage: [
    { required: true, message: "请设置阶段审批", trigger: "blur" },
    { trigger: "blur", validator: validatorData }
  ], //自定义数据有空白就提示-未做
  fields: [{ required: true, message: "请选择要求填写字段", trigger: "change" }],
  isRemind: [{ required: true, message: "请选择进入阶段是否提醒", trigger: "change" }]
};

const init = (id?: number) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  delete dataForm.id;
  if (id) {
    // 编辑
    getInfo(id);
  } else {
    // 添加处理默认数据
    dataForm.stage0link = undefined;
    //处理阶段设置
    dataForm.stage = [
      {
        name: undefined,
        type: "1",
        stage: 1,
        link: [],
        userList: [] //delete
      }
    ];
  }
};

// 添加流程
const addArrHandle = () => {
  let num = dataForm.stage[dataForm.stage.length - 1].stage;
  dataForm.stage.push({
    name: undefined,
    type: "1",
    stage: +num + 1,
    link: [],
    userList: [] //delete
  });
};
// 删除流程
const delArrHandle = (index?: any) => {
  dataForm.stage.splice(index, 1);
};
// 获取信息
const getInfo = (id: number) => {
  requestLoading.value = true;
  baseService.get("/flowable/flowable/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    //处理阶段负责人
    let obj = res.data.stage.find((ele: any) => ele.stage == 0);
    dataForm.stage0link = obj?.link ? obj?.link.map((ele: any) => ele.linkId) : [];
    //处理阶段设置
    dataForm.stage = res.data.stage.filter((ele: any) => ele.stage != 0);
    // 处理阶段人员下拉
    dataForm.stage.forEach((element) => {
      if (element.type == 2) {
        element.userList = element.link.map((ele) => {
          return {
            linkId: ele.linkId,
            name: ele.name
          };
        });
      }
      // 处理用户或角色数据
      element.link = element.link ? element.link.map((ele) => ele.linkId) : [];
    });

    requestLoading.value = false;
  });
};
const getRoleList = () => {
  baseService.get("/sys/tenant/role/all").then((res) => {
    selectListObj.roleList = res.data || [];
  });
};
getRoleList();
const changeDeptId = (id?: String | Number, index_?: Number) => {
  baseService.get("/sys/user/page", { page: 1, limit: 9999, deptId: id }).then((res) => {
    let arr = res.data.list || [];
    arr = arr.map((ele: any) => {
      return {
        linkId: ele.id,
        name: ele.realName
      };
    });
    let finalArr: any = [];
    console.log(arr, dataForm.stage[index_].link);
    // 筛选出已选的用户列表
    (dataForm.stage[index_].userList || []).forEach((ele) => {
      if (dataForm.stage[index_].link && dataForm.stage[index_].link.findIndex((e) => ele.linkId == e) > -1 && arr.findIndex((e) => e.linkId == ele.linkId) < 0) {
        finalArr.push(ele);
      }
    });
    dataForm.stage[index_].userList = [...arr, ...finalArr];
  });
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let form = { ...dataForm };
    form.stage = form.stage.map((ele) => {
      delete ele.userList;
      return ele;
    });
    form.stage.push({
      name: ["", "放款", "退款", "提现"][+form.type] + "审批",
      type: 1,
      stage: 0,
      link: form.stage0link
    });
    delete form.stage0link;
    console.log(form);
    // return;
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/flowable/flowable", form)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            btnLoading.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-drawer__body {
  padding: 20px;
  background: #fff;
}
.el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.el-form-item {
  display: inherit;
  margin-bottom: 12px;
  .el-form-item__label {
    font-weight: 400;
    font-size: 13px;
    color: #606266;
    line-height: 22px;
  }
  .el-checkbox {
    margin-right: 24px;
  }
  :deep(.el-checkbox__inner) {
    border-radius: 4px;
  }
}
.el-card {
  width: 100%;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0px;
  }
  :deep(.el-card__body) {
    padding: 0;
    .cardTitle {
      background: #f5f7fa;
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0px 8px;

      .tag {
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        background: var(--el-color-primary);
        border-radius: 4px;
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #ffffff;
      }

      .el-input__inner {
        line-height: 20px;
        height: 24px;
      }
    }
    .cardContent {
      padding: 8px;
      > div {
        > span {
          display: block;
          font-family: Inter, Inter;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 22px;
          margin-bottom: 2px;
        }
      }
    }
  }
}
</style>
