<template>
    <div class="analysis_page">
        <div class="left_menu">
            <div class="item" :class="{active: item.value == menuValue}"
                 v-for="(item,index) in menuList" :key="index" 
                 @click="menuValue = item.value"
            >
                {{ item.label }}
            </div>
        </div>
        <div class="right_center">
            <!-- 运营效能洞察 -->
            <menuPage1 v-if="menuValue == 1"></menuPage1>
            <!-- 库存周转评估 -->
            <menuPage2 v-if="menuValue == 2"></menuPage2>
            <!-- 财务指标追踪 -->
            <menuPage3 v-if="menuValue == 3"></menuPage3>
            <!-- 售后趋势解析 -->
            <menuPage4 v-if="menuValue == 4"></menuPage4>
            <!-- 销售排行榜 -->
            <menuPage5 v-if="menuValue == 5"></menuPage5>
            <!-- 回收排行榜 -->
            <menuPage6 v-if="menuValue == 6"></menuPage6>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
import menuPage1 from './components/menuPage1.vue';
import menuPage2 from './components/menuPage2.vue';
import menuPage3 from './components/menuPage3.vue';
import menuPage4 from './components/menuPage4.vue';
import menuPage5 from './components/menuPage5.vue';
import menuPage6 from './components/menuPage6.vue';

const menuList = ref([
    {label: "运营效能洞察", value: 1},
    {label: "库存周转评估", value: 2},
    {label: "财务指标追踪", value: 3},
    {label: "售后趋势解析", value: 4},
    {label: "销售排行榜", value: 5},
    {label: "回收排行榜", value: 6},
])
const menuValue = ref(1);

</script>

<style lang='less' scoped>
.analysis_page{
    padding: 12px 20px;
    display: flex;
    align-items: flex-start;
    gap: 24px;
    .left_menu{
        width: 186px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #EBEEF5;
        .item{
            width: 100%;
            height: 50px;
            padding: 18px 20px;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            cursor:pointer;

            &:hover{
                background-color: var(--el-color-primary-light-9);
                color: var(--el-color-primary);
            }
        }
        .active{
            background-color: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
        }
    }
    .right_center{
        flex: 1;
        width: 60%;
        // border: 1px solid red;
    }
}
</style>