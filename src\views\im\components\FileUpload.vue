<template>
  <div class="uploadImgStyle">
    <el-upload :before-upload="uploadChange" :accept="previousProps.accept" :multiple="false" :action="url" :on-success="handleAvatarSuccess" :auto-upload="true">
      <div class="BtnSty" :style="{ width: widthUpload, height: heightUpload }">
        <el-icon class="el-icon--upload" style="color: var(--el-color-primary)"><upload-filled /></el-icon>
      </div>
    </el-upload>
  </div>
</template>

<script lang="ts">
import { reactive, defineComponent, ref } from "vue";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";

export default defineComponent({
  emits: ["uploadSuccess", "beforeUpload"],
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 1
    },
    fileSize: {
      type: Number || String,
      default: 20
    },
    accept: {
      type: String,
      default: "image/*"
    },
    widthUpload: {
      type: String,
      required: false,
      default: "148px"
    },
    heightUpload: {
      type: String,
      required: false,
      default: "148px"
    }
  },

  setup(props, { emit, expose }) {
    const uploadChange = (file) => {
      console.log(file);
      emit("beforeUpload");
    };
    const previousProps = reactive({
      // 是否支持多选
      multiple: props.multiple,
      // 限制上传的文件数量
      limit: props.limit,
      // 限制上传的文件大小,传参时以参数为准。不传时默认20M
      fileSize: props.fileSize,
      // 允许上传的文件类型
      accept: props.accept
    });

    // 文件上传后端地址
    const url = `${app.api}/sys/oss/upload?token=${getToken()}`;

    // 文件上传成功的函数
    const handleAvatarSuccess = (res: any) => {
      emit("uploadSuccess", {
        src: res.data.src,
        targetId: targetId.value
      });
    };
    const targetId = ref();
    const initeTarget = (val: any) => {
      targetId.value = val;
    };
    expose({
      initeTarget
    });
    return {
      uploadChange,
      previousProps,
      url,
      handleAvatarSuccess
    };
  }
});
</script>

<style scoped>
/* .avatar-uploader {
      width: 148px;
      height: 148px;
      display: block;
  } */

.avatar-uploader .avatar {
  /* width: 148px;
    height: 148px; */
  display: block;
}
</style>

<style lang="scss">
.avatar-uploader .el-upload-dragger {
}
</style>
