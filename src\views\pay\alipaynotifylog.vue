<template>
    <div class="mod-pay__alipaynotifylog">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header-right>
                    <el-input v-model="state.dataForm.outTradeNo" :placeholder="$t('order.outTradeNo')" clearable></el-input>
                    <el-input v-model="state.dataForm.notifyId" :placeholder="$t('order.notifyId')" clearable></el-input>
                    <el-input v-model="state.dataForm.tradeStatus" :placeholder="$t('order.tradeStatus')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>


                <template #tradeStatus="{row}">
                    {{ tradeStatus[row.tradeStatus] }}
                </template>

            </ny-table>
        
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, toRefs } from "vue";

const view = reactive({
    getDataListURL: "/pay/alipayNotifyLog/page",
    getDataListIsPage: true,
    deleteIsBatch: true,
    dataForm: {
        outTradeNo: "",
        notifyId: "",
        tradeStatus: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const tradeStatus = {
    TRADE_SUCCESS: "交易成功",
    TRADE_FINISHED: "交易结束",
    WAIT_BUYER_PAY: "交易创建",
    TRADE_CLOSED: "交易关闭"
}

// 表格配置项
const columns = reactive([
    {
        prop: "outTradeNo",
        label: "订单ID",
        minWidth: "120"
    },
    {
        prop: "totalAmount",
        label: "订单金额",
        minWidth: "120"
    },
    {
        prop: "buyerPayAmount",
        label: "付款金额",
        minWidth: "120"
    },
    {
        prop: "notifyId",
        label: "通知校验ID",
        minWidth: "150"
    },
    {
        prop: "tradeNo",
        label: "交易号",
        minWidth: "150"
    },
    {
        prop: "tradeStatus",
        label: "交易状态",
        minWidth: "120"
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: "150",
        sortable: true
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.outTradeNo = "";
    view.dataForm.notifyId = "";
    view.dataForm.tradeStatus = "";
    state.getDataList();
}
</script>
