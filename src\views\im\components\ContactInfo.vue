<template>
    <div class="contact-info-wrap">
        <div class="contact-info">
            <div class="user flx-justify-between">
                <div class="left">
                    <div class="name flx-center">
                        {{ userInfo.nickname }}
                        <el-icon size="24" class="gender"><UserFilled /></el-icon>
                    </div>
                    <!-- <div class="desc">{{ userInfo.nickname }}</div> -->
                </div>
                <el-image class="avatar" :src="userInfo.headUrl || sttingInfo.info.backendLogo"></el-image>
            </div>
            
            <!-- im不支持设置备注, 暂不显示 -->
            <!-- <div class="user-item">
                <div class="label">设置备注</div>
                <div class="value">
                    <el-input v-if="showEdit" v-model="userRemark" @keyup.enter.native="updateUserRemark"></el-input>
                    <div v-else class="flx-center pointer" @click="showEdit = true">
                        <span>{{ userRemark || '未设置' }}</span>
                        <el-icon class="ml-8"><EditPen /></el-icon>
                    </div>
                </div>
            </div> -->
            <div class="user-item">
                <div class="label">部门</div>
                <div class="value">
                    {{ userInfo.deptName || '-' }}
                </div>
            </div>
            <div class="user-item">
                <div class="label">手机号</div>
                <div class="value">
                    {{ userInfo.mobile || '-' }}
                </div>
            </div>

            <el-button type="primary" class="send-btn" @click="initiatedCOnversationHandle">发消息</el-button>

        </div>

    </div>
</template>

<script lang="ts" setup>
import { UserFilled, EditPen } from "@element-plus/icons-vue";
import { useImStore } from "@/store/im";
import { ref, watch } from "vue";
import { getConversation } from '@/utils/imTool';
import { useSettingStore } from '@/store/setting';

const sttingInfo = useSettingStore();
const imStore = useImStore();
const showEdit = ref(false);
const userRemark = ref('');

// 修改备注
const updateUserRemark = () => {
    showEdit.value = false;
}

const userInfo = ref(<any>{});

watch(() => imStore.currentContactInfo, (newValue) => {
    userInfo.value = newValue;
}, {  deep: true, immediate: true })

// 发起会话
const initiatedCOnversationHandle = () => {
    if(!userInfo.value.imUid) return;
    getConversation(userInfo.value.imUid).then((res: any) => {
        res.sessionData = userInfo.value;
        imStore.currentConversation = res;
        imStore.showContactInfo = false;
        imStore.currentConversationType = '';
        
        // startPrivateConversation(imStore.imUid, data.imUid);
    })
}


</script>

<style lang="scss" scoped>
    .contact-info-wrap{
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;

        .contact-info{
            width: 400px;

            .user{
                padding-bottom: 32px;
                margin-bottom: 24px;
                border-bottom: 1px solid #E4E7ED;

                .name{
                    font-size: 24px;
                    font-weight: 400;
                    line-height: 29px;

                    .gender{
                        color: #409eff;
                        margin-left: 10px;
                    }
                }

                .desc{
                    color: #909399;
                    line-height: 17px;
                    padding-top: 4px;
                }
            }

            .avatar{
                width: 64px;
                height: 64px;
                border-radius: 4px;
            }

            .user-item{
                height: 56px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(99,106,116,0.15);

                .label, span{
                    color: #909399;
                }

                .value{
                    color: #171A1D;
                    display: flex;
                    align-items: center;
                }
            }

            .send-btn{
                display: block;
                width: 200px;
                height: 40px;
                margin: 24px auto;
            }
        }
    }
</style>