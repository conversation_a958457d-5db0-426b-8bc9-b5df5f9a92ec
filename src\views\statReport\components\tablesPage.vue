<template>
  <div class="tables_page">
    <div style="margin-bottom: 12px">
      <el-form label-width="auto" style="border-bottom: 1px solid #f0f0f0; padding: 0px 10px">
        <el-row :gutter="20">
          <el-col :span="8" v-for="(item, index) in searchForm">
            <el-form-item :label="item.name">
              <el-input v-model="item.value" type="number" :clearable="true" v-if="item.type == 2" />
              <el-select v-model="item.value" placeholder="请选择" style="width: 100%" :clearable="true" v-else-if="item.type == 3">
                <el-option v-for="(it, ind) in item.options.split(',')" :key="ind" :label="it" :value="it" />
              </el-select>
              <el-date-picker v-model="item.value" type="date" placeholder="请选择日期" format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 100%" :clearable="true" v-else-if="item.type == 4" />
              <el-input v-model="item.value" :clearable="true" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker v-model="createTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 220px" unlink-panels :clearable="true" @change="getCreateTime()" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="searchFn">查询</el-button>
              <el-button @click="restFn">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="tables_header flx-justify-between">
      <div class="butList">
        <el-button color="#73767A" :icon="RefreshRight" @click="getList"></el-button>
        <el-button type="primary" @click="tablesPageAddOrUpdateHandle()">新增</el-button>
        <!-- <el-button type="warning">导入</el-button> -->
        <el-button type="primary" @click="handleExport">导出</el-button>
        <el-popconfirm width="220" confirm-button-text="删除" cancel-button-text="取消" title="确定删除当前数据吗？" @confirm="TableDelete()">
          <template #reference>
            <el-button type="danger" :disabled="!dataListSelections.length">批量删除</el-button>
          </template>
        </el-popconfirm>
        <el-button type="success" v-if="props.menuInfo.isAudit" @click="submitExamineFn">提交</el-button>
        <el-button color="#409EFF" v-if="props.menuInfo.isAudit" @click="examineFn()"><span style="color: #fff">批量审批</span></el-button>
      </div>
    </div>
    <el-table :data="tableData" v-loading="tableLoading" style="width: 100%; margin-top: 10px" border :summary-method="getSummaries" show-summary @selection-change="handleSelectionChange" @sort-change="onSortChange">
      <el-table-column type="selection" align="center" fixed width="60" />
      <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="60" />
      <el-table-column v-for="(item, index) in tableHeader" :prop="item.dynamicKey" :label="item.name" align="center" :width="item.name.length < 10 ? 200 : item.name.length * 20" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(row[`${item.dynamicKey}`])">
            <el-image style="width: 36px; height: 36px; border-radius: 4px" :src="row[`${item.dynamicKey}`]" :preview-src-list="[row[`${item.dynamicKey}`]]" preview-teleported fit="contain" />
          </span>
          <span v-else>{{ row[`${item.dynamicKey}`] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" sortable="custom" align="center" min-width="200">
        <template #default="scope">
          {{ timeFormat(scope.row.createDate) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="tag" label="审批状态" align="center" width="100" v-if="props.menuInfo.isAudit">
        <template #default="{ row }">
          <el-tag type="info" v-if="row.status == 0">未提交</el-tag>
          <el-tag type="warning" v-if="row.status == 1 && myAuditFlow == row.auditFlow">待审核</el-tag>
          <el-tag type="primary" v-if="row.status == 1 && myAuditFlow != row.auditFlow">审核中</el-tag>
          <el-tag type="success" v-if="row.status == 2">审核通过</el-tag>
          <el-tag type="danger" v-if="row.status == 3">审核拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" min-width="200">
        <template #default="{ row }">
          <el-button link type="success" size="small" v-if="row.status == 1 && myAuditFlow == row.auditFlow" @click="examineFn(row)">审批</el-button>
          <el-button link type="primary" size="small" @click="examineFn(row, true)">查看</el-button>
          <el-button link type="primary" size="small" @click="tablesPageAddOrUpdateHandle(row)" v-if="row.status != 1">编辑</el-button>
          <el-popconfirm width="220" confirm-button-text="删除" cancel-button-text="取消" title="确定删除当前数据吗？" @confirm="TableDelete(row)">
            <template #reference>
              <el-button link type="danger" size="small" v-if="row.status != 1">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-pagination :current-page="requestParams.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="requestParams.limit" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
    <!-- 审核 -->
    <examineForm ref="examineFormRef" :menuInfo="menuInfo" @refreshDataList="getList"></examineForm>
  </div>
  <!-- 新增/编辑 -->
  <tablesPageAddOrUpdate ref="tablesPageAddOrUpdateRef" @refreshDataList="getList"></tablesPageAddOrUpdate>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, onMounted, watch } from "vue";
import { RefreshRight, Search } from "@element-plus/icons-vue";
import examineForm from "./examineForm.vue";
import tablesPageAddOrUpdate from "./tablesPage-add-or-update.vue";
import baseService from "@/service/baseService";
import { timeFormat, fileExport } from "@/utils/utils";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
const store = useAppStore();

interface Props {
  menuInfo: any;
}
const props = withDefaults(defineProps<Props>(), {
  menuInfo: ""
});

const searchValue = ref("");
const timeInterval = ref("");

// 序号
const indexMethod = (index: number) => {
  return index + 1;
};

// 表格选择
const dataListSelections = ref(<any>[]);
const handleSelectionChange = (val: []) => {
  dataListSelections.value = val;
};

const tableData = ref([]);
const Summaries = ref([]); // 合计数据

// 表格合计
const getSummaries = (param: any) => {
  const { columns, data } = param;
  const sums: any = [];
  // 获取表头中需要合计的字段
  let summList = tableHeader.value.filter((item: any) => item.isSum).map((item: any) => ({ property: item.dynamicKey }));
  // console.log('======= 获取需要合计的字段 ========', summList);
  // 获取表头中合计字段的下标以及名称
  let summInfo: any = [];
  tableHeader.value.map((item: any, index: number) => {
    if (item.isSum) {
      summInfo.push({ key: item.dynamicKey, index: index + 1 });
    }
  });
  let arr = columns.concat(summList);
  arr.forEach((column: any, index: any) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }
    summInfo.map((itm: any) => {
      Summaries.value.map((ite: any) => {
        if (column.property == itm.key) {
          if (ite.name.slice(0, -3) == itm.key) {
            sums[itm.index + 1] = ite.value.toFixed(2);
          }
        }
      });
    });
  });
  return sums;
};

// 提交
const submitExamineFn = () => {
  const errId = dataListSelections.value.filter((item: any) => item.status != 0);
  if (errId.length) {
    ElMessage.warning("请选择状态未提交的数据!");
    return;
  }

  const ids = dataListSelections.value.map((item: any) => item.id);
  baseService.post("/report/reportaudit/audit/" + props.menuInfo.indexName, ids).then((res) => {
    if (res.code == 0) {
      ElMessage.success("提交成功");
      getList();
    }
  });
};

// 表格请求参数
const requestParams: any = {
  page: 1,
  limit: 10,
  tableId: props.menuInfo.id,
  startTime: "",
  endTime: "",
  asc: null
};
const total = ref(0);

// 动态表头
const tableHeader = ref(<any>[]);
const searchForm = ref(<any>[]); // 筛选表单字段

// 动态字段
const reportcolumnAll = () => {
  tableHeader.value = [];
  baseService.get("/report/reportcolumn/all", { indexName: props.menuInfo.indexName }).then((res) => {
    tableHeader.value = res.data;
    searchForm.value = res.data.filter((item: any) => item.isSelect && item.type != 5);

    // console.log(searchForm.value,'==== res.data =====');
  });
};

// 创建时间点击事件
const createTime = ref("");
const getCreateTime = () => {
  requestParams.startTime = createTime.value ? createTime.value[0] : "";
  requestParams.endTime = createTime.value ? createTime.value[1] : "";
};

// 搜索
const searchFn = () => {
  searchForm.value.map((item: any) => {
    if (item.value) {
      requestParams[item.dynamicKey] = item.value;
    }
  });
  getList();
};

// 重置
const restFn = () => {
  searchForm.value.map((item: any) => {
    if (item.value) {
      delete requestParams[item.dynamicKey];
      item.value = "";
    }
  });
  createTime.value = "";
  requestParams.startTime = "";
  requestParams.endTime = "";
  getList();
};

const sizeChange = (number: any) => {
  requestParams.limit = number;
  getList();
};

const currentChange = (number: any) => {
  requestParams.page = number;
  getList();
};

// 表格数据
const tableLoading = ref(false);
const getList = () => {
  tableData.value = [];
  tableLoading.value = true;
  baseService
    .get("/report/report/table/page", requestParams)
    .then((res) => {
      Summaries.value = res.data.foot ? res.data.foot : [];
      tableData.value = res.data.list;
      total.value = res.data.total;
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 单条删除
const TableDelete = (row?: any) => {
  const ids = dataListSelections.value.map((item: any) => item.id);
  baseService.delete(`/report/report/delete/${props.menuInfo.indexName}`, row ? [row.id] : ids).then((res) => {
    if (res.code == 0) {
      ElMessage.success({
        message: "删除成功",
        duration: 500,
        onClose: () => {
          getList();
        }
      });
    }
  });
};

// 处理审批业务
const myAuditFlow = ref(0);
const examine = () => {
  if (props.menuInfo.isAudit) {
    baseService.get("/report/report/" + props.menuInfo.id).then((res) => {
      myAuditFlow.value = res.data.audits.find((item: any) => item.userId == store.state.user.id).auditFlow - 1;
    });
  }
};

// 审核
const examineKey = ref(0);
const examineFormRef = ref();
const examineFn = (row?: any, isView?: boolean) => {
  const err = dataListSelections.value.filter((item: any) => item.auditFlow != myAuditFlow.value || item.status != 1);
  if (err.length) {
    ElMessage.warning("请选择待审核的数据!");
    return;
  }

  // 审核参数
  const info = {
    indexName: props.menuInfo.indexName,
    userId: store.state.user.id,
    auditFlow: myAuditFlow.value + 1
  };

  examineKey.value++;
  nextTick(() => {
    examineFormRef.value.init(row ? [row] : dataListSelections.value, info, isView);
  });
};

watch(
  () => props.menuInfo.id,
  (newValue) => {
    if (newValue) {
      requestParams.tableId = newValue;
      // 删除筛选项中的key
      for(let key in requestParams){
        if(key.includes('number') || key.includes('field')){
          delete requestParams[key]
        }
      }
      nextTick(() => {
        // 获取表头数据
        reportcolumnAll();
        // 获取表格数据
        getList();
        // 审批业务逻辑
        examine();
      });
    }
  },
  {
    immediate: true
  }
);

// 列表排序
const onSortChange = (data: { column: any; prop: string; order: any }) => {
  console.log(data.order, "==== data.order =====");
  if (data.order) {
    requestParams.asc = data.order == "ascending" ? true : false;
    getList();
  } else {
    requestParams.asc = null;
    getList();
  }
};
const handleExport = () => {
  baseService.get("/report/report/export", { tableId: requestParams.tableId }).then((res) => {
    if (res) {
      fileExport(res, "统计报表-" + props.menuInfo.title);
    }
  });
};
onMounted(() => {
  Promise.all([reportcolumnAll()]).then(() => {
    getList();
  });
});

// 新增/编辑
const addKey = ref(0);
const tablesPageAddOrUpdateRef = ref();
const tablesPageAddOrUpdateHandle = (row?: any) => {
  addKey.value++;
  nextTick(() => {
    tablesPageAddOrUpdateRef.value.init(props.menuInfo, row);
  });
};
</script>

<style lang="less" scoped>
.tables_page {
  margin-top: 14px;
}
</style>
