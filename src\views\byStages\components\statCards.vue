<template>
  <div class="stat_card_finance">
    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 244, 254, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">分期中订单</span>
        </div>
      </div>
      <div class="stat_middle">{{ topData.inStagesQuantity || 0 }}</div>
      <div class="stat_below">
        <div>
          <span class="sum_label">总分期金额：</span>
          <span class="sum_value" style="color: #4165d7">{{ formatCurrency(topData.inStagesByStagesMoney) || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(160deg, rgba(255, 226, 226, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
      <div class="stat_top flx-justify-between">
        <div class="stat_top_title flx-align-center">
          <span class="name">即将到期订单</span>
        </div>
      </div>
      <div class="stat_middle" style="color: #f53f3f">{{ topData.dueQuantity || 0 }}</div>
      <div class="stat_below flx-align-center">
        <div style="flex: 1">
          <span class="sum_label">最快到期时间：</span>
          <span class="sum_value" style="color: #f53f3f">{{ topData.dueTime ? formatTimeStamp(topData.dueTime.time) : '' }}</span>
        </div>
        <div style="color: #f53f3f; display: flex; align-items: center; cursor: pointer" @click="toExamine(topData.dueTime.id)">
          <span style="line-height: 15px">马上付款</span><el-icon size="15"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 254, 234, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">已还清订单</span>
        </div>
      </div>
      <div class="stat_middle">{{ topData.payOffQuantity || 0 }}</div>
      <div class="stat_below">
        <div style="flex: 1">
          <span class="sum_label">手续费总金额：</span>
          <span class="sum_value" style="color: #00b42a">{{ topData.payOffCommissionTotal || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(135deg, rgba(254, 241, 230, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">违约订单</span>
        </div>
      </div>
      <div class="stat_middle">{{ topData.defaultQuantity || 0 }}</div>
      <div class="stat_below flx-align-center">
        <div style="margin-right: 12px">
          <span class="sum_label">违约总金额：</span>
          <span class="sum_value" style="color: #ff9a2e">{{ formatCurrency(topData.defaultTotalMoney) || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 238, 254, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between">
        <div class="stat_top_title flx-align-center">
          <span class="name">二次回收订单</span>
        </div>
      </div>
      <div class="stat_middle">{{ topData.recycleQuantity || 0 }}</div>
      <!-- <div class="stat_below">
        <div>
          <span class="sum_label">回收总金额：</span>
          <span class="sum_value" style="color: #165dff">{{ formatCurrency(topData.recycleTotalMoney) || 0 }}</span>
        </div>
      </div> -->
    </div>
    

    
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { formatTimeStamp } from "@/utils/method";
import dayjs from "dayjs";
const emit = defineEmits(["Examine"]);

const topData = ref(<any>{});
const getAll = (gameId?: string) => {
  baseService.get('/sale/saleOrderByStages/report?gameId='+ (gameId ? gameId : '')).then(res=>{
    if(res.code == 0){
      topData.value = res.data;
    }
    
  })
};

// 金额格式化
const formatCurrency = (number: number) => {
  if (isNaN(number) || number === null) return;
  const numStr = number.toString();
  const decimalIndex = numStr.indexOf(".");
  const integerNum = decimalIndex >= 0 ? numStr.substring(0, decimalIndex) : numStr;
  const integerStr = integerNum.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return decimalIndex >= 0 ? integerStr + numStr.substring(decimalIndex) : integerStr;
};

// 去审核
const toExamine = (id:any) =>{
  emit('Examine',id);
}

onMounted(()=>{
  getAll();
})

defineExpose({
  getAll
});
</script>

<style lang="less" scoped>
.stat_card_finance {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;

  .stat_item {
    // width: 19.5%;
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    .stat_top {
      padding: 12px;

      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }

    .stat_middle {
      padding: 4px 16px;
      font-weight: bold;
      font-size: 20px;
      color: #303133;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .stat_below {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 16px 12px 16px;

      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
