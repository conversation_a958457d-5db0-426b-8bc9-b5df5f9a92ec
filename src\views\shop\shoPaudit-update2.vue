<template>
    <el-drawer v-model="visible"  size="50%" class="drawer_shop">
        <template #header>
            <div class="drawer_title">商品信息审核</div>
        </template>
        <el-scrollbar v-loading="requestLoading">
            <div class="shop_page">
                <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top" >
                    <div class="shop_page_basic" v-show="!pageShow">
                        <div style="padding: 12px">
                            <ny-title title="基本信息" style="padding: 0px 0px 12px 0px"/>
                            <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                                <el-descriptions-item>
                                    <template #label>游戏名称</template>
                                    {{ dataForm.gameName }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>零售价(元)</template>
                                    <el-text type="danger">{{ dataForm.price }}</el-text>
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                        <!-- 商品信息 -->
                        <div style="padding: 12px;">
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="商品标题" prop="title">
                                        <el-input v-model="dataForm.title" type="textarea" placeholder="商品标题" :rows="4" maxlength="255" show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="商品描述" prop="title">
                                        <div class="shop_info">
                                            <div class="shop_info_left">
                                                <el-icon color="#909399"><Warning /></el-icon>
                                                <span class="tip">温馨提示：商品详情信息很重要，直接影响商品排名，越详细精准排名越靠前</span>
                                            </div>
                                            <div class="shop_info_right">
                                                <el-text type="warning" style="margin-right: 10px;" v-if="!isWrite">未填写</el-text>
                                                <el-text type="success" style="margin-right: 10px;" v-else>已填写</el-text>
                                                <el-button type="primary" plain @click="pageShow = true" :icon="Shop">新增游戏属性</el-button>
                                            </div>
                                        </div>
                                        <el-input v-model="dataForm.info" type="textarea" placeholder="商品描述" :rows="10" show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="商品主图" prop="log">
                                        <ny-shop-image-upload v-model="dataForm.log"></ny-shop-image-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="商品详情图" prop="imagesList">
                                        <ny-shop-image-upload v-model="dataForm.imagesList" :limit="100"></ny-shop-image-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item prop="highlights">
                                        <template #label>
                                            <div class="flx-justify-between">
                                                <div>商品亮点</div>
                                                <div class="flx-align-center" >
                                                    <span>亮点展示示例：</span>
                                                    <span class="mytag" style="max-width: 400px;">
                                                        <el-image class="topImg" :src="arrowIcon" />
                                                        {{ dataForm.highlights }}
                                                    </span>
                                                </div>
                                            </div>
                                        </template>
                                        <el-input v-model="dataForm.highlights" placeholder="商品亮点"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div class="shop_page_basic" v-show="!pageShow" style="margin: 12px 0px;">
                        <div style="padding: 12px">
                            <ny-title title="用户信息" style="padding: 0px 0px 12px 0px"/>
                            <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                                <el-descriptions-item>
                                    <template #label>用户ID</template>
                                    {{ dataForm.creator }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>用户名</template>
                                    {{ dataForm.nickname }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>联系手机</template>
                                    {{ dataForm.phone }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>联系微信</template>
                                    {{ dataForm.wx }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                    <div class="shop_page_basic" v-show="!pageShow" style="margin: 12px 0px; padding: 12px">
                        <ny-title title="自动降价" style="padding: 0px 0px 12px 0px"/>
                        <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                            <el-descriptions-item>
                                <template #label>自动降价周期(天)</template>
                                    {{ dataForm.priceReductionCycle || '-' }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template #label>自动降价比例(%)</template>
                                {{ dataForm.priceReductionPercentage || '-'  }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template #label>自动降价底价(元)</template>
                                {{ dataForm.minimumPrice || '-'  }}
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>
                    
                    <div class="shop_page_basic" v-show="!pageShow" style="margin: 12px 0px;" v-if="dataForm.basicInfo.length">
                        <div style="padding: 12px">
                            <ny-title title="商品信息" style="padding: 0px 0px 12px 0px"/>
                            <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                                <el-descriptions-item v-for="(item,index) in dataForm.basicInfo">
                                    <template #label>{{ item.key }}</template>
                                    {{ item.values }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                    <div class="shop_page_basic" v-show="!pageShow" style="margin: 12px 0px;" v-if="dataForm.accountInfo.length">
                        <div style="padding: 12px">
                            <ny-title title="账号信息" style="padding: 0px 0px 12px 0px"/>
                            <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                                <el-descriptions-item v-for="(item,index) in dataForm.accountInfo">
                                    <template #label>{{ item.key }}</template>
                                    {{ item.values }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                </el-form>
                <!-- 商品详情 -->
                <div class="shop_page_details" v-show="pageShow">
                    <el-form :model="dataForm" :rules="rules" ref="dataFormDetailRef" @keyup.enter="dataFormDetailSubmitHandle()" label-position="top">
                        <div class="shop_page_details_card" style="margin-bottom: 16px;">
                            <!-- 描述 -->
                            <ny-title title="商品描述" style="padding: 0px 0px 12px 0px"/>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="商品简介" prop="info">
                                        <el-input v-model="dataForm.info" type="textarea" placeholder="商品简介" :rows="10"></el-input>
                                        <el-text type="warning">tip：估价网站文本内容可在此进行解析。勾选底部属性信息后，会自动更新商品“描述信息”以及“标题”内容；</el-text>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item>
                                        <el-button type="primary" :icon="Pointer" @click="analysisFn">一键解析</el-button>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="shop_page_details_card" style="margin-bottom: 16px;">
                            <!-- 设置 -->
                            <ny-title title="设置" style="padding: 0px 0px 12px 0px"/>
                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="服务器" prop="gameAre">
                                        <el-select v-model="dataForm.gameAre" placeholder="服务器" @change="gameAreChange">
                                            <el-option
                                                v-for="item in sysgameList"
                                                :key="item.id"
                                                :label="item.title"
                                                :value="item.id"
                                            />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="系统区服" prop="server" v-if="sysgameChildrenList.length > 0">
                                        <el-select v-model="dataForm.server" placeholder="服务器" @change="feedBackInfo()">
                                            <el-option
                                                v-for="item in sysgameChildrenList"
                                                :key="item.id"
                                                :label="item.title"
                                                :value="item.id"
                                            />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="包赔标签" prop="compensation">
                                        <el-radio-group v-model="dataForm.compensation" size="small">
                                            <el-radio value="0" border>不可包赔</el-radio>
                                            <el-radio value="1" border>可买包赔</el-radio>
                                            <el-radio value="2" border>永久包赔</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="支持议价标签" prop="bargain">
                                        <el-radio-group v-model="dataForm.bargain" size="small">
                                            <el-radio value="0" border>否</el-radio>
                                            <el-radio value="1" border>是</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="顶级账号标签" prop="topped">
                                        <el-radio-group v-model="dataForm.topped" size="small">
                                            <el-radio value="0" border>否</el-radio>
                                            <el-radio value="1" border>是</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="shop_page_details_card">
                            <!-- 属性 -->
                            <ny-title title="商品属性" style="padding: 0px 0px 12px 0px"/>
                            <el-row>
                                <el-col :span="24">
                                    <div v-for="(item,index) in dataForm.attributesList" :key="index">
                                        <el-form-item :label="item.name" v-if="item.type == 1">
                                            <div class="flod_tab">
                                                <div class="flod_tab_cont">
                                                    <el-radio-group v-model="item.attributeIds" size="small" @change="feedBackInfo()">
                                                        <el-radio :value="it.id" border v-for="it in item.children">{{ it.name }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                                <div class="oper" style="width: 60px;">
                                                    <el-text type="primary" v-if="item.attributeIds" @click="item.attributeIds = ''" class="resetFont" >重置</el-text>
                                                </div>
                                            </div>
                                        </el-form-item>
                                        <el-form-item :label="item.name" v-if="item.type == 2">
                                            <div class="flod_tab">
                                                <div class="flod_tab_cont" :style="{ 'height': !item.fold ? 'auto' : '40px' }">
                                                    <el-checkbox-group v-model="item.attributeIds" @change="feedBackInfo()">
                                                        <el-checkbox :label="it.name" :value="it.id" v-for="it in item.children"/>
                                                    </el-checkbox-group>
                                                </div>
                                                <div class="oper">
                                                    <el-text type="primary" v-if="item.attributeIds.length > 0" @click="item.attributeIds = []" class="resetFont" >重置</el-text>
                                                    <el-button v-if="item.isMult" type="primary" plain size="small"
                                                        @click="item.fold = !item.fold">{{ item.fold ? '展开列表' : '收起列表' }}</el-button>
                                                </div>
                                            </div>
                                        </el-form-item>
                                        <el-form-item :label="item.name" v-if="item.type == 3">
                                            <div class="flod_tab">
                                                <el-input v-model="item.attributeText" :placeholder="item.name" style="width: 25%;" clearable @change="feedBackInfo()"></el-input>
                                            </div>
                                        </el-form-item>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </div>
            </div>
        </el-scrollbar>
        <template #footer>
            <div style="flex: auto" v-show="!pageShow">
                <el-button @click="visible = false">取消</el-button>
                <el-button :loading="btnLoading" type="warning" @click="dataFormSubmitHandle('2')" >一键上架</el-button>
                <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle('1')" >提交</el-button>
            </div>
            <div style="flex: auto" v-show="pageShow">
                <el-button @click="pageShow = false">返回</el-button>
                <el-button type="primary" @click="dataFormDetailSubmitHandle()" >确定保存</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref } from "vue";
import arrowIcon from "@/assets/images/tagicon.png";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back } from '@element-plus/icons-vue';
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);  // 对话框显隐
const pageShow = ref(false); // 商品详情显隐 
const isWrite = ref(false); // 商品详情是否填写
const dataFormRef = ref();  // 表单ref
const dataFormDetailRef = ref();  // 商品详情表单ref
const dataForm = reactive({  // 表单变量
    id: null,
    auditId:'',
    gameId: '',
    gameCode: '',
    gameName: '',
    highlights: '',
    title: '',
    price: 0,
    acquisitionPrice: 0,
    phone: '',
    gameAccount: '',
    gamePassword:'',
    log: [],
    imagesList: [],
    info:'',
    gameAre: '',
    server: '',
    compensation: '',
    bargain: '',
    topped: '',
    attributesList:<any>[],
    creator: '',
    nickname: '',
    wx: '',
    basicInfo:<any>[],
    accountInfo: <any>[]
});
const rules = ref({  // 表单必填项
    gameId: [{ required: true, message: '游戏名称不能为空', trigger: 'change', },],
    title: [{ required: true, message: '商品标题不能为空', trigger: 'blur', },],
    price: [{ required: true, message: '零售价不能为空', trigger: 'blur', },],
    acquisitionPrice: [{ required: true, message: '回收价不能为空', trigger: 'blur', },],
    gameAccount: [{ required: true, message: '游戏账号不能为空', trigger: 'blur', },],
    phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        {
            required: true,
            pattern: /^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
        },
    ],
    log: [{ required: true, message: '游戏主图不能为空', trigger: 'change', },],
    imagesList: [{ required: true, message: '详情图片不能为空', trigger: 'change', },],
    info: [{ required: true, message: '商品简介不能为空', trigger: 'blur', },],
    gameAre: [{ required: true, message: '服务器不能为空', trigger: 'change', },],
    server: [{ required: true, message: '系统区服不能为空', trigger: 'change', },],
    compensation: [{ required: true, message: '包赔标签不能为空', trigger: 'change', },],
    bargain: [{ required: true, message: '支持议价标签不能为空', trigger: 'change', },],
    topped: [{ required: true, message: '顶级账号标签不能为空', trigger: 'change', },],
});

const sysgameList = ref(<any>[]);  // 游戏区服信息 - 服务器
const sysgameChildrenList = ref(<any>[]);  // 游戏区服信息 - 系统区服


// 获取游戏属性信息
const getAttribute = () => {
    requestLoading.value = true;
    baseService.get("/game/attribute/page", { gameId: dataForm.gameId }).then((res) => { 
        if (res.data.length == 0) {
            ElMessage({
                type: 'warning',
                message: '没有查询到当前游戏属性信息请编辑后在来新增商品！',
            })
        } else {
            dataForm.attributesList = [];
            const sxWidth: number = Number(((document.querySelector('.flod_tab') as HTMLElement).offsetWidth * 0.5).toFixed(2));
            res.data.map((item: any) => {
                let allFontLen = 0;
                item.children.forEach((item: any) => {
                    allFontLen += Number((item.name.length) * 14 + 52);
                })
                
                if (Number((sxWidth / allFontLen).toFixed(2)) < 1) { 
                    dataForm.attributesList.push({
                        typeId: item.id,
                        attributeIds: item.type == 2 ? [] : '',
                        attributeText: '',
                        children: item.children,
                        isTitle: item.isTitle,
                        type: item.type,
                        name: item.name,
                        isMult: true,
                        fold: true
                    })
                } else {
                    dataForm.attributesList.push({
                        typeId: item.id,
                        attributeIds: item.type == 2 ? [] : '',
                        attributeText: '',
                        children: item.children,
                        isTitle: item.isTitle,
                        type: item.type,
                        name: item.name,
                        isMult: false,
                        fold: false
                    })
                }
                
            })
            if (dataForm.id) {
                getInfo(dataForm.id);
            }
        }
    }).finally(() => {
        requestLoading.value = false;
    })
}

// 获取游戏区服
const getSysgame = () => {
    baseService.get("/game/sysgame/get/" + dataForm.gameId).then((res) => { 
        sysgameList.value = res.data.areaDtoList;
        // console.log(sysgameList.value,res.data)
    })
}

// 服务器切换点击事件
const gameAreChange = (value: any) => {
    sysgameChildrenList.value = [];
    dataForm.server = '';
    const are = sysgameList.value.filter((item: any) => item.id == value)[0];
    if (are.children.length > 0) {
        sysgameChildrenList.value = are.children
    } else { 
        dataForm.server = value
    }
    feedBackInfo();
}

// 一键解析
const analysisFn = () => {
    if (!dataForm.info) {
        ElMessage.warning("请输入商品简介");
        return;
    }
    const strToArr = dataForm.info.split("\n");
    const infoArr = arrayToKeyValuePairs(strToArr);
    if (typeof infoArr == "string") {
        return;
    }
    infoArr.forEach((element: any) => {
        if (element.key.indexOf("服务器") > -1) {
        const gameAreItem = sysgameList.value.filter((item: any) => item.title == element.value)[0];
        if (gameAreItem.children.length > 0) {
            dataForm.gameAre = gameAreItem.id;
            sysgameChildrenList.value = gameAreItem.children;
        } else {
            dataForm.gameAre = gameAreItem.id;
            dataForm.server = gameAreItem.id;
        }
        } else if (element.key.indexOf("系统区服") > -1) {
        const serverItem = sysgameChildrenList.value.filter((item: any) => item.title == element.value)[0];
        dataForm.server = serverItem.id;
        } else if (element.key.indexOf("您自己认为是亮点的地方") > -1) {
        dataForm.highlights = element.value;
        } else {
        dataForm.attributesList.forEach((item: any) => {
            
            if (item.name == element.key) {
                console.log(element);
                if (item.type == 1) {
                    // 单选
                    const ids = nameQueryId(element.value, item.children);
                    item.attributeIds = ids.join("");
                }
                if (item.type == 2) {
                    // 多选
                    const ids = nameQueryId(element.value, item.children);
                    item.attributeIds = ids;
                }
                if (item.type == 3) {
                    // 文本
                    item.attributeText = element.value;
                }
            }
        });
        }
    });
    ElMessage.success("解析成功！");
};


// 文本内容转换成数组
const arrayToKeyValuePairs = (strArray: any) => {
    try {
        let arr: { key: any, value: any }[] = [];
        strArray.forEach((item: any) => {
            const [key, value] = item.split('：');
            if (value == undefined) {
                throw '解析失败，商品简介文本内容结构错误。请检查后重新解析；'
            }
            arr.push({ key: key, value: value })
        })
        return arr
    } catch (err: any) {
        ElMessage.warning(err)
        return err;
    }
}

// 属性名称查询属性ID
const nameQueryId = (value: string, data: any[]) => {
    const valToArr = value.split(/,|，/)
    const ids:any = [];
    valToArr.forEach((name: string) => {
        data.forEach((item: any) => {
            if (item.name == name) {
                ids.push(item.id)
            }
        })
    })
    return ids
}

// 属性ID查询属性名称
const idQueryName = (value: any, data: any[]) => {
    const names:any = [];
    value.forEach((id: string) => {
        data.forEach((item: any) => {
            if (item.id == id) {
                names.push(item.name)
            }
        })
    })
    return names
}

// 点击属性回显商品简介
const feedBackInfo = () => {
    const infoArr: any = [];
    const titleArr: any = [];
    // 区服
    const gameAreItem = sysgameList.value.filter((item: any) => item.id == dataForm.gameAre)[0];
    infoArr.push(`服务器：${gameAreItem ? gameAreItem.title : ''}`);
    if (sysgameChildrenList.value.length > 0) {
        const serverItem = sysgameChildrenList.value.filter((item: any) => item.id == dataForm.server)[0];
        infoArr.push(`系统区服：${serverItem ? serverItem.title : ''}`);
    } 
    // 属性
    dataForm.attributesList.forEach((item: any) => {
        if (item.type == 1) {
            const names = idQueryName([item.attributeIds], item.children).join('');
            infoArr.push(`${item.name}：${names}`);
        }
        if (item.type == 2) {
            const names = idQueryName(item.attributeIds, item.children).join('，');
            infoArr.push(`${item.name}：${names}`);
        }
        if (item.type == 3) {
            infoArr.push(`${item.name}：${item.attributeText}`);
        }
    })
    dataForm.info = infoArr.join('\n');

    // 拼接商品标题
    // 获取文本类型属性
    const textArr = dataForm.attributesList.filter((item: any) => item.type == 3 && item.isTitle == 0)
    if (textArr.length > 0) {
        textArr.forEach((item: any) => { 
            if (item.attributeText) {
                titleArr.push(`${item.name}${item.attributeText}`);
            }
        })
    }
    
    // 获取单选类型属性
    const radioArr = dataForm.attributesList.filter((item: any) => item.type == 1 && item.isTitle == 0)
    if (radioArr.length > 0) { 
        radioArr.forEach((item: any) => { 
            const names = idQueryName([item.attributeIds], item.children).join('');
            if (names) {
                titleArr.push(`【${names}】`);
            }
        })
    }
    
    // 获取多选类型属性
    const checkboxArr = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0)
    if (radioArr.length > 0) { 
        checkboxArr.forEach((item: any) => {
            if (item.attributeIds.length > 0) {
                titleArr.push(`${item.name}${item.attributeIds.length}`);
            }
        })
    }
    dataForm.title = titleArr.join(' ');
}

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = (status:any) => {
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }

        if (!isWrite.value) {
            ElMessage.warning('请完成商品信息的填写！')
            return false;
        }
        const params:any = JSON.parse(JSON.stringify(dataForm));
        // 处理单选属性改为数组
        params.attributesList.map((item: any) => { 
            if (item.type == 1) {
                item.attributeIds = item.attributeIds ? [item.attributeIds] : []
            }
            if (item.type == 2) {
                item.attributeIds = item.attributeIds ? item.attributeIds : []
            }
            if (item.type == 3) { 
                item.attributeIds = []
            }
        });
        // 处理游戏主图改为字符串
        params.log = params.log.join('');
        params.status = status;

        delete params.id;

        console.log('====== 审核保存 =======',params)

        btnLoading.value = true;
         baseService.post("/shop/shopaudit/add", params).then((res) => {
            ElMessage.success({
                message: t("prompt.success"),
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        }).finally(() => {
            btnLoading.value = false;
        })
    });
};

// 商品详情表单校验
const dataFormDetailSubmitHandle = () => {
    dataFormDetailRef.value.validate((valid: boolean) => {
        if (!valid) {
            ElMessage.warning('请将商品详情填写完整！')
            return false;
        }
        isWrite.value = true;
        pageShow.value = false;
    });
}

// 表单初始化
const init = (gameId: string, id: any) => {
    visible.value = true;
    dataForm.gameId = gameId;
    dataForm.id = id;
    dataForm.auditId = id;
    if (dataForm.gameId) {
        // getSysgame();
        getAttribute();
    }
    
    // // 重置表单数据
    // if (dataFormRef.value) {
    //     dataFormRef.value.resetFields();
    // }
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getInfo = (id: number) => {
    requestLoading.value = true;
    baseService.get("/shop/shopaudit/" + id).then((res) => {
        
        const data = res.data.shopAuditDTO;
        // 是否填写回显
        isWrite.value = data.topped ? true : false ;
        // 处理主图回显
        data.log = data.log ? [data.log] : [];
        // 处理详情图回显
        data.images = data.images ? JSON.parse(data.images) : [];
        dataForm.imagesList = data.images;
        // 处理包赔标签
        data.compensation = data.compensation ? data.compensation.toString() : '';
        // 处理议价标签
        data.bargain = data.bargain.toString();
        // 处理顶级账号标签回显
        data.topped = data.topped ? data.topped.toString() : '';
        // 动态商品信息处理
        data.accountInfo = JSON.parse(data.accountInfo);
        data.basicInfo = JSON.parse(data.basicInfo);

        // 处理服务器回显
        if (sysgameList.value) {
            const list = sysgameList.value
            for (let i = 0; i < list.length; i++){
                if (list[i].id == data.server) {
                    dataForm.gameAre = list[i].id;
                    break;
                } else {
                    for (let j = 0; j < list[i].children.length; j++){
                        if (list[i].children[j].id == data.server) {
                            sysgameChildrenList.value = list[i].children;
                            dataForm.gameAre = list[i].id;
                            dataForm.server = list[i].children[j].id;
                            break;
                        }
                    }
                }
            }
        }
        // 处理属性回显
        if(data.attributesList){
            data.attributesList.forEach((ele: any) => {
                dataForm.attributesList.forEach((item: any) => {
                    if (item.typeId == ele.typeId) {
                        if (item.type == 1) {
                            item.attributeIds = ele.attributeIds.join('');
                        }
                        if (item.type == 2) {
                            item.attributeIds = ele.attributeIds
                        }
                        if (item.type == 3) {
                            item.attributeText = ele.attributeText
                        }
                        Object.assign(ele,item);
                    }
                })
            })
        }
        
        Object.assign(dataForm, data);

        // console.log(dataForm , '====== shuxing ======');
    }).finally(() => {
        requestLoading.value = false;
    })
};


defineExpose({
    init
});
</script>

<style lang='less' scoped>
.shop_page{
    background-color: #F0F2F5;
    // border-radius: 8px;
    padding: 12px;
}
.games{
    padding: 20px 6px 10px 6px;
    .gamesList{
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        list-style: none;
        box-sizing: border-box;
        overflow: hidden;
        .gamesItem{
            min-width: 120px;
            height: 28px;
            font-size: 12px;
            line-height: 28px;
            background-color: #fff;
            border: 1px solid var(--el-color-primary);
            border-radius: 4px;
            text-align: center;
            margin: 0px 10px 10px 10px;
            cursor: pointer;
            -ms-flex-negative: 0;
            flex-shrink: 0;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        .active {
            background-color: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: #fff;
        }
    }
    .icons{
        padding: 6px 10px 10px 10px;
        .el-icon{
            font-size: 16px;
        }
    }
}

.shop_page_basic{
    background-color: #FFF;
    border-radius: 8px;
}
.shop_page_details{
    .shop_page_details_card{
        background-color: #FFF;
        border-radius: 8px;
        padding: 12px;
    }
}
.mytag {
    background: #fcf2bb;
    border-radius: 20px;
    color: #f6930a;
    display: inline-block;
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    max-width: 100%;
    overflow: hidden;
    padding: 0 15px 0 30px;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap;

    .topImg {
        width: 25px;
        height: 25px;
        position: absolute;
        top: 0;
        left: 3px;
    }
}
.flod_tab{
    display: flex;
    align-items: flex-start;
    width: 100%;
    .flod_tab_cont{
        flex: 1;
        overflow: hidden;
        :deep(.el-radio){
            margin-right: 20px;
        }
        :deep(.el-radio.is-bordered.el-radio--small){
            margin-bottom: 10px;
        }
    }
}
.oper {
    width: 120px;
    height: 28px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .resetFont {
        cursor: pointer;
        margin-right: 6px
    }

    .foledBtn {
        float: right;
        padding: 12px 10px;
        margin-right: 0;
    }
}
.shop_info{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    .shop_info_left{
        display: flex;
        align-items: center;
        .tip{
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 4px;
        }
    }
    .shop_info_right{
        display: flex;
        align-items: center;
    }
}
.proportion{
    :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content){
        width: 220px;
    }
}
</style>
<style lang='less'>
.drawer_shop{
    .el-drawer__header{
        margin-bottom: 0px;
    }
    .drawer_title{
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .el-drawer__body{
        padding: 0px;
    }
}
</style>