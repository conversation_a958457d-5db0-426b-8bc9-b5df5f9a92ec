<template>
  <el-dialog width="800px" v-model="dialogVisible" class="im-custom-dialog" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #header="{ close, titleId, titleClass }">
      <div class="im-custom-dialog-header">
        <p class="dialog-title">创建销售订单</p>
      </div>
    </template>

    <div class="create-order-dialog-content flx">
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules" class="w-100">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="选择商品" class="w-100 is-required">
              <el-row class="w-100">
                <el-col :span="12">
                  <div class="flx-justify-between">
                    <el-input v-model="keywords" placeholder="请输入商品编码"></el-input>
                    <el-button class="ml-8" :loading="searchLoading" @click="search">搜索</el-button>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="shop-list" :class="{ launched: !isFold }" v-if="currentShop && currentShop.id">
                    <div class="shop-item pointer flx" @click="isFold = !isFold">
                      <el-image class="shop-img" :src="currentShop.log" fit="cover"></el-image>
                      <div class="shop-info flx-column">
                        <div class="title mle" v-html="currentShop.title"></div>
                        <div class="price">￥{{ currentShop.price }}</div>
                      </div>
                      <div class="arrow" v-if="shopList && shopList.length">
                        <el-icon><ArrowDown /></el-icon>
                      </div>
                    </div>

                    <el-radio-group v-if="shopList && shopList.length" v-model="currentShopId">
                      <el-radio :value="item.id" class="shop-item flx" v-for="(item, index) in shopList" :key="index" @click="selectShop(item)">
                        <div class="flx">
                          <el-image class="shop-img" :src="item.log" fit="cover"></el-image>
                          <div class="shop-info flx-column">
                            <div class="title mle">{{ item.title }}</div>
                            <div class="price">￥{{ item.price }}</div>
                          </div>
                          <div class="arrow"></div>
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择包赔权益">
              <el-select v-model="dataForm.bp1" @change="bpSingleChange">
                <el-option v-for="(item, index) in bpSingleList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择增值包赔">
              <el-select v-model="dataForm.bp2" multiple @change="bpMulChange">
                <el-option v-for="(item, index) in bpMulList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="买方手机号" prop="phone">
              <el-input v-model="dataForm.phone" disabled placeholder="请输入买方手机号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成交价" prop="transactionPrice">
              <el-input v-model="dataForm.transactionPrice" disabled placeholder="请输入成交价"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="flx-justify-end automatic-reply-footer">
      <el-button :loading="btnLoading" @click="handleCancel">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store/index";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const userStore = useAppStore();
const imStore = useImStore();
const emits = defineEmits(["sendOrder"]);

const dialogVisible = ref(false);

const userInfo = ref(<any>{});

// list  商品消息类型  的消息列表
const init = (list: any, user: any) => {
  userInfo.value = user;
  console.log(userInfo.value, "===== userInfo.value ======");
  let orderIds: any = [];
  let data = list.filter((item: any) => item.messageType == "CU:shop");
  data.map((item: any) => {
    if (orderIds.includes(item.id)) return;
    orderIds.push(item.id);
    shopList.value.push(item.content);
  });
  if (shopList.value && shopList.value.length) {
    currentShop.value = shopList.value[0] || {};
    currentShopId.value = currentShop.value.id;
    getTotalPrice();
  }
  if (user) dataForm.value.phone = user.mobile;
  dialogVisible.value = true;
  getPompensationList();
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

const dataForm = ref({
  userId: "",
  shopId: "",
  bp1: "",
  bp2: "",
  transactionPrice: "",
  phone: ""
});

const rules = {
  transactionPrice: [{ required: true, message: "请输入成交价", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号", trigger: "blur" }]
};

// 搜索商品
const keywords = ref("");
const searchLoading = ref(false);
const shopList = ref([]);
const search = () => {
  if (keywords) {
    searchLoading.value = true;
    baseService
      .post("/shop/shop/search2", { search: keywords.value, queryType: 1, page: 1, size: 1 })
      .then((res) => {
        currentShop.value = res.data && res.data.list && res.data.list.length > 0 ? res.data.list[0] : {};
        currentShopId.value = currentShop.value.id;
        dataForm.value.shopId = currentShop.value.id;
        dataForm.value.transactionPrice = currentShop.value.price;
      })
      .finally(() => {
        searchLoading.value = false;
      });
  }
};

// 选择商品
const selectShop = (data: any) => {
  currentShop.value = data;
  dataForm.value.transactionPrice = data.price;
};

// 选择包赔权益
const bpSinglePrice = ref(0);
const bpSingleChange = () => {
  if (!dataForm.value.bp1) {
    bpSinglePrice.value = 0;
    getTotalPrice();
    return;
  }
  let data = bpSingleList.value.find((item) => item.id == dataForm.value.bp1);
  let rate = data.rate / 100;
  data.price = (rate * currentShop.value.price).toFixed(2);
  if (data.price > data.maxIndemnity) data.price = data.maxIndemnity;
  bpSinglePrice.value = Number(data.price);
  getTotalPrice();
};

const getTotalPrice = () => {
  dataForm.value.transactionPrice = (currentShop.value.price + bpMulPrice.value + bpSinglePrice.value).toFixed(2);
};

const bpMulPrice = ref(0);
const bpMulChange = () => {
  bpMulPrice.value = 0;
  if (dataForm.value.bp2 && dataForm.value.bp2.length) {
    bpMulList.value.map((item: any) => {
      if (dataForm.value.bp2.includes(item.id)) {
        let rate = item.rate / 100;
        item.price = (rate * currentShop.value.price).toFixed(2);
        if (item.price > item.maxIndemnity) item.price = item.maxIndemnity;
        bpMulPrice.value += Number(item.price);
      }
    });
  }
  getTotalPrice();
};

// 是否折叠
const isFold = ref(true);
// 已选中的商品id
const currentShopId = ref("");
const currentShop = ref({});

const bpSingleList = ref(<any>[]);
const bpMulList = ref(<any>[]);
const getPompensationList = () => {
  baseService.get("/shop/guarantee/page").then((res) => {
    // 包赔
    bpSingleList.value = res.data.list.filter((item: any) => item.type == "1");
    // 增值
    bpMulList.value = res.data.list.filter((item: any) => item.type == "2");
  });
};

// 提交
const btnLoading = ref(false);
const formRef = ref();
const handleSubmit = () => {
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    dataForm.value.shopId = currentShopId.value;
    if (!dataForm.value.shopId) {
      return ElMessage.warning("请选择商品");
    }
    let params = { ...dataForm.value };
    let guaranteeIds: any = dataForm.value.bp2 ? JSON.parse(JSON.stringify(dataForm.value.bp2)) : [];
    if (dataForm.value.bp1) guaranteeIds.push(dataForm.value.bp1);
    btnLoading.value = true;
    baseService
      .post("/sale/create", {
        shopId: params.shopId,
        userId: userInfo.value.id,
        transactionPrice: params.transactionPrice,
        guaranteeIds: guaranteeIds
      })
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("创建成功！");
          dialogVisible.value = false;

          let data = res.data;

          emits("sendOrder", {
            id: data.saleOrder.id,
            createDate: data.saleOrder.createDate,
            dealAmount: data.saleOrder.dealAmount,
            gameName: data.shopVo.gameName,
            shopVo: data.shopVo,
            saleOrder: data.saleOrder,
            // 销售订单类型
            orderType: "sales"
          });
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>

<style lang="scss">
.create-order-dialog-content {
  padding: 16px;

  .shop-list {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 8px 8px 0 8px;
    margin-top: 8px;
    height: 98px;
    overflow: hidden;
    transition: all 0.3s;

    &.launched {
      height: auto;

      .arrow {
        transform: rotate(180deg);
      }
    }

    .shop-item {
      align-items: center;
      padding-bottom: 8px;
    }

    .shop-img {
      width: 142px;
      height: 80px;
      border-radius: 4px;
    }

    .shop-info {
      flex: 1;
      padding-left: 8px;
      justify-content: space-between;

      .title {
        height: 48px;
        line-height: 24px;
        color: #171a1d;
      }

      .price {
        color: #f44a29;
      }
    }

    .arrow {
      // width: 50px;
      text-align: right;
      transition: all 0.3s;
    }

    .el-radio-group {
      display: inline-block;
      width: 100%;

      .el-radio {
        width: 100%;
        overflow: hidden;
        height: auto;
        display: flex;
        flex-direction: row-reverse;
      }

      .el-radio__label {
        flex: 1;
        display: flex;
        padding-left: 0;
      }
    }
  }
}

.automatic-reply-footer {
  padding: 0 16px 16px 0;
}
</style>
