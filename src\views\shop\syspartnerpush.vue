<template>
  <div class="appraise_page">
    <div class="appraise_card">
      <NyDropdownMenu v-model="dataForm.selectId" :list="editableTabs" labelKey="title" valueKey="id" :isSolid="true" :isBorder="false" @change="handleTabsChange"></NyDropdownMenu>
      <div>
        <el-button type="primary" @click="copyExternal" v-if="NetworkName != '默认'">复制网址</el-button>
        <el-button type="warning" @click="addOrEditHandle('edit')" v-if="NetworkName != '默认'">编辑</el-button>
        <el-button type="success" @click="addOrEditHandle('add')">新增选号网</el-button>
      </div>
    </div>
    <div class="appraise_center">
      <el-scrollbar class="center_left">
        <div class="menu_item" :class="{ active: item.id == dataForm.gameId }" v-for="(item, index) in gamesList" :key="index" @click="flodTabChange(item.id)">
          <img :src="item.thumbnail" class="gameLogo" />
          <div class="gameInfo">
            <span class="title">{{ item.title }}</span>
            <div class="count">
              商品数量：<span>{{ item.shopQuantity1 }}</span>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <div class="center_right">
        <el-table :data="tableData" border style="width: 100%; height: calc(100vh - 250px)" v-loading="tableLoading" cell-class-name="ch-56" row-key="id" ref="tableRef" class="elTable">
          <el-table-column prop="companyName" label="商户名称" align="center" min-width="90">
            <template #default="{ row }">
              <div class="tableCompanyName">
                <!-- <el-icon v-if="!row.isMyself" color="var(--el-color-primary)" class="move" style="cursor: pointer">
                  <Rank />
                </el-icon>
                <div v-else style="width: 1em; height: 1em"></div> -->
                <span>{{ row.companyName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sort" align="center" min-width="100">
            <template #header>
              <div class="flx-align-center">
                <span style="margin-right: 4px">商品展示排序</span>
                <el-tooltip effect="dark" content="数值越小，排序越靠前" placement="top-end">
                  <el-icon color="#909399" size="14">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>

            <template #default="{ row }">
              <span v-if="row.isMyself">自己默认置顶</span>
              <el-input v-model="row.sort" v-else placeholder="请输入排序" @blur="saveFn(row)" />
            </template>
          </el-table-column>
          <el-table-column prop="pricePercent" align="center" min-width="100">
            <template #header>
              <div class="flx-align-center">
                <span style="margin-right: 4px">价格比率</span>
                <el-tooltip effect="dark" content="折扣9折（输入：90）；原价（输入：100）；加价10%（输入：110）" placement="top-end">
                  <el-icon color="#909399" size="14">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="{ row }">
              <div style="display: flex; align-items: center">
                <el-input v-model="row.pricePercent" placeholder="请输入价格比率" @blur="saveFn(row)">
                  <template #append>%</template>
                </el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="商品数量" align="center" min-width="100" />
          <el-table-column prop="isShow" label="选号网显示" align="center" min-width="100">
            <template #default="{ row }">
              <el-switch v-model="row.isShow" :active-value="1" :inactive-value="0" :loading="row.loading" @change="switchFn(row)" />
            </template>
          </el-table-column>
          <el-table-column prop="updateDate" label="更新时间" align="center" min-width="100">
            <template #default="{ row }">
              {{ row.updateDate ? formatTimeStamp(row.updateDate) : "-" }}
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </div>
    </div>
  </div>
  <!-- 编辑网址 -->
  <el-dialog v-model="dialogVisible" :title="dialogInputValue.id ? '编辑' : '新增'" width="500">
    <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
      <el-descriptions-item>
        <template #label
          ><span>选号网名称<span style="color: red">*</span></span></template
        >
        <el-input v-model="dialogInputValue.title" placeholder="请输入"></el-input>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <div class="dialog-footer">
        <div>
          <el-button type="danger" plain v-if="dialogInputValue.id" @click="handleTabsRemove(dialogInputValue.id)">删除</el-button>
        </div>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="addOrEditSubmitLoading" @click="addOrEditSubmit(dialogInputValue.id ? 'edit' : 'add')">保存</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { useSettingStore } from "@/store/setting";
import useClipboard from "vue-clipboard3";
import Sortable from "sortablejs";
const { toClipboard } = useClipboard();

const settingStore = useSettingStore();
const gamesList = ref(<any>[]); // 游戏列表
const PartnerList = ref(<any>[]); // 商户列表
const tableRef = ref();
const tableData = ref(<any>[]); // 页面表格
const dataForm = reactive({
  gameId: null, // 游戏ID
  partnerId: null, // 商户ID
  type: "2",
  selectId: ""
});
const editableTabs = ref(<any>[]); // 页签列表

const NetworkName = ref(""); // 选好网名称

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    dataForm.gameId = res.data ? res.data[0].id : "";
    gamesList.value = res.data;
  });
};

// 表格列表
const tableLoading = ref(false);
const getDataList = () => {
  // tableData.value = [];
  tableLoading.value = true;
  baseService
    .post("/shop/partnerpush/page", dataForm)
    .then((res) => {
      // let tenantIds = res.data.map((item) => item.tenantId);
      // PartnerList.value.map((item) => {
      //   if (!tenantIds.includes(item.tenantId)) {
      //     res.data.push(item);
      //   }
      // });

      tableData.value = res.data.map((item) => {
        return {
          ...item,
          pricePercent: item.pricePercent ? Math.round(item.pricePercent * 100) : 100
        };
      });
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 保存
const saveFn = (row: any) => {
  let params = JSON.parse(JSON.stringify(row));
  params.gameId = dataForm.gameId;
  params.sort = Number(params.sort);
  params.selectId = params.selectId ? params.selectId : dataForm.selectId;
  params.companyType = params.companyType ? params.companyType : "2";
  // params.expiredTime = Number(params.expiredTime);
  params.pricePercent = Number(params.pricePercent) / 100;
  baseService.post("/shop/partnerpush", params).then((res) => {
    ElMessage.success("保存成功！");
    getDataList();
  });
};

// 开关
const switchFn = (row: any) => {
  let params = JSON.parse(JSON.stringify(row));
  params.gameId = dataForm.gameId;
  params.sort = Number(params.sort);
  // params.expiredTime = Number(params.expiredTime);
  params.selectId = params.selectId ? params.selectId : dataForm.selectId;
  params.companyType = params.companyType ? params.companyType : "2";
  params.pricePercent = Number(params.pricePercent);
  params.pricePercent = Number(params.pricePercent) / 100;
  baseService.post("/shop/partnerpush", params).then((res) => {
    ElMessage.success("保存成功！");
    getDataList();
  });
};

const getTenantList = () => {
  baseService.get("/sys/tenant/list").then((res) => {
    PartnerList.value = res.data.map((item) => {
      return {
        ...item,
        pricePercent: 1,
        isShow: 0,
        id: "",
        tenantId: item.tenantCode
      };
    });
  });
};

// 游戏切换点击事件
const flodTabChange = (value: any) => {
  dataForm.gameId = value;
  getDataList();
};

// 切换网址
const handleTabsChange = () => {
  NetworkName.value = editableTabs.value.find((item: any) => item.id == dataForm.selectId).title;
  getDataList();
};

// 表格拖拽排序
const dragSort = () => {
  const tbody = document.querySelector(`.elTable .el-table__body-wrapper tbody`) as HTMLElement;
  Sortable.create(tbody, {
    handle: ".move",
    animation: 300,
    onEnd({ newIndex, oldIndex }) {
      if (tableData.value[oldIndex].isMyself || tableData.value[newIndex].isMyself) {
        tableData.value = [];
        getDataList();
      } else {
        baseService
          .get("/shop/partnerpush/sort", {
            targetId: tableData.value[oldIndex].id,
            sourceId: tableData.value[newIndex].id
          })
          .then((res) => {
            if (res.code == 0) {
              // getDataList();
            }
          })
          .finally(() => {
            tableData.value = [];
            getDataList();
          });
      }
    }
  });
};

onMounted(() => {
  Promise.all([getTenantList(), getGamesList(), getSysShopTabs()]).then(() => {
    setTimeout(() => {
      getDataList();
    }, 1000);
  });
  dragSort();
});

// 获取页签列表
const getSysShopTabs = () => {
  baseService.get("/shop/sysselectshop/page").then((res) => {
    if (!res.data || !res.data.length) return (editableTabs.value = []);
    dataForm.selectId = dataForm.selectId ? dataForm.selectId : res.data[0].id;
    editableTabs.value = res.data;
    NetworkName.value = editableTabs.value.find((item: any) => item.id == dataForm.selectId).title;
    if (dataForm.gameId) {
      getDataList();
    }
    // getSysShopList();
  });
};

// 删除标签页
const handleTabsRemove = (e: any) => {
  ElMessageBox.confirm("确定删除该数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.delete("/shop/sysselectshop", [e]).then((res) => {
      ElMessage.success("删除成功！");
      dialogVisible.value = false;
      dataForm.selectId = "";
      getSysShopTabs();
    });
  });
};

// 新增/编辑页签
const dialogVisible = ref(false);
const dialogInputValue = ref({
  id: "",
  title: ""
});
const addOrEditHandle = (type: any) => {
  dialogVisible.value = true;
  dialogInputValue.value.id = "";
  dialogInputValue.value.title = "";
  if (type == "edit") {
    Object.assign(
      dialogInputValue.value,
      editableTabs.value.find((item: any) => item.id == dataForm.selectId)
    );
  }
};

const addOrEditSubmitLoading = ref(false);
const addOrEditSubmit = (type: string) => {
  if (!dialogInputValue.value.title) {
    ElMessage.warning("请输入选号网名称");
    return;
  }
  addOrEditSubmitLoading.value = true;
  if (type == "add") {
    baseService
      .post("/shop/sysselectshop", { title: dialogInputValue.value.title })
      .then((res) => {
        ElMessage.success("新增成功！");
        dialogVisible.value = false;
        getSysShopTabs();
      })
      .finally(() => {
        addOrEditSubmitLoading.value = false;
      });
  } else {
    baseService
      .put("/shop/sysselectshop", { id: dialogInputValue.value.id, title: dialogInputValue.value.title })
      .then((res) => {
        ElMessage.success("修改成功！");
        dialogVisible.value = false;
        getSysShopTabs();
      })
      .finally(() => {
        addOrEditSubmitLoading.value = false;
      });
  }
};

// 复制外部选号网址
const copyExternal = () => {
  const url = editableTabs.value.filter((item: any) => item.id == dataForm.selectId)[0].url;
  const openUrl = `${settingStore.info.websiteUrl}selectShop?url=${url}`;
  copyInfo(openUrl);
};

// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};
</script>

<style lang="less" scoped>
.appraise_page {
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  margin: 12px 24px 24px 24px;
  height: calc(100vh - 180px);

  .appraise_card {
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px 4px 0px 0px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .appraise_center {
    height: calc(100vh - 238px);
    // border: 1px solid red;
    padding: 12px;
    display: flex;
    gap: 12px;

    .center_left {
      width: 256px;

      .menu_item {
        width: 100%;
        padding: 8px;
        border-radius: 8px;
        display: flex;
        gap: 8px;
        cursor: pointer;

        .gameLogo {
          width: 64px;
          height: 64px;
        }

        .gameInfo {
          height: 64px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          .title {
            font-weight: 500;
            font-size: 16px;
            color: #1d2129;
            text-align: center;
          }

          .count {
            font-weight: 400;
            font-size: 14px;
            color: #4e5969;
            text-align: center;

            span {
              color: var(--el-color-primary);
            }
          }
        }

        &:hover {
          background-color: var(--el-color-primary-light-9);

          :deep(.title) {
            color: var(--el-color-primary) !important;
          }
        }
      }

      .active {
        background-color: var(--el-color-primary-light-9);

        :deep(.title) {
          color: var(--el-color-primary) !important;
        }
      }
    }

    .center_right {
      flex: 1;
      // border: 1px solid blue;
    }
  }
}

.tableCompanyName {
  width: 100%;
  display: flex;
  align-items: center;
  // cursor: pointer;

  span {
    flex: 1;
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
