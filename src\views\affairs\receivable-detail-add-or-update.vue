<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="结算往来单位" prop="settlementUnit">
            <el-input v-model="dataForm.settlementUnit" placeholder="请输入结算往来单位"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单据日期" prop="documentDate">
            <el-date-picker 
              v-model="dataForm.documentDate" 
              type="date" 
              placeholder="请选择单据日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单据类型" prop="documentType">
            <el-select v-model="dataForm.documentType" placeholder="请选择单据类型" style="width: 100%;">
              <el-option label="销售单" value="1"></el-option>
              <el-option label="服务单" value="2"></el-option>
              <el-option label="其他" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单据编号" prop="documentNumber">
            <el-input v-model="dataForm.documentNumber" placeholder="请输入单据编号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="dataForm.businessType" placeholder="请选择业务类型" style="width: 100%;">
              <el-option label="应收款" value="receivable"></el-option>
              <el-option label="预收款" value="advance"></el-option>
              <el-option label="其他" value="other"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="本期金额" prop="amount">
            <el-input-number v-model="dataForm.amount" :precision="2" :min="0" style="width: 100%;" placeholder="请输入金额"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="期末余额" prop="finalBalance">
            <el-input-number v-model="dataForm.finalBalance" :precision="2" :min="0" style="width: 100%;" placeholder="请输入期末余额"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="businessCode">
            <el-input v-model="dataForm.businessCode" placeholder="请输入业务员"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在部门" prop="department">
            <el-select v-model="dataForm.department" placeholder="请选择部门" style="width: 100%;">
              <el-option label="销售部" value="sales"></el-option>
              <el-option label="市场部" value="marketing"></el-option>
              <el-option label="财务部" value="finance"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="游戏名称" prop="gameName">
            <el-input v-model="dataForm.gameName" placeholder="请输入游戏名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="游戏编号" prop="gameNumber">
            <el-input v-model="dataForm.gameNumber" placeholder="请输入游戏编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专属ID" prop="exclusiveId">
            <el-input v-model="dataForm.exclusiveId" placeholder="请输入专属ID"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" type="textarea" placeholder="请输入备注"></el-input>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  settlementUnit: "",
  documentDate: "",
  documentType: "",
  documentNumber: "",
  businessType: "",
  amount: 0,
  finalBalance: 0,
  businessCode: "",
  department: "",
  gameName: "",
  gameNumber: "",
  exclusiveId: "",
  remark: ""
});

const rules = ref({
  settlementUnit: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  documentType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
  documentNumber: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  amount: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  
  // 重置表单
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  
  Object.assign(dataForm, {
    id: "",
    settlementUnit: "",
    documentDate: "",
    documentType: "",
    documentNumber: "",
    businessType: "",
    amount: 0,
    finalBalance: 0,
    businessCode: "",
    department: "",
    gameName: "",
    gameNumber: "",
    exclusiveId: "",
    remark: ""
  });

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/finance/receivable/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    const request = !dataForm.id ? 
      baseService.post("/finance/receivable", dataForm) : 
      baseService.put("/finance/receivable", dataForm);
      
    request.then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>