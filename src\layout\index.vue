<script lang="ts">
import { EMitt, ESidebarLayoutEnum, EThemeSetting } from "@/constants/enum";
import emits from "@/utils/emits";
import { getThemeConfigCache, getThemeConfigCacheByKey, getThemeConfigToClass } from "@/utils/theme";
import { getValueByKeys } from "@/utils/utils";
import { useMediaQuery } from "@vueuse/core";
import { computed, defineComponent, onMounted, onUnmounted, reactive, ref, watch } from "vue";
import { RouteRecordRaw, useRouter, useRoute } from "vue-router";
import { useAppStore } from "@/store";
import { useImStore } from "@/store/im";
import BaseHeader from "./header/base-header.vue";
import BaseSidebar from "./sidebar/base-sidebar.vue";
import MobileSidebar from "./sidebar/mobile-sidebar.vue";
import BaseView from "./view/base-view.vue";

/**
 * 多标签页布局
 */
export default defineComponent({
  name: "Layout",
  components: { BaseView, BaseHeader, BaseSidebar, MobileSidebar },
  setup() {
    const isMobile = useMediaQuery("(max-width: 768px)");
    const themeCache = getThemeConfigCache();
    const sidebarLayoutCache = getThemeConfigCacheByKey(EThemeSetting.NavLayout, themeCache);
    const router = useRouter();
    const route = useRoute();
    const store = useAppStore();
    const state = reactive({
      isShowNav: sidebarLayoutCache !== ESidebarLayoutEnum.Top,
      sidebarLayout: sidebarLayoutCache,
      themeClass: getThemeConfigToClass(themeCache),
      loading: false,
      mixLayoutRoutes: router.options.routes.find((x: RouteRecordRaw) => x.path === "/")?.children ?? ([] as RouteRecordRaw[])
    });
    const containerClassNames = computed(() =>
      Object.values(state.themeClass)
        .concat(isMobile.value ? ["ui-mobile"] : [])
        .join(" ")
    );
    emits.on(EMitt.OnSelectHeaderNavMenusByMixNav, (path) => {
      state.mixLayoutRoutes = store.state.routes.find((x: RouteRecordRaw) => x.path === path)?.children ?? [];
    });
    emits.on(EMitt.OnSetTheme, ([type, value]) => {
      state.themeClass[type] = "ui-" + value;
    });
    emits.on(EMitt.OnSetNavLayout, (vl) => {
      state.sidebarLayout = vl;
      state.isShowNav = vl !== ESidebarLayoutEnum.Top;
      if (vl === ESidebarLayoutEnum.Mix) {
        const currRoute = getValueByKeys(getValueByKeys(router.currentRoute.value.meta, "matched", [])[0], "path", "");
        state.mixLayoutRoutes = store.state.routes.find((x: RouteRecordRaw) => x.path === currRoute)?.children ?? [];
      }
    });
    emits.on(EMitt.OnLoading, (vl) => {
      state.loading = vl;
    });

    // 菜单激活回调
    const onSelect = (MenuSelectEvent: any) => {
      // console.log(MenuSelectEvent,store.state.closedTabs,'===== MenuSelectEvent ====')
    };
    // NOTE: 配置需要缩放页面， 设置缩放属性
    const zoomPage = ["/home", "/desk/index", "/dataAnalysis/index"];
    const layoutZoom = ref("");
    const setZoomElement = () => {
      if (zoomPage.includes(route.path) || route.path == "/") {
        var screenWidth = window.innerWidth;
        let scarl = screenWidth / 1920;
        layoutZoom.value = `zoom: ${scarl}`;
      }
    };
    onUnmounted(() => {
      window.removeEventListener("resize", setZoomElement);
    });
    onMounted(() => {
      window.addEventListener("resize", setZoomElement); // 监听窗口大小变化
    });
    watch(
      () => [route.path, route.query, route.fullPath],
      ([path, query, fullPath]) => {
        layoutZoom.value = "";
        setZoomElement();
      }
    );
    return { state, ESidebarLayoutEnum, containerClassNames, onSelect, store, layoutZoom };
  }
});
</script>
<template>
  <el-container style="height: 100%; overflow: hidden">
    <el-header class="rr-header" height="70px">
      <base-header></base-header>
    </el-header>
    <el-container :class="`rr ${containerClassNames}`" v-loading="state.loading" element-loading-background="#0000" element-loading-lock="true" element-loading-custom-class="rr-loading">
      <el-aside class="rr-sidebar hidden-xs-only" width="255px">
        <base-sidebar :router="true" mode="vertical" :is-mobile="false" :onSelect="onSelect"></base-sidebar>
      </el-aside>
      <el-main class="rr-view" :style="layoutZoom">
        <base-view v-if="!store.state.isLoadingPage"></base-view>
        <div v-else style="padding: 24px; background: #fff; margin: 16px; border-radius: 8px; height: 100%; overflow: hidden">
          <el-skeleton animated style="margin-bottom: 30px">
            <template #template>
              <el-skeleton-item variant="h3" style="width: 15%" />
              <el-skeleton-item variant="h3" style="width: 30%; margin-left: 12px" />
              <div style="margin-top: 30px; display: flex; justify-content: space-between">
                <el-skeleton-item variant="h3" style="width: 40%" />
              </div>
            </template>
          </el-skeleton>
          <el-skeleton animated>
            <template #template>
              <el-skeleton-item style="margin-bottom: 16px" variant="h3" v-for="item in 14" :key="item" />
              <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                <el-skeleton-item variant="h3" style="width: 40%" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </el-main>
    </el-container>
  </el-container>
  <!-- <el-container :class="`rr ${containerClassNames}`" v-loading="state.loading" element-loading-background="#0000" element-loading-lock="true" element-loading-custom-class="rr-loading">
    <el-aside v-if="state.isShowNav" class="rr-sidebar hidden-xs-only" width="255px">
      <base-sidebar v-if="state.sidebarLayout === ESidebarLayoutEnum.Left" :router="true" mode="vertical" :is-mobile="false"></base-sidebar>
      <base-sidebar v-else :menus="state.mixLayoutRoutes" :router="true" mode="vertical" :is-mobile="false"></base-sidebar>
    </el-aside>
    <el-container class="rr-body">
      <el-header class="rr-header" height="50px">
        <base-header></base-header>
      </el-header>
      <div class="rr-sidebar rr-sidebar-mobile hidden-sm-and-up show-xs-only">
        <mobile-sidebar></mobile-sidebar>
      </div>
      <el-container class="rr-view-container">
        <el-main class="rr-view">
          <base-view></base-view>
        </el-main>
      </el-container>
    </el-container>
  </el-container> -->
</template>
