<template>
    <div class="im-group-avatar" :class="{ group: info?.userVos && info.type != 1 && info.type != 4 }">
        <div v-if="info.type == 1 || info.type == 4" class="avatar">
            <img v-if="info.orderFlow == 7" src="../../../assets/images/im_group_cancel.png">
            <img v-else-if="info.orderFlow == 6" src="../../../assets/images/im_group_completed.png">
            <img v-else src="../../../assets/images/im_group_changebind.png">
        </div>  
        <ul v-else-if="info?.userVos">
            <li v-for="(member, index) in info?.userVos" :key="index">
                <im-avatar class="avatar" :user="member"></im-avatar>
            </li>
        </ul>
        <el-image v-else class="group-img" :src="info?.headUrl"></el-image>
    </div>  
</template>

<script lang="ts" setup>
    import { defineProps } from 'vue';
    import ImAvatar from './ImAvatar.vue';


    defineProps({
        info: Object
    })

</script>

<style lang="scss">
    .im-group-avatar{
        width: 42px;
        height: 42px;
        border-radius: 4px;

        .text-avatar{
            font-size: 14px !important;
        }

        &.group {
            background: #fff;
            border: 1px solid #E4E7ED;
            overflow: hidden;
            
            ul,
            li {
                list-style: none;
                margin: 0;
                padding: 0;
            }

            li {
                outline: 1px solid #fff;
                display: flex;

                .avatar{
                    width: 12px !important;
                    height: 12px !important;
                    border-radius: 2px !important;
                    margin: 1px;
                }
            }

            /* 3个图片 */
            li:first-child:nth-last-child(3),
            li:first-child:nth-last-child(3)~li {
                width: 50%;
                .avatar{
                    width: 18px !important;
                    height: 18px !important;
                    border-radius: 2px !important;
                    margin: 1px;
                }
            }

            li:first-child:nth-last-child(3) {
                margin: auto;
            }

            li:first-child:nth-last-child(3)~li {
                float: left;
            }

            /* 4个图片 */
            li:first-child:nth-last-child(4),
            li:first-child:nth-last-child(4)~li {
                width: 50%;
                float: left;
                .avatar{
                    width: 18px !important;
                    height: 18px !important;
                    border-radius: 2px !important;
                }
            }

            /* 5个或8个图片 */
            li:first-child:nth-last-child(5),
            li:first-child:nth-last-child(5)~li,
            li:first-child:nth-last-child(8),
            li:first-child:nth-last-child(8)~li {
                width: 33%;
                float: left;
            }

            li:first-child:nth-last-child(5),
            li:first-child:nth-last-child(5)+li {
                margin-top: 16%;
            }

            li:first-child:nth-last-child(5),
            li:first-child:nth-last-child(8) {
                margin-left: 15%;
            }

            /* 6个图片 */
            li:first-child:nth-last-child(6),
            li:first-child:nth-last-child(6)~li {
                width: 33%;
                float: left;
            }

            li:first-child:nth-last-child(6),
            li:first-child:nth-last-child(6)+li,
            li:first-child:nth-last-child(6)+li+li {
                margin-top: 16%;
            }

            /* 7个图片 */
            li:first-child:nth-last-child(7),
            li:first-child:nth-last-child(9),
            li:first-child:nth-last-child(7)~li,
            li:first-child:nth-last-child(9)~li {
                width: 33%;
                float: left;
            }

            li:first-child:nth-last-child(7) {
                float: none;
                margin: auto;
            }
        }
    }
</style>