# VITE_APP_API=http://localhost:28080/nyyyds-sub
# VITE_APP_API=http://**************/children
# 演示环境
# VITE_APP_API=https://demo1.nyyyds.com/children
# 演示环境 - 硬件服务器
# VITE_APP_API=http://**************:28080/children
# 史廷威
# VITE_APP_API=http://***********:28080/children
# 韩杨杨
# VITE_APP_API=http://***************:28080/children
# 袁帅
# VITE_APP_API=http://***************:28080/children
# 翁佃峰
# VITE_APP_API=http://**************:28080/children
# 王建
# VITE_APP_API=http://**************3:28080/children
VITE_APP_API=http://**************:28080/children
# 胡永帅
# VITE_APP_API=http://***************:28080/children
# 张程
# VITE_APP_API=http://**************:28080/children

# 乐乘
# VITE_APP_API=http://www.lechengwangyo.com/children


# 阿浪
# VITE_APP_API=http://*************:18080/children

# 米创客
# VITE_APP_API=http://*************:18080/children

#玩趣游
# VITE_APP_API=http://*************:18080/children


# websocket url
VITE_WEBSOCKET_URL = ws://**************/children



NODE_ENV=development
# VITE_APP_SHOP_URL=http://**************:28000
#租户模式 [none：不开启租户 | domain：通过域名区分租户 | code：通过编码区分租户]
VITE_APP_TENANT_MODE=code

# 1商家平台 2开放平台 3授权API
VITE_PLATFORM_MODE=1
