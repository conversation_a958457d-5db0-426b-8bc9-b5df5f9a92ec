<template>
    <div class="mod-job__schedule" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:schedule:save')" type="primary"
                        @click="addOrUpdateHandle()">{{
                        $t("add") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:delete')" type="danger"
                        @click="state.deleteHandle()">{{
                            $t("deleteBatch") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:pause')" type="danger" @click="pauseHandle()">{{
                        $t("schedule.pauseBatch") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:resume')" type="danger" @click="resumeHandle()">{{
                        $t("schedule.resumeBatch") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:run')" type="danger" @click="runHandle()">{{
                        $t("schedule.runBatch") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:log')" type="success" @click="logHandle()">{{
                        $t("schedule.log") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.beanName" :placeholder="$t('schedule.beanName')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <!-- 状态 -->
                <template #status="scope">
                    <el-tag v-if="scope.row.status === 1" size="small">{{ $t("schedule.status1") }}</el-tag>
                        <el-tag v-else size="small" type="danger">{{ $t("schedule.status0") }}</el-tag>
                </template>
                
                <!-- 操作 -->
                <template #operation="scope">
                    <el-button v-if="state.hasPermission('sys:schedule:update')" type="primary" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:pause')" type="primary" text bg
                        @click="pauseHandle(scope.row.id)">{{ $t("schedule.pause") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:resume')" type="primary" text bg
                        @click="resumeHandle(scope.row.id)">{{ $t("schedule.resume") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:run')" type="primary" text bg
                        @click="runHandle(scope.row.id)">{{ $t("schedule.run") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:schedule:delete')" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
            
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
        <!-- 弹窗, 日志列表 -->
        <log ref="logRef"></log>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import baseService from "@/service/baseService";
import { IObject } from "@/types/interface";
import AddOrUpdate from "./schedule-add-or-update.vue";
import Log from "./schedule-log.vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
const { t } = useI18n();

const logRef = ref();

const view = reactive({
    getDataListURL: "/sys/schedule/page",
    getDataListIsPage: true,
    deleteURL: "/sys/schedule",
    deleteIsBatch: true,
    dataForm: {
        beanName: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "beanName",
        label: "bean名称",
        minWidth: 120
    },
    {
        prop: "params",
        label: "参数",
        minWidth: 100
    },
    {
        prop: "cronExpression",
        label: "cron表达式",
        minWidth: 120
    },
    {
        prop: "remark",
        label: "备注",
        minWidth: 120
    },
    {
        prop: "status",
        label: "状态",
        minWidth: 100,
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 380
    }
])

// 暂停
const pauseHandle = (id?: string) => {
    if (!id && state.dataListSelections && state.dataListSelections.length <= 0) {
        return ElMessage({
            message: t("prompt.deleteBatch"),
            type: "warning",
            duration: 500
        });
    }
    ElMessageBox.confirm(t("prompt.info", { handle: t("schedule.pause") }), t("prompt.title"), {
        confirmButtonText: t("confirm"),
        cancelButtonText: t("cancel"),
        type: "warning"
    })
        .then(() => {
            baseService.put("/sys/schedule/pause", id ? [id] : state.dataListSelections && state.dataListSelections.map((item: IObject) => item.id)).then(() => {
                ElMessage.success({
                    message: t("prompt.success"),
                    duration: 500,
                    onClose: () => {
                        state.getDataList();
                    }
                });
            });
        })
        .catch(() => {
            //
        });
};

// 恢复
const resumeHandle = (id?: string) => {
    if (!id && state.dataListSelections && state.dataListSelections.length <= 0) {
        return ElMessage({
            message: t("prompt.deleteBatch"),
            type: "warning",
            duration: 500
        });
    }
    ElMessageBox.confirm(t("prompt.info", { handle: t("schedule.resume") }), t("prompt.title"), {
        confirmButtonText: t("confirm"),
        cancelButtonText: t("cancel"),
        type: "warning"
    })
        .then(() => {
            baseService.put("/sys/schedule/resume", id ? [id] : state.dataListSelections && state.dataListSelections.map((item: IObject) => item.id)).then(() => {
                ElMessage.success({
                    message: t("prompt.success"),
                    duration: 500,
                    onClose: () => {
                        state.getDataList();
                    }
                });
            });
        })
        .catch(() => {
            //
        });
};

// 执行
const runHandle = (id?: string) => {
    if (!id && state.dataListSelections && state.dataListSelections.length <= 0) {
        return ElMessage({
            message: t("prompt.deleteBatch"),
            type: "warning",
            duration: 500
        });
    }
    ElMessageBox.confirm(t("prompt.info", { handle: t("schedule.run") }), t("prompt.title"), {
        confirmButtonText: t("confirm"),
        cancelButtonText: t("cancel"),
        type: "warning"
    })
        .then(() => {
            baseService.put("/sys/schedule/run", id ? [id] : state.dataListSelections && state.dataListSelections.map((item: IObject) => item.id)).then(() => {
                ElMessage.success({
                    message: t("prompt.success"),
                    duration: 500,
                    onClose: () => {
                        state.getDataList();
                    }
                });
            });
        })
        .catch(() => {
            //
        });
};

// 日志列表
const logHandle = () => {
    logRef.value.init();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};

// 重置操作
const getResetting = () => {
    view.dataForm.beanName = "";
    state.getDataList();
}
</script>
