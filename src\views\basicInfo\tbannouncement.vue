<template>
  <div class="mod-basicInfo__tbannouncement">
    <el-card shadow="never" class="rr-view-ctx-card mb-20 ny_form_card">
      <ny-form-slot>
        <template #content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.title" placeholder="请输入标题" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <!-- 1上架  2下架 -->
              <el-select v-model="state.dataForm.status" placeholder="请选择状态" clearable>
                <el-option label="已发布" value="1"></el-option>
                <el-option label="未发布" value="2"></el-option>
              </el-select>
              <!-- <el-input v-model="state.dataForm.status" placeholder="状态" clearable></el-input> -->
            </el-form-item>
          </el-form>
        </template>
        <template #button>
          <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
          <el-button @click="resetForm()" type="info">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table 
          :state="state" 
          @pageSizeChange="state.pageSizeChangeHandle"
          @pageCurrentChange="state.pageCurrentChangeHandle"
        >
          <template #header>
            <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
            <el-button v-if="state.hasPermission('basicInfo:tbannouncement:save')" type="primary"
              @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            <el-button v-if="state.hasPermission('basicInfo:tbannouncement:delete')" type="danger"
              @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
            <el-button v-if="state.hasPermission('basicInfo:tbannouncement:upper')" @click="PublisFn"
              type="success">发布</el-button>
            <el-button v-if="state.hasPermission('basicInfo:tbannouncement:down')" @click="CancelPublishingFn"
              type="warning">取消发布</el-button>
          </template>
          
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border
            @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <!-- <el-table-column prop="id" label="id" width="200" header-align="center" align="center"></el-table-column> -->

            <el-table-column prop="title" label="标题" header-align="center" align="center"></el-table-column>
            <el-table-column prop="sort" label="排序" width="100" header-align="center" align="center"></el-table-column>
            <!-- <el-table-column prop="text" label="详情" header-align="center" align="center"></el-table-column> -->
            <!-- <el-table-column prop="text" label="详情" header-align="center" align="center">
            <template #default="scope">
              <div v-html="scope.row.text"></div>
            </template>
    </el-table-column> -->
            <!-- 1上架  2下架 -->
            <el-table-column prop="status" label="状态" width="100" header-align="center" align="center">
              <template #default="scope">
                <el-tag v-if="scope.row.status === 1" effect="dark" type="success">上架</el-tag>
                <el-tag v-else-if="scope.row.status === 2" effect="dark" type="danger">下架</el-tag>
              </template>
            </el-table-column>
            <!-- 否0 是1 -->
            <el-table-column prop="isDelete" label="是否删除" width="100" header-align="center" align="center">
              <template #default="scope">
                <el-tag v-if="scope.row.isDelete === 0" type="success">否</el-tag>
                <el-tag v-else-if="scope.row.isDelete === 1" type="danger">是</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="creatorName" label="创建者" header-align="center" align="left">
              <template #default="scope">
                <div class="creatOrUpdate">
                  <img :src="scope.row.creatorHeadurl ? scope.row.creatorHeadurl : userLogo" alt="">
                  <span>{{ scope.row.creatorName }}</span>
                </div>

              </template>
            </el-table-column>
            <!-- <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column> -->
            <el-table-column prop="createDate" label="创建时间" header-align="center" align="center">
              <template #default="scope">

                <span>{{ formatTimeStamp(scope.row.createDate) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="updaterName" label="更新者" header-align="center" align="center">
              <template #default="scope">
                <div class="creatOrUpdate">
                  <img :src="scope.row.creatorHeadurl ? scope.row.creatorHeadurl : userLogo" alt="">
                  <span>{{ scope.row.updaterName }}</span>
                </div>

              </template>
            </el-table-column>
            <!-- <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column> -->
            <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center">
              <template #default="scope">
                <span>{{ formatTimeStamp(scope.row.updateDate) }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button v-if="state.hasPermission('basicInfo:tbannouncement:update')" type="primary"
                  text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button v-if="state.hasPermission('basicInfo:tbannouncement:delete')" type="danger"
                  text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                <!-- <el-button @click="PublisFn(scope.row)" type="primary" link>发布</el-button>
              <el-button @click="CancelPublishingFn(scope.row)" type="danger" link>取消发布</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </ny-table>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./tbannouncement-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import userLogo from "@/assets/images/user.png";

const view = reactive({
  getDataListURL: "/basicInfo/tbannouncement/page",
  getDataListIsPage: true,
  exportURL: "/basicInfo/tbannouncement/export",
  deleteURL: "/basicInfo/tbannouncement",
  deleteIsBatch: true,
  dataForm: {
    title: "",
    status: "",
  }
});
// 发布按钮 判断status 1是已发布 2是未发布
const PublisFn = (itemdata: any) => {
  

  // if (state.dataListSelections&&state.dataListSelections.status=='2') {

  // }else{
  //   ElMessage.warning('该数据已发布')
  // }

  if (state.dataListSelections?.length>0) {
    // let params =Object.assign({},itemdata)
    // let params =JSON.parse(JSON.stringify(itemdata))
    let params={...state.dataListSelections[0]}
    console.log(params,'发布2',state);
    if (params.status == '1') {
      ElMessage.warning('该数据已发布')
    } else {
      params.status = '1'
      // params.id=itemdata.id
      baseService.put("/basicInfo/tbannouncement", params).then((res) => {
        if (res.code == 0) {
          ElMessage.success("发布成功");
          state.getDataList()
        }
      })
    }

  } else {
    if (state.dataListSelections?.some(item => item.status == '1')) {
      ElMessage.warning('选择的数据有已发布')
    } else {
      let publishParams = state.dataListSelections?.map(item => item.id)
      baseService.put("/basicInfo/tbannouncement/upper", publishParams).then((res) => {
        if (res.code == 0) {
          ElMessage.success("发布成功");
          state.getDataList();
        } else {
          ElMessage.error(res.msg);
        }
      });
      console.log(publishParams, "发布按钮", state.dataListSelections)
    }
  }

}

// 重置
const resetForm = () => {
  state.dataForm.title = ''
  state.dataForm.status = ''
}


// 取消发布 判断status 1是已发布 2是未发布
const CancelPublishingFn = (itemdata: any) => {
  
  
  if (state.dataListSelections?.length>0) {
    // let params = { ...itemdata }
    // let params =Object.assign({},itemdata)
    // let params =JSON.parse(JSON.stringify(itemdata))
    let params={...state.dataListSelections[0]}
    console.log(params,'是否发布1');
    if (params.status == '2') {
      ElMessage.warning('该数据取消发布')
    } else {
      // params.id=itemdata.id
      params.status = '2'
      baseService.put("/basicInfo/tbannouncement", params).then((res) => {
        if (res.code == 0) {
          ElMessage.success("取消发布成功");
          state.getDataList()
        }
      })
    }

  } else {
    if (state.dataListSelections?.some(item => item.status == '2')) {
      ElMessage.warning('选择的数据有未发布')
    } else {
      let cancelpublishParams = state.dataListSelections?.map(item => item.id)

      baseService.put("/basicInfo/tbannouncement/down", cancelpublishParams).then((res) => {
        if (res.code == 0) {
          ElMessage.success("取消发布成功");
          state.getDataList();
        } else {
          ElMessage.error(res.msg);
        }
      });

      console.log(cancelpublishParams, "取消发布按钮", state.dataListSelections)
    }
  }


}

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
<style lang="less">
.creatOrUpdate {
  display: flex;
  // justify-content: center;
  align-items: center;
  padding-left: 10px;

  img {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 20px;
  }
}
</style>