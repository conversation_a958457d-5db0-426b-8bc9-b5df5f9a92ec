<template>
  <div style="width: 100%">
    <div class="title">自动查话费</div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <div style="display: flex; gap: 8px">
          <el-button type="primary" @click="multiHandle(1)">批量重启</el-button>
          <el-button type="warning" @click="multiHandle(2)">批量禁用</el-button>
          <el-button type="success" @click="multiHandle(4)">批量编辑</el-button>
          <el-button color="#409EFF" style="color: #fff" @click="multiHandle(3)">批量执行</el-button>
        </div>
      </template>
      <template #header-right>
        <div style="display: flex; gap: 8px">
          <el-input :prefix-icon="Search" style="width: 320px" v-model="state.dataForm.title" placeholder="请输入手机号/备注" clearable></el-input>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </template>
      <template #status="{ row }">
        <el-button link type="danger" v-if="row.cardStatus == 0">禁用</el-button>
        <el-button link type="success" v-if="row.status == 1">启用</el-button>
      </template>
      <template #remark="{ row }">
        <template v-if="row.remark">{{ row.remark }}</template>
        <div v-else>
          <span>-</span>
          <el-button @click="editHandle(row)" link type="primary"
            ><el-icon><Edit /></el-icon
          ></el-button>
        </div>
      </template>
      <template #create_time="{ row }">
        <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
      </template>
    </ny-table>
    <!-- 批量编辑 -->
    <multiEdit ref="multiEditRef" @refreshDataList="state.getDataList()"></multiEdit>
    <!-- 备注 -->
    <editRemark ref="editRemarkRef" @refreshDataList="state.getDataList()"></editRemark>
  </div>
</template>
          
  <script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import editRemark from "./edit-remark.vue";
import multiEdit from "./multi-Edit.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileCheckPhoneBill/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileCheckPhoneBill",
  deleteIsBatch: true,
  dataForm: {
    title: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 136
  },
  {
    prop: "operator",
    label: "运营商",
    sortable: "custom",
    minWidth: 100
  },
  {
    prop: "balance",
    label: "余额(元)",
    sortable: "custom",
    minWidth: 100
  },
  {
    prop: "status",
    label: "状态",
    sortable: "custom",
    minWidth: 100
  },
  {
    prop: "phoneBillBalance",
    label: "话费余额",
    minWidth: 350
  },
  {
    prop: "executionEnvironment",
    label: "最近执行状况",
    minWidth: 182
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: 136
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: 160
  }
]);

const multiEditRef = ref();
const multiHandle = (index: any) => {
  if (!state.dataListSelections || state.dataListSelections.length == 0) {
    ElMessage.warning("请先勾选！");
    return;
  }
  if (index == 1) {
    // 启用
  } else if (index == 2) {
    // 禁用
  } else if (index == 3) {
    // 执行
  } else if (index == 4) {
    // 编辑
    nextTick(() => {
      multiEditRef.value.init(state.dataListSelections);
    });
  }
};
// 重置操作
const getResetting = () => {
  state.dataForm.title = "";
  state.page = 1;
  state.getDataList();
};

const editRemarkRef = ref();
const editHandle = (row: any) => {
  nextTick(() => {
    editRemarkRef.value.init(2, row.id);
  });
};
</script>
          
          <style lang='less' scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
</style>