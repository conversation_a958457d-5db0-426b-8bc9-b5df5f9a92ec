<template>
  <!-- 回收订单 -->
  <div class="container acquisition-order">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab class="newTabSty" :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <ny-table :state="state" :columns="columns" :show-summary="true" :summary-method="getSummaries" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.aStatus" @change="state.getDataList"></ny-button-group>
        </template>

        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important" v-model="state.dataForm.keywords" placeholder="请输入订单编号/游戏账号" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>

        <!-- 游戏账号 -->
        <template #gameAccount="{ row }">
          <el-text type="primary" text class="pointer" @click="logHandle(row.orderId, 'detail', row)">{{ row.gameAccount }}</el-text>
        </template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == '已换绑'" type="primary">已支付</el-tag>
          <el-tag v-if="row.state == '待换绑'" type="danger">待支付</el-tag>
          <el-tag v-if="row.state == '未入库已打款' || row.state == '已入库已打款' || row.state == '入库且已打款'" type="success">交易成功</el-tag>
          <el-tag v-if="row.state == '已取消'" type="info">交易失败</el-tag>
        </template>

        <!-- 成交价 -->
        <template #payPrice="{ row }">
          {{ row?.paymentInfo?.amount || "-" }}
        </template>

        <template #operation="{ row }">
          <operation-btns
            :key="Math.random()"
            :max="4"
            :buttons="[
              {
                text: '详情',
                type: 'primary',
                click: () => showDetail(row),
                // 待换绑
                isShow: true
              },

              {
                text: '取消',
                type: 'danger',
                click: () => cancelOrderHandle(row),
                isShow: showBtn(['待回收'], row.state) && state.hasPermission('purchase:purchaseorder:cancelOrder')
              }
            ]"
          />
        </template>
      </ny-table>
      <detail ref="detailRef"></detail>
      <!-- 操作日志 -->
      <operation-log ref="operationLogRef"></operation-log>
      <!-- 取消订单 -->
      <order-cancel ref="orderCancelRef" @refresh="state.getDataList()"></order-cancel>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import { BigNumber } from "bignumber.js";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import detail from "./components/detail.vue";
import OrderCancel from "./components/OrderCancel.vue";
import OperationLog from "./components/OperationLog.vue";
import OperationBtns from "@/components/ny-table/src/components/OperationBtns.vue";

import { useAppStore } from "@/store";
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "sn",
    label: "订单编号",
    minWidth: 190
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 200
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 184
  },
  {
    prop: "amount",
    label: "账号金额(元)",
    width: 136,
    sortable: "custom"
  },

  {
    prop: "guaranteePrice",
    label: "包赔费(元)",
    width: 120,
    sortable: "custom"
  },
  {
    prop: "payPrice",
    label: "成交价(元)",
    width: 160,
    sortable: true
  },
  {
    prop: "payTime",
    label: "打款时间",
    width: 136
  },
  {
    prop: "state",
    label: "状态",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    width: 120,
    fixed: "right"
  }
]);
const store = useAppStore();
const view = reactive({
  getDataListURL: "/purchase/page",
  getDataListIsPage: true,
  createdIsNeed: false,
  exportURL: "/purchase/export",
  dataForm: {
    gameId: "",
    aStatus: "",
    keywords: "",
    startTime: "",
    endTime: "",
    order: "",
    orderField: "",
    tenantCode: ""
  }
});

const state = reactive({
  ...useView(view),
  ...toRefs(view)
});

// 重置操作
const getResetting = () => {
  state.dataForm.gameId = "";
  state.dataForm.aStatus = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  { label: "待支付", value: "DZF" },
  { label: "已支付", value: "YZF" },
  { label: "交易成功", value: "JYCG" },
  { label: "交易失败", value: "JYSB" }
];

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
const orderCancelRef = ref();
const cancelOrderHandle = async (row: any) => {
  await nextTick();
  orderCancelRef.value.init(row);
};
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);

// 日志
const operationLogRef = ref();
const logHandle = (id: number, type: string, row: any) => {
  operationLogRef.value.init(id, "", type, row);
};
// 详情
const detailRef = ref();
const showDetail = (id: number) => {
  detailRef.value.init(id);
};
// 合计行计算函数
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      return (sums[index] = "合计");
    } else if (column.property == "guaranteePrice") {
      let total: any = 0;
      state.dataList.map((item: any) => {
        if (item.guaranteePrice) total = total + (+item.guaranteePrice || 0);
      });
      return (sums[index] = total);
    }
  });
  return sums;
};

// 根据状态 是否显示按钮
const showBtn = (statusList: any, state: string) => {
  return statusList.includes(state);
};

onMounted(() => {
  state.dataForm.tenantCode = store.state.user.tenantCode;
  state.dataForm.aType = 2;
  state.getDataList();
});
</script>

<style lang="scss" scoped>
.acquisition-order {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.el-tag {
  border: 1px solid;
}
</style>
