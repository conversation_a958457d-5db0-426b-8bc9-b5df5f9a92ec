<template>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
        <!-- 表单布局 -->
        <ny-form-slot>
            <template v-slot:content>
                <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                    <el-form-item>
                        <ny-select-search v-model="state.dataForm.gameCode" labelKey="name" valueKey="code"
                            url="/game/queryUserList" :param="{ limit: 9999 }" placeholder="选择游戏" />
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="state.dataForm.name" placeholder="游戏属性" clearable></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template v-slot:button>
                <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </template>
        </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
        <ny-table 
            :state="state" 
            :columns="columns"
            routePath="/customized/property/index"
            @pageSizeChange="state.pageSizeChangeHandle"
            @pageCurrentChange="state.pageCurrentChangeHandle" 
            @selectionChange="state.dataListSelectionChangeHandle"
            @dragSort="dragSortHandle"
        >
            <template #header>
                <el-button v-if="state.hasPermission('customized:property:save')" type="primary"
                    @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                <el-button v-if="state.hasPermission('customized:property:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length"
                    @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
                <el-button type="warning" @click="jumpValuationWebsite">跳转估价网站生成</el-button>
            </template>

            <template #type="{row}">
                {{ row.type == 1 ? '单选' : row.type == 2 ? '多选' : row.type == 3 ? '文本' : '' }}
            </template>

            <template #status="{row}">
                <el-tag v-if="row.status == 1" type="success">正常</el-tag>
                <el-tag v-else-if="row.status == 2" type="warning">禁用</el-tag>
                <el-tag v-else-if="row.status == 0" type="danger">异常</el-tag>
            </template>
            
            <template #operation="{row}">
                <el-button 
                    v-if="state.hasPermission('basicInfo:tbannouncement:update')" 
                    type="primary"
                    text bg 
                    @click="addOrUpdateHandle(row.id)"
                >
                    {{ $t("update") }}
                </el-button>
                <el-button 
                    v-if="state.hasPermission('basicInfo:tbannouncement:delete')" 
                    type="danger"
                    text bg 
                    @click="state.deleteHandle(row.id)"
                >
                    {{ $t("delete") }}
                </el-button>

                <el-button 
                    v-if="state.hasPermission('basicInfo:tbannouncement:sort')" 
                    class="dragBtn" 
                    type="primary" 
                    text bg
                >
                    <el-icon class="text-primary">
                        <Rank />
                    </el-icon>
                </el-button>
            </template>

        </ny-table>
    </el-card>

    <!-- 新增 -->
    <property-add-or-update ref="propertyAddOrUpdateRef" :key="propertyAddOrUpdateKey" @refreshDataList="state.getDataList"></property-add-or-update>

</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, onMounted } from 'vue';
import useView from "@/hooks/useView";
import PropertyAddOrUpdate from './property-add-or-update.vue';

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "gameCode",
        label: "游戏名称",
        minWidth: 160
    },
    {
        prop: "name",
        label: "属性",
        minWidth: 150
    },
    {
        prop: "type",
        label: "类型",
        minWidth: 160
    },
    {
        prop: "attributeText",
        label: "属性明细",
        minWidth: 160
    },
    {
        prop: "status",
        label: "状态",
        minWidth: 100
    },
    {
        type: "sort",
        label: "排序",
        width: 100
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 160
    }
]);


const view = reactive({
    getDataListURL: "/paid/tbpaidfunction/page",
    getDataListIsPage: true,
    exportURL: "/paid/tbpaidfunction/export",
    deleteURL: "/paid/tbpaidfunction",
    deleteIsBatch: true,
    dataForm: {
        gameCode: "",
        name: ""
    },
});

const state = reactive({ ...useView(view,), ...toRefs(view) });

// 重置操作
const getResetting = () => {
    state.dataForm.gameCode = "";
    state.dataForm.name = "";
    state.getDataList();
}

// 新增  编辑
const propertyAddOrUpdateRef = ref();
const propertyAddOrUpdateKey = ref(0);
const addOrUpdateHandle = (id?: number) => {
    propertyAddOrUpdateKey.value++;
    nextTick(() => {
        propertyAddOrUpdateRef.value.init(id);
    })
}

// 跳转到估价网站
const jumpValuationWebsite = () => {
    const openUrl: string = window.location.protocol + '//' + window.location.host
    const link = openUrl + '/pages/recycleView/recycleView?gameCode=' + state.dataForm.gameCode;
    window.open(link, '_blank');
}

// 排序
const dragSortHandle = ({newIndex, oldIndex}: { newIndex: number; oldIndex: number }) => {
    console.log(newIndex, oldIndex);
}

</script>