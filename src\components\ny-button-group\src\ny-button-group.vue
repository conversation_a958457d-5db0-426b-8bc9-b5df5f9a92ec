<template>
    <el-scrollbar ref="scrollbarRef"class="ny-button-group">
      <div class="button-group">
        <div
            class="button-item"
            v-for="(item, index) in list"
            :key="index"
            :class="{ active: modelValue == item[value] }"
            @click="$emit('update:modelValue', item[value]),$emit('change', item[value]),clickItem(index)"
        >
            {{ item[label] }}
        </div>
      </div>
    </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, PropType, watch, nextTick } from "vue";
import { IObject } from '@/types/interface';

const props = defineProps({
    modelValue: {
      type: [Number, String] as PropType<number | string>,
      required: true
    },
    list: { 
        type: Array as PropType<IObject[]>,
        required: false
    },
    value: {
      type: String,
      required: false,
      default: 'dictValue'
    },
    label: {
      type: String,
      required: false,
      default: 'dictLabel'
    }
});

const itemScrollLeft = ref([]);
const init = () => {
  let arr: any = document.getElementsByClassName('button-item');
  if(!arr.length) return;
  arr.forEach((item: any) => {
    itemScrollLeft.value.push(item.offsetLeft);
  })
}

const scrollbarRef = ref();
const clickItem = (index: number) => {
  try {
    let btnGroupWidth = document.getElementsByClassName('button-group')[0].clientWidth;
    scrollbarRef.value.setScrollLeft(itemScrollLeft.value[index] - btnGroupWidth / 2)
  } catch (error) {
    
  }
}


watch(() => props.list, (newValue) => {
  nextTick(() => {
    init();
  })
}, { deep: true, immediate: true })


</script>


<script lang="ts">
export default {
    name: 'NyButtonGroup'
}
</script>

<style lang="scss" scoped>
.ny-button-group{
  background: #f2f2f2;
  border-radius: 6px;
  margin-right: 10px;
}
.button-group {
  
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
</style>