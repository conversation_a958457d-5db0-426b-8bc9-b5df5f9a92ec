<template>
  <el-drawer v-model="visible" title="线索详情" size="944" class="ny-drawer article-add-or-update">
    <div class="cardDescriptions" v-loading="dataLoading">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ dataForm.sn || "-" }}</el-descriptions-item>
        <el-descriptions-item label="订单来源">{{ dataForm.orderSourceLabel || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏名称">{{ dataForm.gameName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏区服">{{ dataForm.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item :label="propsData.orderSource == 2 ? '营地号' : '游戏账号'">{{ propsData.orderSource == 2 ? dataForm.campNo : dataForm.gameAccount }}</el-descriptions-item>
        <el-descriptions-item label="零售价（元）">{{ dataForm.saleAmount }}</el-descriptions-item>
        <el-descriptions-item label="商品编码">{{ dataForm.shopCode || "-" }}</el-descriptions-item>
        <el-descriptions-item label="商品类型">{{ dataForm.saleTypeLabel || "-" }}</el-descriptions-item>
        <el-descriptions-item label="提交人">{{ dataForm.submitter || "-" }}</el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ dataForm.createDate || "-" }}</el-descriptions-item>
        <el-descriptions-item label="商品标题" :span="2">{{ dataForm.title || "-" }}</el-descriptions-item>
        <el-descriptions-item label="商品描述" :span="2">
          <template #label>
            <div class="pointer flx-align-center" @click="copyInfo(dataForm.shopInfo)">
              商品描述 <el-icon class="ml-5 text-primary"><DocumentCopy /></el-icon>
            </div>
          </template>
          <div style="cursor: pointer; max-height: 500px; overflow-y: scroll" @dblclick="copyInfo(dataForm.shopInfo)">
            {{ dataForm.shopInfo }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="商品主图" :span="2">
          <div>
            <el-image style="height: 100px" v-if="dataForm.log" :src="dataForm.log" :preview-src-list="[dataForm.log]" preview-teleported fit="scale-down" />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="商品详情图片" :span="2">
          <template #label>
            <el-tooltip effect="dark" content="点击下载图片">
              <div class="pointer flx-align-center" @click="downloadAll">
                商品详情图片 <el-icon class="ml-5 text-primary"><Download /></el-icon>
              </div>
            </el-tooltip>
          </template>
          <div style="display: flex; flex-wrap: wrap; gap: 10px">
            <el-image style="height: 100px" v-for="(item, index) in dataForm.images" :src="item" :preview-src-list="dataForm.images" :initial-index="index" preview-teleported fit="scale-down" />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="cardDescriptions" v-loading="dataLoading" style="margin-top: 12px">
      <div class="p-title mt-0">买家信息</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="买方手机号">{{ dataForm.buyerPhone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="买方姓名">{{ dataForm.payer || "-" }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ dataForm.payTime || "-" }}</el-descriptions-item>
        <el-descriptions-item label="成交价（元）">{{ dataForm.dealAmount || "-" }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import useClipboard from "vue-clipboard3";
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";
import qs from "qs";
const emit = defineEmits(["refresh"]);
const visible = ref(false);
const propsData = ref({} as any);
const init = (row: any) => {
  visible.value = true;
  propsData.value = row;
  if (propsData.value.orderSource == 2) {
    dataLoading.value = true;
    // 估价器
    baseService.get("/purchase/page?state=WAIT_FOR_PUCHASE&limit=9999").then((res) => {
      let dataArr = res.data.list || [];
      let data = dataArr.find((ele) => ele.campNo == propsData.value.gameAccount);
      if (data) {
        data.sn = "";
        dataForm.value = Object.assign(dataForm.value, propsData.value);
        dataForm.value = Object.assign(dataForm.value, data);
      }
      dataLoading.value = false;
    });
  } else {
    getDetails();
  }
};
const dataLoading = ref(false);
const getDetails = () => {
  dataLoading.value = true;
  baseService
    .get("/accountResourcesPool/accountresourcespool/detail/" + propsData.value.orderId)
    .then((res) => {
      if (res.code === 0 && res.data) {
        dataForm.value = Object.assign(res.data, propsData.value);
        dataForm.value.shopInfo = res.data.info || "-";
        if (res.data.images) {
          dataForm.value.images = JSON.parse(res.data.images);
        }
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 标记售后
const dataForm = ref(<any>{
  aftermarketProcessor: "",
  issueImg: ""
});

const { toClipboard } = useClipboard();
// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};
const downloadAll = async () => {
  window.location.href = `${app.api}/shop/shop/exportImages/${dataForm.value.shopId}?${qs.stringify({ token: getToken() })}`;
};
defineExpose({
  init
});
</script>
