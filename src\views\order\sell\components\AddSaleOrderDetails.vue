<template>
    <el-drawer @close="visible = false" :footer="null" v-model="visible" size="944" class="infoDrawer">
        <template #header>
            <div class="drawer_title">合作商销售订单详情</div>
        </template>
        <el-scrollbar v-loading="requestLoading">
            <div class="basicInfoSty cardDescriptions">
                <div class="titleSty">基本信息</div>
                <el-descriptions class="tipinfo" :column="2" size="default" border>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>订单号</div>
                        </template>
                        {{ detailsInfo.sn || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>订单类型</div>
                        </template>
                        合作商销售订单
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>游戏名称</div>
                        </template>
                        {{ detailsInfo.gameName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>游戏区服</div>
                        </template>
                        {{ detailsInfo.serverName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>订单状态</div>
                        </template>
                        <el-tag :type="detailsInfo.state == '交易成功' ? 'success' :'warning'">{{ detailsInfo.state }}</el-tag>
                        
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>出售渠道</div>
                        </template>
                        {{ detailsInfo.channelName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>商品编码</div>
                        </template>
                        {{ detailsInfo.shopCode || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>成交价(元)</div>
                        </template>
                        {{ detailsInfo.dealAmount || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>游戏账号</div>
                        </template>
                        {{ detailsInfo.gameAccount || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>出售人</div>
                        </template>
                        {{ detailsInfo.saleUserName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item :span="2">
                        <template #label>
                            <div>商品标题</div>
                        </template>
                        {{ detailsInfo.shopTitle || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>游戏主图</div>
                        </template>
                        <el-image style="height: 68px; width: 68px" :src="detailsInfo.log" :preview-src-list="[detailsInfo.log]" preview-teleported fit="cover" />
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="basicInfoSty cardDescriptions">
                <div class="titleSty">收款信息</div>
                <el-descriptions class="tipinfo" :column="2" size="default" border>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>三方订单号</div>
                        </template>
                        {{ detailsInfo.thirdPartyOrderCode || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>实收金额(元)</div>
                        </template>
                        {{ detailsInfo.receivableMoney || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>收款账户</div>
                        </template>
                        {{ detailsInfo.accountName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title" v-if="detailsInfo.accountType == '1'">
                        <template #label>
                            <div>支付宝订单号</div>
                        </template>
                        {{ detailsInfo.alipayNo || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>付款凭证</div>
                        </template>
                        <el-image style="height: 68px; width: 68px" :src="detailsInfo.payCredentials" :preview-src-list="[detailsInfo.payCredentials]" preview-teleported fit="cover" />
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="basicInfoSty cardDescriptions" v-if="detailsInfo.state == '交易成功' && detailsInfo.statusRemark == '打款已审核'">
                <div class="titleSty">打款信息</div>
                <el-descriptions class="tipinfo" :column="2" size="default" border>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>付款方式</div>
                        </template>
                        {{ ['支付宝转账','微信','银行卡'][detailsInfo.paymentAccountType - 1] }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>实付金额(元)</div>
                        </template>
                        {{ detailsInfo.paymentMoney || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>付款账户</div>
                        </template>
                        {{ detailsInfo.paymentAccount || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title" v-if="detailsInfo.paymentAccountType == '1'">
                        <template #label>
                            <div>支付宝订单号</div>
                        </template>
                        {{ detailsInfo.lendingAlipayNo || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="title">
                        <template #label>
                            <div>付款凭证</div>
                        </template>
                        <el-image style="height: 68px; width: 68px" :src="detailsInfo.payImage" :preview-src-list="[detailsInfo.payImage]" preview-teleported fit="cover" />
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-scrollbar>
    </el-drawer>
</template>

<script lang='ts' setup>
import baseService from '@/service/baseService';
import { ref, reactive } from 'vue';

const visible = ref(false); // 弹窗变量
const requestLoading = ref(false); // 数据加载
const detailsInfo = ref(<any>{})

// 表单初始化
const init = (id: any) => {
    visible.value = true;
    requestLoading.value = true;
    baseService.get("/partnerSaleOrder/getOrder/" + id).then(res=>{
        detailsInfo.value = res.data
    }).finally(()=>{
        requestLoading.value = false;
    })
};
defineExpose({
    init
});
</script>

<style lang='less' scoped>
.basicInfoSty {
    padding: 12px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 12px;

    .titleSty {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
        padding-left: 8px;
        border-left: 2px solid var(--el-color-primary);
        margin-bottom: 12px;
    }

    .tipinfo {
        :deep(.el-descriptions__label) {
            width: 144px;
            background: #f5f7fa;
            font-family: Inter, Inter;
            font-weight: 500;
            font-size: 14px;
            color: #606266;
            padding: 9px 12px;
            border: 1px solid #ebeef5;
        }
    }
}
</style>