<template>
  <div style="margin-top: 10px">
    <el-button color="#73767A" :icon="RefreshRight" @click="getList"></el-button>
    <el-button type="primary" @click="statAddFn">新增统计图</el-button>
  </div>
  <el-table :data="tableData" style="width: 100%; margin-top: 10px" border class="elTable">
    <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="80" />
    <el-table-column prop="name" label="统计表名称" align="center" />
    <el-table-column prop="type" label="类型" align="center">
      <template #default="{ row }">
        <div v-if="row.type == 'option1'">基础柱状图</div>
        <div v-if="row.type == 'option2'">堆积柱状图</div>
        <div v-if="row.type == 'option3'">基础折线图</div>
        <div v-if="row.type == 'option4'">平滑折线图</div>
        <div v-if="row.type == 'option5'">饼图</div>
        <div v-if="row.type == 'option6'">环形图</div>
      </template>
    </el-table-column>
    <el-table-column prop="createDate" label="时间" align="center">
      <template #default="{ row }">
        {{ formatTimeStamp(row.createDate) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button link type="primary" size="small" @click="refreshChange(row.type, row.id)">编辑</el-button>
        <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定删除该目录吗？" @confirm="statDelete(row.id)">
          <template #reference>
            <el-button link type="danger" size="small">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
    <!-- 空状态 -->
    <template #empty>
      <div style="padding: 68px 0">
        <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
      </div>
    </template>
  </el-table>
  <el-pagination :current-page="requestParams.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="requestParams.limit" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
  <!-- 新增统计图 - 选择报表 -->
  <statAdd ref="statAddRef" @refresh="refreshChange"></statAdd>
  <!-- 配置统计报表 -->
  <chart ref="chartRef" :menuInfo="JSON.parse(JSON.stringify(props.menuInfo))" @refreshDataList="getList()"></chart>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, watch } from "vue";
import { RefreshRight } from "@element-plus/icons-vue";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import statAdd from "../echarts/index.vue";
import chart from "../echarts/chart.vue";

interface Props {
  menuInfo: any;
}
const props = withDefaults(defineProps<Props>(), {
  menuInfo: ""
});

const tableData = ref(<any>[]);
const total = ref(0);
// 请求参数
const requestParams = {
  page: 1,
  limit: 10,
  id: "",
  indexName: ""
};

// 序号
const indexMethod = (index: number) => {
  return index + 1;
};

// 请求列表数据
const getList = () => {
  tableData.value = [];
  baseService.get("/report/reportchart/page", requestParams).then((res) => {
    tableData.value = res.data.list;
    total.value = res.data.total;
  });
};

const sizeChange = (number: any) => {
  requestParams.page = number;
  getList();
};

const currentChange = (number: any) => {
  requestParams.page = number;
  getList();
};

// 删除
const statDelete = (id: any) => {
  baseService.delete("/report/reportchart", [id]).then((res) => {
    if (res.code == 0) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};

watch(
  () => props.menuInfo.id,
  (newValue) => {
    if (newValue) {
      requestParams.id = newValue;
      requestParams.indexName = props.menuInfo.indexName;
      nextTick(() => {
        getList();
      });
    }
  },
  {
    immediate: true
  }
);

// 新增选择统计报表
const statAddRef = ref();
const statAddFn = (id?: any) => {
  nextTick(() => {
    statAddRef.value.init(id);
  });
};

// 选择报表回调
const chartRef = ref();
const addKey = ref(0);
const refreshChange = (type: any, id?: number) => {
  addKey.value++;
  // 配置报表
  nextTick(() => {
    chartRef.value.init(type, id);
  });
};
</script>

<style lang="less" scoped></style>
