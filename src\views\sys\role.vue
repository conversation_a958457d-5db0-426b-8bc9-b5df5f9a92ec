<template>
    <div class="mod-sys__role TableXScrollSty">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table
                :state="state"
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle"
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:role:save')" type="primary" @click="addOrUpdateHandle()">{{
                        $t("add")
                        }}</el-button>
                    <el-button v-if="state.hasPermission('sys:role:delete')" type="danger" @click="state.deleteHandle()">{{
                        $t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.name" :placeholder="$t('role.name')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button v-if="state.hasPermission('sys:role:update')" type="primary" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button type="danger" text bg
                        @click="deleteClick(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>
            </ny-table>
        </el-card>


        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./role-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElNotification, ElMessage, ElMessageBox  } from 'element-plus'

const view = reactive({
    getDataListURL: "/sys/role/page",
    getDataListIsPage: true,
    deleteIsBatch: true,
    dataForm: {
        name: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "name",
        label: "角色名称",
    },
    {
        prop: "remark",
        label: "备注",
    },
    {
        prop: "createDate",
        label: "创建时间",
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

// 重置操作
const getResetting = () => {
    state.dataForm.name = "";
    state.getDataList();
}


// 删除操作
const deleteClick = (id:any) =>{
    ElMessageBox.confirm('确定删除该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        baseService.delete('/sys/role',[id]).then(res=>{
            if(res.code == 0){
                if(res.msg == '操作成功'){
                    ElMessage.success('删除成功')
                }else{
                    ElNotification({
                        title: '提示',
                        message: res.msg,
                        type: 'warning',
                        duration: 0,
                    })
                }
                state.getDataList();
            }
        })
    })
}

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
