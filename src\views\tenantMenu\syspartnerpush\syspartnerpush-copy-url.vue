<template>
  <el-drawer v-model="drawerShow" size="40%" class="manage">
    <template #header>
      <div class="manage_title">复制选号网址</div>
    </template>
    <div style="margin-bottom: 20px">
      <el-button type="primary" @click="addOrEditHandle('add')">新增</el-button>
      <el-button @click="addOrEditHandle('edit')">编辑</el-button>
    </div>
    <el-tabs v-model="editableTabsValue" type="card" class="demo-tabs" closable @tab-remove="handleTabsRemove" @tab-change="handleTabsChange">
      <el-tab-pane v-for="item in editableTabs" :key="item.id" :label="item.title" :name="item.id"> </el-tab-pane>
    </el-tabs>
    <div class="flx-align-center" style="margin-bottom: 20px">
      <el-alert title="提示：折扣9折（输入：90）；原价（输入：100）；加价10%（输入：110）" :closable="false" type="warning" show-icon style="margin-right: 10px" />
      <el-popover placement="bottom">
        <template #reference>
          <el-button type="primary" plain size="large">
            <span>复制选号网址</span>
            <el-icon><ElementPlus /></el-icon>
          </el-button>
        </template>
        <div class="flx-center" style="margin-bottom: 8px">
          <el-button type="primary" text size="small" @click="copyExternal">复制到外部网站</el-button>
        </div>
        <div class="flx-center">
          <el-button type="primary" text size="small" @click="copyInternal">复制到内部平台</el-button>
        </div>
      </el-popover>
    </div>
    <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
      <el-table-column prop="gameName" label="游戏名称" align="center" min-width="120" />
      <el-table-column prop="percent" label="价格比率" align="center" min-width="80">
        <template #default="{ row }"> <el-input-number v-model="row.percent" :min="0" size="small" @change="inputSave(row)" /> % </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { useSettingStore } from "@/store/setting";
import useClipboard from "vue-clipboard3";
const { toClipboard } = useClipboard();

const settingStore = useSettingStore();

const drawerShow = ref(false);
const editableTabsValue = ref(""); // 选择页签
const editableTabs = ref(<any>[]); // 页签列表
const tableData = ref(<any>[]); // 页面表格

// 获取页签列表
const getSysShopTabs = () => {
  baseService.get("/shop/sysselectshop/page").then((res) => {
    editableTabsValue.value = editableTabsValue.value ? editableTabsValue.value : res.data[0].id;
    editableTabs.value = res.data;
    getSysShopList();
  });
};

// 获取选号网址配置信息
const tableLoading = ref(false);
const getSysShopList = () => {
  tableLoading.value = true;
  baseService
    .get("/shop/sysselectshop/" + editableTabsValue.value)
    .then((res) => {
      tableData.value = res.data;
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 保存选号网址配置
const inputSave = (row: any) => {
  row.selectId = editableTabsValue.value;
  baseService.put("/shop/sysselectshop/config", row).then((res) => {
    getSysShopList();
  });
};

// 新增/编辑页签
const addOrEditHandle = (type: any) => {
  let tabItem: any = "";
  if (type == "edit") {
    tabItem = editableTabs.value.filter((item: any) => item.id == editableTabsValue.value)[0];
  }

  ElMessageBox.prompt("名称", type == "add" ? "新增" : "编辑", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: type == "add" ? "" : tabItem.title,
    inputValidator(value) {
      if (!value) {
        return "请输入名称";
      }
    }
  }).then((ele) => {
    if (type == "add") {
      baseService.post("/shop/sysselectshop", { title: ele.value }).then((res) => {
        ElMessage.success("新增成功！");
        getSysShopTabs();
      });
    } else {
      baseService.put("/shop/sysselectshop", { id: tabItem.id, title: ele.value }).then((res) => {
        ElMessage.success("修改成功！");
        getSysShopTabs();
      });
    }
  });
};
// 删除标签页
const handleTabsRemove = (e: any) => {
  ElMessageBox.confirm("确定删除该数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.delete("/shop/sysselectshop", [e]).then((res) => {
      ElMessage.success("删除成功！");
      editableTabsValue.value = "";
      getSysShopTabs();
    });
  });
};

// 切换标签页
const handleTabsChange = (name: any) => {
  getSysShopList();
};

// 复制外部选号网址
const copyExternal = () => {
  const url = editableTabs.value.filter((item: any) => item.id == editableTabsValue.value)[0].url;
  const openUrl = `${settingStore.info.websiteUrl}selectShop?url=${url}`;
  copyInfo(openUrl);
};

// 复制内部选号网址
const copyInternal = () => {
  const openUrl = `${settingStore.info.websiteUrl}selectShop?url=market`;
  copyInfo(openUrl);
};

// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};

const init = () => {
  drawerShow.value = true;
  getSysShopTabs();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.manage_title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
<style lang="less">
.manage {
  .el-drawer__header {
    margin-bottom: 0px;
  }
}
</style>
