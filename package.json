{"name": "nyyyds-tenant-admin", "version": "4.2.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode development", "build:prod": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode production &&vue-tsc --noEmit", "serve": "npm run build && vite preview", "lint": "eslint \"src/**/*.{vue,ts}\" --fix"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"src/**/*.{ts,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@imengyu/vue3-context-menu": "^1.4.3", "@rongcloud/engine": "^5.12.1", "@rongcloud/imlib-next": "^5.12.1", "@tinymce/tinymce-vue": "4.0.4", "@vueuse/core": "9.1.1", "@wangeditor/editor": "5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.6.0", "bignumber.js": "^9.1.2", "bpmn-js": "^8.10.0", "bpmn-js-token-simulation": "^0.10.0", "classnames": "^2.3.1", "codemirror-editor-vue3": "^2.0.6", "core-js": "^3.14.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-china-area-data": "^6.1.0", "element-plus": "2.7.3", "install": "^0.13.0", "lint": "^0.7.0", "lodash": "^4.17.21", "min-dash": "^3.5.2", "mitt": "^2.1.0", "monaco-editor": "^0.25.2", "npm": "^10.8.2", "nprogress": "^0.2.0", "pinia": "2.0.27", "qrcode.vue": "3.4.1", "qs": "^6.10.1", "quill": "^1.3.7", "sortablejs": "^1.13.0", "vue": "^3.4.26", "vue-at": "^3.0.0-alpha.2", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.0.0", "vue-i18n": "9.1.9", "vue-router": "4.2.5", "vue3-pdf-app": "^1.0.3", "vxe-table": "4.0.23", "xe-utils": "3.3.0"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.6", "@types/quill": "^2.0.8", "@types/sortablejs": "^1.10.6", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.26", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.1", "less-loader": "^10.0.0", "lint-staged": "^11.0.0", "prettier": "^2.6.2", "sass": "^1.50.1", "typescript": "^4.6.3", "vite": "5.2.11", "vite-plugin-bundle-obfuscator": "^1.4.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "2.0.1", "vite-tsconfig-paths": "3.4.0", "vue-tsc": "2.0.16"}}