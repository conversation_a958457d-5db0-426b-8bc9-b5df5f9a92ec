import { getValueByKeys } from "@/utils/utils";
import appPack from "../../package.json";
/**
 * 2025-04-01
 * 合作商接口域名、接口映射
 * NOTE: 本地去.env.development修改
 * 配置快捷定位： API Config
 */

const apiList = [
  {
    // 测试
    api: "http://192.168.110.11/children",
    damain: "192.168.110.11",
    wsApi: " ws://192.168.110.11/children"
  },

  {
    // 演示
    api: "https://dapi.nyyyds.com/children",
    damain: "demo1.nyyyds.com",
    wsApi: " ws://dapi.nyyyds.com/children"
  },
  {
    // 硬件服务器 - 演示
    api: "http://192.168.110.12:28080/children",
    damain: "192.168.110.12",
    wsApi: " ws://192.168.110.12:28080/children"
  },
  {
    // 乐乘
    api: "http://www.lechengwangyo.com/children",
    damain: "admin.lechengwangyo.com",
    wsApi: " ws://www.lechengwangyo.com/children"
  },

  {
    // 玩趣游
    api: "https://www.wanquyou.com/children",
    damain: "admin.wanquyou.com,39.91.138.200",
    wsApi: " ws://www.wanquyou.com/children"
  },

  {
    // 米来游
    api: "https://www.milaiyou.com/children",
    damain: "admin.milaiyou.com,39.91.138.134",
    wsApi: " ws://www.milaiyou.com/children"
  },

  {
    // 阿浪
    api: "http://www.alangds.com/children",
    damain: "admin.alangds.com,39.91.139.214",
    wsApi: " ws://www.alangds.com/children"
  },

  {
    // 米创客
    api: "http://www.mimaker.com/children",
    damain: "admin.mimaker.com,39.91.139.141",
    wsApi: " ws://www.mimaker.com/children"
  },

  {
    // 指间游
    api: "https://www.zhijianyo.com/children",
    damain: "admin.zhijianyo.com,39.91.139.117",
    wsApi: " ws://www.zhijianyo.com/children"
  },
  {
    // 清风
    api: "http://39.91.138.195:18080/children",
    damain: "39.91.138.195",
    wsApi: " ws://39.91.138.195:18080/children"
  },
  {
    // 游戏豹
    api: "http://************:18080/children",
    damain: "************"
  },
  {
    // 琳琅网络
    api: "http://*************:18080/children",
    damain: "*************"
  },
  {
    // 卖号帝
    api: "http://************:18080/children",
    damain: "************"
  },
  {
    // 游号宝
    api: "http://************8:18080/children",
    damain: "************8"
  },
  {
    // 虹兔网络
    api: "http://************:18080/children",
    damain: "************"
  }
];

const APIMapping = () => {
  console.log(window.location.hostname);
  let damainItem = apiList.find((ele) => ele.damain.indexOf(window.location.hostname) > -1);
  return damainItem
    ? damainItem.api
    : getValueByKeys(window, "SITE_CONFIG.apiURL") || import.meta.env.VITE_APP_API;
};

const wsApiMapping = () => {
  let damainItem = apiList.find((ele) => ele.damain.indexOf(window.location.hostname) > -1);
  return damainItem ? damainItem.wsApi : import.meta.env.VITE_WEBSOCKET_URL;
};

/**
 * app系统配置
 */
export default {
  /**
   * 系统版本号，自动读取package.json中的version字段
   */
  version: appPack.version,

  /**
   * 系统默认语言
   */
  defaultLang: "zh-CN",

  /**
   * api请求地址，这里读取env环境变量中的VITE_APP_API，优先使用全局变量window.SITE_CONFIG.apiURL钩子，支持在index.html中配置
   */
  api: APIMapping(),
  // api: getValueByKeys(window, "SITE_CONFIG.apiURL") || import.meta.env.VITE_APP_API,

  /**
   * websocket请求地址
   */
  wsApi: wsApiMapping(),

  /**
   * 租户模式
   */
  tenantMode: import.meta.env.VITE_APP_TENANT_MODE,

  /**
   * 启用logo图标，logo尺寸32*32，存放路径@/assets/images/logo.png
   */
  enabledLogo: true,

  /**
   * 开启页面缓存
   */
  enabledKeepAlive: false,

  /**
   * 网络请求超时时间，单位毫秒
   */
  requestTimeout: 30000,

  /**
   * 全屏渲染的页面
   */
  fullscreenPages: ["/login", "/im", "/realnameContract"]
};
