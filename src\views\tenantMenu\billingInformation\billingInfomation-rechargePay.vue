<template>
  <el-dialog v-model="visible" width="480" :title="currentType == '0' ? '充值到我的账户余额' : '充值到我的保证金'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <div class="tipinfo">完成充值后请刷新页面</div>
    <el-input type="number" v-model="dataForm.amount" placeholder="请输入金额" controls-position="right">
      <template #prepend>充值金额</template>
      <template #append>元</template>
    </el-input>
    <el-alert v-if="defaultSet.bondMin != undefined" class="alertSty" :title="'最低缴费' + defaultSet.bondMin + '元'" type="info" show-icon :closable="false" />
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "刷新页面" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "去充值" }}</el-button>
    </template>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref, reactive, defineEmits, defineExpose } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
const defaultSet = reactive({
  bondMin: undefined
});
const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);

const dataForm = ref(<any>{});
// 当前显示类型
const currentType = ref("view");
// 详情id
const detailId = ref("");

const init = (id: string, type: string) => {
  visible.value = true;
  detailId.value = id;
  orderInfo.value = {};
  currentType.value = type;
  baseService.get("/flowable/withdraw").then((res) => {
    defaultSet.bondMin = res.data?.bondMin || undefined;
  });
};

// 提交回复
const orderInfo = ref();
const replyLoading = ref(false);
const dataFormSubmitHandle = () => {
  if (!dataForm.value.amount) {
    return ElMessage.error("请输入充值金额");
  }
  if (+dataForm.value.amount < +defaultSet.bondMin && defaultSet.bondMin != 0) {
    return ElMessage.error("最低缴费" + defaultSet.bondMin + "元");
  }
  replyLoading.value = true;
  baseService
    .get("/finance/tenantfinance/recharge/create", { amount: dataForm.value.amount, type: currentType.value == "0" ? 2 : 3 })
    .then((res_) => {
      if (res_.code == 0) {
        orderInfo.value = res_.data;
        // ElMessage.success("请刷新页面");

        refreshPage();
      }
    })
    .finally(() => {
      replyLoading.value = false;
      dataForm.value.amount = undefined;
    });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("refresh");
};
const refreshPage = () => {
  closeDialog();
  router.push("/cashRegister?orderId=" + orderInfo.value.orderId + "&payAmount=" + orderInfo.value.payAmount + "&amount=" + dataForm.value.amount + "&backUrl=/tenantMenu/billingInformation/index");
  dataForm.value.amount = undefined;
  orderInfo.value = {};
};

defineExpose({
  init
});
</script>
  <style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}
.el-alert {
  padding: 0;
  background: none;
}
</style>
  