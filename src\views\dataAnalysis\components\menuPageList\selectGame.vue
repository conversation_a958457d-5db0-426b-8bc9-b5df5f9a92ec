<template>
    <div class="card_analysis mt-12"
        style="background: linear-gradient(173deg, rgba(217, 224, 247, 0.34) 0%, rgba(217, 224, 247, 0.17) 100%), #f7f8fa">
        <div class="header_analysis">
            <div class="header_analysis_left flx-align-center">
                <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3.4207 0.666687H12.5874C12.8496 0.666687 13.0966 0.790179 13.254 1.00002L15.504 4.00002V16.5C15.504 16.9603 15.131 17.3334 14.6707 17.3334H1.33736C0.877128 17.3334 0.504028 16.9603 0.504028 16.5V4.00002L2.75403 1.00002C2.91141 0.790179 3.1584 0.666687 3.4207 0.666687ZM13.4207 4.00002L12.1707 2.33335H3.83736L2.58736 4.00002H13.4207ZM5.50403 7.33335H3.83736V9.00002C3.83736 11.3012 5.70285 13.1667 8.00405 13.1667C10.3052 13.1667 12.1707 11.3012 12.1707 9.00002V7.33335H10.504V9.00002C10.504 10.3808 9.38471 11.5 8.00405 11.5C6.6233 11.5 5.50403 10.3808 5.50403 9.00002V7.33335Z"
                        fill="#4165D7" />
                </svg>
                <div class="header_analysis_title">回收订单经营数据</div>
            </div>
            <div class="header_analysis_right flx-align-center">
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId" :list="recyclingChanneList" labelKey="channelName"
                    valueKey="channelId" placeholder="回收渠道" class="CoverageStyle" @change="getRecyclingOrderManageData"
                    @clear="getRecyclingOrderManageData" clearable> </NyDropdownMenu> -->
                <NyDropdownMenu v-model="dataForm.purchaseEmployeeId" :list="employeeList" labelKey="employeeName"
                    valueKey="employeeId" placeholder="回收员工" class="CoverageStyle" @change="getRecyclingOrderManageData"
                    @clear="getRecyclingOrderManageData" clearable></NyDropdownMenu>
                <el-date-picker v-model="recyclingTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px; border-radius: 20px; margin-left: 12px"
                    @change="getRecyclingOrderManageData" />
            </div>
        </div>
        <div class="center_analysis">
            <div class="listMap" v-for="(item, index) in analysisList" :key="index">
                <div class="listMap_label flx-align-center">
                    <span>{{ item.label }}</span>
                    <el-tooltip effect="dark" :content="item.tip" placement="top-end">
                        <img src="/src/assets/icons/svg/question-line.svg" style="width: 14px; height: 14px"
                            v-if="item.tip" />
                    </el-tooltip>
                </div>
                <div class="listMap_value" style="color: #4165d7">{{ item.value }}</div>
            </div>
        </div>
    </div>
    <div class="card_analysis mt-12"
        style="background: linear-gradient(173deg, rgba(230, 254, 234, 0.34) 0%, rgba(230, 254, 234, 0.17) 100%), #f7f8fa">
        <div class="header_analysis">
            <div class="header_analysis_left flx-align-center">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M2.25162 13.8863L5.51082 10.6272L7.86783 12.9842L11.6771 9.17491L13.1712 10.669V6.50234H9.00449L10.4986 7.99642L7.86783 10.6272L5.51082 8.27008L1.38926 12.3917C0.927709 11.3562 0.671143 10.2092 0.671143 9.00233C0.671143 4.39997 4.40211 0.669006 9.00449 0.669006C13.6068 0.669006 17.3378 4.39997 17.3378 9.00233C17.3378 13.6047 13.6068 17.3357 9.00449 17.3357C6.22638 17.3357 3.76578 15.9762 2.25162 13.8863Z"
                        fill="#00C568" />
                </svg>
                <div class="header_analysis_title">销售订单经营数据</div>
            </div>
            <div class="header_analysis_right flx-align-center">
                <!-- <NyDropdownMenu v-model="dataForm.salesChannelId" :list="salesChannelList" labelKey="channelName"
                    valueKey="channelId" placeholder="销售渠道" class="CoverageStyle" clearable @change="getSalesOrderManageData"></NyDropdownMenu> -->
                <NyDropdownMenu v-model="dataForm.saleEmployeeId" :list="employeeList" labelKey="employeeName"
                    valueKey="employeeId" placeholder="销售员工" class="CoverageStyle" clearable @change="getSalesOrderManageData"></NyDropdownMenu>
                <el-date-picker v-model="salesTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px; border-radius: 20px; margin-left: 12px" @change="getSalesOrderManageData" />
            </div>
        </div>
        <div class="center_analysis">
            <div class="listMap" v-for="(item, index) in saleOrder" :key="index">
                <div class="listMap_label flx-align-center">
                    <span>{{ item.label }}</span>
                    <el-tooltip effect="dark" :content="item.tip" placement="top-end">
                        <img src="/src/assets/icons/svg/question-line.svg" style="width: 14px; height: 14px"
                            v-if="item.tip" />
                    </el-tooltip>
                </div>
                <div class="listMap_value" style="color: #00c568">{{ item.value }}</div>
            </div>
        </div>
    </div>
    <div class="card_analysis mt-12"
        style="background: linear-gradient(174deg, rgba(232, 230, 254, 0.34) 0%, rgba(217, 224, 247, 0.17) 100%), #f7f8fa">
        <div class="header_analysis">
            <div class="header_analysis_left flx-align-center">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M17.5 6.66671C18.4205 6.66671 19.1667 7.4129 19.1667 8.33337V11.6667C19.1667 12.5872 18.4205 13.3334 17.5 13.3334H16.6151C16.205 16.622 13.3997 19.1667 10 19.1667V17.5C12.7615 17.5 15 15.2615 15 12.5V7.50004C15 4.73862 12.7615 2.50004 10 2.50004C7.23862 2.50004 5.00004 4.73862 5.00004 7.50004V13.3334H2.50004C1.57957 13.3334 0.833374 12.5872 0.833374 11.6667V8.33337C0.833374 7.4129 1.57957 6.66671 2.50004 6.66671H3.38495C3.79503 3.37812 6.60036 0.833374 10 0.833374C13.3997 0.833374 16.205 3.37812 16.6151 6.66671H17.5ZM6.46624 13.1541L7.34969 11.7406C8.11805 12.2219 9.02654 12.5 10 12.5C10.9735 12.5 11.882 12.2219 12.6504 11.7406L13.5339 13.1541C12.5094 13.7958 11.298 14.1667 10 14.1667C8.70204 14.1667 7.49072 13.7958 6.46624 13.1541Z"
                        fill="#722ED1" />
                </svg>
                <div class="header_analysis_title">售后订单经营数据</div>
            </div>
            <div class="header_analysis_right">
                <el-date-picker v-model="operatingHours" type="daterange" start-placeholder="开始时间"
                    end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px; border-radius: 20px" @change="getAfterSaleOrderManageData" />
            </div>
        </div>
        <div class="center_analysis">
            <div class="listMap" v-for="(item, index) in AfterSalesOrder" :key="index">
                <div class="listMap_label flx-align-center" style="cursor: pointer;" @click="toPage">
                    <span>{{ item.label }}</span>
                    <el-icon color="#4E5969" size="14">
                        <ArrowRight />
                    </el-icon>
                </div>
                <div class="listMap_value" style="color: #722ed1">{{ item.value }}</div>
            </div>
        </div>
    </div>
    <div class="card_analysis mt-12" style="background: #f7f8fa">
        <div class="header_analysis" style="padding: 22px 20px 6px 20px">
            <div class="header_analysis_left flx-align-center">
                <div class="header_analysis_title" style="font-size: 20px; margin-left: 0px">营销数据趋势图</div>
            </div>
            <div class="header_analysis_right flx-align-center">
                <div class="legend">
                    <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData" :key="index"
                        @change="changeShow"></el-checkbox>
                </div>
                <el-date-picker v-model="reportTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 220px; border-radius: 20px"
                    @change="getMarketingDataTrend" />
            </div>
        </div>
        <div class="header_describe">实时营销指标趋势可视化分析与订单对比分析</div>
        <div class="charts">
            <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";
import { formatDate } from "@/utils/method";
import { useRouter } from 'vue-router'

const router = useRouter();
const recyclingChanneList = ref(); // 回收渠道列表
const salesChannelList = ref(); // 销售渠道列表
const employeeList = ref(); // 选择员工列表

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    purchaseEmployeeId: "",
    salesChannelId: "",
    saleEmployeeId: "",
});

// 回收订单经营数据
const analysisList = ref([
    { label: "新增回收订单", value: "", tip: "已回收的库存订单数量，不包含【已取消】的数据" },
    { label: "回收总支出(元)", value: "", tip: "" },
    // { label: "转销售订单", value: "", tip: "回收后已经被卖出的库存订单数，不包含【已取消】或【已退款】的数据" },
    // { label: "被销总金额(元)", value: "", tip: "" },
    // { label: "被销毛利润(元)", value: "", tip: "" },
    // { label: "被销转化率", value: "", tip: "被销转化率	指定条件下，【被销订单数】占【回收订单总数】的百分比" },
    // { label: "被销毛利率", value: "", tip: "【被销毛利率】占【被销总金额】的百分比" },
    { label: "转售后订单(次)", value: "", tip: "对应库存产生的售后次数" },
    { label: "已退款订单数", value: "", tip: "" },
    { label: "已亏损订单数", value: "", tip: "售后产生退款后，卖家赔偿金额大于平台回收金额的订单数量" },
    { label: "售后退款率", value: "", tip: "【已退款订单数】占【售后订单数】的百分比" },
    { label: "售后亏损率", value: "", tip: "售后退款导致的亏损订单，占售后订单总数的百分比" }
]);

// 销售订单经营数据
const saleOrder = ref([
    { label: "新增销售订单", value: "265", tip: "销售过的订单数量，不包含【已取消】的数据" },
    { label: "销售总金额(元)", value: "87,765", tip: "" },
    { label: "销售毛利润(元)", value: "-22,417.59", tip: "" },
    { label: "销售毛利率", value: "-25.54%", tip: "【销售毛利润】占【销售总金额】的百分比" },
    { label: "转售后订单(次)", value: "0", tip: "对应销售订单产生的售后次数" },
    { label: "已退款订单数", value: "66", tip: "" },
    { label: "已亏损订单数", value: "0", tip: "【已退款订单数】中对应的售后订单中，【赔偿金额】大于【销售金额】的订单数量" },
    { label: "售后退款率", value: "0", tip: "【已退款订单数】占【售后订单数】的百分比" },
    { label: "直售退款金额", value: "0", tip: "直售商品已完成退款的金额" },
    { label: "真实利润", value: "0", tip: "成交价-回收价-退款金额" }
]);

// 售后订单经营数据
const AfterSalesOrder = ref([
    { label: "新增平台直售售后", value: "24", tip: "" },
    { label: "新增平台代售售后", value: "8", tip: "" },
    { label: "新增合作商回收售后", value: "6", tip: "" }
]);

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: "回收订单",
        type: "line",
        // stack: "Total",
        smooth: true,
        itemStyle: {
            color: "#4165D7"
        },
        lineStyle: {
            width: 3,
            color: "#4165D7"
        },
        showSymbol: false,
        areaStyle: {
            opacity: 0.5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#FFFFFF" },
                { offset: 0, color: "#64A2FF " }
            ])
        },

        emphasis: {
            focus: "series"
        },
        data: []
    },
    {
        name: "销售订单",
        type: "line",
        // stack: "Total",
        smooth: true,
        itemStyle: {
            color: "#00B42A"
        },
        lineStyle: {
            width: 3,
            color: "#00B42A"
        },
        showSymbol: false,
        areaStyle: {
            opacity: 0.5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#FFFFFF" },
                { offset: 0, color: "#cee9e1 " }
            ])
        },
        emphasis: {
            focus: "series"
        },
        data: []
    },
    {
        name: "售后订单",
        type: "line",
        // stack: "Total",
        smooth: true,
        itemStyle: {
            color: "#722ED1"
        },
        lineStyle: {
            width: 3,
            color: "#722ED1"
        },
        showSymbol: false,
        areaStyle: {
            opacity: 0.5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#FFFFFF" },
                { offset: 0, color: "#8364FF " }
            ])
        },
        emphasis: {
            focus: "series"
        },
        data: []
    }
]);
// 图例数据
const legendData = ref([
    { name: "回收订单", color: "#4165D7", show: true },
    { name: "销售订单", color: "#00B42A", show: true },
    { name: "售后订单", color: "#722ED1", show: true }
]);
// 营销数据趋势图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "cross",
                label: {
                    backgroundColor: "#6a7985"
                }
            }
        },
        grid: {
            left: "3%",
            right: "3%",
            top: "4%",
            bottom: "12%",
            containLabel: true
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: "value"
            }
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 100,
            bottom: "6%",
            height: 15
        }]
    }
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 4
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 4
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    console.log(filteredSeries);


    GameSalesStatisticsChart(filteredSeries);
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number, list: any) => {
    // 销售渠道
    if (channelType == 0) {
        salesChannelList.value = list;
    }
    // 回收渠道
    if (channelType == 1) {
        recyclingChanneList.value = list;
    }
};

// 员工列表
const getEmployeeList = (list: any) => {
    employeeList.value = list;
};

// 
const toPage = () =>{
    router.push('/order/afterSales/index')
}

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    recyclingTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    salesTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    operatingHours.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    reportTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getRecyclingOrderManageData();
    getSalesOrderManageData();
    getAfterSaleOrderManageData();
    getMarketingDataTrend();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
});

// 回收订单经营数据
const recyclingTime = ref();
const getRecyclingOrderManageData = () => {    
    baseService.post("/dataAnalysis/recyclingOrderManageData", {
        ...dataForm.value,
        startTime: recyclingTime.value ? recyclingTime.value[0] + " 00:00:00" : "",
        endTime: recyclingTime.value ? recyclingTime.value[1] + " 23:59:59" : ""
    }).then((res) => {
        const data = res.data;
        analysisList.value[0].value = data.newRecoveryOrderCount ? data.newRecoveryOrderCount : "0";
        analysisList.value[1].value = data.totalRecoveryExpense;
        // analysisList.value[2].value = data.resaleOrderCount;
        // analysisList.value[3].value = data.totalRejectedAmount;
        // analysisList.value[4].value = data.rejectedGrossProfit;
        // analysisList.value[5].value = data.rejectedConversionRate + "%";
        // analysisList.value[6].value = data.rejectedGrossProfitMargin + "%";
        analysisList.value[2].value = data.afterSaleOrderCount;
        analysisList.value[3].value = data.refundedOrderCount;
        analysisList.value[4].value = data.deliveredOrderCount;
        analysisList.value[5].value = data.afterSaleRefundRate + "%";
        analysisList.value[6].value = data.afterSaleClaimableRate + "%";
    });
};

// 销售订单经营数据
const salesTime = ref();
const getSalesOrderManageData = () => {
    baseService.post("/dataAnalysis/salesOrderManageData", {
        ...dataForm.value,
        startTime: salesTime.value ? salesTime.value[0] + " 00:00:00" : "",
        endTime: salesTime.value ? salesTime.value[1] + " 23:59:59" : ""
    }).then((res) => {
        const data = res.data;
        saleOrder.value[0].value = data.newSalesOrderCount;
        saleOrder.value[1].value = data.totalSalesAmount;
        saleOrder.value[2].value = data.salesGrossProfit;
        saleOrder.value[3].value = data.salesGrossProfitMargin + "%";
        saleOrder.value[4].value = data.afterSaleOrderCount;
        saleOrder.value[5].value = data.refundedOrderCount;
        saleOrder.value[6].value = data.lostOrderCount;
        saleOrder.value[7].value = data.afterSaleRefundRate + "%";
        saleOrder.value[8].value = data.refundedAmount;
        saleOrder.value[9].value = data.realProfit;
    });
};

// 售后订单经营数据
const operatingHours = ref();
const getAfterSaleOrderManageData = () => {
    baseService.post("/dataAnalysis/afterSaleOrderManageData", {
        ...dataForm.value,
        startTime: operatingHours.value ? operatingHours.value[0] + " 00:00:00" : "",
        endTime: operatingHours.value ? operatingHours.value[1] + " 23:59:59" : ""
    }).then((res) => {
        const data = res.data;
        AfterSalesOrder.value[0].value = data.newPlatformSelfServiceAfterSale;
        AfterSalesOrder.value[1].value = data.newPlatformConsignmentAfterSale;
        AfterSalesOrder.value[2].value = data.newCooperativeMerchantAfterSale;
    });
};

// 营销数据趋势图
const reportTime = ref()
const getMarketingDataTrend = () => {
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/marketingDataTrend", {
        ...dataForm.value,
        startTime: reportTime.value ? reportTime.value[0] + " 00:00:00" : "",
        endTime: reportTime.value ? reportTime.value[1] + " 23:59:59" : ""
    }).then((res) => {
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    });
};

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getRecyclingOrderManageData();
    getSalesOrderManageData();
    getAfterSaleOrderManageData();
    getMarketingDataTrend();
};

defineExpose({
    init,
    getRecyclingChanneList,
    getEmployeeList
});
</script>

<style lang="less" scoped>
.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {

            // gap: 12px;
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
                    color: #1d2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165d7;
                        border-color: #4165d7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #00b42a;
                        border-color: #00b42a;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ed1;
                        border-color: #722ed1;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4e5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.CoverageStyle {
    :deep(.ny_dropdown_menu) {
        border-radius: 20px;
    }

    :deep(.ny_dropdown_menu_border) {
        border-radius: 20px;
    }
}
</style>
