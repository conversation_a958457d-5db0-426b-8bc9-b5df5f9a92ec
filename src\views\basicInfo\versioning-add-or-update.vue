<template>
    <el-drawer v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')"  size="40%" class="ny-drawer">
        <el-card>
            <el-form label-position="top" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
                <el-row>
                  <el-col :span="24">
                        <el-form-item label="版本名称" prop="wgtUrl">
                            <el-input v-model="dataForm.wgtUrl" placeholder="APP版本号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="版本号" prop="version">
                            <el-input type="number" v-model="dataForm.version" placeholder="APP版本号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="更新内容" prop="info">
                            <el-input v-model="dataForm.info" placeholder="更新内容"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="强制更新" prop="mandatory">
                          <el-switch v-model="dataForm.mandatory" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="开启" prop="status">
                          <el-switch v-model="dataForm.status" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="上传安装包" prop="status">
                          <ny-upload-file 
                            ref="fileRef" 
                            :limit="3" 
                            v-model:fileSrc="dataForm.apkUrl" 
                            widthUpload="400px" 
                            heightUpload="185px" 
                            tip="上传文件后缀为.apk 或者 .wgt格式；" 
                            fileSize="50"
                            :isOneLine="true" 
                            :iszip="false"
                            accept=".apk,.wgt"
                          ></ny-upload-file>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>
        <template v-slot:footer>
            <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
    id: null,
    wgtUrl:'',
    version: '',
    info: '',
    mandatory: false,
    status: false,
    apkUrl: '',
});

const rules = ref({
  version: [{ required: true, message: '请输入版本号', trigger: 'change',},],
});

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/globalSetting/sysapp", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    }).finally(() => {
        btnLoading.value = false;
    })
  });
};


const init = (id?: any) => {
  visible.value = true;
  dataForm.id = id;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    dataForm.apkUrl = '';
  }
  if (id) {
    getInfo();
  }
};

// 获取信息
const getInfo = () => {
  baseService.get("/globalSetting/sysapp/" + dataForm.id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

defineExpose({
  init
});
</script>

<style lang='less' scoped>

</style>