<template>
  <el-dialog :footer="null" v-model="visible" :title="dataForm.id ? '编辑任务' : '新增任务'" width="800">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <el-descriptions style="width: 100%; min-height: 198px;" border :column="2">
        <el-descriptions-item label="任务类型">
          <template #label>
            <span>任务类型<span style="color: red">*</span></span>
          </template>
          <el-form-item label="任务类型" prop="type">
            <el-select v-model="dataForm.type" placeholder="请选择任务类型">
              <el-option label="绑定数量查询" :value="1" />
              <el-option label="自动查话费" :value="2" />
              <el-option label="自动互拨电话" :value="3" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="间隔周期(天)">
          <template #label>
            <span>间隔周期(天)<span style="color: red">*</span></span>
          </template>
          <el-form-item label="间隔周期(天)" prop="intervalDay"> <el-input-number v-model="dataForm.intervalDay" :step="1" /> </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="启用">
          <template #label>
            <span>启用<span style="color: red">*</span></span>
          </template>
          <el-form-item label="启用" prop="status"> <el-switch inline-prompt v-model="dataForm.status" :active-value="1" :inactive-value="0"  active-text="是" inactive-text="否" /> </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="执行时间">
          <el-form-item label="执行时间" prop="value16"> <el-date-picker :prefix-icon="Calendar" v-model="dataForm.executeTime" type="date" placeholder="请选择执行时间" /> </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-dialog>
</template>
    
<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { Calendar } from "@element-plus/icons-vue";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const rules = {
  value1: [{ required: true, message: "请选择任务类型", trigger: "blur" }],
  value2: [{ required: true, message: "请输入", trigger: "blur" }],
  value3: [{ required: true, message: "请选择", trigger: "blur" }]
};

const visible = ref(false);
const oparateType = ref(1);
const dataForm = ref({
  type: "",
  intervalDay: null,
  status: 1,
  executeTime: "",
});

const init = (id?: any) => {
  visible.value = true;
  if (id) {
    getDetails(id);
  }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get("/mobile/mobileTaskConfig/" + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/mobile/mobileTaskConfig", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emit("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
  <style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>
    