<template>
  <!-- 买家详情 -->
  <el-drawer v-model="visible" title="买家详情" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%">
    <el-descriptions class="descriptions-label-140" :column="1" border>
      <el-descriptions-item label="昵称">{{ resData.nickname }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ resData.realMobile || "-" }}</el-descriptions-item>
      <el-descriptions-item label="用户ID">{{ resData.id }}</el-descriptions-item>
      <el-descriptions-item label="用户账号">{{ resData.username }}</el-descriptions-item>
      <el-descriptions-item label="头像">
        <el-image :src="resData.avatar || settingStore.info.backendDefaultAvatar" style="width: 96px; height: 96px"></el-image>
      </el-descriptions-item>
      <el-descriptions-item label="微信">{{ resData.wechatName || "-" }}</el-descriptions-item>
      <el-descriptions-item label="邮箱">{{ resData.email || "-" }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ resData.sexual == 2 ? '女' : '男' }}</el-descriptions-item>
      <el-descriptions-item label="生日">{{ resData.birthday || "-" }}</el-descriptions-item>
      <el-descriptions-item label="身份证">{{ resData.idNo || "-" }}</el-descriptions-item>
      <el-descriptions-item label="实名状态">{{ resData.isRealname == 1 ? "已实名" : "未实名" }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag v-if="resData.state != '1'" type="success">正常</el-tag>
        <el-tag v-else type="danger">禁用</el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup lang="ts">
import baseService from "@/service/baseService";
import { useSettingStore } from "@/store/setting";
import { ref, defineExpose } from "vue";

const visible = ref(false);
const settingStore = useSettingStore();

const resData = ref({
  name: "",
  phone: "",
  userId: "",
  userAccount: "",
  avatar: "",
  wechat: "",
  email: "",
  sex: "",
  birthday: "",
  iDCard: "",
  realNameStatus: "",
  status: ""
});

const init = (id: any) => {
  visible.value = true;
  baseService.get("/tb/user/info", { id }).then((res) => {
    resData.value = res?.data;
  });
};

defineExpose({
  init
});
</script>