<template>
    <el-dialog v-model="visible" width="1200px" title="消息通知" :close-on-click-modal="false"
        :close-on-press-escape="false" :append-to-body="true" class="message-notification-dialog"
        @close="imStore.showMessageNotification = false">
        <template #header="{ close, titleId, titleClass }">
            <div style="display: inline-block">
                <ny-button-group :list="[
                        { dictLabel: '服务消息', dictValue: '0' },
                        { dictLabel: '系统消息', dictValue: '1' }
                    ]" v-model="statusTab"
                    style="margin-top: 12px;"
                ></ny-button-group>
            </div>
        </template>
        <!-- IM服务消息 -->
        <div class="message-notification-dialog-content" v-show="statusTab == '0'">
            <div class="message-list" v-loading="listLoading">
                <div class="top flx-justify-between">
                    <div class="tabbar">
                        <div class="tabbar-item" v-for="(item, index) in msgStatusList" :key="index"
                            :class="{ active: item.value == currentStatus }" @click="currentStatusHandle(item.value)">
                            {{ item.label }}({{ item.number }})
                        </div>
                    </div>

                    <el-text v-show="currentStatus == 0 && messageList.length" class="pointer" type="primary"
                        @click="allReadHandle">全部已读</el-text>
                    <el-text v-show="currentStatus == 1 && messageList.length" class="pointer" type="primary"
                        @click="emptiedHandle">清空</el-text>
                </div>

                <el-scrollbar height="calc(100% - 56px)" scroll-y class="scrollbar">
                    <ny-no-data v-if="!messageList || !messageList.length" text="暂无数据"></ny-no-data>
                    <div class="message-list-item flx" v-for="(item, index) in messageList" :key="index"
                        :class="{ active: item.id === currentMessageId }" @click="msgItemHandle(item.id)">
                        <el-image class="image" :src="messageNotificationType[item.type]"></el-image>
                        <div class="item-right">
                            <el-badge is-dot :hidden="item.status ? true : false">
                                <div class="titlte">{{ item.title }}</div>
                            </el-badge>

                            <div class="desc sle">{{ filterHtml(item.message) }}</div>
                            <div class="time">{{ dayjs(item.createDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="message-content" v-loading="detailLoading">
                <ny-no-data v-if="!msgDetail.id" type="2" text="请点击消息列表查看"></ny-no-data>

                <template v-else>
                    <div class="content-top">
                        <div class="title">{{ msgDetail.title }}</div>
                        <div class="time">{{ dayjs(msgDetail.createDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                    </div>

                    <el-scrollbar style="height: calc(100% - 124px)" scroll-y class="scrollbar">
                        <div class="message-text" v-html="msgDetail.message"></div>
                    </el-scrollbar>

                    <div class="flx-justify-end" v-if="msgDetail.type != 'CU:cmdNotice'">
                        <el-button type="primary" @click="jumpDetail">立即查看</el-button>
                    </div>
                </template>
            </div>

        </div>

        <!-- WS系统消息 -->
        <wsMessage v-if="statusTab == '1'"></wsMessage>

        <!-- 消息全部已读确认 -->
        <secondary-confirmation :show="readConfirmationVisible" title="消息全部已读确认" confirmText="确认已读" type="primary"
            :showModal="false" :loading="btnLoading" @close="readConfirmationVisible = false"
            @confirm="readConfirmHandle">
            <template #default>
                <div class="text">
                    <p>您是否确认要将所有未读消息都标记为已读？这个操作会将您的未读消息列表中所有的未读消息，一次性更新为已读状态。</p>
                </div>
            </template>
        </secondary-confirmation>


        <!-- 消息清空确认 -->
        <secondary-confirmation :show="emptiedVisible" title="消息清空确认" confirmText="确认清空" :showModal="false"
            :loading="btnLoading" @close="emptiedVisible = false" @confirm="emptiedConfirmHandle">
            <template #default>
                <div class="text">
                    <p>您是否确认要执行删除所有消息的操作？这个操作将会永久移除您的所有消息记录，一旦删除将无法恢复。</p>
                </div>
            </template>
        </secondary-confirmation>

    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { messageNotificationType, filterHtml } from '@/utils/imTool';
import { useAppStore } from "@/store/index";
import { useImStore } from "@/store/im";
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import SecondaryConfirmation from "@/views/order/components/SecondaryConfirmation.vue";
import baseService from '@/service/baseService';
import dayjs from "dayjs";
import wsMessage from './wsMessage.vue';

const appStore = useAppStore();
const imStore = useImStore();
const router = useRouter();

const visible = computed(() => {
    return imStore.showMessageNotification;
})

const timer = ref();
const statusTab = ref('0');

watch(() => imStore.messageNotificationInfo, (newVal) => {
    if (newVal && newVal.id) {
        // getList();
        currentStatus.value = imStore.messageNotificationInfo.status;
        msgItemHandle(imStore.messageNotificationInfo.id);
    }
})

watch(() => imStore.systemMessage, (newVal) => {
    if (timer.value) clearTimeout(timer.value);
    timer.value = setTimeout(() => {
        getList();
    }, 1000);
})

const currentStatus = ref('0');
const msgStatusList = computed(() => {
    let unreadCount = messageListAll.value.filter(item => !item.status).length;

    return [
        { label: '未读', value: '0', number: unreadCount },
        { label: '已读', value: '1', number: messageListAll.value.length - unreadCount },
    ]
});

const currentStatusHandle = (val: any) => {
    currentStatus.value = val;
    messageList.value = currentStatus.value == '0' ? messageListAll.value.filter(item => !item.status) : messageListAll.value.filter(item => item.status);
}



// 消息列表
const messageList = ref(<any>[]);
const messageListAll = ref(<any>[]);
const listLoading = ref(false);
const getList = () => {
    listLoading.value = true;
    baseService.get('/im/message/page', {
        page: 1,
        limit: 999,
        order: "desc",
        orderField: "create_date"
    }).then(res => {
        if (res.code == 0) {
            let list = res.data.list.map(item => {
                return {
                    ...item,
                    message: item.message.replaceAll('nickname', appStore.state.user.realName)
                }
            });

            messageListAll.value = JSON.parse(JSON.stringify(list));
            imStore.systemMessageList = messageListAll.value.filter(item => !item.status).slice(-5);
            imStore.unreadMessageCount = list.filter(item => !item.status).length;
            currentStatusHandle(0);
        }
    }).finally(() => {
        listLoading.value = false;
    })
}

getList();

// 消息全部已读
const readConfirmationVisible = ref(false);
const btnLoading = ref(false);
const readConfirmHandle = () => {
    btnLoading.value = true;
    baseService.put('/im/message').then(res => {
        if (res.code == 0) {
            getList();
            ElMessage.success('全部已读操作成功');
            imStore.unreadMessageCount = 0;
            readConfirmationVisible.value = false;
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}

const allReadHandle = () => {
    readConfirmationVisible.value = true;
}


// 清空
const emptiedVisible = ref(false);
const emptiedHandle = () => {
    emptiedVisible.value = true;
}
const emptiedConfirmHandle = () => {
    btnLoading.value = true;
    baseService.delete('/im/message').then(res => {
        if (res.code == 0) {
            getList();
            currentMessageId.value = '';
            msgDetail.value = {};
            emptiedVisible.value = false;
            ElMessage.success('清空操作成功');
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}


// 查看详情
const currentMessageId = ref();
const msgDetail = ref(<any>{});
const detailLoading = ref(false);
const msgItemHandle = (id: number) => {
    currentMessageId.value = id;
    detailLoading.value = true;
    baseService.get('/im/message/' + id).then(res => {
        if (res.code == 0) {
            msgDetail.value = res.data;
            msgDetail.value.message = res.data.message.replace('nickname', appStore.state.user.realName)
            readHandle();

        }
    }).finally(() => {
        detailLoading.value = false;
    })
}


// 消息已读
const readHandle = () => {
    let index = messageList.value.findIndex(item => item.id == currentMessageId.value);
    if (!messageList.value[index].status) {
        messageList.value[index].status = 1;
        messageListAll.value[index].state = 1;
        imStore.unreadMessageCount--;
    }
}


// 跳转到对应页面
const jumpDetail = () => {
    let type = msgDetail.value.type;
    imStore.showMessageNotification = false;
    switch (type) {
        case 'CU:cmdAPIExpire':
        case 'CU:cmdApiOpen':
            // 跳转 API 管理页面
            router.push('/plugin/api/index')
            break;
        case 'CU:cmdPaidExpire':
        case 'CU:cmdPaidOpen':
            // 跳转插件功能页面
            router.push('/plugin/paid/paidfunction')
            break;
        case 'CU:cmdBalance':
        case 'CU:cmdRecharge':
            // 跳转我的钱包页面
            router.push('/bill/specification')
            break;
        case 'CU:cmdBargain':
            // 跳转议价中心页面
            router.push('/shop/bargain/index')
            break;
        case 'CU:cmdContractSale':
            // 跳转到销售合同
            router.push('/order/sell/index')
            break;
        case 'CU:cmdContractPurchase':
            // 跳转到收购合同
            router.push('/order/acquisition/index')
            break;
        case 'CU:cmdSale':
            // 跳转该商品对应订单管理页面
            router.push('/order/sell/index')
            break;
        case 'CU:cmdRetrieve':
            // 跳转账号被找回页  
            router.push('/utilityTools/shopAccountFound/index')
            break;
        default:
            break;
    }
}

</script>

<style lang="scss">
.message-notification-dialog {
    padding: 0 !important;
    height: 75%;

    .el-dialog__header {
        height: 64px;
        line-height: 64px;
        padding: 0 24px 0 12px;

        .el-dialog__headerbtn {
            height: 64px;
        }
    }

    .el-dialog__body {
        height: calc(100% - 64px);
    }

    .message-notification-dialog-content {
        height: 100%;
        background: #F2F4F7;
        padding: 12px;
        display: flex;

        .message-list {
            height: 100%;
            width: 372px;
            background: #fff;
            border-radius: 4px;

            .top {
                height: 56px;
                padding: 0 20px;
                border-bottom: 1px solid #E5E6EB;

                .tabbar {
                    display: flex;

                    .tabbar-item {
                        height: 32px;
                        line-height: 32px;
                        border-radius: 100px;
                        color: #4E5969;
                        padding: 0 16px;
                        margin-right: 10px;
                        cursor: pointer;

                        &.active {
                            background: #F0F2F5;
                            color: var(--el-color-primary);
                        }
                    }
                }
            }

            .message-list-item {
                padding: 14px 20px;
                border-bottom: 1px solid #E5E6EB;
                cursor: pointer;

                &:hover,
                &.active {
                    background-color: #f9f9f9;
                }

                &:last-child {
                    border-bottom: none;
                }

                .image {
                    width: 40px;
                    height: 40px;
                }

                .item-right {
                    width: calc(100% - 56px);
                    margin-left: 16px;

                    .el-badge {
                        width: 100%;
                    }

                    .titlte {
                        font-weight: 500;
                        font-size: 14px;
                        line-height: 22px;
                    }

                    .desc {
                        line-height: 22px;
                        color: #606266;
                        padding-top: 2px;
                    }

                    .time {
                        color: #909399;
                        line-height: 22px;
                        padding-top: 6px;
                    }
                }
            }

            .scrollbar,
            .el-scrollbar__view {
                height: 100%;
            }
        }

        .message-content {
            flex: 1;
            height: 100%;
            background: #fff;
            margin-left: 12px;
            border-radius: 4px;
            padding: 20px;

            .message-text {
                line-height: 22px;
            }

            .content-top {
                height: 72px;
                border-bottom: 1px solid #EBEEF5;

                .title {
                    font-size: 18px;
                    line-height: 26px;
                }

                .time {
                    color: #909399;
                    line-height: 22px;
                    padding-top: 4px;
                }
            }

            .scrollbar {
                margin: 12px 0;
            }
        }
    }
}
</style>
