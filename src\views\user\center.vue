<template>
  <div style="margin: auto; display: flex; width: fit-content; margin-top: 15px">
    <div class="leftPart">
      <ny-upload-file style="width: fit-content" :showFileList="false" v-model:fileSrc="dataForm.headUrl" :alwaysShow="true" :limit="1" :fileSize="5" :isSelfSty="false" uploadPreText="" :isDrag="false" :isSimple="true" tip="" accept="image/*">
        <template #content>
          <div style="position: relative">
            <el-image style="width: 100px; height: 100px; margin-bottom: 11px; border-radius: 50%" :src="dataForm.headUrl"></el-image>
            <div class="camera">
              <img style="width: 12px; height: 12px" src="https://oss.nyyyds.com/upload/20241212/84ec2e8675894e8bbb017bcc5ed76155.png" />
            </div></div
        ></template>
      </ny-upload-file>
      <div style="margin-bottom: 13px"><span class="titleSpan">用户名：</span>{{ resData.realName }}</div>
      <div style="margin-bottom: 55px">
        <span class="titleSpan">状态：</span><el-tag :type="dataForm.state == 0 ? 'success' : 'danger'">{{ dataForm.state == 0 ? "正常" : "禁用" }}</el-tag>
      </div>
      <template v-if="store.state.user.tenantCode == 10000">
        <div><img src="https://oss.nyyyds.com/upload/20241212/126ee346541b45d5a8ce627006fe97ee.png" alt="" /><span class="titleSpan">所属部门</span></div>
        <div style="margin-bottom: 30px; margin-top: 2px">{{ dataForm.deptName || "-" }}</div>
      </template>
      <div><img src="https://oss.nyyyds.com/upload/20241212/f017800ca2254c74961200d691f7bacf.png" alt="" /><span class="titleSpan">创建时间</span></div>
      <div style="margin-top: 2px">{{ dataForm.createDate }}</div>
    </div>
    <div class="shop_page_card">
      <ny-title title="基本信息" style="padding: 0px 0px 12px 0px" />
      <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top">
        <el-row :gutter="12">
          <template v-if="store.state.user.tenantCode == 10000">
            <el-col :span="24">
              <el-form-item prop="nickname" label="昵称">
                <el-input v-model="dataForm.nickname" placeholder="昵称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="realName" :label="$t('user.realName')">
                <el-input v-model="dataForm.realName" :placeholder="$t('user.realName')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="email" :label="$t('user.email')">
                <el-input v-model="dataForm.email" :placeholder="$t('user.email')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="mobile" :label="$t('user.mobile')">
                <el-input v-model="dataForm.mobile" :placeholder="$t('user.mobile')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="gender" :label="$t('user.gender')">
                <ny-radio-group v-model="dataForm.gender" dict-type="gender"></ny-radio-group>
              </el-form-item>
            </el-col>
          </template>
          <template v-else>
            <el-col :span="24">
              <el-form-item prop="nickname" label="合作商昵称">
                <el-input v-model="dataForm.nickname" placeholder="请输入合作商昵称"></el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24" style="margin-top: 16px">
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="dataFormSubmitHandle" style="margin-left: 8px" type="primary">保存</el-button>
          </el-col>
        </el-row>
      </el-form>
      <ny-title title="安全信息" style="padding: 0px 0px 12px 0px; margin-top: 40px" />
      <div style="margin-top: 12px; display: flex">
        <span style="font-weight: 500; font-size: 14px; color: #4e5969; line-height: 22px; margin-right: 16px">登陆密码</span>
        <div style="padding-bottom: 22px; border-bottom: 1px solid #dcdfe6; display: flex; justify-content: space-between; flex: 1">
          <span style="font-weight: 400; font-size: 14px; color: #4e5969">已设置。</span>
          <el-button link type="primary" @click="handlePassword">修改</el-button>
        </div>
      </div>
    </div>
    <userpassword ref="userpasswordRef" />
  </div>
</template>
  
<script lang="ts" setup>
import { onMounted, reactive, ref, nextTick } from "vue";
import baseService from "@/service/baseService";
import { isEmail, isMobile } from "@/utils/utils";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import userpassword from "@/views/sys/user-update-password.vue";
import { tr } from "element-plus/es/locale";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const store = useAppStore();
const dataFormRef = ref();
const userpasswordRef = ref();
const dataForm = reactive({
  id: "",
  username: "",
  nickname: "",
  headUrl: "",
  deptId: "",
  deptName: "",
  password: "",
  confirmPassword: "",
  realName: "",
  gender: 0,
  state: 0,
  email: "",
  mobile: "",
  postIdList: [] as IObject[],
  status: 1,
  gameIdList: []
});

const validateEmail = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isEmail(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.email") })));
  }
  callback();
};
const validateMobile = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isMobile(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.mobile") })));
  }
  callback();
};
const rules = ref({
  nickname: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  gender: [{ required: true, message: t("validate.required"), trigger: "change" }],
  realName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  email: [{ validator: validateEmail, trigger: "blur" }],
  mobile: [{ validator: validateMobile, trigger: "blur" }]
});
const resetForm = () => {
  if (store.state.user.tenantCode == 10000) {
    dataForm.nickname = undefined;
    dataForm.email = undefined;
    dataForm.mobile = undefined;
    dataForm.gender = undefined;
  }
  dataForm.realName = undefined;
};
const init = (id?: number) => {
  if (id) {
    getInfo(id);
  }
};
const handlePassword = () => {
  nextTick(() => {
    userpasswordRef.value ? userpasswordRef.value.init() : "";
  });
};
const resData = ref({
  realName: ""
});
// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/user/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
    resData.value = { ...dataForm };
  });
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    baseService
      .put("/sys/user", {
        ...dataForm
      })
      .then(() => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            baseService.get("/sys/user/info").then((res) => {
              resData.value = res.data;
              store.upDateUser(res.data);
            });
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

onMounted(() => {
  init(store.state.user.id);
});
</script>

<style lang="less" scoped>
.leftPart {
  width: 320px;
  height: initial;
  padding: 32px;
  padding-bottom: 23px;
  background: #fafafa;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  font-size: 16px;
  color: #2b2d3e;
  line-height: 22px;

  .camera {
    position: absolute;
    bottom: 12px;
    right: -4px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #f2f3f5;
    border: 2px solid #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      margin-right: 0;
    }
  }

  > div {
    display: flex;
    align-items: center;

    img {
      width: 16px;
      height: 16px;
      margin-right: 3px;
    }
  }

  .titleSpan {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #86909c;
    line-height: 22px;
  }
}
.shop_page {
  background-color: #f0f2f5;
  padding: 12px;
}
.shop_page_card {
  width: 720px;
  margin-top: 30px;
  background-color: #fff;
  border-radius: 8px;
  margin-left: 24px;
}
.mod-sys__user {
  .role-list {
    .el-select {
      width: 100%;
    }
  }
}
</style>
<style lang='less'>
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
  }
}
</style>
  