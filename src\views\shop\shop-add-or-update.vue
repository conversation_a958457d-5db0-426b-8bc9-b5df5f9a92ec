<template>
  <el-dialog v-model="visible" fullscreen class="drawer_shop" @close="drawerCloseHandle('close')" append-to-body>
    <template #header>
      <div class="drawer_title">{{ title ? title : !dataForm.id ? $t("add") : $t("update") }}</div>
    </template>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" :validate-on-rule-change="false" v-loading="requestLoading">
      <el-row>
        <el-col :span="12">
          <el-scrollbar style="height: calc(100vh - 138px)">
            <div class="shop_page">
              <div class="shop_page_basic">
                <div class="titleSty">账号基本信息</div>
                <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                  <el-descriptions-item>
                    <template #label
                      ><span>游戏名称<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="游戏名称" prop="gameId">
                      <el-select v-model="dataForm.gameId" placeholder="Select" :disabled="dataForm.id" @change="gamesClick">
                        <el-option v-for="item in gamesList" :key="item.id" :label="item.title" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>商品标题<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="商品标题" prop="title">
                      <el-input v-model="dataForm.title" type="textarea" placeholder="商品标题" :rows="4"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>商品描述<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="商品描述" prop="info">
                      <el-input v-model="dataForm.info" type="textarea" placeholder="温馨提示：商品详情信息很重要，直接影响商品排名，越详细精准排名越靠前" :rows="10" show-word-limit></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>商品主图<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="商品主图" prop="log">
                      <ny-shop-image-upload v-model="dataForm.log" v-if="!screenshotLoading" :key="dataForm.gameId"></ny-shop-image-upload>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>商品详情图<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="商品详情图" prop="imagesList">
                      <ny-shop-image-upload v-model="dataForm.imagesList" :limit="200" :multiple="true" v-if="!screenshotLoading" :key="dataForm.gameId"></ny-shop-image-upload>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>实名情况截图</span></template>
                    <el-form-item label="实名情况截图" prop="authImage">
                      <ny-shop-image-upload v-model="dataForm.authImage" :key="dataForm.gameId"></ny-shop-image-upload>
                    </el-form-item>
                  </el-descriptions-item>
                  <!-- <el-descriptions-item v-if="dataForm.gameCode == 'HPJY'">
                    <template #label><span>营地截图</span></template>
                    <el-form-item label="营地截图" prop="campImage">
                      <ny-shop-image-upload v-model="dataForm.campImage" :key="dataForm.gameId"></ny-shop-image-upload>
                    </el-form-item>
                  </el-descriptions-item> -->
                  <el-descriptions-item>
                    <template #label><span>商品亮点</span></template>
                    <el-form-item prop="highlights">
                      <el-input v-model="dataForm.highlights" type="textarea" :rows="2" placeholder="商品亮点" @change="feedBackInfo" show-word-limit maxlength="2000"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div class="shop_page_basic" style="margin-top: 12px">
                <div class="titleSty">账号商品信息</div>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140" v-if="source == 'shop' || source == 'tenantShop'">
                  <el-descriptions-item>
                    <template #label
                      ><span>游戏账号<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="游戏账号" prop="gameAccount">
                      <el-input v-model="dataForm.gameAccount" placeholder="游戏账号" @blur="BlackNumber(1)"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>游戏密码</span></template>
                    <el-form-item label="游戏密码" prop="gamePassword">
                      <el-input v-model="dataForm.gamePassword" placeholder="游戏密码"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>零售价<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="零售价" prop="price">
                      <el-input-number v-model="dataForm.price" :min="0" :precision="2" :step="1" controls-position="right" style="width: 100%" />
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>手机号码<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="手机号码" prop="phone">
                      <!-- 租户 手机号 手输 -->
                      <el-input v-if="source == 'tenantShop'" v-model="dataForm.phone" type="text" placeholder="手机号码" maxlength="11" show-word-limit></el-input>
                      <!-- @change="BlackNumber(2)" -->
                      <el-select v-else filterable remote :loading="loading" :remote-method="remoteMethod" clearable v-model="dataForm.phone" placeholder="请输入关键字搜索手机号">
                        <el-option v-for="item in phonesList" :key="item.phone" :label="item.phone" :value="item.phone" />
                      </el-select>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label
                      ><span>回收价<span style="color: red">*</span></span></template
                    >
                    <el-form-item label="回收价" prop="acquisitionPrice">
                      <el-input-number v-model="dataForm.acquisitionPrice" :min="0" :precision="2" :step="1" controls-position="right" style="width: 100%" />
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>回收人</span></template>
                    <el-form-item label="回收人" prop="acquisitionUser">
                      <ny-select-search v-model="dataForm.acquisitionUser" labelKey="realName" valueKey="id" url="/sys/user/all" :param="{}" :isList="false" placeholder="回收人" v-if="store.state.isPlatform" />
                      <el-input :placeholder="store.state.user.realName" disabled v-else></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>自编码</span></template>
                    <el-form-item label="自编码" prop="ownCoding">
                      <el-input v-model="dataForm.ownCoding" placeholder="自编码"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>应急手机号</span></template>
                    <el-form-item label="应急手机号" prop="emergencyPhone">
                      <el-input v-model="dataForm.emergencyPhone" placeholder="应急手机号"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                </el-descriptions>

                <slot name="header" :data="dataForm"></slot>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140">
                  <el-descriptions-item>
                    <template #label><span>备注</span></template>
                    <el-form-item prop="remark">
                      <template #label>
                        <div class="flx-justify-between">
                          <div>备注</div>
                        </div>
                      </template>
                      <el-input v-model="dataForm.remark" type="textarea" :rows="3" placeholder="备注" show-word-limit maxlength="2000"></el-input>
                    </el-form-item>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div class="shop_page_basic" style="margin-top: 12px" v-show="source == 'shop'">
                <!-- 新增商品有 -->
                <div class="titleSty">自动降价设置</div>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140">
                  <el-descriptions-item>
                    <template #label><span>自动降价周期</span></template>
                    <el-form-item label="自动降价周期" prop="priceReductionCycle">
                      <el-input v-model="dataForm.priceReductionCycle" type="number" placeholder="请输入自动降价周期">
                        <template #append>天</template>
                      </el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>自动降价比例</span></template>
                    <el-form-item label="自动降价比例" prop="priceReductionPercentage">
                      <el-input v-model="dataForm.priceReductionPercentage" type="number" placeholder="请输入自动降价比例">
                        <template #append>%</template>
                      </el-input>
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label><span>自动降价底价</span></template>
                    <el-form-item label="自动降价底价" prop="minimumPrice">
                      <el-input v-model="dataForm.minimumPrice" type="number" placeholder="请输入自动降价底价">
                        <template #append>元</template>
                      </el-input>
                    </el-form-item>
                  </el-descriptions-item>
                </el-descriptions>
                <slot name="info" :data="dataForm"></slot>
              </div>
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="12">
          <el-scrollbar style="height: calc(100vh - 138px)">
            <div class="shop_page" style="padding: 12px 12px 12px 0px">
              <div class="shop_tabs" v-if="dataForm.status == 2 || dataForm.status == 3">
                <div class="shop_tabs_item" :class="{ active: activeValue == '1' }" @click="activeValue = '1'">商品信息</div>
                <div class="shop_tabs_item" :class="{ active: activeValue == '2' }" @click="activeValue = '2'">三方对接</div>
              </div>
              <!-- 商品详情 -->
              <div class="shop_page_details" v-show="activeValue == '1'">
                <div class="shop_page_details_card attributeBg" :style="{ borderRadius: dataForm.status == 2 || dataForm.status == 3 ? '0px 8px 0px 0px' : '8px 8px 0 0' }" v-loading="attributeLoading" element-loading-text="属性识别中...">
                  <div class="titleSty">一键勾选游戏属性</div>

                  <ny-button-group
                    :list="[
                      { dictLabel: '自动上号', dictValue: '3' },
                      { dictLabel: '一键解析', dictValue: '2' }
                    ]"
                    v-model="detailsTabsValue"
                    style="display: inline-block; margin-bottom: 10px"
                    v-if="xsGameList.includes(dataForm.gameCode)"
                  ></ny-button-group>
                  <ny-button-group
                    :list="[
                      { dictLabel: '自动获取', dictValue: '1' },
                      { dictLabel: '一键解析', dictValue: '2' }
                    ]"
                    v-model="detailsTabsValue"
                    style="display: inline-block; margin-bottom: 10px"
                    v-if="(buttonPermissions[dataForm.gameCode] || campsiteForm.gameName == '王者荣耀') && !xsGameList.includes(dataForm.gameCode)"
                  ></ny-button-group>
                  <template v-if="detailsTabsValue == '1'">
                    <template v-if="campsiteForm.gameName == '王者荣耀'">
                      <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
                        <el-descriptions-item>
                          <template #label><span>营地号</span></template>
                          <el-form-item label="营地号">
                            <el-input v-model="campsiteForm.campsiteId" placeholder="营地号" style="width: 100%" clearable @blur="campsiteIdFeedback"></el-input>
                          </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item v-if="gameRoleList.length">
                          <template #label><span>选择角色</span></template>
                          <el-form-item>
                            <template #label>
                              选择角色
                              <el-text type="warning" style="margin-left: 12px"
                                ><el-icon><InfoFilled /></el-icon>先加载营地数据，再选择角色</el-text
                              >
                            </template>
                            <el-select v-model="currentRoleId" placeholder="选择角色" :disabled="gameRoleList.length == 0" clearable @change="campsiteChange">
                              <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                            </el-select>
                          </el-form-item>
                        </el-descriptions-item>
                      </el-descriptions>
                      <el-row :gutter="12">
                        <el-col :span="24">
                          <el-button type="primary" :disabled="!campsiteForm.campsiteId" :loading="campsiteButLoad" @click="loadCampsite()">加载营地数据</el-button>
                          <el-button type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" @click="getWzryAttributesList()">自动获取</el-button>
                          <el-button type="info" v-if="unrecognizedList && unrecognizedList.length" @click="showFailureAttribute">查看识别失败属性</el-button>
                          <el-button type="warning" v-if="(gameRoleList && gameRoleList.length) || dataForm.gameCode == 'WWQY'" @click="screenshotHandle" :loading="screenshotLoading"> 一键截图 </el-button>
                        </el-col>
                      </el-row>
                    </template>
                    <template v-else>
                      <!-- 扫码授权 -->
                      <el-descriptions :column="1" border class="descriptions descriptions-label-140" v-if="gameRoleList && gameRoleList.length && campsiteForm.gameName != '王者荣耀'">
                        <el-descriptions-item>
                          <template #label><span>账号角色</span></template>
                          <el-select v-model="currentRoleId" clearable placeholder="账号角色" @clear="roleClear">
                            <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                          </el-select>
                        </el-descriptions-item>
                      </el-descriptions>
                      <scan-code-auth :code="dataForm.gameCode" :gtype="dataForm.gameType" :unrecognizedList="unrecognizedList" :gameRoleList="gameRoleList" :screenshotLoading="screenshotLoading" :buttonPermissions="buttonPermissions" @confirm="getRoleInfo" @screenshotHandle="screenshotHandle">
                        <template #button><el-button type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" v-if="gameRoleList && gameRoleList.length && campsiteForm.gameName != '王者荣耀'" @click="getGameRoleAttributes()">自动获取</el-button></template>
                      </scan-code-auth>
                    </template>
                  </template>
                  <template v-if="(!buttonPermissions[dataForm.gameCode] && detailsTabsValue == '1' && campsiteForm.gameName != '王者荣耀') || detailsTabsValue == '2'">
                    <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
                      <el-descriptions-item>
                        <template #label><span>商品描述</span></template>
                        <el-form-item label="商品描述" prop="info">
                          <el-input v-model="dataForm.info" type="textarea" placeholder="商品描述" :rows="10"></el-input>
                        </el-form-item>
                      </el-descriptions-item>
                    </el-descriptions>
                    <div class="flx-align-center">
                      <el-button type="primary" :icon="Pointer" @click="analysisFn">一键解析</el-button>
                      <div class="flx-align-center" style="margin-left: 8px">
                        <el-icon color="#FF7D00" style="margin-right: 4px"><WarningFilled /></el-icon>
                        <el-text type="warning" style="color: #ff7d00">tip：估价网站文本内容可在此进行解析。勾选底部属性信息后，会自动更新商品“描述信息”以及“标题”内容；</el-text>
                      </div>
                    </div>
                  </template>
                  <template v-if="detailsTabsValue == '3'">
                    <!-- 小算获取 -->
                    <xsObtain
                      ref="xsObtainRef"
                      :xsGameInfo="xsGameInfo"
                      :gameCode="dataForm.gameCode"
                      :gameId="dataForm.gameId"
                      :gameName="campsiteForm.gameName"
                      :serverId="dataForm.server ? dataForm.server : dataForm.gameAre"
                      :taskId="dataForm.taskId"
                      :userName="dataForm.gameAccount"
                      :passWord="dataForm.gamePassword"
                      @xsVerification="(e:boolean)=> xsVerification = e "
                      @oneClickAccess="(e) => (btnLoading = e)"
                      @updateInput="updateInput"
                      @taskId="taskIdChange"
                      @refreshInfo="getInfo(dataForm.id)"
                    ></xsObtain>
                  </template>
                </div>

                <div class="shop_page_details_card" :style="{ borderRadius: '0 0 8px 8px' }">
                  <!-- <dropdownSelect v-model="dataForm.gameAre"></dropdownSelect> -->
                  <div class="titleSty" style="display: flex; align-items: center; justify-content: space-between">
                    <span>属性选择</span>
                    <div style="display: flex; align-items: center">
                      <el-input v-model="searchBlurText" :prefix-icon="Search" clearable placeholder="请输入关键词选择属性"></el-input>
                      <el-button style="margin-left: 8px" type="primary" @click="handleAttributeSearch">查询</el-button>
                      <el-button style="margin-left: 8px" @click="handleAttributeSearchReset"> 重置</el-button>
                    </div>
                  </div>

                  <div class="container">
                    <div class="searchCard" style="display: flex; flex-direction: column; gap: 12px" v-if="showSearchCard">
                      <el-empty v-if="dataForm.attributesList.filter((item:any) => item.type == 2 && item.childrenOrign.length > 0).length < 1" />
                      <template v-else>
                        <template v-for="(item, index) in dataForm.attributesList.filter((item:any) => item.type == 2 && item.childrenOrign.length > 0)" :key="index">
                          <div>
                            <div class="menu_header">
                              <div class="name">{{ item.name }}</div>
                            </div>
                            <div class="menu_center">
                              <el-checkbox-group v-model="item.attributeIds" @change="feedBackInfo()">
                                <div style="display: flex; flex-wrap: wrap; gap: 12px">
                                  <div style="width: 180px" v-for="it in item.childrenOrign" :key="it.id">
                                    <el-checkbox :label="it.name" :value="it.id" v-lazy-render />
                                  </div>
                                </div>
                              </el-checkbox-group>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                    <div class="menu_left">
                      <div class="menu_li" :class="{ menu_active: index == menuIndex }" v-for="(item, index) in ['商城属性', '文本属性', '商品属性', '账号属性']" :key="index" @click="scrollTo(index)">{{ item }}</div>
                    </div>
                    <div class="menu_right">
                      <div class="menu_card" :ref="setSectionRef">
                        <div class="menu_title">商城属性</div>
                        <el-row :gutter="12">
                          <el-col :span="6">
                            <el-form-item label="服务器" prop="gameAre">
                              <el-select v-model="dataForm.gameAre" placeholder="服务器" @change="gameAreChange">
                                <el-option v-for="item in sysgameList" :key="item.id" :label="item.title" :value="item.id" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6" v-if="sysgameChildrenList.length > 0">
                            <el-form-item label="系统区服" prop="server">
                              <el-select v-model="dataForm.server" placeholder="系统区服" @change="feedBackInfo()">
                                <el-option v-for="item in sysgameChildrenList" :key="item.id" :label="item.title" :value="item.id" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label="包赔标签" prop="compensation">
                              <el-select v-model="dataForm.compensation" placeholder="包赔标签">
                                <el-option label="不可包赔" value="0" />
                                <el-option label="可买包赔" value="1" />
                                <el-option label="永久包赔" value="2" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label="支持议价标签" prop="bargain">
                              <el-select v-model="dataForm.bargain" placeholder="支持议价标签">
                                <el-option label="否" value="0" />
                                <el-option label="是" value="1" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label="顶级账号标签" prop="topped">
                              <el-select v-model="dataForm.topped" placeholder="顶级账号标签">
                                <el-option label="否" value="0" />
                                <el-option label="是" value="1" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                      <div class="menu_card" :ref="setSectionRef">
                        <div class="menu_title">文本属性</div>
                        <el-row :gutter="12">
                          <el-col :span="6" v-for="(item, index) in dataForm.attributesList.filter((item:any) => item.type == 3)" :key="index">
                            <el-form-item :label="item.name">
                              <div class="flod_tab">
                                <el-input v-model="item.attributeText" :placeholder="item.name" clearable @change="feedBackInfo()"></el-input>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                      <div class="menu_card" :ref="setSectionRef">
                        <div class="menu_title">商品属性</div>
                        <el-row :gutter="12">
                          <el-col :span="6" v-for="(item, index) in dataForm.attributesList.filter((item:any) => item.type == 1)" :key="index">
                            <el-form-item :label="item.name">
                              <el-select v-model="item.attributeIds" placeholder="请选择" clearable @change="feedBackInfo()" @clear="item.attributeIds = ''">
                                <el-option v-for="it in item.children" :key="it.id" :label="it.name" :value="it.id" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                      <div class="menu_card" :ref="setSectionRef">
                        <div class="menu_title">账号属性</div>
                        <div style="display: flex; flex-direction: column; gap: 12px">
                          <template v-for="(item, index) in dataForm.attributesList.filter((item:any) => item.type == 2)" :key="index">
                            <div>
                              <div class="menu_header">
                                <div class="name">{{ item.name }}</div>
                                <div class="operate">
                                  <el-button type="primary" text v-if="item.attributeIds.length > 0" @click="item.attributeIds = []" class="resetFont">重置</el-button>
                                  <el-button v-if="item.children.length > 4" type="primary" plain text @click="item.fold = !item.fold">
                                    <span style="margin-right: 6px">{{ item.fold ? "展开列表" : "收起列表" }}</span>
                                    <el-icon v-if="item.fold"><ArrowDown /></el-icon>
                                    <el-icon v-else><ArrowUp /></el-icon>
                                  </el-button>
                                </div>
                              </div>
                              <div class="menu_center">
                                <el-checkbox-group v-model="item.attributeIds" @change="feedBackInfo()">
                                  <div style="display: flex; flex-wrap: wrap; gap: 12px">
                                    <div style="width: 180px" v-for="it in spliceChildren(item)" :key="it.id">
                                      <el-checkbox :label="it.name" :value="it.id" v-lazy-render />
                                    </div>
                                  </div>
                                </el-checkbox-group>
                              </div>
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 三方对接 -->
              <thirdParty
                ref="thirdPartyRef"
                :dataForm="dataForm"
                @thridChange="
                  (e) => {
                    thirdType = e;
                  }
                "
                v-if="activeValue == '2'"
              ></thirdParty>
            </div>
          </el-scrollbar>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div style="padding: 8px 20px 20px 0px">
        <template v-if="activeValue == '1'">
          <div style="flex: auto">
            <el-button @click="drawerCloseHandle('close')">取消</el-button>
            <!-- 修改商品 并且已上架 -->
            <template v-if="dataForm.status == 2">
              <el-button v-if="props.source == 'tenantShop'" type="primary" @click="releasedCommand(1)">确定发布</el-button>
              <el-dropdown placement="top" v-else class="ml-8" @command="releasedCommand">
                <el-button type="primary">确定发布</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="2">推送发布</el-dropdown-item>
                    <el-dropdown-item :command="1">正常发布</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>

            <!-- 新增商品 -->
            <el-button v-else-if="source == 'shop' || source == 'tenantShop'" :loading="btnLoading" type="primary" @click="dataFormSubmitHandle('')">{{ !dataForm.id ? "确定发布" : "确定修改" }}</el-button>
            <!-- 小算一键获取保存 -->
            <!-- <el-button v-else-if="xsGameList.includes(dataForm.gameCode)" :disabled="(!xsVerification && !dataForm.id)" :loading="btnLoading" type="primary" @click="xsObtainSubmit">一键获取保存</el-button> -->

            <!-- 商品审核 -->
            <template v-if="source == 'audit'">
              <!-- <el-button :loading="btnLoading" type="warning" @click="dataFormSubmitHandle('2')">一键上架</el-button> -->
              <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle('1')">提交</el-button>
            </template>

            <!-- 回收订单 完善信息 -->
            <template v-if="source == 'order'">
              <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
            </template>
          </div>
        </template>
        <template v-else>
          <el-radio-group v-model="taskType">
            <el-radio v-for="item in thirdType == '1' ? typeList : typeListAPI" :key="item.typeCode" :value="item.typeCode" :label="item.typeCode">{{ item.typeName }}</el-radio>
          </el-radio-group>
          <el-button style="margin-left: 24px" :loading="replyLoading" @click="pushAllSelect" type="primary">推送所选</el-button>
        </template>
      </div>
    </template>

    <!-- 属性识别结果 -->
    <attribute-recognition-result ref="attributeRecognitionResultRef" @confirm="autoChecktAttributes" @showFailureAttributeEmit="showFailureAttribute"></attribute-recognition-result>

    <!-- 选择合作商 -->
    <select-partners ref="selectPartnersRef" :key="selectPartnersKey" btnTxt="推送发布" @change="pushSubmit"></select-partners>
  </el-dialog>
  <!-- </el-dialog> -->
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch, defineProps } from "vue";
import { scrollToFirstError, getAccountPassword } from "@/utils/method";
import ScanCodeAuth from "./components/ScanCodeAuth.vue";
import AttributeRecognitionResult from "./components/AttributeRecognitionResult.vue";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";
import { getUuid } from "@/utils/utils";
import { districtServiceConversionName } from "@/utils/utils";
import SelectPartners from "./components/SelectPartners.vue";
import thirdParty from "./components/thirdParty.vue";
import xsObtain from "./components/xs-obtain.vue";
import { Search } from "@element-plus/icons-vue";
import dropdownSelect from "./components/dropdownSelect.vue";
import { includes } from "lodash";

const { t } = useI18n();
const store = useAppStore();
const emit = defineEmits(["refreshDataList", "completeInfo"]);
const activeValue = ref("1");
const detailsTabsValue = ref("1");

const searchBlurText = ref("");
const showSearchCard = ref(false);

// 授权按钮根据游戏 去显示
const buttonPermissions = {
  DZPD: ["qq", "wx"],
  CYHX: ["qq", "screenshot"],
  CYHXSY: ["qq", "wx"],
  YXLM: ["qq", "screenshot"],
  YXLMSY: ["qq", "wx"],
  JCCZZ: ["qq", "wx", "screenshot"],
  HPJY: ["qq", "wx", "screenshot"],
  QQFC: ["qq"],
  QQFCSY: ["qq", "wx"],
  WWQY: ["qq", "wx", "screenshot"],
  HYRZ: ["qq", "wx"],
  YS: [],
  NZ: ["qq"]
};

const xsGameList = ["CYHXSY", "SMZHSY", "SJZXD", "YJWJ"]; // 小算游戏；注：同步完善插件功能支持游戏表格内容; 验号组件同步添加类别；
const xsVerification = ref(false); // 小算验号通过
const xsGameInfo = ref(); // 小算游戏映射详细信息

const props = defineProps({
  getDetialApi: {
    type: String,
    default: "/shop/shop/"
  },
  // 标题
  title: {
    type: String,
    default: ""
  },
  // 来源
  source: {
    type: String,
    default: ""
  },
  accountSource: {
    type: String,
    default: "ORIGINAL_OWNER"
  },
  // loading
  submitLoading: {
    type: Boolean,
    default: false
  }
});

const visible = ref(false); // 对话框显隐
const dataFormRef = ref(); // 表单ref
const dataForm = reactive({
  // 表单变量
  id: null,
  code: "",
  // 游戏id
  gameId: "",
  // 游戏code
  gameCode: "",
  // 游戏类型
  gameType: "",
  highlights: "",
  remark: "",
  title: "",
  price: 0,
  acquisitionPrice: 0,
  phone: "",
  gameAccount: "",
  gamePassword: "",
  log: [],
  imagesList: [],
  authImage: [], // 实名情况截图
  campImage: [], // 营地截图
  info: "",
  gameAre: "",
  server: "",
  compensation: "",
  bargain: "",
  topped: "",
  attributesList: [] as any,
  acquisitionUser: store.state.isPlatform && !store.state.user.superAdmin ? store.state.user.id : "",
  minimumPrice: undefined,
  priceReductionPercentage: undefined,
  priceReductionCycle: undefined,
  status: 1,
  taskId: "",
  ownCoding: "",
  emergencyPhone: ""
});

const campsiteForm = reactive({
  gameName: "",
  campsiteId: "",
  roleId: "",
  roleList: [] as any
});

const rules = ref({
  // 表单必填项
  gameId: [{ required: true, message: "游戏名称不能为空", trigger: "change" }],
  title: [{ required: true, message: "商品标题不能为空", trigger: "blur" }],
  price: [{ required: true, message: "零售价不能为空", trigger: "blur" }],
  acquisitionPrice: [{ required: true, message: "回收价不能为空", trigger: "blur" }],
  gameAccount: [{ required: true, message: "游戏账号不能为空", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      required: true,
      pattern: /^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ],
  log: [{ required: true, message: "游戏主图不能为空", trigger: "change" }],
  imagesList: [{ required: true, message: "详情图片不能为空", trigger: "change" }],
  info: [{ required: true, message: "商品描述不能为空", trigger: "blur" }],
  gameAre: [{ required: true, message: "服务器不能为空", trigger: "change" }],
  server: [{ required: true, message: "系统区服不能为空", trigger: "change" }],
  compensation: [{ required: true, message: "包赔标签不能为空", trigger: "change" }],
  bargain: [{ required: true, message: "支持议价标签不能为空", trigger: "change" }],
  topped: [{ required: true, message: "顶级账号标签不能为空", trigger: "change" }],
  emergencyPhone: [
    { required: false, message: "请输入应急手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ]
});
const gamesList = ref([] as any); // 游戏列表
const gameTabShow = ref(false); // 游戏列表显隐
const sysgameList = ref([] as any); // 游戏区服信息 - 服务器
const sysgameChildrenList = ref([] as any); // 游戏区服信息 - 系统区服

// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
    let game = gamesList.value.find((item) => item.id == dataForm.gameId);
    dataForm.gameCode = game.gcode;
    dataForm.gameType = game.gtype;
    detailsTabsValue.value = xsGameList.includes(dataForm.gameCode) ? "3" : "1";
    campsiteForm.gameName = game.title;
    if (xsGameList.includes(dataForm.gameCode)) {
      baseService.get("mapping/sysgameinfomapping/list", { type: 2, partnerName: "小算", platformId: dataForm.gameId }).then((res) => {
        xsGameInfo.value = res.data[0];
      });
    }
  });
};
// 获取游戏密码
const setAccountPassword = async () => {
  dataForm.gamePassword = await getAccountPassword(dataForm.id);
};

// 获取手机号数据
const loading = ref(false);
const phonesList = ref([]);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    baseService
      .get("/mobile/mobileCard/page", {
        page: 1,
        limit: 9999,
        phone: query
      })
      .then((res) => {
        phonesList.value = res.data.list;
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    phonesList.value = [];
  }
};

// 获取游戏属性信息
const getAttribute = () => {
  requestLoading.value = true;
  gameUserInfo.value = {};
  baseService
    .get("/game/attribute/page", { gameId: dataForm.gameId })
    .then((res) => {
      if (res.data.length == 0) {
        ElMessage({
          type: "warning",
          message: "没有查询到当前游戏属性信息请编辑后在来新增商品！"
        });
        requestLoading.value = false;
      } else {
        dataForm.attributesList = [];
        res.data.map((item: any) => {
          dataForm.attributesList.push({
            typeId: item.id,
            attributeIds: item.type == 2 ? [] : "",
            attributeText: "",
            children: item.children,
            childrenOrign: [],
            isTitle: item.isTitle,
            type: item.type,
            name: item.name,
            statisticsTag: item.statisticsTag,
            fold: false
          });
        });
        nextTick(() => {
          if (dataForm.id && dataForm.attributesList) {
            getInfo(dataForm.id);
          } else {
            requestLoading.value = false;
          }
        });
      }
    })
    .catch((err) => {
      requestLoading.value = false;
    });
};

// 获取游戏区服
const getSysgame = () => {
  baseService.get("/game/sysgame/get/" + dataForm.gameId).then((res) => {
    sysgameList.value = res.data.areaDtoList;
    // console.log(sysgameList.value, res.data);
  });
};

// 游戏切换点击事件
const gameIdCopy = ref();
const gamesClick = async (value: any) => {
  dataFormRef.value.resetFields();
  await nextTick();
  gameIdCopy.value = value;
  const gameInfo = gamesList.value.find((item: any) => item.id == value);
  dataForm.id = null;
  dataForm.gameId = gameInfo.id;
  dataForm.gameCode = gameInfo.gcode;
  dataForm.gameType = gameInfo.gtype;
  // dataForm.log = [];
  // dataForm.imagesList = [];
  unrecognizedList.value = [];
  recognizedList.value = [];
  gameRoleList.value = [];
  sysgameChildrenList.value = [];
  currentRoleId.value = "";
  detailsTabsValue.value = xsGameList.includes(dataForm.gameCode) ? "3" : "1";
  if (xsGameList.includes(dataForm.gameCode)) {
    baseService.get("mapping/sysgameinfomapping/list", { type: 2, partnerName: "小算", platformId: dataForm.gameId }).then((res) => {
      xsGameInfo.value = res.data[0];
    });
  }

  getAttribute();
  getSysgame();
  // ElMessageBox.confirm("切换游戏后，将清空已填写商品信息。您确定要切换吗?", "提示", {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   type: "warning",
  //   center: true
  // }).then(() => {
  //   dataFormRef.value.resetFields();
  //   dataForm.id = null;
  //   dataForm.gameId = gameInfo.id;
  //   dataForm.gameCode = gameInfo.gcode;
  //   dataForm.gameType = gameInfo.gtype;
  //   unrecognizedList.value = [];
  //   recognizedList.value = [];
  //   gameRoleList.value = [];
  //   getAttribute();
  //   getSysgame();
  // });
};

// 服务器切换点击事件
const gameAreChange = (value: any) => {
  sysgameChildrenList.value = [];
  dataForm.server = "";
  const are = sysgameList.value.filter((item: any) => item.id == value)[0];
  if (are.children.length > 0) {
    sysgameChildrenList.value = are.children;
  } else {
    dataForm.server = value;
  }
  feedBackInfo();
};

// 一键解析
const analysisFn = (type?: string) => {
  if (!dataForm.info) {
    ElMessage.warning("请输入商品简介");
    return;
  }
  const strToArr = dataForm.info.split("\n");
  const infoArr = arrayToKeyValuePairs(strToArr);

  if (typeof infoArr == "string") {
    return;
  }
  infoArr.forEach((element: any) => {
    if (element.key.indexOf("服务器") > -1) {
      if (!element.value) return;
      const gameAreItem = sysgameList.value.filter((item: any) => item.title == element.value)[0];
      if (gameAreItem.children.length > 0) {
        dataForm.gameAre = gameAreItem.id;
        sysgameChildrenList.value = gameAreItem.children;
      } else {
        dataForm.gameAre = gameAreItem.id;
        dataForm.server = gameAreItem.id;
      }
    } else if (element.key.indexOf("系统区服") > -1) {
      const serverItem = sysgameChildrenList.value.filter((item: any) => item.title == element.value)[0];
      if (serverItem) {
        dataForm.server = serverItem.id;
      }
    } else if (element.key.indexOf("您自己认为是亮点的地方") > -1) {
      dataForm.highlights = element.value;
    } else {
      dataForm.attributesList.forEach((item: any) => {
        if (item.name == element.key) {
          if (item.type == 1) {
            // 单选
            const ids = nameQueryId(element.value, item.children);
            item.attributeIds = ids.join("");
          }
          if (item.type == 2) {
            // 多选
            const ids = nameQueryId(element.value, item.children);
            item.attributeIds = ids;
          }
          if (item.type == 3) {
            // 文本
            item.attributeText = element.value;
          }
        }
      });
    }
  });
  if (type) ElMessage.success("解析成功！");
  feedBackInfo();
};

// 文本内容转换成数组
const arrayToKeyValuePairs = (strArray: any) => {
  try {
    let arr: { key: any; value: any }[] = [];
    strArray.forEach((item: any) => {
      const [key, value] = item.split("：");
      if (value == undefined) {
        throw "解析失败，商品简介文本内容结构错误。请检查后重新解析；";
      }
      arr.push({ key: key, value: value });
    });
    return arr;
  } catch (err: any) {
    ElMessage.warning(err);
    return err;
  }
};

// 属性名称查询属性ID
const nameQueryId = (value: string, data: any[]) => {
  const valToArr = value.split(/,|，/);
  const ids: any = [];
  valToArr.forEach((name: string) => {
    data.forEach((item: any) => {
      if (item.name == name.trim()) {
        ids.push(item.id);
      }
    });
  });
  return ids;
};

// 属性ID查询属性名称
const idQueryName = (value: any, data: any[]) => {
  const names: any = [];
  value.forEach((id: string) => {
    data.forEach((item: any) => {
      if (item.id == id) {
        names.push(item.name);
      }
    });
  });
  return names;
};
const checkMultiDataList = ref([] as any[]);
// 点击属性回显商品简介
const feedBackInfo = () => {
  const infoArr: any = [];
  const titleArr: any = [];
  // 区服
  const gameAreItem = sysgameList.value.filter((item: any) => item.id == dataForm.gameAre)[0];
  infoArr.push(`服务器：${gameAreItem ? gameAreItem.title : ""}`);
  if (sysgameChildrenList.value.length > 0) {
    const serverItem = sysgameChildrenList.value.filter((item: any) => item.id == dataForm.server)[0];
    infoArr.push(`系统区服：${serverItem ? serverItem.title : ""}`);
  }
  // 选中的所有子属性，用于标签统计文本属性
  checkMultiDataList.value = [];
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 2 && item.children) {
      let arr = [];
      arr = item.children.filter((ele: any) => item.attributeIds.includes(ele.id) && ele.tags && ele.tags.length > 0);
      checkMultiDataList.value = [...checkMultiDataList.value, ...arr];
    }
  });
  checkboxTotalBackfillWidthTag();

  // 属性
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 1) {
      const names = idQueryName([item.attributeIds], item.children).join("");
      textAttributeBackfilling(item.name, names); // 单选属性回显到文本
      infoArr.push(`${item.name}：${names}`);
    }
    if (item.type == 2) {
      const names = idQueryName(item.attributeIds, item.children).join("，");
      checkboxTotalBackfill(item.name, item.attributeIds.length);
      infoArr.push(`${item.name}：${names}`);
    }
    if (item.type == 3) {
      if (!item.name.includes("【文本】")) {
        infoArr.push(`${item.name}：${item.attributeText}`);
      }
    }
  });

  dataForm.info = infoArr.join("\n");

  // 拼接商品标题
  // 获取文本类型属性
  const textArr = dataForm.attributesList.filter((item: any) => item.type == 3 && item.isTitle == 0);
  if (textArr.length > 0) {
    textArr.forEach((item: any) => {
      if (item.attributeText) {
        titleArr.push(`${item.name}${item.attributeText}`);
      }
    });
  }

  // 获取单选类型属性
  const radioArr = dataForm.attributesList.filter((item: any) => item.type == 1 && item.isTitle == 0);
  if (radioArr.length > 0) {
    radioArr.forEach((item: any) => {
      const names = idQueryName([item.attributeIds], item.children).join("");
      if (names) {
        titleArr.push(`【${names}】`);
      }
    });
  }

  // 获取多选类型属性 -- 统计个数
  const checkboxArr = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0);
  if (checkboxArr.length > 0) {
    checkboxArr.forEach((item: any) => {
      if (item.attributeIds.length > 0) {
        titleArr.push(`${item.name}${item.attributeIds.length}`);
      }
    });
  }

  // 获取多选类型属性 -- 详情内容
  const checkboxArrDetails = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0);
  if (checkboxArrDetails.length > 0) {
    checkboxArrDetails.forEach((item: any) => {
      if (item.attributeIds.length > 0) {
        const names = idQueryName(item.attributeIds, item.children).join("，");
        titleArr.push(`${item.name}：${names}；`);
      }
    });
  }

  // 数组最前面添加亮点信息
  if (dataForm.highlights) titleArr.unshift(`【${dataForm.highlights}】`);
  // 有商品编码，进行拼接
  if (dataForm.code) titleArr.unshift(dataForm.code);
  dataForm.title = titleArr.join(" ").slice(0, 1500);
};

// 单选属性回填文本
const textAttributeBackfilling = (label: any, value: any) => {
  // 属性
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 3 && item.name.includes("【文本】")) {
      if (item.name.slice(0, -4) == label) {
        item.attributeText = value;
      }
    }
  });
};

// 多选属性合计回显
// 含标签的
const checkboxTotalBackfillWidthTag = () => {
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 3 && item.statisticsTag && item.statisticsTag != "0") {
      let arr = [] as any[];
      checkMultiDataList.value.forEach((itemAttri: any) => {
        //  选中的属性所含标签们  包含了 当前文本属性所打的标签
        if (itemAttri.tags.some((i: any) => item.statisticsTag == i.id)) {
          arr.push(itemAttri);
        }
      });
      // 去重
      arr = arr.map((ele) => ele.id);
      arr = Array.from(new Set(arr));
      // 统计
      item.attributeText = arr.length;
    }
  });
};
// 含合计的
const checkboxTotalBackfill = (label: any, value: any) => {
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 3 && item.name.includes("【合计】") && !item.statisticsTag) {
      if (item.name.slice(0, -4) == label) {
        item.attributeText = value;
      }
    }
  });
};

// 营地号回显到文本
const campsiteIdFeedback = (event: FocusEvent) => {
  dataForm.attributesList.forEach((item: any) => {
    if ((item.type == 3 && (item.name == "营地号" || item.name == "营地ID" || item.name == "营地Id")) || item.name == "营地id") {
      item.attributeText = campsiteForm.campsiteId;
    }
  });
};

// 小算一键获取提交
const xsObtainRef = ref();
const xsObtainSubmit = () => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      // 未填写的内容滚动到当前位置
      scrollToFirstError(dataFormRef.value);
      ElMessage.warning("请完善必填项信息，在进行提交！");
      return false;
    }
    if (dataForm.price == 0) {
      ElMessage.warning("零售价必须大于0");
      return;
    }

    if (dataForm.acquisitionPrice == 0) {
      ElMessage.warning("回收价必须大于0");
      return;
    }
    if (xsVerification.value && !dataForm.id) {
      xsObtainRef.value.oneClickAccess(dataForm.server ? dataForm.server : dataForm.gameAre);
    } else {
      dataFormSubmitHandle("");
    }
  });
};
// 小算自动一键获取
const taskIdChange = (e: any) => {
  dataForm.taskId = e;
  dataForm.title = "一键获取标题生成中，请稍后查看~";
  dataForm.info = "一键获取描述生成中，请稍后查看~";
  dataForm.log = ["https://oss.nyyyds.com/upload/20250423/836aa7f50dc24d89b9c8fc7f1b082fec.png"];
  dataForm.imagesList = ["https://oss.nyyyds.com/upload/20250423/836aa7f50dc24d89b9c8fc7f1b082fec.png"];
};

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = (status: any) => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      // 未填写的内容滚动到当前位置
      scrollToFirstError(dataFormRef.value);
      ElMessage.warning("请完善必填项信息，在进行提交！");
      return false;
    }

    // 商品标题长度校验
    // if(dataForm.title.length > (!dataForm.id ? 230 : 240) ){
    //   return ElMessage.warning(`商品标题长度不能超过${!dataForm.id ? 230 : 240}个字符！`);
    // }

    if (dataForm.price == 0) {
      ElMessage.warning("零售价必须大于0");
      return;
    }

    // if(dataForm.acquisitionPrice == 0){
    //   ElMessage.warning("回收价必须大于0");
    //   return;
    // }

    const params = JSON.parse(JSON.stringify(dataForm));
    // 处理单选属性改为数组
    params.attributesList.map((item: any) => {
      if (item.type == 1) {
        item.attributeIds = item.attributeIds ? [item.attributeIds] : [];
      }
      if (item.type == 2) {
        item.attributeIds = item.attributeIds ? item.attributeIds : [];
      }
      if (item.type == 3) {
        item.attributeIds = [];
      }
    });
    // 处理游戏主图改为字符串
    params.log = params.log.join("");
    // 处理实名情况
    params.authImage = params.authImage ? params.authImage.join("") : "";
    // 处理营地截图
    params.campImage = params.campImage ? params.campImage.join("") : "";

    // 去除游戏账号中的空格
    params.gameAccount = params.gameAccount.replace(/\s+/g, "");
    btnLoading.value = true;
    // 05-28  加载状态
    requestLoading.value = true;
    if (props.source == "audit") {
      params.status = status;
      delete params.id;
      localStorage.shopDraft = "";
      // 审核通过
      await baseService.post("/shop/shopaudit", { id: dataForm.id, status: "1" });

      baseService
        .post("/shop/shopaudit/add", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 1000,
            onClose: () => {
              drawerCloseHandle("");
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
          // 05-28  加载状态
          requestLoading.value = false;
        });

      return;
    }

    // 新增商品
    dataForm.id ? "" : (params.accountSource = props.accountSource || "ORIGINAL_OWNER");
    localStorage.shopDraft = "";
    (!dataForm.id ? baseService.post : baseService.put)("/shop/shop", params)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 1000,
          onClose: () => {
            emit("refreshDataList", dataForm.id ? "update" : "");
            drawerCloseHandle("");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
        // 05-28  加载状态
        requestLoading.value = false;
      });
  });
};

// 回收订单 完善信息提交
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      scrollToFirstError(dataFormRef.value);
      ElMessage.warning("请完善必填项信息，在进行提交！");
      return false;
    }
    const params = JSON.parse(JSON.stringify(dataForm));
    params.attributesList = JSON.parse(JSON.stringify(dataForm.attributesList));
    emit("completeInfo", params.attributesList, dataForm);
  });
};

// 加载营地数据
const campsiteButLoad = ref(false);
const loadCampsite = (roleId?: string) => {
  campsiteButLoad.value = true;
  currentRoleId.value = "";
  baseService
    .post("/shop/shop/roleByCampsiteId", { campsiteId: campsiteForm.campsiteId })
    .then((res) => {
      if (res.code == 0) {
        gameRoleList.value = res.data.map((item: any) => {
          return {
            ...item,
            areaName: districtServiceConversionName(dataForm.gameCode, item.areaName)
          };
        });

        // currentRoleId.value = roleId || gameRoleList.value[0].roleId;
        // if (!roleId) campsiteChange(currentRoleId.value);
      }
    })
    .finally(() => {
      campsiteButLoad.value = false;
    });
};

// 王者荣耀游戏数据
const getWzryAttributesList = () => {
  attributeLoading.value = true;
  baseService
    .get("/autoCheck/autoCheck/wzry/getWzryAttrByCampsiteId", {
      campsiteId: campsiteForm.campsiteId,
      roleId: currentRoleId.value
    })
    .then((res: any) => {
      unrecognizedList.value = [];
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      recognizedList.value = attrList;
      autoChecktAttributes();
      feedBackInfo();

      autoCheckServers();
      if (res.data.textAttr) {
        getWzryAttrText(res.data.textAttr);
      }
    })
    .finally(() => {
      attributeLoading.value = false;
    });
};

// 自动获取 - 文本属性回填
const getWzryAttrText = (textAttr: any) => {
  for (let key in textAttr) {
    dataForm.attributesList.map((item: any, index: number) => {
      if (key == item.name) {
        item.attributeText = textAttr[key];
      }
    });
  }
  feedBackInfo();
};

watch(
  () => dataForm.gameId,
  (newVal) => {
    setTimeout(() => {
      try {
        const gameName = gamesList.value.filter((item: any) => item.id == newVal)[0].title;
        campsiteForm.gameName = gameName;
      } catch (error) {
        console.log(error);
      }
    }, 1000);
  }
);

watch(
  () => props.submitLoading,
  (newVal) => {
    btnLoading.value = newVal;
  }
);

// 选择角色
const campsiteChange = (value: any) => {
  // 角色ID位置
  const roleIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "**********");
  // 营地ID位置
  const campIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "2024112610");
  if (roleIndex < 0) {
    // 营地角色ID
    dataForm.attributesList.push({
      typeId: "**********",
      attributeText: value
    });
    // 营地ID
    dataForm.attributesList.push({
      typeId: "2024112610",
      attributeText: campsiteForm.campsiteId
    });
  } else {
    dataForm.attributesList[roleIndex].attributeText = value;
    dataForm.attributesList[campIndex].attributeText = campsiteForm.campsiteId;
  }
};

// 表单初始化
const init = (gameId: string, id?: any) => {
  visible.value = true;
  dataForm.gameId = gameId;
  dataForm.id = id ? id : null;
  if (props.source == "audit") dataForm.auditId = id;

  getGamesList();
  if (dataForm.gameId) {
    getSysgame();
    getAttribute();
    getShopDraft();
  }
  if (dataForm.id && props.source != "order" && props.source != "partnerorder") {
    setAccountPassword();
  }

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};

// 是否有暂存的数据
const getShopDraft = () => {
  let data = localStorage.shopDraft ? JSON.parse(localStorage.shopDraft) : null;
  detailsTabsValue.value = xsGameList.includes(dataForm.gameCode) ? "3" : "1";
  if (data && !dataForm.id && data.source == props.source && (props.source == "shop" || props.source == "tenantShop") && (data.log || (data.imagesList && data.imagesList.length) || data.info || data.title || data.price || data.acquisitionPrice || data.gameAccount)) {
    ElMessageBox.confirm("当前存在未提交的数据，是否继续编辑?", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(() => {
        console.log(dataForm, data);
        // 处理主图回显
        data.log = data.log ? [data.log] : [];
        Object.assign(dataForm, data);
        detailsTabsValue.value = xsGameList.includes(dataForm.gameCode) ? "3" : "1";
        getSysgame();
      })
      .catch((err) => {
        console.log(err);
        detailsTabsValue.value = xsGameList.includes(dataForm.gameCode) ? "3" : "1";
        // catch error
      });
  }
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getInfo = (id: number) => {
  requestLoading.value = true;
  baseService
    .get(props.getDetialApi + id)
    .then((res) => {
      // console.log(res.data)
      if (!res.data) return;

      const data = res.data?.shopAuditDTO || res.data;

      // 处理主图回显
      data.log = data.log ? [data.log] : [];
      // 处理详情图回显
      data.images = data.images ? JSON.parse(data.images) : [];
      // 处理实名情况截图回显
      data.authImage = data.authImage ? [data.authImage] : [];
      // 处理营地截图回显
      data.campImage = data.campImage ? [data.campImage] : [];

      dataForm.imagesList = data.images;
      // 处理包赔标签
      data.compensation = data.compensation?.toString();
      // 处理议价标签
      data.bargain = data.bargain?.toString();
      // 处理顶级账号标签回显
      data.topped = data.topped?.toString();
      // 动态商品信息处理
      try {
        data.accountInfo = JSON.parse(data.accountInfo);
        data.basicInfo = JSON.parse(data.basicInfo);
      } catch (error) {}

      // 处理服务器回显
      if (sysgameList.value) {
        const list = sysgameList.value;
        for (let i = 0; i < list.length; i++) {
          if (list[i].id == data.server) {
            dataForm.gameAre = list[i].id;
            break;
          } else {
            for (let j = 0; j < list[i].children.length; j++) {
              if (list[i].children[j].id == data.server) {
                sysgameChildrenList.value = list[i].children;
                dataForm.gameAre = list[i].id;
                dataForm.server = list[i].children[j].id;
                break;
              }
            }
          }
        }
      }

      if (data.attributesList) {
        // 处理属性回显
        data.attributesList.forEach((ele: any) => {
          // 角色ID回显
          if (ele.typeId == "**********") {
            currentRoleId.value = ele.attributeText;
          }

          // 营地ID回显
          if (ele.typeId == "2024112610") {
            campsiteForm.campsiteId = ele.attributeText;
            // setTimeout(() => {
            //   loadCampsite(currentRoleId.value);
            // }, 1000);
          }

          // 属性回显
          dataForm.attributesList.forEach((item: any) => {
            if (item.typeId == ele.typeId) {
              if (item.type == 1) {
                item.attributeIds = ele.attributeIds.join("");
              }
              if (item.type == 2) {
                item.attributeIds = ele.attributeIds;
              }
              if (item.type == 3) {
                item.attributeText = ele.attributeText;
              }
              Object.assign(ele, item);
            }
          });
        });
      }

      if (data.attributesList) {
        const attributesList = Object.assign(data.attributesList, dataForm.attributesList);
        data.attributesList = attributesList;
      }
      Object.assign(dataForm, data);
      // console.log(dataForm,'====== dataform =-=====')
      if (props.source == "order") {
        analysisFn("getInfo");
      }
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

// 扫码完成之后查询属性
const attributeLoading = ref(false);
// 查询到的 用户信息
const gameUserInfo = ref(<any>{});

// 当前选中的角色
const currentRoleId = ref("");

// 获取角色信息
const gameRoleList = ref([]);
const gameCodeStr = ref("");
const qrcodeAuthType = ref("qq");
const getRoleInfo = async (info: any, gameCode: string, authType: string) => {
  // return getGameRoleAttributes();
  qrcodeAuthType.value = authType;
  unrecognizedList.value = [];

  gameUserInfo.value = info;
  gameCodeStr.value = gameCode;

  // 无畏契约 没有角色列表
  if (gameCode == "WWQY") {
    // console.log(info,'===== 无畏契约扫码回调 ========');
    const roleIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "2025032209");
    if (roleIndex < 0) {
      dataForm.attributesList.push({
        typeId: "2025032209",
        attributeText: info.openId
      });
    } else {
      dataForm.attributesList[roleIndex].attributeText = info.openId;
    }

    // console.log(dataForm.attributesList,'===== 无畏契约扫码更新属性列表 ========');

    // 区服勾选
    if (authType == "qq") {
      let obj = sysgameList.value.find((item: any) => item.title == "QQ");
      dataForm.gameAre = obj.id;
      dataForm.server = obj.id;
    } else {
      let obj = sysgameList.value.find((item: any) => item.title == "微信");
      dataForm.gameAre = obj.id;
      dataForm.server = obj.id;
    }

    return getGameRoleAttributes();
  }

  let res: any = await baseService.post("/autoCheck/autoCheck/getRole", {
    ...gameUserInfo.value,
    // userId: "483673464",
    // token: "CrTV2GkN",
    gameCode: gameCodeStr.value
  });

  if (res.code == 0) {
    if (!res.data || !res.data.length) {
      attributeLoading.value = false;
      return;
    }
    gameRoleList.value = res.data.map((item: any) => {
      return {
        ...item,
        areaName: districtServiceConversionName(dataForm.gameCode, item.areaName, qrcodeAuthType.value)
      };
    });
    console.log(gameRoleList.value);

    currentRoleId.value = gameRoleList.value[0].roleId;
  }
};

// 自动勾选服务器
const autoCheckServers = () => {
  let currentRoleName = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);

  if (!sysgameList.value[0].children || !sysgameList.value[0].children.length) {
    let obj = sysgameList.value.find((item: any) => item.title == currentRoleName.areaName);
    if (!obj) return;
    dataForm.gameAre = obj.id;
    dataForm.server = obj.id;
    return;
  }

  sysgameList.value.map((item: any) => {
    item.children.map((sub: any) => {
      if (sub.title == currentRoleName.areaName) {
        dataForm.gameAre = item.id;
        dataForm.server = sub.id;
        sysgameChildrenList.value = item.children;
      }
    });
  });
};

// 未识别属性列表
const unrecognizedList = ref([] as any);
// 已识别属性列表
const recognizedList = ref([] as any);

// 根据角色获取 游戏资产属性 自动勾选
const getGameRoleAttributes = async () => {
  attributeLoading.value = true;
  let currentRole: any = "";
  if (currentRoleId.value) currentRole = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);
  if (gameCodeStr.value != "WWQY") autoCheckServers();
  try {
    let res: any = await baseService.post("/autoCheck/autoCheck/getAttr", {
      // userId: "483673464",
      // token: "CrTV2GkN",
      gameCode: gameCodeStr.value,
      roleIdList: [currentRoleId.value],
      ...gameUserInfo.value,
      ...currentRole
    });

    if (res.code == 0) {
      attributeLoading.value = false;
      unrecognizedList.value = [];
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      let cfpcSpecialWeapon = res.data.cfpcSpecialWeapon ? res.data.cfpcSpecialWeapon : [];
      recognizedList.value = [...attrList, ...cfpcSpecialWeapon];
      autoChecktAttributes();
      feedBackInfo();

      // 文本属性回显
      if (res.data.textAttr) {
        getWzryAttrText(res.data.textAttr);
      }
    }
  } catch (error) {
    attributeLoading.value = false;
  }
};

// 扫码成功 自动勾选商品属性
const autoChecktAttributes = (attributeList?: any) => {
  let arr = attributeList ? [...attributeList, ...recognizedList.value] : recognizedList.value;
  let list = arr.join(",");

  dataForm.attributesList.map((item: any) => {
    if (item.type == 1) {
      // 单选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids.join("");
    }
    if (item.type == 2) {
      // 多选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids;
    }
  });
};

// 清空账号角色回调
const roleClear = () => {
  gameRoleList.value = [];
  console.log("sdkljfsdlfjsdlfjsdlfjsdlfjl");
};

// 关闭时保存为草稿
const drawerCloseHandle = (type?: string) => {
  if (!visible.value) return;
  visible.value = false;
  if (type != "close" || dataForm.id || (props.source != "shop" && props.source != "tenantShop")) return;
  const params = JSON.parse(JSON.stringify(dataForm));

  // 处理游戏主图改为字符串
  try {
    params.log = params.log.join("");
  } catch (error) {}
  params.source = props.source;

  localStorage.shopDraft = JSON.stringify(params);
};

const attributeRecognitionResultRef = ref();
const showFailureAttribute = () => {
  attributeRecognitionResultRef.value.init(unrecognizedList.value);
};

watch(
  () => unrecognizedList.value,
  (val: any) => {
    if (val && val.length) {
      attributeRecognitionResultRef.value.init(unrecognizedList.value, "first");
    }
  },
  { deep: true }
);

// 一键截图
const screenshotLoading = ref(false);
const screenshotHandle = () => {
  let currentRole: any = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);

  let params = {
    gameCode: dataForm.gameCode,
    roleIdList: [currentRoleId.value],
    ...gameUserInfo.value,
    ...currentRole,
    taskId: getUuid()
  };

  if (dataForm.gameCode == "WZRY") {
    // 营地号
    params.campsiteId = campsiteForm.campsiteId;
    params.otherMap = {
      skin: "",
      realNameStatus: ""
    };

    // 实名情况和星元皮肤 勾选后 需传参   商品主图展示用
    dataForm.attributesList.map((item: any) => {
      if (item.name == "实名情况" && item.attributeIds) {
        let realName = item.children.find((ele: any) => ele.id == item.attributeIds);
        params.otherMap.realNameStatus = realName.name;
      }
      if (item.name == "星元皮肤") {
        params.otherMap.skin = item.attributeIds.join(",");
      }
      if (item.name == "贵族等级" && item.attributeIds) {
        try {
          let vipLevelName = item.children.find((ele: any) => ele.id == item.attributeIds);
          params.otherMap.vipLevel = vipLevelName.name;
        } catch (error) {}
      }
    });
  }

  screenshotLoading.value = true;
  baseService
    .post("/autoCheck/autoCheck/screenshot", params)
    .then((res: any) => {
      // setTimeout(() => {
      //   let { logoImage, detailImage } = res.data;
      //   dataForm.log = logoImage ? [logoImage] : [];
      //   dataForm.imagesList = detailImage ? detailImage : [];
      // }, 1000);
      const roleIndex = dataForm.attributesList.findIndex((obj: any) => obj.typeId == "2025042209");
      if (roleIndex < 0) {
        dataForm.attributesList.push({
          typeId: "2025042209",
          attributeText: params.taskId
        });
      } else {
        dataForm.attributesList[roleIndex].attributeText = params.taskId;
      }
      ElMessage.success("截图任务已提交，请稍后查看");
      dataForm.log = ["https://oss.nyyyds.com/upload/20250423/836aa7f50dc24d89b9c8fc7f1b082fec.png"];
      dataForm.imagesList = ["https://oss.nyyyds.com/upload/20250423/836aa7f50dc24d89b9c8fc7f1b082fec.png"];
    })
    .finally(() => {
      screenshotLoading.value = false;
    });
};

// 发布
const releasedCommand = (e: number) => {
  if (e == 1) {
    dataForm.release = false;
    dataFormSubmitHandle("");
  } else {
    selectPartnersHandle();
  }
};

// 显示选择合作商
const selectPartnersRef = ref();
const selectPartnersKey = ref(0);
const selectPartnersHandle = async () => {
  selectPartnersKey.value++;
  await nextTick();
  selectPartnersRef.value.init();
};

// 推送发布
const pushSubmit = (ids: any) => {
  dataForm.release = true;
  dataForm.partnerIds = ids;
  dataFormSubmitHandle("");
};

// 黑号信息查询
const BlackNumber = (type: number) => {
  if (type == 1) {
    // 游戏账号
    if (!dataForm.gameAccount) return;
    baseService
      .post("/shop/shop/checkAccountIsBlack", {
        gameAccount: dataForm.gameAccount
      })
      .then((res) => {
        console.log(res);
      })
      .catch(() => {
        ElMessageBox.confirm("当前账号标记为黑号，是否保存该账号?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {})
          .catch(() => {
            dataForm.gameAccount = "";
          });
      });
  }
  if (type == 2) {
    // 手机号码
    if (!dataForm.phone) return;
    baseService
      .post("/shop/shop/checkAccountIsBlack", {
        phone: dataForm.phone
      })
      .then((res) => {})
      .catch(() => {
        ElMessageBox.confirm("当前手机号码标记为黑号，是否保存该号码?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {})
          .catch(() => {
            dataForm.phone = "";
          });
      });
  }
};

const taskType = ref(1);
const typeList = [
  {
    typeCode: 1,
    typeName: "寄售"
  },
  {
    typeCode: 2,
    typeName: "上架"
  },
  // {
  //   typeCode: 3,
  //   typeName: "擦亮"
  // },
  {
    typeCode: 4,
    typeName: "编辑"
  },
  {
    typeCode: 5,
    typeName: "下架"
  }
];

const typeListAPI = [
  {
    typeCode: 1,
    typeName: "上架"
  },
  {
    typeCode: 2,
    typeName: "下架"
  }
];

const thirdType = ref("1");
const thirdPartyRef = ref();
const replyLoading = ref(false);
const pushAllSelect = () => {
  thirdPartyRef.value.pushSubmit(taskType.value);
};

const spliceChildren = (item: any) => {
  let list = JSON.parse(JSON.stringify(item.children));
  return !item.fold ? list : list.splice(0, 4);
};

// 瞄点菜单
const menuIndex = ref(0);
const sectionRefs = ref([]);
// 收集所有内容区域的ref
const setSectionRef = (el) => {
  if (el) {
    sectionRefs.value.push(el);
  }
};

// 滚动到指定区域
const scrollTo = async (index: any) => {
  menuIndex.value = index;
  await nextTick(); // 等待DOM更新
  sectionRefs.value[index]?.scrollIntoView({
    behavior: "smooth",
    block: "start"
  });
};

// 自定义懒渲染指令
const vLazyRender = {
  mounted(el: any, binding: any) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 这里可以添加动画效果或直接显示内容
            el.style.opacity = 1;
            observer.unobserve(el);
          }
        });
      },
      {
        threshold: 0.1
      }
    );

    observer.observe(el);
    el.style.opacity = 0;
    el.style.transition = "opacity 0.5s ease";
  }
};

const updateInput = (payload: any) => {
  if (payload.user) {
    dataForm.gameAccount = payload.user;
  }
  if (payload.pwd) {
    dataForm.gamePassword = payload.pwd;
  }
};

const handleAttributeSearch = () => {
  showSearchCard.value = true;
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 2) {
      item.childrenOrign = item.children.filter((ele) => ele.name.includes(searchBlurText.value));
    }
  });
};
const handleAttributeSearchReset = () => {
  searchBlurText.value = "";
  showSearchCard.value = false;
  dataForm.attributesList.forEach((item: any) => {
    if (item.type == 2) {
      item.childrenOrign = [];
    }
  });
};

defineExpose({
  init,
  drawerCloseHandle
});
</script>

<style lang="less" scoped>
.shop_page {
  background-color: #f0f2f5;
  // border-radius: 8px;
  padding: 12px;
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}

.attributeBg {
  background: url("../../assets/images/attributes_auth_bg.png") center no-repeat;
  background-size: 100% 100%;
}

:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: flex-start !important;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}

.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}

.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}

.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .flod_tab_cont {
    flex: 1;
    overflow: hidden;

    :deep(.el-radio) {
      margin-right: 20px;
    }

    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}

.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}

.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .shop_info_left {
    display: flex;
    align-items: center;

    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }

  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}

.shop_tabs {
  display: flex;
  align-items: center;

  .shop_tabs_item {
    width: 120px;
    height: 38px;
    background-color: #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #303133;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;

    &:nth-child(1) {
      border-radius: 8px 0px 0px 0px;
    }
    &:nth-child(2) {
      border-radius: 0px 8px 0px 0px;
    }
  }
  .active {
    background-color: #fff;
    font-weight: 700;
    color: var(--el-color-primary);
  }
}

.container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  position: relative;
  .menu_left {
    width: 104px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    .menu_li {
      padding: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #303133;
      cursor: pointer;
    }
    .menu_active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }
  .menu_right {
    // border: 1px solid red;
    flex: 1;
    height: 59vh;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: auto;

    .menu_card {
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      background: #f7f8fa;
      padding: 12px 12px 0px 12px;
      .menu_title {
        font-size: 14px;
        font-weight: 700;
        line-height: 12px;
        margin-bottom: 8px;
      }
      .menu_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .name {
          color: #606266;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
        .operate {
        }
      }
      .menu_center {
        :deep(.el-checkbox__label) {
          width: 160px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
:deep(.demo-tabs > .el-tabs__header) {
  margin: 0 20px 0;
}
</style>
<style lang="less">
// .demo-tabs > .el-tabs__header {
//   margin: 0 20px 0;
// }
.drawer_shop {
  padding: 0px;
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .el-dialog__headerbtn {
    top: 8px;
    right: 8px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 20px;
    margin-left: 20px;
  }

  .el-drawer__body {
    padding: 0px;
  }
}
.searchCard {
  position: absolute;
  padding: 12px;
  top: 0;
  right: 8px;
  z-index: 99;
  width: calc(100% - 124px);
  border-radius: 4px;
  border: 1px solid var(--el-color-primary);
  background: var(--white, #fff);
  box-shadow: 0px 10px 32px rgba(38, 38, 38, 0.18);
  max-height: 400px;
  overflow-y: scroll;
}
</style>
