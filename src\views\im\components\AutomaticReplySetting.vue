<template>
    <el-dialog width="800px" v-model="dialogVisible" class="im-custom-dialog automatic-reply-dialog" @close="close">
        <template #header="{ close, titleId, titleClass }">
            <div class="im-custom-dialog-header">
                <p class="dialog-title">自动回复设置</p>
            </div>
        </template>
        
        <div class="automatic-reply-dialog-content flx" v-loading="dataLoading"> 
            <el-form label-position="top" ref="formRef" class="w-100">
                <el-form-item label="是否启用自动回复">
                   <el-switch v-model="status" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用"/>
                </el-form-item>
                <el-tabs v-model="currentType" class="demo-tabs">
                    <el-tab-pane label="新会话自动回复内容" name="1">
                        <el-input type="textarea" :rows="10" v-model="sayHelloReply" placeholder="请输入自动回复内容"></el-input>
                    </el-tab-pane>
                    <el-tab-pane label="离线自动回复内容" name="2">
                        <el-input type="textarea" :rows="10" v-model="offlineReply" placeholder="请输入自动回复内容"></el-input>
                    </el-tab-pane>
                </el-tabs>
                <div class="tips mt-8 flx-align-center">
                    <el-icon><WarningFilled /></el-icon>
                    {{  currentType == '1' ? '用户主动发起新群聊后首次回复内容' : '客服离线自动回复内容' }}
                </div>
            </el-form>
        </div>

        <div class="flx-justify-end automatic-reply-footer">
            <el-button :loading="btnLoading" @click="handleCancel">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleSubmit">确定</el-button>
        </div>
        
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineProps, watch, nextTick } from 'vue';
    import { WarningFilled } from '@element-plus/icons-vue';
    import { useImStore } from '@/store/im';
    import baseService from "@/service/baseService";
    import { ElMessage } from 'element-plus';


    const imStore = useImStore();

    const props = defineProps({
        visible: Boolean
    })

    const currentType = ref('1');

    // 打招呼回复
    const sayHelloReply = ref('');
    // 离线回复
    const offlineReply = ref('');
    // 启用状态
    const status = ref(0);
    
    const dialogVisible = ref(false);

    const init = () => {
        dialogVisible.value = true;
        
    }

    // 取消
    const handleCancel = () => {
        dialogVisible.value = false;
        close();
    }

    const close = () => {
        imStore.showAutoReplyDialog = false;
    }


    // 获取自动回复
    const dataLoading = ref(false);
    const sayHelloReplyObj = ref({});
    const offlineReplyObj = ref({});

    const getAutoReply = () => {
        dataLoading.value = true;
        baseService.post('/im/login/get/auto?imUid=' + imStore.imUid).then(res => {
            if(res.code == 0){
                if(res.data && res.data.length){
                    sayHelloReplyObj.value = res.data.find(item => item.type == '1');
                    offlineReplyObj.value = res.data.find(item => item.type == '2');
                    sayHelloReply.value = sayHelloReplyObj.value ? sayHelloReplyObj.value.msg : '';
                    offlineReply.value = offlineReplyObj.value ? offlineReplyObj.value.msg : '';    
                    status.value = res.data[0].status;
                }
            }
        }).finally(() => {
            dataLoading.value = false;
        })
    }

    
    // 提交
    const btnLoading = ref(false);
    const formRef = ref();
    const handleSubmit = () => {
        if((!sayHelloReply.value || !offlineReply.value) && status.value) {
            currentType.value = !sayHelloReply.value ? '1' : '2';
            return ElMessage.warning('请输入自动回复内容');
        }
        
        let params = [
            {
                type: '1',
                status: status.value,
                msg: sayHelloReply.value,
                imUid: imStore.imUid,
                id: sayHelloReplyObj.value ? sayHelloReplyObj.value.id : ''
            },
            {
                type: '2',
                status: status.value,
                msg: offlineReply.value,
                imUid: imStore.imUid,
                id: offlineReplyObj.value ? offlineReplyObj.value.id : ''
            }
        ]
        baseService.post('/im/login/create/auto', params).then(res => {
            if(res.code == 0){
                ElMessage.success('操作成功！');
                close();
            }
        })
    }

    watch(() => props.visible, (newValue) => {
        dialogVisible.value = newValue;
        if(newValue) getAutoReply();
    }, { immediate: true, deep: true })


    defineExpose({
        init
    })
</script>

<style lang="scss">
    .automatic-reply-dialog-content{
        padding: 16px;

        .tips{
            font-size: 12px;
            color: #909399;

            .el-icon{
                margin-right: 5px;
            }
        }
    }

    .automatic-reply-footer{
        padding: 0 16px 16px 0;
    }
</style>