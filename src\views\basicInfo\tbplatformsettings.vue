<template>
  <el-card shadow="never" class="rr-view-ctx-card cardTop">
    <div class="mainBox">
      <div class="leftPart">
        <ul class="menuUl">
          <li
            :class="'menuLi ' + (activeName == item.label ? 'active' : '')"
            v-for="(item, itemIndex) in [
              { label: '商城设置', value: '商城设置' },
              { label: '后台管理系统设置', value: '后台管理系统设置' },
              { label: '商品订单相关设置', value: '商品订单相关设置' },
              { label: '用户协议设置', value: '用户协议设置' },
              { label: 'APP版本管理', value: 'APP版本管理' }
            ]"
            :key="itemIndex"
            @click="changeTab(item.label)"
            :index="itemIndex"
          >
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
      <div class="demo-tabs" style="flex: 1">
        <div class="pagetitle">
          <div style="font-weight: bold; font-size: 16px; color: #303133">{{ activeName }}</div>
          <div class="button" v-if="activeName != 'APP版本管理'">
            <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle">保存更改</el-button>
          </div>
          <div class="button" v-else>
            <el-button type="primary" :loading="btnLoading" @click="addOrUpdateHandle()">新增版本</el-button>
          </div>
        </div>
        <template v-if="activeName == '商城设置'">
          <div style="width: fit-content; margin-top: -8px">
            <ny-button-group
              style="margin-bottom: 11px"
              :list="[
                { dictLabel: '平台设置', dictValue: '平台设置' },
                { dictLabel: 'PC端设置', dictValue: 'PC端设置' },
                { dictLabel: '移动端设置', dictValue: '移动端设置' }
              ]"
              v-model="activeChilderName"
            ></ny-button-group>
          </div>
          <template v-if="activeChilderName == '平台设置'">
            <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
              <div class="titleSty">平台检索设置</div>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item class-name="noneSelfRight" label="平台名称">
                  <el-form-item label="平台名称" prop="seoPlatformName">
                    <el-input v-model="dataForm.seoPlatformName" placeholder="请输入平台名称"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="SEO标题">
                  <el-form-item label="SEO标题" prop="seoTitle">
                    <el-input v-model="dataForm.seoTitle" placeholder="请输入SEO标题"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="SEO描述">
                  <el-form-item label="SEO描述" prop="seoDescription">
                    <el-input v-model="dataForm.seoDescription" placeholder="请输入SEO描述" type="textarea" :rows="5"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="SEO关键词">
                  <el-form-item label="SEO关键词" prop="seoKeywords">
                    <el-input v-model="dataForm.seoKeywords" placeholder="请输入SEO关键词" type="textarea" :rows="5"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="外链地址">
                  <el-form-item label="外链地址">
                    <el-input v-model="dataForm.websiteUrl" placeholder="请输入外链地址"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="接口上传地址">
                  <el-form-item label="接口上传地址">
                    <el-input v-model="dataForm.interfaceAddressUrl" placeholder="接口上传地址"></el-input>
                  </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label="ICO图标">
                  <el-form-item label="ICO图标" prop="seoIco">
                    <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.seoIco" type="image"></ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
              <div class="titleSty">平台用户设置</div>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item label="用户默认头像">
                  <el-form-item label="用户默认头像" prop="userDefaultAvatar">
                    <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.userDefaultAvatar" type="image"></ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </el-form>
          </template>
          <template v-if="activeChilderName == 'PC端设置'">
            <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item class-name="noneSelfRight" label="PC端网址">
                  <el-form-item label="PC端网址" prop="pcWebsiteUrl">
                    <el-input v-model="dataForm.pcWebsiteUrl" placeholder="PC端网址"></el-input>
                    <el-alert class="alertSty" title="带http或https，示例：https://www.baidu.com" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label=""> </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="PC端LOGO">
                  <el-form-item label="PC端LOGO" prop="pcLogo">
                    <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.pcLogo" type="image"></ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="PC端标题图片">
                  <el-form-item label="PC端标题图片" prop="pcTitlePic">
                    <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.pcTitlePic" type="image"></ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="标识语">
                  <el-form-item label="标识语" prop="pcTips">
                    <el-input v-model="dataForm.pcTips" placeholder="请输入标识语"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="ICP备案">
                  <el-form-item label="ICP备案" prop="pcIcp">
                    <el-input v-model="dataForm.pcIcp" placeholder="请输入ICP备案"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="公网安备案号">
                  <el-form-item label="公网安备案号" prop="pcPoliceIcp">
                    <el-input v-model="dataForm.pcPoliceIcp" placeholder="请输入公网安备案号"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="黑名单日查次数">
                  <el-form-item label="黑名单日查次数" prop="blackListQueryLimit"> <el-input-number v-model="dataForm.blackListQueryLimit" placeholder="请输入"></el-input-number><span style="margin-left: 4px">次</span> </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="底部信息" :span="2">
                  <el-form-item label="底部信息" prop="pcFooterInfo">
                    <el-input type="textarea" :rows="5" v-model="dataForm.pcFooterInfo" placeholder="请输入底部信息"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="百度统计" :span="2">
                  <el-form-item label="百度统计" prop="pcBaiduStatiscs">
                    <el-input type="textarea" :rows="5" v-model="dataForm.pcBaiduStatiscs" placeholder="请输入百度统计"></el-input>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </el-form>
          </template>
          <template v-if="activeChilderName == '移动端设置'">
            <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item class-name="noneSelfRight" label="H5端网址">
                  <el-form-item label="H5端网址" prop="mobileWebsiteUrl">
                    <el-input v-model="dataForm.mobileWebsiteUrl" placeholder="请输入H5端网址"></el-input>
                    <el-alert class="alertSty" title="带http或https，示例：https://www.baidu.com" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label=""> </el-descriptions-item>
                <el-descriptions-item label="移动端LOGO" :span="2">
                  <el-form-item label="移动端LOGO" prop="mobileLogo">
                    <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.mobileLogo" type="image"></ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="APP下载地址">
                  <el-form-item label="APP下载地址" prop="mobileDownloadUrl">
                    <el-input v-model="dataForm.mobileDownloadUrl" placeholder="请输入APP下载地址"></el-input>
                    <el-alert class="alertSty" title="Android" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="APP版本号">
                  <el-form-item label="APP版本号" prop="mobileVersion">
                    <el-input v-model="dataForm.mobileVersion" placeholder="请输入APP版本号"></el-input>
                    <el-alert class="alertSty" title="请保持和Hbuilder打包的版本号一致，若不一致，强制用户升级" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="更新说明" :span="2">
                  <el-form-item label="更新说明" prop="mobileUpdateDescription">
                    <el-input type="textarea" v-model="dataForm.mobileUpdateDescription" placeholder="请输入更新说明" :rows="5"></el-input>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </el-form> </template
        ></template>
        <template v-if="activeName == '后台管理系统设置'">
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
            <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
              <el-descriptions-item class-name="noneSelfRight" label="后台名称">
                <el-form-item label="后台名称" prop="backendTitle">
                  <el-input v-model="dataForm.backendTitle" placeholder="请输入后台名称"></el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label=""> </el-descriptions-item>
              <el-descriptions-item :span="2" class-name="noneSelfRight" label="后台LOGO">
                <el-form-item label="后台LOGO" prop="backendLogo">
                  <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.backendLogo" type="image"></ny-upload-file>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item class-name="noneSelfRight" label="员工默认头像">
                <el-form-item label="员工默认头像" prop="backendDefaultAvatar">
                  <ny-upload-file listType="picture-card" uploadPreText="将图片拖到此处或" tip="建议JPEG、PNG格式，2M以内" widthUpload="120px" heightUpload="120px" :imgContain="true" v-model:fileSrc="dataForm.backendDefaultAvatar" type="image"></ny-upload-file>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
          </el-form>
        </template>
        <template v-if="activeName == '商品订单相关设置'">
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
            <div class="titleSty">商品设置</div>
            <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
              <el-descriptions-item class-name="noneSelfRight" label="商品出售费率">
                <el-form-item label="商品出售费率" prop="shopSaleRate">
                  <el-input type="number" v-model="dataForm.shopSaleRate" placeholder="请输入商品出售费率">
                    <template #append>%</template>
                  </el-input>
                  <el-alert class="alertSty" title="商品出售最终收益 = 商品出售价 * 商品出售费率" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="违约金">
                <el-form-item label="违约金" prop="liquidatedDamages">
                  <el-input type="number" v-model="dataForm.liquidatedDamages" placeholder="请输入违约金比率">
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item class-name="noneSelfRight" label="出售商品需要实名认证">
                <template #label>
                  <span>出售商品需要<br />实名认证</span>
                </template>
                <el-form-item label="出售商品需要实名认证" prop="shopRealnameSale">
                  <el-switch v-model="dataForm.shopRealnameSale" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="购买商品需要实名认证">
                <template #label>
                  <span>购买商品需要<br />实名认证</span>
                </template>
                <el-form-item label="购买商品需要实名认证" prop="shopRealnameBuy">
                  <el-switch v-model="dataForm.shopRealnameBuy" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用" />
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <div class="titleSty">议价设置</div>
            <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
              <el-descriptions-item class-name="noneSelfRight" label="买家出价限制">
                <el-form-item label="买家出价限制" prop="bargainRateLimit">
                  <el-input v-model="dataForm.bargainRateLimit" placeholder="请输入买家出价限制">
                    <template #suffix>%</template>
                  </el-input>
                  <el-alert class="alertSty" title="0为不限制，不能低于原价的%" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="买家每个商品议价次数">
                <template #label>
                  <span>买家每个商品<br />议价次数</span>
                </template>
                <el-form-item label="买家每个商品议价次数" prop="bargainCountLimit">
                  <el-input v-model="dataForm.bargainCountLimit" placeholder="请输入买家每个商品议价次数">
                    <template #suffix>次/天</template>
                  </el-input>
                  <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item class-name="noneSelfRight" label="议价有效时间">
                <el-form-item label="议价有效时间" prop="bargainTimeLimit">
                  <el-input v-model="dataForm.bargainTimeLimit" placeholder="请输入议价有效时间">
                    <template #suffix>小时</template>
                  </el-input>
                  <el-alert class="alertSty" title="0为默认三天，议价成功后多少个小时后失效" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="买家总议价次数">
                <el-form-item label="买家总议价次数" prop="bargainTotalLimit">
                  <el-input v-model="dataForm.bargainTotalLimit" placeholder="请输入买家总议价次数">
                    <template #suffix>次/天</template>
                  </el-input>
                  <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <div class="titleSty">擦亮设置</div>
            <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
              <el-descriptions-item class-name="noneSelfRight" label="商品擦亮提示">
                <el-form-item label="商品擦亮提示" prop="shopRehotTips">
                  <el-input v-model="dataForm.shopRehotTips" placeholder="请输入商品擦亮提示"></el-input>
                  <el-alert class="alertSty" title="商品擦亮会更新商品排序" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="商品一天擦亮次数">
                <el-form-item label="商品一天擦亮次数" prop="shopRehotCount">
                  <el-input v-model="dataForm.shopRehotCount" placeholder="请输入商品一天擦亮次数"></el-input>
                  <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <div class="titleSty">订单设置</div>
            <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
              <el-descriptions-item label="商品售出审核">
                <template #label>
                  <span>商品售出审核</span>
                </template>
                <el-form-item label="商品售出审核" prop="salesOrderReview">
                  <el-switch v-model="dataForm.salesOrderReview" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用" @change="salesOrderReviewChange"/>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
          </el-form>
        </template>
        <template v-if="activeName == '用户协议设置'">
          <div style="width: fit-content; margin-top: -8px">
            <ny-button-group
              style="margin-bottom: 11px"
              :list="[
                { dictLabel: '用户协议', dictValue: '用户协议' },
                { dictLabel: '隐私政策', dictValue: '隐私政策' },
                { dictLabel: '卖家须知', dictValue: '卖家须知' },
                { dictLabel: '购买须知', dictValue: '购买须知' },
                { dictLabel: '购买指南', dictValue: '购买指南' },
                { dictLabel: '出售指南', dictValue: '出售指南' }
              ]"
              v-model="activeChilderName"
            ></ny-button-group>
          </div>
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto" class="editorForm">
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.userAgreement" v-if="activeChilderName == '用户协议'"></wangEditor>
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.privateAgreement" v-if="activeChilderName == '隐私政策'"></wangEditor>
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.buyerAgreement" v-if="activeChilderName == '卖家须知'"></wangEditor>
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.sellerAgreement" v-if="activeChilderName == '购买须知'"></wangEditor>
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.purchaseGuide" v-if="activeChilderName == '购买指南'"></wangEditor>
            <wangEditor style="height: calc(100vh - 340px)" v-model="dataForm.sellingGuide" v-if="activeChilderName == '出售指南'"></wangEditor>
          </el-form>
        </template>
        <template v-if="activeName == 'APP版本管理'">
          <ny-table style="width: 100%; margin: auto" :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
            <template #mandatory="{ row }">
              <el-switch v-model="row.mandatory" @change="updateSwitch(row)" />
            </template>
            <template #status="{ row }">
              <el-switch v-model="row.status" @change="updateSwitch(row)" />
            </template>
            <template #operation="{ row }">
              <el-button type="primary" text bg @click="addOrUpdateHandle(row.id)">
                {{ $t("update") }}
              </el-button>
              <el-button type="danger" text bg @click="state.deleteHandle(row.id)" :disabled="row.id === '10000'">
                {{ $t("delete") }}
              </el-button>
            </template>
          </ny-table>
          <!-- 新增编辑 -->
          <versioningAddOrUpdate ref="versioningAddOrUpdateRef" @refreshDataList="state.getDataList"></versioningAddOrUpdate>
        </template>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs, nextTick } from "vue";
import useView from "@/hooks/useView";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import versioningAddOrUpdate from "./versioning-add-or-update.vue";

const { t } = useI18n();
const activeName = ref("商城设置");
const activeChilderName = ref("平台设置");
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  //平台名称
  seoPlatformName: "",
  //SEO标题
  seoTitle: "",
  //SEO关键词
  seoKeywords: "",
  //SEO描述
  seoDescription: "",
  // 外链地址  (跳转估价网站， 收集包赔， 选号网站)
  websiteUrl: "",
  // 上传接口地址 (商城  选号网站 上传图片接口地址)
  interfaceAddressUrl: "",
  //ICO图标
  seoIco: "",
  //PC端网址
  pcWebsiteUrl: "",
  //PC端LOGO
  pcLogo: "",
  // PC端标题图片
  pcTitlePic: "",
  //标识语
  pcTips: "",
  //ICP备案
  pcIcp: "",
  //公网安备案号
  pcPoliceIcp: "",
  // 黑名单日查次数
  blackListQueryLimit: "",
  //底部信息
  pcFooterInfo: "",
  //百度统计
  pcBaiduStatiscs: "",
  //H5端网址
  mobileWebsiteUrl: "",
  //移动端LOGO
  mobileLogo: "",
  //APP下载地址
  mobileDownloadUrl: "",
  //APP版本号
  mobileVersion: "",
  //更新说明
  mobileUpdateDescription: "",
  //后台名称
  backendTitle: "",
  //后台LOGO
  backendLogo: "",
  //员工默认头像
  backendDefaultAvatar: "",
  //用户默认头像
  userDefaultAvatar: "",
  //商品出售费率
  shopSaleRate: "",
  // 商品违约金
  liquidatedDamages: "",
  //出售商品需要实名认证
  shopRealnameSale: 0,
  //购买商品需要实名认证
  shopRealnameBuy: 0,
  // 买家出价限制
  bargainRateLimit: "",
  // 买家商品议价次数
  bargainCountLimit: "",
  // 买家议价总次数
  bargainTotalLimit: "",
  // 议价有效时间
  bargainTimeLimit: "",
  // 商品擦亮提示
  shopRehotTips: "",
  // 商品一天擦亮次数
  shopRehotCount: "",
  // 用户协议
  userAgreement: "",
  // 隐私政策
  privateAgreement: "",
  // 买家须知
  buyerAgreement: "",
  // 购买须知
  sellerAgreement: "",
  // 购买指南
  purchaseGuide: "",
  // 出售指南
  sellingGuide: "",
  // 销售订单审核
  salesOrderReview: 0,
});

const rules = ref({
  // platformName: [{required: true, message: t("validate.required"), trigger: "blur"}],
});
onMounted(() => {
  getInfo();
});
// 切换tab
const changeTab = (name?: any) => {
  activeName.value = name;
  name == "商城设置" ? (activeChilderName.value = "平台设置") : name == "用户协议设置" ? (activeChilderName.value = "用户协议") : "";
  // getInfo();
};
// 获取信息
const getInfo = () => {
  baseService.get("/global/setting/get").then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    baseService
      .put("/global/setting/update", dataForm)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 版本管理表格
const view = reactive({
  getDataListURL: "/globalSetting/sysapp/page",
  getDataListIsPage: true,
  deleteURL: "/globalSetting/sysapp",
  deleteIsBatch: true
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    prop: "wgtUrl",
    label: "版本名称",
    width: 180
  },
  {
    prop: "version",
    label: "版本号",
    width: 180
  },
  {
    prop: "info",
    label: "更新内容",
    width: 340
  },
  {
    prop: "mandatory",
    label: "强制更新",
    width: 100
  },
  {
    prop: "status",
    label: "开启",
    width: 100
  },
  {
    prop: "apkUrl",
    label: "下载地址"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    Width: 80
  }
]);

const addKey = ref(0);
const versioningAddOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    versioningAddOrUpdateRef.value.init(id);
  });
};

const updateSwitch = (row: any) => {
  baseService.put("/globalSetting/sysapp", row).then((res) => {
    if (res && res.code == 0) {
      ElMessage.success("保存成功");
    }
    state.getDataList;
  });
};

const salesOrderReviewChange = (val: boolean | string | number) =>{
  if(val){
     ElMessageBox.confirm(
    '使用商品售出审核流程，须指定部门负责人;',
    '提示',
    {
      confirmButtonText: '知道了',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      
    })
    .catch(() => {
      dataForm.salesOrderReview = 0;
    })
  }
}

</script>

<style scoped lang="less">
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        flex: 1;
        > div {
          width: 100%;
        }
      }
    }
  }
}

:deep(.el-descriptions-item) {
  flex-basis: 50%; /* 使得每个item占据50%的宽度 */
}
</style>

<style scoped lang="less">
.pagetitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  .label {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
.el-alert {
  padding: 0;
  background: none;
}
:deep(.el-form-item__label) {
  padding-right: 0;
}
:deep(.el-tabs__item) {
  padding: 0 !important;

  &.is-active {
    font-weight: bold;
  }
}
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.mainBox {
  display: flex;
  margin-top: 12px;
  .leftPart {
    margin-right: 24px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .menuUl {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      .menuLi {
        width: 186px;
        cursor: pointer;
        padding: 20px;
        word-break: keep-all;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #303133;
        line-height: 14px;
        &.active {
          background-color: var(--color-primary-light);
          color: var(--color-primary);
        }
      }
    }
  }
  .el-descriptions {
    :deep(.el-form-item__label) {
      display: none;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
    :deep(.el-descriptions__label) {
      font-weight: normal;
      width: 144px;
    }
    :deep(.el-descriptions__content) {
      min-height: 80px;
    }
    :deep(.noneSelfRight) {
      border-right: 0 !important;
    }
    :deep(.noneSelfLeft) {
      border-left: 0 !important;
    }
    :deep(.noneSelfLabel) {
      background: none;
      border-left: 0 !important;
      border-right: 0 !important;
    }
  }
}

.uploadFileStyle {
  :deep(.file-uploader) {
    display: flex;
    flex-direction: column;

    .el-upload-list {
      width: 100%;
    }
    .el-upload--picture-card {
      height: fit-content !important;
      width: fit-content !important;
      border: none;
      background-color: none;
    }
    .el-upload-list__item {
      margin-bottom: 0;
    }
  }
}
.editorForm {
  > div {
    border-radius: 8px;
    border: 1px solid #e9ecef !important;
    overflow: hidden;
  }
}
</style>
