<script lang="ts">
import app from "@/constants/app";
import { defineComponent, reactive, computed } from "vue";
import { useI18n } from "vue-i18n";
import emits from "@/utils/emits";
import { EMitt, EThemeSetting } from "@/constants/enum";
import { getThemeConfigCacheByKey } from "@/utils/theme";
import { useSettingStore } from '@/store/setting';
interface ILogo {
  logoUrl?: string;
  logoName?: string;
}

/**
 * 顶部logo
 */
export default defineComponent({
  name: "Logo",
  props: {
    logoUrl: String,
    logoName: {
      type: String,
      default: "logo"
    }
  },
  setup(props: ILogo) {
    const sttingInfo = useSettingStore();
    const { t } = useI18n(); //国际化
    const state = reactive({
      collapseSidebar: getThemeConfigCacheByKey(EThemeSetting.SidebarCollapse)
    });
    emits.on(EMitt.OnSwitchLeftSidebar, () => {
      state.collapseSidebar = !state.collapseSidebar;
    });
    
    return { props, t, app, state, sttingInfo };
  }
});
</script>

<template>
  <span :class="`rr-header-ctx-logo-img-wrap ${'enabled-logo-' + app.enabledLogo}`">
    
    <!-- 支持显示图片logo或 者产品名称缩写，二选一模式，通过注释开启功能，app.enabledLogo控制正常模式下图片logo是否显示，如果有图片logo，收起状态会强制显示图片logo -->
    <!-- <img :src="props.logoUrl" class="rr-header-ctx-logo-img" :alt="props.logoName" /> -->
    <img :src="sttingInfo.info.backendLogo" class="rr-header-ctx-logo-img" />
    <span class="rr-header-ctx-logo-name" v-if="!state.collapseSidebar">{{ sttingInfo.info.backendTitle }}</span>
    <!-- t("ui.app.productName") -->
    <span class="rr-header-ctx-logo-line"></span>
  </span>
  <!-- <span class="rr-header-ctx-logo-text">{{ props.logoName }}</span> -->
</template>
