<template>
  <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
    <ny-flod-tab
      type="card"
      :list="[
        { label: '精准估价', value: '0' },
        { label: '价格表', value: '1' }
      ]"
      v-model="currentTypeIndex"
      value="value"
      label="label"
    ></ny-flod-tab>

    <!-- 精确估价 -->
    <valuation v-if="currentTypeIndex == 0"></valuation>

    <!-- 价格表 -->
    <attributePrice v-if="currentTypeIndex == 1"></attributePrice>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Valuation from "./accurateValuation.vue";
import attributePrice from "./attributePrice.vue";

const currentTypeIndex = ref(0);
</script>
