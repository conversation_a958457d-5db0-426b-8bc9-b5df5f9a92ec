<template>
    <div >
        <el-container class="stat_pages">
            <el-aside width="256px" style="height: calc(100vh - 180px);" v-show="menuIsShow">
                <div class="stat_menu">
                    <div class="menu_top flx-justify-between">
                        <div class="title">报表目录</div>
                        <div class="butList">
                            <el-button :icon="RefreshRight" size="small" @click="getCatalogue"/>
                            <el-button :icon="Plus" size="small" @click="addOrUpdateHandle('1','add')"/>
                            <el-button :icon="DArrowLeft" size="small" @click="menuIsShow = !menuIsShow"/>
                        </div>
                    </div>
                    <div class="menu_list">
                        <el-tree
                            style="max-width: 256px"
                            :data="dataSource"
                            node-key="id"
                            :props="{label:'title'}"
                            default-expand-all
                            :expand-on-click-node="false"
                            @node-click="menuChange"
                        >
                            <template #default="{ node, data }">
                                <span class="flx-justify-between" style="width: 100%;padding: 8px 8px 8px 0;" v-if="menuChangeID == data.id">
                                    <span>{{ node.label }}</span>
                                    <div class="operate">
                                        <el-icon v-if="data.pid == '0'" @click="addOrUpdateHandle('2','add',data)"><Plus /></el-icon>
                                        <el-icon @click="addOrUpdateHandle((data.pid == '0'? '1':'2'),'edit',data)"><Edit/></el-icon>
                                        <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定删除该目录吗？" @confirm="menuDelete(data.id)">
                                            <template #reference>
                                                <el-icon><Delete /></el-icon>
                                            </template>
                                        </el-popconfirm>
                                    </div>
                                </span>
                            </template>
                        </el-tree>
                    </div>
                </div>
            </el-aside>
            <el-button :icon="DArrowRight" size="small" v-show="!menuIsShow" @click="menuIsShow = !menuIsShow"/>
            <el-main>
                <contentPage ref="contentPageRef" :menuInfo="menuInfo" v-if="menuInfo"></contentPage>
            </el-main>
        </el-container>

        <!-- 新增/编辑报表 -->
        <addOrUpdate ref="addOrUpdateRef" @refreshDataList='getCatalogue' :key="addKey"></addOrUpdate>
    </div>
</template>

<script lang='ts' setup>
import { ref, reactive, nextTick, onMounted } from 'vue';
import { RefreshRight, Plus, DArrowLeft, DArrowRight } from '@element-plus/icons-vue';
import contentPage from './components/contentPage.vue';
import addOrUpdate from './add-or-update.vue';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const dataSource = ref<any[]>([])

const menuIsShow = ref(true);

// 菜单点击
const menuInfo = ref(<any>'');
const menuChangeID = ref(0);
const menuChange = (e:any) =>{
    menuChangeID.value = e.id;
    if(e.pid != '0'){
        menuInfo.value = e;
    }
    
}

const contentPageRef = ref();

const addKey = ref(0);
const addOrUpdateRef = ref();
// type : 1(分类) 2(报表)
const addOrUpdateHandle = (type:any,operate:string,data?:any) => {
    addKey.value++;
    nextTick(() => {
        addOrUpdateRef.value.init(type,operate,data);
    });
};

// 获取目录
const getCatalogue = () =>{
    baseService.get('/report/report/catalogue').then(res=>{
        dataSource.value = res.data
        menuInfo.value = dataSource.value[0].children[0] ? dataSource.value[0].children[0] : '';
    })
}

// 删除目录
const menuDelete = (id:any) =>{
    baseService.delete('/report/report',[id]).then(res=>{
        if(res.code == 0){
            ElMessage.success('删除成功');
            getCatalogue();
        }
    })
}


onMounted(()=>{
    // menuInfo.value = dataSource.value[0].children[0];
    getCatalogue()
})
</script>

<style lang='less' scoped>
.stat_pages{
    padding: 10px 20px;
    width: 100%;
    
    .el-main{
        --el-main-padding: 0px 0px 0px 12px;
    }
    
    .stat_menu{
        height: 100%;
        border-radius: 4px;
        border: 1px solid #EBEEF5;

        .menu_top{
            padding: 9px 8px;
            border-bottom: 1px solid #EBEEF5;
            .title{
                font-weight: 400;
                font-size: 14px;
                color: #303133;
                line-height: 22px;
            }
            .butList{
                .el-button--small{
                    padding: 6px 6px;
                }
                .el-button+.el-button{
                    margin-left: 4px;
                }
            }
            
        }
    }
    
}
.operate{
    .el-icon{
        color: var(--color-primary);
        margin-left: 8px;
    }
}
</style>
