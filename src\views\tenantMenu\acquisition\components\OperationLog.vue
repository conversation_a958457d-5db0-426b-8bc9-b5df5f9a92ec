<template>
    <el-drawer v-model="visible" :title="!showLog ? '订单详情' : '操作日志'"  size="40%" class="ny-drawer article-add-or-update">
      <el-card class="article-add-or-update-form" v-loading="dataLoading">
        <div class="p-title mt-0">基本信息</div>
        <el-descriptions class="descriptions-label-140" :column="1" border>
          <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
          <el-descriptions-item label="游戏大区">{{ resData.serverName ? resData.serverName : orderInfo.serverName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="卖方手机号">{{ resData.customerPhone || "-" }}</el-descriptions-item>
          <el-descriptions-item label="账号价格"
            ><el-text type="danger">￥{{ resData.amount || "-" }}</el-text></el-descriptions-item
          >
          <el-descriptions-item label="包赔费">￥{{ orderInfo.guaranteeAmount ? orderInfo.guaranteeAmount : resData.guaranteePrice ? resData.guaranteePrice : "-" }}</el-descriptions-item>
          <template v-if="!showLog">
            <el-descriptions-item label="创建时间">{{ resData.createDate }}</el-descriptions-item>
            <el-descriptions-item label="打款时间">{{ resData.payTime || "-" }}</el-descriptions-item>
            <el-descriptions-item label="换绑时间">{{ changeInfo.createDate || "-" }}</el-descriptions-item>
            <el-descriptions-item label="回收成功时间">{{ resData.dealDate || "-" }}</el-descriptions-item>
            <el-descriptions-item label="回收人">{{ resData.purchaseUserName || "-" }}</el-descriptions-item>
  
            <el-descriptions-item v-if="!showLog" label="买方手机号">{{ changeInfo.ourBindPhone || "-" }}</el-descriptions-item>
            <el-descriptions-item label="游戏密码">
              <div class="flx-justify-between" v-if="changeInfo.ourPassword">
                <span>{{ isShowGamePassword ? changeInfo.ourPassword : "******" }}</span>
                <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                  <View v-if="!isShowGamePassword" />
                  <Hide v-if="isShowGamePassword" />
                </el-icon>
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-card>
  
      <el-card class="mt-12" v-if="showLog && state.hasPermission('purchase:purchaseorder:logPage')">
        <div class="p-title mt-0">操作日志</div>
        <ny-table :state="state" :columns="columns" :pagination="false" :showColSetting="false"> </ny-table>
      </el-card>
    </el-drawer>
  </template>  
  
  <script lang="ts" setup>
  import { ref, reactive, toRefs, defineExpose } from "vue";
  import { View, Hide } from "@element-plus/icons-vue";
  import baseService from "@/service/baseService";
  import useView from "@/hooks/useView";
  
  const view = reactive({
    getDataListURL: "/purchase/logPage",
    createdIsNeed: false,
    getDataListIsPage: true,
    dataForm: {
      orderId: ""
    }
  });
  
  const state = reactive({ ...useView(view), ...toRefs(view) });
  
  const columns = reactive([
    {
      prop: "optionalUserName",
      label: "操作人"
    },
    {
      prop: "optionalTitle",
      label: "操作行为"
    },
    {
      prop: "createDate",
      label: "操作时间"
    }
  ]);
  const visible = ref(false);
  
  // 是否显示日志
  const showLog = ref(false);
  
  // 订单信息
  const orderInfo = ref(<any>{});
  
  // 换绑信息
  const changeInfo = ref(<any>{});
  
  const orderId = ref("");
  const init = (id: any, marker?: boolean, type?: string, row?: any) => {
    orderInfo.value = row && row.orderInfo ? row.orderInfo : {};
    changeInfo.value = row && row.changeInfo ? row.changeInfo : {};
    resData.value = {
      serverName: row.serverName,
      customerPhone: row.customerPhone,
      guaranteePrice: row.guaranteePrice,
      gameAccount: row.gameAccount,
      amount: row.amount,
      createDate: row.createDate,
      purchaseUserName: row.realName,
      dealDate: row.inboundTime, // 入库时间
      payDate: row.payTime, // 支付时间
    }; 
  
    visible.value = true;
    showLog.value = type == "log" ? true : false;
    orderId.value = id;
    view.dataForm.orderId = id;
    getDetails();
    state.getDataList();
  };
  const resData = ref(<any>{
    orderInfo: {},
    changeInfo: {}
  });
  const dataLoading = ref(false);
  const getDetails = () => {
    dataLoading.value = true;
    baseService
      .get("/purchase/orderLog/" + orderId.value)
      .then((res) => {
        if (res.code === 0) {
          resData.value = {
            ...resData.value,
            ...res.data
          };
        }
      })
      .finally(() => {
        dataLoading.value = false;
      });
  };
  
  // 是否显示游戏密码
  const isShowGamePassword = ref(false);
  
  // 标记售后
  const dataForm = ref(<any>{});
  
  const rules = reactive({
    type: [{ required: true, message: "请选择售后类型", trigger: "change" }],
    processingPeople: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
    screenshot: [{ required: true, message: "请上传问题截图", trigger: "change" }]
  });
  
  const formRef = ref();
  const submitForm = () => {
    formRef.value.validate((valid: boolean) => {
      if (!valid) return;
    });
  };
  defineExpose({
    init
  });
  </script>