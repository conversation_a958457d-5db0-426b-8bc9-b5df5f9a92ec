<template>
  <el-dialog class="contractTemplateDialog" v-model="visible" width="480" :title="(!dataForm.id ? $t('add') : $t('update')) + '模板'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 270px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item style="margin-bottom: 8px" prop="templateName" label="模板名称">
        <el-input clearable v-model="dataForm.templateName" placeholder="请输入合同模板名称"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="templateType" label="模板类型">
        <el-select v-model="dataForm.templateType" placeholder="请选择模板类型">
          <el-option label="收购合同" value="0" />
          <el-option label="销售合同" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="phone" label="选择主体">
        <el-select v-model="dataForm.phone" placeholder="请选择合同签署主体">
          <el-option v-for="item in options" :key="item.id" :label="item.bestsignUserName" :value="item.bestsignMobile" />
        </el-select>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="signType" label="选择签署类型">
        <el-select v-model="dataForm.signType" placeholder="请选择签署类型">
          <el-option label="个人签署模板" value="1" />
          <el-option label="企业签署模板" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="file" label="上传合同文件">
        <ny-upload-file @loadData="loadData" v-if="visible" ref="fileRef" :isContract="true" :otherUrl="otherUrl" :limit="1" v-model:fileSrc="dataForm.file" widthUpload="432px" heightUpload="185px" tip="" :isOneLine="true" accept=".pdf"></ny-upload-file>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">下一步</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  templateName: undefined,
  file: undefined,
  templateType: undefined,
  phone: undefined,
  signType: undefined
});
const options = ref([]);
const rules = ref({
  templateName: [{ required: true, message: "请输入合同模板名称", trigger: "blur" }],
  templateType: [{ required: true, message: "请选择类型", trigger: "blur" }],
  phone: [{ required: true, message: "请选择合同签署主体", trigger: "blur" }],
  signType: [{ required: true, message: "请选择签署类型", trigger: "blur" }],
  file: [{ required: true, message: "请选择文件", trigger: "change" }]
});
const otherUrl = ref();
const init = () => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  options.value = [];
  baseService.get("/bestsign/bestsignUserPage", { limit: 9999, bestsignUserType: 2 }).then((res) => {
    options.value = res.data.list || [];
  });
};
const fileRef = ref();
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate();
  if (!dataForm.templateName || !dataForm.templateType || !dataForm.phone || !dataForm.signType) {
    ElMessage.warning("请完善信息！");
    return;
  }
  otherUrl.value = `/bestsign/createTemplate?phone=${dataForm.phone}&templateName=${dataForm.templateName}&templateType=${dataForm.templateType}&signType=${dataForm.signType}`;
  fileRef.value.notAutoSubmit();
};
const loadData = () => {
  visible.value = false;
  emit("refreshDataList");
};
defineExpose({
  init
});
</script>
<style lang="scss" >
.contractTemplateDialog {
  padding: 24px;
  .el-dialog__headerbtn {
    transform: translate(-25%, 25%);
  }
  .el-form-item {
    display: block;
  }
}
</style>

