@import "../theme/base.less";

*,
:after,
:before {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
    "微软雅黑", <PERSON><PERSON>, sans-serif;
  color: #303133;
  background: #f6f9ff;
  background-image: url("../images/body_img.png");
  background-repeat: no-repeat;
  background-size: 100%;

  // 禁止复制文字
  // -webkit-user-select: none; /* Chrome, Safari, Opera */
  // -moz-user-select: none; /* Firefox */
  // -ms-user-select: none; /* Internet Explorer/Edge */
  // user-select: none;

  //字体
  .text {
    &-2 {
      color: #8c8c8c;
    }
    &-3 {
      color: #606266;
    }
  }

  .text-center {
    text-align: center;
  }

  a {
    color: @--color-primary;
    text-decoration: none;

    &:focus,
    &:hover {
      color: @--color-primary;
    }
  }
}

.iconfont {
  cursor: pointer;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: text-bottom;
  display: inline-block;
  fill: currentColor;
  width: 17px;
  height: 17px;
}

.icon-svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
}

.el-badge__content {
  height: 16px;
  line-height: 16px;
  padding: 0 5px;
  border: none;
  background: #ff4d4f !important;
}

.ele-badge-static {
  line-height: 0;
}

.ele-badge-static .el-badge__content {
  position: static;
  transform: none;
}

//alert
.ele-alert-border.is-light.el-alert--warning {
  border: 1px solid #faad144d !important;
}

.el-alert--warning.is-light {
  background-color: #fff7e8 !important;
  color: #faad14 !important;
}

.ele-alert-border.is-light .el-alert__title {
  color: #262626 !important;
  font-size: 14px !important;
}

.el-alert__content {
  padding: 0;
}

//menu
.el-menu-item a,
.el-menu-item span,
.el-sub-menu > .el-sub-menu__title a,
.el-sub-menu > .el-sub-menu__title span {
  color: @dark-text;
  text-decoration: none;
  // margin-left: 5px;
  display: inline-flex;
  width: 100%;
}

.rr-sidebar-menu.el-menu--horizontal > .el-menu-item {
  padding: 0 12px;
  height: 62px;
  line-height: 62px;
}

.rr-sidebar-menu-pop-dark,
.rr-sidebar-menu-pop-light {
  box-shadow: none !important;
  border-width: 0 !important;
}

.el-sub-menu__icon-arrow {
  font-weight: bold;
}

//pop
.el-popper.is-dark a {
  color: #fff;
  text-decoration: none;
}

.el-popover.el-popper {
  max-height: 300px;
  overflow: auto;
}

//表格
.el-table thead {
  color: #303133 !important;

  th {
    background-color: #f5f7fa !important;
  }
}

.el-table__fixed-right::before {
  background: transparent !important; //element-plus表格高度动态计算bug，强制下划线不显示颜色
}

.el-form--inline .el-form-item {
  margin-right: 16px !important;
}

//分页
.el-pagination {
  margin-top: 15px !important;
  justify-content: right;
}

//tinymce
.tox-tinymce-aux {
  z-index: 3000 !important;
}

//弹窗popover
.popover-pop {
  padding: 10px 0 5px 5px !important;

  &-body {
    max-height: 255px;
    overflow: auto;
  }
}

//弹窗
.rr-dialog {
  min-width: 600px;
}

.rr {
  display: flex;
  flex-direction: column;
  // width: 100vw;
  // height: 100vh;
  overflow: hidden;

  &-loading {
    z-index: 9999;
  }

  //全屏页面渲染
  &-fullscreen {
    width: 100%;
    height: 100%;

    &.new-pop-window > div {
      padding: 15px;
      margin: 15px;
      background: #fff;
      border-radius: 4px;
    }
  }

  &-error {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 1200;
  }

  &-drawer {
    .el-drawer__header {
      color: #595959;
      font-size: 15px;
      margin-bottom: 0;
      padding: 13px 16px;
      border-bottom: 1px solid #f4f4f4;
    }

    .el-drawer__body {
      padding: 15px;
      overflow: auto;
    }
  }

  //顶部
  &-header {
    background: #fff;
    padding: 0 !important;
    // position: fixed;
    // top: 0;
    // left: 0;
    // width: 100%;
    z-index: 200;

    &-ctx {
      display: flex;
      height: 70px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

      &-logo {
        display: flex;
        color: #ffffff;
        background-color: #191a23;
        font-size: 24px;
        font-weight: 500;
        letter-spacing: 1.5px;
        // width: 230px;
        height: 80px;
        overflow: hidden;
        white-space: nowrap;
        justify-content: center;
        font-family: Avenir, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue,
          Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,
          Noto Color Emoji;
        align-items: center;
        position: relative;
        transition: width 0.3s;
        padding: 0 15px;

        &-img {
          width: 56px;
          height: 56px;
          display: inline-block;
          flex-shrink: 0;

          &-wrap {
            display: flex;
            align-items: center;
            margin-left: 16px;

            &.enabled-logo {
              &-false {
                display: none;
              }
            }
          }
        }

        &-name {
          display: flex;
          align-items: center;
          margin-left: 10px;
          font-weight: bold;
          font-size: 24px;
          color: #303133;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        &-line {
          display: inline-block;
          width: 10px;
          height: 1px;
        }

        &-text {
          display: inline-block;
          line-height: 1;
          overflow: hidden;
          text-transform: uppercase;
          font-weight: 700;
          font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
            "微软雅黑", Arial, sans-serif;
        }
      }
    }
  }

  &-body {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  //左侧侧边栏
  &-sidebar {
    // margin-top: 50px;
    // width: 230px !important;
    // min-height: calc(100vh - 50px);
    overflow-x: hidden !important;
    transition: width 0.3s;
    z-index: 120;
    scrollbar-width: none;

    &-menu {
      transition: width 0.3s;
      overflow: hidden;

      &.el-menu--horizontal {
        border-bottom: none !important;
      }

      .el-menu-item {
        transition: none !important;
      }
    }

    &::-webkit-scrollbar {
      display: none;
    }

    .el-menu {
      // width: 255px !important;
      border-right: 0 !important;

      &-item {
        height: 45px;
        line-height: 45px;
        margin: 2px 0;
      }

      &-item,
      .el-sub-menu__title {
        background: transparent !important;

        &:focus {
          background: transparent !important;
        }
      }

      &-item,
      .el-sub-menu__title,
      &-item-group__title {
        font-size: 14px;
      }

      .el-sub-menu {
        margin-top: 10px;
        width: 100%;
        .el-sub-menu__title {
          i {
            color: inherit !important;
          }
        }
      }

      .el-menu-item,
      .el-sub-menu .el-sub-menu__title {
        margin: 0;
        height: 36px;
        line-height: 36px;
        padding: 0px 16px !important;
      }

      .el-sub-menu {
        .el-menu-item {
          padding-left: 0px !important;
          height: 28px;
          line-height: 28px;
          padding: 0px;
          width: 50%;
          font-size: 12px;
          // border: 1px solid blue;
        }
      }

      .el-menu-item [class^="el-icon"],
      .el-sub-menu > .el-sub-menu__title [class^="el-icon"] {
        font-size: 17px;
        margin-right: 0;
        width: auto;
      }

      .el-menu-item a,
      .el-menu-item span,
      .el-sub-menu > .el-sub-menu__title a,
      .el-sub-menu > .el-sub-menu__title span {
        // margin-left: 10px;
        > a {
          margin-left: 0;
        }
      }
    }
  }

  //页面内容区域外层
  &-view {
    flex: 1;
    display: flex !important;
    flex-direction: column;
    padding: 0 !important;
    border-top: 1px solid #f4f4f4 !important;

    &-container {
      margin-top: 50px;
    }

    &-wrap {
      display: flex;
      flex-direction: column;
    }

    &-ctx {
      // margin-top: 39px;
      padding: 16px 16px 16px 16px !important;
      flex: 1;
      height: 100%;
      //页面内容区域
      &-card {
        min-height: calc(100% - 5px);
        border-width: 0 !important;
         .el-card__body{
            // padding: 0px 20px !important;
          }
      }
    }

    //页面内容顶部tab标签栏
    &-tab {
      background: #fff;
      width: 100%;
      height: 39px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;

      &__header {
        &:hover {
          background: inherit !important;
        }
      }

      &-wrap {
        position: fixed;
        top: 50px;
        left: 255px;
        right: 0;
        display: flex;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
        z-index: 100;
        transition: left 0.3s;
      }

      &-ops {
        width: 40px;
        flex-shrink: 0;
        background: #fff;
        display: flex !important;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #f4f4f4;
        cursor: pointer;
        text-align: center;
        color: #8c8c8c !important;
        font-weight: 400 !important;
        font-size: 16px !important;
        margin-right: 5px; //element-plus el-dropdown自动定位bug bottom-end指令不生效，临时采用偏移5px
      }

      .el-tabs__active-bar {
        height: 0;
      }

      .el-tabs__nav {
        &-prev,
        &-next {
          .el-icon {
            display: none;
          }
        }

        .el-tabs__item {
          padding: 0 15px !important;
          border-right: 1px solid #f4f4f4;
          user-select: none;
          color: #8c8c8c;

          &:hover {
            color: #262626;
            background-color: rgba(0, 0, 0, 0.02);
          }

          .is-icon-close {
            transition: none !important;

            &:hover {
              color: #fff;
              background-color: #ff4d4f;
            }
          }

          &::before {
            content: "";
            width: 9px;
            height: 9px;
            margin-right: 8px;
            display: inline-block;
            background-color: #ddd;
            border-radius: 50%;
          }

          &.is-active {
            color: @primary-bg-light;
            background-color: @primary-bg-light !important;

            &:before {
              background-color: @primary-bg-light;
            }
          }

          &:nth-child(2) {
            &::before {
              content: none;
            }
          }
        }
      }

      .el-tabs__nav-wrap {
        padding: 0px 39px 0 40px !important;

        &::before,
        &::after {
          width: 40px;
          height: 40px;
          line-height: 44px;
          text-align: center;
          box-sizing: border-box;
          font-size: 16px;
          color: #8c8c8c;
          transition: background-color 0.2s;
          position: absolute;
          top: 0;
          left: 0;
          font-family: element-icons !important;
          font-style: normal;
          font-weight: 400;
          font-variant: normal;
          text-transform: none;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          cursor: not-allowed;
        }

        &::before {
          content: url('data:image/svg+xml;charset=utf-8,<svg width="16" height="16" color="rgb(140 140 140)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M609.408 149.376L277.76 489.6a32 32 0 000 44.672l331.648 340.352a29.12 29.12 0 0041.728 0 30.592 30.592 0 000-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 000-42.688 29.12 29.12 0 00-41.728 0z"></path></svg>');
          border-right: 1px solid #f4f4f4;
        }

        &::after {
          content: url('data:image/svg+xml;charset=utf-8,<svg width="16" height="16" color="rgb(140 140 140)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 000 42.752L652.736 512 340.864 831.872a30.592 30.592 0 000 42.752 29.12 29.12 0 0041.728 0L714.24 534.336a32 32 0 000-44.672L382.592 149.376a29.12 29.12 0 00-41.728 0z"></path></svg>');
          right: 0;
          left: auto;
          bottom: auto;
          height: auto;
          background-color: transparent;
          border-left: 1px solid #f4f4f4;
        }
      }

      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        box-sizing: border-box;
        font-size: 16px;
        color: #8c8c8c;
        transition: background-color 0.2s;
        z-index: 10;

        i {
          vertical-align: middle;
          margin-top: -4px;
        }

        &:hover {
          background: rgba(0, 0, 0, 0.02);
        }
      }

      .el-tabs__nav-prev {
        border-right: 1px solid #f4f4f4;
      }
    }
  }
}

.ql-toolbar.ql-snow {
  width: 100% !important;
}

.el-form--inline {
  .el-form-item {
    & > .el-input,
    .el-cascader,
    .el-select,
    .el-date-editor,
    .el-autocomplete {
      min-width: 200px;
    }
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  // -webkit-text-fill-color: #333 !important;
  /* 文字颜色 */
  // -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  /* 背景色 */
  // border: 1px solid #ccc !important;
  /* 边框颜色 */
  // background-color: white !important;
  /* 背景色 */
  transition: background-color 50000s ease-in-out 0s !important;
  /* 防止背景色改变 */
}

// 自定义表单card样式
.ny_form_card {
  margin-bottom: 15px;

  .el-card__body {
    padding: 20px 20px 2px 20px;
  }
}

// 表格按钮列表
.ny-table-button-list {
  padding-bottom: 18px;
}

// flex
.flx {
  display: flex;
}
.flx-1 {
  flex: 1;
}
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-between {
  display: flex;
  justify-content: space-between;
}
.flx-justify-center {
  display: flex;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-justify-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flx-justify-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flx-align-center {
  display: flex;
  align-items: center;
}
.flx-align-end {
  display: flex;
  align-items: flex-end;
}
.flx-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.flx-nowrap {
  flex-wrap: nowrap;
}

// clearfix
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}

// 外边距、内边距全局样式
each(range(100), { 
  .mt-@{value} {
    margin-top: @value*1px !important;
  }
  .mr-@{value} {
    margin-right: @value*1px !important;
  }
  .mb-@{value} {
    margin-bottom: @value*1px !important;
  }
  .ml-@{value} {
    margin-left: @value*1px !important;
  }
  .pt-@{value} {
    padding-top: @value*1px !important;
  }
  .pr-@{value} {
    padding-right: @value*1px !important;
  }
  .pb-@{value} {
    padding-bottom: @value*1px !important;
  }
  .pl-@{value} {
    padding-left: @value*1px !important;
  }
})
  .mt-0 {
  margin-top: 0;
}
.w-100 {
  width: 100% !important;
}
.h-100 {
  height: 100% !important;
}

.input-240 {
  width: 240px !important;
}

// 文字单行省略号
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 文字多行省略号
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow-wrap: anywhere;
}

.buttonlink {
  padding: 7px 12px !important;
  background-color: #f4f6f9 !important;
}

.el-input-text-left {
  text-align: left;

  .el-input__inner {
    text-align: left !important;
  }
}

.text-primary {
  color: var(--el-color-primary) !important;
}

.el-table thead th {
  padding: 0 !important;
  height: 1px;
  .cell {
    padding: 8px 12px;
    height: 100% !important;
    width: 100%;
    // background: var(--color-primary-light) !important;
    font-weight: bold;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #1d2129;
    // justify-content: center;
    // border-right: 1px solid var(--el-table-borde);
    // color: #82909F !important;
  }

  &.is-center .cell {
    justify-content: center;
  }

  &.is-right .cell {
    justify-content: flex-end;
  }
}

.el-table-column--selection {
  text-align: center;
  .cell {
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.pointer {
  cursor: pointer;
}
.bold {
  font-weight: bold;
}

.text-size-small {
  font-size: 12px;
}
.text-size-13 {
  font-size: 13px;
}
.text-size-base {
  font-size: 14px;
}
.text-size-large {
  font-size: 16px;
}

.p-title {
  color: #303133;
  display: flex;
  line-height: 20px;
  margin: 12px 0;
  font-size: 14px;
  font-weight: bold;
  align-items: center;

  &.mt-0 {
    margin-top: 0;
  }

  &::before {
    content: "";
    display: inline-block;
    width: 2px;
    height: 20px;
    background: var(--el-color-primary);
    margin-right: 8px;
  }
}

.el-drawer__header {
  padding: 20px 20px 8px 20px !important;
  margin: 0 !important;
  line-height: 24px;
  box-sizing: border-box;
  font-weight: bold;

  .el-drawer__title {
    font-size: 18px;
    color: #303133;
  }
}

.el-table {
  .el-button.is-text,
  .el-button.is-text.is-has-bg {
    height: 22px;
    background-color: transparent !important;
    padding: 0 4px;
  }
  .el-button + .el-button {
    margin-left: 8px;
  }
  .el-table__body {
    .ch-96 {
      height: 96px;
      line-height: 96px;
    }
    .ch-80 {
      height: 80px;
      line-height: 80px;
    }
    .ch-56 {
      height: 56px;
      line-height: 56px;
    }
    .ch-40 {
      height: 40px;
      line-height: 40px;
    }
  }
}

.descriptions-label-140 tr {
  display: flex;
}

.descriptions-label-140 .el-descriptions__label.el-descriptions__cell.is-bordered-label {
  width: 140px;
  color: #606266;
  font-weight: 500;
  white-space: normal;
  word-break: break-all;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.descriptions-label-140 .el-descriptions__cell.el-descriptions__content.is-bordered-content {
  flex: 1;
}

// 抽屉样式
.ny-drawer {
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    background: #f0f2f5;
    padding: 12px;
    .el-card.is-always-shadow {
      box-shadow: none;
      border: none;
    }
  }

  .drawer-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}

.el-dialog__title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
}

.drawer {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.el-dialog.im-custom-dialog {
  padding: 0;

  .el-dialog__header {
    padding-right: 0;
  }
}
.im-custom-dialog-header {
  height: 56px;
  line-height: 56px;
  box-shadow: inset 0px -1px 0px 0px #f0f0f0;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: bold;
  padding-right: 0;

  .el-dialog__headerbtn {
    height: 56px;
  }
}

.notification-wrap {
  display: flex;
  align-items: center;
  cursor: pointer;

  .notification-img {
    width: 42px;
    height: 42px;
    border-radius: 50%;
  }
  .notification-charat {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: #eee;
    color: #999;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .notification-title {
    font-size: 16px;
    font-weight: bold;
  }
  .notification-content {
    width: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 10px;
  }

  .notification-desc {
    color: #999;
  }

  .group {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    background: #fff;
    border: 1px solid #e4e7ed;
    overflow: hidden;
    padding-left: 0;
    margin: 0;

    li {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    li {
      outline: 1px solid #fff;
      display: flex;
      height: 12px;

      .avatar {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        margin: 1px;
      }
    }

    /* 3个图片 */
    li:first-child:nth-last-child(3),
    li:first-child:nth-last-child(3) ~ li {
      width: 50%;
      height: 18px;
      .avatar {
        width: 18px;
        height: 18px;
        border-radius: 2px;
        margin: 1px;
      }
    }

    li:first-child:nth-last-child(3) {
      margin: auto;
    }

    li:first-child:nth-last-child(3) ~ li {
      float: left;
    }

    /* 4个图片 */
    li:first-child:nth-last-child(4),
    li:first-child:nth-last-child(4) ~ li {
      width: 50%;
      height: 18px;
      float: left;
      .avatar {
        width: 18px;
        height: 18px;
        border-radius: 2px;
      }
    }

    /* 5个或8个图片 */
    li:first-child:nth-last-child(5),
    li:first-child:nth-last-child(5) ~ li,
    li:first-child:nth-last-child(8),
    li:first-child:nth-last-child(8) ~ li {
      width: 33%;
      float: left;
    }

    li:first-child:nth-last-child(5),
    li:first-child:nth-last-child(5) + li {
      margin-top: 16%;
    }

    li:first-child:nth-last-child(5),
    li:first-child:nth-last-child(8) {
      margin-left: 15%;
    }

    /* 6个图片 */
    li:first-child:nth-last-child(6),
    li:first-child:nth-last-child(6) ~ li {
      width: 33%;
      float: left;
    }

    li:first-child:nth-last-child(6),
    li:first-child:nth-last-child(6) + li,
    li:first-child:nth-last-child(6) + li + li {
      margin-top: 16%;
    }

    /* 7个图片 */
    li:first-child:nth-last-child(7),
    li:first-child:nth-last-child(9),
    li:first-child:nth-last-child(7) ~ li,
    li:first-child:nth-last-child(9) ~ li {
      width: 33%;
      float: left;
    }

    li:first-child:nth-last-child(7) {
      float: none;
      margin: auto;
    }
  }
}

.el-dropdown-link:focus,
.el-tooltip__trigger:focus {
  outline: none;
}

.descriptionsDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    padding: 0px;
  }

  .partBg {
    background-color: #f0f2f5;
    padding: 12px;
  }

  .detailcard {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.cardDescriptions {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;

  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .el-descriptions__body {
    display: flex;
    justify-content: space-between;
    border: 1px solid #ebeef5;
    border-bottom: 0;
    border-right: 0;

    tbody {
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;

        .el-descriptions__label {
          display: flex;
          align-items: center;
          font-weight: normal;
          width: 144px;
          border: 0 !important;
          border-right: 1px solid #ebeef5 !important;
          border-bottom: 1px solid #ebeef5 !important;
        }

        .el-descriptions__content {
          display: flex;
          align-items: center;
          min-height: 48px;
          flex: 1;
          padding: 12px !important;
          border: 0 !important;
          border-right: 1px solid #ebeef5 !important;
          border-bottom: 1px solid #ebeef5 !important;

          > div {
            width: 100%;
          }

          .el-form-item__label {
            display: none;
          }

          .el-form-item {
            margin-bottom: 0;
          }
        }

        .noneSelfRight {
          border-right: 0 !important;
        }

        .noneSelfLeft {
          border-left: 0 !important;
        }

        .noneSelfLabel {
          background: none;
          border-left: 0 !important;
          border-right: 0 !important;
        }
      }
    }
  }
}
// 滚动条+分页器 （父级上的类）
.TableXScrollSty {
  .el-card {
    overflow: visible !important;
  }
  .el-table {
    overflow: visible !important;
    .el-table__header-wrapper {
      position: sticky;
      top: 0;
      z-index: 99;
    }
    .el-scrollbar {
      .el-scrollbar__bar {
        &.is-horizontal {
          display: block !important;
          height: 16px;
          position: fixed;
          bottom: 80px;
          left: 292px;
          z-index: 99;
          opacity: 1;

          .el-scrollbar__thumb {
            background: #c9cdd4 !important;
            opacity: 1;
          }
        }

        &.is-vertical {
          display: none !important;
        }
      }
    }
  }
  .tableSort {
    color: #1d2129;
    font-weight: bold;
    .el-dropdown {
      cursor: pointer;
      .clickValue {
        font-weight: bold;
        color: #1d2129;
      }
      .el-icon {
        color: #1d2129;
      }
    }
  }

  .el-popper {
    > span:first-child {
      display: block;
      max-width: 600px;
      max-height: 300px;
      margin-bottom: 5px;
      overflow-y: scroll;
      padding-right: 2px;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: hsla(0, 0%, 54.9%);
      }
    }
  }

  .table-body {
    padding-bottom: 60px;
  }

  .NYpagination {
    position: fixed;
    bottom: 16px;
    right: 36px;
    background-color: #fff;
    z-index: 99;
    height: 64px;
    padding: 16px;
    .el-pagination {
      margin-top: 0 !important;
    }
  }
}
// 多搜索框自适应 （ny-table上的类）
.nyTableSearchFormFitable {
  .table-header {
    .header-button-lf {
      width: fit-content;
      max-width: 30%;

      .ny-button-group {
        margin-right: 0;
      }
      // flex: none;
    }
    .header-button-ri {
      flex: 1;
      margin-left: 48px;
      // #header-right上的类
      .nyTableSearchForm {
        display: flex;
        align-items: center;
        width: 100%;
        .el-form-item {
          flex: 1;
          .el-form-item__content {
            width: 100%;
            .el-select,
            .el-input {
              max-width: 280px;
              min-width: 100px;
              width: 100% !important;
            }
            .el-date-editor {
              min-width: 180px;
              width: 100%;
              max-width: 320px;
            }
          }
        }
      }
    }
  }
}
 
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
inpit {
  border: none
}