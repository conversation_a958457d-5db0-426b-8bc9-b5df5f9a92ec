<template>
  <div class="container sell-order-wrap TableXScrollSty">
    <ny-table
      :showColSetting="false"
      noDataType="1"
      cellHeight="ch-96"
      class="nyTableSearchFormFitable"
      :state="state"
      :columns="columns"
      @pageSizeChange="state.pageSizeChangeHandle"
      @pageCurrentChange="state.pageCurrentChangeHandle"
      @selectionChange="state.dataListSelectionChangeHandle"
      @sortableChange="sortableChange"
    >
      <template #header>
        <el-button v-if="state.hasPermission('game:attribute:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button type="info" @click="exportHandle">导出</el-button>
      </template>

      
      <template #payType="{ row }">
        <span>{{ ["支付宝", "微信", "银行卡"][row.payType - 1] }}</span>
      </template>
      <!-- 关联商品 -->
      <template #shopCode="{ row }">
        <el-text type="primary" text class="pointer" @click="jumpGoodsDetails(row)">{{ row.shopCode }}</el-text>
      </template>

      <!-- 状态 -->
      <template #auditStatus="{ row }">
        <el-tag v-if="row.auditStatus == '0'" type="warning">待审核</el-tag>
        <el-tag v-if="row.auditStatus == '1'" type="success">已通过</el-tag>
        <el-tag v-if="row.auditStatus == '2'" type="danger">已拒绝</el-tag>
      </template>

      <!-- 收款信息 -->
      <template #checkView="{ row }">
        <el-text type="primary" text class="pointer" @click="checkDetail(row)">查看</el-text>
      </template>

      <!-- 开销凭证 -->
      <template #voucher="{ row }">
        <span v-if="!row.voucher || row.voucher.length < 1">-</span>
        <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px" v-else>
          <el-image style="width: 64px; height: 64px" :src="item" v-for="item in row.voucher.split(',')" alt="" preview-teleported :preview-src-list="row.voucher.split(',')" />
        </div>
      </template>

      <!-- 发票 -->
      <template #invoice="{ row }">
        <span v-if="!row.invoice || row.invoice.length < 1">-</span>
        <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px" v-else>
          <el-image style="width: 64px; height: 64px" :src="item" v-for="item in row.invoice.split(',')" alt="" preview-teleported :preview-src-list="row.invoice.split(',')" />
        </div>
      </template>

      <template #operation="{ row }">
        <el-button link type="primary" v-if="row.auditStatus == '0'" @click="addOrUpdateHandle(row)">编辑</el-button>
        <el-button link type="primary" @click="addOrUpdateHandle({ ...row, isCheck: true })">详情</el-button>
        <el-button link type="danger" v-if="row.auditStatus == '2'" @click="state.deleteHandle(row.id)">删除</el-button>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort"> 支出金额 </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
    <!-- 收款信息 -->
    <detail ref="detailRef" :key="detailKey" />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refresh="state.getDataList()"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { fileExport } from "@/utils/utils";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import ShopInfo from "@/views/shop/shop-info.vue";
import detail from "./detail.vue";
import addOrUpdate from "./otherOutTable-add-or-update.vue";
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "name",
    label: "费用名称",
    minWidth: 160
  },
  {
    prop: "amount",
    label: "支出金额(元)",
    minWidth: 160
  },
  {
    prop: "payType",
    label: "支付方式",
    minWidth: 160
  },
  {
    prop: "depName",
    label: "申请部门",
    minWidth: 160
  },
  {
    prop: "submitter",
    label: "提交人",
    minWidth: 160
  },
  {
    prop: "submitTime",
    label: "提交时间",
    minWidth: 160
  },
  {
    prop: "voucher",
    label: "开销凭证",
    minWidth: 160
  },
  {
    prop: "invoice",
    label: "发票",
    minWidth: 160
  },
  {
    prop: "shopCode",
    label: "关联商品",
    width: 160
  },
  {
    prop: "checkView",
    label: "收款信息",
    minWidth: 160
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "remark",
    label: "备注",
    width: 200
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 160
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/othercost/page",
  getDataListIsPage: true,
  exportURL: "/sale/export",
  deleteURL: "/automaticReconciliation/othercost",
  deleteIsBatch: true,
  dataForm: {
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (row?: any) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(view.dataForm.billType,{ ...row });
  });
};
const props = defineProps({
  pParams: {}
});
watch(
  () => props.pParams,
  () => {
    state.dataForm = Object.assign(state.dataForm, props.pParams);
  },
  {
    immediate: true,
    deep: true
  }
);
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleUpDateData = () => {
  state.getDataList();
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/othercost/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `其他支出`);
  });
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};
// 收款信息
const detailRef = ref();
const detailKey = ref(0);
const checkDetail = (row: any) => {
  detailKey.value++;  
  nextTick(() => {
    detailRef.value.init({ ...row });
  });
};
// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.amount) if (item.amount) total = total + (item.amount || 0);
  });

  return total.toFixed(2);
};
defineExpose({
  handleUpDateData
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
