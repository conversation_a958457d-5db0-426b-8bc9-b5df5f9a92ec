<template>
    <div class="business_page">

        <div class="business_header flx-align-center">
            <el-radio-group v-model="dataForm.type" @change="radioChange">
                <el-radio value="1">游戏</el-radio>
                <!-- <el-radio value="2">渠道</el-radio> -->
                <el-radio value="3">员工</el-radio>
            </el-radio-group>
        </div>
        <div class="page_title">结算流水分析</div>
        <div class="card_list">
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>收入总金额(元)</span>
                        <el-tooltip effect="dark" content="销售收入与其他收入之和" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ flowAnalysis.incomeTotal }}</div>
                <div class="item_footer">
                    <div class="box">销售：{{ flowAnalysis.salesAmount }}</div>
                    <!-- <div class="box">其他：{{ flowAnalysis.other }}</div> -->
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>支出总金额(元)</span>
                        <el-tooltip effect="dark" content="回收订单支出金额与员工薪资之和" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ flowAnalysis.expenditureTotal }}</div>
                <div class="item_footer">
                    <div class="box">回收订单：{{ flowAnalysis.recyclingOrderAmount }}</div>
                    <!-- <div class="box">员工薪资：{{ flowAnalysis.employeeSalary }}</div> -->
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>售后退款(元)</span>
                        <el-tooltip effect="dark" content="销售售后和回收售后已退款的总金额" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ flowAnalysis.afterSaleRefund }}</div>
                <div class="item_footer">
                    <div class="box">销售售后：{{ flowAnalysis.salesAfterSaleAmount }}</div>
                    <div class="box">回收售后：{{ flowAnalysis.recyclingAfterSaleAmount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(232,230,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>流水合计(元)</span>
                        <el-tooltip effect="dark" content="指定时间段下，其他项合计所得流水" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ flowAnalysis.flowTotalAmount }}</div>
                <div class="item_footer">
                    <div class="box" style="height: 20px;"></div>
                </div>
            </div>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">结算流水趋势图</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="legend">
                        <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData"
                            :key="index" @change="changeShow"></el-checkbox>
                    </div>
                    <el-date-picker v-model="settlementTime" type="daterange" start-placeholder="开始时间"
                        end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        style="width: 220px; border-radius: 20px" @change="getSettlementFlowTrend"/>
                </div>
            </div>
            <div class="header_describe">销售、回收、售后订单数据</div>
            <div class="charts">
                <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
            </div>
        </div>
        <div style="display: flex;align-items: center; gap: 12px;">
            <div style="flex: 1;">
                <div class="page_title">商品价值分析</div>
                <div class="card_list">
                    <div class="card_list_item"
                        style="background: linear-gradient( 174deg, rgba(232,230,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                        <div class="item_header">
                            <div class="flx-align-center">
                                <span>回收总成本(元)</span>
                                <el-tooltip effect="dark" content="当前时间下，最终回收总成本金额（可配置）" placement="top-end">
                                    <img src="/src/assets/icons/svg/question-line.svg"
                                        style="width: 14px;height: 14px;margin-left: 4px;">
                                </el-tooltip>
                            </div>
                            <div class="month">
                                <el-date-picker v-model="recyclingTime" type="month" format="YYYY-MM" value-format="YYYY-MM"
                        size="small" style="width: 70px;" :clearable="false" @change="getTotalCostOfRecycling" />
                            </div>
                        </div>
                        <div class="item_center">{{ RecyclingInfo.recyclingTotalCost }}</div>
                        <div class="item_footer">
                            <div class="box">同比增长：{{ RecyclingInfo.growthAmount }}</div>
                            <div class="box">增长率：{{ RecyclingInfo.growthRate == null ? '-' : RecyclingInfo.growthRate }}%</div>
                        </div>
                    </div>
                    <div class="card_list_item"
                        style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                        <div class="item_header">
                            <div class="flx-align-center">
                                <span>商品总估值(元)</span>
                                <el-tooltip effect="dark" content="当前时间下，最终商品总估值（零售价）金额（可配置）" placement="top-end">
                                    <img src="/src/assets/icons/svg/question-line.svg"
                                        style="width: 14px;height: 14px;margin-left: 4px;">
                                </el-tooltip>
                            </div>
                            <div class="month flx-align-center">
                                <el-date-picker v-model="productTime" type="month" format="YYYY-MM" value-format="YYYY-MM"
                        size="small" style="width: 70px;" :clearable="false" @change="getTotalValuationOfShop" />
                            </div>
                        </div>
                        <div class="item_center">{{ CommodityValuationInfo.totalValuation }}</div>
                        <div class="item_footer">
                            <div class="box">同比增长：{{ CommodityValuationInfo.growthAmount }}</div>
                            <div class="box">增长率：{{ CommodityValuationInfo.growthRate == null ? '-' : CommodityValuationInfo.growthRate}}%</div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="flex: 1;">
                <div class="page_title">综合分析</div>
                <div class="card_list">
                    <div class="card_list_item"
                        style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                        <div class="item_header">
                            <div class="flx-align-center">
                                <span>资产综合增长(元)</span>
                                <el-tooltip effect="dark" content="指定时间段下，回收增长价值+流水增长金额" placement="top-end">
                                    <img src="/src/assets/icons/svg/question-line.svg"
                                        style="width: 14px;height: 14px;margin-left: 4px;">
                                </el-tooltip>
                            </div>
                            <div class="month flx-align-center">
                                <el-date-picker v-model="assetTime" type="month" format="YYYY-MM" value-format="YYYY-MM"
                        size="small" style="width: 70px;" :clearable="false" @change="getAssetComprehensiveGrowth" />
                            </div>
                        </div>
                        <div class="item_center">{{ AssetInfo.comprehensiveGrowthAmount }}</div>
                        <div class="item_footer">
                            <div class="box">同比增长：{{ AssetInfo.growthAmount }}</div>
                            <div class="box">增长率：{{ AssetInfo.comprehensiveGrowthRate == null ? '-' : AssetInfo.comprehensiveGrowthRate }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref, watch, onMounted } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";
import { formatDate } from "@/utils/method";

const settlementTime = ref(); // 结算流水趋势图时间
const recyclingTime = ref(); // 回收总成本月份
const productTime = ref(); // 商品总估值月份
const assetTime = ref(); // 资产综合增长月份

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    employeeId: "",
    startTime: "",
    endTime: "",
    type: "1"
});

// 图例数据
const legendData = ref([
    { name: '销售金额', color: '#4165D7', show: true },
    { name: '回收金额', color: '#00C568', show: true },
    { name: '售后金额', color: '#722ED1', show: true },
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '销售金额',
        type: 'bar',
        itemStyle: {
            color: "#4165D7"
        },
        barMaxWidth: 30,
        data: [],
    },
    {
        name: '回收金额',
        type: 'bar',
        itemStyle: {
            color: "#00B42A"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '售后金额',
        type: 'line',
        itemStyle: {
            color: "#722ED1"
        },
        smooth: true,
        lineStyle: {
            width: 3,
            color: "#722ED1"
        },
        data: []
    }
])

// 游戏库存折线图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any, color?: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '6%',
            top: '10%',
            bottom: '16%',
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '金额(元)',
                // min: 0,
                // max: 'dataMax',
                // interval: 5000,
                // axisLabel: {
                //     formatter: '{value} 元'
                // }
            },
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 40,
            bottom: "4%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 9
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 18
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    settlementTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    recyclingTime.value = formatDate(new Date(), 'YYYY-MM');
    productTime.value = formatDate(new Date(), 'YYYY-MM');
    assetTime.value = formatDate(new Date(), 'YYYY-MM');
    getFinancialIndexTracking();
    getSettlementFlowTrend();
    getTotalCostOfRecycling();
    getTotalValuationOfShop();
    getAssetComprehensiveGrowth();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})

// 单选切换
const radioChange = () => {
    getFinancialIndexTracking();
    getSettlementFlowTrend();
    getTotalCostOfRecycling();
    getTotalValuationOfShop();
    getAssetComprehensiveGrowth();
}


// 结算流水分析
const flowAnalysis = reactive({
    incomeTotal: 0,
    salesAmount: 0,
    other: 0,
    expenditureTotal: 0,
    recyclingOrderAmount: 0,
    employeeSalary: 0,
    afterSaleRefund: 0,
    salesAfterSaleAmount: 0,
    recyclingAfterSaleAmount: 0,
    flowTotalAmount: 0,
    otherCost: 0
})
const getFinancialIndexTracking = () =>{
    baseService.post("/dataAnalysis/financialIndexTracking",dataForm.value).then(res=>{
        Object.assign(flowAnalysis,res.data)
    })
}

// 结算流水趋势图
const getSettlementFlowTrend = () =>{
    dataForm.value.startTime = settlementTime.value ? settlementTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = settlementTime.value ? settlementTime.value[1] + " 23:59:59" : "";
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/settlementFlowTrend",dataForm.value).then(res=>{
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

// 回收总成本
const RecyclingInfo = ref({
    recyclingTotalCost: 0,
    growthAmount: 0,
    growthRate: 0
});
const getTotalCostOfRecycling = () =>{
    baseService.post("/dataAnalysis/totalCostOfRecycling",{ 
        ...dataForm.value,
        monthDate: recyclingTime.value
    }).then(res=>{
        Object.assign(RecyclingInfo.value,res.data)
    })
}

// 商品总估值
const CommodityValuationInfo = ref({
    totalValuation: 0,
    growthAmount: 0,
    growthRate: 0
})
const getTotalValuationOfShop = () =>{
    baseService.post("/dataAnalysis/totalValuationOfShop",{ 
        ...dataForm.value,
        monthDate: productTime.value
    }).then(res=>{
        Object.assign(CommodityValuationInfo.value,res.data)
    })
}

// 资产综合增长
const AssetInfo = ref({
    comprehensiveGrowthAmount: 0,
    growthAmount: 0,
    comprehensiveGrowthRate: 0
})
const getAssetComprehensiveGrowth = () =>{
    baseService.post("/dataAnalysis/assetComprehensiveGrowth",{
        ...dataForm.value,
        monthDate: assetTime.value
    }).then(res=>{
        Object.assign(AssetInfo.value,res.data)
    })
}

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getFinancialIndexTracking();
    getSettlementFlowTrend();
    getTotalCostOfRecycling();
    getTotalValuationOfShop();
    getAssetComprehensiveGrowth();
};

defineExpose({
    init
});
</script>

<style lang='less' scoped>
.business_page {
    margin-top: 12px;
}

.page_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
    margin-top: 12px;
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165D7;
                        border-color: #4165D7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #00B42A;
                        border-color: #00B42A;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ED1;
                        border-color: #722ED1;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.analysis_type_item {
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    line-height: 22px;
    padding: 3px 12px;
    background: #FFFFFF;
    border-radius: 24px;
    border: 1px solid #D4D7DE;
}

.card_list {
    display: flex;
    align-items: center;
    gap: 12px;

    .card_list_item {
        flex: 1;
        padding: 16px 20px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;

        .item_header {
            font-weight: bold;
            font-size: 16px;
            color: #1C1C1C;
            line-height: 20px;

            display: flex;
            align-items: center;
            justify-content: space-between;

            .month {
                font-weight: 500;
                font-size: 14px;
                color: #1C1C1C;
                line-height: 20px;

                :deep(.el-text) {
                    cursor: pointer;
                }

                :deep(.ny_dropdown_menu) {
                    padding: 0;
                }

                :deep(.clickValue),
                :deep(.placeholder) {
                    line-height: normal;
                    cursor: pointer;
                }

                :deep(.el-date-editor) {
                    line-height: 20px;
                    height: 20px;

                    .el-input__prefix {
                        position: absolute;
                        right: 4px;

                        .el-input__prefix-inner {
                            color: #303133;
                        }
                        .el-input__prefix-inner>:last-child{
                            margin-right: 0px;
                        }
                    }

                    .el-input__wrapper {
                        box-shadow: none;
                        background-color: transparent;

                        .el-input__inner {
                            cursor: pointer;
                        }
                    }
                }
            }
        }

        .item_center {
            font-weight: bold;
            font-size: 24px;
            color: #1C1C1C;
            line-height: 36px;
            margin: 10px 0px;
        }

        .item_footer {
            display: flex;
            align-items: center;

            .box {
                flex: 1;
                font-weight: 400;
                font-size: 13px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }
    }
}
</style>