<template>
    <el-dialog width="480px" v-model="dialogVisible" class="business-card-dialog" @close="close" :modal="false">
        <template #header="{ close, titleId, titleClass }">
            <div class="business-card-header">
                <p class="dialog-title">名片</p>
            </div>
        </template>
        
        <div class="business-card-dialog-content flx-column">
            <im-avatar class="avatar" :user="userInfo"></im-avatar>
            <div class="nickname">{{ userInfo.nickname }}</div>
            <div class="phone">
                <span class="icon">
                    <el-icon><PhoneFilled /></el-icon>
                </span>
                <span class="phone-num">{{ userInfo.mobile || '-' }}</span>
            </div>
        </div>
        
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose } from 'vue';
    import ImAvatar from './ImAvatar.vue';
    
    const dialogVisible = ref(false);
    const userInfo = ref(<any>{});

    const init = (info: any) => {
        userInfo.value = info;
        dialogVisible.value = true;
    }

    const close = () => {
        
    }

    defineExpose({
        init
    })
</script>

<style lang="scss">
    .business-card-dialog{
        padding: 0;

        .el-dialog__header{
            padding-right: 0;
        }
    }
    .business-card-header{
        height: 56px;
        line-height: 56px;
        box-shadow: inset 0px -1px 0px 0px #F0F0F0;
        padding: 0 16px;
        color: rgba(0,0,0,0.85);
        font-size: 16px;
        font-weight: bold;
        padding-right: 0;
    }

    .business-card-dialog-content{
        padding: 45px 0 55px 0;
        align-items: center;

        .avatar{
            width: 72px;
            height: 72px;
            border-radius: 4px;
        }

        .nickname{
            font-size: 20px;
            line-height: 24px;
            font-weight: bold;
            margin-top: 8px;
        }

        .phone{
            margin-top: 8px;
            display: flex;
            align-items: center;
            
            .icon{
                background: rgba(65,101,215,0.1);
                width: 20px;
                height: 20px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--el-color-primary);
            }

            .phone-num{
                margin-left: 5px;
                font-size: 20px;
                color: #606266;
            }
        }
    }

</style>