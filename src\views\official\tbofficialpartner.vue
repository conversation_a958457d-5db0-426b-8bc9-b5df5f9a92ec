<template>
  <div class="mod-official__tbofficialpartner">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-form-slot>
        <template #content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.name" placeholder="合作伙伴名称" clearable></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template #button>
          <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        <el-button v-if="state.hasPermission('official:tbofficialpartner:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button v-if="state.hasPermission('official:tbofficialpartner:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="合作伙伴名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="logo" label="合作伙伴logo" header-align="center" align="center">
          <template v-slot="scope">
            <el-image style="height: 30px" :src="scope.row.logo" :preview-src-list="[scope.row.logo]" preview-teleported fit="cover" />
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者ID" header-align="center" align="center"> </el-table-column>
        <el-table-column prop="updater" label="更新者ID" header-align="center" align="center"> </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
          <template v-slot="scope">
            <el-button v-if="state.hasPermission('official:tbofficialpartner:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('official:tbofficialpartner:delete')" type="primary" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./tbofficialpartner-add-or-update.vue";

const view = reactive({
  getDataListURL: "/official/tbofficialpartner/page",
  getDataListIsPage: true,
  exportURL: "/official/tbofficialpartner/export",
  deleteURL: "/official/tbofficialpartner",
  deleteIsBatch: true,
  dataForm: {
    name: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.name = "";
  state.getDataList();
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
