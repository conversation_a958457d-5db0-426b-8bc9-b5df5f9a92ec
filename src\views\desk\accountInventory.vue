<template>
  <div class="accountInventory">
    <div v-show="state.xdata && state.xdata.length > 0" style="width: 100%; height: 100%" ref="accountInventoryChartRef"></div>
    <ny-no-data type="3" v-if="!state.xdata || state.xdata.length < 1" />
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted, watch, nextTick } from "vue";
import * as echarts from "echarts";
import commonData from "./index.ts";
import baseService from "@/service/baseService";
const accountInventoryChartRef = ref(null); // 游戏库存折线图
const props = defineProps({
  allData: <any>{}
});
const state = reactive({
  xdata: <any>[],
  yData: {
    acquisitionTotal: <any>[], //当前回收总价
    accountNums: <any>[], //新增账号数
    soldNums: <any>[], //出售账号数
    transactionTotal: <any>[] //当前出售总价
  }
});
// 统计表报数据

const charts = ref(<any>[]);

// 游戏库存折线图
const GameSalesStatisticsChart = () => {
  const userGrowthChart = echarts.init(accountInventoryChartRef.value);
  const option = {
    color: ["#722ED1", "#FF7D00", "#37E2E2", "#3469FF"],
    tooltip: {
      backgroundColor: "transparent", //背景色
      padding: 8, //边距
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985"
        }
      },
      textStyle: {
        lineHeight: 22,
        textBorderWidth: 8,
        textShadowColor: "#fff"
      }
    },
    grid: {
      left: "4%",
      right: "4%",
      bottom: "4%",
      top: "6%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: handleXTime(state.xdata)
      }
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: function (value: any) {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + "万";
            }
            return value;
          }
        }
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value} 个"
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: "当前回收总价",
        type: "line",
        smooth: true,
        lineStyle: {
          width: 3,
          color: "#722ED1"
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.2,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0, color: "#8364FF " }
          ])
        },
        emphasis: {
          focus: "series"
        },
        data: state.yData.acquisitionTotal || []
      },
      {
        name: "新增账号数",
        type: "line",
        smooth: true,
        yAxisIndex: 1,
        lineStyle: {
          width: 3,
          color: "#FF7D00"
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.2,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0, color: "#FFD364 " }
          ])
        },
        emphasis: {
          focus: "series"
        },
        data: state.yData.accountNums || []
      },
      {
        name: "出售账号数",
        type: "line",
        smooth: true,
        lineStyle: {
          width: 3,
          color: "#37E2E2"
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0, color: "#64FFEC " }
          ])
        },
        emphasis: {
          focus: "series"
        },
        data: state.yData.soldNums || []
      },
      {
        name: "当前出售总价",
        type: "line",
        smooth: true,
        lineStyle: {
          width: 3,
          color: "#3469FF"
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: "#FFFFFF" },
            { offset: 0, color: "#64A2FF " }
          ])
        },
        emphasis: {
          focus: "series"
        },
        data: state.yData.transactionTotal || []
      }
    ]
  };
  userGrowthChart.setOption(option);
  charts.value.push(userGrowthChart);
};
const handleXTime = (data: any) => {
  let afterArr = data.map((ele: any) => {
    let arr = ele.split("-");
    return arr?.length == 3 ? `${arr[1]}/${arr[2]}` : "";
  });
  return afterArr;
};
watch(
  () => props.allData,
  () => {
    if (props.allData.lineData) {
      state.xdata = props.allData.lineData.xData ? props.allData.lineData.xData : [];
      props.allData.lineData.yData.forEach((element: any, index: any) => {
        state.yData.acquisitionTotal.push(element.acquisitionTotal);
        state.yData.accountNums.push(element.accountNums);
        state.yData.soldNums.push(element.soldNums);
        state.yData.transactionTotal.push(element.transactionTotal);
      });
      nextTick(() => {
        GameSalesStatisticsChart();
      });
    }
  }
);
onMounted(async () => {});
</script>

<style lang='less' scoped>
.accountInventory {
  width: 100%;
  height: 288px;
  // border: 1px solid red;
}
</style>