<template>
  <div class="mod-finance__daily-settlement">
    <el-card shadow="never" class="rr-view-ctx-card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.xzzjzh" placeholder="资金账户" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.zhyt" placeholder="账户用途" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.zhhm" placeholder="账户户名" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.khyh" placeholder="开户银行" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" :offset="18">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <ny-table :state="state" :columns="columns" @selectionChange="state.dataListSelectionChangeHandle" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle">
        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <template #ldzjye="{ row }">
          <span class="amount-text">{{ formatCurrency(row.ldzjye) }}</span>
        </template>
        <template #zrsrze="{ row }">
          <span class="amount-text">{{ formatCurrency(row.zrsrze) }}</span>
        </template>
        <template #zrzcze="{ row }">
          <span class="amount-text">{{ formatCurrency(row.zrzcze) }}</span>
        </template>
        <template #zrqcye="{ row }">
          <span class="amount-text">{{ formatCurrency(row.zrqcye) }}</span>
        </template>
        <template #zrqmye="{ row }">
          <span class="amount-text">{{ formatCurrency(row.zrqmye) }}</span>
        </template>
        <template #zrqmdshje="{ row }">
          <span class="amount-text">{{ formatCurrency(row.zrqmdshje) }}</span>
        </template>
        <template #tpsc="{ row }">
          <el-button type="primary" size="small" @click="uploadImageHandle(row)">
            {{ row.tpsc && row.tpsc.length > 0 ? '查看' : '上传' }}
          </el-button>
        </template>
        <template #zt="{ row }">
          <el-tag :type="getAuditStatusTagType(row.zt)">
            {{ row.zt || '待审批' }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <el-button type="info" text bg @click="addOrUpdateHandle(row.id, true)">详情</el-button>
          <el-button type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 / 详情 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./daily-settlement-add-or-update.vue";
// import ExcelImport from "./daily-settlement-import.vue";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";

const { t } = useI18n();

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/finance/daily-settlement/page",
  getDataListIsPage: true,
  exportURL: "/finance/daily-settlement/export",
  deleteURL: "/finance/daily-settlement",
  deleteIsBatch: true,
  dataForm: {
    xzzjzh: "",
    zhyt: "",
    zhhm: "",
    khyh: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "xzzjzh",
    label: "资金账户",
    minWidth: 150
  },
  {
    prop: "zhyt",
    label: "账户用途",
    minWidth: 120
  },
  {
    prop: "zhhm",
    label: "账户户名",
    minWidth: 120
  },
  {
    prop: "khyh",
    label: "开户银行",
    minWidth: 120
  },
  {
    prop: "ldzjye",
    label: "联动资金余额",
    minWidth: 120
  },
  {
    prop: "zrsrze",
    label: "昨日收入总额",
    minWidth: 120
  },
  {
    prop: "zrzcze",
    label: "昨日支出总额",
    minWidth: 120
  },
  {
    prop: "zrqcye",
    label: "昨日期初余额",
    minWidth: 120
  },
  {
    prop: "zrqmye",
    label: "昨日期末余额",
    minWidth: 120
  },
  {
    prop: "zrqmdshje",
    label: "昨日期末待收货金额",
    minWidth: 160
  },
  {
    prop: "tpsc",
    label: "图片上传",
    minWidth: 100
  },
  {
    prop: "cjry",
    label: "创建人员",
    minWidth: 100
  },
  {
    prop: "cjsj",
    label: "创建时间",
    minWidth: 160
  },
  {
    prop: "xgry",
    label: "修改人员",
    minWidth: 100
  },
  {
    prop: "xgsj",
    label: "修改时间",
    minWidth: 160
  },
  {
    prop: "zt",
    label: "审批结果",
    minWidth: 100
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: 180
  }
]);

// 格式化金额
const formatCurrency = (amount: number | string) => {
  if (!amount && amount !== 0) return "0.00";
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(num)) return "0.00";
  return num.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 获取审批状态标签类型
const getAuditStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    "待审批": "warning",
    "同意": "success",
    "拒绝": "danger"
  };
  return statusMap[status] || "info";
};

// 导入
const importRef = ref();
const importHandle = () => {
  if (importRef.value && importRef.value.init) {
    importRef.value.init();
  }
};

// 导出
const exportHandle = () => {
  baseService.get("/finance/daily-settlement/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "日清日结列表");
    }
  }).catch((error) => {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  });
};

// 图片上传处理
const uploadImageHandle = (row: IObject) => {
  addOrUpdateHandle(row.id, true);
};

// 重置操作
const getResetting = () => {
  state.dataForm.xzzjzh = "";
  state.dataForm.zhyt = "";
  state.dataForm.zhhm = "";
  state.dataForm.khyh = "";
  state.getDataList();
};

// 新增/编辑/详情
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number, isView?: boolean) => {
  addKey.value++;
  nextTick(() => {
    if (addOrUpdateRef.value && addOrUpdateRef.value.init) {
      addOrUpdateRef.value.init(id, isView);
    }
  });
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.amount-text {
  color: #e6a23c;
  font-weight: 500;
}
</style>


