<template>
  <el-drawer class="infoAuditDrawer" v-model="visible" size="944">
    <template #header>
      <div class="drawer_title">商品售出审核</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <!-- 基本信息 -->
      <div class="basicInfoSty cardDescriptions">
        <div class="titleSty">商品信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label><div>游戏名称</div> </template>
            {{ dataForm.gameName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>游戏账号</div> </template>
            {{ dataForm.gameAccount }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>商品编码</div> </template>
            {{ dataForm.shopCode }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>零售价(元)</div> </template>
            {{ dataForm.price }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>出售人</div> </template>
            {{ dataForm.saleUserName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>出售渠道</div> </template>
            {{ dataForm.channelName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>买方手机号</div> </template>
            {{ dataForm.buyerPhone }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>成交价(元)</div> </template>
            {{ dataForm.transactionPrice }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>包赔费(元)</div> </template>
            {{ dataForm.guaranteeAmount }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>手续费(元)</div> </template>
            {{ dataForm.fee }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty cardDescriptions">
        <div class="titleSty">收款信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label><div>三方订单编号</div> </template>
            {{ dataForm.thirdPartyOrderCode }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>应收金额(元)</div> </template>
            {{ dataForm.transactionPrice }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>付款方式</div> </template>
            {{ dataForm.payType }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title" v-if="dataForm.payType == '支付宝'">
            <template #label><div>支付宝订单号</div> </template>
            {{ dataForm.alipayOrderNo }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label><div>付款凭证</div> </template>
            <el-image style="height: 4.25rem; width: 4.25rem" :src="dataForm.payCredentials" :preview-src-list="[dataForm.payCredentials]" preview-teleported fit="cover" v-if="dataForm.payCredentials" />
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-top: 12px; display: flex; justify-content: flex-end" v-if="dataForm.status">
          <el-tag :type="dataForm.status == 1 ? 'success' : 'danger'" size="large">
            <div style="display: flex; align-items: center; gap: 9px">
              <el-icon v-if="dataForm.status == 1"><CircleCheckFilled /></el-icon>
              <el-icon v-else><CircleCloseFilled /></el-icon>
              <span style="font-weight: 500; font-size: 14px">{{ dataForm.status == 1 ? "对账成功" : "对账异常" }}</span>
            </div>
          </el-tag>
        </div>
      </div>
      <!-- 审核相关 -->
      <div class="basicInfoSty" style="padding-bottom: 0">
        <div class="titleSty">审核操作</div>
        <div class="shop_page_basic">
          <!-- 商品信息 -->
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top" style="padding: 0px">
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="审核操作" prop="auditStatus">
                  <el-radio-group v-model="dataForm.auditStatus">
                    <el-radio value="1" :disabled="dataForm.status == 2">审核通过</el-radio>
                    <el-radio value="0" :disabled="dataForm.status == 1">审核拒绝</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="dataForm.auditStatus == '0'">
                <el-form-item label="拒绝原因" prop="remarks_">
                  <el-input maxlength="100" show-word-limit type="textarea" v-model="dataForm.remarks_" placeholder="请输入拒绝原因" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="basicInfoSty">
        <div class="titleSty">流程进度</div>
        <el-steps direction="vertical" :active="activeLength">
          <el-step :title="dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.applTitle">
            <template #description>
              <div class="cardInfo">
                <div>
                  申请人：<span style="color: var(--el-color-primary)">{{ dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.apper }}</span>
                </div>
                <div>
                  申请时间：
                  <span>{{ formatTimeStamp( dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.createDate) }}</span>
                </div>
              </div>
            </template>
          </el-step>
          <el-step :title="dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.auditTitle">
            <template #description>
              <div class="cardInfo">
                <div>
                  审批人：<span style="color: var(--el-color-primary)">{{ dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.auditer }}</span>
                </div>
                <div>
                  处理时间：
                  <span>{{ formatTimeStamp(dataForm.saleOrderAuditInfo && dataForm.saleOrderAuditInfo.auditDate) || "-" }}</span>
                </div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </el-scrollbar>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const dataFormRef = ref(); // 表单ref
const dataForm = ref(<any>{
  remarks: ""
});
const rules = ref({
  // 表单必填项
  auditStatus: [{ required: true, message: "请选择审核操作", trigger: "change" }],
  remarks_: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }]
});

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    auditHandle();
  });
};

// 审核提交
const auditHandle = () => {
  btnLoading.value = true;
  let params = {
    auditStatus: dataForm.value.auditStatus,
    reason: dataForm.value.remarks_,
    id: dataForm.value.saleOrderAuditInfo.id,
    shopId: dataForm.value.shopId,
    saleOrderId: dataForm.value.saleOrderAuditInfo.saleOrderId
  };
  baseService
    .put("SaleOrderAudit/saleorderaudit", params)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};   
const detailOrderCode = ref();
// 表单初始化
const init = (id: String | Number, orderCode?: string) => {
  visible.value = true;
  detailOrderCode.value = orderCode;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  getInfo(id);
};
const activeLength = ref(1);
// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getInfo = (id: String | Number) => {
  requestLoading.value = true;
  baseService
    .get("sale/getAuditPageInfo/" + id)
    .then((res) => {
      const data = res.data.shopInfo;
      Object.assign(dataForm.value, data);
      console.log(dataForm.value.saleOrderAuditInfo);
      
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}
.shop_page {
  padding: 12px;
}
.games {
  padding: 20px 6px 10px 6px;
  .gamesList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    .gamesItem {
      min-width: 120px;
      height: 28px;
      font-size: 12px;
      line-height: 28px;
      background-color: #fff;
      border: 1px solid var(--el-color-primary);
      border-radius: 4px;
      text-align: center;
      margin: 0px 10px 10px 10px;
      cursor: pointer;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .active {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }
  }
  .icons {
    padding: 6px 10px 10px 10px;
    .el-icon {
      font-size: 16px;
    }
  }
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
}
.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}
.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;
  .flod_tab_cont {
    flex: 1;
    overflow: hidden;
    :deep(.el-radio) {
      margin-right: 20px;
    }
    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}
.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}
.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  .shop_info_left {
    display: flex;
    align-items: center;
    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="less">
.infoAuditDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-textarea__inner {
    min-height: 80px !important;
  }

  .el-step__head {
    &.is-process,
    &.is-finish {
      .el-step__icon {
        background: var(--el-color-primary);
        color: #fff;
        border-color: #fff;
        .el-step__icon-inner {
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 14px;
        }
      }
    }
    .el-step__icon {
      background: #f0f2f5;
      color: #909399;
      border-color: #fff;
      .el-step__icon-inner {
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 14px;
      }
    }

    .el-step__line {
      width: 1px;
      .el-step__line-inner {
        border: 1px solid #c0c4cc;
        border-width: 0px !important;
      }
    }
  }
  .el-step__main .el-step__title {
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .cardInfo {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #ebeef5;
    padding: 16px 12px;
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 16px;
    margin-bottom: 10px;
    > div {
      margin-bottom: 2px;
    }
  }
}
</style>
