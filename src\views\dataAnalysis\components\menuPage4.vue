<template>
    <div>
        <div class="menu_title">财务指标追踪</div>
        <div class="top">
            <ny-button-group :list="[
                { dictLabel: '售后概况', dictValue: '0' },
                { dictLabel: '售后明细', dictValue: '1' },
            ]" v-model="typeValue" @change=""></ny-button-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId" :list="recyclingChanneList" labelKey="channelName" valueKey="channelId"
                    placeholder="回收渠道" clearable></NyDropdownMenu>
                <NyDropdownMenu v-model="dataForm.salesChannelId" :list="salesChannelList" labelKey="channelName" valueKey="channelId"
                    placeholder="销售渠道" clearable></NyDropdownMenu> -->
                <el-date-picker v-model="dateTime" type="daterange" start-placeholder="开始时间"
                    end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px;margin-left: 12px;" 
                    v-if="typeValue == '0' || typeValue == '1'"
                    @change="selectTime"
                />
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <!-- 售后概况 -->
        <AftersalesOverview ref="AftersalesOverviewRef" v-if="typeValue == '0'"></AftersalesOverview>
        <!-- 售后明细 -->
        <AfterDalesDetails ref="AfterDalesDetailsRef" v-if="typeValue == '1'"></AfterDalesDetails>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import baseService from "@/service/baseService";
import AftersalesOverview from "./menuPageList/AftersalesOverview.vue";
import AfterDalesDetails from "./menuPageList/AfterDalesDetails.vue";

const typeValue = ref("0");
const dateTime = ref();
const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    salesChannelId: "",
    startTime: "",
    endTime: "",
});

const AftersalesOverviewRef = ref();
const AfterDalesDetailsRef = ref();

const gameList = ref(); // 游戏列表
const recyclingChanneList = ref(); // 回收渠道列表
const salesChannelList = ref(); // 销售渠道列表

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) =>{
    baseService.get("/dataAnalysis/channelSearchList",{channelType}).then(res=>{
        // 销售渠道
        if(channelType == 0){
            salesChannelList.value = res.data;
        }
        // 回收渠道
        if(channelType == 1){
            recyclingChanneList.value = res.data;
        }
    })
}

const selectTime = () =>{
    dataForm.value.startTime = dateTime.value ? dateTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = dateTime.value ? dateTime.value[1] + " 23:59:59" : "";
}

// 查询
const queryChagne = () =>{
    if(AftersalesOverviewRef.value){
        AftersalesOverviewRef.value.init(dataForm.value)
    }
    if(AfterDalesDetailsRef.value){
        AfterDalesDetailsRef.value.init(dataForm.value)
    }
}

// 重置
const resettingChange = () =>{
    dataForm.value.gameId = "";
    dataForm.value.salesChannelId = "";
    dataForm.value.recyclingChannelId ="";
    dataForm.value.startTime = "";
    dataForm.value.endTime = "";
    dateTime.value = [];
    if(AftersalesOverviewRef.value){
        AftersalesOverviewRef.value.init(dataForm.value)
    }
    if(AfterDalesDetailsRef.value){
        AfterDalesDetailsRef.value.init(dataForm.value)
    }
}

onMounted(()=>{
    getGameList();
    getRecyclingChanneList(0);
    getRecyclingChanneList(1);
})

</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}


</style>
