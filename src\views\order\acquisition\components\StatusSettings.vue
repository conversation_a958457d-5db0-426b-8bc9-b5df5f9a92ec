<template>
  <el-dialog v-model="dialogVisible" title="回收换绑" width="480">
    <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="1">
      <el-descriptions-item>
        <template #label
          ><span>换绑状态<span style="color: red">*</span></span></template
        >
        <el-select v-model="params.waitForChangeBindingReason" placeholder="请选择换绑状态">
          <el-option v-for="item in waitForChangeBindingReasonList" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
    <div style="height: 62px"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { ref, reactive } from "vue";
const emit = defineEmits(['refresh']);
const dialogVisible = ref(false);

const params = reactive({
  id: "",
  waitForChangeBindingReason: ""
});

const waitForChangeBindingReasonList = reactive([
  { label: "（腾讯-3天）", value: "1" },
  { label: "（腾讯-买家问题）", value: "2" },
  { label: "（腾讯-换绑失败）", value: "3" },
  { label: "（腾讯-验证超限）", value: "4" },
  { label: "（腾讯-保护期）", value: "5" },
  { label: "（米哈游-买家问题）", value: "6" },
  { label: "（网易-保护期）", value: "7" },
  { label: "（网易-买家问题）", value: "8" },
  { label: "（其他）", value: "9" },
  { label: "（再次上架）", value: "10" }
]);

const submit = () => {
  if (!params.waitForChangeBindingReason) {
    ElMessage.warning("请选择换绑状态");
    return;
  }
  baseService.put("/purchase/updateReason", params).then((res) => {
    if (res.code == 0) {
      ElMessage.success("保存成功");
      dialogVisible.value = false;
      emit("refresh");
    }
  });
};

const init = (id: any,waitForChangeBindingReason: string) => {
  dialogVisible.value = true;
  params.id = id;
  params.waitForChangeBindingReason = waitForChangeBindingReason;
};

defineExpose({
  init
});
</script>

<style lang="less" scoped></style>
