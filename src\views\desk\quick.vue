<template>
  <div class="quick">
    <div class="above">
      <div class="left">
        <span class="title">快捷入口</span>
      </div>
    </div>
    <div class="inlet_list">
      <div @click="jumpRoute(item.path, item.inPage)" :class="props.isWrap ? 'inlet_list_item isWrap' : 'inlet_list_item'" v-for="item in quickData" :key="item.title">
        <div class="inlet_icon">
          <img :src="item.icon" />
        </div>
        <span class="inlet_name">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { useRouter } from "vue-router";
import { ref, reactive } from "vue";
const props = defineProps({
  isWrap: {
    type: Boolean,
    required: true
  }
});
const router = useRouter();
const jumpRoute = (url: any, inPage = true) => {
  inPage ? router.push(url) : window.open(url);
};
const quickData = ref([
  {
    icon: "https://oss.nyyyds.com/upload/20241211/2523f082fd4449f5a6792eed36729bd1.png",
    path: "/shop/shop",
    title: "商品列表"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/efd0c18088eb49d282903ce34debe33b.png",
    path: "/shop/shoPaudit",
    title: "商品审核"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/99f8859998b7428b8776e66c7337f45f.png",
    path: "/shop/syspartnerpush",
    title: "选号网站"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/1b784c1f20fc410ca1a01df3a0438542.png",
    path: "/shop/partnerPushDetails/index",
    title: "API接口管理"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/7c294f7f5b1e452fa4cd8f2a4c0444e8.png",
    path: "/shop/appraised-price/index",
    title: "估价器"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/13665257f98f460bbd0dc63ddd06877e.png",
    path: "/shop/bargain/index",
    title: "议价中心"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/2db1ea48662d42ae8653957d54a378fd.png",
    path: "/order/acquisition/index",
    title: "回收订单"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/5d05288b84764cf48c289071bab712dc.png",
    path: "/order/sell/index",
    title: "销售订单"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/eda97e78d9214c14813205d330617676.png",
    path: "/plugin/api/index",
    title: "API管理"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/d6c40792253e47dcac58150fa9518de0.png",
    path: "/plugin/paid/paidfunction",
    title: "付费功能"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/810196f99d894c5d83d54d07d684e5fd.png",
    path: "/game/attribute",
    title: "筛选属性"
  },
  {
    icon: "https://oss.nyyyds.com/upload/20241211/e5c44cc6f3614a3cbf8abefb52bc2c94.png",
    path: "https://www.yuque.com/zhangzz-bx7zu/yf3mcg?#",
    inPage: false,
    title: "平台公告"
  }
]);
</script>

<style lang='less' scoped>
.quick {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.inlet_list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 7px;
  .inlet_list_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 0px;
    margin: 7px 0px;
    cursor: pointer;

    &.isWrap {
      min-width: 94px;
    }
    .inlet_icon {
      width: 36px;
      height: 36px;
      background-color: var(--el-color-primary);
      border-radius: 6px;
      padding: 6px;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .inlet_name {
      font-weight: 400;
      font-size: 12px;
      color: #1d2129;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>