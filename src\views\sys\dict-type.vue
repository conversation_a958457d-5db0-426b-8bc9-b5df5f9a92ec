<template>
    <div class="mod-sys__dict" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:dict:save')" type="primary" @click="addOrUpdateHandle()">{{
                        $t("add")
                        }}</el-button>
                    <el-button v-if="state.hasPermission('sys:dict:delete')" type="danger" @click="state.deleteHandle()">{{
                        $t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.dictName" :placeholder="$t('dict.dictName')" clearable></el-input>
                    <el-input v-model="state.dataForm.dictType" :placeholder="$t('dict.dictType')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                
                <!-- 字典类型 -->
                <template #dictType="scope">
                    <el-button type="primary" link @click="showTypeList(scope.row)">{{ scope.row.dictType }}</el-button>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button type="primary" text bg @click="showTypeList(scope.row)">字典配置</el-button>
                    <el-button v-if="state.hasPermission('sys:dict:update')" type="warning" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:dict:delete')" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
        <!-- 字典类型数据 -->
        <el-drawer v-if="dictDataVisible" v-model="dictDataVisible" :title="state.focusDictTypeTitle" :size="800"
            :close-on-press-escape="false" class="rr-drawer">
            <DictTypeList :dictTypeId="state.focusDictTypeId"></DictTypeList>
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./dict-type-add-or-update.vue";
import DictTypeList from "./dict-data.vue";
import { IObject } from "@/types/interface";
import { useRoute } from "vue-router";
const route = useRoute();

const dictDataVisible = ref(false);

const view = reactive({
    getDataListURL: "/sys/dict/type/page",
    getDataListIsPage: true,
    deleteURL: "/sys/dict/type",
    deleteIsBatch: true,
    dataForm: {
        id: "0",
        dictName: "",
        dictType: ""
    },
    focusDictTypeId: "",
    focusDictTypeTitle: ""
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
      prop: "dictName",
      label: "字典名称",
      minWidth: 150
    },
    {
      prop: "dictType",
      label: "字典类型",
      minWidth: 150
    },
    {
      prop: "sort",
      label: "排序",
      minWidth: 80,
      sortable: true
    },
    {
      prop: "remark",
      label: "备注",
      minWidth: 150
    },
    {
      prop: "createDate",
      label: "创建时间",
      minWidth: 150,
      sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 220
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.dictName = "";
    view.dataForm.dictType = "";
    state.getDataList();
}

const showTypeList = (row: IObject) => {
    dictDataVisible.value = true;
    state.focusDictTypeId = row.id;
    state.focusDictTypeTitle = `${route.meta.title} - ${row.dictType}`;
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
