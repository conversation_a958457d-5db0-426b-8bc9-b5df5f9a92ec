<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="visible" :close-on-click-modal="false" title="盼之频繁提交校验" :destroy-on-close="true" width="466">
    <el-form @keyup.enter="onSubmitPre()" ref="formRef" :rules="rules" :model="dataForm">
      <el-form-item prop="phone" label="手机号">
        <el-input type="mobile" clearable v-model="dataForm.phone" placeholder="请输入手机号"> </el-input>
      </el-form-item>
      <el-form-item prop="code" label="验证码">
        <el-input v-model="dataForm.code" clearable type="text" placeholder="请输入短信验证码">
          <template #suffix>
            <el-button :disabled="state.codeCd" size="small" slot="append" @click="handleCaptcha" style="border: none; font-size: 14px; color: #4165d7">
              获取验证码
              <div v-if="state.codeCd">({{ state.long }})</div>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">{{ t("Cancel") }}</el-button>
      <el-button :loading="state.submitLoading" @click="onSubmitPre" type="primary">提交 </el-button>
    </template>
  </el-dialog>
</template>
  
  <script setup lang="ts">
import { ref, reactive, inject, watch, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance, FormItemRule } from "element-plus";
import { ElMessage, ElNotification } from "element-plus";
import baseService from "@/service/baseService";

const emit = defineEmits(["pass"]);

const formRef = ref();
const { t } = useI18n();
const state: {
  codeCd: Boolean;
  submitLoading: Boolean;
  long: number;
} = reactive({
  long: 0,
  codeCd: false,
  submitLoading: false
});

const dataForm = reactive({
  phone: undefined,
  partnerId: undefined,
  code: undefined
});
const rules: Partial<Record<string, FormItemRule[]>> = reactive({
  phone: [
    {
      required: true,
      message: "手机号",
      trigger: "blur"
    },
    {
      validator: (rule: any, val: string, callback: Function) => {
        if (dataForm.phone) {
          if (!/^(1[3-9])\d{9}$/.test(dataForm.phone.toString())) {
            callback(new Error("请输入正确的手机号!"));
          } else {
            callback();
          }
        } else {
          callback(new Error("请输入手机号"));
        }
        callback();
      },
      trigger: "blur"
    }
  ],
  code: [
    {
      required: true,
      message: "验证码",
      trigger: "blur"
    }
  ]
});
const visible = ref(false);

const onSubmitPre = () => {
  formRef.value?.validate((valid: any) => {
    if (valid) {
      state.submitLoading = true;
      baseService
        .post("/script/sysscriptpushtaskdetails/submitPhoneCode", {
          phone: dataForm.phone,
          code: dataForm.code,
          partnerId: dataForm.partnerId
        })
        .then((res) => {
          if (res.code == 0) {
            ElMessage.success("校验成功");
            emit("pass");
            closeDialog();
          }
        })
        .finally(() => {
          state.submitLoading = false;
        });
    }
  });
};
let timerId = ref<any>(null);
// 获取手机验证码
const handleCaptcha = () => {
  if (!dataForm.phone || !/^(1[3-9])\d{9}$/.test(dataForm.phone.toString())) {
    ElNotification({
      message: "请输入正确的手机号",
      type: "error"
    });
    console.log("手机号校验不过！！！！！");
    return false;
  } else {
    console.log("手机号校验已经通过！！！！！");
    // 获取验证码
    baseService.post("/script/sysscriptpushtaskdetails/sendPhoneCode", {
      phone: dataForm.phone,
      partnerId: dataForm.partnerId
    });
    // 开启计时
    state.codeCd = true;
    state.long = 60;
    timerId.value = setInterval(() => {
      state.long--;
      if (state.long <= 0) {
        state.long = 60;
        state.codeCd = false;
        clearInterval(timerId.value);
        timerId.value = null;
      }
    }, 1000);
  }
};

const closeDialog = () => {
  visible.value = false;
};
const init = async (id?: any) => {
  visible.value = true;
  dataForm.partnerId = id;
};
onUnmounted(() => {
  if (timerId.value) {
    clearInterval(timerId.value);
  }
});
defineExpose({
  init
});
</script>
  
  