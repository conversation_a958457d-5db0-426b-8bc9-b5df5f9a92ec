interface PaginationParams<T> {
  currentPage: number;
  pageSize: number;
  data: T[];
}

/**
 * 分页方法
 * @param currentPage 当前页码（从1开始）
 * @param pageSize 每页显示条数
 * @param data 完整数据集
 * @returns 当前页数据
 */
export function usePagination<T>({ currentPage, pageSize, data }: PaginationParams<T>): T[] {
  // 处理非法参数
  const validPage = Math.max(1, currentPage);
  const validSize = Math.max(1, pageSize);
  
  // 计算分页起始位置
  const startIndex = (validPage - 1) * validSize;
  const endIndex = startIndex + validSize;

  // 返回分页后的数据
  return data.slice(startIndex, endIndex);
}

/**
 * 排序方法
 * @param prop 排序字段
 * @param order 排序方法
 * @param data 排序数组
 * @returns 排序后的数据
 */
interface SortListParams<T> {
  prop: string;
  order: string;
  data: T[];
}

export function useSortList<T>({prop, order, data }: SortListParams<T>) {
  if(order == 'ascending'){
    return data.sort((a:any, b:any)=>a[prop] - b[prop]);
  }
  if(order == 'descending'){
    return data.sort((a:any, b:any)=>b[prop] - a[prop]);
  }
}