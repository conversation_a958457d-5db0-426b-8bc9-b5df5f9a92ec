<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card tabsStyle">
      <ul class="headCardUl">
        <li class="headCardLi" v-for="(cardItem, index) in cardInfo" :key="index">
          <!-- 数据统计 -->
          <div class="head flex">
            <div class="flex">
              <img src="@/assets/images/flowIcon11.png" style="width: 16px; height: 16px" v-if="index == 0" />
              <img src="@/assets/images/flowIcon22.png" style="width: 16px; height: 16px; margin-right: 4px" v-if="index == 1" />
              <img src="@/assets/images/flowIcon33.png" style="width: 16px; height: 16px; margin-right: 4px" v-if="index == 2" />
              <img src="@/assets/images/flowIcon44.png" style="width: 16px; height: 16px; margin-right: 4px" v-if="index == 3" />
              <span>{{ cardItem.top.label }}</span>
            </div>
            <div>
              <template v-if="cardItem.top.canSearch">
                <el-date-picker :style="{ width: cardItem.top.width }" :value-format="'YYYY-MM-DD'" :format="'YYYY-MM-DD'" v-model="cardItem.top.dateValue" @change="topDateChange($event, index)" />
              </template>
            </div>
          </div>
          <!-- 数值 -->
          <div class="middle flex">
            <span style="font-weight: bold; font-size: 16px; color: #303133; line-height: 19px">{{ cardItem.middle }}</span>
          </div>
          <!-- 尾部 -->
          <div class="bottom flex">
            <div class="flex" style="justify-content: space-between; flex: 1">
              <div class="flex" style="flex: 1" v-for="(bottomItem, bottomItemIndex) in cardItem.bottom.dataList" :key="bottomItemIndex">
                <span>{{ bottomItem.tip }}</span>
                <span :style="'font-weight: 400;font-size: 12px;height: 20px;display: -webkit-box;color:' + bottomItem.color">{{ bottomItem.data }}</span>
              </div>
            </div>

            <el-link v-if="cardItem.bottom.hasBottom" :type="cardItem.bottom.type" :underline="false" @click="cardItem.bottom.handle"
              >{{ cardItem.bottom.label }}<el-icon size="15"><ArrowRight /></el-icon
            ></el-link>
          </div>
        </li>
      </ul>
      <!-- tab、 -->
      <el-tabs v-model="currentTableType" class="ny-tabs" @tab-change="handleClick">
        <el-tab-pane label="合同使用明细" name="1"></el-tab-pane>
        <el-tab-pane label="合同模板管理" name="2"></el-tab-pane>
        <el-tab-pane label="企业认证管理" name="3"></el-tab-pane>
      </el-tabs>
      <!-- btn/search -->
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div class="tableTopBtn">
          <template v-if="currentTableType == '2'">
            <el-button type="primary" @click="contractTemplateHandle()">新增模板</el-button>

            <el-button type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
          </template>
          <template v-else-if="currentTableType == '3'">
            <el-button type="warning" @click="certificationHandle({})">新增主体</el-button>
          </template>
          <template v-else>
            <!-- <el-button type="primary" @click="addOrUpdateHandle()">创建合同</el-button> -->
            <el-button type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
          </template>
        </div>
        <ny-form-slot>
          <template #content>
            <el-form class="contractSearchForm" :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item>
                <el-input :prefix-icon="Search" style="width: 280px !important" v-model="state.dataForm.keywords" :placeholder="currentTableType == '1' ? '请输入甲方/乙方名称/订单编号' : currentTableType == '2' ? '请输入模板编号/模板名称' : '请输入企业名称'" clearable></el-input>
              </el-form-item>
              <el-form-item v-if="currentTableType != 3">
                <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels @change="createDateChange" />
              </el-form-item>
            </el-form>
          </template>
          <template #button>
            <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
            <el-button style="margin-left: 8px" @click="getResetting()">重置</el-button>
          </template>
        </ny-form-slot>
      </div>
      <!-- table -->
      <div class="table-body" id="table-body">
        <el-table id="tableContract" @sort-change="state.dataListSortChangeHandle" v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
          <template v-if="currentTableType == '1'">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="bestsignContractId" label="合同编号" header-align="center" align="center" :show-overflow-tooltip="true" min-width="170"></el-table-column>
            <el-table-column prop="sn" label="订单编号" header-align="center" align="center" :show-overflow-tooltip="true" min-width="200"></el-table-column>
            <el-table-column prop="contractTitle" label="合同标题" header-align="center" align="center" min-width="136"></el-table-column>
            <el-table-column prop="templateName" label="模板名称" header-align="center" align="center" min-width="136"></el-table-column>
            <el-table-column prop="name" label="合同发件人" header-align="center" align="center" :show-overflow-tooltip="true" width="136">
              <template v-slot="{ row }">
                <!-- 始终是我们这方 收购我们是乙方。销售时我们是甲方 -->
                {{ row.contractType == "收购订单" ? row.partyBName : row.partyAName }}
              </template>
            </el-table-column>
            <el-table-column prop="idcard" label="合同接收人" header-align="center" align="center" :show-overflow-tooltip="true" width="136">
              <template v-slot="{ row }">
                <!-- 甲方。乙方 -->
                {{ row.contractType == "收购订单" ? row.partyAName : row.partyBName }}
              </template>
            </el-table-column>
            <el-table-column prop="contractType" label="合同类型" header-align="center" align="center" :show-overflow-tooltip="true" width="136">
              <!-- 销售合同（销售订单签署的）和回收合同（回收订单签署的） -->
              <template v-slot="{ row }">
                {{ row.contractType == "收购订单" ? "回收合同" : "销售合同" }}
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建时间" sortable header-align="center" align="center" :show-overflow-tooltip="true" width="170">
              <template v-slot="{ row }">
                {{ formatTimeStamp(row.createDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="confirmDate" label="完成时间" sortable header-align="center" align="center" :show-overflow-tooltip="true" width="170">
              <template v-slot="{ row }">
                {{ row.state == "合同已锁定" ? formatTimeStamp(+row.confirmDate) : "" }}
              </template>
            </el-table-column>
            <el-table-column prop="state" label="签署状态" header-align="center" align="center" width="123">
              <template v-slot="{ row, $index }">
                <el-tag v-if="row.state == '合同已锁定'" type="success">签署完成</el-tag>
                <el-tooltip v-else effect="dark" content="查询并同步该合同签署状态" placement="top">
                  <span @click="refreshState(row, $index)">
                    <el-tag v-loading="row.loading1" v-if="row.state == '未签署'" type="warning">
                      {{ new Date().getTime() > +(row.expireDate + "000") ? "已过期" : "签署中" }}
                      <el-icon style="font-size: 14px; margin-left: 4px"><Refresh /></el-icon>
                    </el-tag>
                    <el-tag v-loading="row.loading1" v-if="row.state == '买方已签署'" type="primary">
                      买方已签署 <el-icon style="font-size: 14px; margin-left: 4px"><Refresh /></el-icon>
                    </el-tag>
                    <el-tag v-loading="row.loading1" v-if="row.state == '卖方已签署'" type="primary">
                      卖方已签署 <el-icon style="font-size: 14px; margin-left: 4px"><Refresh /></el-icon>
                    </el-tag>
                    <el-tag v-loading="row.loading1" v-if="row.state == '双方已签署'" type="success">
                      双方已签署 <el-icon style="font-size: 14px; margin-left: 4px"><Refresh /></el-icon>
                    </el-tag>
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="currentTableType == '2'">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="templateId" label="模板编号" header-align="center" align="center" :show-overflow-tooltip="true" width="217"></el-table-column>
            <el-table-column prop="templateName" label="模板名称" header-align="center" align="center" min-width="217"></el-table-column>
            <el-table-column prop="templateType" label="类型" header-align="center" align="center" width="217">
              <template v-slot="{ row }">
                {{ row.templateType == 1 ? "销售合同" : "回收合同" }}
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建时间" sortable header-align="center" align="center" width="217"> </el-table-column>
            <el-table-column prop="updateDate" label="更新时间" sortable header-align="center" align="center" width="217"> </el-table-column>
            <el-table-column prop="isDelete" label="模板状态" header-align="center" align="center" width="123">
              <template v-slot="{ row }">
                <el-tag v-if="row.isDelete == 1" type="danger">停用</el-tag>
                <el-tag v-if="row.isDelete == 0" type="success">正常</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="变量设置" header-align="center" align="center" width="123">
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="用于模板变量与平台字段相统一，创建合同时可自动生成对应内容" placement="top">
                  <el-icon style="margin-right: 8px"><Warning /></el-icon>
                </el-tooltip>
                <span>变量设置</span>
              </template>
              <template v-slot="{ row }">
                <el-link class="btnLink" :underline="false" type="primary" @click="varSetHandle(row)">设置</el-link>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="currentTableType == '3'">
            <el-table-column prop="bestsignUserName" label="企业名称" header-align="center" align="center" :show-overflow-tooltip="true" width="217">
              <template v-slot="{ row, $index }">
                <el-button @click="certificationHandle({ ...row }, false)" type="text">{{ row.bestsignUserName }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="licenseCode" label="工商注册号或统一社会信用代码" header-align="center" align="center" min-width="217"></el-table-column>
            <el-table-column prop="legalPersonName" label="法定代表人姓名" header-align="center" align="center" width="217"> </el-table-column>
            <el-table-column prop="legalPersonIdNo" label="法定代表人证件号" header-align="center" align="center" width="217"> </el-table-column>
            <el-table-column prop="bestsignMobile" label="法定代表人手机号" header-align="center" align="center" width="217"> </el-table-column>
            <el-table-column prop="regStatus" label="认证状态" header-align="center" align="center" width="123">
              <template v-slot="{ row, $index }">
                <el-tag v-if="row.regStatus == '5'" type="success">认证成功</el-tag>
                <el-tag v-else-if="row.regStatus == '4'" type="danger">{{ statusList[+row.regStatus] || "-" }}</el-tag>
                <el-tooltip v-else effect="dark" content="查询并同步企业认证状态" placement="top">
                  <span @click="searchStatus($index)">
                    <el-tag type="warning"
                      >{{ statusList[+row.regStatus] || "-" }}<el-icon style="font-size: 14px; margin-left: 4px"><Refresh /></el-icon
                    ></el-tag>
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="isDefault" label="设为默认" header-align="center" align="center" width="123">
              <template v-slot="{ row }">
                <el-switch :active-value="1" :inactive-value="0" @click="changeDefault(row)" v-model="row.isDefault" />
              </template>
            </el-table-column>
          </template>
          <el-table-column prop="oparate" label="操作" fixed="right" header-align="center" align="center" width="200">
            <template v-slot="{ row, $index }">
              <template v-if="currentTableType == '1'">
                <el-link v-loading="row.loading4" class="btnLink" :underline="false" style="margin-right: 12px" type="warning" v-if="row.state == '合同已锁定'" @click="downPdf(row, $index)">下载</el-link>
                <el-link v-loading="row.loading2" class="btnLink" :underline="false" style="margin-right: 12px" type="primary" @click="previewHandle(row, $index)">查看</el-link>
                <el-link v-loading="row.loading3" class="btnLink" :underline="false" type="success" @click="coppyHandle(row, $index)">复制链接</el-link>
              </template>
              <template v-else-if="currentTableType == '2'">
                <el-popconfirm :icon="InfoFilled" confirm-button-text="已了解" @confirm="contractTemplateEditRefHandle(row.id)" width="300px" title="编辑成功后需要手动刷新一次列表">
                  <template #reference>
                    <el-link class="btnLink" :underline="false" style="margin-right: 12px" type="primary">编辑</el-link>
                  </template>
                </el-popconfirm>
                <!-- <el-link class="btnLink" :underline="false" style="margin-right: 12px" @click="previewHandle" type="warning">复制</el-link> -->
                <el-link class="btnLink" :underline="false" @click="state.deleteHandle(row.id)" type="danger">删除</el-link>
              </template>
              <template v-else-if="currentTableType == '3'">
                <el-link @click="certificationHandle({ ...row })" class="btnLink" :underline="false" style="margin-right: 12px" type="primary">编辑</el-link>
                <el-link class="btnLink" :underline="false" @click="signtureHandle({ ...row })" style="margin-right: 12px" type="warning">上传签章</el-link>
                <el-link
                  class="btnLink"
                  :underline="false"
                  @click="
                    dialogForm.visible = true;
                    dialogForm.id = row.id;
                  "
                  type="danger"
                  >删除</el-link
                >
              </template>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </div>
      <div class="NYpagination" :style="`width: ${tableWidth}px;`">
        <el-pagination background :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle">
        </el-pagination>
      </div>
      <el-dialog v-model="dialogForm.visible" width="500">
        <template #header>
          <span style="font-weight: bold; font-size: 18px; color: #303133"
            ><el-text type="danger"
              ><el-icon><InfoFilled /></el-icon
            ></el-text>
            删除企业确认</span
          >
        </template>
        <div style="margin-top: 8px">删除企业后，该企业认证将失效，是否删除？</div>
        <template #footer>
          <div class="dialog-footer">
            <el-button
              @click="
                dialogForm.visible = false;
                dialogForm.id = undefined;
              "
              >取消</el-button
            >
            <el-button type="danger" @click="deleteHandle"> 确认删除 </el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 预览 -->
      <el-drawer v-model="dialogVisible" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="50%">
        <div style="width: 100%; height: 100%">
          <pdfPreview :src="preUrl" page-scale="page-fit" :height="800" theme="dark" />
        </div>
      </el-drawer>
      <!-- 合同数量用完商户 -->
      <addContract ref="addContractRef"></addContract>
      <!-- 编辑 -->
      <contractTemplateEdit @refreshDataList="state.getDataList()" ref="contractTemplateEditRef"></contractTemplateEdit>
      <!-- 签章管理 -->
      <signatureMana ref="signatureManaRef"> </signatureMana>
      <!-- 企业认证 -->
      <enterpriseCertification @refreshDataList="state.getDataList()" :key="enterpriseCertificationkey" ref="enterpriseCertificationRef"> </enterpriseCertification>
      <!-- 变量设置 -->
      <varSet ref="varSetRef"> </varSet>
      <!-- 上传签章 -->
      <addSignture @refreshDataList="state.getDataList()" ref="addSigntureRef"></addSignture>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import pdfPreview from "@/components/pdf-preview/index.vue";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { formatTimeStamp } from "@/utils/method";
import { Search } from "@element-plus/icons-vue";
import { nextTick, reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
import addContract from "./add-or-update-contract.vue";
import contractTemplateEdit from "./contractTemplateEdit.vue";
import enterpriseCertification from "./enterpriseCertification.vue";
import signatureMana from "./signatureMana.vue";
import varSet from "./varSet.vue";
import { InfoFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import addSignture from "./add-signature.vue";
import useClipboard from "vue-clipboard3";
const { toClipboard } = useClipboard();
const statusList = ref(["taskId不存在或已过期", "新申请", "申请中", "超时", "申请失败", "成功", "失败重试"]);
// 组件
const addContractRef = ref();
const contractTemplateEditRef = ref();
const enterpriseCertificationRef = ref();
const signatureManaRef = ref();
const varSetRef = ref();

const view = reactive({
  getDataListURL: "/bestsign/contractPage",
  getDataListIsPage: true,
  deleteURL: "/bestsign/deleteContract",
  deleteIsBatch: true,
  dataForm: {
    keywords: undefined,
    startTime: undefined,
    endTime: undefined
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
const currentTableType = ref("1"); //当前Tab栏
const cardInfo = reactive([
  {
    top: {
      img: "",
      label: "今日合同使用量",
      canSearch: true,
      width: "112px",
      dateValue: undefined
    },
    middle: 0,
    bottom: {
      dataList: [
        {
          tip: "模板使用总量：",
          data: "0",
          color: "#4165D7"
        }
      ],
      hasBottom: false,
      label: "",
      type: "primary",
      handle: () => {} //待审核tab
    }
  },
  {
    top: {
      img: "",
      label: "今日企业来往次数",
      canSearch: true,
      width: "112px",
      dateValue: undefined
    },
    middle: 0,
    bottom: {
      dataList: [
        {
          tip: "本月来往次数：",
          data: "0",
          color: "#409EFF"
        }
      ],
      hasBottom: false,
      label: "",
      type: "primary",
      handle: () => {} //待审核tab
    }
  },
  {
    top: {
      img: "",
      label: "今日个人来往次数",
      canSearch: true,
      width: "112px",
      dateValue: undefined
    },
    middle: 0,
    bottom: {
      dataList: [
        {
          tip: "本月来往次数：",
          data: "0",
          color: "#67C23A"
        }
      ],
      hasBottom: false,
      label: "",
      type: "primary",
      handle: () => {} //待审核tab
    }
  },
  {
    top: {
      img: "",
      label: "当前剩余合同份数",
      canSearch: false,
      width: "112px",
      dateValue: undefined
    },
    middle: 0,
    bottom: {
      dataList: [
        {
          tip: "总购买合同份数：",
          data: "0",
          color: "#F44A29"
        }
      ],
      hasBottom: false,
      label: "",
      type: "primary",
      handle: () => {} //待审核tab
    }
  }
]);
// 企业认证数据
const bestsignEnpUserData = ref();
// 获取今天日期
const defaultDate = ref();

// 时间区间筛选
const createDate = ref();
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
  state.getDataList();
};
const getResetting = () => {
  state.dataForm.keywords = undefined;
  state.dataForm.startTime = undefined;
  state.dataForm.endTime = undefined;
  createDate.value = undefined;
  state.getDataList();
};
// 切换tab
const handleClick = () => {
  state.getDataListURL = currentTableType.value == "1" ? "/bestsign/contractPage" : currentTableType.value == "2" ? "/bestsign/templatePage" : "/bestsign/bestsignUserPage";
  state.deleteURL = currentTableType.value == "1" ? "/bestsign/deleteContract" : currentTableType.value == "2" ? "/bestsign/delete" : "/bestsign/deleteUser";
  state.dataList = [];
  currentTableType.value == "3" ? (state.dataForm.bestsignUserType = 2) : "";
  state.getDataList();
};
// ------------------------------------企业认证
const dialogForm = reactive({
  id: undefined,
  visible: false
});
const deleteHandle = () => {
  baseService
    .delete("/bestsign/deleteUser", [dialogForm.id])
    .then((res) => {
      ElMessage.success("操作成功！");
      state.getDataList();
    })
    .finally(() => {
      dialogForm.visible = false;
    });
};
const enterpriseCertificationkey = ref(0);
const certificationHandle = async (obj?: any, isEdit?: Boolean) => {
  enterpriseCertificationkey.value++;
  await nextTick();
  enterpriseCertificationRef.value.init(obj, isEdit);
};
// 查询认证状态
const searchStatus = (index?: any) => {
  // 1-个人   2-企业
  baseService.get("/bestsign/queryRegStatus/" + state.dataList[index].bestsignMobile + "/2").then((res) => {
    state.dataList[index].regStatus = res.data?.regStatus;
  });
};
const changeDefault = (obj: any) => {
  if (!obj.id) return;
  baseService
    .put("/bestsign/setDefaultUser", { ...obj })
    .then((res) => {
      ElMessage.success(res.msg);
      state.getDataList();
    })
    .catch((err) => {
      state.getDataList();
    });
};
const addSigntureRef = ref();
const signtureHandle = async (obj?: any) => {
  await nextTick();
  addSigntureRef.value.init(obj);
};
// -----------------------------------合同管理
// 预览
const dialogVisible = ref();
const preUrl = ref();
const previewHandle = async (obj: any, i: any) => {
  state.dataList[i].loading2 = true;
  let res = await baseService.get("/bestsign/previewContract/" + obj.id);
  state.dataList[i].loading2 = false;
  window.open(res?.data);
};
const coppyHandle = (obj: any, i: any) => {
  state.dataList[i].loading3 = true;
  baseService
    .get("/bestsign/previewContract/" + obj.id)
    .then(async (res) => {
      try {
        await toClipboard(res?.data);
        ElMessage.success("复制成功");
      } catch (e: any) {
        ElMessage.warning("复制操作不被支持或失败：", e);
      }
    })
    .finally(() => {
      state.dataList[i].loading3 = false;
    });
};
// 下载
const downPdf = async (obj: any, i: any) => {
  state.dataList[i].loading4 = true;
  let res = await baseService.get("/bestsign/downloadContract/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  state.dataList[i].loading4 = false;
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
const refreshState = (obj: any, i: any) => {
  if (obj.state == "签署完成") return;
  state.dataList[i].loading1 = true;
  baseService
    .get("/bestsign/checkContractStatus/" + obj.id)
    .then((res) => {
      res?.code == 0 ? state.query() : "";
    })
    .finally(() => {
      state.dataList[i].loading1 = false;
    });
};
// --------------------------------合同模板
const contractTemplateEditRefHandle = (id?: any) => {
  baseService.get("/bestsign/updateTemplate/" + id).then((res) => {
    if (res.code == 0) {
      window.open(res.data);
    }
  });
};
// 调起组件
const addOrUpdateHandle = async (obj?: any) => {
  await nextTick();
  addContractRef.value.init(obj);
};
const contractTemplateHandle = async (obj?: any) => {
  await nextTick();
  contractTemplateEditRef.value.init(obj);
};

const varSetHandle = async (obj?: any) => {
  await nextTick();
  varSetRef.value.init(obj.id);
};
const topDateChange = (event: any, index: any) => {
  // 清空时恢复默认值
  if (!cardInfo[index].top.dateValue) {
    cardInfo[index].top.dateValue = defaultDate.value;
  }
  gerStatistics(+index + 1);
};
const gerStatistics = (index: any) => {
  baseService
    .post("/bestsign/statistics", {
      type: index,
      startTime: cardInfo[index - 1].top.dateValue + " 00:00:00",
      endTime: cardInfo[index - 1].top.dateValue + " 23:59:59"
    })
    .then((res) => {
      let data = [res.data?.today, res.data?.enpMonthTotal, res.data?.personalMonthTotal, res.data?.partnerCount][index - 1];
      cardInfo[index - 1].middle = [data.todayCount, data.monthCount, data.monthCount, data.countBalance][index - 1];
      cardInfo[index - 1].bottom.dataList[0].data = [data.totalCount, data.totalCount, data.totalCount, data.countTotal][index - 1];
    });
};
const tableWidth = ref(0);
onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  let month = now.getMonth() + 1; // 月份从0开始，需要加1
  let day = now.getDate();
  month = +month > 9 ? month : "0" + month;
  day = +day > 9 ? day : "0" + day;
  defaultDate.value = year + "-" + month + "-" + day;
  baseService
    .post("/bestsign/statistics", {
      type: null
    })
    .then((res) => {
      for (let index = 1; index < 5; index++) {
        cardInfo[index - 1].top.dateValue = defaultDate.value;
        let data = [res.data?.today, res.data?.enpMonthTotal, res.data?.personalMonthTotal, res.data?.partnerCount][index - 1];
        cardInfo[index - 1].middle = [data.todayCount, data.monthCount, data.monthCount, data.countBalance][index - 1];
        cardInfo[index - 1].bottom.dataList[0].data = [data.totalCount, data.totalCount, data.totalCount, data.countTotal][index - 1];
      }
    });

  tableWidth.value = document.getElementById("tableContract").offsetWidth;
  window.addEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById("tableContract").offsetWidth;
    });
  }); // 监听窗口大小变化
});

onUnmounted(() => {
  window.removeEventListener("resize", () => {
    nextTick(() => {
      tableWidth.value = document.getElementById("tableContract").offsetWidth;
    });
  });
});
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  align-items: center;
}
.headCardUl,
.headCardLi {
  padding: 0;
  list-style: none;
}
.headCardUl {
  display: flex;
  align-items: cenetr;
  flex-wrap: nowrap;

  .headCardLi {
    width: 25%;
    margin-right: 12px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;

    &:last-child {
      margin-right: 0;
    }

    .head {
      justify-content: space-between;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 18px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
      :deep(.el-input) {
        height: 24px;
        .el-input__wrapper {
          height: 24px;
          line-height: 24px;
          .el-input__inner {
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 20px;
          }
        }
      }
    }

    .middle {
      justify-content: space-between;
      span {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      :deep(.el-statistic__content) {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bottom {
      margin-top: 4px;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      justify-content: space-between;
      :deep(.el-link__inner) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 14px;
        height: 11px;
        margin-left: 5px;
      }
    }
  }
}
.cardTop {
  margin-bottom: 20px;
}

.tableTopBtn {
  margin-bottom: 12px;
}

.ny-tabs {
  :deep(.el-tabs__item) {
    width: 124px;
    padding: 0;
  }
}
.tabsStyle {
  .el-card__body {
    padding-bottom: 0;
  }
}
:deep(.form_solt_content) {
  max-width: inherit !important;
}
:deep(.form_solt_right) {
  margin-bottom: 12px !important;
}
.contractSearchForm {
  .el-form-item {
    margin-right: 8px !important;
    margin-bottom: 12px;
  }
}

.btnLink {
  :deep(.el-link__inner) {
    padding: 0 4px;
    height: 22px;
    background: #f5f7fa;
    border-radius: 2px 2px 2px 2px;
  }
}

.el-tag {
  border: 1px solid;
  cursor: pointer;

  :deep(.el-tag__content) {
    display: flex;
    align-items: center;
  }
}
</style>
