<template>
  <div>
    <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @dragSort="dragSortHandle">
      <template #header>
        <el-button type="primary" v-if="state.hasPermission('shop:guaranteeinfo:save')" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <!-- <el-button type="danger" v-if="state.hasPermission('shop:guaranteeinfo:delete')" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button> -->
      </template>
      <template #infoOptions="{ row }">
        <span v-if="row.infoType == 0">文本</span>
        <span v-if="row.infoType == 1">图片</span>
        <span v-if="row.infoType == 2">数字</span>
        <span v-if="row.infoType == 3">{{ row.infoOptions }}</span>
        <span v-if="row.infoType == 4">{{ row.infoOptions }}</span>
        <span v-if="row.infoType == 5">城市</span>
      </template>
      <template #required="{ row }">
        <span v-if="row.required == 0">非必填</span>
        <span v-if="row.required == 1">必填</span>
      </template>
      <template #isShow="{ row }">
        <el-switch v-model="row.isShow" :active-value="1" :inactive-value="0" :loading="row.loading" :disabled="row.isDefault == 1" @change="isShowChange(row)" />
      </template>
      <template #operation="{ row }">
        <el-button v-if="state.hasPermission('shop:guaranteeinfo:update')" :disabled="row.isDefault == 1" type="primary" text bg @click="addOrUpdateHandle(row.id)" size="small">{{ $t("update") }}</el-button>
        <el-button v-if="state.hasPermission('shop:guaranteeinfo:delete')" :disabled="row.isDefault == 1" type="danger" text bg @click="state.deleteHandle(row.id)" size="small">{{ $t("delete") }}</el-button>
        <el-button class="move" type="primary" text bg>
          <el-icon class="text-primary">
            <Rank />
          </el-icon>
        </el-button>
      </template>
    </ny-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./guaranteeInfo-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "title",
    label: "信息名称",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "infoOptions",
    label: "字段配置格式",
    minWidth: "320",
    align: "center"
  },
  {
    prop: "sortNum",
    label: "排序",
    minWidth: "80",
    align: "center"
  },
  {
    prop: "required",
    label: "是否必填",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "guaranteenGroupName",
    label: "分组",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "isShow",
    label: "是否显示",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "100"
  }
]);

const view = reactive({
  getDataListURL: "/shop/guaranteeinfo/page",
  getDataListIsPage: true,
  deleteURL: "/shop/guaranteeinfo",
  deleteIsBatch: true,
  dataForm: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 是否显示点击事件
const isShowChange = (row: any) => {
  if (row.id) {
    row.loading = true;
    baseService
      .put("/shop/guaranteeinfo", row)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
      })
      .finally(() => {
        row.loading = false;
      });
  }
};
// 排序
const dragSortHandle = async ({ newIndex, oldIndex }: { newIndex: number; oldIndex: number }) => {
  console.log(newIndex, oldIndex);
  console.log(state.dataList);
  // targetId 移动到目标游戏id
  // sourceId 选择移动游戏id

  let res = await baseService.put("/shop/guaranteeinfo/changeSort", { movedId: state.dataList[oldIndex].id, targetId: state.dataList[newIndex].id });
  if (res.code == 0) {
    ElMessage.success("排序成功");
  }
  state.getDataList();
  state.dataList?.sort((a, b) => a.sort - b.sort);
};
</script>

<style lang="less" scoped></style>
