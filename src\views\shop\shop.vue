<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title" @change="gamesChange"></ny-flod-tab>
      <shop-stat ref="shopStatRef" :gameId="view.dataForm.gameId" v-if="store.state.user.id == 1067246875800000001 || (store.state.user.id != 1067246875800000001 && store.state.user.overseerPermissions)"></shop-stat>
      <ny-table noDataType="4" cellHeight="ch-96" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <div class="flx-justify-between" style="padding-top: 12px">
            <ny-button-group :list="groupList" v-model="view.dataForm.status" @change="handleStatusClick"></ny-button-group>
          </div>
        </template>
        <template #header-right>
          <el-button type="info" :icon="Refresh" :loading="shopInitLoading" @click="getShopInit" v-if="store.state.user.id == 1067246875800000001">索引同步</el-button>
        </template>
        <template #header-custom>
          <div class="flx-justify-between pb-12">
            <div v-if="state.dataForm.status != 5">
              <template v-if="state.dataForm.status == 6">
                <el-button v-if="state.hasPermission('shop:shop:delete')" type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
                <el-button v-if="state.hasPermission('shop:shop:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
              </template>
              <template v-else>
                <el-button v-if="state.hasPermission('shop:shop:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                <!-- <el-button type="primary" @click="shopJob()"></el-button> -->

                <el-dropdown>
                  <el-button type="warning" style="margin: 0px 12px">库存设置</el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="nyDropdownMenu">
                      <el-dropdown-item @click="shopJob()">自动降价</el-dropdown-item>
                      <el-dropdown-item @click="setStockWarn()">库存预警设置</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button v-if="state.hasPermission('shop:shop:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
                <el-button v-if="state.hasPermission('shop:shop:delete')" type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>

                <!-- <el-button type="warning" @click="openLink()">跳转估价网站生成</el-button> -->
                <!-- <el-button type="warning" @click="setStockWarn()">库存预警设置</el-button> -->
                <el-button type="info" v-if="view.dataForm.status == '2'" @click="delistClickMore(state.dataListSelections)" :disabled="!state.dataListSelections || !state.dataListSelections.length">批量下架</el-button>
              </template>
            </div>
            <div class="filter" style="display: flex; align-items: center; justify-content: flex-end; flex: 1">
              <div style="margin-right: 12px" class="flx-align-center">
                <el-input v-model="state.dataForm.title" placeholder="游戏标题" :prefix-icon="Search" clearable></el-input>
                <el-input v-model="state.dataForm.code" placeholder="商品编码" :prefix-icon="Search" clearable></el-input>
                <el-input v-model="state.dataForm.gameAccount" placeholder="游戏账号" :prefix-icon="Search" clearable></el-input>
                <el-input v-model="state.dataForm.remark" placeholder="备注" :prefix-icon="Search" clearable></el-input>
              </div>
              <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
              <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
              <el-button @click="visibleFilter = true">高级筛选</el-button>
            </div>
          </div>
        </template>
        <template #code="{ row }">
          <div v-html="row.code"></div>
        </template>
        <template #remark="{ row }">
          <div v-html="row.remark"></div>
        </template>
        <template #listingTime="{ row }">
          <span>{{ formatTimeStamp(row.listingTime) }}</span>
        </template>
        <template #sellTime="{ row }">
          <span>{{ formatTimeStamp(row.sellTime) }}</span>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #delistingTime="{ row }">
          <span>{{ formatTimeStamp(row.delistingTime) }}</span>
        </template>
        <template #title="{ row }">
          <div class="shoping">
            <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
            <div class="info">
              <div class="title mle" v-html="row.title" @click="toDetails(row)"></div>
              <div class="sle" style="width: 185px; text-align: left">
                {{ `${row.gameName} / ${row.serverName}` }}
              </div>
              <div>
                <span>{{ state.getDictLabel("shop_compensation", row.compensation) }}</span>
              </div>
            </div>
          </div>
        </template>
        <template #gamePassword="{ row }">
          <div style="display: flex; align-items: center; justify-content: center; gap: 4px">
            <div>{{ row.isShowGamePassword ? row.gamePassword : "******" }}</div>
            <el-icon v-if="state.hasPermission('shop:shop:password')" class="pointer" @click="setAccountPassword(row)">
              <View v-if="row.isShowGamePassword" />
              <Hide v-if="!row.isShowGamePassword" />
            </el-icon>
          </div>
        </template>

        <template #updateDate="{ row }">
          <span>{{ formatTimeStamp(row.updateDate) }}</span>
        </template>
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <div v-if="row.status == '3'" class="flx-column">
            <el-tag type="danger">已下架</el-tag>
            <span style="font-size: 12px">{{ `（${row.delistingRemark}）` }}</span>
          </div>
          <div v-if="row.status == '4'" class="flx-column">
            <el-tag type="success">已出售</el-tag>
            <span style="font-size: 12px">{{ `(${row.channelName || "-"})` }}</span>
          </div>
          <el-tag type="warning" v-if="row.status == '5'">{{ row.statusName }}</el-tag>
          <el-tag type="warning" v-if="row.status == '6'">{{ row.statusName }}</el-tag>
          <el-tag type="warning" v-if="row.status == '7'">交易中</el-tag>
          <el-tag type="warning" v-if="row.status == '10'">{{ row.statusName }}</el-tag>
          <el-tag style="color: #d91ad9; background: #ffe8fb" v-if="row.status == '11'">{{ row.statusName }}</el-tag>
        </template>
        <template #log="{ row }">
          <el-button type="primary" text bg @click="toLog(row)">查看</el-button>
        </template>
        <template #operation="{ row }">
          <!-- 判断是给【全部】按钮显示做的 -->
          <div style="display: flex; align-items: center; justify-content: center" v-if="row.status != 6 && row.status != 5 && row.status != 10 && row.status != 11">
            <el-button v-if="state.hasPermission('shop:shop:update') && row.status != '4'" type="primary" text bg @click="addOrUpdateHandle(row.id, row.gameId)">{{ $t("update") }}</el-button>
            <!-- 并上类型是平台代售 -->
            <!-- <el-button type="warning" text bg v-if="row.status == '2'" @click="delistClick(row)">下架</el-button> -->
            <el-button type="success" text bg v-if="row.status == '2' || (row.status == '3' && row.accountSourceName != '平台代售')" @click="delistClick(row, 'sold')">售出</el-button>
            <!-- <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定上架改商品吗？" @confirm="onListingAction(row, 2)">
              <template #reference>
                <el-button type="primary" text bg v-if="row.status == '1' || row.status == '3'">上架</el-button>
              </template>
            </el-popconfirm> -->
            <el-dropdown placement="top" v-if="row.status == '1' || row.status == '3'" @command="(e: any) => releasedCommand(e, row)">
              <el-button type="primary" text bg>上架</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="2">推送上架</el-dropdown-item>
                  <el-dropdown-item :command="1">正常上架</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定重新上架该商品吗？" @confirm="onListingAction(row, 8)">
              <template #reference>
                <el-button type="primary" text bg v-if="row.status == '4'">重新上架</el-button>
              </template>
            </el-popconfirm>
            <el-button type="danger" text bg v-if="row.status == '2'" @click="delistClick(row)">下架</el-button>
            <el-button type="warning" text bg v-if="row.accountSourceName != '平台代售' && row.status != 4" @click="markSaleshandle(row)">标记售后</el-button>
            <!-- <el-button type="warning" text bg v-if="state.hasPermission('shop:shop:delete') && (row.status == '1' || row.status == '3')" @click="state.deleteHandle(row.id)">
              {{ $t("delete") }}
            </el-button> -->
          </div>
          <div style="display: flex; align-items: center; justify-content: center" v-else-if="row.status == 5">
            <template v-if="row.afterType != '处理中'">
              <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定作废该商品吗？" @confirm="cancellationPre(row)">
                <template #reference>
                  <el-button type="primary" text bg v-if="row.accountSourceName != '平台代售'">作废</el-button>
                </template>
              </el-popconfirm>
              <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定重新上架该商品至【待上架】吗？" @confirm="onListingAction(row, 9)">
                <template #reference>
                  <el-button type="primary" text bg>重新上架</el-button>
                </template>
              </el-popconfirm>
              <el-button type="warning" text bg v-if="row.afterType == '已过期' && row.saleStatus == '0'" @click="markSaleshandle(row)">重新标记</el-button>
            </template>
            <template v-else>
              <el-button type="primary" text bg>-</el-button>
            </template>
          </div>
          <div v-else>-</div>
        </template>

        <!-- 问题账号 -->
        <template #saleStatus="{ row }">
          <el-tag type="danger" v-if="row.saleStatus == '0'">未售</el-tag>
          <el-tag type="primary" v-if="row.saleStatus == '1'">已售</el-tag>
        </template>
        <template #afterType="{ row }">
          <el-tag type="warning" v-if="row.afterType == '处理中'">处理中</el-tag>
          <el-tag type="danger" v-else-if="row.afterType == '赔付(全额退款)'">赔付(全额退款)</el-tag>
          <el-tag type="danger" v-else-if="row.afterType == '赔付(部分退款)'">赔付(部分退款)</el-tag>
          <el-tag type="info" v-else-if="row.afterType == '不予处理'">不予处理</el-tag>
          <el-tag type="info" v-else-if="row.afterType == '其他'">其他</el-tag>
          <el-tag type="info" v-else-if="row.afterType == '已过期'">已过期</el-tag>
          <el-tag type="success" v-else>{{ row.afterType }}</el-tag>
        </template>

        <!-- 回收人  平台代售不用显示 -->
        <!-- <template #acquisitionName="{ row }">
          <div v-if="row.accountSourceName != '平台代售'">{{ row.acquisitionName }}</div>
          <div v-else>-</div>
        </template> -->

        <template #operation2="{ row }">
          <div style="display: flex; align-items: center; justify-content: center">
            <template v-if="row.afterType != '处理中'">
              <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定作废该商品吗？" @confirm="cancellationPre(row)">
                <template #reference>
                  <el-button type="primary" text bg v-if="row.accountSourceName != '平台代售'">作废</el-button>
                </template>
              </el-popconfirm>
              <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定重新上架该商品至【待上架】吗？" @confirm="onListingAction(row, 9)">
                <template #reference>
                  <el-button type="primary" text bg>重新上架</el-button>
                </template>
              </el-popconfirm>
              <el-button type="warning" text bg v-if="row.afterType == '已过期' && row.saleStatus == '0'" @click="markSaleshandle(row)">重新标记</el-button>
            </template>
            <template v-else>
              <el-button type="primary" text bg>-</el-button>
            </template>
          </div>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span class="tableSort">
              <NyDropdownMenu
                :isBorder="false"
                v-model="SummariesParams"
                :list="
                  store.state.user.id == 1067246875800000001
                    ? [
                        { label: '零售价', value: 1 },
                        { label: '回收价', value: 2 },
                        { label: '成交价', value: 3 },
                        { label: '毛利价', value: 4 }
                      ]
                    : [
                        { label: '零售价', value: 1 },
                        { label: '回收价', value: 2 }
                      ]
                "
                labelKey="label"
                valueKey="value"
                isTop
              ></NyDropdownMenu>
            </span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="refreshTotal" source="shop"></add-or-update>
    <!-- 日志 -->
    <log ref="logRef"></log>
    <!-- 下架 -->
    <delist ref="delistRef" @refreshDataList="refreshTotal"></delist>
    <!-- 自动降价 -->
    <job ref="jobRef"></job>
    <!-- 查看商品详情 -->
    <info ref="infoRef"></info>
    <!-- 标记售后 -->
    <markSales @refresh="refreshTotal" ref="markSalesRef"></markSales>
    <!-- 库存预警设置 -->
    <shopStockwarn ref="stockwarnRef"></shopStockwarn>

    <!-- 选择合作商 -->
    <select-partners ref="selectPartnersRef" :key="selectPartnersKey" btnTxt="推送上架" @change="pushSubmit"></select-partners>

    <el-dialog v-model="doneForm.visible" title="商品备注" width="500" @close="doneForm.visible = false">
      <el-input type="textarea" :rows="3" v-model="doneForm.remark"></el-input>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="doneForm.visible = false">取消</el-button>
          <el-button type="primary" @click="cancellation"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 高级筛选 -->
    <el-dialog v-model="visibleFilter" title="高级筛选" width="900">
      <el-descriptions style="width: 100%" border :column="2">
        <el-descriptions-item class-name="noneSelfRight" label="游戏区服" v-if="view.dataForm.gameId && view.dataForm.gameId.length > 0">
          <el-select v-if="view.dataForm.gameId && view.dataForm.gameId.length > 0" filterable placeholder="请选择区服" v-model="state.dataForm.server" clearable>
            <el-option v-for="(item, index) in serveList" :key="index" :value="item.id" :label="item.title">
              {{ item.title }}
            </el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="游戏标题">
          <el-input v-model="state.dataForm.title" placeholder="请输入游戏标题" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="商品编码">
          <el-input v-model="state.dataForm.code" placeholder="请输入商品编码" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="商品描述">
          <el-input v-model="state.dataForm.search" placeholder="请输入商品描述" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="游戏账号">
          <el-input v-model="state.dataForm.gameAccount" placeholder="请输入游戏账号" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <el-input v-model="state.dataForm.remark" placeholder="请输入备注" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          <el-select placeholder="请选择类型" v-model="state.dataForm.accountSource" clearable>
            <el-option v-for="(item, index) in saleTypeList" :key="index" :value="item.value" :label="item.label">
              {{ item.label }}
            </el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          <el-select placeholder="请选择创建人" v-model="state.dataForm.creator" clearable filterable>
            <el-option v-for="(item, index) in memberList" :key="index" :value="item.id" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="回收人">
          <el-select placeholder="请选择回收人" v-model="state.dataForm.acquisitionUser" clearable filterable>
            <el-option v-for="(item, index) in employeeList" :key="index" :value="item.id" :label="item.realName">
              {{ item.realName }}
            </el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="出售人">
          <el-select placeholder="请选择出售人" v-model="state.dataForm.transactionUser" clearable filterable>
            <el-option v-for="(item, index) in employeeList" :key="index" :value="item.id" :label="item.realName">
              {{ item.realName }}
            </el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          <el-input v-model="state.dataForm.phone" placeholder="请输入手机号" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="自编码">
          <el-input v-model="state.dataForm.ownCoding" placeholder="请输入自编码" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>

        <el-descriptions-item label="应急手机号">
          <el-input v-model="state.dataForm.emergencyPhone" placeholder="请输入应急手机号" :prefix-icon="Search" clearable></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="出售渠道">
          <el-cascader style="width: 100%" :show-all-levels="false" clearable v-model="selectedOptions" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择出售渠道" @change="handleChange" />
          <!-- <el-select placeholder="请选择出售渠道" v-model="state.dataForm.channelId" clearable>
            <el-option v-for="(item, index) in channelList" :key="index" :value="item.id" :label="item.title">
              {{ item.title }}
            </el-option>
          </el-select> -->
        </el-descriptions-item>
        <el-descriptions-item label="时间">
          <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" />
        </el-descriptions-item>
        <el-descriptions-item v-if="view.dataForm.gameId && view.dataForm.gameId.length > 0" label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
      </el-descriptions>
      <template v-slot:footer>
        <el-button @click="visibleFilter = false">{{ $t("cancel") }}</el-button>
        <el-button
          type="primary"
          @click="
            visibleFilter = false;
            queryFn();
          "
          >查询</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./shop-add-or-update.vue";
import Delist from "./shop-delist.vue";
import Log from "./shop-log.vue";
import Job from "./shop-job.vue";
import Info from "./shop-info.vue";
import shopStat from "./shop-stat.vue";
import markSales from "./shop-markSales.vue";
import shopStockwarn from "./shop-stockwarn.vue";
import SelectPartners from "./components/SelectPartners.vue";
import { formatTimeStamp, getAccountPassword } from "@/utils/method";
import ExcelImport from "./attribute-import.vue";
import baseService from "@/service/baseService";
import { getDictDataList, generateUUID } from "@/utils/utils";
import { Search, Refresh } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { useI18n } from "vue-i18n";
const store = useAppStore();
const settingStore = useSettingStore();
const { t } = useI18n();

const gamesList = ref(<any>[]); // 游戏列表
const totalPrice = ref(<any>""); // 合计数据
const logRef = ref(); // 日志
const delistRef = ref(); // 下架
const jobRef = ref(); // 自动降价
const infoRef = ref(); // 查看商品详情
const shopStatRef = ref();
const timeInterval = ref(); // 时间区间
const channelList = ref([]); // 出售渠道列表
const serveList = ref(<any>[]); // 区服列表

const view = reactive({
  getDataListURL: "/shop/shop/search",
  getDataListIsPage: true,
  getDataListIsPageSize: true,
  exportURL: "/shop/shop/export",
  deleteURL: "/shop/shop",
  deleteIsBatch: true,
  listRequestMethod: "post",
  customSorting: true,
  dataForm: {
    queryType: "1",
    gameId: "", // 游戏id
    search: "", // 复合搜索框
    status: "", // 状态
    start: "", // 开始时间
    end: "", // 结束时间
    title: "", // 游戏标题
    code: "", // 商品编码
    gameAccount: "", // 游戏账号
    remark: "", // 备注
    accountSource: "", // 类型
    creator: "", // 创建人
    acquisitionUser: "", // 回收人
    channelId: "", // 出售渠道
    ownCoding: "", // 自编码
    emergencyPhone: "", // 应急手机号
    transactionUser: "", // 出售人
    phone: "", // 手机号
    orderBy: [
      {
        field: "createDate",
        isAsc: false
      }
    ]
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格设置
const columns = ref();
const defaultcolumns = ref([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: "340"
  },
  // {
  //     prop: "gameName",
  //     label: "游戏名称",
  //     minWidth: "200"
  // },
  {
    prop: "accountSourceName",
    label: "类型",
    minWidth: "100"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: "140"
  },
  {
    prop: "gamePassword",
    label: "游戏密码",
    minWidth: "100"
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: "120"
  },
  {
    prop: "acquisitionName",
    label: "回收人",
    minWidth: "100"
  },
  {
    prop: "acquisitionPrice",
    label: "回收价(元)",
    minWidth: "120",
    sortable: "custom"
  },
  {
    prop: "creatorName",
    label: "创建人",
    minWidth: "100"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "sellTime",
    label: "售出时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "saleUserName",
    label: "售出人",
    minWidth: "100"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "130",
    sortable: "custom"
  },
  {
    prop: "transactionPrice",
    label: "成交价(元)",
    minWidth: "100"
  },
  {
    prop: "grossProfitPrice",
    label: "毛利(元)",
    minWidth: "100"
  },

  // {
  //     prop: "compensation",
  //     label: "包赔",
  //     minWidth: "120"
  // },
  {
    prop: "ownCoding",
    label: "自编码",
    width: 120
  },
  {
    prop: "emergencyPhone",
    label: "应急手机号",
    width: 120
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "200"
  },
  {
    prop: "updateDate",
    label: "修改时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "listingTime",
    label: "上架时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "delistingTime",
    label: "下架时间",
    minWidth: "200",
    sortable: "custom"
  }
]);
const defaultcolumnsOverseer = ref([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: "300"
  },
  {
    prop: "accountSourceName",
    label: "类型",
    minWidth: "100"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: "140"
  },
  {
    prop: "gamePassword",
    label: "游戏密码",
    minWidth: "100"
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: "120"
  },
  {
    prop: "acquisitionName",
    label: "回收人",
    minWidth: "100"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "130",
    sortable: "custom"
  },
  {
    prop: "creatorName",
    label: "创建人",
    minWidth: "100"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "sellTime",
    label: "售出时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "saleUserName",
    label: "售出人",
    minWidth: "100"
  },
  {
    prop: "ownCoding",
    label: "自编码",
    width: 120
  },
  {
    prop: "emergencyPhone",
    label: "应急手机号",
    width: 120
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "200"
  },
  {
    prop: "updateDate",
    label: "修改时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "listingTime",
    label: "上架时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "delistingTime",
    label: "下架时间",
    minWidth: "200",
    sortable: "custom"
  }
]);
let troublecolumns = reactive([
  {
    prop: "saleStatus",
    label: "出售状态",
    fixed: "right",
    minWidth: "100"
  },
  {
    prop: "afterType",
    label: "售后状态",
    fixed: "right",
    minWidth: "128"
  },
  {
    prop: "log",
    label: "日志",
    fixed: "right",
    minWidth: "80"
  },
  {
    prop: "operation2",
    label: "操作",
    fixed: "right",
    minWidth: "220"
  }
]);
let doneColumns = reactive([
  {
    prop: "status",
    label: "状态",
    fixed: "right",
    minWidth: "110"
  },
  {
    prop: "log",
    label: "日志",
    fixed: "right",
    minWidth: "80"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "220"
  }
]);

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number, gameId?: any) => {
  addKey.value++;
  if (gameId) {
    nextTick(() => {
      addOrUpdateRef.value.init(gameId, id);
    });
  } else {
    const gameIds = view.dataForm.gameId ? view.dataForm.gameId : gamesList.value[1].id;
    nextTick(() => {
      addOrUpdateRef.value.init(gameIds, id);
    });
  }
};

const groupList = ref([
  { dictLabel: "全部", dictValue: "" },
  { dictLabel: "待上架", dictValue: "1" },
  { dictLabel: "已上架", dictValue: "2" },
  { dictLabel: "已下架", dictValue: "3" },
  { dictLabel: "已出售", dictValue: "4" },
  { dictLabel: "问题账号", dictValue: "5" },
  { dictLabel: "作废账号", dictValue: "6" }
]);

// 出售类型
const saleTypeList = [
  // { label: "合作商同步", value: "PARTNER_SYNCHRONIZATION" },
  { label: "平台直售", value: "ORIGINAL_OWNER" },
  { label: "平台代售", value: "PLATFORM_CONSIGNMENT" }
];

// 区服列表
const getServeList = () => {
  baseService.get("/shop/shop/getGameArea", { gameId: view.dataForm.gameId }).then((res) => {
    serveList.value = [...res.data];
  });
};
const setAccountPassword = async (row: any) => {
  row.isShowGamePassword = !row.isShowGamePassword;
  row.gamePassword = await getAccountPassword(row.id);
};

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ title: "全部游戏", id: "" }, ...res.data];
    shopTotalPrice();
    if (shopStatRef.value) {
      shopStatRef.value.getAll(view.dataForm.gameId);
    }
  });
};
// 游戏切换
const gamesChange = (val: any) => {
  view.dataForm.gameId = val;
  state.dataForm.server = "";
  serveList.value = [];
  getServeList();
  refreshFn();
  if (shopStatRef.value) {
    shopStatRef.value.getAll(view.dataForm.gameId);
  }
};

// 获取表格合计
const shopTotalPrice = () => {
  baseService.get("/shop/shop/totalPrice", { queryType: 1, gameId: view.dataForm.gameId }).then((res) => {
    totalPrice.value = res.data;
  });
};

// 切换状态
const handleStatusClick = (tab: any) => {
  view.dataForm.status = tab;

  // 排序
  if (tab == "") {
    view.dataForm.orderBy[0] = {
      field: "createDate",
      isAsc: false
    };
  } else if (tab == "1") {
    view.dataForm.orderBy[0] = {
      field: "createDate",
      isAsc: false
    };
  } else if (tab == "2") {
    view.dataForm.orderBy[0] = {
      field: "listingTime",
      isAsc: false
    };
  } else if (tab == "3") {
    view.dataForm.orderBy[0] = {
      field: "delistingTime",
      isAsc: false
    };
  } else if (tab == "4") {
    view.dataForm.orderBy[0] = {
      field: "sellTime",
      isAsc: false
    };
  } else {
    view.dataForm.orderBy = [];
  }

  // 排序
  if (tab == "") {
    view.dataForm.orderBy[0] = {
      field: "createDate",
      isAsc: false
    };
  } else if (tab == "1") {
    view.dataForm.orderBy[0] = {
      field: "createDate",
      isAsc: false
    };
  } else if (tab == "2") {
    view.dataForm.orderBy[0] = {
      field: "listingTime",
      isAsc: false
    };
  } else if (tab == "3") {
    view.dataForm.orderBy[0] = {
      field: "delistingTime",
      isAsc: false
    };
  } else if (tab == "4") {
    view.dataForm.orderBy[0] = {
      field: "sellTime",
      isAsc: false
    };
  } else {
    view.dataForm.orderBy = [];
  }

  if (tab == 5) {
    columns.value = [...defaultcolumns.value, ...troublecolumns].map((ele) => {
      ele.isShow = true;
      return ele;
    });
    // state.getDataListURL = "";
  } else if (tab == 6) {
    columns.value = defaultcolumns.value.map((ele) => {
      ele.isShow = true;
      return ele;
    });
    // state.getDataListURL = "";
  } else {
    columns.value = [...defaultcolumns.value, ...doneColumns].map((ele) => {
      ele.isShow = true;
      return ele;
    });
    state.getDataListURL = "/shop/shop/search";
  }

  refreshFn();
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item?.price) total += item?.price || 0;
    });
  } else if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item?.acquisitionPrice) total += item?.acquisitionPrice || 0;
    });
  } else if (SummariesParams.value == 3) {
    state.dataList.map((item: any) => {
      if (item?.transactionPrice) total += item?.transactionPrice || 0;
    });
  } else if (SummariesParams.value == 4) {
    state.dataList.map((item: any) => {
      if (item?.grossProfitPrice) total += item?.grossProfitPrice || 0;
    });
  }
  return total.toFixed(2);
};

// 查看日志
const toLog = (row: any) => {
  nextTick(() => {
    logRef.value.init(row.id);
  });
};

// 商品上架
const onListingAction = (row: any, status = 2) => {
  let params = {
    id: row.id,
    release: currentShop.value.release || false,
    partnerIds: currentShop.value.partnerIds,
    status: status
  };
  baseService.post("/shop/shop/status", params).then((res) => {
    ElMessage.success("上架成功");
    state.getDataList();
    state.query();
    shopTotalPrice();
    if (shopStatRef.value) {
      shopStatRef.value.getAll(view.dataForm.gameId);
    }
  });
};

// 问题账号作废
const doneForm = reactive({
  visible: false,
  remark: undefined,
  shop: <any>{}
});
const cancellationPre = (row: any) => {
  doneForm.visible = true;
  doneForm.shop = JSON.parse(JSON.stringify(row));
  doneForm.remark = doneForm.shop.remark;
};
const cancellation = () => {
  baseService.post("/shop/shop/cancellation", { id: doneForm.shop.id, remark: doneForm.remark }).then((res) => {
    ElMessage.success("作废成功");
    doneForm.visible = false;
    doneForm.shop = {};
    doneForm.remark = undefined;
    state.getDataList();
  });
};

// 商品下架
const delistClick = (row: any, sold?: string) => {
  nextTick(() => {
    delistRef.value.init(row.id, sold, row);
  });
};
const delistClickMore = (arr: any) => {
  let ids = arr.map((ele) => ele.id);
  nextTick(() => {
    delistRef.value.initMore(ids);
  });
};
const markSalesRef = ref();
// 标记售后
const markSaleshandle = (row: any) => {
  nextTick(() => {
    markSalesRef.value.init(row);
  });
};
// 库存预警设置
const stockwarnRef = ref();
const setStockWarn = (row?: any) => {
  nextTick(() => {
    stockwarnRef.value.init(row);
  });
};
// 打开估价网站
const openLink = () => {
  const gameId = view.dataForm.gameId ? view.dataForm.gameId : gamesList.value[1].id;
  window.open(`${settingStore.info.websiteUrl}recycleView?gameId=${gameId}&ny=${generateUUID()}`, "_blank");
};

// 自动降价
const shopJob = () => {
  nextTick(() => {
    jobRef.value.init();
  });
};

// 点击标题查看详情
const toDetails = (row: any) => {
  if (!row.log) {
    ElMessage.warning("数据生成中");
    return;
  }
  nextTick(() => {
    infoRef.value.init(row);
  });
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  view.dataForm.orderBy = [
    {
      field: "",
      isAsc: false
    }
  ];
  if (order == "ascending") {
    view.dataForm.orderBy[0].field = prop;
    view.dataForm.orderBy[0].isAsc = true;
  }
  if (order == "descending") {
    view.dataForm.orderBy[0].field = prop;
    view.dataForm.orderBy[0].isAsc = false;
  }
  if (order == null) {
    delete view.dataForm.orderBy;
  }
  state.getDataList();
};

// 查询
const queryFn = () => {
  view.dataForm.start = timeInterval.value ? timeInterval.value[0] + " 00:00:00" : "";
  view.dataForm.end = timeInterval.value ? timeInterval.value[1] + " 23:59:59" : "";
  refreshFn();
};
// 重置
const resetFn = () => {
  view.dataForm.search = "";
  view.dataForm.start = "";
  view.dataForm.end = "";
  view.dataForm.code = "";
  view.dataForm.title = "";
  view.dataForm.remark = "";
  view.dataForm.gameAccount = "";
  view.dataForm.accountSource = "";
  view.dataForm.creator = "";
  view.dataForm.acquisitionUser = "";
  view.dataForm.channelId = "";
  view.dataForm.ownCoding = "";
  view.dataForm.emergencyPhone = "";
  timeInterval.value = "";
  selectedOptions.value = [];
  refreshFn();
};

// 刷新页面
const refreshFn = () => {
  shopTotalPrice();
  state.getDataList();
};
// 刷新顶部
const refreshTotal = (type?: string) => {
  if (type == "update") return state.query();
  refreshFn();
  if (shopStatRef.value) {
    shopStatRef.value.getAll(view.dataForm.gameId);
  }
};
// 获取出售渠道
const channelPage = () => {
  baseService.get("/channel/channel/page", { limit: 9999, channelTypes: "0,3" }).then((res) => {
    channelList.value = res.data.list;
  });
};

// 根据ID获取出售渠道名称
const channelIdByName = (id: any) => {
  if (!id) {
    return "平台支付";
  }
  let obj: any = channelList.value.find((item: any) => item.id == id);
  return obj?.title || "-";
};

// 员工列表
const memberList = ref([]);
const getMemberList = () => {
  baseService.get("/shop/shop/getAllCreator").then((res) => {
    memberList.value = res.data.filter((item) => item.name);
  });
};

// 员工列表
const employeeList = ref([]);
const getEmployeeList = () => {
  baseService.get("/sys/user/page", { page: 1, limit: 999 }).then((res) => {
    employeeList.value = res.data.list;
  });
};

// 上架
const currentShop = ref(<any>{});
const releasedCommand = (e: number, row: any) => {
  currentShop.value = row;
  // 是黑号就提示二次确认
  baseService
    .post("/shop/shop/checkAccountIsBlack", {
      id: row.id,
      status: 2
    })
    .then((res) => {
      if (e == 1) {
        // 正常上架
        currentShop.value.release = false;
        onListingAction(currentShop.value, 2);
      } else {
        // 推送上架
        selectPartnersHandle();
      }
    })
    .catch(() => {
      ElMessageBox.confirm("确认上架黑号?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (e == 1) {
          // 正常上架
          currentShop.value.release = false;
          onListingAction(currentShop.value, 2);
        } else {
          // 推送上架
          selectPartnersHandle();
        }
      });
    });
};

// 显示选择合作商
const selectPartnersRef = ref();
const selectPartnersKey = ref(0);
const selectPartnersHandle = async () => {
  selectPartnersKey.value++;
  await nextTick();
  selectPartnersRef.value.init();
};

// 推送上架
const pushSubmit = (ids: any) => {
  currentShop.value.release = true;
  currentShop.value.partnerIds = ids;
  onListingAction(currentShop.value, 2);
};

const visibleFilter = ref(false); // 高级筛选

// 索引同步
const shopInitLoading = ref(false);
const getShopInit = () => {
  shopInitLoading.value = true;
  baseService.post("/shop/shop/init/es").finally(() => {
    shopInitLoading.value = false;
  });
};

// 获取出售渠道
const ChannelTreeList = ref(<any>[]);
const getChannelTree = () => {
  baseService.get("/channel/channel/getChannelTree", { type: "0" }).then((res) => {
    ChannelTreeList.value = res.data;
  });
};
const selectedOptions = ref([]);
const handleChange = (selectedData: any) => {
  if (selectedData && selectedData.length > 0) {
    state.dataForm.channelId = selectedData[selectedData.length - 1];
  } else {
    state.dataForm.channelId = "";
  }
};

onMounted(() => {
  if (!store.state.user.overseerPermissions && store.state.user.id != 1067246875800000001) {
    defaultcolumns.value = defaultcolumnsOverseer.value;
  }
  columns.value = [...defaultcolumns.value, ...doneColumns];
  getGamesList();
  channelPage();
  getMemberList();
  getEmployeeList();
  getServeList();
  getChannelTree();
});
</script>

<style lang="less" scoped>
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;

  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.filter {
  .el-input {
    flex: 1;
    margin-left: 12px;
  }
  .el-select {
    width: 200px;
    margin-left: 12px;
  }
}

:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;

  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;

      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }

      .el-descriptions__content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
        min-height: 48px;
        flex: 1;

        > div {
          // width: 100%;
        }

        .el-form-item__label {
          display: none;
        }

        .el-form-item {
          margin-bottom: 0;
        }
      }

      .noneSelfRight {
        border-right: 0 !important;
      }

      .noneSelfLeft {
        border-left: 0 !important;
      }

      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
.nyDropdownMenu {
  :deep(.el-dropdown-menu__item) {
    margin: 0px 4px;
    border-radius: 4px;
  }
}
</style>
