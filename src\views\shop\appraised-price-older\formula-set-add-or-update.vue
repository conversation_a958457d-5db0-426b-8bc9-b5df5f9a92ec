<!-- 公式设定新增 -->
<template>
  <el-drawer
    v-model="visible"
    :title="dataForm.id ? '修改' : '新增'"
    :close-on-click-moformuladal="false"
    :close-on-press-escape="false"
    
    size="40%"
  >
    <el-row class="formula-add-wrap">
      <el-col :span="12">
        <el-form
          :model="dataForm"
          :rules="rules"
          ref="dataFormRef"
          label-width="120px"
          label-position="top"
          class="left-form pr-12"
        >
          <el-form-item label="游戏名称" prop="gameId">
            <el-select v-model="dataForm.gameId" @change="getPropertyList"> 
              <el-option v-for="(item, index) in gameList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统区服">
            <el-select v-model="dataForm.gameAreaId"> 
              <el-option v-for="(item, index) in gameAreaList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="条件">
            <template #label>
              <span>
                <span>设置</span>
                <el-tooltip effect="dark" placement="top-start">
                  <template #content>
                    <!-- <div>
                      请根据所选游戏的属性，列举公式。<br />属性比较需要加英文状态括号()<br />判断属性后填写的数量
                      用 < > = 例如 (皮肤数量>20) (英雄数量<20)<br />判断属性是否存在
                      将属性用()括起来即可 例如 (小国标敖隐)<br />
                      示例 (皮肤数量>20)(英雄数量<20)(小国标敖隐) 解释
                      皮肤数量大于20并且英雄数量小于20并且有小国标敖隐皮肤 条件成立
                    </div> -->
                    <div>
                      请根据所选游戏的属性，设置价格<br />
                      属性之间用回车换行隔开，判断属性后填写的数量用 < > >= <= <br />
                      示例 <br />
                      皮肤数量>20=200<br />
                      小国标敖隐=100<br />
                      解释 <br />
                      皮肤数量大于20的价格等于200
                      小国标敖隐的价格等于100
                    </div>
                  </template>
                  <el-icon size="20" class="ml-5 tip text-primary"><Warning /></el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-input
              v-model="dataForm.settingBos"
              placeholder="请输入设置"
              type="textarea"
              :rows="5"
              @blur="(e: any) => { blurEvent(e, 1) }"
            />
          </el-form-item>
          <el-form-item label="公式" prop="formulaContent">
            <template #label>
              <span>
                <span>公式</span>
                <el-tooltip effect="dark" placement="top-start">
                  <template #content>
                    <!-- <div>
                      请根据所选游戏的属性，列举公式。<br />属性运算需要加英文状态括号()<br />根据填写数字计算用
                      属性*价格 例如(皮肤数量*2)<br />单独设置属性价值需要在属性后面写 =
                      价值 例如 (小国标敖隐=50)<br />示例
                      0.8*((小国标敖隐=50)+(皮肤数量*2)+(妄想食味=20)) 解释 小国标敖隐
                      单价50 妄想食味 单价20 每个皮肤 单价2 计算出的价格相加乘0.8<br />示例
                      (皮肤数量>20) (英雄数量<20)(小国标敖隐) 解释
                      皮肤数量大于20并且英雄数量小于20并且有小国标敖隐皮肤 条件成立
                    </div> -->
                    <div>
                      请根据所选游戏的属性，列举公式。<br />
                      属性运算需要加英文状态括号()<br />
                      示例<br />
                      0.8*(皮肤数量+小国标敖隐)<br />
                      解释<br />
                      根据设置的价格 皮肤数量价格 小国标敖隐价格相加乘0.8
                    </div>
                  </template>
                  <el-icon size="20" class="ml-5 tip text-primary"><Warning /></el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-input
              v-model="dataForm.formulaContent"
              placeholder="请输入公式"
              type="textarea"
              :rows="5"
              @blur="(e: any) => { blurEvent(e, 2) }"
            />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12" class="right">
        <div class="attribute-con">
          <div class="attribute-title">当前游戏属性</div>
          <el-input
            placeholder="搜索游戏属性"
            v-model="attrKeywords"
            @change="attrKeywordsChange()"
          >
            <template #prefix>
              <el-icon class="el-input__icon">
                <search />
              </el-icon>
            </template>
          </el-input>
          <div class="attribute-con-div" v-loading="propertyListLoading">
            <el-empty v-if="!propertyList || !propertyList.length"</el-empty>
            <div class="attribute-list">
              <el-scrollbar height="365px">
                <dl
                  v-for="(item, index) in propertyList"
                  :key="index"
                  class="property-list"
                >
                  <dt>
                    <div style="width: 18px" @click="item.fold = !item.fold">
                      <el-icon v-if="item.fold"><CaretBottom /></el-icon>
                      <el-icon v-else><CaretRight /></el-icon>
                    </div>
                    <p @click="getAttr(item.id, item.name, 1)">
                      {{ item.name }}
                    </p>
                  </dt>
                  <dd :style="{ height: item.fold ? 'auto' : '0' }">
                    <div
                      class="attributeTextList"
                      :class="item.fold ? 'isshot' : 'ishigh'"
                    >
                      <p
                        v-for="(item2, index2) in item[
                          item.children && item.children.length ? 'children' : 'listAD'
                        ]"
                        :key="index2"
                        @click="getAttr(item2.gadId, item2.name, 2)"
                      >
                        {{ item2.name }}
                      </p>
                    </div>
                  </dd>
                </dl>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle">{{
        $t("confirm")
      }}</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, nextTick, defineEmits } from "vue";
import baseService from "@/service/baseService";
import { IObject } from "@/types/interface";
import { Warning } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const emits = defineEmits(['refresh']);
const visible = ref(false);

const dataForm = ref({
  id: "",
  // 游戏
  gameId: "",
  // 系统区服
  gameAreaId: "",
  // 条件
  settingBos: "",
  // 公式
  formulaContent: ""
});

const rules = ref({
  gameId: [
    { required: true, message: "请选择游戏", trigger: "change" },
  ],
  formulaContent: [
    { required: true, message: "请输入公式", trigger: "blur" },
  ]
})

const init = (data?: any) => {
  visible.value = true;
  getGamesList();

  if(data){
    dataForm.value = data;
    dataForm.value.settingBos = joinConditions(dataForm.value.settingVoList);
  }
};

const joinConditions = (data: any) => {
  let arr: IObject = [];
  data.map((item: any) => {
    arr.push(item.settingContent);
  })
  console.log(arr)
  return arr.join('\n');
}

// 游戏列表
const gameList = ref([] as IObject);
const getGamesList = async () => {
    let res = await baseService.get("/game/sysgame/listGames");
    if (res.code == 0) {
        gameList.value = res.data;

        if(dataForm.value.id) getPropertyList();
    }
}

// 区服列表
const gameAreaList = ref<any[]>([]);

// 当前游戏属性列表
const propertyList = ref<any[]>([]);
const propertyListCopy = ref<any[]>([]);
const propertyListLoading = ref(false);

const getPropertyList = async () => {
  propertyListLoading.value = true;
  let index = gameList.value.findIndex((item:any) => item.id == dataForm.value.gameId);
  gameAreaList.value = gameList.value[index].areaList;
    let res = await baseService.get("/game/attribute/listTree/" + dataForm.value.gameId);
    propertyListLoading.value = false;
    if (res.code == 0) {
        propertyList.value = res.data;
        propertyListCopy.value = JSON.parse(JSON.stringify(res.data));
    }
}

// 选择游戏属性
const getAttr = (id: any, name: any, type: any) => {
  if (blurInputIndex.value == 1) {
    //条件
    let index = inputSelectionStart.value;
    let str = dataForm.value.settingBos;
    dataForm.value.settingBos = str.slice(0, index) + name + str.slice(index);
    // getSetJsons1();
  } else if (blurInputIndex.value == 2) {
    let index = inputSelectionStart.value;
    let str = dataForm.value.formulaContent;
    dataForm.value.formulaContent = str.slice(0, index) + name + str.slice(index);
    // getSetJsons2();
  }
};

// 搜索属性
const attrKeywords = ref("");
const attrKeywordsChange = () => {
  if(!attrKeywords.value) return propertyList.value = JSON.parse(JSON.stringify(propertyListCopy.value));
  propertyList.value = deepFilter(JSON.parse(JSON.stringify(propertyListCopy.value)));
}
const deepFilter = (list: any) => {
  return list.filter(item => {

    item.children = deepFilter(item.children)

    return item.name.indexOf(attrKeywords.value) > -1 || item.children.length
  })
};

// 光标-条件
const blurInputIndex = ref(1);
const inputSelectionStart = ref(0);
const blurEvent = (event: IObject, index: number) => {
  blurInputIndex.value = index;
  inputSelectionStart.value = event.srcElement.selectionStart;
};

// 提交
const btnLoading = ref(false);
const dataFormRef = ref();
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let data = JSON.parse(JSON.stringify(dataForm.value))
      const params: IObject = {
        id: data.id,
        gameId: data.gameId,
        gameAreaId: data.gameAreaId,
        formulaContent: data.formulaContent,
        settingBos: data.settingBos.split(/[(\r\n)\r\n]+/)
      };
      
      if (params.gameAreaId) {
        params.gameAreaName = gameAreaList.value.find((ele: any) => {
          return ele.id == data.gameAreaId;
        }).title
       }
       
      let selectedGame = gameList.value.find((ele: any) => {
        return ele.id == dataForm.value.gameId;
      });
      params.gameCode = selectedGame.gcode;
      params.gameName = selectedGame.title;
      btnLoading.value = true;
      baseService[params.id ? 'put' : 'post']("/appraise/formula", params)
        .then((res: any) => {
          ElMessage.success('操作成功');
          visible.value = false;
          emits('refresh');
        }).finally(() => {
          btnLoading.value = false;
        })
    }
  })
};

defineExpose({
  init,
});
</script>

<style lang="less" scoped>
.formula-add-wrap {
  .tip {
    vertical-align: middle;
    margin-top: -4px;
  }
  .left-form {
    .el-form-item:last-child {
      margin-bottom: 0;
    }
  }
  .right {
    border-left: 1px solid #e4e7ed;
    padding-left: 12px;
  }
  .attribute-con {
    height: 100%;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-sizing: border-box;
    overflow: hidden;

    .attribute-title {
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      padding-bottom: 10px;
    }

    .attribute-con-div {
      padding: 0 10px;
      .attribute-list {
        flex: 1;
        width: 100%;
        padding: 6px 0;
        margin: 0 auto;
        box-sizing: border-box;

        .property-list {
          width: 100%;
          padding: 0 5px;
          margin-bottom: 12px;
          box-sizing: border-box;
          p {
            padding: 0;
            margin: 0;
            color: #303133;
            font-size: 14px;
          }

          dt {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 0;
          }

          dd {
            margin-top: 5px;
            overflow: hidden;
            padding: 0;
            margin: 0;

            .attributeTextList {
              padding: 0 20px;

              p {
                margin: 2px 0;
                font-size: 14px;
                line-height: 2.2;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
