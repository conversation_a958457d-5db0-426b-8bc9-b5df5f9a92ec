<template>
  <div style="width: 100%">
    <div class="title">任务管理</div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <div>
          <el-button type="info" @click="exportHandle()">导出</el-button>
        </div>
      </template>
      <template #header-right>
        <div style="display: flex; gap: 8px; height: 32px">
          <el-input :prefix-icon="Search" style=" height: 32px" v-model="state.dataForm.phone" placeholder="请输入手机号码" clearable></el-input>
          <el-select style="width: 144px" v-model="state.dataForm.type" placeholder="请选择任务类型" clearable>
            <el-option label="绑定数量查询" value="1" />
            <el-option label="自动查话费" value="2" />
            <el-option label="自动互拨电话" value="3" />
          </el-select>
          <el-select style="width: 144px" v-model="state.dataForm.status" placeholder="状态" clearable>
            <el-option label="失败" value="0"></el-option>
            <el-option label="成功" value="1"></el-option>
          </el-select>
          <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </template>
      <template #type="{ row }">
        <span v-if="row.type == 1">绑定数量查询</span>
        <span v-if="row.type == 2">自动查话费</span>
        <span v-if="row.type == 3">自动互拨电话</span>
      </template>
      <template #status="{ row }">
        <el-tag type="danger" v-if="row.status == 0">失败</el-tag>
        <el-tag type="warning" v-if="row.status == 1">成功</el-tag>
      </template>
      <template #startTime="{ row }">
        <span>{{ row.startTime ? formatDate(row.startTime, undefined) : "-" }}</span>
      </template>
      <template #receiveTime="{ row }">
        <span>{{ row.receiveTime ? formatDate(row.receiveTime, undefined) : "-" }}</span>
      </template>
      <template #successTime="{ row }">
        <span>{{ row.successTime ? formatDate(row.successTime, undefined) : "-" }}</span>
      </template>
      <template #createDate="{ row }">
        <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
      </template>
    </ny-table>
    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
  </div>
</template>
  
<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import filterCom from "./filter.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { fileExport } from "@/utils/utils";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileTaskLog/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileTaskLog",
  deleteIsBatch: true,
  dataForm: {
    phone: "",
    type: "",
    status: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    prop: "type",
    label: "任务类型",
    minWidth: 140
  },
  {
    prop: "phone",
    label: "手机号码",
    minWidth: 140
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 140
  },
  {
    prop: "startTime",
    label: "开始时间",
    minWidth: 180
  },
  {
    prop: "receiveTime",
    label: "接收时间",
    minWidth: 180
  },
  {
    prop: "successTime",
    label: "完成时间",
    minWidth: 180
  },
  {
    prop: "endTime",
    label: "结束信息",
    minWidth: 180
  },
  {
    prop: "repeatTimes",
    label: "重试次数",
    minWidth: 140
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 180
  }
]);

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm.phone = "";
  state.dataForm.status = "";
  state.dataForm.type = "";
  createDate.value = [];
  state.page = 1;
  state.getDataList();
};

// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/mobile/mobileTaskLog/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `任务管理`);
  });
};

const getParamsInfo = (params: any) => {
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(5);
  });
};
</script>
  
<style lang='less' scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 32px;
  margin-bottom: 12px;
}

.el-button {
  margin-left: 0px;
}
</style>