<template>
  <div class="basicInfoSty cardDescriptions">
    <div class="titleSty">基本信息</div>
    <el-descriptions :column="2" border class="descriptions">
      <el-descriptions-item>
        <template #label
          ><span>费用名称<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="费用名称" prop="name">
          <span v-if="checkInfo">
            {{ dataForm.name }}
          </span>
          <el-input v-else v-model="dataForm.name" placeholder="请输入"></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label
          ><span>支出金额(元)<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="支出金额" prop="amount">
          <span v-if="checkInfo">
            {{ dataForm.amount }}
          </span>
          <el-input v-else v-model="dataForm.amount" placeholder="请输入"></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label
          ><span>支付方式<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="支付方式" prop="payType">
          <span v-if="checkInfo">
            {{ ["支付宝转账", "微信", "银行卡"][dataForm.payType - 1] }}
          </span>
          <el-select v-else v-model="dataForm.payType" clearable placeholder="请选择支付方式">
            <el-option v-for="(item, index) in ['支付宝转账', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label><span>关联商品</span></template>
        <el-form-item label="关联商品" prop="shopCode">
          <span v-if="checkInfo">
            {{ dataForm.shopCode }}
          </span>
        </el-form-item>
      </el-descriptions-item>
      <template v-if="dataForm.lendingAlipayNo">
        <el-descriptions-item class-name="noneSelfRight">
          <template #label
            ><span>支付宝订单号<span v-if="!checkInfo" style="color: red">*</span></span></template
          >
          <el-form-item label="支付宝订单号" prop="lendingAlipayNo">
            <span v-if="checkInfo">
              {{ dataForm.lendingAlipayNo }}
            </span>
            <el-input v-else v-model="dataForm.lendingAlipayNo" placeholder="请输入支付宝订单号"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
      </template>
      <el-descriptions-item :span="2">
        <template #label><span>备注</span></template>
        <el-form-item label="备注" prop="remark">
          <div v-if="checkInfo">
            {{ dataForm.remark || "-" }}
          </div>
          <el-input v-else type="textarea" v-model="dataForm.remark" placeholder="请输入备注" :rows="5"></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label><span>开销凭证</span></template>
        <el-form-item label="开销凭证">
          <div v-if="checkInfo">
            <span v-if="!dataForm.voucher || dataForm.voucher.length < 1">-</span>
            <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
              <el-image style="width: 100px" :src="item" v-for="item in dataForm.voucher.split(',')" alt="" :preview-src-list="dataForm.voucher.split(',')" />
            </div>
          </div>
          <ny-upload v-else v-model:imageUrl="dataForm.voucher" :limit="3" :fileSize="5" accept="image/*"></ny-upload>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label><span>发票</span></template>
        <el-form-item label="发票">
          <div v-if="checkInfo">
            <span v-if="!dataForm.invoice || dataForm.invoice.length < 1">-</span>
            <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px" v-else>
              <el-image style="width: 100px" :src="item" v-for="item in dataForm.invoice.split(',')" alt="" :preview-src-list="dataForm.invoice.split(',')" />
            </div>
          </div>
          <ny-upload v-else v-model:imageUrl="dataForm.invoice" :limit="3" :fileSize="5" accept="image/*"></ny-upload>
        </el-form-item>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div class="basicInfoSty cardDescriptions">
    <div class="titleSty">收款信息</div>
    <el-descriptions :column="2" border class="descriptions">
      <el-descriptions-item>
        <template #label
          ><span>账户类型<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="账户类型" prop="accountType">
          <span v-if="checkInfo">
            {{ ["支付宝", "微信", "银行卡"][dataForm.accountType - 1] }}
          </span>
          <el-select v-else v-model="dataForm.accountType" clearable placeholder="请选择账户类型">
            <el-option v-for="(item, index) in ['支付宝', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label
          ><span>收款账号<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="收款账号" prop="account">
          <span v-if="checkInfo">
            {{ dataForm.account || "-" }}
          </span>
          <el-input v-else v-model="dataForm.account" placeholder="请输入收款账号"></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label
          ><span>{{ dataForm.accountType != "3" ? "收款账户" : "姓名" }}<span v-if="!checkInfo" style="color: red">*</span></span></template
        >
        <el-form-item label="姓名" prop="accountName">
          <span v-if="checkInfo">
            {{ dataForm.accountName || "-" }}
          </span>
          <el-input v-else v-model="dataForm.accountName" placeholder="请输入姓名"></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item v-if="dataForm.accountType == '3'">
        <template #label><span>开户银行</span></template>
        <el-form-item label="开户银行" prop="bankName">
          <span v-if="checkInfo"> {{ dataForm.bankName || "-" }} </span>
          <el-input v-else v-model="dataForm.bankName" placeholder="请输入开户银行"></el-input>
        </el-form-item>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 12px; display: flex; justify-content: flex-end" v-if="props.orderInfo.autoReconciliationStatus">
      <el-tag :type="dataForm.autoReconciliationStatus == 1 ? 'success' : 'danger'" size="large">
        <div style="display: flex; align-items: center; gap: 9px">
          <el-icon v-if="dataForm.autoReconciliationStatus == 1"><CircleCheckFilled /></el-icon>
          <el-icon v-else><CircleCloseFilled /></el-icon>
          <span style="font-weight: 500; font-size: 14px">{{ dataForm.autoReconciliationStatus == 1 ? "对账成功" : "对账异常" }}</span>
        </div>
      </el-tag>
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted, watch } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
const dataForm = ref({} as any);
const checkInfo = ref(true);
interface Props {
  orderInfo: any;
}
const props = withDefaults(defineProps<Props>(), {
  orderInfo: ""
});
watch(
  () => props.orderInfo,
  () => {
    dataForm.value = props.orderInfo;
    // baseService.get("/automaticReconciliation/othercost/" + props.orderInfo.orderCode).then((res) => {
    //   Object.assign(dataForm.value, res.data);
    // });
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<style lang="less" scoped>
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
}
</style>
