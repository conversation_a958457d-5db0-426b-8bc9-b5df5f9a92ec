<template>
    <operation-log 
        ref="operationRef" 
        :show="show" 
        @close="show=false" 
        title="订单删除确认" 
        confirmText="确认删除"
        type="danger"
        :loading="btnLoading"
        @confirm="confirm">   
        <template #default>
            <div class="text">
                <p>您即将删除订单：{{ orderId.join(',') }}</p>
                <p>删除订单与该订单相关的所有信息将无法恢复，是否确认删除？</p>
            </div>
        </template>
    </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose } from "vue";
import OperationLog from "./SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refresh"]);
const show = ref(false);
const orderId = ref();
const currentType = ref('');
const init = (ids: any, type: string) => {
    orderId.value = ids;
    currentType.value = type;
    show.value = true;
}

// 确认删除
const btnLoading = ref(false);
const confirm = () => {
    btnLoading.value = true;
    let requestApi = currentType.value == 'afterSales' ? '/saleAfter/delete' : currentType.value == 'acquisition' ? '/purchase/delete' : '/sale/delete'
    baseService.delete(requestApi, orderId.value).then(res => {
        if (res.code == 0) {
            ElMessage.success("删除成功");
            show.value = false;
            emit("refresh");
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}
defineExpose({
    init
})

</script>