<template>
  <div class="mod-finance__payable-detail">
    <el-card shadow="never" class="rr-view-ctx-card cardTop ny_form_card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.wljsdw" placeholder="请输入往来单位" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.djlx" placeholder="请选择单据类型" clearable>
                <el-option label="账号出售" value="账号出售"></el-option>
                <el-option label="账号收购" value="账号收购"></el-option>
                <el-option label="其他" value="其他"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.djbh" placeholder="请输入单据编号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.yxbh" placeholder="请输入游戏编号" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="state.dataForm.yxmc" placeholder="请输入游戏名称" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.szbm" placeholder="请选择部门" clearable>
                <el-option label="久云组" value="久云组"></el-option>
                <el-option label="技术部" value="技术部"></el-option>
                <el-option label="运营部" value="运营部"></el-option>
                <el-option label="财务部" value="财务部"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
                clearable
                style="width: 240px">
              </el-date-picker>
            </el-col>
            <el-col :span="6">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <ny-table
        :state="state"
        :columns="columns"
        @pageSizeChange="state.pageSizeChangeHandle"
        @pageCurrentChange="state.pageCurrentChangeHandle"
        @selectionChange="state.dataListSelectionChangeHandle">

        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <!-- 自定义列内容 -->
        <template #djlx="scope">
          <el-tag :type="getDocumentTypeTagType(scope.row.djlx)" size="small">
            {{ scope.row.djlx }}
          </el-tag>
        </template>

        <template #ywlx="scope">
          <el-tag :type="getBusinessTypeTagType(scope.row.ywlx)" size="small">
            {{ scope.row.ywlx }}
          </el-tag>
        </template>

        <template #djrq="scope">
          <span>{{ formatTimeStamp(scope.row.djrq, 'YYYY-MM-DD') }}</span>
        </template>

        <template #bqys="scope">
          <span class="amount-text">{{ formatCurrency(scope.row.bqys) }}</span>
        </template>

        <template #bqys01="scope">
          <span class="amount-text paid">{{ formatCurrency(scope.row.bqys01) }}</span>
        </template>

        <template #bqye="scope">
          <span class="amount-text unpaid">{{ formatCurrency(scope.row.bqye) }}</span>
        </template>

        <template #cjsj="scope">
          <span>{{ formatTimeStamp(scope.row.cjsj) }}</span>
        </template>

        <template #xgsj="scope">
          <span>{{ formatTimeStamp(scope.row.xgsj) }}</span>
        </template>

        <template #operation="scope">
          <el-button type="info" link size="small" @click="addOrUpdateHandle(scope.row.id, true)">详情</el-button>
          <el-button type="warning" link size="small" @click="paymentRecordHandle(scope.row)">付款</el-button>
          <el-button v-if="state.hasPermission('finance:payable:update')" type="primary" link size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
        </template>
      </ny-table>

      <el-pagination
        :current-page="state.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="state.limit"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="state.pageSizeChangeHandle"
        @current-change="state.pageCurrentChangeHandle">
      </el-pagination>
    </el-card>

    <!-- 弹窗, 新增 / 修改 / 详情 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./payable-detail-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import type { FormInstance } from "element-plus";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "wljsdw",
    label: "往来结算单位",
    width: 150
  },
  {
    prop: "djrq",
    label: "单据日期",
    width: 120
  },
  {
    prop: "djlx",
    label: "单据类型",
    width: 100
  },
  {
    prop: "djbh",
    label: "单据编号",
    width: 150
  },
  {
    prop: "ywlx",
    label: "业务类型",
    width: 100
  },
  {
    prop: "bqys",
    label: "本期应收",
    width: 120
  },
  {
    prop: "bqys01",
    label: "本期已收",
    width: 120
  },
  {
    prop: "bqye",
    label: "期末余额",
    width: 120
  },
  {
    prop: "ywy",
    label: "业务员",
    width: 120
  },
  {
    prop: "szbm",
    label: "所在部门",
    width: 100
  },
  {
    prop: "yxmc",
    label: "游戏名称",
    width: 120
  },
  {
    prop: "yxbh",
    label: "游戏编号",
    width: 120
  },
  {
    prop: "yxzh",
    label: "游戏账号",
    width: 120
  },
  {
    prop: "cjsj",
    label: "创建时间",
    width: 160
  },
  {
    prop: "xgsj",
    label: "修改时间",
    width: 160
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/finance/payable/page",
  getDataListIsPage: true,
  exportURL: "/finance/payable/export",
  deleteURL: "/finance/payable",
  deleteIsBatch: true,
  dataForm: {
    wljsdw: "",
    djlx: "",
    djbh: "",
    ywlx: "",
    szbm: "",
    yxmc: "",
    yxbh: "",
    yxzh: "",
    startCreateDate: "",
    endCreateDate: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();

const addOrUpdateHandle = (id?: number, isView?: boolean) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, isView);
  });
};

// 时间选择变化
const handleDateChange = () => {
  if (createTimeRange.value) {
    state.dataForm.startCreateDate = createTimeRange.value[0] + " 00:00:00";
    state.dataForm.endCreateDate = createTimeRange.value[1] + " 23:59:59";
  } else {
    state.dataForm.startCreateDate = "";
    state.dataForm.endCreateDate = "";
  }
};

// 获取单据类型标签类型
const getDocumentTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    "账号出售": "primary",
    "账号收购": "success",
    "其他": "warning"
  };
  return typeMap[type] || "info";
};

// 获取业务类型标签类型
const getBusinessTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    "应付款": "danger",
    "应收款": "success",
    "其他": "warning"
  };
  return typeMap[type] || "info";
};

// 格式化金额
const formatCurrency = (amount: number | string) => {
  if (!amount) return "0.00";
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return num.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 付款记录
const paymentRecordHandle = (row: IObject) => {
  const routeParams = {
    path: `/finance/payment-record`,
    query: {
      payableId: row.id,
      settlementUnit: row.wljsdw,
      _mt: `付款记录 - ${row.wljsdw}`
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 重置操作
const getResetting = () => {
  createTimeRange.value = undefined;
  state.dataForm.wljsdw = "";
  state.dataForm.djlx = "";
  state.dataForm.djbh = "";
  state.dataForm.ywlx = "";
  state.dataForm.szbm = "";
  state.dataForm.yxmc = "";
  state.dataForm.yxbh = "";
  state.dataForm.yxzh = "";
  state.dataForm.startCreateDate = "";
  state.dataForm.endCreateDate = "";
  state.getDataList();
};

// 导出
const exportHandle = () => {
  baseService.get("/finance/payable/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "应付明细账列表");
    }
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.cardTop {
  margin-bottom: 20px;
}

.amount-text {
  color: #e6a23c;
  font-weight: 500;
  
  &.paid {
    color: #67c23a;
  }
  
  &.unpaid {
    color: #f56c6c;
  }
}
</style>



