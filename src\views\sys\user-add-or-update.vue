<template>
  <el-drawer v-model="visible" size="50%" class="drawer_shop">
    <template #header>
      <div class="drawer_title">{{ !dataForm.id ? $t("add") : $t("update") }}</div>
    </template>
    <div class="shop_page">
      <div class="shop_page_card">
        <ny-title title="基本信息" style="padding: 0px 0px 12px 0px" />
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="username" :label="$t('user.username')">
                <el-input v-model="dataForm.username" :placeholder="$t('user.username')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="nickname" label="昵称">
                <el-input v-model="dataForm.nickname" placeholder="昵称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="deptName" :label="$t('user.deptName')">
                <ny-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="realName" :label="$t('user.realName')">
                <el-input v-model="dataForm.realName" :placeholder="$t('user.realName')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="mobile" :label="$t('user.mobile')">
                <el-input v-model="dataForm.mobile" :placeholder="$t('user.mobile')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="password" :label="$t('user.password')" :class="{ 'is-required': !dataForm.id }">
                <el-input v-model="dataForm.password" type="password" show-password autocomplete="new-password" :placeholder="$t('user.password')" clearable></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item prop="confirmPassword" :label="$t('user.confirmPassword')" :class="{ 'is-required': !dataForm.id }">
                <el-input v-model="dataForm.confirmPassword" type="password" :placeholder="$t('user.confirmPassword')" clearable></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item prop="email" :label="$t('user.email')">
                <el-input v-model="dataForm.email" :placeholder="$t('user.email')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="roleIdList" :label="$t('user.roleIdList')" class="role-list">
                <el-select v-model="dataForm.roleIdList" multiple :placeholder="$t('user.roleIdList')">
                  <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="postIdList" :label="$t('user.postIdList')" class="role-list">
                <el-select v-model="dataForm.postIdList" multiple :placeholder="$t('user.postIdList')">
                  <el-option v-for="post in postList" :key="post.id" :label="post.postName" :value="post.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="gender" :label="$t('user.gender')">
                <ny-radio-group v-model="dataForm.gender" dict-type="gender"></ny-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="status" :label="$t('user.status')">
                <el-radio-group v-model="dataForm.status">
                  <el-radio :value="0" border>{{ $t("user.status0") }}</el-radio>
                  <el-radio :value="1" border>{{ $t("user.status1") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="dualMode" label="商城App双模式">
                <el-switch v-model="dataForm.dualMode" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="overseerPermissions" label="超管权限">
                <el-switch v-model="dataForm.overseerPermissions" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="禁用" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="headUrl" :label="$t('user.headUrl')">
                <ny-upload v-model:imageUrl="dataForm.headUrl" :limit="1" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="shop_page_card" style="margin-top: 12px">
        <div class="flx-justify-between">
          <ny-title title="游戏分配" style="padding: 0px 0px 12px 0px" />
          <el-button type="primary" plain size="small" @click="dialogVisible = true" style="margin-bottom: 12px">选择游戏</el-button>
        </div>
        <div v-if="dataForm.gameIdList.length">
          <el-descriptions :column="1" border class="descriptions descriptions-label-140">
            <el-descriptions-item>
              <template #label>已分配游戏</template>
              {{ gameIdByName(dataForm.gameIdList) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <ny-no-data type="3" description="暂未分配游戏" :image-size="80" v-else />
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-drawer>

  <!-- 分配游戏 -->
  <el-dialog v-model="dialogVisible" title="分配游戏" width="734px">
    <el-transfer
      v-model="dataForm.gameIdList"
      filterable
      filter-placeholder="搜索游戏名称"
      :data="gamesList"
      :titles="['选择游戏', '已选游戏']"
      :button-texts="['从已选取消', '添加到已选']"
      :props="{
        key: 'id',
        label: 'title'
      }"
      class="select-partner"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { isEmail, isMobile } from "@/utils/utils";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const store = useAppStore();
const visible = ref(false);
const roleList = ref<any[]>([]);
const postList = ref<any[]>([]);
const dataFormRef = ref();
// 分配游戏
const dialogVisible = ref(false);
const gamesList = ref(<any>[]); // 游戏列表
const gameIdList = ref([]);

// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/allGames").then((res) => {
    gamesList.value = res.data;
  });
};

const dataForm = reactive({
  id: "",
  username: "",
  nickname: "",
  headUrl: "",
  deptId: "",
  deptName: "",
  password: "",
  confirmPassword: "",
  realName: "",
  gender: 0,
  email: "",
  mobile: "",
  roleIdList: [] as IObject[],
  postIdList: [] as IObject[],
  status: 1,
  gameIdList: [],
  // 商城app双模式是否启用
  dualMode: 0,
  //  超管权限 0 关闭   1开启
  overseerPermissions: 0
});

const validatePassword = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (!dataForm.id && !/\S/.test(value)) {
    return callback(new Error(t("validate.required")));
  }
  callback();
};
const validateConfirmPassword = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (!dataForm.id && !/\S/.test(value)) {
    return callback(new Error(t("validate.required")));
  }
  if (dataForm.password !== value) {
    return callback(new Error(t("user.validate.confirmPassword")));
  }
  callback();
};
const validateEmail = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isEmail(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.email") })));
  }
  callback();
};
const validateMobile = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isMobile(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.mobile") })));
  }
  callback();
};
const rules = ref({
  username: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  // headUrl:[{ required: true, message: t("validate.required"), trigger: "change" }],
  deptName: [{ required: true, message: t("validate.required"), trigger: "change" }],
  password: [{ validator: validatePassword, trigger: "blur" }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: "blur" }],
  realName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  email: [{ validator: validateEmail, trigger: "blur" }],
  mobile: [
    { required: true, message: t("validate.required"), trigger: "blur" },
    { validator: validateMobile, trigger: "blur" }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  dataForm.deptId = "";
  dataForm.gameIdList = [];
  getGamesList();
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  Promise.all([getRoleList(), getPostList()]).then(() => {
    if (id) {
      getInfo(id);
    }
  });
};

// 获取角色列表
const getRoleList = () => {
  return baseService.get("/sys/role/list").then((res) => {
    roleList.value = res.data;
  });
};

// 获取岗位列表
const getPostList = () => {
  return baseService.get("/sys/post/list").then((res) => {
    postList.value = res.data;
  });
};

// 根据游戏id获取名称
const gameIdByName = (idList: any) => {
  const nameList: any = [];
  gamesList.value.map((item: any) => {
    idList.map((ele: any) => {
      if (item.id == ele) {
        nameList.push(item.title);
      }
    });
  });
  return nameList.join("、");
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/user/${id}`).then((res) => {
    res.data.gameIdList = res.data.gameIdList ? res.data.gameIdList : [];
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/sys/user", {
      ...dataForm,
      roleIdList: [...dataForm.roleIdList]
    })
      .then(() => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            // 更新本人信息
            if (store.state.user.id == dataForm.id) {
              baseService.get("/sys/user/info").then((res) => {
                store.upDateUser(res.data);
              });
            }
            visible.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 弹窗关闭回调
const close = () => {
  dataFormRef.value.resetFields();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.shop_page {
  background-color: #f0f2f5;
  padding: 12px;
}
.shop_page_card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}
.mod-sys__user {
  .role-list {
    .el-select {
      width: 100%;
    }
  }
}
</style>
<style lang="less">
.select-partner {
  display: flex;
  align-items: center;

  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    .el-button {
      display: block;
      margin: 10px 0;
    }
  }
}
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
  }
}
</style>
