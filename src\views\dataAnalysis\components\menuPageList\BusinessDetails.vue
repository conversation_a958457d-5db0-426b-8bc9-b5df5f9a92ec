<template>
    <div class="business_page">
        <div class="business_header flx-align-center">
            <el-button type="info" :loading="exportclickLoading" @click="exportclick">导出数据</el-button>
            <el-radio-group v-model="dataForm.type" style="margin-left: 24px" @change="radioChange">
                <el-radio value="1">员工</el-radio>
                <!-- <el-radio value="2">渠道</el-radio> -->
                <el-radio value="3">游戏</el-radio>
            </el-radio-group>
        </div>
        <div class="business_center">
            <el-table :data="paginatedData" style="width: 100%;" cell-class-name="ch-56" border class="business_table" @sort-change="sortChange">
                <template v-for="item in columns" :key="item">
                    <el-table-column :prop="item.prop" :label="item.label" :sortable="item.prop != 'name' ? 'custom' : false"
                        :min-width="item.minWidth" align="center">
                        <template #header v-if="item.prop == 'name'">
                            <span v-if="dataForm.type == '1'">员工</span>
                            <span v-if="dataForm.type == '2'">渠道</span>
                            <span v-if="dataForm.type == '3'">游戏</span>
                        </template>
                        <template #default="{ row, $index }" v-if="item.label == '周转率' || item.label == '毛利率'"> {{
                            row[item.prop] }}% </template>
                        <template #default="{ row, $index }" v-else>
                            {{ row[item.prop] }}
                        </template>
                    </el-table-column>
                </template>
                <!-- 空状态 -->
                <template #empty>
                    <div style="padding: 68px 0">
                        <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                    </div>
                </template>
            </el-table>
            <el-pagination :current-page="pagination.page" :page-sizes="[10, 20, 50, 100, 500, 1000]"
                :page-size="pagination.size" :total="total" layout="total, sizes, prev, pager, next, jumper"
                :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
        </div>
    </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { nextTick, onMounted, reactive, ref, toRefs, computed } from "vue";
import { fileExport } from "@/utils/utils";
import { usePagination, useSortList } from "@/views/dataAnalysis/pagination"

const tableData = ref(<any>[]);
// 表格配置项
const columns = reactive([
    {
        prop: "name",
        label: "员工",
        minWidth: 80
    },
    {
        prop: "recoveryOrderCount",
        label: "回收订单数",
        minWidth: 100
    },
    // {
    //     prop: "convertedOrderCount",
    //     label: "已转化订单数",
    //     minWidth: 100
    // },
    // {
    //     prop: "conversionRate",
    //     label: "周转率",
    //     minWidth: 100
    // },
    // {
    //     prop: "recoveryGrossProfitMargin",
    //     label: "毛利率",
    //     minWidth: 100
    // },
    {
        prop: "salesOrderCount",
        label: "销售订单数",
        minWidth: 100
    },
    {
        prop: "totalSalesAmount",
        label: "总销售额(元)",
        minWidth: 100
    },
    {
        prop: "totalProfit",
        label: "总利润(元)",
        minWidth: 100
    },
    {
        prop: "salesGrossProfitMargin",
        label: "毛利率",
        minWidth: 100
    },
    {
        prop: "customerUnitPrice",
        label: "客单价(元)",
        minWidth: 100
    }
]);

// 分页
const pagination = ref({
    page: 1,
    size: 10,
})
const total = ref();

// 计算分页数据
const paginatedData = computed(() =>
    usePagination({
        currentPage: pagination.value.page,
        pageSize: pagination.value.size,
        data: tableData.value
    })
);

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    salesChannelId: "",
    employeeId: "",
    saleEmployeeId: "",
    purchaseEmployeeId: "",
    type: "1",
    export: false
});

const radioChange = () => {
    getOperatingDetail();
};

const exportclickLoading = ref(false);
const exportclick = () => {
    dataForm.value.export = true;
    exportclickLoading.value = true;
    baseService
        .get("/dataAnalysis/operatingDetailExport", dataForm.value)
        .then((res) => {
            if (res) {
                fileExport(res, "运营效能洞察-经营明细");
            }
        })
        .finally(() => {
            exportclickLoading.value = false;
        });
};

const getOperatingDetail = () => {
    baseService.post("/dataAnalysis/operatingDetail", dataForm.value).then((res) => {
        tableData.value = res.data;
        total.value = tableData.value.length;
    });
};

// 分页方法
const sizeChange = (number: any) => {
    pagination.value.size = number;
    pagination.value.page = 1;
};

const currentChange = (number: any) => {
    pagination.value.page = number;
};

// 排序事件
const sortChange = (column: any) => {
    if(column.order != null){
        tableData.value = useSortList({
            prop: column.prop,
            order: column.order,
            data: tableData.value
        })
    }
};

onMounted(() => {
    getOperatingDetail();
});
const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getOperatingDetail();
};

defineExpose({
    init
});
</script>

<style lang="less" scoped>
.business_page {
    margin-top: 12px;
}

.business_header {}

.business_center {
    margin-top: 12px;
}

.business_table {
    :deep(th .cell) {
        background: none !important;
    }

    :deep(th:nth-child(n + 2):nth-child(-n + 2)) {
        background-color: rgba(65, 101, 215, 0.1) !important;

        .cell {
            color: #4165d7;
        }
    }

    :deep(th:nth-child(n + 3):nth-child(-n + 7)) {
        background-color: rgba(0, 180, 42, 0.1) !important;

        .cell {
            color: #00c568;
        }
    }

    :deep(td:nth-child(n + 2):nth-child(-n + 2)) {
        background-color: rgba(65, 101, 215, 0.05) !important;
    }

    :deep(td:nth-child(n + 3):nth-child(-n + 7)) {
        background-color: rgba(0, 180, 42, 0.05) !important;
    }
}
</style>
