<script lang="ts" setup>
import moveObject from "@/hooks/useDrag";
import { ref, onMounted, watch } from "vue";
import { localGet, localSet } from "@/utils/cache";
import { browserTabTitle } from "@/utils/notification";
import { getThemeConfigCache, setThemeColor, updateTheme } from "@/utils/theme";
import { EPageLayoutEnum, EThemeColor, EThemeSetting } from "@/constants/enum";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store";
import colorTheme1 from "@/assets/images/colorTheme1.gif";
import colorTheme2 from "@/assets/images/colorTheme2.gif";
import colorTheme3 from "@/assets/images/colorTheme3.gif";
import colorTheme4 from "@/assets/images/colorTheme4.gif";
import colorTheme5 from "@/assets/images/colorTheme5.gif";
import colorTheme6 from "@/assets/images/colorTheme6.gif";
import colorTheme7 from "@/assets/images/colorTheme7.gif";
import colorTheme8 from "@/assets/images/colorTheme8.gif";
import colorTheme9 from "@/assets/images/colorTheme9.gif";
import colorTheme10 from "@/assets/images/colorTheme10.gif";
import colorTheme11 from "@/assets/images/colorTheme11.gif";
import colorTheme12 from "@/assets/images/colorTheme12.gif";
import colorTheme13 from "@/assets/images/colorTheme13.gif";
import colorTheme14 from "@/assets/images/colorTheme14.gif";
import colorTheme15 from "@/assets/images/colorTheme15.gif";
import colorTheme16 from "@/assets/images/colorTheme16.gif";
import colorTheme17 from "@/assets/images/colorTheme17.gif";
const store = useAppStore();

const imStore = useImStore();

const imImage = ref('');

onMounted(() => {
  const el = document.getElementById("dragRef");
  let xyData = localGet("offsetX&Y") || { left: document.documentElement.clientWidth - el?.offsetWidth, top: 140 };
  moveObject.useMove(el, xyData, false);
  //读取主题色缓存
  const themeCache = getThemeConfigCache();
  const themeColor = themeCache[EThemeSetting.ThemeColor];
  imImage.value = themeColorByImage(themeColor);
});
const dragMethod = (e:any) => {
  e.preventDefault();
};

const jumpIm = () => {
  if(moveObject.isMove) return
  let originUrl = location.origin + '/#/im'
  browserTabTitle.clear();
  window.open(originUrl, imStore.imUid)
}

watch(
  ()=> store.state.themeColor,
  (newValue)=>{
    imImage.value = themeColorByImage(newValue);
  }
)

// 根据主题色返回相关IM图标
const themeColorByImage = (themeColor:any) =>{
  if(themeColor == '#409eff'){
    return colorTheme1
  }else if(themeColor == '#0BB2D4'){
    return colorTheme2
  }else if(themeColor == '#3E8EF7'){
    return colorTheme3
  }else if(themeColor == '#11C26D'){
    return colorTheme4
  }else if(themeColor == '#17B3A3'){
    return colorTheme5
  }else if(themeColor == '#667AFA'){
    return colorTheme6
  }else if(themeColor == '#997B71'){
    return colorTheme7
  }else if(themeColor == '#9463F7'){
    return colorTheme8
  }else if(themeColor == '#757575'){
    return colorTheme9
  }else if(themeColor == '#EB6709'){
    return colorTheme10
  }else if(themeColor == '#F74584'){
    return colorTheme11
  }else if(themeColor == '#FCB900'){
    return colorTheme12
  }else if(themeColor == '#FF4C52'){
    return colorTheme13
  }else if(themeColor == '#141414'){
    return colorTheme14
  }else if(themeColor == '#4165d7'){
    return colorTheme15
  }else if(themeColor == '#d0378d'){
    return colorTheme16
  }else if(themeColor == '#7397ab'){
    return colorTheme17
  }
}

</script>

<template>
  <div draggable="true" id="dragRef" @dragstart="dragMethod" @click="jumpIm">
    <img :src="imImage" alt="" srcset="" />
  </div>
</template>

<style lang="scss" scoped>
#dragRef {
  width: 88px;
  height: 88px;
  // background: #ffffff;
  box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
  border-radius: 12px;
  cursor: move;
  // padding: 6px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  // transition: all 0.3s;
  // right: -50px !important;

  // &:hover{
  //   right: 0 !important;
  // }
  img {
    width: 88px;
    height: 88px;
    border-radius: 12px;
  }
  font-family: OPPOSans, OPPOSans;
  font-weight: bold;
  font-size: 14px;
  color: var(--el-color-primary);
  line-height: 20px;
}
</style>
