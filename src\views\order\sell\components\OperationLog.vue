<template>
  <el-drawer v-model="visible" :title="currentTitle" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%" class="ny-drawer article-add-or-update">
    <el-card class="article-add-or-update-form" v-loading="dataLoading">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions class="descriptions-label-140" :column="1" border>
        <el-descriptions-item label="订单编号">{{ resData.sn || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏大区">{{ resData.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item v-if="!showLog" label="买方手机号">{{ resData.tbUserPhone || resData.buyerPhone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="账号价格">
          <el-text type="danger" v-if="typeof resData.saleAmount == 'number'">￥{{ resData.saleAmount }}</el-text>
          <el-text type="danger" v-else>-</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="包赔费">￥{{ resData.guaranteePrice || "-" }}</el-descriptions-item>
        <template v-if="!showLog">
          <el-descriptions-item label="创建时间">{{ resData.createDate }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ resData.payTime || "-" }}</el-descriptions-item>
          <!-- <el-descriptions-item label="回收成功时间">{{ resData.dealDate || "-" }}</el-descriptions-item> -->
          <el-descriptions-item label="回收人">{{ resData.acquisitionName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="卖方手机号">{{ resData.mobile || "-" }}</el-descriptions-item>
          <el-descriptions-item label="游戏密码">
            <div class="flx-justify-between" v-if="resData.gamePassword">
              <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </el-card>

    <el-card class="mt-12" v-if="!markerAfterSale && showLog && state.hasPermission('purchase:purchaseorder:logPage')">
      <div class="p-title mt-0">操作日志</div>
      <ny-table :state="state" :columns="columns" :pagination="false" :showColSetting="false"> </ny-table>
    </el-card>

    <el-card class="mt-12" v-if="markerAfterSale">
      <div class="p-title mt-0">标记售后</div>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <el-row :gutter="12">
          <el-col :span="12" v-if="resData.state">
            <el-form-item label="售后类型" prop="saleAfterType">
              <ny-select v-model="dataForm.saleAfterType" dict-type="after_sales_type" placeholder="请选择售后类型" @change="dataForm.subDelistingCause = ''" :filterValue="resData.state == '交易成功' ? 'CANCEL' : ''"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.saleAfterType == 'ACCOUNT_RETURN' || dataForm.saleAfterType == 'CANCEL'">
            <el-form-item label="二级原因" prop="subDelistingCause" v-if="dataForm.saleAfterType == 'ACCOUNT_RETURN'">
              <ny-select v-model="dataForm.subDelistingCause" dict-type="retrieve_type_sale" placeholder="请选择二级原因"></ny-select>
            </el-form-item>
            <el-form-item label="二级原因" prop="cancelReasonType" v-if="dataForm.saleAfterType == 'CANCEL'">
              <ny-select v-model="dataForm.cancelReasonType" dict-type="paid_cancel_order" placeholder="请选择二级原因"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.saleAfterType == 'OTHER' || (dataForm.saleAfterType == 'CANCEL' && dataForm.subDelistingCause == '0')">
            <el-form-item label="其他原因备注" prop="saleAfterRemark">
              <el-input v-model="dataForm.saleAfterRemark" placeholder="请输入其他原因备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请选择处理人" prop="userId">
              <ny-select-search v-model="dataForm.userId" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择处理人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="售后过期时间" prop="expirationTime">
              <el-date-picker popper-class="date_picke" :disabled-date="disabledDate" v-model="dataForm.expirationTime" type="datetime" placeholder="请选择售后过期时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题截图" prop="saleAfterPics">
              <ny-upload v-model:imageUrl="dataForm.saleAfterPics" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template #footer v-if="markerAfterSale">
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { formatTimeStamp } from "@/utils/method";
const emit = defineEmits(["refresh"]);

const view = reactive({
  getDataListURL: "/sale/optionalLog/",
  createdIsNeed: false,
  getDataListIsPage: true,
  dataForm: {
    orderId: "",
    type: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    prop: "optionalUserName",
    label: "操作人"
  },
  {
    prop: "optionalTitle",
    label: "操作行为"
  },
  {
    prop: "createDate",
    label: "操作时间"
  }
]);

const visible = ref(false);

// 是否订单标记售后
const markerAfterSale = ref(false);

// 是否显示日志
const showLog = ref(false);

const currentTitle = ref(<any>"");

const orderId = ref("");
const init = (data: any, marker?: boolean, title?: string) => {
  console.log(data, "====== data ======");

  visible.value = true;
  currentTitle.value = title;
  showLog.value = title == "操作日志" ? true : false;
  markerAfterSale.value = marker ? true : false;
  orderId.value = data.id;
  view.getDataListURL = view.getDataListURL + data.id;
  view.dataForm.orderId = data.id;

  dataForm.value.saleOrderId = data.id;

  resData.value = data;

  getDetails();
  state.getDataList();
};
const disabledDate = (time: any) => {
  // 禁用今天之前的日期
  return time.getTime() < new Date().getTime();
};
const resData = ref(<any>{
  orderInfo: {},
  changeInfo: {}
});
const dataLoading = ref(false);
const getDetails = () => {
  dataLoading.value = true;
  baseService
    .post("/shop/shop/search", {
      limit: 10,
      page: 1,
      queryType: "1",
      search: resData.value.gameAccount,
      size: 10
    })
    .then((res) => {
      if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
        delete res.data.list[0].id;
        resData.value = Object.assign(resData.value, res.data.list[0]);
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 是否显示游戏密码
const isShowGamePassword = ref(false);

// 标记售后
const dataForm = ref(<any>{
  userId: "",
  saleAfterPics: ""
});

const rules = reactive({
  saleAfterType: [{ required: true, message: "请选择售后类型", trigger: "change" }],
  subDelistingCause: [{ required: true, message: "请选择二级原因", trigger: "change" }],
  cancelReasonType: [{ required: true, message: "请选择二级原因", trigger: "change" }],
  expirationTime: [{ required: true, message: "请选择过期时间", trigger: "change" }],
  userId: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
  saleAfterPics: [{ required: true, message: "请上传问题截图", trigger: "change" }]
});

//
const resultoptions = ref([
  {
    label: "未售未放款被找回",
    value: "1"
  },
  {
    label: "未售已放款被找回",
    value: "2"
  },
  {
    label: "已售未放款被找回",
    value: "3"
  },
  {
    label: "已售已放款被找回",
    value: "4"
  },
  {
    label: "未售未放款疑似被找回",
    value: "5"
  },
  {
    label: "未售已放款疑似被找回",
    value: "6"
  },
  {
    label: "已售未放款疑似被找回",
    value: "7"
  },
  {
    label: "已售已放款疑似被找回",
    value: "8"
  }
]);
const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let data = JSON.parse(JSON.stringify(dataForm.value));
    data.shopId = resData.value.shopId;
    data.saleAfterPics = data.saleAfterPics.split(",");
    baseService
      .post("/saleAfter/createSaleAfterOrder", data)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          if (dataForm.value.saleAfterType == "ACCOUNT_RETURN") {
            // 账号找回加黑名单
            addBlackData();
          }
          emit("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
const addBlackData = () => {
  baseService.post("/blacklist/blacklist", {
    id: null,
    account: resData.value.gameAccount,
    gameName: resData.value.gameName,
    idCard: "-",
    name: "-",
    phone: resData.value.phone,
    platform: "-",
    remarks: resultoptions.value[+dataForm.value.subDelistingCause - 1].label
  });
};
defineExpose({
  init
});
</script>
<style lang="scss">
.date_picke {
  .el-picker-panel__footer {
    display: none !important;
  }
}
</style>
