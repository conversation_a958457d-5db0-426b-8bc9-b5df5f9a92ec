<template>
    <div class="mod-finance__tenantfinance">
        <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
                    <el-button v-if="state.hasPermission('finance:tenantfinance:save')" type="primary"
                        @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                    <el-button v-if="state.hasPermission('finance:tenantfinance:delete')" type="danger"
                        @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
                </template>
                
                <!-- 操作 -->
                <template #operate="scope">
                    <el-button v-if="state.hasPermission('finance:tenantfinance:update')" type="primary" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('finance:tenantfinance:delete')" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
        
        </el-card>
        <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit"
            :total="state.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./tenantfinance-add-or-update.vue";

const view = reactive({
    getDataListURL: "/finance/tenantfinance/page",
    getDataListIsPage: true,
    exportURL: "/finance/tenantfinance/export",
    deleteURL: "/finance/tenantfinance",
    deleteIsBatch: true,
    dataForm: {
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "type",
        label: "类型",
        minWidth: 100,
    },
    {
        prop: "amount",
        label: "变动金额",
        minWidth: 100
    },
    {
        prop: "balance",
        label: "余额",
        minWidth: 100
    },
    {
        prop: "info",
        label: "说明",
        minWidth: 150
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])



const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addKey.value++;
    nextTick(() => {
        addOrUpdateRef.value.init(id);
    });
};
</script>