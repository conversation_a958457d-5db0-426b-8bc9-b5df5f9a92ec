<template>
  <div class="business_page">
    <div class="card_analysis mt-12" style="background: #f7f8fa">
      <div class="header_analysis" style="padding: 22px 20px 20px 20px">
        <div class="header_analysis_left flx-align-center">
          <div class="header_analysis_title" style="font-size: 20px; margin-left: 0px">待售库存明细</div>
        </div>
        <div class="header_analysis_right flx-align-center">
          <!-- <div class="analysis_type_item" style="margin-left: 10px;">取当前数据进行比对</div> -->
        </div>
      </div>
      <div style="padding: 0px 20px 20px 20px">
        <div class="business_header flx-align-center">
          <el-button type="info" :loading="exportclickLoading" @click="exportclick">导出数据</el-button>
          <el-radio-group v-model="dataForm.type" style="margin-left: 24px" @change="radioChange">
            <el-radio value="1">回收员工</el-radio>
            <!-- <el-radio value="2">回收渠道</el-radio> -->
            <el-radio value="3">游戏</el-radio>
          </el-radio-group>
        </div>
        <div class="business_center">
          <el-table :data="paginatedData" style="width: 100%; margin-top: 12px" cell-class-name="ch-56" border class="business_table" @sort-change="sortChange">
            <template v-for="item in columns" :key="item">
              <el-table-column :prop="item.prop" :label="item.label" :sortable="item.prop != 'name' ? 'custom' : false" :min-width="item.minWidth" align="center">
                <template #header v-if="item.prop == 'name'">
                  <span v-if="dataForm.type == '1'">回收员工</span>
                  <span v-if="dataForm.type == '2'">回收渠道</span>
                  <span v-if="dataForm.type == '3'">游戏</span>
                </template>
                <template #default="{ row, $index }">
                  {{ row[item.prop] }}
                </template>
              </el-table-column>
            </template>
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
          <el-pagination :current-page="pagination.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="pagination.size" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { nextTick, onMounted, reactive, ref, toRefs, computed } from "vue";
import { fileExport } from "@/utils/utils";
import { usePagination, useSortList } from "@/views/dataAnalysis/pagination";

const tableData = ref(<any>[]);

// 分页
const pagination = ref({
  page: 1,
  size: 10
});
const total = ref();

// 计算分页数据
const paginatedData = computed(() =>
  usePagination({
    currentPage: pagination.value.page,
    pageSize: pagination.value.size,
    data: tableData.value
  })
);

// 表格配置项
const columns = reactive([
  // {
  //     type: "selection",
  //     width: 50
  // },
  {
    prop: "name",
    label: "回收员工",
    minWidth: 112
  },
  {
    prop: "inventoryTotal",
    label: "库存总数",
    minWidth: 80,
    sortable: "custom"
  },
  {
    prop: "totalCost",
    label: "总成本(元)",
    minWidth: 80,
    sortable: "custom"
  },
  {
    prop: "totalValuation",
    label: "总估值(元)",
    minWidth: 100,
    sortable: "custom"
  },

  {
    prop: "zeroToFiveHundred",
    label: "0~500元存量",
    minWidth: 100,
    sortable: "custom"
  },
  {
    prop: "fiveHundredToOneThousand",
    label: "501~1000元存量",
    minWidth: 100,
    sortable: "custom"
  },
  {
    prop: "oneThousandAbove",
    label: "1000元以上存量",
    minWidth: 100,
    sortable: "custom"
  }
]);

const dataForm = ref({
  gameId: "",
  recyclingChannelId: "",
  purchaseEmployeeId: "",
  type: "1"
});

onMounted(() => {
  getWaitingSaleInventoryDetail();
});

const radioChange = () => {
  getWaitingSaleInventoryDetail();
};

// 待售库存明细
const getWaitingSaleInventoryDetail = () => {
  baseService.post("/dataAnalysis/waitingSaleInventoryDetail", dataForm.value).then((res) => {
    tableData.value = res.data;
    total.value = tableData.value.length;
  });
};

const exportclickLoading = ref(false);
const exportclick = () => {
  exportclickLoading.value = true;
  baseService
    .get("/dataAnalysis/waitingSaleInventoryDetailExport", dataForm.value)
    .then((res) => {
      if (res) {
        fileExport(res, "待售库存明细");
      }
    })
    .finally(() => {
      exportclickLoading.value = false;
    });
};

// 分页方法
const sizeChange = (number: any) => {
  pagination.value.size = number;
  pagination.value.page = 1;
};

const currentChange = (number: any) => {
  pagination.value.page = number;
};

// 排序事件
const sortChange = (column: any) => {
    if(column.order != null){
        tableData.value = useSortList({
            prop: column.prop,
            order: column.order,
            data: tableData.value
        })
    }
};

const init = (form: any) => {
  Object.assign(dataForm.value, form);
  getWaitingSaleInventoryDetail();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.business_page {
  margin-top: 12px;
}

.business_header {
}

.business_table {
  :deep(th .cell) {
    background: none !important;
  }

  :deep(th:nth-child(n + 2):nth-child(-n + 4)) {
    background-color: rgba(247, 114, 52, 0.1) !important;

    .cell {
      color: #cc9213;
    }
  }

  :deep(th:nth-child(n + 5):nth-child(-n + 7)) {
    background-color: rgba(65, 101, 215, 0.1) !important;

    .cell {
      color: #722ed1;
    }
  }

  :deep(td:nth-child(n + 2):nth-child(-n + 4)) {
    background-color: rgba(247, 114, 52, 0.05) !important;
  }

  :deep(td:nth-child(n + 5):nth-child(-n + 7)) {
    background-color: rgba(65, 101, 215, 0.05) !important;
  }
}

.card_analysis {
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e6eb;

  .header_analysis {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 20px;

    .header_analysis_left {
      .header_analysis_title {
        font-weight: 500;
        font-size: 16px;
        color: #1d252f;
        line-height: 24px;
        margin-left: 4px;
      }
    }

    .header_analysis_right {
      .legend {
        margin-right: 16px;

        :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
          color: #1d2129;
        }

        .el-checkbox:nth-child(1) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #4165d7;
            border-color: #4165d7;
          }
        }

        .el-checkbox:nth-child(2) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #00b42a;
            border-color: #00b42a;
          }
        }

        .el-checkbox:nth-child(3) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #722ed1;
            border-color: #722ed1;
          }
        }
      }
    }
  }

  .header_describe {
    font-weight: 400;
    font-size: 13px;
    color: #4e5969;
    line-height: 22px;
    padding: 0px 20px;
  }

  .center_analysis {
    padding: 12px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .listMap {
      width: 200px;

      .listMap_label {
        span {
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          line-height: 22px;
          margin-right: 2px;
        }
      }

      .listMap_value {
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
      }
    }
  }
}

.analysis_type_item {
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  padding: 3px 12px;
  background: #ffffff;
  border-radius: 24px;
  border: 1px solid #d4d7de;
}
</style>
