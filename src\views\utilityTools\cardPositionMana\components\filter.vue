<template>
  <el-dialog v-model="visible" title="高级筛选" width="800">
    <!-- 号码信息管理 -->
    <el-descriptions style="width: 100%" border :column="2" v-if="filterType == 1">
      <el-descriptions-item class-name="noneSelfRight" label="手机号">
        <el-input v-model="dataForm.phone" clearable placeholder="请输入手机号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="部门">
        <ny-dept-tree v-model="dataForm.deptId" clearable :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
      </el-descriptions-item>
      <el-descriptions-item label="办卡人">
        <el-input v-model="dataForm.createUser" clearable placeholder="请输入办卡人"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="ICCID">
        <el-input v-model="dataForm.iccid" clearable placeholder="请输入ICCID"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="运营商">
        <el-select v-model="dataForm.operator" clearable placeholder="请选择运营商">
          <el-option label="移动" value="移动" />
          <el-option label="联通" value="联通" />
          <el-option label="电信" value="电信" />
          <el-option label="广电" value="广电" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="主卡">
        <el-input v-model="dataForm.mainPhone" clearable placeholder="请输入主卡"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="卡状态">
        <el-select v-model="dataForm.onlineStatus" clearable placeholder="请选择卡状态">
          <el-option label="正常" :value="1" />
          <el-option label="异常" :value="2" />
          <el-option label="停用" :value="3" />
          <el-option label="沉默号" :value="4" />
          <el-option label="空号" :value="5" />
          <el-option label="欠费" :value="6" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="余额(元)">
        <el-input-number style="width: 96px" clearable v-model="dataForm.startBalance" controls-position="right" :precision="2" :step="1" />
        <span style="margin: 0 6px">~</span>
        <el-input-number style="width: 96px" v-model="dataForm.endBalance" controls-position="right" :precision="2" :step="1" />
      </el-descriptions-item>
      <el-descriptions-item label="卡位置">
        <el-input v-model="dataForm.cardPosition" clearable placeholder="请输入卡位置"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="是否频繁">
        <el-switch inline-prompt v-model="dataForm.frequently" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" />
      </el-descriptions-item>
      <!-- <el-descriptions-item label="是否租用">
        <el-switch inline-prompt v-model="dataForm.rent" clearable active-value="1" inactive-value="0" active-text="是" inactive-text="否" />
      </el-descriptions-item> -->
      <el-descriptions-item label="设备状态">
        <el-select v-model="dataForm.deviceStatus" clearable placeholder="请选择设备状态">
          <el-option label="未绑定" :value="0" />
          <el-option label="已绑定" :value="1" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
    </el-descriptions>
    <!-- 短信设备管理 -->
    <el-descriptions style="width: 100%; min-height: 198px" border :column="2" v-if="filterType == 2">
      <el-descriptions-item class-name="noneSelfRight" label="手机号">
        <el-input v-model="dataForm.phone" clearable placeholder="请输入手机号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="卡位编号">
        <el-input v-model="dataForm.positionCode" clearable placeholder="请输入卡位编号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="卡板编号">
        <el-input v-model="dataForm.boardCode" clearable placeholder="请输入卡板编号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="在线状态">
        <el-select v-model="dataForm.onlineStatus" clearable placeholder="请选择在线状态">
          <el-option label="在线" :value="1" />
          <el-option label="离线" :value="0" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="设备类型">
        <el-input v-model="dataForm.deviceType" clearable placeholder="请输入设备类型"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        <el-input v-model="dataForm.remark" clearable placeholder="请输入备注"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="部门">
        <ny-dept-tree v-model="dataForm.deptId" clearable :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
      </el-descriptions-item>
      <el-descriptions-item label="启用状态">
        <el-select v-model="dataForm.enableStatus" clearable placeholder="请选择启用状态">
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.createDate" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
      </el-descriptions-item>
      <el-descriptions-item label="上线时间">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.onlineTime" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="onlineTimeChange" />
      </el-descriptions-item>
      <el-descriptions-item label="离线时间" class-name="noneSelfRight">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.offlineTime" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="offlineTimeChange" />
      </el-descriptions-item>
      <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
    </el-descriptions>
    <!-- 短信列表管理 -->
    <el-descriptions style="width: 100%; min-height: 198px" border :column="2" v-if="filterType == 3">
      <el-descriptions-item label="发送手机号">
        <el-input v-model="dataForm.sendPhone" clearable placeholder="请输入发送手机号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="接收手机号">
        <el-input v-model="dataForm.receivePhone" clearable placeholder="请输入接收手机号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item class-name="noneSelfRight" label="类型">
        <el-input v-model="dataForm.type" clearable placeholder="请输入类型"></el-input>
        <!-- <el-select v-model="dataForm.type" placeholder="请选择类型">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
      </el-descriptions-item>
      <el-descriptions-item label="内容">
        <el-input v-model="dataForm.content" clearable placeholder="请输入内容"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="部门">
        <ny-dept-tree v-model="dataForm.deptId" clearable :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.createDate" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
      </el-descriptions-item>
    </el-descriptions>
    <!-- 开卡人管理 -->
    <el-descriptions style="width: 100%; min-height: 198px" border :column="2" v-if="filterType == 4">
      <el-descriptions-item label="办卡人">
        <el-input v-model="dataForm.userName" clearable placeholder="请输入办卡人"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="联系方式">
        <el-input v-model="dataForm.contactInformation" clearable placeholder="请输入联系方式"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="微信">
        <el-input v-model="dataForm.wx" clearable placeholder="请输入微信"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="支付账号">
        <el-input v-model="dataForm.alipayAccount" clearable placeholder="请输入支付账号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="单卡分成">
        <el-input v-model="dataForm.singleDivideInto" clearable placeholder="请输入单卡分成"></el-input>
      </el-descriptions-item>
      <el-descriptions-item class-name="noneSelfRight" label="负责人">
        <el-input v-model="dataForm.personInCharge" clearable placeholder="请输入负责人"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="与负责人关系">
        <el-input v-model="dataForm.relationship" clearable placeholder="请输入与负责人关系"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="供卡人类型">
        <el-select v-model="dataForm.provideUserType" clearable placeholder="请选择供卡人类型">
          <el-option label="自管理" value="自管理" />
          <el-option label="合作卡主" value="合作卡主" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.createDate" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        <el-input v-model="dataForm.remark" clearable placeholder="请输入备注"></el-input>
      </el-descriptions-item>
    </el-descriptions>
    <!-- 短信发送记录 -->
    <el-descriptions style="width: 100%; min-height: 198px" border :column="2" v-if="filterType == 5">
      <el-descriptions-item label="发送方手机号">
        <el-input v-model="dataForm.sendPhone" clearable placeholder="请输入发送方手机号"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="目标手机号类型">
        <el-input v-model="dataForm.receivePhone" clearable placeholder="请输入目标手机号类型"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-select v-model="dataForm.type" clearable placeholder="请选择状态">
          <el-option label="未发送" value="0" />
          <el-option label="发送成功" value="1" />
          <el-option label="设备不在线" value="2" />
          <el-option label="设备已确认" value="3" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="内容">
        <el-input v-model="dataForm.content" clearable placeholder="请输入内容"></el-input>
      </el-descriptions-item>
      <el-descriptions-item label="部门">
        <ny-dept-tree v-model="dataForm.deptId" clearable :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.createDate" clearable type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
      </el-descriptions-item>
    </el-descriptions>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">查询</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { Calendar } from "@element-plus/icons-vue";
import { IObject } from "@/types/interface";
const { t } = useI18n();
const emit = defineEmits(["paramsData"]);

const visible = ref(false);
const dataForm = ref({} as IObject);
const filterType = ref(1);
const init = (type: any) => {
  filterType.value = type;
  visible.value = true;
};

// 重置
const reset = () => {
  dataForm.value = {};
};

// 获取信息
const dataFormSubmitHandle = () => {
  visible.value = false;
  emit("paramsData", dataForm.value);
};

// 创建时间
const createDateChange = () => {
  dataForm.value.startCreateDate = dataForm.value.createDate && dataForm.value.createDate.length ? dataForm.value.createDate[0] : "";
  dataForm.value.endCreateDate = dataForm.value.createDate && dataForm.value.createDate.length ? dataForm.value.createDate[1] : "";
};

// 上线时间
const onlineTimeChange = () => {
  dataForm.value.startOnlineTime = dataForm.value.onlineTime && dataForm.value.onlineTime.length ? dataForm.value.onlineTime[0] : "";
  dataForm.value.endOnlineTime = dataForm.value.onlineTime && dataForm.value.onlineTime.length ? dataForm.value.onlineTime[1] : "";
};

// 离线时间
const offlineTimeChange = () => {
  dataForm.value.startOfflineTime = dataForm.value.offlineTime && dataForm.value.offlineTime.length ? dataForm.value.offlineTime[0] : "";
  dataForm.value.endOfflineTime = dataForm.value.offlineTime && dataForm.value.offlineTime.length ? dataForm.value.offlineTime[1] : "";
};

defineExpose({
  init,
  reset
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>
