<template>
  <el-dialog v-model="visible" title="库存预警设置" :width="480" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 240px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item label="" prop="alarmRangeStart">
        <template #label>
          <span style="display: flex">库存<el-button style="padding: 0" text type="success">不足</el-button>值范围</span>
        </template>
        <div style="display: flex">
          <span>≤</span>
          <el-input style="margin: 0 10px; width: 320px" v-model="dataForm.alarmRangeStart" placeholder="请输入商品数量">
            <template #append>个</template>
          </el-input>
          <span style="display: flex; width: 150px">时为“<el-button style="padding: 0" text type="success">库存不足</el-button>”</span>
        </div>
      </el-form-item>
      <el-form-item label="" prop="alarmRangeEnd">
        <template #label>
          <span style="display: flex">库存<el-button style="padding: 0" text type="danger">滞销</el-button>值范围</span>
        </template>
        <div style="display: flex">
          <span>≥</span>
          <el-input style="margin: 0 10px; width: 320px" v-model="dataForm.alarmRangeEnd" placeholder="请输入商品数量">
            <template #append>个</template>
          </el-input>
          <span style="display: flex; width: 150px">时为“<el-button style="padding: 0" text type="danger">库存滞销</el-button>”</span>
        </div>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  alarmRangeEnd: undefined,
  alarmRangeStart: undefined,
  settingType: 0 // 0 库存设置
});

const rules = ref({});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  getInfo();
};

// 获取信息
const getInfo = () => {
  baseService.get("/shop/shopSetting/getStockSetting").then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  if (dataForm.alarmRangeStart && dataForm.alarmRangeEnd && +dataForm.alarmRangeEnd < +dataForm.alarmRangeStart) {
    ElMessage.warning("库存滞销值应大于库存不足值！");
    return;
  }
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.put("/shop/shopSetting/editStockSetting", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
.el-form-item {
  display: block;

  :deep(.el-input-group__append) {
    background-color: #fff !important;
  }
  .el-button--primary {
    color: #4165d7 !important; // 不跟随系统色
  }
}
</style>