<template>
  <el-dialog v-model="visible" width="480" :title="'提现申请'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-suffix="：">
      <el-descriptions title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>可提现金额(元)</div>
          </template>
          {{ formatCurrency(allAmount) }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>提现金额(元)<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="amount" label="提现金额" required>
            <el-input v-model="dataForm.amount" placeholder="请输入提现金额" type="number" controls-position="right" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>选择账户<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="accountId" label="选择账户" required>
            <el-select v-model="dataForm.accountId" placeholder="选择收款账户" clearable @change="changeAccount">
              <el-option v-for="(item, index) in selectData" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select> </el-form-item></el-descriptions-item
        ><el-descriptions-item label-class-name="title">
          <template #label>
            <div>备注</div>
          </template>
          <el-form-item prop="remark" label="备注">
            <el-input v-model="dataForm.remark" type="textarea" :rows="5" placeholder="请输入备注" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "提交审核" }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive, toRefs } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { formatCurrency } from "@/utils/method";

const { t } = useI18n();
const emit = defineEmits(["close", "refreshDataList"]);
const allAmount = ref(0); //可提现余额
const visible = ref(false);
const rules = {
  amount: [{ required: false, message: "请输入提现金额", trigger: "blur" }],
  remark: [{ required: false, message: "请输入备注", trigger: "change" }],
  accountId: [{ required: true, message: "请选择选择账户", trigger: "change" }]
};
const dataFormRef = ref();
const dataForm = ref(<any>{
  amount: undefined,
  remark: undefined,
  accountId: undefined
});
// 详情id
const init = (info?: any) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    dataForm.value.amount = undefined;
  }
  allAmount.value = info.allAmount;
  info ? (dataForm.value.id = info.id) : "";
  getInfo();
};
const selectData = ref([]);
// 获取详情
const getInfo = async () => {
  let res = await baseService.get("/wallet/tenantaccount/page", { limit: 9999 });
  selectData.value = res.data.list.filter((ele: any) => ele.type != 2);
};
const changeAccount = () => {
  let obj: any = {};
  obj = selectData.value.find((ele: any) => ele.id == dataForm.value.accountId);
  dataForm.value.account = obj.account;
  dataForm.value.bankName = obj.bankName;
  dataForm.value.recipient = obj.name;
  dataForm.value.accountType = obj.type == 3 ? 2 : 1;
};
// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = async () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let params = { ...dataForm.value };
    delete params.accountId;
    replyLoading.value = true;
    params.id ? (params.state = 0) : "";
    baseService.post("/wallet/bill/withdraw/save", params).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          replyLoading.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emit("close");
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
  display: flex;
}
:deep(.el-form-item--default) {
  margin-bottom: 12px;
  display: block;
}
.fontSty {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.el-statistic__content) {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #e6a23c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
