<template>
    <el-dialog width="800px" v-model="dialogVisible" class="create-group-dialog">
        <template #header="{ close, titleId, titleClass }">
            <div class="create-group-header">
                <p class="dialog-title">{{ title }}</p>
            </div>
        </template>
        
        <div class="create-group-dialog-content flx">
            <div class="left-container">
                <div class="search-container">
                    <el-input 
                        v-model="searchKeywords" placeholder="请输入昵称进行搜索" :prefix-icon="Search" clearable
                        @input="inputChange(false)"
                        @keyup.enter="inputChange(true)"    
                    />
                </div>
                
                <!-- 最近联系人 -->
                <div class="recent-contacts card">
                    <div class="card-title">最近联系人</div>
                    <div class="contacts-list">
                        <el-checkbox-group class="w-100" v-model="checkedMember">
                            <el-checkbox class="checkbox" v-for="(item, index) in recentContacts" :key="index" :value="item.targetId + '-' + item.nickname">
                                <div class="member-item flx-align-center">
                                    <el-image v-if="item.headUrl || item.imRole" class="avatar" :src="item.headUrl || ''">
                                        <template #error>
                                            <div class="image-slot">
                                                <el-image class="avatar" :src="(item.imRole == 1 || item.imRole == 2) ? settingStore.info.backendDefaultAvatar : settingStore.info.userDefaultAvatar"></el-image>
                                            </div>
                                        </template>
                                    </el-image>
                                    <el-image v-else class="avatar" :src="settingStore.info.userDefaultAvatar"></el-image>
                                    <el-tooltip placement="top" :content="item.nickname">
                                        <span class="sle">{{ item.nickname || '-' }}</span>
                                    </el-tooltip>
                                </div>
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </div>

                <!-- 选择成员 -->
                <div class="select-member card">
                    <div class="card-title flx-justify-between">
                        选择成员
                        <div class="right-btn" v-if="!searchKeywords">
                            <div 
                                class="btn" 
                                v-for="(item, index) in ['角色', '部门']" 
                                :key="index"
                                :class="{ active: currentType == index }"
                                @click="typeTabsChange(index)"
                            >
                                {{ item }}
                            </div>
                        </div>  
                    </div>
                    
                    <div class="member-content flx">
                        <el-scrollbar class="role" v-if="!searchKeywords">
                            <!-- 角色 -->
                            <template v-if="currentType==0">
                                <div 
                                    class="role-item" 
                                    v-for="(item, index) in roles" 
                                    :key="index"
                                    :class="{ active: index == currentRoleIndex }"
                                    @click="roleSelectChange(index)"
                                >
                                    {{ item.name }}
                                </div>
                            </template>

                            <!-- 部门 -->
                            <template v-else>
                                <el-tree :data="deptTree" :props="{ label: 'name' }" @node-click="handleNodeClick"></el-tree>
                            </template>
                        </el-scrollbar>
                        
                        <el-scrollbar class="select-member-list">
      
                            <el-checkbox
                                v-model="checkAll"
                                :indeterminate="isIndeterminate"
                                @change="handleCheckAllChange"
                                v-if="memberList && memberList.length"
                            >
                                全选
                            </el-checkbox>
                            <el-checkbox-group v-model="checkedMember" @change="handleCheckedMemberChange">
                                <el-checkbox
                                    v-for="(member, index) in memberList"
                                    :key="index"
                                    :label="member.imUid"
                                    :value="member.imUid + '-' + member.nickname"
                                >
                                    <div class="member-item flx-align-center">
                                        <el-image class="avatar" :src="member.headUrl || settingStore.info.backendDefaultAvatar"></el-image>
                                        <span class="sle">{{ member.nickname || '-' }}</span>
                                    </div>
                                </el-checkbox>
                            </el-checkbox-group>
                            <ny-no-data type="3" v-if="!memberList || !memberList.length" description="未搜索到相关人员" />
                        </el-scrollbar>
                        
                    </div>

                </div>
                      
            </div>

            <div class="right-container flx-column">
                <div class="selected flx-justify-between">
                    <div class="selected-count">
                        已选 (<span class="count">{{ checkedMember.length }}</span><span>/1000</span>)
                    </div>
                    <div class="clear pointer text-primary" @click="emptied">清空</div>
                </div>

                <el-scrollbar class="select-member-list">
                    <div class="flx member-item-wrap">
                        <div class="member-item" v-for="(item, index) in checkedMember" :key="index">
                            {{ findeMemberName(item) }}
                            <el-icon @click="delMember(index)"><Close /></el-icon>
                        </div>
                    
                    </div>
                </el-scrollbar>
                <el-form label-position="top" model="dataForm" :rules="rules" ref="formRef" v-if="sourceType == 'create'">
                    <el-form-item label="群聊类型" class="is-required">
                        <ny-radio-group v-model="dataForm.type" :list="groupChatsTypes" value="value" label="label"></ny-radio-group>
                    </el-form-item>
                    <el-form-item label="订单号" prop="orderId" v-if="dataForm.type == '1'">
                        <el-input v-model="dataForm.orderId" placeholder="请输入订单号"></el-input>
                    </el-form-item>
                    <el-form-item label="群聊名称" prop="groupName" v-if="dataForm.type == '3'">
                        <el-input v-model="dataForm.groupName" placeholder="请输入群聊名称"></el-input>
                    </el-form-item>
                </el-form>

            </div>
        </div>

        <div class="flx-justify-end create-group-footer">
            <el-button :loading="btnLoading" @click="handleCancel">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleSubmit">确定</el-button>
        </div>
        
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineProps, computed } from 'vue';
    import { Search, Close } from '@element-plus/icons-vue';
    import baseService from '@/service/baseService';
    import { ElMessage } from 'element-plus';
    import { useAppStore } from '@/store/index';
    import { useImStore } from '@/store/im';
    import { useSettingStore } from "@/store/setting";

    const store = useAppStore();
    const imStore = useImStore();
    const settingStore = useSettingStore();

    defineProps({
        title: {
            type: String,
            default: '创建群聊'
        }
    })
    
    const dialogVisible = ref(false);
    
    // 来源类型
    const sourceType = ref('');

    const init = (type: string) => {
        sourceType.value = type;
        // 只保留前8条
        let list = getCoversationList();
        recentContacts.value = list.map(item => {
            return {
                headUrl: item.sessionData ? item.sessionData.headUrl : '',
                nickname: item.sessionData ? item.sessionData.nickname : '',
                imRole: item.sessionData ? item.sessionData.imRole : '',
                targetId: item.targetId
            }
        });
        dialogVisible.value = true;
        memberList.value = [];
        getRoles();
        getdepts();
    }

    // 获取会话前八条 单聊
    const getCoversationList = () => {
       let arr = JSON.parse(JSON.stringify(imStore.imConversationList));
       let list = arr.filter(item => item.conversationType == 1);
       return list.slice(0, 8);
    }

    // 按昵称搜索
    const searchKeywords = ref('');
    const searchLoading = ref(false);
    const inputChange = (loading: boolean) => {
        searchLoading.value = loading;
        let data = JSON.parse(JSON.stringify(memberListCopy.value));
        if (searchKeywords.value) {
            data = data.filter(item => item.nickname && item.nickname.includes(searchKeywords.value));
        }
        setTimeout(() => {
            searchLoading.value = false;
        }, 200);
        memberList.value = data;
    };


    // 最近联系人
    const recentContacts = ref<any>([]);
    // 已选中的联系人
    const recentContactsCheckedList = ref<any>([]);
    

    // 选择成员 筛选类型
    const currentType = ref(0);
    const typeTabsChange = (index: number) => {
        currentType.value = index;
        if(currentType.value == 0){
            roleSelectChange(0);
        }else{

        }
    }
    
    // 角色列表
    const currentRoleIndex = ref();
    const roles = ref(<any>[]);
    const dataLoading = ref(false);

    const getRoles = () => {
        dataLoading.value = true;
        baseService.get('/sys/role/all/list').then((res: any) => {
            roles.value = res.data;
            roles.value.map((item: any) => {
                
                if(item.users && item.users.length) {
                    item.users = item.users.filter((item: any) => item.imUid != imStore.imUid);
                    memberList.value = [...memberList.value, ...item.users];
                } 
            })
            memberListCopy.value = JSON.parse(JSON.stringify(memberList.value));
        }).finally(() => {
            dataLoading.value = false;
        })
    }
    const roleSelectChange = (index: number) => {
        currentRoleIndex.value = index;
        memberList.value = roles.value[index].users || [];
    }


    // 部门列表
    const deptTree = ref([]);
    const getdepts = () => {
        baseService.get('/sys/dept/all/list').then((res: any) => {
            deptTree.value = res.data;
            
            // roleSelectChange(0);
        })
    }
    const handleNodeClick = (node: any) => {
        memberList.value = node.users || [];
    }   

    // 成员列表
    const memberList = ref([]);
    const memberListCopy = ref([]);

    

    const dataForm = ref({
        // 1交易群，2中介群3普通群,4回收群
        type: '1',
        // 群名称
        groupName: '',
        // 订单号
        orderId: '',
        // 群主id
        waiterId: '',
        // 群员imUid
        imUids: []
    })

    // 群聊类型
    const groupChatsTypes = [
        { value: '1', label: '交易群' },
        // { value: '2', label: '中介群' },
        { value: '3', label: '普通群' }
    ]
    

    // 已选中的人员
    const checkedMember = ref(<any>[]);

    // 是否全选
    const checkAll = ref(false);
    // 是否半选
    const isIndeterminate = ref(false);

    // 全选
    const handleCheckAllChange = val => {
        if (val) {
            checkedMember.value = memberList.value.map(item => item.imUid + "-" + item.nickname);
        } else {
            checkedMember.value = [];
        }
        isIndeterminate.value = false;
    };

    // 单选
    const handleCheckedMemberChange = value => {
        const checkedCount = value.length;
        checkAll.value = checkedCount === memberList.value.length;
        isIndeterminate.value = checkedCount > 0 && checkedCount < memberList.value.length;
    };

    // 通过id 查找对应的用户名
    const findeMemberName = computed(() => {
        return data => {
            return data.split("-")[1];
        };
    });

    // 清空
    const emptied = () => {
        checkAll.value = false;
        isIndeterminate.value = false;
        checkedMember.value = [];
    };
    // 删除已选
    const delMember = index => {
        checkedMember.value.splice(index, 1);
        if (checkedMember.value.length == 0) {
            isIndeterminate.value = false;
        } else {
            checkAll.value = false;
            isIndeterminate.value = true;
        }
    };
    

    const rules = {
        type: [
            { required: true, message: '请选择群聊类型', trigger: 'blur' }
        ],
        groupName: [
            { required: true, message: '请输入群聊名称', trigger: 'blur' }
        ]
    }

    // 取消
    const handleCancel = () => {
        dialogVisible.value = false;
    }
    
    // 提交
    const btnLoading = ref(false);
    const formRef = ref();
    const handleSubmit = () => {
        
        if(!checkedMember.value || !checkedMember.value.length){
            return ElMessage.warning('请选择群成员');
        }

        // 创建群聊
        dataForm.value.waiterId = store.state.user.id;
        dataForm.value.imUids = checkedMember.value.map(item => item.split("-")[0])
        if(sourceType.value == 'create') dataForm.value.imUids.push(imStore.imUid);


        // 直接选择人员加入群
        if(sourceType.value == 'add'){
            let members =  dataForm.value.imUids.map(item => {
                return { id: item }
            })
            btnLoading.value = true;
            baseService.post('/im/login/group/invitation', {
                // 群组id
                id: imStore.currentConversation.targetId,
                members: members
            }).then(res => {
                if(res.code == 0){
                    ElMessage.success('操作成功！');
                    dialogVisible.value = false;
                }
            }).finally(() => {
                btnLoading.value = false;
            })
            return 
        }


        formRef.value.validate((valid:any) => {
            if(!valid) return;
        })
        

        btnLoading.value = true;
        baseService.post('/im/login/group/create', dataForm.value).then(res => {
            if(res.code == 0){
                ElMessage.success('操作成功！');
                dialogVisible.value = false;
            }
        }).finally(() => {
            btnLoading.value = false;
        })
    }


    defineExpose({
        init
    })
</script>

<style lang="scss">
    .create-group-dialog{
        padding: 0;

        .el-dialog__header{
            padding-right: 0;
        }
    }
    .create-group-header{
        height: 56px;
        line-height: 56px;
        box-shadow: inset 0px -1px 0px 0px #F0F0F0;
        padding: 0 16px;
        color: rgba(0,0,0,0.85);
        font-size: 16px;
        font-weight: bold;
        padding-right: 0;

        
    }

    .create-group-dialog-content{
        padding: 16px;
        .left-container{
            width: 475px;
            padding-right: 16px;
            border-right: 1px solid rgba(186,191,197,0.4);

            .card{
                margin-top: 8px;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid rgba(186,191,197,0.4);

                .member-item{
                    height: 32px;
                    line-height: 32px;

                    .avatar{
                        width: 32px;
                        height: 32px;
                        border-radius: 4px;
                        margin-right: 3px;
                    }

                    span{
                        width: calc(100% - 35px);
                    }
                }

                &.recent-contacts{
                    height: 118px;

                    .contacts-list{
                        display: flex;
                        flex-wrap: wrap;

                        .checkbox{
                            margin-top: 8px;
                            width: 25%;
                            margin-right: 0;

                            .el-checkbox__label{
                                width: calc(100% - 14px);
                            }
                        }
                        
                    }
                }

                &.select-member{
                    height: 320px;
                    
                    .right-btn{
                        display: flex;
                        background: #F5F6F7;
                        border: 1px solid rgba(186,191,197,0.4);
                        border-radius: 4px;

                        .btn{
                            line-height: 24px;
                            width: 60px;
                            text-align: center;
                            color: rgba(0,0,0,0.6);
                            cursor: pointer;

                            &.active{
                                color: var(--el-color-primary);
                                background: #fff;
                                border-radius: 4px;
                            }
                        }
                    }

                    .member-content{
                        padding-top: 8px;
                        
                        .role{
                            width: 240px;
                            height: 280px;

                            .role-item{
                                height: 36px;
                                line-height: 36px;
                                padding-left: 16px;
                                cursor: pointer;

                                &.active, &:hover{
                                    background: #F5F6F7;
                                }
                            }
                        }

                        .el-tree-node__content{
                            height: 36px;
                            line-height: 36px;
                        }

                        .select-member-list{
                            flex: 1;
                            height: 270px;
                            overflow-x: hidden;
                            margin-left: 12px;
                            
                            .checkbox{
                                width: 100%;
                               
                                margin-bottom: 8px;
                            }

                            .el-checkbox {
                                height: 36px;
                                width: 100%;
                                padding-right: 0;
                                margin-right: 0;
                                margin-bottom: 8px;
                            }
                        }
                    }
                }
            }
        }

        .right-container{
            flex: 1;
            margin-left: 16px;
            height: 478px;

            .selected{
                height: 36px;
                color: rgba(0,0,0,0.6);
                border-bottom: 1px solid rgba(186,191,197,0.4);

                .count{
                    font-size: 16px;   
                    font-weight: bold;
                    color: rgba(0,0,0,0.8);
                }
            }

            .el-radio{
                margin-right: 8px !important;
                padding-right: 8px !important;
            }

            .select-member-list{
                height: 290px;

                .member-item-wrap{
                    flex-wrap: wrap;

                    .member-item{
                        display: flex;
                        align-items: center;
                        margin: 8px 8px 0 0;
                        background: #F5F6F7;
                        border-radius: 4px;
                        height: 30px;
                        color: rgba(0,0,0,0.6);
                        padding: 0 8px;

                        .el-icon{
                            margin-left: 5px;
                        }
                    }
                }
            }

            .el-form-item:last-child{
                margin-bottom: 0;
            }
        }
    }

    .create-group-footer{
        padding: 0 16px 16px 0;
    }
</style>