<template>
  <el-dialog v-model="visible" width="500" :title="(!dataForm.id ? $t('add') : $t('update')) + '手机号'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 188px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <template v-if="!dataForm.id">
        <template v-for="(ele, i_) in dataForm.phoneList" :key="i_">
          <el-form-item style="margin-bottom: 8px" :prop="`phoneList.${i_}.phone`" :rules="rules.phone">
            <template #label>
              <div style="display: flex; justify-content: space-between; width: 100%">
                <span style="color: #606266">手机号码{{ i_ + 1 }}</span>
                <div>
                  <el-button style="width: 14px; height: 14px" :icon="Plus" @click="addArrHandle" type="primary" circle />
                  <el-button v-if="i_ != 0" style="margin-left: 12px; width: 14px; height: 14px" type="danger" @click="delArrHandle" :icon="Delete" circle />
                </div>
              </div>
            </template>
            <el-input clearable v-model="ele.phone" placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <el-form-item style="margin-bottom: 8px" prop="remark">
            <template #label>
              <div style="display: flex; justify-content: space-between; width: 100%">
                <span style="color: #606266">备注{{ i_ + 1 }}</span>
              </div>
            </template>
          <el-input clearable v-model="ele.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
        </template>
      </template>
      <template v-else>
        <el-form-item style="margin-bottom: 8px" prop="phone">
          <template #label>
            <div style="display: flex; justify-content: space-between; width: 100%">
              <span style="color: #606266">手机号码</span>
            </div>
          </template>
          <el-input clearable v-model="dataForm.phone" placeholder="请输入手机号码"></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 8px" prop="remark">
          <template #label>
            <div style="display: flex; justify-content: space-between; width: 100%">
              <span style="color: #606266">备注</span>
            </div>
          </template>
          <el-input clearable v-model="dataForm.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 8px" prop="remark2">
          <template #label>
            <div style="display: flex; justify-content: space-between; width: 100%">
              <span style="color: #606266">备注2</span>
            </div>
          </template>
          <el-input clearable v-model="dataForm.remark2" placeholder="请输入备注2"></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 8px" prop="status">
          <template #label>
            <div style="display: flex; justify-content: space-between; width: 100%">
              <span style="color: #606266">状态</span>
            </div>
          </template>
          <el-radio-group v-model="dataForm.status">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">废弃</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  phoneList: <any>[]
});

const validatePhone = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (!/^1[0-9]{10}$/.test(value)) {
    return callback(new Error(t("validate.format", { attr: "手机号码" })));
  }
  callback();
};
const rules = ref({
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { validator: validatePhone, trigger: "blur" }
  ],
  status: [{ required: true, message: "请选择状态", trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  delete dataForm.id;
  if (id) {
    dataForm.id = id;
    getInfo(id);
  } else {
    dataForm.phoneList = [
      {
        phone: undefined
      }
    ];
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/phone/sysphone/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (!dataForm.id) {
      let form = { phones: dataForm.phoneList };
      // form.phones = dataForm.phoneList.map((ele) => ele.phone);
      // console.log( form,'====  dataForm.phoneList ======');
      baseService.post("/phone/sysphone", form).then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    } else {
      baseService.put("/phone/sysphone", dataForm).then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    }
  });
};
// 添加违规行为
const addArrHandle = () => {
  dataForm.phoneList.push({
    phone: undefined
  });
};
// 删除流程违规行为
const delArrHandle = (index?: any) => {
  dataForm.phoneList.splice(index, 1);
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: block;
  :deep(.el-form-item__label) {
    width: 100%;
    padding-right: 0;
  }
}
</style>
