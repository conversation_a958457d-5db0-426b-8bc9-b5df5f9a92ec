<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" width="480" class="ny-drawer">
    <el-form label-position="top" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <div class="cardDescriptions" style="padding: 0">
        <el-descriptions :column="1" border class="descriptions" style="margin-bottom: 12px">
          <el-descriptions-item>
            <template #label>部门名称<span style="color: red">*</span></template>
            <el-form-item prop="name" :label="t('dept.name')">
              <el-input v-model="dataForm.name" :placeholder="t('dept.name')"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>上级部门</template>
            <el-form-item prop="parentName" :label="t('dept.parentName')" class="dept-list">
              <el-popover :width="400" ref="deptListPopover" placement="bottom-start" trigger="click" popper-class="popover-pop">
                <template v-slot:reference>
                  <el-input v-model="dataForm.parentName" :readonly="true" :placeholder="t('dept.parentName')">
                    <template v-slot:suffix>
                      <el-icon v-if="user.superAdmin === 1 && dataForm.pid !== '0'" @click.stop="deptListTreeSetDefaultHandle()" class="el-input__icon"><circle-close /></el-icon>
                    </template> </el-input
                ></template>
                <div class="popover-pop-body"><el-tree :data="deptList" :props="{ label: 'name', children: 'children' }" node-key="id" ref="deptListTree" :highlight-current="true" :expand-on-click-node="false" accordion @current-change="deptListTreeCurrentChangeHandle"> </el-tree></div>
              </el-popover>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>负责人</template>
            <el-form-item prop="leaderName" :label="t('dept.leaderName')">
              <el-select v-model="dataForm.leaderName" placeholder="请选择负责人" clearable @visible-change="selectUserInfo()" @clear="dataForm.leaderId = ''"></el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>排序</template>
            <el-form-item prop="sort" :label="t('dept.sort')">
              <el-input-number v-model="dataForm.sort" controls-position="right" :min="0" style="width: 100%" :label="t('dept.sort')"></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>是否启用</template>
            <el-form-item prop="isEnable" :label="t('dept.sort')">
              <el-switch :active-value="1" :inactive-value="0" v-model="dataForm.isEnable" />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">{{ t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ t("confirm") }}</el-button>
    </template>
  </el-dialog>

  <select-user v-if="selectUserVisible" ref="selectUserRef" @commitHandle="setUserInfo"></select-user>
</template>

<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { IObject } from "@/types/interface";
import { useAppStore } from "@/store";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import SelectUser from "./employeesAnddept/select-user.vue";

const store = useAppStore();
const { t } = useI18n();

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const selectUserVisible = ref(false);
const dataFormRef = ref();
const selectUserRef = ref();
const deptList = ref([]);
const deptListPopover = ref();
const deptListTree = ref();

const dataForm = reactive({
  id: "",
  name: "",
  pid: "",
  leaderId: "",
  leaderName: "",
  parentName: "",
  sort: 0,
  isEnable: 1
});

const user = computed(() => store.state.user);

const rules = ref({
  name: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  parentName: [{ required: true, message: t("validate.required"), trigger: "change" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  getDeptList().then(() => {
    if (id) {
      getInfo(id);
    }
    {
      deptListTreeSetDefaultHandle();
    }
  });
};

// 获取部门列表
const getDeptList = () => {
  return baseService.get("/sys/dept/list").then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    deptList.value = res.data;
  });
};

const selectUserInfo = () => {
  selectUserVisible.value = true;
  nextTick(() => {
    selectUserRef.value.init({
      id: dataForm.leaderId
    });
  });
};

const setUserInfo = (userInfo: IObject) => {
  dataForm.leaderId = userInfo.id;
  dataForm.leaderName = userInfo.realName;
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/dept/${id}`).then((res) => {
    Object.assign(dataForm, res.data);

    if (dataForm.pid === "0") {
      return deptListTreeSetDefaultHandle();
    }
    deptListTree.value.setCurrentKey(dataForm.pid);
  });
};

// 上级部门树, 设置默认值
const deptListTreeSetDefaultHandle = () => {
  dataForm.pid = "0";
  dataForm.parentName = t("dept.parentNameDefault");
};

// 上级部门树, 选中
const deptListTreeCurrentChangeHandle = (data: IObject) => {
  dataForm.pid = data.id;
  dataForm.parentName = data.name;
  deptListPopover.value.hide();
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/sys/dept", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.mod-sys__dept {
  .dept-list {
    .el-input__inner,
    .el-input__suffix {
      cursor: pointer;
    }
  }
}
:deep(.el-input-group__append) {
  background-color: #fff !important;
}
</style>
