<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <!-- <el-form-item label="" prop="id">
        <el-input v-model="dataForm.id" placeholder=""></el-input>
      </el-form-item> -->
      <el-form-item label="标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item label="详情" prop="text">
        <!-- <el-input v-model="dataForm.text" placeholder="详情"></el-input> -->
        <wangEditor v-model="dataForm.text"></wangEditor>
      </el-form-item>
      <!-- <el-form-item label="状态1上架  2下架" prop="status">
        <el-input v-model="dataForm.status" placeholder="状态1上架  2下架"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="是否删除 否0 是1" prop="isDelete">
        <el-input v-model="dataForm.isDelete" placeholder="是否删除 否0 是1"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="创建者" prop="creator">
        <el-input v-model="dataForm.creator" placeholder="创建者"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="创建时间" prop="createDate">
        <el-input v-model="dataForm.createDate" placeholder="创建时间"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="更新者" prop="updater">
        <el-input v-model="dataForm.updater" placeholder="更新者"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="更新时间" prop="updateDate">
        <el-input v-model="dataForm.updateDate" placeholder="更新时间"></el-input>
      </el-form-item> -->
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import WangEditor from "@/components/wang-editor/index.vue"
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
import { useAppStore } from "@/store";

 

const visible = ref(false);
const dataFormRef = ref();
const store = useAppStore();

const dataForm = reactive({
  id: "",
  title: "",
  sort: "",
  text: "",
  status: "",
  isDelete: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/basicInfo/tbannouncement/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    // 判断valid是否为false，如果是，则返回false
    if (!valid) {
      return false;
    }
    console.log(dataForm,store.state.user);
    // if (dataForm.id) {
    //   dataForm.updater = store.state.user.name;
    // } else {
    //   dataForm.creator = store.state.user.name;
    // }
    
    // 如果dataForm.id不存在，则调用baseService.post方法，否则调用baseService.put方法
    (!dataForm.id ? baseService.post : baseService.put)("/basicInfo/tbannouncement", dataForm).then((res) => {
      // 弹出成功提示信息
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          // 关闭弹窗
          visible.value = false;
          // 触发父组件的refreshDataList方法
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>