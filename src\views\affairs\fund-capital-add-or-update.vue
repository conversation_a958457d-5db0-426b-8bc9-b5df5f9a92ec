<template>
  <el-dialog v-model="visible" :title="dataForm.id ? '编辑' : '新增'" :close-on-click-modal="false" :close-on-press-escape="false" width="800px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="100px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="借款人" prop="borrowerName">
            <el-input v-model="dataForm.borrowerName" placeholder="请输入借款人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资金类型" prop="fundType">
            <el-select v-model="dataForm.fundType" placeholder="请选择资金类型" style="width: 100%">
              <el-option label="财务借款" value="1"></el-option>
              <el-option label="运营借款" value="2"></el-option>
              <el-option label="资金账户" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账号" prop="accountNumber">
            <el-input v-model="dataForm.accountNumber" placeholder="请输入账号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="openBank">
            <el-input v-model="dataForm.openBank" placeholder="请输入开户银行"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input-number v-model="dataForm.amount" :precision="2" :min="0" placeholder="请输入金额" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="dataForm.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="正常" value="1"></el-option>
              <el-option label="冻结" value="2"></el-option>
              <el-option label="注销" value="3"></el-option>
              <el-option label="待激活" value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="用途" prop="purpose">
            <el-input v-model="dataForm.purpose" placeholder="请输入用途"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  borrowerName: "",
  fundType: "",
  accountNumber: "",
  openBank: "",
  amount: 0,
  status: "1",
  purpose: "",
  remark: ""
});

const rules = ref({
  borrowerName: [{ required: true, message: "借款人不能为空", trigger: "blur" }],
  fundType: [{ required: true, message: "资金类型不能为空", trigger: "change" }],
  accountNumber: [{ required: true, message: "账号不能为空", trigger: "blur" }],
  amount: [{ required: true, message: "金额不能为空", trigger: "blur" }],
  status: [{ required: true, message: "状态不能为空", trigger: "change" }]
});

const init = (id?: number) => {
  visible.value = true;
  
  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    borrowerName: "",
    fundType: "",
    accountNumber: "",
    openBank: "",
    amount: 0,
    status: "1",
    purpose: "",
    remark: ""
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get(`/finance/capital/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/finance/capital", dataForm).then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>