<template>
  <!-- 售后订单 -->
  <div class="container sell-order-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          {
            id: 1,
            title: '收入订单'
          },
          {
            id: 2,
            title: '支出订单'
          }
        ]"
        @change="getResetting"
        v-model="dataForm.billType"
        value="id"
        label="title"
      ></ny-flod-tab>

      <div class="stat_card">
        <div class="stat_item" style="background: linear-gradient(160deg, rgba(230, 254, 234, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">本月对账成功订单</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.monthDate1" type="month" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle">{{ totalRebackData.nowMonth || 0 }}</div>
          <div class="stat_below">
            <span class="sum_label">本年对账成功：</span>
            <span class="sum_value" style="color: #00b42a">{{ totalRebackData.nowYear || 0 }}</span>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(160deg, rgba(230, 238, 254, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">对账成功金额(元)</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.monthDate2" type="month" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle">{{ formatCurrency(totalRebackData.nowMonth2 || 0) }}</div>
          <div class="stat_below">
            <span class="sum_label">本年对账成功：</span>
            <span class="sum_value" style="color: #165dff">{{ formatCurrency(totalRebackData.nowYear2 || 0) }}</span>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(160deg, rgba(255, 226, 226, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">本月对账异常订单</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.monthDate3" type="month" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle" style="color: #f53f3f">{{ totalRebackData.nowMonth3 || 0 }}</div>
          <div class="stat_below">
            <span class="sum_label">待处理订单：</span>
            <div style="flex: 1;" class="flx-justify-between">
              <span class="sum_value" style="color: #f53f3f">{{ totalRebackData.toBeProcessed || 0 }}</span>
              <!-- <el-button v-if="totalRebackData.toBeProcessed" type="primary" link style="font-size: 12px; margin-left: 8px; color: #f53f3f" @click="handleError()"
                >处理异常 <el-icon><arrowRight /></el-icon
              ></el-button> -->
            </div>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(160deg, rgba(254, 241, 230, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">须{{ dataForm.billType == 1 ? '追':'付'}}金额(元)</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.monthDate4" type="month" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle">{{ formatCurrency(totalRebackData.nowMonth4 || 0) }}</div>
          <div class="stat_below">
            <span class="sum_label">本年已{{ dataForm.billType == 1 ? '追':'付'}}：</span>
            <span class="sum_value" style="color: #ff9a2e">{{ formatCurrency(totalRebackData.nowYear4 || 0) }}</span>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <ny-button-group
          label="label"
          value="value"
          :list="dataForm.billType == 1 ? [
            { label: '销售订单', value: '2' },
            { label: '售后订单', value: '3' },
            { label: dataForm.billType == 1 ? '其他收入' : '其他支出', value: '4' }
          ] : [
            { label: '销售订单', value: '2' },
            { label: '回收订单', value: '1' },
            { label: '售后订单', value: '3' },
            { label: dataForm.billType == 1 ? '其他收入' : '其他支出', value: '4' }
          ]"
          v-model="dataForm.type"
          @change="getDataList"
        ></ny-button-group>
        <div style="display: flex; align-items: center; gap: 8px">
          <div style="width: 280px !important">
            <el-input v-model="dataForm.searchParam" :placeholder="dataForm.type != '4' ? '请输入订单编号/商品编码/商品标题' : '关联商品'" clearable :prefix-icon="Search"></el-input>
          </div>
          <el-date-picker v-model="createDate" style="width: 240px !important" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
          <div style="width: 160px !important">
            <el-select v-model="dataForm.status" clearable filterable placeholder="请选择对账状态">
              <el-option
                v-for="(item, index) in [
                  { label: '对账成功', value: '1' },
                  { label: '异常订单', value: '2' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <el-button
            type="primary"
            @click="
              getDataList();
              getTotalData();
            "
            >{{ $t("query") }}</el-button
          >
          <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </div>
      <div style="padding-top: 12px">
        <!-- pParams 所有参数都在，如需要特殊处理，去对应的组件内参数接收的地方处理 -->
        <!-- 回收订单 -->
        <recycleTable v-if="dataForm.type == '1'" :pParams="dataForm" ref="reconcileTableRef" />
        <!-- 销售订单 -->
        <sellTable v-if="dataForm.type == '2'" :pParams="dataForm" ref="reconcileTableRef" />
        <!-- 售后订单 -->
        <afterSaleTable v-if="dataForm.type == '3'" :pParams="dataForm" ref="reconcileTableRef" />
        <!-- 其他收入 -->
        <otherInTable v-if="dataForm.type == '4' && dataForm.billType == 1" :pParams="dataForm" ref="reconcileTableRef" />
        <!-- 其他支出 -->
        <otherOutTable v-if="dataForm.type == '4' && dataForm.billType == 2" :pParams="dataForm" ref="reconcileTableRef" />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
import sellTable from "./sellTable.vue";
import recycleTable from "./recycleTable.vue";
import afterSaleTable from "./afterSaleTable.vue";
import otherInTable from "./otherInTable.vue";
import otherOutTable from "./otherOutTable.vue";
import dayjs from "dayjs";

const reconcileTableRef = ref();
// 传参
const dataForm = reactive({
  billType: 1, // 顶部类型
  type: "2", //订单类型
  searchParam: "", //输入框
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  status: "" // 对账状态
});
const createDate = ref([]);
// 统计
const totalRebackData = ref({
  nowMonth: 0,
  nowYear: 0,
  nowMonth2: 0,
  nowYear2: 0,
  nowMonth3: 0,
  toBeProcessed: 0,
  nowMonth4: 0,
  nowYear4: 0
});
const totalParams = reactive({
  monthDate1: dayjs().format("YYYY-MM"),
  monthDate2: dayjs().format("YYYY-MM"),
  monthDate3: dayjs().format("YYYY-MM"),
  monthDate4: dayjs().format("YYYY-MM")
});

const createDateChange = () => {
  dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};
const getDataList = () => {
  nextTick(() => {
    reconcileTableRef.value.handleUpDateData(dataForm);
  });
};
const getResetting = () => {
  dataForm.searchParam = "";
  dataForm.startTime = "";
  dataForm.endTime = "";
  dataForm.status = "";
  createDate.value = [];
  if(dataForm.billType == 1 && dataForm.type == '1'){
    dataForm.type = '2'
  }
  nextTick(() => {
    reconcileTableRef.value.handleUpDateData(dataForm);
  });
  getTotalData();
};

// 统计接口处理
const getTotalData = () => {
  // 本月对账成功订单
  baseService.post("/automaticReconciliation/autoreconciliation/reconciliationCount",{ billTye: dataForm.billType, month: totalParams.monthDate1 }).then(res=>{
    if(res.code == 0){
      totalRebackData.value.nowMonth = res.data.nowMonth;
      totalRebackData.value.nowYear = res.data.nowYear;
    }
  })
  // 对账成功金额
  baseService.post("/automaticReconciliation/autoreconciliation/reconciliationAmount",{ billTye: dataForm.billType, month: totalParams.monthDate2 }).then(res=>{
    if(res.code == 0){
      totalRebackData.value.nowMonth2 = res.data.nowMonth;
      totalRebackData.value.nowYear2 = res.data.nowYear;
    }
  })
  // 本月对账异常订单
  baseService.post("/automaticReconciliation/autoreconciliation/exceptionOrderCount",{ billTye: dataForm.billType, month: totalParams.monthDate3 }).then(res=>{
    if(res.code == 0){
      totalRebackData.value.nowMonth3 = res.data.nowMonth;
      totalRebackData.value.toBeProcessed = res.data.toBeProcessed;
    }
  })
  // 须追金额
  baseService.post("/automaticReconciliation/autoreconciliation/madeUpDifferenceCount",{ billTye: dataForm.billType, month: totalParams.monthDate4 }).then(res=>{
    if(res.code == 0){
      totalRebackData.value.nowMonth4 = res.data.nowMonth;
      totalRebackData.value.nowYear4 = res.data.nowYear;
    }
  })
};
const handleError = () => {
  // 处理异常
};

onMounted(()=>{
  getTotalData();
})
</script>

<style lang="scss" scoped>
.stat_card {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
  .stat_item {
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    height: 120px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: 1px solid #e5e6eb;
    .stat_top {
      padding: 12px;
      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }
    .stat_middle {
      padding: 4px 16px;
      font-weight: 500;
      font-size: 20px;
      color: #303133;
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .stat_below {
      display: flex;
      align-items: center;
      padding: 4px 16px 12px 16px;
      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
