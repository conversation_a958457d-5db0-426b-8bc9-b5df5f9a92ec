<template>
    <el-dialog v-model="dialogVisible" title="充值星球币" width="800">
        <div class="recharge_body">
            <div class="line_text">当前余额：{{ balance }}</div>
            <div>
                <div class="line_text">充值说明<span style="color: red;margin-right: 14px;">*</span>1元=10星球币，充值后不可提现</div>
            <div class="line_text">充值方式<span style="color: red;margin-right: 14px;">*</span>支付宝</div>
            </div>
            <div class="center">
                <div class="cardItem">
                    <div class="ItemNumber">
                        <div class="money">5000</div>
                        <img src="/src/assets/images/coinText.png">
                    </div>
                    <div class="recharge_submit" @click="rechargeNow(500)">￥500 立即充值</div>
                </div>
                <div class="cardItem">
                    <div class="ItemNumber">
                        <div class="money">10000</div>
                        <img src="/src/assets/images/coinText.png">
                    </div>
                    <div class="recharge_submit" @click="rechargeNow(1000)">￥1000 立即充值</div>
                </div>
                <div class="cardItem">
                    <div class="ItemNumber">
                        <div class="money">100000</div>
                        <img src="/src/assets/images/coinText.png">
                    </div>
                    <div class="recharge_submit" @click="rechargeNow(10000)">￥10000 立即充值</div>
                </div>
                <div class="cardItem">
                    <div class="ItemNumber">
                        <el-input v-model="num" style="width: 151px; margin-right: 10px;" placeholder="请输入正整数" />
                        <!-- <el-input-number v-model="num" placeholder="请输入正整数" step-strictly controls-position="right" min="10" step="10" @change="" style="margin-right: 10px;">
                            <template #decrease-icon>
                            <el-icon>
                                <Minus />
                            </el-icon>
                            </template>
                            <template #increase-icon>
                            <el-icon>
                                <Plus />
                            </el-icon>
                            </template>
                        </el-input-number> -->
                        <img src="/src/assets/images/coinText.png">
                    </div>
                    <div class="recharge_submit" v-if="num > 0" @click="rechargeNow(num/10)">￥{{ num/10 }} 立即充值</div>
                    <div class="recharge_submit" v-else @click="rechargeNow(num/10)">自定义</div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from "element-plus";
const emit = defineEmits(["change"]);
const dialogVisible = ref(false);
const num = ref();
const rechargeNow = (num:number) =>{
    console.log(num);
    if(!num){
        ElMessage.warning("请输入充值金额");
        return
    }
    emit("change",num);
    dialogVisible.value = false;
}

const balance = ref();
const init = (row?: any) => {
    balance.value = row
    dialogVisible.value = true;
    num.value = null
};
defineExpose({
    init
});
</script>

<style lang='less' scoped>
.recharge_body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .line_text {
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: #606266;
    }

    .center {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        .cardItem {
            width: 378px;
            height: 143px;
            background-image: url("/src/assets/images/coinBgSmall.png");
            background-size: 100% 100%;
            padding: 16px;
            .ItemNumber {
                padding: 11.5px 0px 35.5px 0px;
                display: flex;
                align-items: center;
                .money {
                    font-family: "ZenDots-Regular";
                    font-size: 32px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 32px;
                    background: linear-gradient(90deg, #63451E 0.01%, #060503 14.27%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-right: 10px;
                }
            }

        }
    }
}

.recharge_submit {
    width: 144px;
    background: linear-gradient(90deg, #63451E 0%, #060503 100%);
    border-radius: 4px 4px 4px 4px;
    font-weight: bold;
    font-size: 14px;
    color: #FFD7AF;
    line-height: 22px;
    padding: 5px 0px;
    cursor: pointer;
    text-align: center;
}
</style>