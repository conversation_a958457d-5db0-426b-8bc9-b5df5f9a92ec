<template>
    <el-dialog
        v-model="visible" 
        title="设置" 
        :close-on-click-modal="false" 
        :close-on-press-escape="false" 
        width="1000px" 
        @close="close"
    >
        <el-tabs v-model="activeId" @tab-click="tabChange">
            <el-tab-pane label="商城出售商品信息字段" name="0"></el-tab-pane>
            <el-tab-pane label="商城出售账号信息字段" name="1"></el-tab-pane>
            <!-- <el-tab-pane label="后台商品信息字段" name="2"></el-tab-pane> -->
        </el-tabs>

        <el-alert type="warning" :closable="false" class="mb-15">
            <template #title>
                <div>这里设置类型的附加字段，比如出售王者荣耀账号，可以设置账号的信息</div>
                <div>示例1：名称：账号，密码，类型：文本</div>
            </template>
        </el-alert>
        
        <div v-loading="dataLoading">
            <div class="ml-10 mr-10">
                <el-button class="btns w-100" type="primary" plain size="large" @click="addDatas">添加字段</el-button>
            </div>
    
            <el-scrollbar max-height="500px" class="goods-set-card-wrap mt-10">
                <set-mall-goods-info ref="setInfoRef" @setInfoSave="setInfoSave" v-if="activeId == '0'"></set-mall-goods-info>
                
                <set-mall-account-info ref="setInfoRef" @setInfoSave="setInfoSave" v-if="activeId == '1'"></set-mall-account-info>
                
                <set-goods-info ref="setInfoRef" @setInfoSave="setInfoSave" v-if="activeId == '2'"></set-goods-info>
            </el-scrollbar>
        </div>


        <template v-slot:footer>
            <el-button :loading="btnLoading" @click="close">{{ $t("cancel") }}</el-button>
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
   </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits } from "vue";
import { uuid } from "@/components/ny-flowable/package/utils";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";
import SetMallGoodsInfo from "./components/set-mall-goods-info.vue";
import SetMallAccountInfo from "./components/set-mall-account-info.vue";
import SetGoodsInfo from "./components/set-goods-info.vue";

const emits = defineEmits(["refreshDataList"]);

const gameCode = ref(<string | number>"");
const gameId = ref(<string | number>"");
const activeId = ref("0");

const visible = ref(false);


const init = (id: number| string) => {
   visible.value = true;
   gameId.value = id;
   getDetail();
}

// 切换tab
const tabChange = (e: any) => {
    activeId.value = e.props.name;
    getDetail();
}

// 获取详情
const dataLoading = ref(false);
const getDetail = async () => {
   dataLoading.value = true;
   let res = await baseService.get("/game/sysgame/listCustomField", { gameId: gameId.value, fieldType: activeId.value });
   dataLoading.value = false;
   if(res.code == 0){
        let data = res.data;
        data.map((item: any) => {
            // 数组对象只保留value
            item.fieldValue = item.fieldValues.map((item: any) => item.value);
            delete item.fieldValues;
        })
        setInfoRef.value.init(res.data);
   }
}

// 添加字段
const setInfoRef = ref();
const addDatas = () => {
    setInfoRef.value.addData();
}

const close = () => {
    visible.value = false;
}

// 提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
    setInfoRef.value.validateForm();
}

// 保存
const setInfoSave = (data: any) => {
    btnLoading.value = true;
    data.map((item: any) => {
        item.gameId = gameId.value;
        item.fieldType = activeId.value;
    })
    baseService.post('/game/sysgame/addCustomField', data).then(res => {
        if (res.code == 0) {
            ElMessage({
                type: 'success',
                message: '操作成功！',
            })
            getDetail();
        } else {
            ElMessage({
                type: 'error',
                message: '操作失败！',
            })
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}


defineExpose({
   init
})

</script>

<style lang="less">
.goods-set-card-wrap{
    padding: 0 10px;
    .flx{
        flex-wrap: wrap;
    }
    .card-item{
        margin: 15px 0;
    }

    .el-card__header{
        padding: 10px 20px !important;
    }
}
</style>