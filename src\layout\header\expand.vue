<script lang="ts">
import Lang from "@/components/base/lang";
import SvgIcon from "@/components/base/svg-icon";
import TenantSwitch from "@/components/tenant-switch";
import rechargeDialog from "@/components/rechargeDialog/index.vue";
import { EMitt, ESidebarLayoutEnum, EThemeSetting } from "@/constants/enum";
import baseService from "@/service/baseService";
import { checkPermission } from "@/utils/utils";
import { useFullscreen } from "@vueuse/core";
import { computed, defineComponent, nextTick, onMounted, onUnmounted, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { useAppStore } from "@/store";
import { useImStore } from "@/store/im";
import { useSettingStore } from "@/store/setting";
import SettingSidebar from "../setting/index.vue";
import userLogo from "@/assets/images/user.png";
import "@/assets/css/header.less";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import emits from "@/utils/emits";
import axios from "axios";
import app from "@/constants/app";
import { getCache, getToken } from "@/utils/cache";
import userpassword from "@/views/sys/user-update-password.vue";
import messageNotification from "../view/messageNotification.vue";

interface IExpand {
  userName?: string;
}

/**
 * 顶部右侧扩展区域
 */
export default defineComponent({
  name: "Expand",
  components: { SettingSidebar, SvgIcon, Lang, TenantSwitch, userpassword, messageNotification, rechargeDialog },
  props: {
    userName: String,
    nickname: String,
    headUrl: String
  },
  setup(props: IExpand) {
    const { t } = useI18n();
    const router = useRouter();
    const store = useAppStore();
    const imStore = useImStore();
    const settingStore = useSettingStore();
    const { isFullscreen, toggle } = useFullscreen();
    const tenantSwitch = ref(false);
    const tenantSwitchRef = ref();
    const tenantName = ref();
    const userpasswordRef = ref();
    watch(
      () => store.state.appIsLogin,
      (vl) => {
        if (vl) {
          getTenantInfo();
        }
      }
    );

    const messageCount = computed(() => imStore.unreadMessageCount);

    // 当前租户信息
    const getTenantInfo = () => {
      baseService.get("/sys/tenant/info").then((res) => {
        tenantName.value = res.data.tenantName;
      });
    };

    const onClickUserMenus = (path: string) => {
      if (path === "/login") {
        ElMessageBox.confirm(t("prompt.info", { handle: t("logout") }), t("prompt.title"), {
          confirmButtonText: t("confirm"),
          cancelButtonText: t("cancel"),
          type: "warning"
        })
          .then(() => {
            //清理数据
            store.logout();
            router.push(path);
            // baseService.post("/logout").finally(() => {
            //   router.push(path);
            // });
          })
          .catch(() => {
            //
          });
      } else if (path === "/user/password") {
        nextTick(() => {
          userpasswordRef.value ? userpasswordRef.value.init() : "";
        });
      } else {
        router.push(path);
      }
    };
    const onClickMessage = () => {
      imStore.showMessageNotification = true;
    };
    const onSwitchTenant = () => {
      if (store.state.user.superAdmin === 1 && store.state.user.tenantCode === "10000") {
        tenantSwitch.value = true;
        nextTick(() => {
          if (tenantSwitchRef.value) {
            tenantSwitchRef.value.init();
          }
        });
      }
    };
    const messagePermission = computed(() => checkPermission(store.state.permissions, "sys:notice:all"));
    const jumpRoute = (url: any, inPage = true) => {
      inPage ? router.push(url) : window.open(url);
    };
    const onRefresh = () => {
      emits.emit(EMitt.OnReloadTabPage);
    };

    const rechargeDialogRef = ref();
    const rechargeHandle = () => {
      nextTick(() => {
        rechargeDialogRef.value.init();
      });
    };
    const showRechargeTip = ref(false);
    const closeRechargeTip = (always = "0") => {
      showRechargeTip.value = false;
      localStorage.setItem("hideRechargeTip", always);
    };
    // 属性
    const url = app.api;
    const gameList = ref([]);
    const getAllGames = () => {
      gameList.value = [];
      baseService.get("/game/sysgame/listGames").then((res) => {
        gameList.value = res.data || [];
      });
    };
    const attributeExport = reactive({
      visible: false,
      loading: false,
      ids: []
    });
    const handleExport = () => {
      if (attributeExport.ids.length < 1) {
        ElMessage.warning("请选择游戏");
        return;
      }
      attributeExport.visible = false;
      attributeExport.loading = true;
      axios
        .post(url + "/game/attribute/childrenExport", [...attributeExport.ids], {
          responseType: "blob",
          headers: {
            token: getToken()
          }
        })
        .then((response) => {
          attributeExport.loading = false;
          // 获取文件名
          const fileName = "属性导出";
          // 创建Blob对象
          const blob = new Blob([response.data], { type: response.headers["content-type"] });

          // 下载文件
          downloadBlob(blob, fileName);
        })
        .catch((error) => {
          console.error("下载失败:", error);
          ElMessage.error("文件下载失败");
        });
    };

    // 通用的Blob下载方法
    const downloadBlob = (blob: any, fileName: any) => {
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
    };
    const attributeImport = reactive({
      visible: false,
      options: [],
      ids: []
    });
    const handleSyncData = () => {
      attributeImport.visible = true;
      attributeImport.ids = [];
      baseService.get("/game/sysgame/officialGames").then((res) => {
        attributeImport.options = res.data || [];
      });
    };
    const handleSubmitSync = () => {
      // if (!attributeImport.ids || attributeImport.ids.length < 1) {
      //   ElMessage.warning("请选择游戏！");
      //   return;
      // }
      attributeImport.visible = false;
      ElMessage.success("已提交同步任务！");
      baseService.post("/game/sysgame/sync", attributeImport.ids).then((res) => {
        attributeImport.ids = [];
      });
    };

    onMounted(() => {
      if (store.state.rechargeTip) {
        showRechargeTip.value = true;
        if (localStorage.getItem("hideRechargeTip") && localStorage.getItem("hideRechargeTip") == "1") {
          // 余额不足且设置过不再提示
          showRechargeTip.value = false;
        }
      }
      if (!getToken()) return;
      getAllGames();
    });
    onUnmounted(() => {});
    return {
      props,
      store,
      settingStore,
      isFullscreen,
      messageCount,
      tenantSwitch,
      tenantSwitchRef,
      userpasswordRef,
      messagePermission,
      rechargeDialogRef,
      userLogo,
      tenantName,
      showRechargeTip,
      closeRechargeTip,
      rechargeHandle,
      onClickUserMenus,
      onClickMessage,
      onSwitchTenant,
      toggle,
      onRefresh,
      jumpRoute,
      gameList,
      attributeExport,
      handleExport,
      attributeImport,
      getToken,
      handleSyncData,
      handleSubmitSync,
      url,
      t
    };
  }
});
</script>
<template>
  <div class="rr-header-right-items">
    <!-- <div class="hidden-xs-only" @click="onSwitchTenant">
      <svg-icon name="team" :style="`margin-right: 5px;`"></svg-icon>
      <span style="font-size: 14px"> {{ t("ui.user.links.tenantSwitch") }}：{{ tenantName }} </span>
    </div>
    <tenant-switch v-if="tenantSwitch" ref="tenantSwitchRef"></tenant-switch> -->
    <div class="tips" v-if="store.state.rechargeTip">
      <el-tag type="danger" effect="plain" round style="height: 40px; color: #fff; border: none; border-radius: 2.5rem; background: linear-gradient(90deg, #ff5f5f 17.92%, #f51500 84.73%)">
        <span>余额不足，请尽快充值</span>
        <el-tag type="danger" effect="plain" round @click="rechargeHandle" style="cursor: pointer; margin-left: 8px">去充值</el-tag>
      </el-tag>
      <rechargeDialog ref="rechargeDialogRef" />
    </div>
    <div @click="toggle" class="hidden-xs-only">
      <span>
        <svg-icon :name="isFullscreen ? 'tuichuquanping' : 'fullscreen2'"></svg-icon>
      </span>
    </div>
    <div @click="jumpRoute('https://www.yuque.com/zhangzz-bx7zu/yf3mcg?#', false)" style="display: flex; justify-content: center; align-items: center; margin-top: 2px; cursor: pointer">
      <el-tooltip class="box-item" effect="dark" content="平台公告" placement="bottom">
        <el-icon><Tickets /></el-icon>
      </el-tooltip>
    </div>
    <!-- v-if="messagePermission && store.state.user.tenantCode === '10000'" -->
     <!-- 2025-06-24 测试反馈去除消息通知判断条件 -->
    <div>
      <el-badge :value="messageCount > 0 ? messageCount : ''" type="danger" :max="99" @click="onClickMessage">
        <el-tooltip class="box-item" effect="dark" content="通知" placement="bottom">
          <el-icon class="icon">
            <bell />
          </el-icon>
        </el-tooltip>
      </el-badge>
    </div>
    <div @click="onRefresh" style="cursor: pointer">
      <div class="el-badge">
        <el-tooltip class="box-item" effect="dark" content="刷新" placement="bottom">
          <el-icon><refresh-right /></el-icon>
        </el-tooltip>
      </div>
    </div>
    <div @click="jumpRoute('/sys/themeMana/index', true)" style="cursor: pointer" v-if="store.state.user.tenantCode === '10000'">
      <div class="el-badge">
        <el-tooltip class="box-item" effect="dark" content="商城主题" placement="bottom">
          <img style="width: 18px; height: 18px" src="https://oss.nyyyds.com/upload/20241212/ed2acf6b74814a7590d5ede43a8252e2.png" alt="" />
        </el-tooltip>
      </div>
    </div>
    <setting-sidebar></setting-sidebar>
    <!-- 属性导入/导出 -->
    <div>
      <el-divider style="margin: 0 12px 0 0" direction="vertical" />
      <el-button
        :loading="attributeExport.loading"
        type="info"
        @click="
          attributeExport.visible = true;
          attributeExport.ids = [];
        "
        >属性导出</el-button
      >
      <el-button @click="handleSyncData" type="primary">属性同步</el-button>
    </div>
    <div style="display: flex; justify-content: center; align-items: center">
      <el-image :src="props.headUrl" :alt="props.userName" style="width: 30px; height: 30px; border-radius: 50%; margin-top: 3px; margin-right: 5px">
        <template #error>
          <div class="image-slot">
            <el-image class="avatar" :src="settingStore.info.backendDefaultAvatar"></el-image>
          </div>
        </template>
      </el-image>
      <el-dropdown @command="onClickUserMenus">
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="/user/center">
              {{ store.state.user.tenantCode == 10000 ? t("ui.user.links.userCenter") : "商家中心" }}
            </el-dropdown-item>
            <el-dropdown-item command="/user/password">
              {{ t("ui.user.links.editPassword") }}
            </el-dropdown-item>
            <el-dropdown-item command="/login">
              {{ t("ui.user.links.logout") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
        <span class="el-dropdown-link" style="display: flex">
          {{ store.state.user.tenantCode == 10000 ? props.userName : props.nickname }}
          <el-icon class="el-icon--right" style="font-size: 14px"><arrow-down /></el-icon>
        </span>
      </el-dropdown>
    </div>
    <userpassword ref="userpasswordRef"></userpassword>

    <!-- 消息通知 -->
    <message-notification ref="messageNotificationRef" v-if="props.userName"></message-notification>

    <!-- 余额不足提示 -->
    <el-dialog @close="showRechargeTip = false" v-model="showRechargeTip" style="padding: 24px; border-radius: 8px" width="480" append-to-body :close-on-press-escape="false">
      <template #title>
        <div style="font-size: 18px; font-weight: bold; display: flex; align-items: center">
          <el-icon style="margin-right: 8px" color="#F56C6C"><InfoFilled /></el-icon>
          余额不足提醒
        </div>
      </template>
      <div style="display: flex; flex-direction: column; align-items: flex-end">
        <div style="line-height: 18px; margin: 12px 0; font-size: 14px; width: 100%">
          账户余额目前不足50元，请及时充值！<br />
          充值步骤：<br />
          1. 登录您的账号，进入插件市场-我的钱包；<br />
          2. 点击“账户余额”中的“充值”选项；<br />
          3.根据页面提示的方式完成充值操作。
        </div>
        <div><el-button @click="closeRechargeTip('1')">暂不提醒</el-button><el-button @click="rechargeHandle" type="primary">立即充值</el-button></div>
      </div>
    </el-dialog>

    <!-- 属性导出 -->
    <el-dialog @close="attributeExport.visible = false" v-model="attributeExport.visible" style="border-radius: 8px" title="属性导出" width="480" append-to-body :close-on-press-escape="false">
      <div class="cardDescriptions" style="padding: 0">
        <el-descriptions title="" :column="1" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label>
              <div>选择游戏<span style="color: red">*</span></div>
            </template>
            <el-select v-model="attributeExport.ids" multiple placeholder="请选择游戏">
              <el-option v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="attributeExport.visible = false">取消</el-button>
        <el-button type="primary" @click="handleExport">确认</el-button>
      </template>
    </el-dialog>
    <!-- 属性导入 -->
    <el-dialog @close="attributeImport.visible = false" v-model="attributeImport.visible" style="border-radius: 8px" title="属性同步" width="480" append-to-body :close-on-press-escape="false">
      <div class="cardDescriptions" style="padding: 0">
        <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>选择游戏</div> </template>
            <el-select v-model="attributeImport.ids" multiple clearable placeholder="请选择游戏">
              <el-option v-for="item in attributeImport.options" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button
          @click="
            attributeImport.visible = false;
            attributeImport.ids = [];
          "
          >取消</el-button
        >
        <el-button type="primary" @click="handleSubmitSync">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.tips {
  :deep(.el-tag__content) {
    display: flex;
    align-items: center;
  }
}
</style>
<style leng="less">
.primaryElNotice {
  .el-notification__icon {
    color: #3e8ef7;
  }
}
</style>
