<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <div class="mainBox">
      <div class="leftPart">
        <ul class="menuUl">
          <li :class="'menuLi ' + (state.activeName == item.label ? 'active' : '')" v-for="(item, itemIndex) in tabsList" :key="itemIndex" @click="changeTab(item.label)" :index="itemIndex">
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
      <div ref="rightPart" style="flex: 1">
        <template v-if="state.activeName == '手机号管理'"> <numberInfo /> </template>
        <template v-if="state.activeName == '卡板设备管理'"> <smsDevice /> </template>
        <template v-if="state.activeName == '短信接收记录'"> <smsListMana /> </template>
        <template v-if="state.activeName == '开卡人管理'"> <openCard /> </template>
        <template v-if="state.activeName == '短信发送记录'"> <smsSendRecords /> </template>
        <template v-if="state.activeName == '任务配置'"> <taskConfig /> </template>
        <template v-if="state.activeName == '任务管理'"> <taskMana /> </template>
        <template v-if="state.activeName == '手机互拨记录'"> <autoCallphone /> </template>
        <template v-if="state.activeName == '自动查话费'"> <autoCheckpay /> </template>
        <template v-if="state.activeName == 'Q绑查数量'"> <qBindNumber /> </template>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, onMounted, reactive, ref, toRefs, onUnmounted } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import numberInfo from "./components/numberInfo.vue"; // 号码信息管理
import smsDevice from "./components/smsDevice.vue"; // 短信设备管理
import smsListMana from "./components/smsListMana.vue"; // 短信列表管理
import openCard from "./components/openCard.vue"; // 开卡人管理
import smsSendRecords from "./components/smsSendRecords.vue"; // 短信发送记录
import taskConfig from "./components/taskConfig.vue"; // 任务配置
import taskMana from "./components/taskMana.vue"; // 任务管理
import autoCallphone from "./components/autoCallphone.vue"; // 手机互拨记录
import autoCheckpay from "./components/autoCheckpay.vue"; // 自动查话费
import qBindNumber from "./components/qBindNumber.vue"; // Q绑查数量

const route = useRoute();
const tabsList = ref([
  { label: "手机号管理", value: "手机号管理" },
  { label: "开卡人管理", value: "开卡人管理" },
  { label: "卡板设备管理", value: "卡板设备管理" },
  { label: "短信接收记录", value: "短信接收记录" },
  { label: "短信发送记录", value: "短信发送记录" }
  // { label: "任务配置", value: "任务配置" },
  // { label: "任务管理", value: "任务管理" },
  // { label: "手机互拨记录", value: "手机互拨记录" },
  // { label: "自动查话费", value: "自动查话费" },
  // { label: "Q绑查数量", value: "Q绑查数量" }
]);
const state = reactive({
  activeName: "手机号管理"
});
// 切换tab
const changeTab = (name?: any) => {
  state.activeName = name;
};

const rightPart = ref();
const getRightWidth = () => {
  let fatherBox = document.querySelector(".mainBox");
  rightPart.value.style.width = `${+fatherBox.offsetWidth - 212}px`;
};
onMounted(() => {
  if (route.query.activeName) {
    state.activeName = route.query.activeName;
  }
  getRightWidth();
  window.addEventListener("resize", getRightWidth); // 监听窗口大小变化
});
onUnmounted(() => {
  window.removeEventListener("resize", getRightWidth);
});
</script>

<style lang="less" scoped>
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.mainBox {
  display: flex;
  margin-top: 12px;
  .leftPart {
    margin-right: 24px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .menuUl {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      .menuLi {
        width: 186px;
        cursor: pointer;
        padding: 20px;
        word-break: keep-all;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #303133;
        line-height: 14px;
        &.active {
          background-color: var(--color-primary-light);
          color: var(--color-primary);
        }
      }
    }
  }
}
:deep(.el-tag) {
  border: 1px solid !important;
}
</style>
