<template>
  <div class="analysis_page">
    <div class="card_analysis mt-12" style="background: #f7f8fa">
      <div class="header_analysis" style="padding: 22px 20px 20px 20px">
        <div class="header_analysis_left flx-align-center">
          <div class="header_analysis_title" style="font-size: 20px; margin-left: 0px">商品三方对接明细</div>
        </div>
        <div class="header_analysis_right flx-align-center">
          <el-date-picker v-model="detailTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 220px; border-radius: 20px" @change="detailTimeChange" />
          <div class="analysis_type" style="margin-left: 10px">
            <div class="analysis_type_item" :class="{ active: dataForm.type == item.value }" v-for="(item, index) in dateTypeList" :key="index" @click="dateTypeChange(item)">{{ item.label }}</div>
          </div>
        </div>
      </div>
      <div style="padding: 0px 20px 20px 20px">
        <el-table :data="tableData" class="analysis_table" border cell-class-name="ch-56" @sort-change="sortChange">
          <el-table-column prop="department" align="center" width="120">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.deptId" :list="departmentList" placeholderColor="#303133" clearable isSolid :isBorder="false" labelKey="departmentName" valueKey="departmentId" placeholder="部门" @change="getDockingDetails"></NyDropdownMenu>
            </template>
            <template #default="{ row }">
              <span>{{ row.department || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="gameName" align="center" width="180">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.gameId" :list="gameList" placeholderColor="#303133" :isBorder="false" isSolid clearable labelKey="gameName" valueKey="gameId" placeholder="游戏" @change="getDockingDetails"></NyDropdownMenu>
            </template>
          </el-table-column>
          <el-table-column prop="shopCode" label="商品编号" align="center" />
          <el-table-column prop="part3" label="对接明细" width="500">
            <template #default="{ row }">
              <div class="docking_line">
                <el-scrollbar style="width: 380px">
                  <div class="line_left">
                    <template v-for="(item, index) in row.partnerInfoList" :key="index">
                      <el-tooltip effect="dark" :content="item.partnerName" placement="top-end">
                        <div class="partners">
                          <!-- <img src="/src/assets/images/colorTheme1.gif" alt="" style="border: 1px solid #606266;"/> -->
                          <div class="nameImg">{{ item.partnerName.substring(0, 2) }}</div>
                          <div class="selected" v-if="item.whetherPush">
                            <img src="/src/assets/images/yes1.svg" v-if="item.pushStatus == 1" alt="" style="width: 16px; height: 16px" />
                            <img src="/src/assets/images/yes2.svg" v-if="item.pushStatus == 2" alt="" style="width: 16px; height: 16px" />
                            <img src="/src/assets/images/no2.svg" v-if="item.pushStatus == 3" alt="" style="width: 16px; height: 16px" />
                          </div>
                          <div class="mask" v-else></div>
                        </div>
                      </el-tooltip>
                    </template>
                  </div>
                </el-scrollbar>

                <div class="line_right">
                  已推送：<span>{{ row.dockingCount || 0 }}</span
                  >/{{ row.partnerInfoListSize }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="employee" align="center" width="120">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.purchaseEmployeeId" placeholderColor="#303133" :list="employeeList" isSolid :isBorder="false" clearable labelKey="employeeName" valueKey="employeeId" placeholder="回收员工" @change="getDockingDetails"></NyDropdownMenu>
            </template>
          </el-table-column>
          <el-table-column prop="recyclingTime" label="回收时间" sortable="custom" width="180" align="center" />
          <el-table-column label="操作" align="center" width="80">
            <template #default="scope">
              <el-button type="primary" text bg @click="ViewDetails(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
        <el-pagination :current-page="dataForm.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="dataForm.size" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
      </div>
    </div>
  </div>
  <el-drawer v-model="drawer" title="推送明细" size="50%" class="drawer_shop">
    <PushDetails ref="PushDetailsRef" :dataForm="shopInfo"></PushDetails>
  </el-drawer>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import PushDetails from "./PushDetails.vue";
import { ref, reactive, onMounted } from "vue";

const drawer = ref(false);
const PushDetailsRef = ref();
const shopInfo = ref(<any>{});

const detailTime = ref();
const dataForm = ref({
  gameId: "",
  recyclingChannelId: "",
  purchaseEmployeeId: "",
  startTime: "",
  endTime: "",
  type: "0",
  deptId: "",
  page: 1,
  size: 10
});

const dateTypeList = [
  { label: "日", value: "1" },
  { label: "月", value: "2" }
];

const total = ref();

const departmentList = ref(); // 部门列表
const gameList = ref(); // 游戏列表
const employeeList = ref(); // 选择员工列表

// 部门列表
const getDepartmentSearchList = () => {
  baseService.get("/dataAnalysis/departmentSearchList").then((res) => {
    departmentList.value = res.data;
  });
};

// 游戏列表
const getGameList = () => {
  baseService.get("/dataAnalysis/gameSearchList").then((res) => {
    gameList.value = res.data;
  });
};

// 员工列表
const getEmployeeList = () => {
  baseService.get("/dataAnalysis/employeeSearchList").then((res) => {
    employeeList.value = res.data;
  });
};

// 时间区间筛选
const detailTimeChange = () => {
  dataForm.value.startTime = detailTime.value ? detailTime.value[0] + " 00:00:00" : "";
  dataForm.value.endTime = detailTime.value ? detailTime.value[0] + " 23:59:59" : "";
  dataForm.value.type = "0";
  getDockingDetails();
};

const dateTypeChange = (e: any) => {
  dataForm.value.type = e.value;
  detailTime.value = null;
  getDockingDetails();
};

const tableData = ref(<any>[]);

// 商品三方对接明细
const getDockingDetails = () => {
  baseService.post("/dataAnalysis/dockingDetails", dataForm.value).then((res) => {
    total.value = res.data.total;
    tableData.value = res.data.list;
  });
};

const sizeChange = (number: any) => {
  dataForm.value.size = number;
  getDockingDetails();
};

const currentChange = (number: any) => {
  dataForm.value.page = number;
  getDockingDetails();
};

const ViewDetails = (row: any) => {
  shopInfo.value = row;
  drawer.value = true;
};

// 排序事件
const sortChange = (column: any) => {
  if (column.order) {
    dataForm.value.orderItem = {
      column: column.prop,
      asc: column.order == "ascending" ? true : false
    };
  }else{
    delete dataForm.value.orderItem
  }
  getDockingDetails();
};

onMounted(() => {
  getDockingDetails();
  getDepartmentSearchList();
  getGameList();
  getEmployeeList();
});

const init = (form: any) => {
  Object.assign(dataForm.value, form);
  getDockingDetails();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.analysis_table_Menu {
  :deep(.placeholder) {
    font-weight: 600;
  }
  :deep(.clickValue) {
    font-weight: 600;
  }
}
.card_analysis {
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e6eb;

  .header_analysis {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 20px;

    .header_analysis_left {
      .header_analysis_title {
        font-weight: 500;
        font-size: 16px;
        color: #1d252f;
        line-height: 24px;
        margin-left: 4px;
      }
    }

    .header_analysis_right {
      .legend {
        margin-right: 16px;

        :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
          color: #1d2129;
        }

        .el-checkbox:nth-child(1) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #4165d7;
            border-color: #4165d7;
          }
        }

        .el-checkbox:nth-child(2) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #00b42a;
            border-color: #00b42a;
          }
        }

        .el-checkbox:nth-child(3) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #722ed1;
            border-color: #722ed1;
          }
        }
      }
    }
  }

  .header_describe {
    font-weight: 400;
    font-size: 13px;
    color: #4e5969;
    line-height: 22px;
    padding: 0px 20px;
  }

  .center_analysis {
    padding: 12px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .listMap {
      width: 200px;

      .listMap_label {
        span {
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          line-height: 22px;
          margin-right: 2px;
        }
      }

      .listMap_value {
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
      }
    }
  }
}

.analysis_type {
  display: flex;
  align-items: center;
  gap: 10px;
}

.analysis_type_item {
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  padding: 3px 12px;
  background: #ffffff;
  border-radius: 24px;
  border: 1px solid #d4d7de;
  cursor: pointer;
}

.active {
  background: #4165d7;
  color: #ffffff;
  border: 1px solid #4165d7;
}

.analysis_table {
  width: 100%;

  :deep(th .cell) {
    background: none !important;
    font-weight: bold;
    font-size: 14px;
    // color: #909399;
    line-height: 22px;
  }

  // :deep(th:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.1) !important;
  // }

  // :deep(td:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.05) !important;
  // }
}

.docking_line {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .line_left {
    display: flex;
    align-items: center;
    gap: 4px;
    // width: 480px;
    // overflow-x: auto;

    .partners {
      width: 32px;
      height: 40px;
      position: relative;

      img {
        width: 32px;
        height: 32px;
        border-radius: 6px;
      }

      .nameImg {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--el-color-primary);
        color: #fff;
        font-size: 12px;
        font-weight: bold;
      }

      .mask {
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        border-radius: 6px;
        width: 32px;
        height: 32px;
        top: 0;
        left: 0;
        z-index: 2;
      }

      .selected {
        position: absolute;
        left: 9px;
        bottom: -7px;
        z-index: 2;
      }
    }
  }

  .line_right {
    font-weight: 400;
    font-size: 12px;
    color: #303133;
    line-height: 14px;

    span {
      color: var(--el-color-primary);
    }
  }
}
</style>
<style lang="less">
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    padding: 12px;
    background-color: #f0f2f5;
  }
}
</style>
