<template>
  <div class="ny-no-data flx-column">
    <!-- <img v-if="type == '1'" :src="notDataImg1" /> -->
    <img v-if="type == '2'" :src="notDataImg2" />
    <div v-if="type == '2'" class="text">{{ text }}</div>
    <img v-if="type == '1'" style="width: 170px" src="@/components/ny-table/src/components/noMessage.png" alt="" />
    <img v-if="type == '3'" style="width: 170px;" src="@/components/ny-table/src/components/noResult.png" alt="" />
  </div>
</template>
<script setup lang="ts">
import notDataImg1 from "@/assets/images/im_not_data.png";
import notDataImg2 from "@/assets/images/msg_notification_nodata.png";

defineProps({
  type: {
    type: String,
    default: "1"
  },
  text: String
});
</script>

<script lang="ts">
export default {
  name: "NyNoData"
};
</script>

<style lang="less" scoped>
.ny-no-data {
  padding: 40px 0;
  align-items: center;
  justify-content: center;
  height: fit-content;

  .text {
    color: #909399;
    font-size: 12px;
    padding-top: 12px;
  }
}
</style>
