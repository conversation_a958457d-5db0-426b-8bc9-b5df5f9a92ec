<template>
  <el-dialog :footer="null" v-model="visible" :title="['', '发送短信', '手机号问题反馈'][+oparateType]" width="800">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <el-descriptions style="width: 100%" border :column="2">
        <template v-if="oparateType == 1">
          <el-descriptions-item label="发送手机号" :span="2">
            <template #label>
              <span>发送手机号<span style="color: red">*</span></span>
            </template>
            <el-form-item label="发送手机号" prop="deviceId">
              <el-select filterable remote :loading="phoneLoading" :remote-method="getPhoneList" clearable v-model="dataForm.deviceId" placeholder="请输入关键字搜索发送手机号">
                <el-option v-for="item in phonesList" :key="item.id" :label="item.phone" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="接收手机号" :span="2">
            <template #label>
              <span>接收手机号<span style="color: red">*</span></span>
            </template>
            <el-form-item label="接收手机号" prop="receivePhone">
              <el-select filterable remote :loading="receivePhoneLoading" :remote-method="getReceivePhoneList" @change="receivePhoneChange" clearable v-model="dataForm.receivePhone" placeholder="请输入关键字搜索接收手机号">
                <el-option v-for="item in receivePhoneList" :key="item.id" :label="item.type" :value="item.phone" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="短信内容" :span="2">
            <template #label>
              <span>短信内容<span style="color: red">*</span></span>
            </template>
            <el-form-item label="短信内容" prop="content">
              <el-input v-model="dataForm.content" placeholder="请输入短信内容" type="textarea" :rows="5"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </template>
        <template v-if="oparateType == 2">
          <el-descriptions-item label="异常手机号" :span="2">
            <template #label>
              <span>异常手机号<span style="color: red">*</span></span>
            </template>
            <el-form-item label="异常手机号" prop="phone">
              <el-input v-model="dataForm.phone" placeholder="请输入手机号" ></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="异常备注" :span="2">
            <template #label>
              <span>异常备注<span style="color: red">*</span></span>
            </template>
            <el-form-item label="异常备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="请输入异常备注" type="textarea" :rows="5"></el-input>
            </el-form-item> </el-descriptions-item
        ></template>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-dialog>
</template>
  
<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const rules = {
  deviceId: [{ required: true, message: "请选择发送手机号", trigger: "change" }],
  receivePhone: [{ required: true, message: "请选择接收手机号", trigger: "change" }],
  content: [{ required: true, message: "请输入短信内容", trigger: "blur" }],
  phone: [{ required: true, message: "请输入异常手机号", trigger: "change" }],
  remark: [{ required: true, message: "请输入异常备注", trigger: "change" }],
};

const visible = ref(false);
const oparateType = ref(1);
const dataForm = reactive({}  as IObject);



const init = (type?: any) => {
  visible.value = true;
  oparateType.value = type;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};


// 发送手机号列表
const phoneLoading = ref(false);
const phonesList = ref(<any>[]); 
const getPhoneList = (phone:any) =>{
  phoneLoading.value = true;
  baseService.get("/mobile/mobileCard/getPhoneList",{ phone: phone}).then(res=>{
    console.log(res.data);
    if(res.code == 0){
      phonesList.value = res.data
    }
  }).finally(()=>{
    phoneLoading.value = false;
  })
}

// 接收手机号列表
const receivePhoneLoading = ref(false);
const receivePhoneList = ref(<any>[]); 
const getReceivePhoneList = (phone:any) =>{
  receivePhoneLoading.value = true;
  baseService.get("/mobile/mobileSendMessage/getReceivePhoneList",{ info: phone,page:1,limit: 999}).then(res=>{
    console.log(res)
    if(res.code == 0){
      receivePhoneList.value = res.data
      console.log();
      
    }
  }).finally(()=>{
    receivePhoneLoading.value = false;
    console.log('=sldkjfdslkjfsdlkfjsdlfjlsdfjl');
    
  })
}

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      
      // 发送短信
      if(oparateType.value == 1){
        dataForm.sendPhone = phonesList.value.find((item:any) => item.id == dataForm.deviceId).phone
        dataForm.type = receivePhoneList.value.find((item:any) => item.phone == dataForm.receivePhone).type
        console.log(dataForm,'==== dataForm.value =====');
        baseService.post("/mobile/mobileSendMessage/sendMessage",dataForm).then(res=>{
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            // emit("refreshDataList");
          }
        }).finally(() => {
          btnLoading.value = false;
        });
      }
      // 问题反馈
      if(oparateType.value == 2){
        baseService.post("/mobile/mobileProblemFeedback", dataForm)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emit("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
      }
    }
  });
};

const receivePhoneChange = (value: any) =>{
  const info = receivePhoneList.value.find((item:any) => item.phone == value);
  dataForm.content = info.sendMessage;
}

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>
  