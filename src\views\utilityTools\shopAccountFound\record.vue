<template>
  <div class="TableXScrollSty">
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header-right>
        <div>
          <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px; margin-left: 12px" />
        </div>
        <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
        <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
      </template>
      <template #executionStatus="{ row }">
        <el-tag type="success" v-if="row.executionStatus == '2'">执行成功</el-tag>
        <el-tag type="warning" v-if="row.executionStatus == '1'">执行中</el-tag>
        <el-tag type="danger" v-if="row.executionStatus == '3'">执行失败</el-tag>
      </template>
      <template #startTime="{ row }">
        {{ formatTimeStamp(row.startTime) }}
      </template>
      <template #endTime="{ row }">
        {{ formatTimeStamp(row.endTime) }}
      </template>
      <template #updateDate="{ row }">
        {{ formatTimeStamp(row.updateDate) }}
      </template>
      <template #operation="{ row }">
        <el-button type="primary" text bg @click="toExamine(row)">详情</el-button>
      </template>
    </ny-table>
    <recordDetail ref="recordDetailRef" :key="recordDetailKey" />
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import recordDetail from "./recordDetail.vue";
const timeInterval = ref(); // 时间区间
const view = reactive({
  getDataListURL: "/shop/sysAccountFoundParent/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    orderField: "create_date", // 排序字段
    order: "desc", // 排序方式
    startTime: undefined, // 开始时间
    endTime: undefined // 结束时间
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格设置
const columns = reactive([
  {
    prop: "startTime",
    label: "排查开始时间",
    minWidth: "120"
  },
  {
    prop: "endTime",
    label: "排查结束时间",
    minWidth: "140"
  },
  {
    prop: "recoveredNumber",
    label: "被找回",
    minWidth: "140"
  },
  {
    prop: "soldNumber",
    label: "被转手",
    minWidth: "120"
  },
  {
    prop: "dropNumber",
    label: "掉绑",
    minWidth: "200"
  },
  {
    prop: "executionStatus",
    label: "执行状态",
    minWidth: "120"
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: "120"
  },
  {
    prop: "operation",
    label: "操作",
    minWidth: "100"
  }
]);
// 查询
const queryFn = () => {
  view.dataForm.startTime = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.endTime = timeInterval.value ? timeInterval.value[1] : "";
  state.getDataList();
};
// 重置
const resetFn = () => {
  view.dataForm.startTime = "";
  view.dataForm.endTime = "";
  timeInterval.value = "";
  state.getDataList();
};

// ----------详情
const recordDetailRef = ref();
const recordDetailKey = ref(1);
const toExamine = (row: any) => {
  recordDetailKey.value++;
  nextTick(() => {
    recordDetailRef.value.init(row);
  });
};
onMounted(() => {});
</script>

<style lang="less" scoped>
:deep(.shopTitleSty) {
  .el-link__inner {
    width: inherit;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    display: block;
    padding-right: 20px;
  }
  width: inherit;
}
.el-tag {
  border: 1px solid;
  &.noBorder {
    border: 1px dotted;
  }
}
</style>
