<template>
    <operation-log 
        ref="operationRef" 
        :show="show" 
        @close="show=false" 
        title="收集包赔" >   
        <template #default>
            <div class="text">
                <p>将此地址复制后发送给对方，在浏览器打开即可填写包赔信息，对方提交包赔信息后，可点击包赔信息下查看按钮进行查看</p>
            </div>
        </template>

        <template #footer>
            <el-button @click="show=false">取消</el-button>
            <el-button type="primary" v-copy="link">复制地址</el-button>
        </template>
    </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose } from "vue";
import { useSettingStore } from "@/store/setting";
import OperationLog from "../../components/SecondaryConfirmation.vue";

const show = ref(false);
const link = ref("");
const settingStore = useSettingStore();
const init = (data: any) => {
    show.value = true;
    link.value = `${settingStore.info.websiteUrl}compensation?userId=${data.tbUserId || ''}&orderId=${data.id}&collectType=0`
}

defineExpose({
    init
})

</script>