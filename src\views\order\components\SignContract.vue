<template>
  <operation-log ref="operationRef" :show="show" :showFooter="false" @close="show = false" title="签署合同" type="primary">
    <template #default>
      <div v-if="!showBtn" style="padding-bottom: 30px">
        <el-select style="margin-bottom: 20px; width: 100%" v-model="propObj.templateId" required placeholder="请选择合同模板(必选)" size="large">
          <el-option v-for="item in options" :key="item.id" :label="item.templateName" :value="item.id" />
        </el-select>
        <el-select style="margin-bottom: 50px; width: 100%" v-model="propObj.bestsignUserId" required placeholder="请选择合同签署主体(必选)" size="large">
          <el-option v-for="item in options_" :key="item.id" :label="item.bestsignUserName" :value="item.id" />
        </el-select>
        <div style="display: flex; justify-content: flex-end">
          <el-button v-if="propObj.bestsignContractId" @click="showBtn = true">取消</el-button>
          <el-button v-loading="loading" @click="creatUrl" type="primary">点击生成签署合同</el-button>
        </div>
      </div>
      <div class="text" v-else>
        <p style="margin-bottom: 20px">{{ propObj.contractType == "PURCHASE_CONTRACT" ? "卖家签署合同" : "买家签署合同" }}</p>
        <el-button v-if="failResult" @click="show = false" type="danger">生成失败！</el-button>
        <el-button v-else @click="openUrl" type="primary">点击复制{{ propObj.contractType == "PURCHASE_CONTRACT" ? "卖家" : "买家" }}签署合同链接地址</el-button>
        <el-button v-if="failResult || propObj.bestsignContractId" @click="showBtn = false"> 重新生成签署合同 </el-button>
      </div>
    </template>
  </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose } from "vue";
import OperationLog from "./SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import useClipboard from "vue-clipboard3";
const emit = defineEmits(["refreshdata"]);
const propObj = ref();
const show = ref(false);
const loading = ref(false);
const showBtn = ref(false);
const failResult = ref(false);
const options = ref([]);
const options_ = ref([]);
const sourceType = ref("");
const init = (obj: any, source?: any) => {
  show.value = true;
  propObj.value = {}; //重置下
  sourceType.value = source;
  showBtn.value = obj.bestsignContractId ? true : false;
  propObj.value = obj;
  propObj.value.url = obj.bestsignContractId ? (obj.signLink ? obj.signLink : obj.auditLink) : undefined;
  propObj.value.templateId = undefined;
  options.value = [];
  failResult.value = false;
  options_.value = [];
  baseService.get("/bestsign/bestsignUserPage", { limit: 9999, bestsignUserType: 2 }).then((res) => {
    options_.value = res.data.list || [];
  });
  baseService.get("/bestsign/listTemplateByType/" + (propObj.value.contractType == "PURCHASE_CONTRACT" ? 0 : 1)).then((res) => {
    options.value = res.data || [];
  });
};
const creatUrl = () => {
  if (!propObj.value.templateId) {
    ElMessage.warning("请选择模板！");
    return;
  }
  if (!propObj.value.bestsignUserId) {
    ElMessage.warning("请选择主体！");
    return;
  }
  loading.value = true;
  var params: any = "";
  if (propObj.value.contractType == "PURCHASE_CONTRACT") {
    params = {
      orderId: propObj.value.orderId,
      contractType: propObj.value.contractType,
      templateId: propObj.value.templateId,
      bestsignUserId: propObj.value.bestsignUserId
    };
  } else {
    params = {
      orderId: propObj.value.orderId,
      contractType: propObj.value.contractType,
      templateId: propObj.value.templateId,
      bestsignUserId: propObj.value.bestsignUserId
    };
  }
  baseService
    .post(propObj.value.bestsignContractId ? "/bestsign/reCreateContract" : "/bestsign/createContract", params)
    .then((res: any) => {
      showBtn.value = true;
      loading.value = false;
      propObj.value.url = res.data;
      emit("refreshdata", propObj.value.url);
      if (sourceType.value == "im") show.value = false;
    })
    .catch(() => {
      showBtn.value = true;
      loading.value = false;
      failResult.value = true;
    });
};
const { toClipboard } = useClipboard();
const openUrl = async () => {
  try {
    await toClipboard(propObj.value.url);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};
defineExpose({
  init
});
</script>
