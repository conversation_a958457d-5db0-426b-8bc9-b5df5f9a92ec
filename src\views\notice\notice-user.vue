<template>
    <div class="mod-demo__sysnoticeuser">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header-right>
                    <ny-select v-model="state.dataForm.noticeType" dict-type="notice_type" style="width: 200px;" :placeholder="$t('notice.type')"></ny-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
                <!-- 类型 -->
                <template #noticeType="{row}">
                    {{ state.getDictLabel("notice_type", row.noticeType) }}
                </template>

                <!-- 阅读状态 -->
                <template #readStatus="{row}">
                    <el-tag v-if="row.readStatus === 0" type="danger">{{ $t("notice.readStatus0") }}</el-tag>
                    <el-tag v-else type="success">{{ $t("notice.readStatus1") }}</el-tag>
                </template>
                
                <!-- 操作 -->
                <template #operation="{row}">
                    <el-button type="primary" text bg @click="viewHandle(row)">{{ $t("notice.view")}}</el-button>
                </template>

            </ny-table>

        </el-card>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, toRefs } from "vue";
import { IObject } from "@/types/interface";
import baseService from "@/service/baseService";
import { registerDynamicToRouterAndNext } from "@/router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const view = reactive({
    getDataListURL: "/sys/notice/mynotice/page",
    getDataListIsPage: true,
    dataForm: {
        noticeType: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        prop: "title",
        label: "标题",
        minWidth: 250
    },
    {
        prop: "noticeType",
        label: "类型",
        minWidth: 100
    },
    {
        prop: "senderName",
        label: "发送者",
        minWidth: 100
    },
    {
        prop: "senderDate",
        label: "发送时间",
        minWidth: 150,
        sortable: true
    },
    {
        prop: "readStatus",
        label: "阅读状态",
        minWidth: 100
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.noticeType = "";
    state.getDataList();
}

const viewHandle = (row: IObject) => {
    // 路由参数
    const routeParams = {
        path: "/notice/notice-user-view",
        query: {
            id: row.id,
            _mt: t("notice.view2")
        }
    };

    // 如果未读，则标记为已读
    if (row.readStatus === 0) {
        updateReadStatus(row.id);
    }

    // 动态路由
    registerDynamicToRouterAndNext(routeParams);
};

const updateReadStatus = (noticeId: string) => {
    baseService.put("/sys/notice/mynotice/read/" + noticeId).then(() => {
        //
    });
};
</script>
