<template>
    <div class="mod-sys__menu" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:menu:save')" type="primary"  @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                </template>

                <!-- 图标 -->
                <template #icon="scope">
                    <svg class="iconfont" aria-hidden="true">
                        <use :xlink:href="`#${scope.row.icon}`"></use>
                    </svg>
                </template>

                <!-- 类型 -->
                <template #menuType="scope">
                    <el-tag v-if="scope.row.menuType === 0">{{ $t("menu.type0") }}</el-tag>
                    <el-tag v-else type="info">{{ $t("menu.type1") }}</el-tag>
                </template>

                <!-- 打开方式 -->
                <template #openStyle="scope">
                    <span v-if="scope.row.menuType !== 0"></span>
                    <el-tag v-else-if="scope.row.openStyle === 1">{{ $t("menu.openStyle1") }}</el-tag>
                    <el-tag v-else type="info">{{ $t("menu.openStyle0") }}</el-tag>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button v-if="state.hasPermission('sys:menu:save') && scope.row.menuType === 0"
                        type="primary" text bg @click="addHandle(scope.row)">{{ $t("add") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:menu:update')" type="primary" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:menu:delete')" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
            
        </el-card>

        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./menu-add-or-update.vue";

const view = reactive({
    getDataListURL: "/sys/menu/list",
    deleteURL: "/sys/menu"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        prop: "name",
        label: "名称",
        minWidth: 160
    },
    {
        prop: "icon",
        label: "图标",
        minWidth: 100,
    },
    {
        prop: "menuType",
        label: "类型",
        minWidth: 100
    },
    {
        prop: "openStyle",
        label: "打开方式",
        minWidth: 100
    },
    {
        prop: "sort",
        label: "排序",
        minWidth: 80
    },
    {
        prop: "url",
        label: "路由",
        minWidth: 140
    },
    {
        prop: "permissions",
        label: "权限标识",
        minWidth: 160
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 200
    }
])

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};

const addHandle = (row: any) => {
    addOrUpdateRef.value.init2(row);
};
</script>
