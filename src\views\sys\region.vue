<template>
	<div class="mod-sys__region" style="margin-top: 12px;">
		<el-card shadow="never" class="rr-view-ctx-card">
			<ny-table 
                :state="state" 
                :columns="columns"
				:lazy="true"
				:load="load"
				hasChildren="hasChildren"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:region:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                </template>
				
				<!-- 区域类型 -->
				<template #treeLevel="scope">
					<span v-if="scope.row.treeLevel === 1">{{ $t("region.province") }}</span>
					<span v-else-if="scope.row.treeLevel === 2">{{ $t("region.city") }}</span>
					<span v-else>{{ $t("region.county") }}</span>
				</template>

				<!-- 操作 -->
				<template #operation="scope">
					<el-button v-if="state.hasPermission('sys:region:update')" type="primary" text bg
						@click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
					<el-button v-if="state.hasPermission('sys:region:delete')" type="danger" text bg
						@click="deleteRow(scope.row)">{{ $t("delete") }}</el-button>
				</template>

			</ny-table>
			
		</el-card>

		<!-- 弹窗, 新增 / 修改 -->
		<add-or-update ref="addOrUpdateRef" @refreshDataList="refreshData"></add-or-update>
	</div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./region-add-or-update.vue";
import baseService from "@/service/baseService";
import { IObject, IFunction } from "@/types/interface";

const elTableRef = ref();

const view = reactive({
	getDataListURL: "/sys/region/list",
	deleteURL: "/sys/region",
	nodeMaps: new Map()
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
		prop: "name",
		label: "区域名称",
		minWidth: 150
	},
    {
        prop: "id",
        label: "区域标识",
        minWidth: 120
    },
    {
        prop: "treeLevel",
        label: "区域类型",
        minWidth: 120
    },
    {
        prop: "sort",
        label: "排序",
        minWidth: 100
    },
	{
        prop: "updateDate",
        label: "更新时间",
        minWidth: 150,
		sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

const load = (tree: IObject, treeNode: IObject, resolve: IFunction) => {
	state.nodeMaps.set(tree.id, { tree, treeNode, resolve });
	baseService.get(`/sys/region/list?pid=${tree.id}`).then((res) => {
		resolve(res.data);
	});
};

const deleteRow = (row: IObject) => {
	const { id, pid } = row;
	state.deleteHandle(id).then(() => {
		elTableRef.value.store.states.lazyTreeNodeMap["pid"] = [];

		const { tree, treeNode, resolve } = state.nodeMaps.get(pid) || {};
		if (tree) {
			load(tree, treeNode, resolve);
		} else {
			state.getDataList();
		}
	});
};

const refreshData = (row: IObject) => {
	const { pid } = row;
	const { tree, treeNode, resolve } = state.nodeMaps.get(pid) || {};
	if (tree) {
		load(tree, treeNode, resolve);
	} else {
		state.getDataList();
	}
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: string) => {
	addOrUpdateRef.value.init(id);
};
</script>
