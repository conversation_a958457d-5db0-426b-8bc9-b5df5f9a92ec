<template>
  <el-radio-group v-model="modelValue" :disabled="disabled" @change="$emit('update:modelValue', $event)">
    <el-radio :value="data[value]" v-for="data in dataList" :key="data[label]" border>{{ data[label] }}</el-radio>
  </el-radio-group>
</template>
<script lang="ts">
import { getDictDataList } from "@/utils/utils";
import { IObject } from '@/types/interface';
import { defineComponent, computed, PropType } from 'vue';
import { useAppStore } from "@/store";
export default defineComponent({
  name: "NyRadioGroup",
  props: {
    modelValue: {
      type: [Number, String] as PropType<number | string>,
      required: true
    },
    dictType: {
      type: String,
      required: false
    },
    list: {
      type: Array as PropType<IObject[]>,
      required: false
    },
    value: {
      type: String,
      required: false,
      default: 'dictValue'
    },
    label: {
      type: String,
      required: false,
      default: 'dictLabel'
    },
    disabled: {
      type: Object,
      default: false
    }
  },
  setup(props) {
    const store = useAppStore();

    const dataList = computed(() => {
      if (props.dictType) {
        return getDictDataList(store.state.dicts, props.dictType);
      } else if (props.list) {
        return props.list;
      }
      return [];
    });
    
    return {
      modelValue: computed(() => `${props.modelValue}`),
      dataList
    };
  }
});
</script>
