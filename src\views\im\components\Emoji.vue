<template>
    <!-- emoji -->
    <el-scrollbar class="im-emoji-scrollbar" :class="{ show: showEmoji }">
        <div class="im-emoji-wrap">
            <div 
                class="emoji-item" 
                v-for="(item, index) in emojiList" 
                :key="index" 
                :title="item.symbol"
                @click="$emit('selectEmoji', item.emoji)"
            >
                {{ item.emoji }}
            </div>
        </div>
    </el-scrollbar>
</template>

<script lang="ts" setup>
    import { ref, onMounted, defineProps } from 'vue';
    import { imEmoji } from '@/utils/imTool';

    defineProps({
        showEmoji: {
            type: Boolean,
            default: false
        }
    })

    const emojiList = ref(<any>[]);
    
    onMounted(() => {
        emojiList.value = imEmoji.getList();
        emojiList.value.splice(3, 1);
    })
    
</script>

<style lang="scss" scope>
    .im-emoji-scrollbar{
        position: absolute;
        height: 0;
        opacity: 0;
        top: -120px;
        background: #fff;
        transition: opacity .3s;
        

        &.show{
            height: 120px;
            opacity: 1;
        }
    }
    .im-emoji-wrap{
        display: flex;
        flex-wrap: wrap;
        

        .emoji-item{
            width: 40px;
            height: 40px;
            line-height: 40px;
            font-size: 20px;
            text-align: center;
            cursor: pointer;
            
            &:hover{
                background: #E6E6E6;
                border-radius: 4px;
            }
        }
    }
</style>