<template>
  <el-dialog v-model="visible" width="500" :title="(!dataForm.id ? $t('add') : '编辑') + '快捷消息'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item label="选择游戏" prop="gameId">
        <el-select v-model="dataForm.gameId" filterable placeholder="请选择游戏" @change="changeGame">
          <el-option v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="快捷消息1" prop="msg1">
        <el-input placeholder="请输入快捷消息" v-model="dataForm.msg1"></el-input>
      </el-form-item>
      <el-form-item label="快捷消息2" prop="msg2">
        <el-input placeholder="请输入快捷消息" v-model="dataForm.msg2"></el-input>
      </el-form-item>
      <el-form-item label="快捷消息3" prop="msg3">
        <el-input placeholder="请输入快捷消息" v-model="dataForm.msg3"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  msg1: "",
  msg2: "",
  msg3: "",
  gameId: "",
  gameName: ""
});
const gameList = ref([]);
const rules = {
  gameId: [{ required: true, message: "请选择游戏", trigger: "blur" }]
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/im/imquickphrases/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};
const changeGame = () => {
  if (dataForm.gameId) {
    let obj = gameList.find((ele) => ele.id == dataForm.gameId);
    dataForm.gameName = obj.title;
  } else {
    dataForm.gameName = "";
  }
};
// 获取游戏下拉
const getgameList = () => {
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    gameList.value = res.data;
  });
};
getgameList();
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/im/imquickphrases", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: inherit;
  margin-bottom: 12px;
  .el-form-item__label {
    font-weight: 400;
    font-size: 13px;
    color: #606266;
    line-height: 22px;
  }
  .el-checkbox {
    margin-right: 24px;
  }
  :deep(.el-checkbox__inner) {
    border-radius: 4px;
  }
}
</style>
