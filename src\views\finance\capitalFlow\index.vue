<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '订单统计', value: '1' },
          { label: '保证金统计', value: '2' }
        ]"
        v-model="state.tabType"
        value="value"
        label="label"
        @change="changeTab"
      ></ny-flod-tab>
      <template v-if="state.tabType == '1'">
        <ul class="headCardUl">
          <li class="headCardLi" v-for="(cardItem, index) in cardInfo" :key="index">
            <!-- 头部 -->
            <div class="head flex">
              <div class="flex">
                <img src="@/assets/images/billingInformationIcon.png" style="width: 16px; height: 16px; margin-right: 4px" v-if="index == 0" />
                <img src="@/assets/images/flowIcon1.png" style="width: 16px; height: 16px; margin-right: 4px" v-else-if="index == 1" />
                <img src="@/assets/images/flowIcon2.png" style="width: 16px; height: 16px; margin-right: 4px" v-else-if="index == 2" />
                <el-icon v-else style="margin-right: 4px">
                  <Histogram color="#F56C6C" v-if="index == 3" />
                  <Histogram color="#67C23A" v-if="index == 4" />
                </el-icon>

                <span>{{ cardItem.top.label }}</span>
              </div>
              <div>
                <el-date-picker
                  :clearable="false"
                  :style="{ width: cardItem.top.width }"
                  v-model="cardItem.top.dateValue"
                  :type="cardItem.top.type"
                  :value-format="cardItem.top.type == 'month' ? 'YYYY-MM' : 'YYYY-MM-DD'"
                  :format="cardItem.top.type == 'month' ? 'MM月' : 'YYYY-MM-DD'"
                  @change="getStatistics(index, cardItem.top.dateValue)"
                />
              </div>
            </div>
            <!-- 数值 -->
            <div class="middle flex">
              <div style="width: 50%" v-for="(midelItem, mI) in cardItem.middle" :key="mI">
                <el-statistic :value="midelItem.data" :precision="2" />
              </div>
              <div v-if="index == 2">
                <el-tooltip placement="top">
                  <template #content>
                    直售利润 = 成交价 - 回收支出 <br />
                    代售利润 = 包赔费 + 手续费
                  </template>
                  <el-icon size="16" color="#E6A23C"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </div>
            <!-- 尾部 -->
            <div class="bottom flex" v-show="cardItem.bottom.show">
              <span>{{ cardItem.bottom.tip }}</span>
              <el-statistic :value-style="'font-weight: 400;font-size: 12px;height: 20px;display: -webkit-box;color:' + cardItem.bottom.color" :value="cardItem.bottom.data" :precision="2" />
            </div>
          </li>
        </ul>
        <template v-for="tableIndex in [0, 1]">
          <ny-table
            cellHeight="ch-40"
            :routePath="'/finance/capitalFlow/index' + tableIndex"
            :showColSetting="false"
            :key="tableIndex"
            v-if="state.dataForm.billType == tableIndex"
            :state="state"
            :columns="tableColums[tableIndex]"
            @pageSizeChange="state.pageSizeChangeHandle"
            @pageCurrentChange="state.pageCurrentChangeHandle"
            @selectionChange="state.dataListSelectionChangeHandle"
            @sortableChange="sortableChange"
          >
            <template #header>
              <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.billType" @change="getResetting"></ny-button-group>
            </template>

            <template #header-right>
              <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                <el-form-item>
                  <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
                </el-form-item>
                <el-form-item>
                  <el-select v-model="state.dataForm.orderType" placeholder="订单类型" clearable>
                    <el-option v-for="(item, index) in orderTypeList[tableIndex]" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item>
                <el-select v-model="state.dataForm.tradeType" placeholder="交易类型" clearable>
                  <el-option v-for="(item, index) in feedbackType[tableIndex]" :key="index" :label="item.label" :value="item.label"></el-option>
                </el-select>
              </el-form-item> -->
                <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
              </el-form>
            </template>

            <template #header-custom>
              <div class="mb-12">
                <el-button v-if="state.hasPermission('finance:billingInformation:export')" type="primary" @click="getExport">{{ $t("export") }}</el-button>
              </div>
            </template>

            <!-- 订单编号 -->
            <template #sn="{ row }">
              <div class="linkSty">
                <el-link :underline="false" v-copy="row.sn"
                  >{{ row.sn }}<el-icon class="copyIcon"><DocumentCopy /></el-icon>
                </el-link>
              </div>
            </template>

            <!-- 订单利润 -->
            <template #orderType="{ row }">{{ row.orderType || row.orderType === 0 ? orderTypeList[0][+row.orderType].label : "-" }} </template>

            <!-- 支付方式 -->
            <template #paymentType="{ row }">
              <span v-if="row.paymentType == 1">支付宝</span>
              <span v-if="row.paymentType == 2">微信</span>
              <span v-if="row.paymentType == 3">银行卡</span>
              <span v-if="row.paymentType == 4">余额</span>
              <span v-if="!row.paymentType">-</span>
            </template>
            <template #footer>
              <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
                <span style="font-weight: bold; color: #1d2129">{{ tableIndex == 0 ? "交易金额（元）" : "支出金额（元）" }}</span>
                <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
                <span>合计={{ getSummaries(tableIndex) }}</span>
              </div>
            </template>
          </ny-table>
        </template>
      </template>
      <template v-if="state.tabType == '2'"> <marginStatistics ref="marginStatisticsRef"></marginStatistics></template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search, DocumentCopy } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import marginStatistics from "./marginStatistics.vue";

// 表格配置项
const tableColums = reactive({
  0: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "sn",
      label: "订单编号",
      minWidth: 180
    },
    {
      prop: "shopCode",
      label: "商品编号",
      minWidth: 113
    },
    {
      prop: "orderType",
      label: "订单类型",
      minWidth: 144
    },
    {
      prop: "payer",
      label: "付款方",
      minWidth: 144
    },
    {
      prop: "saleUserName",
      label: "销售人",
      minWidth: 136
    },
    {
      prop: "dealTime",
      label: "交易时间",
      minWidth: 170
    },
    {
      prop: "paymentType",
      label: "支付方式",
      width: 124
    },
    {
      prop: "totalAmount",
      label: "交易金额(元)",
      width: 136,
      sortable: "custom"
    }
  ],
  1: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "sn",
      label: "订单编号",
      minWidth: 180
    },
    {
      prop: "shopCode",
      label: "商品编号",
      minWidth: 113
    },
    {
      prop: "orderType",
      label: "订单类型",
      minWidth: 133
    },
    {
      prop: "payee",
      label: "收款方",
      minWidth: 140
    },
    {
      prop: "handleUserName",
      label: "处理人",
      minWidth: 155
    },
    {
      prop: "dealTime",
      label: "交易时间",
      minWidth: 155
    },
    {
      prop: "paymentType",
      label: "支付方式",
      width: 133
    },
    {
      prop: "amount",
      label: "支出金额(元)",
      width: 133,
      sortable: "custom"
    }
  ]
});
const cardInfo = reactive([
  {
    top: {
      img: "",
      label: "今日交易金额(元)",
      type: "date", //时间检索类型,
      dateValue: undefined,
      width: "112px"
    },
    middle: [
      {
        tip: "",
        data: 0
      }
    ],
    bottom: {
      show: false,
      tip: "订单利润：",
      data: 0,
      color: "#4165D7"
    }
  },
  {
    top: {
      img: "",
      label: "今日售后赔付(元)",
      type: "date", //时间检索类型,
      dateValue: undefined,
      width: "112px"
    },
    middle: [
      {
        tip: "",
        data: 0
      }
    ],
    bottom: {
      show: false,
      tip: "已支付：",
      data: 0,
      color: "#67C23A"
    }
  },
  {
    top: {
      img: "",
      label: "今日真实利润(元)",
      type: "date", //时间检索类型,
      dateValue: undefined,
      width: "112px"
    },
    middle: [
      {
        tip: "",
        data: 0
      }
    ],
    bottom: {
      show: true,
      tip: "回收支出：",
      data: 0,
      color: "#E6A23C"
    }
  },
  {
    top: {
      img: "",
      label: "月收入统计(元)",
      type: "month", //时间检索类型,
      dateValue: undefined,
      width: "70px"
    },
    middle: [
      {
        tip: "",
        data: 0
      }
    ],
    bottom: {
      show: true,
      tip: "本年度总收入金额：",
      data: 0,
      color: "#F56C6C"
    }
  },
  {
    top: {
      img: "",
      label: "月支出统计(元)",
      type: "month", //时间检索类型,
      dateValue: undefined,
      width: "70px"
    },
    middle: [
      {
        tip: "",
        data: 0
      }
    ],
    bottom: {
      show: true,
      tip: "本年度总支出金额：",
      data: 0,
      color: "#67C23A"
    }
  }
]);
const defaultDate = ref();
const view = reactive({
  getDataListURL: "/flowable/capitalrecord/page",
  getDataListIsPage: true,
  deleteURL: "/sub/bargain/delete",
  deleteIsBatch: true,
  dataForm: {
    billType: "0",
    tradeType: "",
    orderType: "",
    start: "",
    end: "",
    order: "",
    orderField: ""
  },
  tabType: "1"
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 重置操作
const getResetting = () => {
  state.dataForm.tradeType = "";
  state.dataForm.orderType = "";
  state.dataForm.start = "";
  state.dataForm.end = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "收入流水", value: 0 },
  { label: "支出流水", value: 1 }
];

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};

const orderTypeList = ref({
  0: [
    { label: "回收订单", value: "0" },
    { label: "销售订单", value: "1" },
    { label: "售后订单", value: "2" },
    { label: "其他", value: "3" }
  ],
  1: [
    { label: "回收订单", value: "0" },
    { label: "销售订单", value: "1" },
    { label: "售后订单", value: "2" }
  ]
});
const feedbackType = ref({
  0: [{ label: "商品收入", value: "0" }],
  1: [
    { label: "商品支付", value: "1" },
    { label: "已售退款", value: "2" }
  ]
});
const getStatistics = (index: any, date: any) => {
  // NOTE: 空值的时候置默认值
  if (!date) {
    cardInfo[index].top.dateValue = cardInfo[index].top.type == "date" ? defaultDate.value[0] : defaultDate.value[1];
    date = cardInfo[index].top.type == "date" ? defaultDate.value[0] : defaultDate.value[1];
  }

  let url = ["/flowable/capitalrecord/getTransactionAmountByDate", "/flowable/capitalrecord/getAfterSaleCompensate", "/flowable/capitalrecord/getRealProfit", "/flowable/capitalrecord/getMonthlyIncome", "/flowable/capitalrecord/getMonthlyExpense"];
  let param = index < 3 ? { date } : { month: date };
  baseService.get(url[index], param).then((res) => {
    if (res) {
      if (index == 0) {
        cardInfo[index].middle[0].data = res.data?.transactionAmount || 0;
        cardInfo[index].bottom.data = res.data?.orderProfit || 0;
      } else if (index == 1) {
        cardInfo[index].middle[0].data = res.data?.saleAfterAmount || 0;
      } else if (index == 2) {
        cardInfo[index].middle[0].data = res.data?.income || 0;
        cardInfo[index].bottom.data = res.data?.outcome || 0;
      } else if (index == 3) {
        cardInfo[index].middle[0].data = res.data?.monthIncome || 0;
        cardInfo[index].bottom.data = res.data?.yearIncome || 0;
      } else if (index == 4) {
        cardInfo[index].middle[0].data = res.data?.monthOutcome || 0;
        cardInfo[index].bottom.data = res.data?.yearOutcome || 0;
      }
    }
  });
};
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 导出
const getExport = () => {
  baseService.get("/flowable/capitalrecord/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, state.dataForm.billType == "0" ? "订单统计-收入流水" : "订单统计-支出流水");
    }
  });
};
const marginStatisticsRef = ref();
const changeTab = () => {
  if (state.tabType == "1") {
    state.getDataList();
  } else {
    nextTick(() => {
      marginStatisticsRef.value.getResetting();
    });
  }
};
// 合计行计算函数
const getSummaries = (param: any) => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.totalAmount && param == 0) total = +item.totalAmount + total;
    if (item.amount && param == 1) total = +item.amount + total;
  });
  return total.toFixed(2);
};
onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  let month = now.getMonth() + 1; // 月份从0开始，需要加1
  let day = now.getDate();
  month = +month > 9 ? month : "0" + month;
  day = +day > 9 ? day : "0" + day;
  defaultDate.value = [year + "-" + month + "-" + day, year + "-" + month];
  for (let index = 0; index < 5; index++) {
    cardInfo[index].top.dateValue = cardInfo[index].top.type == "date" ? defaultDate.value[0] : defaultDate.value[1];
    getStatistics(+index, cardInfo[index].top.dateValue);
  }
});
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: cenetr;
}

.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}

.headCardUl {
  display: flex;
  align-items: cenetr;
  flex-wrap: nowrap;
  padding: 0;
  list-style: none;

  .headCardLi {
    width: 20%;
    margin-right: 20px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;
    list-style: none;

    &:last-child {
      margin-right: 0;
    }

    .head {
      justify-content: space-between;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 20px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
      :deep(.el-input),
      :deep(.el-date-editor) {
        height: 24px;
        .el-input__wrapper {
          height: 24px;
          line-height: 24px;
          .el-input__inner {
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 20px;
          }
        }
      }
    }

    .middle {
      justify-content: space-between;
      span {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      :deep(.el-statistic__content) {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bottom {
      margin-top: 8px;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      :deep(.el-link__inner) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 14px;
        height: 11px;
        margin-left: 5px;
      }
    }
  }
}
.linkSty {
  display: flex;
  align-items: center;
  text-align: center;
  width: fit-content;
  margin: auto;
  .copyIcon {
    display: none;
    width: 1px;
  }
  &:hover {
    .copyIcon {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
</style>
