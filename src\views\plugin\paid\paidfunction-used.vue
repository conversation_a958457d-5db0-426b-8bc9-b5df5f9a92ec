<template>
  <el-dialog v-model="visible" title="使用明细" :footer="null" :close-on-click-modal="false" :close-on-press-escape="false" width="1018px">
    <ny-table style="width: 100%; margin: auto" :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header-right>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-form-item>
            <el-input style="width: 280px !important" v-model="state.dataForm.userName" placeholder="请输入用户名称" clearable :prefix-icon="Search"></el-input>
          </el-form-item>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
        </el-form>
      </template>
      <template #type="{ row }">
        <span v-if="row.type == 1">普通用户</span>
        <espan v-if="row.type == 2">合作商</espan>
        <span v-if="row.type == 3">客服</span>
      </template>
    </ny-table>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, watch, toRefs, defineEmits, defineExpose, reactive } from "vue";
import { useAppStore } from "@/store";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const view = reactive({
  getDataListURL: "/function/sysfunctionuses/page",
  getDataListIsPage: true,
  dataForm: {
    userName: "",
    functionId: ""
  },
  createdIsNeed: false
});
const columns = reactive([
  {
    label: "用户名称",
    prop: "userName"
  },
  {
    label: "类型",
    prop: "type"
  },
  {
    label: "使用次数",
    prop: "usesCount"
  }
]);

const state = reactive({ ...useView(view), ...toRefs(view) });
const init = async (id: string) => {
  visible.value = true;
  state.dataForm.functionId = id;
  state.getDataList();
};
const getResetting = () => {
  state.dataForm.userName = "";
  state.getDataList();
};
defineExpose({
  init
});
</script>
<style lang="less"></style>
