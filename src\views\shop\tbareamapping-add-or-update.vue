<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="主键" prop="id">
        <el-input v-model="dataForm.id" placeholder="主键"></el-input>
      </el-form-item>
      <el-form-item label="合作商游戏ID" prop="partenrGameId">
        <el-select v-model="dataForm.partenrGameId" placeholder="请选择">
          <el-option label="全部" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合作商区服id" prop="partenrAreaId">
        <el-select v-model="dataForm.partenrAreaId" placeholder="请选择">
          <el-option label="全部" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合作商区服父id" prop="partenrAreaPid">
        <el-select v-model="dataForm.partenrAreaPid" placeholder="请选择">
          <el-option label="全部" value="0"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  gameId: "",
  gameName: "",
  pName: "",
  areaName: "",
  areaId: "",
  pId: "",
  partenrId: "",
  partenrName: "",
  partenrGameId: "",
  partenrGameName: "",
  partenrAreaId: "",
  partenrAreaPid: "",
  partenrAreaPname: "",
  partenrAreaName: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
  isDelete: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbareamapping/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/shop/tbareamapping", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>