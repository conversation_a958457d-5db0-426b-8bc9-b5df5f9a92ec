<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <div class="header_center">
      <div class="router_info">
        <span class="info_name">同步上架</span>
        <span class="info_line">|</span>
        <span class="info_blurb">恒星后台管理系统，让账号交易变的更安全</span>
      </div>
      <div>
        <el-button type="primary" v-if="pushStaskInfo && pushStaskInfo.count > 0" plain @mouseenter="mouseenterChange(1)" @mouseleave="mouseleaveChange(1)">推送中：{{ pushStaskInfo.pushCount }}/{{ pushStaskInfo.count }}</el-button>
        <pushStask v-if="pushStaskShow" @mouseenterChange="mouseenterChange(2)" @mouseleaveChange="mouseleaveChange(2)" @refresh="changeDataForm"></pushStask>
      </div>
    </div>

    <ny-flod-tab class="newTabSty" :list="state.typeList" v-model="state.dataForm.btnType" value="value" label="label" @change="changeDataForm"> </ny-flod-tab>
    <div v-if="state.dataForm.btnType == 4" class="mainBox">
      <div class="leftPart">
        <div class="title">选择合作商</div>
        <div class="scrollWrap" v-if="state.dataForm.btnType == 2">
          <ul class="menuUl">
            <li :class="'menuLi ' + (state.dataForm.partnerId == item.id ? 'active' : '')" v-for="(item, itemIndex) in state.curAllPartnerList" :key="itemIndex" @click="handleselectPartner(item.id)" :index="itemIndex">
              <span>{{ item.name }}</span>
            </li>
          </ul>
        </div>
        <template v-else>
          <el-select filterable v-model="state.dataForm.partnerId" style="width: 186px; margin-bottom: 10px" @change="handleselectPartner(state.dataForm.partnerId)">
            <el-option v-for="item in state.curAllPartnerList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <div class="title">选择游戏</div>
          <div class="scrollWrap lower">
            <ul class="menuUl">
              <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in state.allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
                <span>{{ item.gameName }}</span>
              </li>
            </ul>
          </div>
        </template>
      </div>
      <div class="rightPart">
        <commonTable ref="commonTableRef"></commonTable>
      </div>
    </div>
    <!-- 商品推送 -->
    <index2 v-if="state.dataForm.btnType == 5" :curAllPartnerList="state.curAllPartnerList" />
    <!-- 推送记录 -->
    <pushRecords v-if="state.dataForm.btnType == 1" :curAllPartnerList="state.curAllPartnerList"></pushRecords>
  </el-card>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick, provide } from "vue";
import commonTable from "./commonTable.vue";
import index2 from "./index2.vue";
import automaticShelvingSettings from "@/views/shop/partnerPushDetails/automatic-shelving-settings.vue";
import { useRouter } from "vue-router";
import pushStask from "./push-stask.vue";
import pushRecords from "./push-records.vue";
const router = useRouter();
const { currentRoute } = router;

const state = reactive({
  typeList: [
    // { label: "游戏映射", value: 2 },
    // { label: "区服映射", value: 3 },
    { label: "属性映射", value: 4 },
    // { label: "信息映射", value: 0 },
    { label: "商品推送", value: 5 },
    { label: "推送记录", value: 1 }
  ],
  curAllPartnerList: [
    {
      name: "全部合作商",
      id: null
    }
  ],
  allSelectArr: [
    {
      title: "选择游戏",
      data: [],
      addHandle: () => {},
      hasButton: false,
      buttonText: "",
      selectType: "radio"
    }
  ],
  dataForm: {
    btnType: 4, //按钮位置（不用于传参）
    partnerId: null, //合作商id
    gameId: undefined //游戏id
  }
});
const commonTableRef = ref();
const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  changeDataForm();
};
// 切换合作商获取游戏+更新table数据
const handleselectPartner = (id: any) => {
  state.dataForm.partnerId = id;
  state.allSelectArr = [];
  baseService.get("/script/sysscriptpartnerinfo/allGameList", { partnerId: state.dataForm.partnerId }).then((res_) => {
    if (res_.code == 0) {
      state.allSelectArr = res_.data || [];
      if (state.allSelectArr.length > 0) {
        state.dataForm.gameId = state.allSelectArr[0].gameId;
        // 获取列表
        changeDataForm(true);
      } else {
        // if (state.dataForm.btnType == 2)
        // 如果游戏没数据， 但是是游戏映射的tab下, 也需要更新列表
        changeDataForm(true);
      }
    }
  });
};

// 向列表发送更新信号  使用： handleselectGame handleselectPartner
const changeDataForm = (changePartner = false) => {
  let gameId = state.dataForm.gameId;
  if (state.dataForm.btnType == 2) {
    gameId = undefined;
  }
  nextTick(() => {
    commonTableRef.value.changeDataForm(state.dataForm.partnerId, gameId, state.dataForm.btnType, changePartner);
  });
};
// 获取合作商数据
const getSelectInfo = () => {
  baseService.get("/script/sysscriptpartnerinfo/allList").then((res) => {
    state.curAllPartnerList = res.data || [];
    if (res.data.length > 0) {
      state.dataForm.partnerId = res.data[0].id;
      handleselectPartner(state.dataForm.partnerId);
    }
  });
};

const pushStaskShow = ref(false);
const mouseTimer = ref();
// 鼠标移入事件
const mouseenterChange = (num: any) => {
  pushStaskShow.value = true;
  if (num == 1) {
    clearTimeout(pushStaskTimer.value);
    pushStaskContent();
  }
  if (num == 2) {
    clearTimeout(mouseTimer.value);
  }
};

// 鼠标移出事件
const mouseleaveChange = (num: any) => {
  if (num == 1) {
    mouseTimer.value = setTimeout(() => {
      pushStaskShow.value = false;
    }, 2000);
  } else {
    clearTimeout(mouseTimer.value);
    pushStaskShow.value = false;
  }
};

// 获取推送任务数量
const pushStaskInfo = ref(<any>"");
const pushStaskTimer = ref();
const pushStaskContent = () => {
  baseService.post("/script/sysscriptpushtask/queryTheListOfTaskCount").then((res) => {
    if (res.code == 0) {
      pushStaskInfo.value = res.data;
      if (pushStaskInfo.value && pushStaskInfo.value.count > 0) {
        pushStaskTimer.value = setTimeout(() => {
          pushStaskContent();
        }, 3000);
      } else {
        clearTimeout(pushStaskTimer.value);
        pushStaskShow.value = false;
      }
    }
  });
};

// 执行推送任务获取
const startPushStaskContent = () => {
  clearTimeout(pushStaskTimer.value);
  pushStaskContent();
};

provide("startPushStaskContent", startPushStaskContent);

onMounted(() => {
  getSelectInfo();
  pushStaskContent();
});
onUnmounted(() => {
  clearTimeout(pushStaskTimer.value);
});
</script>
<style lang="scss" scoped>
.header_center {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
}
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rr-view-ctx-card {
  // padding-top: 20px;
  min-height: calc(100vh - 154px);

  // :deep(.el-card__body) {
  //   padding: 0;
  // }

  .cards {
    display: flex;
    margin-bottom: 16px;

    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }

      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .mainBox {
    display: flex;

    .leftPart {
      width: 186px;
      margin-right: 12px;

      .title {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 22px;
        margin-bottom: 2px;
      }

      display: flex;
      flex-direction: column;

      .scrollWrap {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        height: calc(100vh - 260px);
        overflow: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .menuUl {
          .menuLi {
            cursor: pointer;
            padding: 20px;
            word-break: keep-all;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Inter, Inter;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            &.active {
              background-color: var(--color-primary-light);
              color: var(--color-primary);
            }
          }
        }

        &.lower {
          height: calc(100vh - 330px);
        }
      }
    }

    .rightPart {
      flex: 1;
      overflow: hidden;
    }
  }
}
.router_info {
  display: flex;
  align-items: center;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
</style>
