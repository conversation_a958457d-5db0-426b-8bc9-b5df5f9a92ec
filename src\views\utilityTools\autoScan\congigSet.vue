<template>
  <el-drawer v-model="visible" title="规则配置" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="944px" class="ny-drawer article-add-or-update">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <div class="card">
        <div class="titleSty">基本配置</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item label="合作商名称" v-if="fromPrice" :span="2">
            <template #label>
              <span>合作商名称<span style="color: red">*</span></span>
            </template>
            <el-form-item label="合作商名称" prop="partnerCode">
              <el-select v-model="dataForm.partnerCode" placeholder="请选择合作商名称" style="width: 292px" @change="dataForm.gameId ? initFilterData() : ''">
                <el-option :label="item.value" :value="item.code" v-for="item in curAllPartnerList" :key="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="规则名称" :span="2">
            <template #label>
              <span>规则名称<span style="color: red">*</span></span>
            </template>
            <span v-if="showDeatil">{{ dataForm.configName }}</span>
            <el-form-item v-else label="规则名称" prop="configName">
              <el-input style="width: 290px" placeholder="请输入规则名称" v-model="dataForm.configName"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="合作商名称" :span="1" v-if="showDeatil">
            <template #label>
              <span>合作商名称</span>
            </template>
            <span>{{ propsData.partnerName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="游戏名称" :span="showDeatil ? 1 : 2">
            <template #label>
              <span>游戏名称<span style="color: red">*</span></span>
            </template>
            <span v-if="propsData.gameName || showDeatil">{{ propsData.gameName }}</span>
            <el-form-item v-else label="游戏区服" prop="gameId">
              <el-select @change="dataForm.gameId ? initFilterData() : ''" v-model="dataForm.gameId" placeholder="请选择游戏" style="width: 292px">
                <el-option :label="item.gameName" :value="item.gameId" v-for="item in gameList" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="游戏区服" :span="2">
            <template #label>
              <span>游戏区服<span style="color: red">*</span></span>
            </template>
            <span v-if="showDeatil">{{ dataForm.serverId === "0" ? "全部" : dataForm.serverName }}</span>
            <el-form-item v-else label="游戏区服" prop="serverId">
              <div class="gameServes" style="padding-right: 30px" :class="{ oneLine: showMore }">
                <div class="serve" :class="{ active: dataForm.serverId == item.id, oneLine: showMore }" @click="dataForm.serverId = item.id" v-for="(item, index) in severList" :key="item.id">
                  {{ item.title }}
                </div>
                <div @click="showMore = false" class="btnMore" v-if="showMore">
                  <el-button
                    >更多<el-icon> <ArrowDown /> </el-icon
                  ></el-button>
                </div>
                <div class="btnMore" v-else>
                  <el-button @click="showMore = true"
                    >收起<el-icon> <ArrowUp /> </el-icon
                  ></el-button>
                </div>
              </div>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="价格区间(元)" :span="2">
            <template #label>
              <span>价格区间(元)<span style="color: red">*</span></span>
            </template>
            <span v-if="showDeatil">{{ dataForm.minPrice }} <span style="margin: 0 8px">至</span> {{ dataForm.maxPrice }}</span>
            <el-form-item v-else label="价格区间(元)" prop="minPrice">
              <el-input style="width: 132px" placeholder="请输入最低价" v-model="dataForm.minPrice"></el-input>
              <span style="margin: 0 8px">至</span>
              <el-input style="width: 132px" placeholder="请输入最高价" v-model="dataForm.maxPrice"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="时间区间" :span="2">
            <template #label>
              <span>时间区间<span style="color: red">*</span></span>
            </template>
            <span v-if="showDeatil">{{ dataForm.timeRange[0] }}<span style="margin: 0 8px">至</span>{{ dataForm.timeRange[1] }}</span>
            <el-form-item v-else label="时间区间" prop="startTime">
              <div style="width: 292px">
                <el-date-picker :disabled-date="disabledDate" style="width: auto" @change="rangeChange" v-model="dataForm.timeRange" type="daterange" value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始时间" :end-placeholder="formatDate(new Date(), 'YYYY-MM-DD')" />
              </div>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="card">
        <div class="titleSty">关键词标签配置</div>
        <el-descriptions style="width: 100%" border :column="1">
          <el-descriptions-item label="精确属性" v-if="fromPrice">
            <template #label>
              <span>属性统计</span>
            </template>
            <div>{{ propsData.totalAttributeText }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="精确属性">
            <template #label>
              <span>精确属性</span>
            </template>
            <span v-if="showDeatil">{{ dataForm.preciseAttribute }}<span v-if="dataForm.preciseAttribute1">，</span>{{ dataForm.preciseAttribute1 }}</span>
            <div v-else>
              <el-popover popper-style="max-height: 400px" placement="bottom-start" :width="730" v-model:visible="visiblepopover" trigger="click">
                <template #reference>
                  <div>
                    <el-input :suffix-icon="Search" v-model="searchParam" placeholder="请输入关键词或选择属性进行筛选" />
                  </div>
                </template>
                <searchPage :gameId="dataForm.gameId" :partnerCode="dataForm.partnerCode" :searchParam="searchParam" :attributesList="dataForm.attributesList" @submit="handlequeryParams" @searchChange="searchParam = ''"></searchPage>
              </el-popover>
              <div style="display: flex; color: #909090; margin-bottom: 4px" v-if="historyResult.length > 0">
                历史记录：<el-link type="primary" v-for="(ele, index) in historyResult" :key="ele" @click="handlequeryParams({ text: ele })"><span v-if="index != 0">，</span>{{ ele }}</el-link>
              </div>
              <div class="selectResult">
                <template v-for="(item, index) in dataForm.attributesTexts" :key="index">
                  <div class="chanageItem">
                    <div class="item">
                      {{ item }}
                      <el-icon size="18" @click="clickFn(index, 0)">
                        <CircleCloseFilled />
                      </el-icon>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in dataForm.attributesTexts1" :key="index">
                  <div class="chanageItem">
                    <div class="item">
                      {{ item }}
                      <el-icon size="18" @click="clickFn(index, 1)">
                        <CircleCloseFilled />
                      </el-icon>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="card">
        <div class="titleSty">执行周期</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item label="执行时间" :span="1">
            <template #label>
              <span>执行时间</span>
            </template>
            <span v-if="showDeatil">{{ dataForm.timeStart }}-{{ dataForm.timeEnd }}</span>
            <el-form-item v-else label="执行时间" prop="timeRange_">
              <el-date-picker type="daterange" style="width: 264px" v-model="dataForm.timeRange_" value-format="YYYY-MM-DD HH:mm:ss" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="执行次数" :span="1">
            <template #label>
              <span>执行次数</span>
            </template>
            <span v-if="showDeatil">{{ dataForm.executeTimes }}</span>
            <el-form-item v-else label="执行次数" prop="executeTimes">
              <el-input-number :min="1" style="width: 151px; margin: 0 10px" placeholder="请输入" v-model="dataForm.executeTimes" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="是否重复" :span="2" v-if="!showDeatil">
            <template #label>
              <span>是否重复</span>
            </template>
            <el-form-item label="是否重复" prop="repeat1">
              <el-switch v-model="dataForm.repeat1" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" />
            </el-form-item>
          </el-descriptions-item>
          <template v-if="dataForm.repeat1 == 1">
            <el-descriptions-item label="重复频率" :span="2">
              <template #label>
                <span>重复频率</span>
              </template>
              <span v-if="showDeatil">
                每{{ dataForm.repeatInterval || "-" }}天
                <!-- {{ dataForm.repeatType == 1 ? "天" : dataForm.repeatType == 3 ? "月" : dataForm.repeatType == 2 ? "周" : "" }}
              {{ dataForm.repeatType == 3 ? (dataForm.appointDay == 2 ? "第一天" : "最后一天") : cnDate(dataForm.appointDay) }} -->
              </span>
              <el-form-item v-else label="重复频率" prop="value3">
                <div class="flx">
                  <span>每</span>
                  <el-input-number :min="1" v-model="dataForm.repeatInterval" style="width: 151px; margin: 0 10px" placeholder="请输入" /><span>天</span>
                  <!-- <el-select v-model="dataForm.repeatType" placeholder="" @change="dataForm.appointDay = 1" style="width: 58px">
                  <el-option label="天" :value="1"></el-option>
                  <el-option label="周" :value="2"></el-option>
                  <el-option label="月" :value="3"></el-option>
                </el-select> -->
                  <div v-if="dataForm.repeatType == 2" class="gameServes oneLine" style="margin-left: 10px; width: fit-content">
                    <div class="serve" :class="{ active: dataForm.appointDay == item.value }" @click="dataForm.appointDay = item.value" v-for="(item, index) in dateList" :key="index">{{ item.label }}</div>
                  </div>
                  <el-select v-if="dataForm.repeatType == 3" v-model="dataForm.appointDay" placeholder="" style="width: 100px; margin-left: 10px">
                    <el-option label="第一天" :value="1"></el-option>
                    <el-option label="最后一天" :value="2"></el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="结束重复" :span="2">
              <template #label>
                <span>结束重复</span>
              </template>
              <span v-if="showDeatil"
                >{{ ["无限重复", "终止于某天", "限定次数"][+dataForm.endType] }}
                <span v-if="dataForm.endType == 1"> {{ dataForm.endCondition }} </span>
                <span v-if="dataForm.endType == 2">{{ dataForm.endCondition }} 次后</span>
              </span>
              <el-form-item v-else label="结束重复" prop="endType">
                <el-select v-model="dataForm.endType" @change="dataForm.endCondition = ''" placeholder="" style="width: 114px">
                  <el-option label="无限重复" :value="0"></el-option>
                  <el-option label="终止于某天" :value="1"></el-option>
                  <el-option label="限定次数" :value="2"></el-option>
                </el-select>
                <div v-if="dataForm.endType == 1">
                  <el-date-picker style="width: 264px; margin: 0 10px" v-model="dataForm.endCondition" value-format="YYYY-MM-DD" placeholder="请选择时间" />
                </div>
                <div v-if="dataForm.endType == 2">
                  <el-input-number :min="1" style="width: 151px; margin: 0 10px" placeholder="请输入" v-model="dataForm.endCondition" />
                  <span>次后</span>
                </div>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="是否生效" :span="2" v-if="!showDeatil">
              <template #label>
                <span>是否生效</span>
              </template>
              <el-form-item label="是否生效" prop="takeEffect">
                <el-switch v-model="dataForm.takeEffect" :active-value="1" :inactive-value="0" active-text="开启" inactive-text="关闭" />
              </el-form-item>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
    </el-form>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button v-if="!showDeatil" :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import { ref, defineExpose, defineEmits, inject } from "vue";
import { useAppStore } from "@/store";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import searchPage from "./searchPage.vue";
import { formatDate } from "@/utils/method";

const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);
const severList = ref(<any>[]);
const showMore = ref(true);
const dataForm = ref({
  gameId: "",
  partnerCode: "",
  configName: "",
  serverId: "",
  minPrice: undefined,
  maxPrice: undefined,
  startTime: "",
  preciseAttribute: "",
  preciseAttributeId: "",
  preciseAttribute1: "",
  timeStart: "",
  timeEnd: "",
  timeRange_: [],
  executeTimes: "",
  repeatType: 1,
  appointDay: 1,
  repeat1: 0,
  endType: 0,
  endCondition: "",
  takeEffect: "",
  repeatInterval: "",
  attributesList: <any>[],
  attributesTexts: <any>[],
  attributesTextIds: <any>[],
  attributesTexts1: <any>[],
  timeRange: [undefined, undefined]
});
const gameList = inject("propGamrList");
const visible = ref(false);
const showDeatil = ref(false);
const dataLoading = ref(false);
const validatePrice = (rule: any, value: any, callback: any) => {
  if (!dataForm.value.minPrice && dataForm.value.minPrice != 0) {
    callback(new Error("请输入最低价"));
  } else if (!dataForm.value.maxPrice && dataForm.value.maxPrice != 0) {
    callback(new Error("请输入最高价"));
  } else if (+dataForm.value.minPrice > +dataForm.value.maxPrice) {
    callback(new Error("最高价不能低于最低价"));
  } else {
    callback();
  }
};
const rules = {
  configName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
  partnerCode: [{ required: true, message: "请选择合作商", trigger: "blur" }],
  gameId: [{ required: true, message: "请选择游戏", trigger: "blur" }],
  serverId: [{ required: true, message: "请选择区服", trigger: "blur" }],
  minPrice: [{ required: true, validator: validatePrice, trigger: "blur" }],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "blur" }]
};
// 日期
const dateList = ref([
  { value: 2, label: "一" },
  { value: 3, label: "二" },
  { value: 4, label: "三" },
  { value: 5, label: "四" },
  { value: 6, label: "五" },
  { value: 7, label: "六" },
  { value: 1, label: "日" }
]);
const cnDate = (val: any) => {
  let obj = dateList.value.find((ele) => ele.value == val);
  return obj?.label;
};
const propsData = ref();
const init = async (rows?: any, isdetail = false) => {
  // --初始化
  fromPrice.value = false;
  searchParam.value = undefined;
  severList.value = [];
  visible.value = true;
  showDeatil.value = isdetail;
  propsData.value = rows; //用于回显
  dataForm.value = {
    gameId: propsData.value.gameId,
    partnerCode: propsData.value.partnerCode,
    configName: "",
    serverId: "",
    minPrice: undefined,
    maxPrice: undefined,
    startTime: "",
    preciseAttribute: "",
    preciseAttributeId: "",
    preciseAttribute1: "",
    timeStart: "",
    timeEnd: "",
    timeRange_: [],
    executeTimes: "",
    repeatType: 1,
    appointDay: 1,
    repeat1: 0,
    endType: 0,
    endCondition: "",
    takeEffect: "",
    repeatInterval: "",
    attributesList: <any>[],
    attributesTexts: <any>[],
    attributesTextIds: <any>[],
    attributesTexts1: <any>[],
    timeRange: [undefined, formatDate(new Date(), "YYYY-MM-DD")]
  };
  if (!showDeatil.value) {
    historyResult.value = localStorage.getItem("hisSearch") ? localStorage.getItem("hisSearch") : "";
    historyResult.value = historyResult.value ? JSON.parse(historyResult.value) : [];
  }
  if (rows.configId) {
    // 编辑-详情
    await getDetails();
  } else if (propsData.value.gameId) {
    // 新增-选游戏
    initFilterData();
  }
};
// 估价器使用
const fromPrice = ref(false);
const curAllPartnerList = ref([]);
const initPrice = async (rows?: any) => {
  baseService.get("/scan/autoScanConfig/getPartners").then((res) => {
    if (res.code == 0) {
      curAllPartnerList.value = res.data || [];
    }
  });
  // --初始化
  fromPrice.value = true;
  searchParam.value = undefined;
  severList.value = [];
  visible.value = true;
  showDeatil.value = false;
  propsData.value = rows; //用于回显
  dataForm.value = {
    gameId: propsData.value.gameId,
    partnerCode: "",
    configName: "",
    serverId: "",
    minPrice: undefined,
    maxPrice: propsData.value.maxPrice ? +propsData.value.maxPrice : undefined,
    startTime: "",
    preciseAttribute: propsData.value.attributesTexts.join(","),
    preciseAttributeId: propsData.value.attributesTextIds.join(","),
    preciseAttribute1: "",
    timeStart: "",
    timeEnd: "",
    timeRange_: [],
    executeTimes: "",
    repeatType: 1,
    appointDay: 1,
    repeat1: 0,
    endType: 0,
    endCondition: "",
    takeEffect: "",
    repeatInterval: "",
    attributesList: <any>[],
    attributesTexts: propsData.value.attributesTexts,
    attributesTextIds: propsData.value.attributesTextIds,
    attributesTexts1: <any>[],
    timeRange: [undefined, formatDate(new Date(), "YYYY-MM-DD")]
  };
  if (propsData.value.gameId && dataForm.value.partnerCode) {
    // 新增-选游戏
    initFilterData();
  }
};
const getDetails = () => {
  dataLoading.value = true;
  baseService
    .get("/scan/autoScanConfig/" + propsData.value.configId)
    .then((res) => {
      if (res.code == 0) {
        if (res.data) {
          dataForm.value = {
            ...res.data,
            attributesTexts: res.data.preciseAttribute && res.data.preciseAttribute.length > 0 ? res.data.preciseAttribute.split(",") : [],
            attributesTextIds: res.data.preciseAttributeId && res.data.preciseAttributeId.length > 0 ? res.data.preciseAttributeId.split(",") : [],
            attributesTexts1: res.data.preciseAttribute1 && res.data.preciseAttribute1.length > 0 ? res.data.preciseAttribute1.split(",") : [],
            timeRange: [res.data.startTime, formatDate(new Date(), "YYYY-MM-DD")],
            timeRange_: [res.data.timeStart, res.data.timeEnd]
          };
          if (!showDeatil.value) initFilterData();
        }
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};
const initFilterData = () => {
  getGameAttributes();
  getServe();
};
// 时间切换时
const rangeChange = () => {
  if (dataForm.value.timeRange && dataForm.value.timeRange[0]) {
    dataForm.value.startTime = dataForm.value.timeRange[0];
  } else {
    dataForm.value.startTime = "";
    dataForm.value.timeRange = [undefined, undefined];
  }
  dataForm.value.timeRange[1] = formatDate(new Date(), "YYYY-MM-DD");
};
const disabledDate = (time: any) => {
  // 禁用今天之前的日期
  return time.getTime() > new Date().getTime();
};
// 获取游戏属性
const getGameAttributes = () => {
  dataForm.value.attributesList = [];
  baseService
    .get("/scan/autoScanConfig/getAttribute", {
      gameId: dataForm.value.gameId,
      partnerCode: dataForm.value.partnerCode
    })
    .then((res: any) => {
      res.data.map((item: any) => {
        dataForm.value.attributesList.push({
          typeId: item.id,
          id: item.id,
          attributeIds: [],
          attributeText: "",
          children: item.children,
          name: item.attributeName,
          start: "",
          end: ""
        });
      });
    });
};
// --
// ------区服
const getServe = () => {
  severList.value = [];
  baseService.get("/shop/shop/getGameArea", { gameId: dataForm.value.gameId }).then((res) => {
    severList.value = [
      {
        id: "0",
        title: "全部"
      },
      ...res.data
    ];
  });
};
// ------表单
const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let params = { ...dataForm.value };
      params.timeStart = params.timeRange_[0];
      params.timeEnd = params.timeRange_[1];
      delete params.timeRange_;
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/scan/autoScanConfig", params)
        .then((res) => {
          if (res.code === 0) {
            ElMessage.success(res.msg);
            emits("refreshDataList");
            visible.value = false;
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

const visiblepopover = ref(false);
const searchParam = ref();
const historyResult = ref(<any>[]);
// 处理筛选条件请求参数
const handlequeryParams = (data: any) => {
  visiblepopover.value = false;
  if (data.text) {
    // push-去重-赋值-存历史
    dataForm.value.attributesTexts1.push(data.text);
    dataForm.value.attributesTexts1 = [...new Set(dataForm.value.attributesTexts1)];
    dataForm.value.preciseAttribute1 = dataForm.value.attributesTexts1.join(",");
    historyResult.value.push(data.text);
    historyResult.value = [...new Set(historyResult.value)];
    localStorage.setItem("hisSearch", JSON.stringify(historyResult.value));
    return;
  }
  // push-去重-赋值
  let attributesTexts = dataForm.value.attributesTexts;
  let attributesTextIds = dataForm.value.attributesTextIds;
  data.attributesList.forEach((item: any) => {
    attributesTexts.push(item.name);
    attributesTextIds.push(item.id);
  });
  attributesTexts = [...new Set(attributesTexts)];
  attributesTextIds = [...new Set(attributesTextIds)];
  dataForm.value.attributesTexts = attributesTexts;
  dataForm.value.attributesTextIds = attributesTextIds;
  dataForm.value.preciseAttribute = attributesTexts.join(",");
  dataForm.value.preciseAttributeId = attributesTextIds.join(",");
};
// 选择删除点击事件
const clickFn = (index: any, type: any) => {
  if (type == 0) {
    dataForm.value.attributesTexts.splice(index, 1);
    dataForm.value.attributesTextIds.splice(index, 1);
    dataForm.value.preciseAttribute = dataForm.value.attributesTexts.join(",");
    dataForm.value.preciseAttributeId = dataForm.value.attributesTextIds.join(",");
  } else {
    dataForm.value.attributesTexts1.splice(index, 1);
    dataForm.value.preciseAttribute1 = dataForm.value.attributesTexts1.join(",");
  }
};
defineExpose({
  init,
  initPrice
});
</script>

<style lang="scss" scoped>
.card {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
  margin-bottom: 12px;

  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  :deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;

    tbody {
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;

        .el-descriptions__label {
          display: flex;
          align-items: center;
          font-weight: normal;
          width: 144px;
        }

        .el-descriptions__content {
          display: flex;
          align-items: center;
          min-height: 48px;
          flex: 1;

          > div {
            width: 100%;
          }

          .el-form-item__label {
            display: none;
          }

          .el-form-item {
            margin-bottom: 0;
          }
        }

        .noneSelfRight {
          border-right: 0 !important;
        }

        .noneSelfLeft {
          border-left: 0 !important;
        }

        .noneSelfLabel {
          background: none;
          border-left: 0 !important;
          border-right: 0 !important;
        }
      }
    }
  }

  .gameServes {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    width: 700px;

    &.oneLine {
      flex-wrap: nowrap;
      overflow: hidden;
      min-height: 32px;
    }

    .serve {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 22px;
      padding: 5px 12px;
      word-break: keep-all;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }

    .btnMore {
      padding-left: 24px;
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 1;
      background: linear-gradient(270deg, #fff 0%, rgba(255, 255, 255, 0.9) 80%, rgba(255, 255, 255, 0.5) 100%);
    }
  }

  .selectResult {
    display: flex;
    flex-wrap: wrap;
    .attrName {
      font-weight: bold;
      font-size: 14px;
      color: #303133;
      line-height: 22px;
    }

    .chanageItem {
      display: flex;
      flex-wrap: wrap;

      .item {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: var(--el-color-primary);
        background-color: var(--color-primary-light);
        line-height: 22px;
        padding: 5px 12px;
        border-radius: 50px;
        margin-right: 4px;
        margin-top: 4px;

        .el-icon {
          color: var(--el-color-primary);
          margin-left: 8px;
          cursor: pointer;
        }
      }
    }
  }
}

.topTab {
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  margin: -12px;
  margin-bottom: 12px;
}
</style>
