<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '全部用户', value: '' },
          { label: '正常用户', value: '0' },
          { label: '禁用用户', value: '1' }
        ]"
        v-model="view.dataForm.state"
        value="value"
        label="label"
        @change="tabClick"
      ></ny-flod-tab>
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <el-button type="danger" v-if="state.hasPermission('tb:user:delete')" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
        </template>
        <template #header-right>
          <el-input v-model="view.dataForm.username" placeholder="请输入用户名称" :prefix-icon="Search" style="width: 220px" />
          <el-input v-model="view.dataForm.mobile" placeholder="请输入手机号" :prefix-icon="Search" style="width: 220px" />
          <el-button type="primary" @click="queryFn">查询</el-button>
          <el-button @click="resetFn">重置</el-button>
        </template>
        <template #avatar="{ row }">
          <el-image style="height: 40px" :src="row.avatar" :preview-src-list="[row.avatar]" preview-teleported v-if="row.avatar" />
          <span v-else>-</span>
        </template>
        <template #isRealname="{ row }">
          <span v-if="row.isRealname == '0'">未实名</span>
          <span v-if="row.isRealname == '1'">已实名</span>
        </template>
        <template #state="{ row }">
          <span v-if="row.state == '0'">正常用户</span>
          <span v-if="row.state == '1'">禁用用户</span>
        </template>
        <template #sexual="{ row }">
          <span v-if="row.sexual == '1'">男</span>
          <span v-if="row.sexual == '2'">女</span>
          <span v-else>-</span>
        </template>
        <template #idNo="{ row }">
          <span v-if="row.idNo">{{ row.idNo.substring(0, 6) + "********" + row.idNo.substring(14, 18) }}</span>
          <span v-else>-</span>
        </template>

        <!-- 是否启用 -->
        <template #whetherToEnable="{ row }">
          <el-switch v-model="row.state" :inactive-value="1" :active-value="0" @change="changeEnable(row.id)" />
        </template>
      </ny-table>
    </el-card>
  </div>
</template>

<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const view = reactive({
  getDataListURL: "/tb/user/page",
  getDataListIsPage: true,
  deleteURL: "/tb/user/delete",
  deleteIsBatch: true,
  dataForm: {
    orderField: "",
    order: "",
    username: "",
    mobile: "",
    state: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "id",
    label: "用户ID",
    minWidth: "180",
    align: "center"
  },
  {
    prop: "username",
    label: "用户名称",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "nickname",
    label: "昵称",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "mobile",
    label: "手机号",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "avatar",
    label: "头像",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "isRealname",
    label: "实名状态",
    minWidth: "100",
    align: "center"
  },
  {
    prop: "idNo",
    label: "身份证号",
    minWidth: "180",
    align: "center"
  },
  {
    prop: "state",
    label: "用户状态",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "wechatName",
    label: "微信",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "email",
    label: "邮箱",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "sexual",
    label: "性别",
    minWidth: "80",
    align: "center"
  },
  {
    prop: "birthday",
    label: "生日",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "160",
    align: "center",
    sortable: "custom"
  },
  {
    prop: "whetherToEnable",
    label: "是否启用",
    minWidth: "100",
    align: "center",
    fixed: "right"
  }
]);

// tab切换
const tabClick = (tab: any) => {
  view.dataForm.state = tab;
  state.getDataList();
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  if (order == "ascending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "asc";
  }
  if (order == "descending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "desc";
  }
  if (order == null) {
    view.dataForm.orderField = "";
    view.dataForm.order = "";
  }
  state.getDataList();
};

// 查询
const queryFn = () => {
  state.getDataList();
};
// 重置
const resetFn = () => {
  view.dataForm.username = "";
  view.dataForm.mobile = "";
  view.dataForm.state = "";
  state.getDataList();
};

// 修改启用状态
const changeEnable = (id: any) => {
  if (!id) return;
  baseService.put("/tb/user/forbidden", [id]).then((res) => {
    ElMessage.success({
      message: "修改成功",
      duration: 500
    });
    state.getDataList();
  });
};
</script>

<style lang='less' scoped>
</style>