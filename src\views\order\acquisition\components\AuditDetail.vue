<template>
    <el-dialog v-model="visible" title="打款审核详情" width="480px">
        <el-descriptions class="descriptions-label-140" :column="1" border>
            <el-descriptions-item label="审核结果">审核拒绝</el-descriptions-item>
            <el-descriptions-item label="拒绝原因">{{ tasks.remarks }}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <el-button type="primary" @click="reSubmitPayment">重新提交打款</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineEmits } from 'vue';

    const visible = ref(false);
    const emit = defineEmits(['resubmitPayment']);
    const info = ref(<any>{});
    const tasks = ref(<any>{});

    const init = (data: any) => {
        info.value = data;
        tasks.value = {};
        if(data.auditEntity.tasks && data.auditEntity.tasks.length){
            data.auditEntity.tasks.forEach((x: any) => {
                if(x.status == 2){
                    tasks.value = x;
                }
            });
        }
        visible.value = true;
    }

    // 重新提交打款
    const reSubmitPayment = () => {
        visible.value = false;
        emit('resubmitPayment', info.value.orderId);
    }
    
    defineExpose({
        init
    })

</script>