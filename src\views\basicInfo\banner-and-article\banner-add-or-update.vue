<template>
    <el-drawer
        v-model="visible"
        :title="dataForm.id ? '修改轮播图' : '新增轮播图'"
        :close-on-click-moformuladal="false"
        :close-on-press-escape="false"
        size="944px"
        class="ny-drawer article-add-or-update"
    >
        <el-card class="article-add-or-update-form" v-loading="dataLoading">
            <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
                <el-form-item label="分组" prop="articleType">
                    <ny-select
                        style="width: 360px;"
                        v-model="dataForm.type"
                        dict-type="banner_type"
                        placeholder="请选择分组"
                        @change="state.getDataList()"
                    ></ny-select>
                </el-form-item>
                <el-form-item label="图片上传" prop="image">
                    <ny-upload :limit="1" v-model:imageUrl="dataForm.image" accept="image/*" ></ny-upload>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input class="input-w-360" v-model="dataForm.sort" type="number" placeholder="请输入排序"></el-input>
                </el-form-item>
            </el-form>

            <ny-table 
                :state="state" 
                :columns="columns"
                :showColSetting="false"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <span>绑定文章</span>
                </template>
                
                <template #header-right>
                    <el-input v-model="state.dataForm.title" placeholder="请输入标题名称" clearable :prefix-icon="Search"></el-input>
                    
                    <el-button type="primary" @click="state.getDataList()">{{
                    $t("query")
                    }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <template #status="{row}">
                    <el-tag type="success" v-if="row.status==1">已发布</el-tag>
                    <el-tag type="info" v-else>未发布</el-tag>
                </template>

                
                <template #operation="{row}">
                    <el-button 
                        v-if="dataForm.articleId != row.id"
                        type="primary"
                        text bg 
                        @click="bindHandle(row.id, row.title)"
                    >
                        绑定
                    </el-button>
                    <el-button 
                        v-else
                        type="danger"
                        text bg 
                        @click="bindHandle('', row.title)"
                    >
                        取消绑定
                    </el-button>
                </template>

            </ny-table>
        </el-card>

        <template #footer>
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" plian @click="submitForm(1)">保存并启用</el-button>
            <el-button v-if="!dataForm.status || dataForm.status == 0" :loading="btnLoading" type="primary" @click="submitForm(0)">保存</el-button>
        </template>
    </el-drawer>
</template>  

<script lang="ts" setup>
    import { IObject } from "@/types/interface";
    import { ref, defineExpose, defineEmits, reactive, toRefs } from "vue";
    import { useAppStore } from "@/store";
    import { ElMessage } from "element-plus";
    import baseService from "@/service/baseService";
    import useView from "@/hooks/useView";
    import { Search } from "@element-plus/icons-vue";

    const emits = defineEmits(['refresh']);

    const dataForm = ref({
        title: "",
        type: "",
        sort: "",
        image: ""
    } as IObject);
    const visible = ref(false);

    const rules = {
    }

    const view = reactive({
        getDataListURL: "basics/article/page",
        getDataListIsPage: true,
        dataForm: {
            title: "",
            status: '1'
        },
    });

    const state = reactive({ ...useView(view,), ...toRefs(view)});

    // 表格配置项
    const columns = reactive([
        {
            prop: "title",
            label: "标题",
            minWidth: 410
        },
        {
            prop: "status",
            label: "状态",
            minWidth: 200
        },
        {
            prop: "operation",
            label: "操作",
            fixed: "right",
            width: 120
        }
    ]);


    // 重置操作
    const getResetting = () => {
        state.dataForm.title = "";
        state.getDataList();
    }

    const init = (id?: number) => {
        visible.value = true;
        if(id){
            getDetails(id);
        }
    }

    // 绑定文章
    const bindHandle = (id: number | string, title: string) => {
        dataForm.value.articleId = id;
        dataForm.value.title = id ? title : "";
    }
    
    const dataLoading = ref(false);
    const getDetails = (id: number) => {
        dataLoading.value = true;
        baseService.get('/basics/slideshow/' + id).then(res => {
            if (res.code === 0) {
                dataForm.value = res.data;
            }
        }).finally(() => {
            dataLoading.value = false;
        });
    }
    
    const dataFormRef = ref();
    const btnLoading = ref(false);
    const submitForm = (status: number) => {
        dataFormRef.value.validate((valid: boolean) => {
            if (valid) {
                btnLoading.value = true;
                dataForm.value.status = status;
                baseService[dataForm.value.id ? 'put' : 'post']('/basics/slideshow', dataForm.value).then(res => {
                    if (res.code === 0) {
                        visible.value = false;
                        ElMessage.success(res.msg);
                        emits('refresh');
                    }
                }).finally(() => {
                    btnLoading.value = false;
                })
            }
        })
    }    

    defineExpose({
        init
    })

</script>

<style lang="scss">
.article-add-or-update{
    .input-w-360{
        width: 360px;
    }
    
}
</style>