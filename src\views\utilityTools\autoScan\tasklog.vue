<template>
  <div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header-right>
        <el-date-picker
          v-model="timeInterval"
          @change="
            () => {
              view.dataForm.start = timeInterval ? timeInterval[0] + ' 00:00:00' : '';
              view.dataForm.end = timeInterval ? timeInterval[1] + ' 23:59:59' : '';
            }
          "
          type="daterange"
          range-separator="到"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          unlink-panels
          style="width: 220px; margin-left: 12px"
        />
        <el-select v-model="state.dataForm.gameName" placeholder="请选择游戏" clearable style="width: 186px">
          <el-option v-for="item in props.gameList" :key="item.gameName" :label="item.gameName" :value="item.gameName" />
        </el-select>
        <el-select v-model="state.dataForm.partnerName" placeholder="请选择合作商" clearable style="width: 186px">
          <el-option v-for="item in props.partnerList" :key="item.value" :label="item.value" :value="item.value" />
        </el-select>
        <el-button type="primary" @click="queryFn">查询</el-button>
        <el-button @click="resetFn">重置</el-button>
      </template>
      <template #status="{ row }">
        <el-tag type="primary" v-if="row.status == '0'">执行中</el-tag>
        <el-tag type="success" v-if="row.status == '1'">执行成功</el-tag>
        <div style="display: flex; align-items: center;justify-content: center;" v-if="row.status == '2'">
          <el-tag type="danger">执行失败</el-tag>
          <el-tooltip effect="dark" :content="row.failReason" placement="top">
            <el-icon style="cursor: pointer; margin-left: 8px" color="#F56C6C"><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      <template #createDate="{ row }">
        {{ formatDate(row.createDate, undefined) }}
      </template>
      <template #operate="{ row }">
        <el-button @click="handlesync(row)" link type="primary">查看</el-button>
      </template>
    </ny-table>
    <congigSet ref="congigSetRef" />
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { formatDate, formatCurrency } from "@/utils/method";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import congigSet from "./congigSet.vue";
const props = defineProps({
  partnerList: [],
  gameList: []
});
const timeInterval = ref([]);
const view = reactive({
  getDataListURL: "/scan/autoScanLog/page",
  getDataListIsPage: true,
  // getDataListIsPage: true,
  // deleteURL: "/tb/user/delete",
  deleteIsBatch: true,
  dataForm: {
    partnerName: "",
    gameName: "",
    start: "",
    end: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: "180",
    align: "center"
  },
  {
    prop: "partnerName",
    label: "合作商名称",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "createDate",
    label: "执行时间",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "scanNum",
    label: "扫描商品数量(个)",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "hitNum",
    label: "匹配规则数量(个)",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "status",
    label: "执行状态",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "operate",
    label: "规则配置",
    minWidth: "100",
    align: "center",
    fixed: "right"
  }
]);

// 查询
const queryFn = () => {
  state.getDataList();
};
// 重置
const resetFn = () => {
  state.dataForm.gameName = "";
  state.dataForm.partnerName = "";
  state.dataForm.start = "";
  state.dataForm.end = "";
  timeInterval.value = [];
  state.getDataList();
};

const congigSetRef = ref();
const handlesync = (row: any) => {
  nextTick(() => {
    congigSetRef.value.init(row, true);
  });
};
</script>

<style lang="less" scoped></style>
