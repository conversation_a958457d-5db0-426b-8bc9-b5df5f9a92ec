import { useSettingStore } from '@/store/setting';
import { useImStore } from "@/store/im";

export const showJudge = (data: any, content: string, targetId: string, sentTime: number) => {
    browserTabTitle.show();
    // if(isCurrentPage) return;
    // 判断是否支持显示
    if (!('Notification' in window)) {
        alert('抱歉，此浏览器不支持桌面通知！')
    }
    // granted: 用户允许该网站发送通知 default: 默认值，用户还未选择 denied: 用户拒绝该网站发送通知
    // Notification.permission === 'granted' 可用于检测用户通知权限
    Notification.requestPermission().then((result) => {
        if (result === 'denied') {
            console.log('用户拒绝')
            return
        } else if (result === 'default') {
            console.log('用户未授权')
            return
        }
        sendMesgToDesk(data, content, targetId, sentTime);
    })
}

// 显示消息通知
let notification:any = null
let timer:any = null
const sendMesgToDesk = (data: any, content: string, targetId: string, sentTime: number) => {
    const imStore = useImStore();
    if(notification) notification.close();
    let title = data.name
    let options = {
        tag: sentTime, // 多条消息时tag相同只显示一条通知，需要显示多条时tag一定要不同，（谷歌每次只能显示一条，火狐可以显示多条）
        body: content, // 通知主体
        data: { // 可以放置任意数据，方便后续使用
            content: content,
            originUrl: `${location.origin}/#/im?targetId=${targetId}`
        },
        requireInteraction: true, // 不自动关闭通知 默认值为false，通知会在三四秒之后自动关闭，（谷歌有用，火狐依然会自动关闭）
        image: '', // 通知上方可显示需要展示的大图
        icon: data.portraitUri // 通知图标 默认是浏览器图标
    }
    notification = new Notification(title, options)
    
    // 事件处理
    notification.onclick = ({ currentTarget: { data } }) => {
        // notification.close() 单个通知关闭
        event.preventDefault(); 
        browserTabTitle.clear();
        window.focus()
        // 默认进入系统之前打开的页面，也可以这里自定义进入的页面
        window.open(`${location.origin}/#/im?targetId=${targetId}`, imStore.imUid)
    }
    notification.onshow = () => {
        if(timer) clearTimeout(timer)
        timer = setTimeout(function () {
            notification.close();
        }, 5000);
        console.log('通知显示了')
    }
    notification.onclose = () => {
        console.log('通知被关闭了')
    }
    notification.onerror = () => {
        console.log('遇到错误')
    }
}

// 浏览器页签标题
export const browserTabTitle = {
    time: 0,
    title: document.title,
    timer: 0,
    show: function () {
        clearTimeout(browserTabTitle.timer);
        browserTabTitle.title = useSettingStore().info.backendTitle ? useSettingStore().info.backendTitle : document.title;
        browserTabTitle.timer = setTimeout(function () {
            browserTabTitle.time++;
            browserTabTitle.show();
            if (browserTabTitle.time % 2 == 0) {
                document.title = "【您来了一条新消息】" + browserTabTitle.title;
            } else {
                document.title = browserTabTitle.title;
            }
        }, 600);
    },
    clear: function () {
        clearTimeout(browserTabTitle.timer);
        document.title = useSettingStore().info.backendTitle ? useSettingStore().info.backendTitle : document.title;
    }
};
