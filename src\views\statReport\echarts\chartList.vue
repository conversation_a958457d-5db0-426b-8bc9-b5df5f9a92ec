<template>
    <div class="chart-list-wrap">
        <div style="display: flex;align-items: flex-end;justify-content: space-between">
            <div class="router_info">
                <span class="info_name">数据分析</span>
                <span class="info_line">|</span>
                <span class="info_blurb">报表数据可视化，以便快速洞察业务的相关情况；</span>
            </div>
            <div style="padding: 10px 0px;">
                <el-date-picker
                    v-model="StatisticsListDate"
                    type="daterange"
                    range-separator="到"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 220px;"
                    unlink-panels
                    @change="TrendsChartInfo"
                />
            </div>
        </div>
        
        <el-row :gutter="15" v-if="List.length">
            <el-col 
                :span="item.chartWitch == '1' ? 8 : item.chartWitch == '2' ? 16 : item.chartWitch == '3' ? 24 : 8" 
                v-for="(item,index) in List" :key="index"
            >
                <div class="card">
                    <div class="card_header">{{ item.name }}</div>
                    <div class="card_container">
                        <chartItemPage :chartInfo="item.options"></chartItemPage>
                    </div>
                </div>
            </el-col>
        </el-row>

        <div class="h-100" v-else>
            <ny-no-data type="3" />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref,reactive, onMounted } from 'vue';
import baseService from "@/service/baseService";
import chartItemPage from './chartItem.vue';

const StatisticsListDate = ref(<any>''); // 动态报表筛选时间
const chartList = ref(<any>[]);     // 获取图表列表
const chartIndex = ref(0);          // 列表下标
const List = ref(<any>[]);          // 图表渲染列表

const TrendsChartInfo = () =>{
    chartIndex.value = 0;
    List.value = [];
    getChartList();
}

// 获取统计报表列表
const getChartList = () =>{
    baseService.get('/report/reportchart/page',{page:1,limit:9999}).then(res=>{
        if(res.code == 0){
            chartList.value = res.data.list;
            chartItem();
        }
    })
}

// 动态加载图表内容
const chartItem = () =>{
    if(chartIndex.value <= (chartList.value.length -1)){
        const id = chartList.value[chartIndex.value].id;
        baseService.post("/report/reportchart/query",{
            id,
            start: StatisticsListDate.value ? StatisticsListDate.value[0] : '',
            end: StatisticsListDate.value ? StatisticsListDate.value[1] : '',
        }).then(res=>{
            if(res.code == 0){
                // console.log(res.data,"======= 详情信息 =======");
                chartIndex.value ++;
                chartInfo(res.data);
                chartItem();
                // baseService.post('/report/reportchart/query',res.data).then(ele=>{
                //     if(ele.code == 0){
                //         chartIndex.value ++;
                //         chartInfo(res.data);
                //         chartItem();
                //     }
                // })
            }
        })
    }
}

//  恢复报表配置项
const chartInfo = (data:any) =>{
    // console.log(data,`第${chartIndex.value}个图表内容`);
    const dataConter = data.data

    const dataForm = {
        name: data.name,
        chartWitch: '',
        position: 'top',   // 图例位置
        labelShow: false, // 数据标签
        legendShow: true, // 图例
        ThemeColor: '主题1', // 主题色
        options: <any>{}
    }
    Object.assign(dataForm,JSON.parse(data.otherSetting))
    const option = JSON.parse(data.setting);
    option.index = chartIndex.value;
    if(data.type == 'option1' || data.type == 'option2' || data.type == 'option3' || data.type == 'option4'){
        option.yAxis.axisLabel = {
            formatter: function(value:any) {
                // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
                if (value >= 10000) {
                    return (value / 10000).toFixed(1) + '万';
                }
                return value;
            }
        }
    }
    switch (data.type) {
        case "option1":
            option.xAxis.data = dataConter.other.xdata;
            option.series = [];
            dataConter.other.ydata.map((e:any)=>{
                option.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'bar',
                    label:{
                        show: dataForm.labelShow,
                        position: dataForm.position,
                    },
                    barMaxWidth: 30,
                })
            })
            break;
        case "option2":
            option.xAxis.data = dataConter.other.xdata;
            option.series = [];
            dataConter.other.ydata.map((e:any)=>{
                option.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'bar',
                    stack: 'Ad', // 堆叠
                    label:{
                        show: dataForm.labelShow,
                        position: dataForm.position,
                    },
                    barMaxWidth: 30,
                })
            })
            break;
        case "option3":
            option.xAxis.data = dataConter.other.xdata;
            option.series = [];
            option.legend.data = [];
            dataConter.other.ydata.map((e:any)=>{
                option.legend.data.push(e.name)
                option.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'line',
                    stack: 'Total',
                    label: {
                        show: dataForm.labelShow,
                        position: dataForm.position,
                    },
                })
            })
            break;
        case "option4":
            option.xAxis.data = dataConter.other.xdata;
            option.series = [];
            option.legend.data = [];
            dataConter.other.ydata.map((e:any)=>{
                option.legend.data.push(e.name)
                option.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'line',
                    stack: 'Total',
                    smooth: true,
                    label: {
                        show: dataForm.labelShow,
                        position: dataForm.position,
                    },
                })
            })
            break;
        case "option5":
            option.series[0].data = dataConter.pie;
            break;
        case "option6":
            option.series[0].data = dataConter.pie;
            break;
    }
    dataForm.options = option;
    List.value.push(dataForm);
    // console.log(List.value,'====== dataForm ======');
}



onMounted(()=>{
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    StatisticsListDate.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getChartList();
})

// 格式化日期
const formatDate = (value:any, type?:any)=> {
    // 计算日期相关值
    let time = typeof value == 'number' ? new Date(value) : value;
    let Y = time.getFullYear();
    let M = time.getMonth() + 1;
    let D = time.getDate();
    let h = time.getHours();
    let m = time.getMinutes();
    let s = time.getSeconds();
    let week = time.getDay();
    // 如果传递了type的话
    if (type == undefined) {
        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);
    } else if (type == 'week') {
        return week + 1;
    }else if(type== 'YYYY-MM-DD'){
        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) ;
        
    }else if(type== 'YYYY-MM'){
        return Y + '-' + (M < 10 ? '0' + M : M);
        
    }else if(type== 'YYYY'){
        return Y;
    }
}

</script>

<style lang='less' scoped>
.chart-list-wrap{
    padding: 15px 15px 0px 15px;
    background-color: #FFFFFF;
}
.router_info {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
    //   padding: 20px 20px 0px 20px;
      .info_name {
        font-weight: bold;
        font-size: 20px;
        color: #303133;
        line-height: 28px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .info_line {
        color: #e4e7ed;
        margin: 0px 12px;
      }
      .info_blurb {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
.card{
    background-color: #fff;
    border-radius: 6px;
    font-size: 12px;
    letter-spacing: .5px;
    color: #000;
    line-height: 1;
    margin-bottom: 15px;
    border: 1px solid #e4e7ed;
    .card_header{
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        font-size: 15px;
        font-weight: 700;
        padding: 0 20px;
    }
    .card_container{
        height: 400px;
    }
}
</style>