<template>
  <el-drawer v-model="visible" :title="dataForm.id ? '编辑号码信息' : '新增号码信息'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="944px" class="ny-drawer article-add-or-update">
    <el-alert style="margin-bottom: 12px; border: 1px solid #f8e3c5" show-icon title="请确保添加手机号已实名认证并且开卡人已知晓，正规合法的来源与用途!" type="warning" />
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <div class="card">
        <div class="titleSty">基本信息</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item class-name="noneSelfRight" label="手机号">
            <template #label>
              <span>手机号<span style="color: red">*</span></span>
            </template>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="dataForm.phone" placeholder="请输入手机号" @blur="getPositionCodeByPhone"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            <el-form-item label="部门" prop="deptName">
              <ny-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName" style="width: 100%"></ny-dept-tree>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="办卡人">
            <el-form-item label="办卡人" prop="createUser">
              <el-input v-model="dataForm.createUser" placeholder="请输入开卡人"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="ICCID">
            <el-form-item label="ICCID" prop="iccid">
              <el-input v-model="dataForm.iccid" disabled placeholder="ICCID"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="运营商" class-name="noneSelfRight">
            <template #label>
              <span>运营商<span style="color: red">*</span></span>
            </template>
            <el-form-item label="运营商" prop="operator">
              <el-select v-model="dataForm.operator" placeholder="请选择运营商">
                <el-option label="移动" value="移动" />
                <el-option label="联通" value="联通" />
                <el-option label="电信" value="电信" />
                <el-option label="广电" value="广电" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="card">
        <div class="titleSty">卡位信息</div>
        <el-descriptions style="width: 100%" border :column="2">
          <el-descriptions-item label="主卡">
            <el-form-item label="主卡" prop="mainPhone">
              <el-input v-model="dataForm.mainPhone" placeholder="请输入主卡"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="卡状态">
            <template #label>
              <span>卡状态</span>
            </template>
            <el-form-item label="卡状态" prop="onlineStatus">
              <el-select v-model="dataForm.onlineStatus" placeholder="请选择卡状态">
                <el-option label="正常" :value="1" />
                <el-option label="异常" :value="2" />
                <el-option label="停用" :value="3" />
                <el-option label="沉默号" :value="4" />
                <el-option label="空号" :value="5" />
                <el-option label="欠费" :value="6" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="余额">
            <el-form-item label="余额" prop="balance"> <el-input-number v-model="dataForm.balance" :precision="2" :step="0.1" /><span style="margin-left: 4px">元</span> </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="月租">
            <el-form-item label="月租" prop="monthlyRent"> <el-input-number v-model="dataForm.monthlyRent" :precision="2" :step="0.1" /><span style="margin-left: 4px">元</span> </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="卡位置">
            <el-form-item label="卡位置" prop="deviceId">
              <el-input v-model="dataForm.deviceId" disabled></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="异常备注">
            <el-form-item label="异常备注" prop="abnormalRemark">
              <el-input v-model="dataForm.abnormalRemark" placeholder="请输入异常备注"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <!-- <template v-if="dataForm.id">
            <el-descriptions-item label="是否频繁">
              <el-form-item label="是否频繁" prop="frequently"> <el-switch inline-prompt v-model="dataForm.frequently" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" /> </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="频繁时间">
              <el-form-item label="频繁时间" prop="frequentlyTime"> <el-date-picker :prefix-icon="Calendar" disabled v-model="dataForm.frequentlyTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择频繁时间" /> </el-form-item>
            </el-descriptions-item>
          </template> -->

          <!-- <el-descriptions-item label="是否租用">
            <el-form-item label="是否租用" prop="rent"> <el-switch inline-prompt v-model="dataForm.rent" active-value="1" inactive-value="0" active-text="是" inactive-text="否" /> </el-form-item>
          </el-descriptions-item> -->
        </el-descriptions>
      </div>
    </el-form>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import { Calendar } from "@element-plus/icons-vue";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";

const { t } = useI18n();
const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);

const validatePhone = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (!/^1[0-9]{10}$/.test(value)) {
    return callback(new Error(t("validate.format", { attr: "手机号码" })));
  }
  callback();
};

const rules = {
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { validator: validatePhone, trigger: "blur" }
  ],
  operator: [{ required: true, message: "请选择运营商", trigger: "blur" }],
  // onlineStatus: [{ required: true, message: "请选择卡状态", trigger: "blur" }]
};

const init = (id?: number) => {
  visible.value = true;
  // getTenant()
  if (id) {
    getDetails(id);
  }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get("/mobile/mobileCard/" + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
        getPositionCodeByPhone();
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 租户下拉
const options = ref([]);
const getTenant = () => {
  options.value = [];
  baseService.get("").then((res) => {
    if (res.code === 0) {
      options.value = res.data;
    }
  });
};

// 根据手机号查询卡位置
const getPositionCodeByPhone = () => {
  if (!dataForm.value.phone) return;
  baseService.get("/mobile/mobileDevice/getPositionCodeByPhone/" + dataForm.value.phone).then((res) => {
    if (res.code == 0) {
      dataForm.value.deviceId = res.data;
    }
  });
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/mobile/mobileCard", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.card {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }
  :deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;
    tbody {
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;
        .el-descriptions__label {
          display: flex;
          align-items: center;
          font-weight: normal;
          width: 144px;
        }
        .el-descriptions__content {
          display: flex;
          align-items: center;
          min-height: 48px;
          flex: 1;
          > div {
            width: 100%;
          }
          .el-form-item__label {
            display: none;
          }
          .el-form-item {
            margin-bottom: 0;
          }
        }
        .noneSelfRight {
          border-right: 0 !important;
        }
        .noneSelfLeft {
          border-left: 0 !important;
        }
        .noneSelfLabel {
          background: none;
          border-left: 0 !important;
          border-right: 0 !important;
        }
      }
    }
  }
}
</style>
