<template>
  <el-select v-model="localValue" @change="handleChange" :placeholder="placeholder" clearable filterable>
    <el-option :label="data[labelKey]" v-for="data in dataList" :key="data[valueKey]" :value="data[valueKey]">
      {{ data[labelKey] }}
    </el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from "vue";
import { useAppStore } from "@/store";
import baseService from "@/service/baseService";
import Param from "@/views/devtools/param.vue";

export default defineComponent({
  /**
   * <AUTHOR>
   * 当前组件请求后端查询接口后下拉选择
   */
  name: "NySelectSearch",
  props: {
    // 绑定赋值
    modelValue: [Number, String],
    placeholder: String,
    // 下拉label显示的key
    labelKey:{type:String,required:true},
    // 下拉绑定值得key
    valueKey:{type:String,require:true},
    // 后台请求地址
    url:{type:String,required:true},
    // 请求参数
    param: {type: Object},
    // 接口返回参数是否有List
    isList: {type: Object,default: true},
  },
  setup(props, { emit }) {
    const store = useAppStore();
    const localValue = ref(props.modelValue);
    const dataList = ref([]);
    const url=props.url
    const fetchDataList = async () => {
      try {
        // 使用 Java 接口来获取数据字典项
        const response = await baseService.get(url,props.param);
        dataList.value = props.isList ? response.data.list : response.data;
      } catch (error) {
        console.error("Error fetching data list:", error);
      }
    };

    const handleChange = (value: string | number) => {
      emit("update:modelValue", value);
      emit("changeReturnEntity",dataList.value.find((item) => item[props.valueKey] === value));
    };
    // Watch for changes in modelValue prop to sync with localValue
    watch(
      () => props.modelValue,
      (newValue) => {
        localValue.value = newValue;
      }
    );
    
    // 监听param参数变化
    watch(() => props.param, (newValue) => {
      fetchDataList();
    }, {
      deep: true
    })

    // Fetch data list on component mount
    onMounted(() => {
      fetchDataList();
    });

    return {
      localValue,
      dataList,
      handleChange,
    };
  },
});
</script>
