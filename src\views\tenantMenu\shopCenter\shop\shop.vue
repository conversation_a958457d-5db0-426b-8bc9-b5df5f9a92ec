<template>
  <div class="shopSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab class="newTabSty" :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title" @change="gamesChange"></ny-flod-tab>
      <shop-stat ref="shopStatRef" :gameId="view.dataForm.gameId"></shop-stat>

      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <div class="flx-justify-between">
            <ny-button-group :list="groupList" v-model="view.dataForm.status" @change="handleStatusClick"></ny-button-group>
          </div>
        </template>
        <template #header-right>
          <div>
            <el-input class="searchIpt" v-model="state.dataForm.search" placeholder="请输入游戏标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input>
            <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 240px; margin-left: 12px" />
          </div>
          <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #header-custom>
          <div style="padding-bottom: 12px">
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            <el-button type="primary" @click="shopJob()">自动降价</el-button>
            <el-button class="buttonSty" type="primary" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.exportHandle()">{{ $t("export") }}</el-button>
            <el-button type="danger" @click="deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
            <!-- <el-button type="warning" @click="openLink()">跳转估价网站生成</el-button> -->
          </div>
        </template>
        <template #code="{ row }">
          <div v-html="row.code"></div>
        </template>
        <template #listingTime="{ row }">
          <span>{{ formatTimeStamp(row.listingTime) }}</span>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #title="{ row }">
          <div class="shoping">
            <el-image style="height: 60px; width: 60px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
            <div class="info">
              <div class="title mle" v-html="row.title" @click="toDetails(row)"></div>
              <div>
                <span>{{ `${row.gameName} / ${row.serverName}` }}</span>
              </div>
              <div>
                <span>{{ state.getDictLabel("shop_compensation", row.compensation) }}</span>
              </div>
            </div>
          </div>
        </template>
        <template #updateDate="{ row }">
          <span>{{ formatTimeStamp(row.updateDate) }}</span>
        </template>
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <div v-if="row.status == '3'" class="flx-column">
            <el-tag type="danger">已下架</el-tag>
            <span style="font-size: 12px">{{ `（${row.delistingRemark}）` }}</span>
          </div>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
          <el-tag type="warning" v-if="row.status == '5'">{{ row.statusName }}</el-tag>
          <el-tag type="warning" v-if="row.status == '6'">{{ row.statusName }}</el-tag>
          <el-tag type="success" v-if="row.status == '7'">交易中</el-tag>
        </template>
        <template #operation="{ row }">
          <el-button text bg type="info" @click="toLog(row)">日志</el-button>
          <el-button text bg v-if="state.hasPermission('shop:shop:update') && row.status != '4'" type="primary" @click="addOrUpdateHandle(row.id, row.gameId)">{{ $t("update") }}</el-button>
          <el-button text bg type="warning" v-if="row.status == '2'" @click="delistClick(row)">下架</el-button>
          <el-button text bg type="success" v-if="row.status == '2'" @click="delistClick(row, 'sold')">售出</el-button>
          <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定上架改商品吗？" @confirm="releasedCommand(1, row)">
            <template #reference>
              <el-button type="primary" link v-if="row.status == '1' || row.status == '3'">上架</el-button>
            </template>
          </el-popconfirm>
          <!-- <el-dropdown placement="top" v-if="row.status == '1' || row.status == '3'" @command="(e: any) => releasedCommand(e, row)">
            <el-button type="primary" text bg>上架</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="2">推送上架</el-dropdown-item>
                <el-dropdown-item :command="1">正常上架</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->

          <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定重新上架改商品吗？" @confirm="onListingAction(row)">
            <template #reference>
              <el-button text bg type="primary" v-if="row.status == '4'">重新上架</el-button>
            </template>
          </el-popconfirm>
          <el-button text bg v-if="state.hasPermission('shop:shop:delete') && (row.status == '1' || row.status == '3')" type="danger" :underline="false" @click="deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" accountSource="PARTNER_SYNCHRONIZATION" source="tenantShop" @refreshDataList="refreshTotal"></add-or-update>
    <!-- 日志 -->
    <log ref="logRef"></log>
    <!-- 下架 -->
    <delist ref="delistRef" @refreshDataList="refreshTotal"></delist>
    <!-- 自动降价 -->
    <job ref="jobRef"></job>
    <!-- 查看商品详情 -->
    <info ref="infoRef"></info>

    <!-- 选择合作商 -->
    <select-partners ref="selectPartnersRef" :key="selectPartnersKey" btnTxt="推送上架" @change="pushSubmit"></select-partners>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
// import AddOrUpdate from "./shop-add-or-update.vue";
import AddOrUpdate from "@/views/shop/shop-add-or-update.vue";
import Delist from "./shop-delist.vue";
import Log from "./shop-log.vue";
import Job from "./shop-job.vue";
import Info from "./shop-info.vue";
import shopStat from "./shop-stat.vue";
import { formatTimeStamp } from "@/utils/method";
import ExcelImport from "./attribute-import.vue";
import baseService from "@/service/baseService";
import SelectPartners from "@/views/shop/components/SelectPartners.vue";
import { getDictDataList, generateUUID } from "@/utils/utils";
import { Search } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { useI18n } from "vue-i18n";
const store = useAppStore();
const settingStore = useSettingStore();
const { t } = useI18n();

const gamesList = ref(<any>[]); // 游戏列表
const totalPrice = ref(<any>""); // 合计数据
const logRef = ref(); // 日志
const delistRef = ref(); // 下架
const jobRef = ref(); // 自动降价
const infoRef = ref(); // 查看商品详情
const shopStatRef = ref();
const timeInterval = ref(); // 时间区间

const view = reactive({
  getDataListURL: "/shop/shop/search",
  getDataListIsPage: true,
  getDataListIsPageSize: true,
  exportURL: "/shop/shop/export",
  deleteURL: "/shop/shop",
  deleteIsBatch: true,
  listRequestMethod: "post",
  customSorting: true,
  dataForm: {
    queryType: "1",
    gameId: "", // 游戏id
    search: "", // 复合搜索框
    status: "", // 状态
    start: "", // 开始时间
    end: "" // 结束时间
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格设置
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: "300"
  },
  // {
  //     prop: "gameName",
  //     label: "游戏名称",
  //     minWidth: "200"
  // },
  // {
  //     prop: "log",
  //     label: "游戏主图",
  //     minWidth: "100"
  // },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: "140"
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: "120"
  },
  {
    prop: "acquisitionPrice",
    label: "回收价(元)",
    minWidth: "100"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "130",
    sortable: "custom"
  },
  {
    prop: "transactionPrice",
    label: "成交价(元)",
    minWidth: "100"
  },
  {
    prop: "grossProfitPrice",
    label: "毛利(元)",
    minWidth: "100"
  },
  // {
  //     prop: "compensation",
  //     label: "包赔",
  //     minWidth: "120"
  // },
  {
    prop: "listingTime",
    label: "上架时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "updateDate",
    label: "修改时间",
    minWidth: "200"
  },
  {
    prop: "status",
    label: "状态",
    fixed: "right",
    minWidth: "100"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "200"
  }
]);

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number, gameId?: any) => {
  addKey.value++;
  if (gameId) {
    nextTick(() => {
      addOrUpdateRef.value.init(gameId, id);
    });
  } else {
    const gameIds = view.dataForm.gameId ? view.dataForm.gameId : gamesList.value[1].id;
    nextTick(() => {
      addOrUpdateRef.value.init(gameIds, id);
    });
  }
};

const groupList = ref([
  { dictLabel: "全部", dictValue: "" },
  { dictLabel: "待上架", dictValue: "1" },
  { dictLabel: "已上架", dictValue: "2" },
  { dictLabel: "已下架", dictValue: "3" },
  { dictLabel: "已出售", dictValue: "4" }
]);
const deleteHandle = (id?: string) => {
  if (state.deleteIsBatch && !id && state.dataListSelections && state.dataListSelections.length <= 0) {
    ElMessage.warning({
      message: t("prompt.deleteBatch"),
      duration: 500
    });
    return;
  }
  ElMessageBox.confirm(t("prompt.info", { handle: t("delete") }), t("prompt.title"), {
    confirmButtonText: t("confirm"),
    cancelButtonText: t("cancel"),
    type: "warning"
  })
    .then(() => {
      baseService.delete(`${state.deleteURL}${state.deleteIsBatch ? "" : "/" + id}`, state.deleteIsBatch ? (id ? [id] : state.dataListSelections ? state.dataListSelections.map((item: any) => state.deleteIsBatchKey && item[state.deleteIsBatchKey]) : {}) : {}).then(() => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            refreshTotal();
          }
        });
      });
    })
    .catch(() => {
      //
    });
};
// 游戏列表
const getGamesList = () => {
  baseService.get("/partner/us/gameList").then((res) => {
    gamesList.value = [{ title: "全部游戏", id: "" }, ...res.data];
    shopTotalPrice();
    shopStatRef.value.getAll(view.dataForm.gameId);
  });
};

// 游戏切换
const gamesChange = (val: any) => {
  view.dataForm.gameId = val;
  refreshFn();
  shopStatRef.value.getAll(view.dataForm.gameId);
};

// 获取表格合计
const shopTotalPrice = () => {
  baseService.get("/shop/shop/totalPrice", { queryType: 1, gameId: view.dataForm.gameId }).then((res) => {
    totalPrice.value = res.data;
  });
};

// 切换状态
const handleStatusClick = (tab: any) => {
  view.dataForm.status = tab;
  refreshFn();
};

// 合计行计算函数
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: any) => {
    if (index === 0) {
      return (sums[index] = "合计");
    } else {
      if (column.label == "回收价(元)") {
        sums[index] = totalPrice.value.acquisitionTotalPrice;
      } else if (column.label == "成交价(元)") {
        sums[index] = totalPrice.value.transactionTotalPrice;
      } else {
        sums[index] = "";
      }
    }
  });
  return sums;
};

// 查看日志
const toLog = (row: any) => {
  nextTick(() => {
    logRef.value.init(row.id);
  });
};

// 商品上架
const onListingAction = (row: any, status = 2) => {
  baseService
    .post("/shop/shop/status", {
      id: row.id,
      release: currentShop.value.release || false,
      partnerIds: currentShop.value.partnerIds,
      status: status
    })
    .then((res) => {
      ElMessage.success("上架成功");
      refreshTotal();
    });
};

// 商品下架
const delistClick = (row: any, sold?: string) => {
  nextTick(() => {
    delistRef.value.init(row.id, sold);
  });
};

// 打开估价网站
const openLink = () => {
  const gameId = view.dataForm.gameId ? view.dataForm.gameId : gamesList.value[1].id;
  window.open(`${settingStore.info.websiteUrl}recycleView?gameId=${gameId}&ny=${generateUUID()}`, "_blank");
};

// 自动降价
const shopJob = () => {
  nextTick(() => {
    jobRef.value.init();
  });
};

// 点击标题查看详情
const toDetails = (row: any) => {
  nextTick(() => {
    infoRef.value.init(row);
  });
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  view.dataForm.orderBy = [
    {
      field: "",
      isAsc: false
    }
  ];
  if (order == "ascending") {
    view.dataForm.orderBy[0].field = prop;
    view.dataForm.orderBy[0].isAsc = true;
  }
  if (order == "descending") {
    view.dataForm.orderBy[0].field = prop;
    view.dataForm.orderBy[0].isAsc = false;
  }
  if (order == null) {
    delete view.dataForm.orderBy;
  }
  state.getDataList();
};

// 查询
const queryFn = () => {
  view.dataForm.start = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.end = timeInterval.value ? timeInterval.value[1] : "";
  refreshFn();
};
// 重置
const resetFn = () => {
  view.dataForm.search = "";
  view.dataForm.start = "";
  view.dataForm.end = "";
  timeInterval.value = "";
  refreshFn();
};

// 刷新页面
const refreshFn = () => {
  shopTotalPrice();
  state.getDataList();
};
// 刷新顶部
const refreshTotal = () => {
  refreshFn();
  shopStatRef.value.getAll(view.dataForm.gameId);
};
onMounted(() => {
  getGamesList();
});

// 上架
const currentShop = ref(<any>{});
const releasedCommand = (e: number, row: any) => {
  currentShop.value = row;
  if (e == 1) {
    // 正常上架
    currentShop.value.release = false;
    onListingAction(currentShop.value, 2);
  } else {
    // 推送上架
    selectPartnersHandle();
  }
};

// 显示选择合作商
const selectPartnersRef = ref();
const selectPartnersKey = ref(0);
const selectPartnersHandle = async () => {
  selectPartnersKey.value++;
  await nextTick();
  selectPartnersRef.value.init();
};

// 推送上架
const pushSubmit = (ids: any) => {
  currentShop.value.release = true;
  currentShop.value.partnerIds = ids;
  onListingAction(currentShop.value, 2);
};
</script>

<style lang="less" scoped>
.shoping {
  display: flex;
  align-items: center;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
.shopSty {
  .buttonSty {
    background: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
  .rr-view-ctx-card {
    .el-link {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
    :deep(.el-link__inner) {
      padding: 0 4px;
      background: #f5f7fa;
      border-radius: 2px 2px 2px 2px;
    }
    .el-tag {
      border: 1px solid;
    }
  }
}
</style>
