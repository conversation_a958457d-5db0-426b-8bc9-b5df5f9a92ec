<template>
  <div class="mod-tenant__tenant">
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
      <ny-table :state="state" :columns="columns" routePath="/tenant/page" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" :selectable="checkSelectable">
        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
        </template>
        <template #header-right>
          <el-input v-model="state.dataForm.tenantName" placeholder="请输入合作商名称" :prefix-icon="Search" clearable></el-input>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>

        <!-- 初始信息 -->
        <template #information="{ row }">
          <el-button type="primary" text @click="copyAccountNumber(row)">查看</el-button>
        </template>

        <!-- 状态 -->
        <template #status="{ row }">
          <el-tag v-if="row.status === 0" type="danger">停用</el-tag>
          <el-tag v-else type="success">正常</el-tag>
        </template>
        <template #idCard="{ row }">
          <span v-if="row.idCard">{{ row.idCard.substring(0, 6) + "********" + row.idCard.substring(13, 17) }}</span>
          <span v-else>-</span>
        </template>

        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button type="primary" text bg @click="addOrUpdateHandle(row.id)" :disabled="row.id === '10000'">
            {{ $t("update") }}
          </el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(row.id)" :disabled="row.id === '10000'">
            {{ $t("delete") }}
          </el-button>
        </template>
      </ny-table>
    <!-- </el-card> -->
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" :key="addOrUpdateKey" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 查看初始化信息 -->
    <el-dialog v-model="InfoDialog" width="500">
      <template #header>
        <div class="InfoDialog_title">
          <el-icon color="#4165D7"><WarningFilled /></el-icon><span>初始信息查看</span>
        </div>
      </template>
      <el-descriptions :column="1" border class="descriptions descriptions-label-140">
        <el-descriptions-item>
          <template #label>合作商名称</template>
          {{ Initialization.tenantName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>登录账号</template>
          {{ Initialization.account }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>初始密码</template>
          {{ Initialization.pwd }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>合作商域名</template>
          {{ Initialization.loginUrl }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>密钥名称</template>
          {{ Initialization.agentName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>密钥ID</template>
          {{ Initialization.keyText }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>接口文档</template>
          {{ ApiFoxUrl }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>接口地址</template>
          {{ sttingInfo.info.interfaceAddressUrl }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="InfoDialog = false">取消</el-button>
          <el-button type="primary" @click="Copyinformation">复制信息</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import AddOrUpdate from "./tenant-add-or-update.vue";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import useClipboard from "vue-clipboard3";
import { useSettingStore } from "@/store/setting";
const sttingInfo = useSettingStore();
const { toClipboard } = useClipboard();

const InfoDialog = ref(false);
const Initialization = ref(<any>""); // 初始化信息

const view = reactive({
  getDataListURL: "/sys/tenant/page",
  getDataListIsPage: true,
  deleteURL: "/sys/tenant",
  deleteIsBatch: true,
  exportURL: "/sys/tenant/export",
  addOrUpdate: ref(),
  dataForm: {
    tenantName: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const checkSelectable = (row:any) =>{
  if(row.id == 10000){
    return false
  } else {
    return true
  }
}

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "tenantName",
    label: "合作商名称",
    minWidth: 120
  },
  {
    prop: "tenantType",
    label: "合作商类型",
    minWidth: 120
  },
  {
    prop: "tenantDomain",
    label: "域名",
    minWidth: 120
  },
  {
    prop: "username",
    label: "登录账号",
    minWidth: 120
  },
  {
    prop: "idCard",
    label: "身份证",
    minWidth: 120
  },
  {
    prop: "realName",
    label: "注册人",
    minWidth: 120
  },
  {
    prop: "information",
    label: "初始信息",
    minWidth: 80
  },
  {
    prop: "status",
    label: "合作商状态",
    minWidth: 80
  },
  {
    prop: "createDate",
    label: "创建时间",
    sortable: true,
    minWidth: 150
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 140
  }
]);

// 获取秘钥详情
const copyAccountNumber = (row: any) => {
  baseService.get("/sys/tenant/accountNumber", { id: row.id }).then((res) => {
    InfoDialog.value = true;
    Initialization.value = res.data;
    Initialization.value.tenantName = row.tenantName;
  });
};
// 复制秘钥
const ApiFoxUrl = "https://apifox.com/apidoc/shared/957db0a3-0dd6-46b7-b5cf-6b4de44ed56d";
const Copyinformation = () => {
  InfoDialog.value = false;
  const info = Initialization.value;
  const textInfo = `合作商名称：${info.name || ""} \n登录账号：${info.account || ""} \n初始密码：${info.pwd || ""} \n合作商域名：${info.loginUrl || ""} \n密钥名称：${info.agentName || ""} \n密钥ID： ${info.keyText || ""} \n接口文档： ${ApiFoxUrl} \n接口地址： ${ sttingInfo.info.interfaceAddressUrl || "" } \n请登录后立即修改密码，防止账号泄露！`;
  copyInfo(textInfo);
};

// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};

// 重置操作
const getResetting = () => {
  view.dataForm.tenantName = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateKey = ref(0);
const addOrUpdateHandle = async (id?: number) => {
  addOrUpdateKey.value++;
  await nextTick();
  addOrUpdateRef.value.init(id);
};
</script>
<style lang="less" scoped>
.InfoDialog_title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  span {
    margin-left: 10px;
  }
}
</style>