<template>
  <!-- 回收订单 -->
  <div class="container acquisition-order TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <ny-table noDataType="3" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.state" @change="state.getDataList"></ny-button-group>
        </template>

        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important" v-model="state.dataForm.keywords" placeholder="请输入游戏账号/自编码/应急手机号" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>

        <template #header-custom>
          <div class="mb-12">
            <el-button type="primary" v-if="state.hasPermission('purchase:purchaseorder:add')" @click="addHandle('add')">新增回收订单</el-button>
            <!-- <el-button type="primary" v-if="state.hasPermission('purchase:purchaseorder:export')" @click="state.exportHandle">导出</el-button> -->
            <!-- <el-button type="danger" v-if="state.hasPermission('purchase:purchaseorder:delete')" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="orderDeleteHandle()">删除</el-button> -->
          </div>
        </template>

        <!-- 回收人 -->
        <template #realName="{ row }">
          {{ row.orderType == 2 ? row.realName1 : row.realName }}
        </template>
        <!-- 回收类型 -->
        <template #acquisitionType="{ row }">
          {{ row.acquisitionType == 2 ? "合作商回收" : "平台回收" }}
        </template>

        <!-- 游戏账号 -->
        <template #gameAccount="{ row }">
          <el-text type="primary" text class="pointer" @click="logHandle(row.id, 'detail', row)">{{ row.state == "待收购" ? row.campNo || "查看属性详情" : row.gameAccount }}</el-text>
        </template>

        <!-- 账号金额 -->
        <!-- <template #amount="{ row }">
          {{ row.orderType == 2 ? row.amount : row?.orderInfo?.acquisitionPrice || row.amount || "-" }}
        </template> -->

        <!-- 包赔信息 -->
        <template #guaranteeInformation="{ row }">
          <el-text v-if="row.orderType == 2" class="pointer" type="primary" text @click="editSimpleOrderHandle({ ...row, checkCollect: true, isCheck: true })">查看</el-text>
          <el-text v-else-if="state.hasPermission('purchase:purchaseorder:getGuaranteeInfoCollect')" class="pointer" type="primary" text @click="guaranteeInfoHandle(row.id)">查看</el-text>
        </template>

        <!-- 包赔费 -->
        <template #guaranteePrice="{ row }">
          {{ row.guaranteePrice || row.orderInfo?.guaranteeAmount || "-" }}
        </template>

        <!-- 备注 -->
        <template #remark="{ row }">
          <span v-if="row.orderType == 2">{{ row.remark }}</span>
          <span v-else>{{ row.changeInfo ? row.changeInfo.remark : "-" }}</span>
          <!-- 暂时不显示 创建商品有回收人时会有的备注 -->
          <span v-if="row.remark && false">({{ row.remark }})</span>
        </template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == '待收购'" type="danger">待回收</el-tag>
          <el-tag v-if="row.state == '待换绑'" type="danger">待换绑</el-tag>
          <el-tag v-if="row.state == '已换绑'" type="primary">已换绑</el-tag>
          <el-tag v-if="row.state == '未入库已打款'" type="warning">已打款</el-tag>
          <el-tag v-if="row.state == '已入库未打款'" type="success">已入库</el-tag>
          <el-tag v-if="row.state == '入库且已打款'" type="success">已入库</el-tag>
          <el-tag v-if="row.state == '已取消'" type="info">已取消</el-tag>
          <div v-if="row.orderType == 2">
            <el-text type="info" size="small">(简易流程)</el-text>
          </div>
          <div>
            <el-text type="info" size="small" v-if="row.state == '待换绑'">{{ row.waitForChangeBindingReason }}</el-text>
          </div>
        </template>

        <!-- 合同状态 -->
        <template #contractState="{ row }">
          <div v-if="row.orderType != 2">
            <template v-if="row.contractState == '未签署'">
              <div>
                <el-tag type="danger"> {{ row.contractState }} </el-tag>
              </div>
              <!-- <div v-if="row.state == '已换绑'">
                <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="signContractHandle(row)">生成合同</el-link>
              </div> -->
            </template>
            <template v-if="row.contractState == '卖方已签署'">
              <div>
                <el-tag type="warning">
                  {{ row.contractState }}
                  <el-icon @click="downPdf(row)"><Management /></el-icon>
                </el-tag>
              </div>
              <div>
                <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="submitsignContractHandle(row)">确认签署</el-link>
              </div>
            </template>
            <template v-if="row.contractState == '合同已锁定'">
              <div>
                <el-tag type="success">
                  <div style="display: flex; gap: 4px; align-items: center">
                    <span>{{ row.contractState }}</span>
                    <el-icon @click="downPdf(row)"><Management /></el-icon>
                  </div>
                </el-tag>
              </div>
              <div>
                <el-link style="font-size: 12px; text-decoration: underline" :underline="false" type="primary" @click="downPdf2(row)">下载证据报告</el-link>
              </div>
            </template>
          </div>
          <div v-else style="display: flex; align-items: center; justify-content: center">
            <span>
              <el-tag v-if="row.contractSign != 1" type="danger"> 未签署 </el-tag>
              <el-tag v-else type="success">
                <div style="display: flex; gap: 4px; align-items: center">
                  <span>已签署</span> <el-icon @click="downFile(row)"><Management /></el-icon>
                </div>
              </el-tag>
            </span>
          </div>
        </template>

        <template #operation="{ row }">
          <operation-btns
            :key="Math.random()"
            :max="4"
            :buttons="[
              {
                text: '创建回收单',
                type: 'primary',
                click: () => addHandle('create', row),
                // 待回收 已取消  并且没有订单号
                isShow: false && showBtn(['待收购', '已取消'], row.state) && state.hasPermission('purchase:purchaseorder:addOrder')
              },
              {
                text: '付款',
                type: 'primary',
                click: () => showPayment(row),
                // 待换绑 并且合作商回收
                isShow: showBtn(['待换绑'], row.state) && row.channelName == '合作商回收' && row.orderType != 2
              },
              {
                text: '换绑',
                type: 'primary',
                click: () => changeBindHandle(row.id),
                // 待换绑 已收集过包赔 并且已确认
                isShow: showBtn(['待换绑'], row.state) && row.guaranteeIds && state.hasPermission('purchase:purchaseorder:changePhone') && row.orderType != 2
              },
              {
                text: '入库',
                type: 'primary',
                click: () => warehousingHandle(row),
                //  换绑 合同已实名 合同已签署  已打款未入库  已完善信息 && row.contractState == '合同已锁定' 暂时将合同不作为必经流程
                isShow: showBtn(['未入库已打款'], row.state) && row.orderInfo && row.orderType != 2
              },
              {
                text: '完善信息',
                type: 'warning',
                click: () => completeInfoHandle(row),
                // 已换绑
                isShow: showBtn(['已换绑', '未入库已打款'], row.state) && state.hasPermission('purchase:purchaseorder:completeInfo') && row.orderType != 2
              },
              {
                text: '收集包赔',
                type: 'warning',
                click: () => collectionGuaranteeHandle(row),
                // 待换绑, 还没收集包赔
                isShow: showBtn(['待换绑'], row.state) && !row.baopei && state.hasPermission('purchase:purchaseorder:createGuaranteeCollectLink') && row.orderType != 2
              },
              {
                text: '包赔确认',
                type: 'warning',
                click: () => guaranteeInfoHandle(row.id),
                // 待换绑, 已收集过包赔信息 还没确认
                isShow: showBtn(['待换绑'], row.state) && row.baopei && !row.guaranteeIds && state.hasPermission('purchase:purchaseorder:getGuaranteeInfoCollect') && row.orderType != 2
              },
              {
                text: '提交打款',
                type: 'success',
                click: () => paymentSubmitHandle(row.id),
                // 已换绑并且合同实名 签署合同  ,已入库未打款  && row.contractState == '合同已锁定' 暂时将合同不作为必经流程
                isShow: showBtn(['已换绑', '已入库未打款'], row.state) && state.hasPermission('purchase:purchaseorder:confirmPayment') && row.orderType != 2
              },
              {
                text: '打款已拒绝',
                type: 'warning',
                click: () => viewReviewDetails(row),
                //
                isShow: showBtn(['已换绑', '待换绑'], row.state) && row.auditEntity && row.auditEntity.status == 2 && state.hasPermission('purchase:purchaseorder:confirmPayment') && row.orderType != 2
              },
              {
                text: '打款信息',
                type: 'success',
                click: () => paymentConfirmedHandle(row.id),
                // 未入库已打款, 已入库未打款
                isShow: showBtn(['未入库已打款', '已入库未打款'], row.state) && state.hasPermission('purchase:purchaseorder:applyPay') && row.orderInfo && row.orderType != 2
              },
              {
                text: '状态设置',
                type: 'warning',
                click: () => statusSettings(row),
                isShow: showBtn(['待换绑'], row.state)
              },
              {
                //废弃
                text: '查看合同',
                type: 'primary',
                click: () => previewHandle(row),
                isShow: false && showBtn(['合同已锁定'], row.contractState) && row.orderType != 2
              },
              // 合作商回收  已入库已打款
              // {
              //   text: '标记售后',
              //   type: 'warning',
              //   click: () => logHandle(row.id, 'log', row, true),
              //   isShow: showBtn(['入库且已打款'], row.state) && row.channelName == '合作商回收'
              // },

              // ================================ 简易流程操作按钮-----------------------------
              {
                text: '编辑',
                type: 'primary',
                click: () => editSimpleOrderHandle(row),
                // 待换绑, 已换绑,
                isShow: showBtn(['已换绑', '未入库已打款', '入库且已打款'], row.state) && state.hasPermission('purchase:purchaseorder:addOrder') && row.orderType == 2
              },
              {
                text: '完善信息并入库',
                type: 'warning',
                click: () => completeInfoHandle(row),
                // 待换绑, 已换绑,
                isShow: showBtn(['未入库已打款'], row.state) && state.hasPermission('purchase:purchaseorder:addOrder') && row.orderType == 2
              },
              {
                text: '取消',
                type: 'danger',
                click: () => cancelOrderHandle(row),
                // 待换绑, 已换绑,
                isShow: showBtn(['待换绑', '已换绑'], row.state) && state.hasPermission('purchase:purchaseorder:cancelOrder')
              },
              {
                text: '日志',
                type: 'info',
                click: () => logHandle(row.id, 'log', row),
                isShow: state.hasPermission('purchase:purchaseorder:logPage')
              },
              {
                text: '删除',
                type: 'danger',
                click: () => orderDeleteHandle(row.id),
                // 已取消
                isShow: showBtn(['已取消'], row.state) && state.hasPermission('purchase:purchaseorder:delete')
              }
            ]"
          />
        </template>

        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span style="font-weight: bold; color: #1d2129">回收价</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>

    <!-- 操作日志 -->
    <operation-log ref="operationLogRef"></operation-log>

    <!-- 删除订单 -->
    <order-delete ref="orderDeleteRef" :key="orderDeleteKey" @refresh="state.getDataList()"></order-delete>

    <!-- 订单入库确认 -->
    <warehousing-confirm ref="warehousingConfirmRef" :key="warehousingConfirmKey" @refresh="state.getDataList()"></warehousing-confirm>

    <!-- 取消订单 -->
    <order-cancel ref="orderCancelRef" :key="orderCancelKey" @refresh="state.getDataList()"></order-cancel>

    <!-- 打款信息 -->
    <make-payment-confirmation ref="paymentConfirmRef" :kye="paymentConfirmKey" @refresh="state.getDataList()"></make-payment-confirmation>

    <!-- 提交打款申请 -->
    <submit-payment-application ref="paymentSubmitRef" :key="paymentSubmitKey" @refresh="state.getDataList()"></submit-payment-application>

    <!-- 签署合同 -->
    <sign-contract @refreshdata="state.getDataList()" ref="signContractRef"></sign-contract>

    <!-- 确认签署 -->
    <submitSign @refreshdata="state.getDataList()" ref="submitSignRef"></submitSign>

    <!-- 完善信息 -->
    <complete-info ref="completeInfoRef" :key="completeInfoKey" @refresh="state.getDataList()"></complete-info>

    <!-- 换绑 -->
    <change-bind ref="changeBindRef" :key="changeBindKey" @refresh="state.getDataList()"></change-bind>

    <!-- 包赔材料 -->
    <guaranteed-materials ref="guaranteedMaterialsRef" :key="guaranteedMaterialsKey" @refresh="state.getDataList()"></guaranteed-materials>

    <!-- 收集包赔 -->
    <collection-guarantee ref="collectionGuaranteeRef" :key="collectionGuaranteeKey"></collection-guarantee>

    <!-- 新增回收订单 -->
    <add-order ref="addOrderRef" :key="addOrderKey" @refresh="state.getDataList()" @completeInfo="completeInfohandle"></add-order>
    <!-- 简易回收的编辑 -->
    <editSimpleOrder ref="editSimpleOrderRef" @refresh="state.getDataList()" @completeInfo="completeInfohandle"></editSimpleOrder>

    <!-- 提交打款拒绝  查看拒绝原因 -->
    <audit-detail ref="auditDetailRef" :key="auditDetailKey" @resubmitPayment="(id: any) => paymentSubmitHandle(id)"></audit-detail>

    <!-- 合作商回收  付款 -->
    <payment ref="paymentRef" :key="paymentKey" @refresh="state.getDataList()"></payment>

    <!-- 回收换绑 -->
    <StatusSettings ref="StatusSettingsRef" @refresh="state.getDataList()"></StatusSettings>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { Search, Management } from "@element-plus/icons-vue";
import { BigNumber } from "bignumber.js";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import OperationLog from "./components/OperationLog.vue";
import OrderDelete from "../components/OrderDelete.vue";
import WarehousingConfirm from "./components/WarehousingConfirm.vue";
import OrderCancel from "../components/OrderCancel.vue";
import MakePaymentConfirmation from "../components/MakePaymentConfirmation.vue";
import SubmitPaymentApplication from "../components/SubmitPaymentApplication.vue";
import SignContract from "../components/SignContract.vue";
import submitSign from "../components/submitSign.vue";
import ContractRealName from "../components/ContractRealName.vue";
import CompleteInfo from "./components/CompleteInfo.vue";
import ChangeBind from "./components/ChangeBind.vue";
import GuaranteedMaterials from "./components/GuaranteedMaterials.vue";
import AddOrder from "./components/AddOrder.vue";
import editSimpleOrder from "./components/editSimpleOrder.vue";
import OperationBtns from "@/components/ny-table/src/components/OperationBtns.vue";
import CollectionGuarantee from "./components/CollectionGuarantee.vue";
import AuditDetail from "./components/AuditDetail.vue";
import Payment from "./components/Payment.vue";
import StatusSettings from "./components/StatusSettings.vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 表格配置项
const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 120
  },
  {
    prop: "gameAccount",
    label: "游戏账号/营地号",
    minWidth: 130
  },
  {
    prop: "state",
    label: "订单状态",
    width: 120
  },
  {
    prop: "channelName",
    label: "回收渠道",
    minWidth: 120
  },
  {
    prop: "amount",
    label: "回收价(元)",
    minWidth: 120,
    sortable: "custom"
  },
  {
    prop: "realName",
    label: "回收人",
    width: 120
  },
  {
    prop: "contractState",
    label: "合同状态",
    minWidth: 130
  },
  {
    prop: "guaranteeInformation",
    label: "包赔信息",
    width: 120
  },
  {
    prop: "guaranteePrice",
    label: "包赔费(元)",
    width: 120
  },
  {
    prop: "ownCoding",
    label: "自编码",
    width: 120
  },
  {
    prop: "emergencyPhone",
    label: "应急手机号",
    width: 120
  },
  {
    prop: "createDate",
    label: "创建时间",
    width: 190
  },
  {
    prop: "remark",
    label: "备注",
    width: 190
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 310
  }
]);

const view = reactive({
  getDataListURL: "/purchase/page",
  getDataListIsPage: true,
  exportURL: "/purchase/export",
  dataForm: {
    gameId: "",
    state: "",
    keywords: "",
    startTime: "",
    endTime: "",
    order: "",
    orderField: ""
  }
});

const state = reactive({
  ...useView(view),
  ...toRefs(view)
});

// 重置操作
const getResetting = () => {
  state.dataForm.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  // { label: "待回收", value: "WAIT_FOR_PUCHASE" },
  { label: "待换绑", value: "WAIT_FOR_CHANGE_BINDING" },
  // 简易回收，未上传付款截图为已换绑
  { label: "已换绑", value: "ALREADY_CHANGE_BINDING" },
  // 简易回收，未完善信息时为已打款
  { label: "已打款", value: "NOT_INBOUND_BUT_REMIT" },
  { label: "已入库", value: "INBOUND_AND_REMIT" },
  { label: "已取消", value: "CANCELED" }
];

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.amount) total += item?.amount || 0;
  });
  return total.toFixed(2);
};

// 新增回收订单
const addOrderRef = ref();
const addOrderKey = ref(0);
const addHandle = async (type?: string, row?: any) => {
  addOrderKey.value++;
  await nextTick();
  addOrderRef.value.init(type, gamesList.value, row, state.dataForm.gameId);
};

// 换绑
const changeBindRef = ref();
const changeBindKey = ref(0);
const changeBindHandle = async (id: number) => {
  changeBindKey.value++;
  await nextTick();
  changeBindRef.value.init(id);
};

// 日志
const operationLogRef = ref();
const logHandle = (id: number, type: string, row: any, mark = false) => {
  if (row.orderType == 2 && type == "detail") {
    // 简易流程
    editSimpleOrderHandle({ ...row, isCheck: true });
    return;
  }
  operationLogRef.value.init(id, mark, type, row);
};

// 删除订单
const orderDeleteRef = ref();
const orderDeleteKey = ref(0);
const orderDeleteHandle = async (id?: number) => {
  orderDeleteKey.value++;
  await nextTick();
  let ids: any[] = [];
  if (!id && state.dataListSelections && state.dataListSelections.length) {
    state.dataListSelections?.map((item) => {
      if (item.state == "已取消") {
        ids.push(item.id);
      }
    });
  }

  if (state.dataListSelections && state.dataListSelections.length && !ids.length) {
    return ElMessage.error("请选择已取消状态的订单进行删除!");
  }

  orderDeleteRef.value.init(id ? [id] : ids, "acquisition");
};

// 入库
const warehousingConfirmRef = ref();
const warehousingConfirmKey = ref(0);
const warehousingHandle = async (row: any) => {
  warehousingConfirmKey.value++;
  await nextTick();
  warehousingConfirmRef.value.init(row);
};

// 取消订单
const orderCancelRef = ref();
const orderCancelKey = ref(0);
const cancelOrderHandle = async (row: any) => {
  orderCancelKey.value++;
  await nextTick();
  orderCancelRef.value.init(row);
};

// 打款信息
const paymentConfirmRef = ref();
const paymentConfirmKey = ref(0);
const paymentConfirmedHandle = async (id: number) => {
  paymentConfirmKey.value++;
  await nextTick();
  paymentConfirmRef.value.init(id);
};

// 提交打款
const paymentSubmitRef = ref();
const paymentSubmitKey = ref(0);
const paymentSubmitHandle = async (id: number) => {
  paymentSubmitKey.value++;
  await nextTick();
  paymentSubmitRef.value.init(id, "acquisition");
};
// 编辑、查看简易流程信息
const editSimpleOrderRef = ref();
const editSimpleOrderHandle = async (row: any) => {
  await nextTick();
  editSimpleOrderRef.value.init(row, gamesList.value);
};

// 签署合同
const signContractRef = ref();
const signContractHandle = async (obj: any) => {
  obj.contractType = "PURCHASE_CONTRACT";
  // SALE_CONTRACT 销售合同
  signContractRef.value.init(obj);
};
// 下载合同
const downPdf = async (obj: any) => {
  let res = await baseService.get("/bestsign/downloadContract/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
const downPdf2 = async (obj: any) => {
  let res = await baseService.get("/bestsign/downloadReport/" + obj.bestsignContractId);
  ElMessage.success("下载成功");
  let a = document.createElement("a");
  a.download = obj.bestsignContractId + obj.contractTitle + "上上签证据报告"; //指定下载的文件名
  a.href = res?.data; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
const downFile = async (row: any) => {
  if (!row.contractAttachment) {
    ElMessage.warning("请先上传合同附件！");
    return;
  }
  let a = document.createElement("a");
  a.download = row.gameName + "/" + row.gameAccount + "/签署合同"; //指定下载的文件名
  a.href = row.contractAttachment; //  URL对象
  a.click(); // 模拟点击
  URL.revokeObjectURL(a.href); // 释放URL 对象
};
const submitSignRef = ref();
const submitsignContractHandle = async (obj: any) => {
  await nextTick();
  submitSignRef.value.init(obj);
};
// 查看合同
const previewHandle = async (obj: any) => {
  if (!obj.bestsignContractId) return;
  window.open(obj?.signLink);
};

// 完善信息
const completeInfoRef = ref();
const completeInfoKey = ref(0);
const completeInfoHandle = async (row: any) => {
  completeInfoKey.value++;
  await nextTick();
  completeInfoRef.value.init(row);
};

// 包赔材料
const guaranteedMaterialsRef = ref();
const guaranteedMaterialsKey = ref(0);
const guaranteeInfoHandle = async (id: number) => {
  guaranteedMaterialsKey.value++;
  await nextTick();
  guaranteedMaterialsRef.value.init(id);
};

// 收集包赔
const collectionGuaranteeRef = ref();
const collectionGuaranteeKey = ref(0);
// 是否有收集包赔的动作
const isCollectCompensationAction = ref(false);
const collectionGuaranteeHandle = async (row: any) => {
  isCollectCompensationAction.value = true;
  collectionGuaranteeKey.value++;
  await nextTick();
  collectionGuaranteeRef.value.init(row);
};

// 根据状态 是否显示按钮
const showBtn = (statusList: any, state: string) => {
  return statusList.includes(state);
};

// 查看审核详情
const auditDetailRef = ref();
const auditDetailKey = ref(0);
const viewReviewDetails = async (row: any) => {
  auditDetailKey.value++;
  await nextTick();
  auditDetailRef.value.init(row);
};

// 如果有收集包赔动作 当前页签激活时刷新列表
document.addEventListener("visibilitychange", () => {
  if (document.visibilityState == "visible" && isCollectCompensationAction.value) {
    state.getDataList();
    isCollectCompensationAction.value = false;
  }
});

// 付款
const paymentRef = ref();
const paymentKey = ref(0);
const showPayment = async (row: any) => {
  paymentKey.value++;
  await nextTick();
  paymentRef.value.open(row);
};

const completeInfohandle = (row: any) => {
  completeInfoHandle(row);
};

// 待换绑 - 状态设置
const StatusSettingsRef = ref();
const statusSettings = (row: any) => {
  StatusSettingsRef.value.init(row.id, row.waitForChangeBindingReason);
};
</script>

<style lang="scss" scoped>
.acquisition-order {
  .contract-icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
</style>
