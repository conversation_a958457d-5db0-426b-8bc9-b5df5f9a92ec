<template>
  <div class="mod-finance__payable-ledger">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table
        :state="state"
        :columns="columns"
        @pageSizeChange="state.pageSizeChangeHandle"
        @pageCurrentChange="state.pageCurrentChangeHandle"
        @selectionChange="state.dataListSelectionChangeHandle">

         <template #header>
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
            <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
            <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
          </template>

        <template #header-right>
          <el-input v-model="state.dataForm.wljsdw" placeholder="请输入往来结算单位" :prefix-icon="Search" style="width: 200px !important" clearable></el-input>
          <el-input v-model="state.dataForm.djbh" placeholder="请输入单据编号" style="width: 160px !important" clearable></el-input>
          <el-select v-model="state.dataForm.ywlx" placeholder="请选择业务类型" style="width: 120px" clearable>
            <el-option label="应收款" value="应收款" />
            <el-option label="应付款" value="应付款" />
          </el-select>
          <el-date-picker
            v-model="createTimeRange"
            type="daterange"
            range-separator="到"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            unlink-panels
            style="width: 220px"
            @change="dateChange" />
          <el-button type="primary" @click="state.getDataList()">查询</el-button>
          <el-button @click="resetFn">重置</el-button>
        </template>

        <template #ywlx="{ row }">
          <el-tag :type="row.ywlx === '应收款' ? 'success' : 'warning'" size="small">
            {{ row.ywlx }}
          </el-tag>
        </template>

        <template #bqys="{ row }">
          <span class="amount-text">{{ formatCurrency(row.bqys) }}</span>
        </template>

        <template #bqys01="{ row }">
          <span class="amount-text paid">{{ formatCurrency(row.bqys01) }}</span>
        </template>

        <template #bqye="{ row }">
          <span class="amount-text unpaid">{{ formatCurrency(row.bqye) }}</span>
        </template>

        <template #cjsj="{ row }">
          <span>{{ formatTimeStamp(row.cjsj) }}</span>
        </template>

        <template #xgsj="{ row }">
          <span>{{ formatTimeStamp(row.xgsj) }}</span>
        </template>

        <template #operation="{ row }">
          <el-button type="info" link size="small" @click="addOrUpdateHandle(row.id, true)">详情</el-button>
          <el-button v-if="row.ywlx === '应付款'" type="success" link size="small" @click="paymentRecordHandle(row)">付款</el-button>
          <el-button type="primary" link size="small" @click="addOrUpdateHandle(row.id)">编辑</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 / 详情 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
     <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
// 修复导入路径 - 创建对应的组件文件
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "wljsdw",
    label: "往来结算单位",
    width: 150
  },
  {
    prop: "djrq",
    label: "单据日期",
    width: 120
  },
  {
    prop: "djlx",
    label: "单据类型",
    width: 100
  },
  {
    prop: "djbh",
    label: "单据编号",
    width: 160
  },
  {
    prop: "ywlx",
    label: "业务类型",
    width: 100
  },
  {
    prop: "bqys",
    label: "本期应收",
    width: 120
  },
  {
    prop: "bqys01",
    label: "本期已收",
    width: 120
  },
  {
    prop: "bqye",
    label: "期末余额",
    width: 120
  },
  {
    prop: "ywy",
    label: "业务员",
    width: 120
  },
  {
    prop: "szbm",
    label: "所在部门",
    width: 100
  },
  {
    prop: "yxmc",
    label: "游戏名称",
    width: 120
  },
  {
    prop: "yxzh",
    label: "游戏账号",
    width: 120
  },
  {
    prop: "yxbh",
    label: "游戏编号",
    width: 120
  },
  {
    prop: "bz",
    label: "备注",
    width: 150
  },
  {
    prop: "cjsj",
    label: "创建时间",
    width: 150
  },
  {
    prop: "xgsj",
    label: "修改时间",
    width: 150
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/dao/fdpayablereceivableledger/page",
  getDataListIsPage: true,
  exportURL: "/dao/fdpayablereceivableledger/export",
  deleteURL: "/dao/fdpayablereceivableledger",
  deleteIsBatch: true,
  dataForm: {
    wljsdw: "",
    djbh: "",
    ywlx: "",
    startCreateDate: "",
    endCreateDate: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();

const addOrUpdateHandle = (id?: number, isView?: boolean) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, isView);
  });
};

// 时间选择变化
const dateChange = () => {
  if (createTimeRange.value) {
    state.dataForm.startCreateDate = createTimeRange.value[0];
    state.dataForm.endCreateDate = createTimeRange.value[1];
  } else {
    state.dataForm.startCreateDate = "";
    state.dataForm.endCreateDate = "";
  }
};

// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 付款记录
const paymentRecordHandle = (row: IObject) => {
  const routeParams = {
    path: `/finance/payment-record`,
    query: {
      payableId: row.id,
      settlementUnit: row.wljsdw,
      _mt: `付款记录 - ${row.wljsdw}`
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 批量付款
const paymentHandle = () => {
  if (!state.dataListSelections || !state.dataListSelections.length) {
    return;
  }
  const ids = state.dataListSelections.map((item: any) => item.id);
  const routeParams = {
    path: `/finance/batch-payment`,
    query: {
      ids: ids.join(','),
      _mt: '批量付款'
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 重置操作
const resetFn = () => {
  createTimeRange.value = undefined;
  state.dataForm.wljsdw = "";
  state.dataForm.djbh = "";
  state.dataForm.ywlx = "";
  state.dataForm.startCreateDate = "";
  state.dataForm.endCreateDate = "";
  state.getDataList();
};

// 导出
const exportHandle = () => {
  baseService.get("/dao/fdpayablereceivableledger/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "应付应收总账列表");
    }
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.amount-text {
  color: #e6a23c;
  font-weight: 500;

  &.paid {
    color: #67c23a;
  }

  &.unpaid {
    color: #f56c6c;
  }
}
</style>




