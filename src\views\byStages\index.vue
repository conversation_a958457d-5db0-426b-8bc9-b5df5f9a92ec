<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title" @change="gamesChange"></ny-flod-tab>
      <statCards ref="statCardsRef" @Examine="toExamine"></statCards>
      <ny-table noDataType="4" cellHeight="ch-96" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <div class="flx-justify-between">
            <ny-button-group
              :list="[
                { dictLabel: '全部', dictValue: '' },
                { dictLabel: '待支付', dictValue: '1' },
                { dictLabel: '待审核', dictValue: '0' },
                { dictLabel: '分期中', dictValue: '2' },
                { dictLabel: '已还清', dictValue: '3' },
                { dictLabel: '标记售后', dictValue: '6' },
                { dictLabel: '已取消', dictValue: '4' }
              ]"
              v-model="view.dataForm.state"
              @change="handleStatusClick"
            ></ny-button-group>
          </div>
        </template>
        <template #header-right>
          <el-input v-model="view.dataForm.keywords" style="width: 240px" placeholder="请输入商品编码/游戏账号" :prefix-icon="Search" clearable />
          <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" />
          <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
          <el-button type="warning" @click="setUpChange">分期设置</el-button>
        </template>
        <template #title="{ row }">
          <div class="shoping">
            <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
            <div class="info">
              <div class="title mle" v-html="row.title"></div>
              <div class="sle" style="width: 185px;text-align: left;">
                {{ `${row.gameName} / ${row.serverName}` }}
              </div>
              <div>
                <span>{{ state.getDictLabel("shop_compensation", row.compensation) }}</span>
              </div>
            </div>
          </div>
        </template>
        <template #state="{ row }">
          <el-tag type="warning" v-if="row.state == 0">待审核</el-tag>
          <el-tag type="danger" v-if="row.state == 1">待支付</el-tag>
          <el-tag type="primary" v-if="row.state == 2">分期中</el-tag>
          <el-tag type="success" v-if="row.state == 3">已还清</el-tag>
          <el-tag type="info" v-if="row.state == 4">已取消</el-tag>
          <el-tag style="color: #D91AD9;background: #FFE8FB;" v-if="row.state == 5">已违约</el-tag>
          <el-tag type="warning" v-if="row.state == 6">标记售后</el-tag>
          <el-tag type="primary" v-if="row.state == 7">二次回收</el-tag>
        </template>
        <template #byStagesPeriods="{ row }">
          <span>{{ row.alreadyRepaidPeriods }}/{{ row.byStagesPeriods }}</span>
        </template>
        <template #payCredentials="{row}">
          <el-image style="height: 68px; width: 68px;border-radius: 4px;" :src="row.payCredentials" :preview-src-list="[row.payCredentials]" preview-teleported fit="cover" v-if="row.payCredentials"/>
          <span v-else>-</span>
        </template>
        <template #downPaymentsPayTime="{ row }">
          <span>{{ formatTimeStamp(row.downPaymentsPayTime) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button type="primary" link @click="paymentConfirmationChagne(row)" v-if="row.state == 1">收款确认</el-button>
          <el-button type="primary" link @click="repaymentChange(row,1)" v-if="row.state == 2">还款</el-button>
          <el-button type="primary" link @click="InstallmentReviewChange(row)" v-if="row.state == 0 && row.isAudit == 1">审核</el-button>
          <el-button type="primary" link @click="handleRecycle(row)" v-if="row.state == 2 || row.state == 3">二次回收</el-button>
          <el-button type="success" link @click="repaymentChange(row,2)" v-if="row.state == 2">提前还款</el-button>
          <el-button type="warning" link @click="operationLogHandle(row, true, '订单标记售后')"v-if="row.state == 0 || row.state == 2 || row.state == 3 || row.state == 5">标记售后</el-button>
          <el-button type="danger" link @click="cancel(row)" v-if="row.state == 1">取消</el-button>
          <el-button type="info" link @click="operationLogHandle(row, false, '操作日志')">日志</el-button>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span class="tableSort">
              <NyDropdownMenu
                :isBorder="false"
                v-model="SummariesParams"
                :list="[
                  { label: '成交价', value: 1 },
                  { label: '分期金额', value: 2 },
                  { label: '每月应还金额', value: 3 },
                  { label: '首付款', value: 4 },
                  { label: '手续费总额', value: 5 },
                ]"
                labelKey="label"
                valueKey="value"
                isTop
              ></NyDropdownMenu>
            </span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>
  </div>
  <!-- 分期设置 -->
  <byStagesSetup ref="byStagesSetupRef"></byStagesSetup>
  <!-- 还款 -->
  <repayment ref="repaymentRef" @refresh="refreshFun"></repayment>
  <!-- 审核 -->
  <InstallmentReview ref="InstallmentReviewRef" @refresh="refreshFun"></InstallmentReview>
  <!-- 收款确认 -->
  <paymentConfirmation ref="paymentConfirmationRef" @refresh="refreshFun"></paymentConfirmation>
  <!-- 操作日志 -->
  <operation-log ref="operationLogRef" :key="operationLogKey" @refresh="refreshFun"></operation-log>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRefs, nextTick } from "vue";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { formatTimeStamp } from "@/utils/method";
import statCards from "./components/statCards.vue";
import byStagesSetup from "./components/byStagesSetup.vue";
import repayment from "./components/repayment.vue";
import InstallmentReview from "./components/InstallmentReview.vue";
import paymentConfirmation from "./components/paymentConfirmation.vue";
import OperationLog from "./components/OperationLog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAppStore } from "@/store";
const store = useAppStore();
const gamesList = ref(<any>[]); // 游戏列表
const timeInterval = ref(); // 时间区间

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/sale/saleOrderByStages/page",
  getDataListIsPage: true,
  getDataListIsPageSize: true,
  customSorting: true,
  dataForm: {
    gameId: "", // 游戏id
    state: "",
    keywords: "",
    startTime: "",
    endTime: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = ref([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: "140"
  },
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "title",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: "140"
  },
  {
    prop: "state",
    label: "订单状态",
    minWidth: "100"
  },
  {
    prop: "transactionPrice",
    label: "成交价(元)",
    minWidth: "100"
  },
  {
    prop: "byStagesMoney",
    label: "分期金额(元)",
    minWidth: "160",
  },
  {
    prop: "repaymentDay",
    label: "还款日",
    minWidth: "100"
  },
  {
    prop: "byStagesPeriods",
    label: "分期期数(月)",
    minWidth: "120"
  },
  {
    prop: "monthRepaymentAmount",
    label: "每月应还金额(元)",
    minWidth: "180"
  },
  {
    prop: "downPayments",
    label: "首付款(元)",
    minWidth: "100"
  },
  {
    prop: "commissionTotal",
    label: "手续费总额(元)",
    minWidth: "160",
  },
  {
    prop: "downPaymentsPayTime",
    label: "首付款支付时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "buyerPhone",
    label: "买方手机号",
    width: 120
  },
  {
    prop: "sellerName",
    label: "销售人",
    minWidth: "100"
  },
  {
    prop: "saleTypeName",
    label: "销售类型",
    minWidth: "100"
  },
  {
    prop: "sellerChannelName",
    label: "销售渠道",
    width: 120
  },
  {
    prop: "thirdOrderNumber",
    label: "三方订单号",
    width: 120
  },
  {
    prop: "payCredentials",
    label: "付款凭证",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "330"
  }
]);

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ title: "全部游戏", id: "" }, ...res.data];
    state.getDataList();
  });
};

const refreshFun = () =>{
  statCardsRef.value.getAll(view.dataForm.gameId);
  state.getDataList();
}

// 游戏切换
const gamesChange = (val: any) =>{
  view.dataForm.gameId = val;
  statCardsRef.value.getAll(val);
  state.getDataList();
}

// 状态切换
const handleStatusClick = (tab: any) =>{
  view.dataForm.state = tab;
  state.getDataList();
}

// 统计卡片
const statCardsRef = ref();

// 查询
const queryFn = () => {
  view.dataForm.startTime = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.endTime = timeInterval.value ? timeInterval.value[1] : "";
  statCardsRef.value.getAll(view.dataForm.gameId);
  state.getDataList();
};

// 重置
const resetFn = () => {
  view.dataForm.keywords = "";
  view.dataForm.startTime = "";
  view.dataForm.endTime = "";
  timeInterval.value = "";
  state.getDataList();
};


// 分期设置
const byStagesSetupRef = ref();
const setUpChange = () =>{
  byStagesSetupRef.value.init();
}

// 马上还款
const toExamine = (id:any) =>{
  baseService.get('/sale/saleOrderByStages/page',{id}).then(res=>{
    if(res.code == 0){
      repaymentChange(res.data.list[0],1);
    }
  })
}

// 还款
const repaymentRef = ref();
const repaymentChange = (row:any, type: number) =>{
  repaymentRef.value.init(row, type)
}

// 审核
const InstallmentReviewRef = ref();
const InstallmentReviewChange = (row:any) =>{
  InstallmentReviewRef.value.init(row);
}

// 收款确认
const paymentConfirmationRef = ref();
const paymentConfirmationChagne = (row:any) =>{
  paymentConfirmationRef.value.init(row)
}

// 二次回收
const handleRecycle = (data: any) => {
  ElMessageBox.confirm("是否申请将商品二次回收？", "申请回收", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService
      .put("/sale/saleOrderByStages/secondRecycle", {
        gameAccount: data.gameAccount,
        orderSource: "4",
        orderSourceLabel: "分期订单-二次回收",
        submitter: store.state.user.realName,
        submitterId: store.state.user.id,
        accountPoolType: "0",
        gameId: data.gameId,
        gameName: data.gameName,
        orderId: data.id
      })
      .then((res) => {
        ElMessage.success("已回收至账号资源池!");
        state.query();
      });
  });
};

// 取消
const cancel = (row:any) =>{
  ElMessageBox.confirm("是否取消当前分期订单？", "取消订单", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService
      .put("/sale/saleOrderByStages/cancel/" + row.id)
      .then((res) => {
        ElMessage.success("订单取消成功!");
        state.query();
      });
  });
}

// 操作日志 or 标记售后
const operationLogRef = ref();
const operationLogKey = ref(0);
const operationLogHandle = async (row: any, marker?: boolean, type?: string) => {
  console.log(row);
  operationLogKey.value++;
  await nextTick();
  operationLogRef.value.init(row, marker, type);
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item?.transactionPrice) total += item?.transactionPrice || 0;
    });
  } else if(SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item?.byStagesMoney) total += item?.byStagesMoney || 0;
    });
  }else if(SummariesParams.value == 3) {
    state.dataList.map((item: any) => {
      if (item?.monthRepaymentAmount) total += item?.monthRepaymentAmount || 0;
    });
  }else if(SummariesParams.value == 4) {
    state.dataList.map((item: any) => {
      if (item?.downPayments) total += item?.downPayments || 0;
    });
  }else if(SummariesParams.value == 5) {
    state.dataList.map((item: any) => {
      if (item?.commissionTotal) total += item?.commissionTotal || 0;
    });
  }
  return total.toFixed(2);
};

onMounted(() => {
  getGamesList();
});
</script>

<style lang="less" scoped>
.rr-view-ctx-card {
  :deep(.el-card__body) {
    padding-top: 12px;
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;

  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
