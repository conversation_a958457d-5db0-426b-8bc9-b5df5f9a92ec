<template>
    <div class="stat_card">
        <div class="stat_item"
            style="background: linear-gradient(135deg, rgba(230, 244, 254, 0.47) 24.79%, rgba(217, 224, 247, 0.10) 58.47%), #FFF;">
            <div class="stat_top flx-justify-between" style="padding: 12px 0px 12px 12px;">
                <div class="stat_top_title flx-align-center">
                    <!-- <img :src="shopStat1" class="icon"> -->
                    <span class="name">今日平台成交(元)</span>
                </div>
                <div class="stat_top_time">
                    <el-date-picker v-model="params1.date" type="date" format="MM/DD" value-format="YYYY-MM-DD"
                        size="small" style="width: 65px" :clearable="false" @change="getShopReport1" />
                </div>
            </div>
            <div class="stat_middle">{{ formatCurrency(dataForm1.price) || 0 }}</div>
            <div class="stat_below">
                <span class="sum_label">平台总成交：</span>
                <span class="sum_value" style="color: #F44A29;">{{ formatCurrency(dataForm1.totalPrice) || 0 }}</span>
            </div>
        </div>

        <div class="stat_item"
            style="background: linear-gradient(135deg, rgba(230, 254, 234, 0.47) 24.79%, rgba(217, 224, 247, 0.10) 58.47%), #FFF;">
            <div class="stat_top flx-justify-between" style="padding: 12px 0px 12px 12px;">
                <div class="stat_top_title flx-align-center">
                    <!-- <img :src="shopStat2" class="icon"> -->
                    <span class="name">今日平台毛利率(%)</span>
                </div>
                <div class="stat_top_time">
                    <el-date-picker v-model="params2.date" type="date" format="MM/DD" value-format="YYYY-MM-DD"
                        size="small" style="width: 65px;" :clearable="false" @change="getShopReport2" />
                </div>
            </div>
            <div class="stat_middle">{{ formatCurrency(dataForm2.price) || 0 }}</div>
            <div class="stat_below">
                <span class="sum_label">平台总利润：</span>
                <span class="sum_value" style="color: #00B42A;">{{ formatCurrency(dataForm2.totalPrice) || 0 }}</span>
            </div>
        </div>

        <div class="stat_item"
            style="background: linear-gradient(135deg, rgba(254, 241, 230, 0.47) 24.79%, rgba(217, 224, 247, 0.10) 58.47%), #FFF;">
            <div class="stat_top flx-justify-between" style="padding: 12px 0px 12px 12px;">
                <div class="stat_top_title flx-align-center">
                    <!-- <img :src="shopStat3" class="icon"> -->
                    <span class="name">今日代售成交(元)</span>
                </div>
                <div class="stat_top_time">
                    <el-date-picker v-model="params5.date" type="date" format="MM/DD" value-format="YYYY-MM-DD"
                        size="small" style="width: 65px;" :clearable="false" @change="getShopReport5" />
                </div>
            </div>
            <div class="stat_middle">{{ formatCurrency(dataForm5.price) || 0 }}</div>
            <div class="stat_below flx-align-center">
                <div style="margin-right: 12px;">
                    <span class="sum_label">总成交：</span>
                    <span class="sum_value" style="color: #FF9A2E;">{{ formatCurrency(dataForm5.totalPrice) || 0
                    }}</span>
                </div>
                <div>
                    <span class="sum_label">总利润：</span>
                    <span class="sum_value" style="color: #FF9A2E;">{{ formatCurrency(dataForm6.totalPrice) || 0
                    }}</span>
                </div>
            </div>
        </div>

        <div class="stat_item"
            style="background: linear-gradient(135deg, rgba(230, 238, 254, 0.47) 24.79%, rgba(217, 224, 247, 0.10) 58.47%), #FFF;">
            <div class="stat_top flx-justify-between">
                <div class="stat_top_title flx-align-center">
                    <!-- <img :src="shopStat4" class="icon"> -->
                    <span class="name">库存成本(元)</span>
                </div>
                <div class="stat_top_time">
                    <NyDropdownMenu :isBorder="false" v-model="params3.dateType" :list="[
                        { label: '本年', value: 4 },
                        { label: '本月', value: 2 },
                        { label: '本周', value: 1 }
                    ]" labelKey="label" valueKey="value" style="height: 20px; line-height: 20px"
                        @change="getShopReport3()"></NyDropdownMenu>
                </div>
            </div>
            <div class="stat_middle">{{ formatCurrency(dataForm3.price) || 0 }}</div>
            <div class="stat_below">
                <span class="sum_label">当前总成本：</span>
                <span class="sum_value" style="color: #165DFF;">{{ formatCurrency(dataForm3.totalPrice) || 0 }}</span>
            </div>
        </div>

        <div class="stat_item"
            style="background: linear-gradient(135deg, rgba(232, 230, 254, 0.47) 24.79%, rgba(217, 224, 247, 0.10) 58.47%), #FFF;">
            <div class="stat_top flx-justify-between">
                <div class="stat_top_title flx-align-center">
                    <!-- <img :src="shopStat3" class="icon"> -->
                    <el-tooltip effect="dark" content="周转率计算=出售商品数量/总商品数量" placement="top-end">
                        <span class="name flx-align-center">库存周转率(%)
                            <el-icon color="#303133" size="14" style="margin-left: 4px;">
                                <QuestionFilled />
                            </el-icon>
                        </span>
                    </el-tooltip>
                </div>
                <div class="stat_top_time">
                    <NyDropdownMenu :isBorder="false" v-model="params4.dateType" :list="[
                        { label: '本年', value: 4 },
                        { label: '本月', value: 2 },
                        { label: '本周', value: 1 }
                    ]" labelKey="label" valueKey="value" style="height: 20px; line-height: 20px"
                        @change="getShopReport4()"></NyDropdownMenu>
                </div>
            </div>
            <div class="stat_middle">{{ dataForm4.turnoverRate }}</div>
            <div class="stat_below flx-align-center">
                <div style="margin-right: 12px;">
                    <span class="sum_label">期初数：</span>
                    <span class="sum_value" style="color: #722ED1;">{{ formatCurrency(dataForm4.initial) || 0 }}</span>
                </div>
                <div>
                    <span class="sum_label">当前数：</span>
                    <span class="sum_value" style="color: #722ED1;">{{ formatCurrency(dataForm4.ending) || 0 }}</span>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import shopStat1 from "@/assets/images/shop_stat1.png";
import shopStat2 from "@/assets/images/shop_stat2.png";
import shopStat3 from "@/assets/images/shop_stat3.png";
import shopStat4 from "@/assets/images/shop_stat4.png";
import shopStat5 from "@/assets/images/shop_stat5.png";

const data = reactive({
    gameId: ''
})

const getAll = (gameId: any) => {
    data.gameId = gameId;
    getShopReport1();
    getShopReport2();
    getShopReport3();
    getShopReport4();
    getShopReport5();
    getShopReport6();
}

// 今日平台成交
const params1 = reactive({
    type: '1',
    accountSource: 'ORIGINAL_OWNER',
    date: ''
})
const dataForm1 = ref(<any>'');
const getShopReport1 = () => {
    baseService.post("/shop/shop/report", { ...params1, gameId: data.gameId }).then(res => {
        dataForm1.value = res.data
    })
}

// 今日平台毛利
const params2 = reactive({
    type: '2',
    accountSource: 'ORIGINAL_OWNER',
    date: ''
})
const dataForm2 = ref(<any>'');
const getShopReport2 = () => {
    baseService.post("/shop/shop/report", { ...params2, gameId: data.gameId }).then(res => {
        dataForm2.value = res.data
    })
}

// 库存成本
const params3 = reactive({
    type: '3',
    dateType: '4'
})
const dataForm3 = ref(<any>'');
const getShopReport3 = () => {
    baseService.post("/shop/shop/report", { ...params3, gameId: data.gameId }).then(res => {
        dataForm3.value = res.data
    })
}
const report3Click = (val: any) => {
    params3.dateType = val;
    getShopReport3()
}

// 库存周转率
const params4 = reactive({
    type: '4',
    dateType: '4'
})
const dataForm4 = ref(<any>'');
const getShopReport4 = () => {
    baseService.post("/shop/shop/report", { ...params4, gameId: data.gameId }).then(res => {
        dataForm4.value = res.data
    })
}
const report4Click = (val: any) => {
    params4.dateType = val;
    getShopReport4()
}

// 今日代售成交
const params5 = reactive({
    accountSource: 'PLATFORM_CONSIGNMENT',
    date: ''
})
const dataForm5 = ref(<any>'');
const getShopReport5 = () => {
    baseService.post("/shop/shop/report", { ...params5, gameId: data.gameId, type: '1' }).then(res => {
        dataForm5.value = res.data
    })
}

// 今日代售成交 - 总利润
const dataForm6 = ref(<any>'');
const getShopReport6 = () => {
    baseService.post("/shop/shop/report", { ...params5, gameId: data.gameId, type: '2' }).then(res => {
        dataForm6.value = res.data
    })
}


// 金额格式化
const formatCurrency = (number: number) => {
    if (isNaN(number) || number === null) return;
    const numStr = number.toString();
    const decimalIndex = numStr.indexOf('.');
    const integerNum = decimalIndex >= 0 ? numStr.substring(0, decimalIndex) : numStr;
    const integerStr = integerNum.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return decimalIndex >= 0 ? integerStr + numStr.substring(decimalIndex) : integerStr;
}


onMounted(() => {
    params1.date = formatDate(new Date(), 'YYYY-MM-DD');
    params2.date = formatDate(new Date(), 'YYYY-MM-DD');
    params5.date = formatDate(new Date(), 'YYYY-MM-DD');
})

// 格式化日期
const formatDate = (value: any, type: any) => {
    // 计算日期相关值
    let time = typeof value == 'number' ? new Date(value) : value;
    let Y = time.getFullYear();
    let M = time.getMonth() + 1;
    let D = time.getDate();
    let h = time.getHours();
    let m = time.getMinutes();
    let s = time.getSeconds();
    let week = time.getDay();

    if (type == undefined) {
        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);
    } else if (type == 'week') {
        return week + 1;
    } else if (type == 'YYYY-MM-DD') {
        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D);
    } else if (type == 'YYYY-MM') {
        return Y + '-' + (M < 10 ? '0' + M : M);
    } else if (type == 'YYYY') {
        return Y;
    }
}

defineExpose({
    getAll
});

</script>

<style lang='less' scoped>
.stat_card {
    display: flex;
    align-content: center;
    justify-content: space-between;
    gap: 12px;

    .stat_item {
        // width: 19.5%;
        flex: 1;
        background: #FFFFFF;
        border-radius: 8px;
        border: 1px solid #EBEEF5;

        .stat_top {
            padding: 12px;

            .stat_top_title {
                .icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                }

                .name {
                    font-weight: 400;
                    font-size: 14px;
                    color: #303133;
                    line-height: 16px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }

            .stat_top_time {
                :deep(.el-text) {
                    cursor: pointer;
                }

                :deep(.ny_dropdown_menu) {
                    padding: 0;
                }

                :deep(.clickValue),
                :deep(.placeholder) {
                    line-height: normal;
                    cursor: pointer;
                }

                :deep(.el-date-editor) {
                    line-height: 20px;
                    height: 20px;

                    .el-input__prefix {
                        position: absolute;
                        right: 4px;

                        .el-input__prefix-inner {
                            color: #303133;
                        }
                    }

                    .el-input__wrapper {
                        box-shadow: none;
                        background-color: transparent;

                        .el-input__inner {
                            cursor: pointer;
                        }
                    }
                }
            }
        }

        .stat_middle {
            padding: 4px 16px;
            font-weight: bold;
            font-size: 16px;
            color: #303133;
            line-height: 19px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }

        .stat_below {
            padding: 4px 16px 12px 16px;

            .sum_label {
                font-weight: 400;
                font-size: 12px;
                color: #606266;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }

            .sum_value {
                font-weight: 400;
                font-size: 12px;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>