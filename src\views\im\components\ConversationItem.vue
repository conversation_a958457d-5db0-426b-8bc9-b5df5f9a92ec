<template>
    <div class="conversation-item" :class="{ active: item.targetId == imStore.currentConversation.targetId }">
        <div class="is-top" v-if="showIsTop && item.isTop"></div>
        <div class="conversation-left">
            <!-- 单聊 -->
            <im-avatar v-if="item.conversationType == '1'" class="avatar" :user="item.sessionData"></im-avatar>
            <!-- <img v-if="item.conversationType == '1'" class="avatar" :src="item.sessionData ? item.sessionData.headUrl : ''" /> -->

            <!-- 群类型  type 1交易群，2中介群3普通群 4收购群 -->
            <!-- 交易群   orderFlow 1·创建群聊2审核包赔3买家验号，4账号换绑5财务放款6交易完成7交易失败-->
            <div v-else-if="item.conversationType == '3' && item.sessionData && (item.sessionData.type == 1 || item.sessionData.type == 4)" class="avatar">
                <img v-if="item.sessionData.orderFlow == 7" src="../../../assets/images/im_group_cancel.png">
                <img v-else-if="item.sessionData.orderFlow == 6" src="../../../assets/images/im_group_completed.png">
                <img v-else src="../../../assets/images/im_group_changebind.png">
            </div>  

            <!-- 群组 -->
            <div v-else-if="item.sessionData && item.conversationType == '3'" class="avatar group group-3">
                <ul>
                    <li v-for="(member, index) in item.sessionData.userVos" :key="index">
                        <im-avatar class="avatar" :user="member"></im-avatar>
                    </li>
                </ul>
            </div>  

            <div class="name">
                <div class="title flx-justify-between">
                    <!-- 单聊 -->
                    <div class="nickname sle flx-align-center" v-if="item.conversationType == '1'">
                        {{ item.sessionData ? item.sessionData.nickname : '' }}
                        <template v-if="item.sessionData.imRole">
                            <el-tag 
                                v-if="item.sessionData && item.sessionData.imRole!=0" 
                                :type="item.sessionData.imRole == 1 ? 'primary' : 'warning'" 
                                size="small"
                                class="ml-8"
                            >
                                {{ item.sessionData.imRole == 1 ? '客服' : '认证商户' }}
                            </el-tag>
                        </template>
                        
                    </div>
                    <!-- 群组 -->
                    <div class="nickname sle" v-if="item.conversationType == '3'">{{ item.sessionData ? item.sessionData.groupName : '' }}
                        <!-- 免打扰 -->
                        <el-icon class="ml-10" v-if="item.notificationLevel == 5"><MuteNotification /></el-icon>
                    </div>
                    <div class="time" v-if="item!.latestMessage && showIsTop">{{ timeStringAutoShort(item!.latestMessage!.sentTime) }}</div>
                </div>
                <div class="last-msg flx-justify-between" v-if="item!.latestMessage && item!.latestMessage.content && showIsTop">
                    <!-- 该会话有人@我 未读状态就会一直显示 -->
                    <div class="sle" v-if="item.hasMentioned && item.unreadMessageCount"><el-text type="danger">[有人@我]</el-text></div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:ImgMsg'">[图片]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:FileMsg'">[视频]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:shop'">[商品]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:image'">[图片]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:order'">[订单]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:createGroup'">[创建群聊]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:uploadGroup'">[更新群]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:orderFlow'">[订单流程]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:flow'">[订单流程更新]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:collect'">[收集包赔信息]</div>
                    <div class="sle" v-else-if="item.latestMessage.messageType == 'RC:IWNormalMsg' && item.latestMessage.content.msgType == 'CU:contract'">[签署合同]</div>
                    <div class="sle" v-else>
                        <!-- 当前会话有人@我  并且会话是未读状态 -->
                        <el-text 
                            type="danger" 
                            v-if="item?.latestMessage?.content.mentionedInfo 
                            && item?.latestMessage?.content?.mentionedInfo.userIdList.includes(imStore.imUid)
                            && item.unreadMessageCount"
                        >
                            [有人@我]
                        </el-text>
                        {{ item?.latestMessage?.content?.content }}
                    </div>
                    
                    <el-badge v-if="item.unreadMessageCount && item.notificationLevel != 5" class="unread-num" :value="item.unreadMessageCount"></el-badge>
                    <div v-if="item.unreadMessageCount && item.notificationLevel == 5" class="unread"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { defineProps } from 'vue';
    import { useImStore } from '@/store/im';
    import { timeStringAutoShort } from '@/utils/imTool';
    import ImAvatar from './ImAvatar.vue';
    
    const imStore = useImStore();
    
    defineProps({
        item: Object,
        showIsTop: Boolean,
    })
    
</script>

<style lang="scss">
    .conversation-item {
        padding: 20px 16px;
        cursor: pointer;
        position: relative;

        .is-top {
            position: absolute;
            left: 0;
            top: 0px;
            width: 0;
            height: 0;
            border-top: 10px solid var(--el-color-primary);
            border-right: 10px solid transparent;
        }

        

        .conversation-left {
            flex: 1;
            display: flex;
            align-items: center;

            .avatar {
                width: 42px;
                height: 42px;
                border-radius: 4px;
                overflow: hidden;
                display: inline-block;
            }

            .name {
                // flex: 1;
                width: calc(100% - 50px);
                line-height: 1;
                padding-left: 8px;

                .title {
                    .time {
                        font-size: 12px;
                        color: #A8ABB2;
                    }

                    .el-icon{
                        color: #A8ABB2;
                    }

                    .nickname {
                        flex: 1;
                    }

                }

                .last-msg {
                    color: #909399;
                    padding-top: 8px;

                    .unread {
                        display: inline-block;
                        background: #FF0000;
                        font-size: 11px;
                        color: #fff;
                        border-radius: 10px;
                        width: 12px;
                        height: 12px;
                    }

                    .unread-num {
                        display: inline-block;

                        :deep(.el-badge__content) {
                            background: #FF0000 !important;
                            font-size: 11px;
                            height: 16px;
                            padding: 0 4px;
                        }
                    }
                }
            }
        }

        &:hover {
            background: #E6E6E6;
        }

        &.active {
            background: var(--el-color-primary);
            color: #fff;

            .name .title .time,
            .name .last-msg,
            .el-icon {
                color: #fff !important;
            }
            .is-top{
                border-top: 10px solid #fff;
            }
        }

    }

    .group {
        background: #fff;

        .text-avatar{
            font-size: 14px !important;
        }
        
        ul,
        li {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        li {
            outline: 1px solid #fff;
            display: flex;

            .avatar{
                width: 12px !important;
                height: 12px !important;
                border-radius: 2px;
                margin: 1px;
            }
        }

        /* 3个图片 */
        li:first-child:nth-last-child(3),
        li:first-child:nth-last-child(3)~li {
            width: 50%;
            .avatar{
                width: 18px !important;
                height: 18px !important;
                border-radius: 2px;
                margin: 1px;
            }
        }

        li:first-child:nth-last-child(3) {
            margin: auto;
        }

        li:first-child:nth-last-child(3)~li {
            float: left;
        }

        /* 4个图片 */
        li:first-child:nth-last-child(4),
        li:first-child:nth-last-child(4)~li {
            width: 50%;
            float: left;
            .avatar{
                width: 18px !important;
                height: 18px !important;
                border-radius: 2px !important;
            }
        }

        /* 5个或8个图片 */
        li:first-child:nth-last-child(5),
        li:first-child:nth-last-child(5)~li,
        li:first-child:nth-last-child(8),
        li:first-child:nth-last-child(8)~li {
            width: 33%;
            float: left;
        }

        li:first-child:nth-last-child(5),
        li:first-child:nth-last-child(5)+li {
            margin-top: 16%;
        }

        li:first-child:nth-last-child(5),
        li:first-child:nth-last-child(8) {
            margin-left: 15%;
        }

        /* 6个图片 */
        li:first-child:nth-last-child(6),
        li:first-child:nth-last-child(6)~li {
            width: 33%;
            float: left;
        }

        li:first-child:nth-last-child(6),
        li:first-child:nth-last-child(6)+li,
        li:first-child:nth-last-child(6)+li+li {
            margin-top: 16%;
        }

        /* 7个图片 */
        li:first-child:nth-last-child(7),
        li:first-child:nth-last-child(9),
        li:first-child:nth-last-child(7)~li,
        li:first-child:nth-last-child(9)~li {
            width: 33%;
            float: left;
        }

        li:first-child:nth-last-child(7) {
            float: none;
            margin: auto;
        }
    }
</style>