<template>
    <el-dialog v-model="visible" class="order-confirm-dialog" :close-on-click-modal="false" :close-on-press-escape="false" @close="close" width="480px">
        <template #header="{ close, titleId, titleClass }">
            <div class="flx-justify-between confirm-title">
                <div class="title flx-center">
                    <el-text :type="type"><el-icon size="16" class="icon"><InfoFilled /></el-icon></el-text>
                    {{ title }}
                </div>
            </div>
        </template>

        <div class="confirm-content">
            <slot></slot>
        </div>

            <template #footer v-if="showFooter">
                <slot name="footer">
                    <el-button :loading="btnLoading" @click="close">取消</el-button>
                    <el-button :loading="btnLoading" :type="type" @click="confirmHandle">{{ confirmText }}</el-button>
                </slot> 
            </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref, defineProps, watch, defineEmits } from "vue";
    import { InfoFilled } from "@element-plus/icons-vue";

    const visible = ref(false);
    const props = defineProps({
        show: Boolean,
        // 标题
        title: String,
        // 确认按钮文字
        confirmText: String,
        // 图标和按钮颜色
        type: {
            type: String,
            default: "danger"
        },
        // 是否显示底部操作
        showFooter: {
            type: Boolean,
            default: true
        },
        // loading
        loading: {
            type: Boolean,
            default: false
        }
    });

    const emits = defineEmits(["close", "confirm"]);  

    watch(() => props.show, (newValue) => {
        visible.value = newValue;
    })
    
    const btnLoading = ref(props.loading);
    watch(() => props.loading, (newValue) => {
        btnLoading.value = newValue;
    })

    // 关闭
    const close = () => {
        visible.value = false;
        emits("close");
    }

    // 提交
    const confirmHandle = () => {
        emits("confirm");
    }
</script>

<style lang="scss">
    .order-confirm-dialog{
        padding: 24px;

        .confirm-title{
            font-size: 18px;
            font-weight: bold;
            line-height: 26px;
    
            .icon{
                margin-right: 10px;
            }
        }

        .text{
            p{
                line-height: 22px;
                padding: 0;
                margin: 0;
            }
            ul{
                padding: 0;
                margin: 0;
                li{
                    line-height: 22px;
                }
            }
            padding: 10px 0;
        }
    }
</style>