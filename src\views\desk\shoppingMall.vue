<template>
  <div class="shoppingMall">
    <div class="above">
      <div class="left">
        <span class="title">商城概览</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
      </div>
    </div>

    <div class="card">
      <div class="card_item">
        <div class="item_above">
          <div class="item_left">
            <img src="../../assets/images/shop_stat4.png" />
            <span>库存成本(元)</span>
          </div>
          <div class="item_right">
            <div class="but">
              <span
                @click="
                  state.shopDataParams[3] = item.value;
                  state.shopDataType = '3';
                  deskShopStatics(state.shopDataType, state.shopDataParams[+state.shopDataType]);
                "
                style="cursor: pointer"
                :class="{ active: state.shopDataParams[3] == item.value }"
                :key="item.value"
                v-for="(item, index) in [
                  { label: '本周', value: '1' },
                  { label: '本月', value: '2' }
                ]"
                >{{ item.label }}</span
              >
            </div>
          </div>
        </div>
        <div class="item_middle">{{ formatCurrency(state.allShopData.firstCard.price || 0) }}</div>
        <div class="item_below">
          <div class="item_below_value">
            <span class="label">在库回收总价：</span>
            <span class="value" style="color: #67c23a">{{ formatCurrency(state.allShopData.firstCard.totalPrice || 0) }}</span>
          </div>
        </div>
      </div>
      <div class="card_item">
        <div class="item_above">
          <div class="item_left">
            <img src="../../assets/images/shop_stat5.png" />
            <span>库存周转率(%)</span>
          </div>
          <div class="item_right">
            <div class="but">
              <span
                @click="
                  state.shopDataParams[4] = item.value;
                  state.shopDataType = '4';
                  deskShopStatics(state.shopDataType, state.shopDataParams[+state.shopDataType]);
                "
                style="cursor: pointer"
                :class="{ active: state.shopDataParams[4] == item.value }"
                :key="item.value"
                v-for="item in [
                  { label: '本周', value: '1' },
                  { label: '本月', value: '2' }
                ]"
                >{{ item.label }}</span
              >
            </div>
          </div>
        </div>
        <div class="item_middle">{{ formatCurrency(state.allShopData.secondCard.turnoverRate || 0) }}</div>
        <div class="item_below">
          <div class="item_below_value">
            <span class="label">期初库存数：</span>
            <span class="value" style="color: var(--el-color-primary)">{{ formatCurrency(state.allShopData.secondCard.initial || 0) }}</span>
          </div>
          <div class="item_below_value">
            <span class="label">当前库存数：</span>
            <span class="value" style="color: var(--el-color-primary)">{{ formatCurrency(state.allShopData.secondCard.ending || 0) }}</span>
          </div>
        </div>
      </div>
      <div class="card_item">
        <div class="item_above" style="padding: 12px">
          <div class="item_left">
            <img src="../../assets/images/shop_stat1.png" />
            <span>今日毛利(元)</span>
          </div>
          <div class="item_right">
            <el-select
              @change="
                state.shopDataType = '1';
                deskShopStatics(state.shopDataType, state.shopDataParams[+state.shopDataType]);
              "
              v-model="state.shopDataParams[1]"
              placeholder="全部游戏"
              size="small"
              style="width: 120px; min-height: 20px"
            >
              <el-option :label="item.title" :value="item.id" :key="item.id" v-for="item in state.gameList" />
            </el-select>
          </div>
        </div>
        <div class="item_middle">{{ formatCurrency(state.allShopData.thirdCard.maoli || 0) }}</div>
        <div class="item_below">
          <div class="item_below_value">
            <span class="label">成交总价：</span>
            <span class="value" style="color: #f44a29">{{ formatCurrency(state.allShopData.thirdCard.maoliTotal || 0) }}</span>
          </div>
        </div>
      </div>
      <div class="card_item">
        <div class="item_above" style="padding: 12px">
          <div class="item_left">
            <img src="../../assets/images/shop_stat3.png" />
            <span>今日代售成交(元)</span>
          </div>
          <div class="item_right">
            <el-select
              @change="
                state.shopDataType = '2';
                deskShopStatics(state.shopDataType, state.shopDataParams[+state.shopDataType]);
              "
              v-model="state.shopDataParams[2]"
              placeholder="全部游戏"
              size="small"
              style="width: 120px; min-height: 20px"
            >
              <el-option :label="item.title" :value="item.id" :key="item.id" v-for="item in state.gameList" />
            </el-select>
          </div>
        </div>
        <div class="item_middle">{{ formatCurrency(state.allShopData.lastCard.price || 0) }}</div>
        <div class="item_below">
          <div class="item_below_value">
            <span class="label">代售总成交：</span>
            <span class="value" style="color: #e6a23c">{{ formatCurrency(state.allShopData.lastCard.totalPrice || 0) }}</span>
          </div>
          <div class="item_below_value">
            <span class="label">总利润：</span>
            <span class="value" style="color: #e6a23c">{{ formatCurrency(state.allShopData.lastCard.allProfit || 0) }}</span>
          </div>
        </div>
      </div>
    </div>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="12">
        <accountInventory :allData="state.linePieData" />
      </el-col>
      <el-col :span="12">
        <gameShop :allData="state.linePieData" />
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="12">
        <div class="stock_card">
          <div class="stock_top">
            <div class="stock_top_item">
              <img src="../../assets/images/shop_stat6.png" />
              <span>库存预警（个）</span>
            </div>
            <div class="stock_top_item">
              <div class="but">
                <span
                  @click="
                    state.stockParams = item.value;
                    getDeskstockAlarmdata();
                  "
                  style="cursor: pointer"
                  :class="{ active: state.stockParams == item.value }"
                  :key="item.value"
                  v-for="item in [
                    { label: '本周', value: 'week' },
                    { label: '本月', value: 'month' }
                  ]"
                  >{{ item.label }}</span
                >
              </div>
            </div>
          </div>
          <div class="stock_button">
            <div class="stock_button_count">
              <div class="number">{{ formatCurrency(state.tableDataWarn.totalCount || 0) }}</div>
              <div class="all">
                <span class="label">全部库存：</span>
                <span class="value" style="color: var(--el-color-primary)">{{ formatCurrency(state.tableDataWarn.allCount || 0) }}</span>
              </div>
            </div>
            <div class="stock_button_charts">
              <div style="width: 100%; height: 100%" ref="stockChartRef"></div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="stock_card">
          <div class="stock_top">
            <div class="stock_top_item">
              <img src="../../assets/images/shop_stat7.png" />
              <span>本月回收统计(元/个)</span>
            </div>
            <div class="stock_top_item">
              <el-select @change="getRecoveryData" v-model="state.gameParam" placeholder="全部游戏" size="small" style="width: 120px">
                <el-option :label="item.title" :value="item.id" :key="item.id" v-for="item in state.gameList" />
              </el-select>
            </div>
          </div>
          <div class="stock_button_num">
            <div class="stock_button_num_item">
              <div class="number">{{ state.recoverData.totalAmounts ? formatCurrency(state.recoverData.totalAmounts) : 0 }}</div>
              <div class="all">
                <span class="label">在库回收总价：</span>
                <span class="value" style="color: var(--el-color-primary)">{{ state.recoverData.allAmounts ? formatCurrency(state.recoverData.allAmounts) : 0 }}</span>
              </div>
            </div>
            <div class="stock_button_num_item">
              <div class="number">{{ state.recoverData.totalCounts ? formatCurrency(state.recoverData.totalCounts) : 0 }}</div>
              <div class="all">
                <span class="label">在库回收总量：</span>
                <span class="value" style="color: var(--el-color-primary)">{{ state.recoverData.allCounts ? formatCurrency(state.recoverData.allCounts) : 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang='ts' setup>
import commonData from "./index.ts";
import { ref, reactive, onMounted, watch, nextTick } from "vue";
import { formatDate, formatCurrency } from "@/utils/method";
import accountInventory from "./accountInventory.vue";
import gameShop from "./gameShop.vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService.js";

const state = reactive({
  shopDataParams: ["", "", "", "1", "1"],
  shopDataType: "1",
  gameList: <any>[],
  linePieData: {},
  allShopData: {
    firstCard: {
      price: 0,
      totalPrice: 0
    },
    secondCard: {
      turnoverRate: 0,
      initial: 0,
      ending: 0
    },
    thirdCard: {
      maoli: 0,
      maoliTotal: 0
    },
    lastCard: {
      price: 0,
      totalPrice: 0,
      allProfit: 0
    }
  },
  stockParams: "week",
  recoverData: {
    totalCounts: 0, //本月总价
    allCounts: 0, // 总价
    totalAmounts: 0, //本月总量
    allAmounts: 0 //总量
  },
  gameParam: "",
  tableDataWarn: {
    allCount: 0,
    totalCount: 0,
    xdata: <any>[],
    ydata: <any>[]
  }
});
const stockChartRef = ref();
const charts = ref(<any>[]);

const stockChart = () => {
  const userGrowthChart = echarts.init(stockChartRef.value);
  const option = {
    legend: {
      width: 20,
      itemWidth: 10,
      itemHeight: 10,
      icon: "circle",
      top: "0%",
      right: "0%",
      textStyle: {
        fontSize: 12,
        lineHeight: 10
      },
      zlevel: 999,
      data: ["正常", "滞销", "不足"]
    },
    tooltip: {},
    grid: {
      left: "0%",
      right: "15%",
      bottom: "0%",
      top: "0%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      axisLabel: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      data: state.tableDataWarn.xdata
    },
    yAxis: {
      axisLabel: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    series: [
      {
        name: "正常",
        type: "bar",
        barWidth: "30%",
        stack: "Total",
        itemStyle: {
          color: "#4165D7"
        },
        data: state.tableDataWarn.ydata[0]
      },
      {
        name: "滞销",
        type: "bar",
        barWidth: "30%",
        stack: "Total",
        itemStyle: {
          color: "#F56C6C"
        },
        data: state.tableDataWarn.ydata[1]
      },
      {
        name: "不足",
        type: "bar",
        barWidth: "30%",
        stack: "Total",
        itemStyle: {
          color: "#67C23A"
        },
        data: state.tableDataWarn.ydata[2]
      }
    ]
  };
  userGrowthChart.setOption(option);
  charts.value.push(userGrowthChart);
};
// 商城概览top4
const deskShopStatics = (type: any, params: any) => {
  if (type == 1) {
    baseService
      .post("/shop/shop/report", {
        type: "2",
        accountSource: "ORIGINAL_OWNER",
        date: formatDate(new Date(), "YYYY-MM-DD"),
        gameId: params
      })
      .then((res) => {
        state.allShopData.thirdCard = res.data;
        console.log(state.allShopData.thirdCard)
      });
  } else if (type == 2) {
    baseService
      .post("/shop/shop/report", {
        accountSource: "PLATFORM_CONSIGNMENT",
        date: formatDate(new Date(), "YYYY-MM-DD"),
        gameId: params,
        type: "1"
      })
      .then((res) => {
        state.allShopData.lastCard = res.data;
        baseService
        .post("/shop/shop/report", {
          accountSource: "PLATFORM_CONSIGNMENT",
          date: formatDate(new Date(), "YYYY-MM-DD"),
          gameId: params,
          type: "2"
        })
        .then((res) => {
          state.allShopData.lastCard.allProfit = res.data.totalPrice;
        });
      });
    
  } else {
    baseService
      .post("/shop/shop/report", {
        type,
        dateType: params
      })
      .then((res) => {
        type == 3 ? (state.allShopData.firstCard = res.data) : (state.allShopData.secondCard = res.data);
      });
  }
};
const getDeskstockAlarmdata = async () => {
  // 库存预警
  let res = await baseService.get("/console/stockAlarm", { weekOrMonth: state.stockParams });
  state.tableDataWarn = res.data || {};
  state.tableDataWarn.xdata = !state.tableDataWarn.xdata ? commonData.currentWeekDate() : state.tableDataWarn.xdata;
  let arr = <any>[];
  state.tableDataWarn?.ydata.forEach((ele: any) => {
    if (ele.name == "正常") {
      arr[0] = ele.data;
    }
    if (ele.name == "滞销") {
      arr[1] = ele.data;
    }
    if (ele.name == "不足") {
      arr[2] = ele.data;
    }
  });
  state.tableDataWarn.ydata = arr;
  stockChart();
};
// 本月回收统计
const getRecoveryData = async () => {
  let res = await baseService.get("/console/purchaseData", { gameId: state.gameParam });
  state.recoverData = res.data;
};
onMounted(async () => {
  state.gameList = await commonData.getGameList();
  state.linePieData = await commonData.getDeskShopdata();
  for (let index = 1; index < 5; index++) {
    deskShopStatics(index, state.shopDataParams[index]);
  }
  getRecoveryData();

  // 库存预警
  let res = await baseService.get("/console/stockAlarm", { weekOrMonth: state.stockParams });
  state.tableDataWarn = res.data || {};
  state.tableDataWarn.xdata = !state.tableDataWarn.xdata ? commonData.currentWeekDate() : state.tableDataWarn.xdata;
  let arr = <any>[];
  state.tableDataWarn?.ydata.forEach((ele: any) => {
    if (ele.name == "正常") {
      arr[0] = ele.data;
    }
    if (ele.name == "滞销") {
      arr[1] = ele.data;
    }
    if (ele.name == "不足") {
      arr[2] = ele.data;
    }
  });
  state.tableDataWarn.ydata = arr;
  stockChart();
});
</script>

<style lang='less' scoped>
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.shoppingMall {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}
.card {
  display: flex;
  align-items: center;
  margin-top: 12px;
  .card_item + .card_item {
    margin-left: 12px;
  }
  .card_item {
    flex: 1;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ebeef5;
    .item_above {
      padding: 16px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item_left {
        display: flex;
        align-items: center;
        img {
          width: 16px;
          height: 16px;
          margin-right: 2px;
        }
        span {
          font-weight: 400;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .item_right {
        .but {
          display: flex;
          span {
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 12px;
          }
          .active {
            color: var(--el-color-primary);
          }
        }
      }
    }
    .item_middle {
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding: 4px 16px;
    }
    .item_below {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 16px 12px 16px;
      .item_below_value {
        display: flex;
        align-items: center;
      }
    }
  }
}
.label {
  font-weight: 400;
  font-size: 12px;
  color: #606266;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.value {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.stock_card {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ebeef5;
  .stock_top {
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .stock_top_item {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
      span {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .but {
        display: flex;
        span {
          font-weight: 400;
          font-size: 14px;
          color: #909399;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-left: 12px;
        }
        .active {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .stock_button {
    padding: 4px 16px 7px 16px;
    display: flex;
    align-items: center;
    .stock_button_count {
      // border: 1px solid red;
      width: 50%;
      .number {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .all {
        margin-top: 8px;
      }
    }
    .stock_button_charts {
      width: 50%;
      height: 66px;
      // border: 1px solid red;
    }
  }
  .stock_button_num {
    padding: 4px 16px 12px 16px;
    display: flex;
    align-items: center;
    .stock_button_num_item {
      flex: 1;
      .number {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .all {
        margin-top: 8px;
      }
    }
  }
}
</style>