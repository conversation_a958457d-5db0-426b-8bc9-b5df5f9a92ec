<template>
    <el-drawer
        v-model="visible"
        :title="dataForm.id ? '修改文章' : '发布文章'"
        :close-on-click-moformuladal="false"
        :close-on-press-escape="false"
        size="944px"
        class="ny-drawer article-add-or-update"
    >
        <el-card class="article-add-or-update-form" v-loading="dataLoading">
            <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
                <el-form-item label="文章标题" prop="title">
                    <el-input type="textarea" :rows="3" maxlength="255" show-word-limit v-model="dataForm.title" placeholder="请输入商品标题"></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input class="input-w-360" v-model="dataForm.sort" type="number" placeholder="请输入排序"></el-input>
                </el-form-item>
                <el-form-item label="文章分类" prop="articleType">
                    <el-select 
                        filterable
                        :filter-method="dataFilter"
                        class="input-w-360" 
                        v-model="dataForm.articleType"
                        placeholder="请选择文章分类"
                    >       
                        <el-option v-for="item in articleTypeList" :key="item.id" :label="item.dictLabel" :value="item.id">
                            <div class="flx-justify-between">
                                <div class="flx-1 sle">{{ item.dictLabel }}</div>
                                <el-text type="danger" class="pointer" @click.stop="deleteArticleType(item.id)">删除</el-text>
                            </div>
                            
                        </el-option>
                        <template #footer>
                            <el-button class="w-100" type="primary" text size="small" @click="onAddOption">
                                新建分类
                            </el-button>
                        </template>
                    </el-select>
                </el-form-item>
                <el-form-item label="封面图" prop="image">
                    <ny-upload :limit="1" v-model:imageUrl="dataForm.image" accept="image/*" ></ny-upload>
                </el-form-item>
                <el-form-item label="文章内容" prop="article">
                    <wangEditor v-model="dataForm.article"></wangEditor>
                </el-form-item>
            </el-form>
        </el-card>

        <template #footer>
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" plian @click="submitForm(1)">保存并发布</el-button>
            <el-button v-if="!dataForm.status || dataForm.status == 0" :loading="btnLoading" type="primary" @click="submitForm(0)">保存</el-button>
        </template>
    </el-drawer>
</template>  

<script lang="ts" setup>
    import { IObject } from "@/types/interface";
    import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
    import { getDictDataList } from "@/utils/utils";
    import { useAppStore } from "@/store";
    import { ElMessage } from "element-plus";
    import WangEditor from "@/components/wang-editor/index.vue";
    import baseService from "@/service/baseService";
    import { useHandleData } from "@/hooks/useHandleData";

    const store = useAppStore();
    const emits = defineEmits(['refresh']);

    const dataForm = ref({} as IObject);
    const visible = ref(false);

    // 文章分类
    const articleTypeList = ref(<any>[]);
    // const articleTypeList = computed(() => {
    //     return getDictDataList(store.state.dicts, 'article_type');
    // });

    watch(() => store.state.dicts, (newVal) => {
        articleTypeList.value = getDictDataList(newVal, 'article_type');
    },{
        deep: true,
        immediate: true
    })

    const rules = {
        title: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        articleType: [{ required: true, message: '请选择文章分类', trigger: 'blur' }],
        article: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
        image:  [{ required: true, message: '请上传封面图', trigger: 'change' }],
    }

    const init = (id?: number) => {
        visible.value = true;
        if(id){
            getDetails(id);
        }
    }
    
    const dataLoading = ref(false);
    const getDetails = (id: number) => {
        dataLoading.value = true;
        baseService.get('/basics/article/' + id).then(res => {
            if (res.code === 0) {
                dataForm.value = res.data;
            }
        }).finally(() => {
            dataLoading.value = false;
        });
    }
    
    const dataFormRef = ref();
    const btnLoading = ref(false);
    const submitForm = (status: number) => {
        dataFormRef.value.validate((valid: boolean) => {
            if (valid) {
                dataForm.value.status = status;
                dataForm.value.dictLabel = articleTypeList.value.find(item => item.id === dataForm.value.articleType).dictLabel
                btnLoading.value = true;
                baseService[dataForm.value.id ? 'put' : 'post']('/basics/article', dataForm.value).then(res => {
                    if (res.code === 0) {
                        visible.value = false;
                        ElMessage.success(res.msg);
                        emits('refresh');
                    }
                }).finally(() => {
                    btnLoading.value = false;
                })
            }
        })
    }    

    // 删除文章分类
    const deleteArticleType = async (id: number) => {
        await useHandleData('/basics/article/delType', {id}, '确定删除文章分类', 'get');
        if(dataForm.value.articleType == id){
            dataForm.value.articleType = "";
        }
        let res = await baseService.get("/sys/dict/type/all");
        store.updateState({
            dicts: res.data || []
        });
    }
    
    const filterValue = ref('');
    const dataFilter = (val: string) => {
        filterValue.value = val;
        let data = getDictDataList(store.state.dicts, 'article_type');
        if (val) {
            articleTypeList.value = data.filter((item) => {
                if (item.dictLabel.includes(val) || item.dictLabel.toUpperCase().includes(val.toUpperCase())) {
                    return true
                }
            })
        } else {
            articleTypeList.value = data;
        }

    }

    // 新增文章分类
    const onAddOption = async () => {
        await baseService.get('/basics/article/saveType', { type: filterValue.value })
        
        let dict = await baseService.get("/sys/dict/type/all");

        store.updateState({
            dicts: dict.data || []
        });
        
        nextTick(() => {
            dataForm.value.articleType = articleTypeList.value.find((item: any) => item.dictLabel == filterValue.value).id;
        })
    }

    defineExpose({
        init
    })

</script>

<style lang="scss">
.article-add-or-update{
    .article-add-or-update-form{
        min-height: 100%;

        .input-w-360{
            width: 360px;
        }
    }
}
</style>