<template>
  <el-dialog v-model="visible" :title="`设置商品信息字段`" :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="tops">
      <div class="introduction">
        <p>这里设置类型的附加字段，比如出售王者荣耀账号，可以设置账号的信息</p>
        <p>示例1：名称：账号，密码，类型：文本</p>
      </div>
      <el-button class="btns" type="primary" @click="addDatas" v-blur>添加字段</el-button>
    </div>
    <el-scrollbar max-height="400px" class="dynamicForm">
      <el-form ref="dynamicFormRef" :model="dynamicForm" label-width="80px" class="dynamicFormCon">
        <div v-for="(item, index) in dynamicForm.list" :key="index + '_form'" class="dynamicFormItem">
          <div class="formLeft">
            <div class="formIndex">字段{{ index + 1 }}</div>
            <div class="formCon">
              <el-form-item label="名称" :prop="`list[${index}].name`" :rules="{
    required: true,
    message: '请输入名称',
    trigger: 'blur'
  }">
                <el-input v-model="item.name" placeholder="请输入" clearable />
              </el-form-item>
              <div style="display: flex; align-items: flex-start; ">
                <el-form-item label="表单" :prop="`list[${index}].fieldType`" :rules="{
    required: true,
    message: '请选择表单',
    trigger: 'blur'
  }" style="flex: 1;">
                  <ny-radio v-model="item.fieldType" :datas="state.radioData"
                    @change="item.rquired = ''; item.correspondingAttribute = ''; item.detailsNames = ''; item.fillType = ''; item.detailsVos = [{name: ''}];"></ny-radio>
                </el-form-item>
                <el-form-item label="必填项" :prop="`list[${index}].rquired`">
                  <el-switch v-model="item.rquired" active-value="1" inactive-value="0" active-color="#409eff"
                    inactive-color="#ecf5ff">
                  </el-switch>
                </el-form-item>
              </div>
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <el-form-item label="字段类型" :prop="`list[${index}].correspondingAttribute`" style="width: 54%">
                  <el-select v-model="item.correspondingAttribute" clearable placeholder="字段类型">
                    <el-option label="营地号" :value="5" />
                    <el-option label="实名情况" :value="6" />
                    <el-option label="包赔" :value="7" />
                    <el-option label="是否顶级账号" :value="8" />
                  </el-select>
                </el-form-item>
                <el-form-item label="填写类型" v-if="item.fieldType && item.fieldType == '1'"
                  :prop="`list[${index}].fillType`" style="width: 37%">
                  <el-select v-model="item.fillType" clearable placeholder="字段类型">
                    <el-option label="字符" :value="1" />
                    <el-option label="数字" :value="2" />
                  </el-select>
                </el-form-item>
              </div>
              <div v-if="item.fieldType && item.fieldType != 1">
                <div v-for="(item2, index2) in item.detailsVos" :key="index2 + '_formSon'" class="dynamicFormSon">
                  <el-form-item :label="index2 == 0 ? '选项' : ''" :prop="`list[${index}].detailsVos[${index2}].name`"
                    :rules="[
    { required: true, message: '请输入选项', trigger: 'blur' },
    { min: 1, message: '至少输入1个字符', trigger: 'blur' }
  ]">
                    <div class="itemSon">
                      <el-input v-model="item2.name" placeholder="请输入" clearable />
                      <el-tag v-if="item.detailsVos.length > 1" @click="removeSon(item, item2)" class="dels">-</el-tag>
                      <el-tag v-if="index2 == item.detailsVos.length - 1" @click="addSon(item)" class="adds">+</el-tag>
                    </div>
                  </el-form-item>
                </div>
              </div>
              <el-form-item label="排序" :prop="`list[${index}].sort`" :rules="{
    required: true,
    message: '请输入排序',
    trigger: 'blur'
  }">
                <el-input v-model="item.sort" placeholder="请输入" clearable type="number" />
              </el-form-item>
            </div>
          </div>
          <div class="formRight" @click="removeItem(item)">
            <img :src="delBtn">
            <span>删除</span>
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dynamicFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { uuid } from "@/components/ny-flowable/package/utils";
import delBtn from "@/assets/images/delBtn.png";
import useView from "@/hooks/useView";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);

const view = reactive({
  radioData: [{ label: '文本', value: '1' }, { label: '单选', value: '2' }, { label: '多选', value: '3' }],
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const gameId: any = ref('');
const gameCode: any = ref('');
const init = (id?: number, code?: any) => {
  visible.value = true;
  gameId.value = id;
  gameCode.value = code;
  // dataForm.id = "";

  // // 重置表单数据
  // if (dataFormRef.value) {
  //   dataFormRef.value.resetFields();
  // }

  if (id) {
    getInfo(id);
  }


};

const dynamicFormRef = ref();
const dynamicForm: any = reactive({
  list: [
    {
      name: '',
      fieldType: '',
      rquired: '',
      detailsVos: [{ name: '' }],
      detailsNames: '',
      correspondingAttribute: '',
      sort: '',
      type: 1,
      gameCode: gameCode.value
    }
  ]
});
// 新增一项
const addDatas = () => {
  dynamicForm.list.push({
    name: '',
    fieldType: '',
    rquired: '',
    detailsVos: [{ name: '' }],
    detailsNames: '',
    correspondingAttribute: '',
    sort: '',
    type: 1,
    gameCode: gameCode.value
  });
}
// 删除一项
const removeItem = (item: any) => {
  var index = dynamicForm.list.indexOf(item)
  if (index !== -1) {
    dynamicForm.list.splice(index, 1)
  }
}

// 选项字段
const addSon = (item: any) => {
  var index = dynamicForm.list.indexOf(item)
  if (index !== -1) {
    dynamicForm.list[index].detailsVos.push({ name: '' });
  }
}
const removeSon = (item: any, item2: any) => {
  var index = dynamicForm.list.indexOf(item)
  if (index !== -1) {
    var index2 = dynamicForm.list[index].detailsVos.indexOf(item2)
    if (index2 !== -1) {
      dynamicForm.list[index].detailsVos.splice(index2, 1)
    }
  }
}

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbgame/" + id + '/listShopAttribute').then((res) => {
    if (res.data.length > 0) {
      Object.assign(dynamicForm.list, res.data);
    } else {
      dynamicForm.list = [{
        name: '',
        fieldType: '',
        rquired: '',
        detailsVos: [{ name: '' }],
        detailsNames: '',
        correspondingAttribute: '',
        sort: '',
        type: 1,
        gameCode: gameCode.value
      }];
    }
  }).catch((err) => { });
  nextTick(() => {
    dynamicFormRef.value.clearValidate();
  })
};

// 表单提交
const dynamicFormSubmitHandle = () => {
  let arrs: any = JSON.parse(JSON.stringify(dynamicForm.list));
  let names: any = [];
  arrs.forEach((element: any) => {
    element.gameCode = element.gameCode === '' ? gameCode.value : element.gameCode;
    if (element.detailsVos.length > 0) {
      element.detailsVos.forEach((element2: any) => {
        if (element2.name != '') {
          names.push(element2.name)
        }
      });
      element.detailsNames = names.join(',');
    }
  });
  dynamicFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.post("/shop/tbgame/" + gameId.value + '/editShopAttribute', arrs).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.tops {
  width: 100%;
  padding: 0 4%;

  .introduction {
    width: 100%;
    margin: 21px auto 0;
    padding: 5px 21px;
    background-color: #EFF5FF;
    border-radius: 14px;
    box-sizing: border-box;

    p {
      width: 100%;
      margin: 0;
      padding: 0;
      font-size: 14px;
      color: #000;
      line-height: 34px;
      word-break: break-all;
      white-space: normal;

      span {
        color: red;
      }
    }
  }

  .btns {
    width: 100%;
    height: 30px;
    letter-spacing: 1px;
    margin: 21px auto;
    background-color: #4165D7;
    border-radius: 8px;
  }
}

.dynamicForm {
  width: 100%;
  padding: 0 4%;
}

.dynamicFormCon {
  padding: 26px 28px;
  background-color: #F0F2F5;
  border-radius: 25px;
  box-sizing: border-box;
}

.dynamicFormItem {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.formLeft {
  flex: 1;
  margin-right: 28px;
  padding: 25px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 13px;
  box-sizing: border-box;
}

.formIndex {
  width: 115px;
  height: 100%;
  font-size: 17px;
  color: #000;
  text-align: center;
}

.formCon {
  flex: 1;
  padding: 0 43px 0 28px;
  box-sizing: border-box;
  border-left: 1px solid #e9e9e9;
}

.formRight {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 28px;
    height: 28px;
  }

  span {
    font-size: 14px;
    color: #999;
    line-height: 30px;
  }
}

.itemSon {
  display: flex;
  align-items: center;

  .el-input {
    width: 100px;
  }

  .el-tag {
    width: 21px;
    height: 21px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 21px;
    line-height: 21px;
    border-radius: 12px;
    border: none;
    cursor: pointer;

    &.dels {
      color: #FF6161;
      line-height: 16px;
      padding-bottom: 5px;
      background-color: #FFF0F0;
    }

    &.adds {
      color: #5FB3FF;
      background-color: #E9F5FF;
    }
  }
}
</style>