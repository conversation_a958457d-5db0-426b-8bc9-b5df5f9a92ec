<template>
  <el-dialog v-model="visible" title="倍率配置" :close-on-click-modal="false" :close-on-press-escape="false" width="1000">
    <el-table height="450" :data="gameList" border v-loading="dataListLoading">
      <el-table-column align="center" prop="gameTitle" label="游戏名称" min-width="120" />
      <el-table-column align="center" label="推送加价倍率" min-width="100">
        <template #header>
          推送加价倍率
          <el-tooltip effect="dark" content="倍率超过100为加价,低于100为打折" placement="top-start">
            <el-icon color="#909399" size="16"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <el-input type="number" :controls="false" :min="0" v-model="row.pushExtraRate" placeholder="请输入" style="width: 100%" @change="inputChange(row)">
            <template #append>%</template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否推送" width="100">
        <template #default="{ row }">
          <!-- 是否开通 0 否 1是 默认0 -->
          <el-switch v-model="row.state" active-value="1" inactive-value="0" inline-prompt active-text="是" inactive-text="否" @change="(e: any) => {inputChange(row)}" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="接收加价倍率" min-width="100">
        <template #header>
          接收加价倍率
          <el-tooltip effect="dark" content="倍率超过100为加价,低于100为打折" placement="top-start">
            <el-icon color="#909399" size="16"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <el-input type="number" :controls="false" :min="0" v-model="row.receptionExtraRate" placeholder="请输入" style="width: 100%" @change="inputChange(row)">
            <template #append>%</template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否在商城显示" width="140">
        <template #default="{ row }">
          <!-- 是否开通 0 否 1是 默认0 -->
          <el-switch v-model="row.isShow" :active-value="1" :inactive-value="0" inline-prompt active-text="是" inactive-text="否" @change="(e: any) => {inputChange(row)}" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="过期时间" min-width="100">
        <template #default="{ row }">
          <el-input v-model="row.expiredDays" placeholder="请输入" style="width: 100%" @change="inputChange(row)">
            <template #append>天</template>
          </el-input>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>

    <div class="flx-justify-end pt-20">
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="submitConfig">保存</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, nextTick } from "vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage } from "element-plus";

const visible = ref(false);
const currentId = ref(null);

const init = (data: any) => {
  visible.value = true;
  currentId.value = data.id;
  getGamesList();
};

// 游戏列表
const gameList = ref([]);
const dataListLoading = ref(false);
const getGamesList = async () => {
  dataListLoading.value = true;
  try {
    let res = await baseService.get("/partner/partnergame/list/" + currentId.value);
    dataListLoading.value = false;
    if (res.code == 0) {
      gameList.value = res.data;
    }
  } catch (error) {
    dataListLoading.value = false;
  }
};

const submitConfig = () => {
  ElMessage.success("保存成功");
  visible.value = false;
};

// 修改配置
const inputChange = async (row: any) => {
  await nextTick();
  try {
    await baseService.put("/partner/partnergame/update", row);
    ElMessage.success("操作成功");
  } catch (error) {
    getGamesList();
  }
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.el-dialog__body {
  padding: 30px;
  height: 600px;
}
</style>
