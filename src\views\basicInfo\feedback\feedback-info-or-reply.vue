<template>
    <el-dialog 
        v-model="visible" 
        :title="currentType == 'view' ? $t('view') : $t('reply')" 
        :close-on-click-modal="false" 
        :close-on-press-escape="false"
        @close="closeDialog"
    >

        <el-form v-if="currentType == 'view'" ref="formRef" label-width="100px" label-suffix="：">
            <el-form-item label="ID">
                {{ dataForm.id }}
            </el-form-item>
            <el-form-item label="类型">
                <span v-if="dataForm.type == 0">投诉</span>
                <span v-else-if="dataForm.type == 1">建议</span>
                <span v-else></span>
            </el-form-item>
            <el-form-item label="用户">
                {{ dataForm.userId }}
            </el-form-item>
            <el-form-item label="图片">
                <el-image v-if="dataForm.problemPic" class="goods-imgs"
                    :src="dataForm.problemPic"
                    :preview-src-list="[dataForm.problemPic]" />
            </el-form-item>
            <el-form-item label="提交时间" :prop="dataForm.createTime">
                {{ dataForm.createTime }}
            </el-form-item>
            <el-form-item label="问题" :prop="dataForm.content">
                {{ dataForm.content }}
            </el-form-item>
            <el-form-item label="回复状态" :prop="dataForm.status">
                <span v-if="dataForm.status == 0">未回复</span>
                <span v-else-if="dataForm.status == 1">已回复</span>
                <span v-else></span>
            </el-form-item>
            <el-form-item label="回复" :prop="dataForm.reply">
                {{ dataForm.reply }}
            </el-form-item>
            <el-form-item label="回复时间" :prop="dataForm.replyTime">
                {{ dataForm.replyTime }}
            </el-form-item>
        </el-form>

        <el-form v-else ref="formRef" :model="dataForm" label-width="100px" label-suffix="：">
            <el-form-item label="类型">
                <span v-if="dataForm.type == 0">投诉</span>
                <span v-else-if="dataForm.type == 1">建议</span>
                <span v-else></span>
            </el-form-item>
            <el-form-item label="问题">
                {{ dataForm.content }}
            </el-form-item>
            <el-form-item label="回复" :prop="dataForm.reply">
                <el-input v-model="dataForm.reply" :rows="3" type="textarea"
                    placeholder="请输入回复内容" />
            </el-form-item>
        </el-form>
      
        <template v-if="currentType == 'reply'" v-slot:footer>
            <el-button :loading="replyLoading" @click="closeDialog">{{ $t("cancel") }}</el-button>
            <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref, defineEmits,defineExpose } from 'vue';
    import baseService from '@/service/baseService';
    import { ElMessage } from 'element-plus';
    
    const emits = defineEmits(['close', 'refresh']);
    const visible = ref(false);
    
    const dataForm = ref(<any>{});

    // 当前显示类型
    const currentType = ref('view');
    // 详情id
    const detailId = ref('');
    
    const init = (id: string, type: string) => {
        visible.value = true;
        detailId.value = id;
        currentType.value = type;
        getInfo(id);
    }

    // 获取详情
    const getInfo = async (id: string) => {
        let res = await baseService.get('/api/feedback/info', { id: id });
        dataForm.value = res.data;
    }

    // 提交回复
    const replyLoading = ref(false);
    const dataFormSubmitHandle = async () => {
        if(!dataForm.value.reply){
            return ElMessage.error('请输入回复内容');
        }
        replyLoading.value = true;
        let res = await baseService.post('/api/feedback/reply', { id: detailId.value, reply: dataForm.value.reply });
        replyLoading.value = false;
        if(res.code == 0){
            ElMessage.success('回复成功');
            emits('refresh');
            closeDialog();
        }
    }

    // 关闭
    const closeDialog = () => {
        visible.value = false;
        emits('close');
    }

    defineExpose({
        init
    })
    
</script>