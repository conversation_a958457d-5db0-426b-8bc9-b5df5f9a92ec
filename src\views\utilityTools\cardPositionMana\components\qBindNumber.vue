<template>
  <div style="width: 100%">
    <div class="title">Q绑查数量</div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <div style="display: flex; gap: 8px">
          <el-button type="primary" @click="multiHandle(1)">批量重启</el-button>
          <el-button type="warning" @click="multiHandle(2)">批量禁用</el-button>
          <el-button color="#409EFF" style="color: #fff" @click="multiHandle(3)">批量执行</el-button>
          <el-button type="info" @click="exportHandle()">导出</el-button>
        </div>
      </template>
      <template #header-right>
        <div style="display: flex; gap: 8px">
          <el-input :prefix-icon="Search" style="width: 320px" v-model="state.dataForm.info" placeholder="请输入手机号/备注" clearable></el-input>
          <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </template>
      <template #status="{ row }">
        <el-button link type="danger" v-if="row.cardStatus == 0">禁用</el-button>
        <el-button link type="success" v-if="row.status == 1">正常</el-button>
      </template>
      <template #operate="{ row }">
        <!-- 获取列表数据后，需要处理下该字段的默认值 -->
        <el-input-number style="width: 90px" size="small" @blur="handleBind(row)" v-model="row.number" :step="1" />
      </template>
      <template #remark="{ row }">
        <template v-if="row.remark">{{ row.remark }}</template>
        <div v-else>
          <span>-</span>
          <el-button @click="editHandle(row)" link type="primary"
            ><el-icon><Edit /></el-icon
          ></el-button>
        </div>
      </template>
      <template #queryTime="{ row }">
        <span>{{ row.queryTime ? formatDate(row.queryTime, undefined) : "-" }}</span>
      </template>
      <template #updateDate="{ row }">
        <span>{{ row.updateDate ? formatDate(row.updateDate, undefined) : "-" }}</span>
      </template>
    </ny-table>
    <!-- 备注 -->
    <editRemark ref="editRemarkRef" @refreshDataList="state.getDataList()"></editRemark>
  </div>
</template>
          
<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { fileExport } from "@/utils/utils";
import editRemark from "./edit-remark.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileQBindingQuantity/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    info: "",
    start: "",
    end: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 140
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 100
  },
  {
    prop: "queryTime",
    label: "查绑时间",
    minWidth: 180
  },
  {
    prop: "queryQuantity",
    label: "查绑数量",
    minWidth: 90
  },

  {
    prop: "bindingQq",
    label: "绑定QQ号",
    minWidth: 140
  },
  {
    prop: "queryAfterQuantity",
    label: "查后绑定",
    minWidth: 100
  },
  {
    prop: "queryAfterUnbindQuantity",
    label: "查后解绑",
    minWidth: 100
  },
  {
    prop: "actualQuantity",
    label: "实绑",
    minWidth: 120
  },
  {
    prop: "executionEnvironment",
    label: "最近执行情况",
    minWidth: 110
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: 136
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: 180
  }
]);
const multiHandle = (index: any) => {
  if (!state.dataListSelections || state.dataListSelections.length == 0) {
    ElMessage.warning("请先勾选！");
    return;
  }
  if (index == 1) {
    // 启用
  } else if (index == 2) {
    // 禁用
  } else if (index == 3) {
    // 执行
  }
};
// 实绑数量更新
const handleBind = (row: any) => {
  //   baseService.put("", row).then((res) => {});
};
// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm.info = "";
  state.dataForm.start = "";
  state.dataForm.end = "";
  createDate.value = [];
  state.page = 1;
  state.getDataList();
};

// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/mobile/mobileQBindingQuantity/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `Q绑查询数量`);
  });
};

const editRemarkRef = ref();
const editHandle = (row: any) => {
  nextTick(() => {
    editRemarkRef.value.init(3, row.id);
  });
};
</script>
          
<style lang='less' scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
</style>