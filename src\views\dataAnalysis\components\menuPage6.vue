<template>
    <div>
        <div class="menu_title">回收排行榜</div>
        <div class="top">
            <el-radio-group v-model="dataForm.type" @change="queryChagne">
                <el-radio value="1">游戏销量</el-radio>
                <el-radio value="2">员工销量</el-radio>
                <!-- <el-radio value="3">渠道销量</el-radio> -->
            </el-radio-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId"  :list="recyclingChanneList" labelKey="channelName"
                    valueKey="channelId" placeholder="销售渠道" clearable></NyDropdownMenu> -->
                <el-date-picker v-model="SalesTime" type="daterange" start-placeholder="开始时间"
                    end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    style="width: 220px;margin-left: 12px;" @change="salesTimeChange"/>
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <div class="card_list" style="margin-top: 12px;">
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>今日回收订单</span>
                        <el-tooltip effect="dark" content="今日回收过的订单数量，不包含【已取消】的数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ recycleInfo.todaySalesOrder }}</div>
                <div class="item_footer">
                    <div class="box">总金额(元)：{{ recycleInfo.todayTotalAmount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>本月回收订单</span>
                        <el-tooltip effect="dark" content="本月回收过的订单数量，不包含【已取消】的数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ recycleInfo.monthSalesOrder }}</div>
                <div class="item_footer">
                    <div class="box">总金额(元)：{{ recycleInfo.monthTotalAmount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>员工回收量占比</span>
                        <el-tooltip effect="dark" content="按员工维度，显示当前时间下（默认本月）回收量最高的前 6 个员工的回收量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="EmployeeRecyclingRef"></div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>游戏回收量占比</span>
                        <el-tooltip effect="dark" content="按游戏维度，显示当前时间下（默认本月）回收量最高的前 6 个游戏的回收量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="GameSalesRef"></div>
                </div>
            </div>
            <!-- <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(232,230,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>渠道回收量占比</span>
                        <el-tooltip effect="dark" content="按渠道维度，显示当前时间下（默认本月）回收量最高的前 6 个渠道的回收量占比" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="charts">
                    <div style="width: 100%;height: 100%;" ref="ChannelSalesVolumeRef"></div>
                </div>
            </div> -->
        </div>
        <RecyclingQuantityDetails ref="RecyclingQuantityDetailsRef"></RecyclingQuantityDetails>
        <div style="display: flex;align-items: center;gap: 12px;">
            <div style="flex: 1;">
                <RecyclingOrders ref="RecyclingOrdersRef"></RecyclingOrders>
            </div>
            <div style="flex: 1;">
                <RecyclingAmount ref="RecyclingAmountRef"></RecyclingAmount>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import * as echarts from "echarts";
import RecyclingOrders from "./charts/RecyclingOrders.vue";
import RecyclingAmount from "./charts/RecyclingAmount.vue";
import RecyclingQuantityDetails from "./charts/RecyclingQuantityDetails.vue";
import baseService from "@/service/baseService";

const SalesTime = ref();

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    startTime: "",
    endTime: "",
    type: "1"
});

const gameList = ref(); // 游戏列表
const recyclingChanneList = ref(); // 回收渠道列表

const RecyclingQuantityDetailsRef = ref();
const RecyclingOrdersRef = ref();
const RecyclingAmountRef = ref();

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) => {
    baseService.get("/dataAnalysis/channelSearchList", { channelType }).then((res) => {
        recyclingChanneList.value = res.data;
    });
};

const charts = ref(<any>[]);
const EmployeeRecyclingRef = ref(null);
// 员工回收量占比
const EmployeeSalesChart = () => {
    const userGrowthChart = echarts.init(EmployeeRecyclingRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 85,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: recycleInfo.value.employeeSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const GameSalesRef = ref(null);
// 游戏回收量占比
const GameSalesChart = () => {
    const userGrowthChart = echarts.init(GameSalesRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 85,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 60,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: recycleInfo.value.gameSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const ChannelSalesVolumeRef = ref(null);
// 渠道回收量占比
const ChannelSalesVolumeChart = () => {
    const userGrowthChart = echarts.init(ChannelSalesVolumeRef.value);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            x: 'right',
            y: 'center',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                fontSize: 10,
                width: 45,
                overflow: 'truncate',
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '90%'],
                center: ['16%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: recycleInfo.value.channelSalesRanking
            }
        ]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const salesTimeChange = () =>{
    dataForm.value.startTime = SalesTime.value ? SalesTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = SalesTime.value ? SalesTime.value[1] + " 23:59:59" : "";
}

onMounted(() => {
    getGameList();
    getRecyclingChanneList(1);
    getPurchaseStatistics();
})

// 查询
const queryChagne = () =>{
    getPurchaseStatistics();
    RecyclingQuantityDetailsRef.value.init(dataForm.value);
    RecyclingOrdersRef.value.init(dataForm.value);
    RecyclingAmountRef.value.init(dataForm.value);
}

// 重置
const resettingChange = () =>{
    dataForm.value.gameId = "";
    dataForm.value.recyclingChannelId = "";
    dataForm.value.startTime = "";
    dataForm.value.endTime = "";
    SalesTime.value = [];
    getPurchaseStatistics();
    RecyclingQuantityDetailsRef.value.init(dataForm.value);
    RecyclingOrdersRef.value.init(dataForm.value);
    RecyclingAmountRef.value.init(dataForm.value);
}

const recycleInfo = ref({
    todaySalesOrder: 0,
    todayTotalAmount: 0,
    monthSalesOrder: 0,
    monthTotalAmount: 0,
    employeeSalesRanking: [],
    gameSalesRanking: [],
    channelSalesRanking: []
})
const getPurchaseStatistics = () => {
    baseService.post("/dataAnalysis/purchaseStatistics", dataForm.value).then(res => {
        Object.assign(recycleInfo.value, res.data);
        charts.value = [];
        EmployeeSalesChart();
        GameSalesChart();
        ChannelSalesVolumeChart();
    })
}

</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card_list {
    display: flex;
    align-items: center;
    gap: 12px;

    .card_list_item {
        flex: 1;
        padding: 16px 20px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;

        .item_header {
            font-weight: bold;
            font-size: 16px;
            color: #1C1C1C;
            line-height: 20px;

            display: flex;
            align-items: center;
            justify-content: space-between;

            .month {
                font-weight: 500;
                font-size: 14px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }

        .item_center {
            font-weight: bold;
            font-size: 24px;
            color: #1C1C1C;
            line-height: 36px;
            margin: 10px 0px;
        }

        .item_footer {
            display: flex;
            align-items: center;

            .box {
                flex: 1;
                font-weight: 400;
                font-size: 13px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }

        .charts {
            width: 100%;
            height: 76px;
            // border: 1px solid red;
        }
    }
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F77234;
                        border-color: #F77234;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #0FC6C2;
                        border-color: #0FC6C2;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
</style>
