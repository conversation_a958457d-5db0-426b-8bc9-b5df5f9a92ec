<template>
  <div class="TableXScrollSty channelPage">
    <ny-flod-tab
      :list="[
        { label: '收入流水', value: '1' },
        { label: '支出流水', value: '2' }
      ]"
      v-model="state.dataForm.billType"
      @change="state.getDataList()"
      value="value"
      label="label"
    ></ny-flod-tab>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <el-button type="info" @click="exportHandle">导出</el-button>
      </template>
      <template #header-right>
        <el-date-picker v-model="createDate" style="width: 240px !important" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
        <el-input v-model="state.dataForm.alipayOrderNo" placeholder="请输入订单号" style="width: 240px" clearable></el-input>
        <div style="width: 160px !important">
          <el-select v-model="state.dataForm.reconciliationStatus" clearable filterable placeholder="请选择对账状态">
            <el-option
              v-for="(item, index) in [
                { label: '未对账', value: '1' },
                { label: '已对账', value: '2' }
              ]"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
        <el-button @click="rechargeChange">{{ $t("resetting") }}</el-button>
      </template>

      <template #alipayOrderNo="{ row }">
        <div class="linkSty">
          <el-link :underline="false" v-copy="row.alipayOrderNo"
            >{{ row.alipayOrderNo }}<el-icon class="copyIcon"><DocumentCopy /></el-icon>
          </el-link>
        </div>
      </template>

      <template #transAmount="{ row }">
        <el-text :type="state.dataForm.billType == '1' ? 'danger' : 'success'">{{ state.dataForm.billType == "1" ? "+" : "-" }}{{ formatCurrency(row.transAmount) }}</el-text>
      </template>
      <template #transDt="{ row }">
        <span>{{ formatDate(row.transDt) }}</span>
      </template>
      <!-- 状态 -->
      <template #reconciliationStatus="{ row }">
        <el-tag v-if="row.reconciliationStatus == '2'" type="success">已对账</el-tag>
        <el-tag v-if="row.reconciliationStatus == '1'" type="danger">未对账</el-tag>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" text bg @click="handleRemark(row)">备注</el-button>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort"> 交易金额 </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>

    <!-- 订单备注 -->
    <el-dialog v-model="dialogVisible" title="订单备注" width="480" @close="cancelChange">
      <div class="cardDescriptions" style="padding: 0; padding-bottom: 80px">
        <el-descriptions :column="1" border class="descriptions" style="margin-bottom: 12px">
          <el-descriptions-item>
            <template #label>订单号</template>
            {{ dialogInputValue.alipayOrderNo }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>交易金额(元)</template>
            {{ dialogInputValue.transAmount }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>备注</span></template>
            <el-input v-model="dialogInputValue.remark" placeholder="请输入备注" type="textarea" :rows="5"></el-input>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button :loading="submitLoading" @click="cancelChange">取消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="handleSumbit()">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatCurrency, formatDate } from "@/utils/method";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "alipayOrderNo",
    label: "订单号",
    minWidth: "240"
  },
  {
    prop: "accountName",
    label: "账户名称",
    minWidth: "150"
  },
  {
    prop: "account",
    label: "账号",
    minWidth: "130"
  },
  {
    prop: "merchantOrderNo",
    label: "商家订单号",
    minWidth: "180"
  },
  {
    prop: "transAmount",
    label: "交易金额(元)",
    minWidth: "140"
  },
  {
    prop: "transDt",
    label: "交易时间",
    minWidth: "160"
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "130"
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    minWidth: "130"
  },
  {
    prop: "operation",
    label: "操作",
    minWidth: "100"
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/alipayrecord/page",
  getDataListIsPage: true,
  deleteURL: "/channel/channel",
  deleteIsBatch: true,
  dataForm: {
    billType: "1",
    reconciliationStatus: "",
    alipayOrderNo: "",
    startTime: "", // 开始时间
    endTime: "" // 结束时间
  }
});
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};
const state = reactive({ ...useView(view), ...toRefs(view) });

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.transAmount) if (item.transAmount) total = total + (item.transAmount || 0);
  });

  return total.toFixed(2);
};

// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/alipayrecord/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `支付宝流水`);
  });
};

// 重置操作
const rechargeChange = () => {
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.reconciliationStatus = "";
  state.dataForm.alipayOrderNo = "";
  createDate.value = [];
  state.getDataList();
};

// 备注按钮点击事件
const dialogVisible = ref(false);
const dialogInputValue = ref({} as any);
const handleRemark = (row: any) => {
  dialogVisible.value = true;
  dialogInputValue.value = { ...row };
};
// 订单备注保存
const submitLoading = ref(false);
const handleSumbit = () => {
  submitLoading.value = true;
  baseService
    .put("/automaticReconciliation/alipayrecord", dialogInputValue.value)
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success({
          message: "保存成功！",
          duration: 500
        });
        dialogVisible.value = false;
        state.getDataList();
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

// 取消关闭按钮
const cancelChange = () => {
  dialogVisible.value = false;
  dialogInputValue.value = {};
};
</script>

<style lang="less" scoped>
.channelPage {
  padding: 0px 24px;
}
.linkSty {
  display: flex;
  align-items: center;
  text-align: center;
  width: fit-content;
  margin: auto;
  .copyIcon {
    display: none;
    width: 1px;
  }
  &:hover {
    .copyIcon {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
.channelBody {
  display: flex;
  gap: 12px;
  .channelBody_left {
    width: 186px;
    height: calc(100vh - 228px);
    border-radius: 4px;
    border: 1px solid #ebeef5;
    .left_top_but {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .left_top_menu {
      .left_top_item {
        padding: 18px 12px 17px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .name {
          font-weight: 400;
          font-size: 12px;
          color: #303133;
          width: 100px;
          line-height: 22px;
        }
        .operate {
          display: flex;
          gap: 12px;
        }
        &:hover {
          .name {
            color: var(--el-color-primary);
          }
          background-color: var(--el-color-primary-light-9);
        }
      }
      .active {
        .name {
          color: var(--el-color-primary);
        }
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
  .channelBody_right {
    flex: 1;
    // border: 1px solid red;
  }
}
.TableXScrollSty {
  :deep(.NYpagination) {
    right: 48px;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
