<template>
  <div class="container bargain-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <ny-table noDataType="4" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.state" @change="state.getDataList"></ny-button-group>
        </template>

        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important;" v-model="state.dataForm.keywords" placeholder="请输入商品编码/买家ID/卖家ID" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
            </el-form-item>

            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>

        <template #header-custom>
          <div class="mb-12">
            <el-button type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">删除</el-button>
          </div>
        </template>

        <!-- 商品标题 -->
        <template #shopTitle="{ row }">
          <el-text class="pointer" type="primary" text @click="goodsDetailsHandle(row)">{{ row.shopTitle }}</el-text>
        </template>

        <!-- 买家名称 -->
        <template #buyerName="{ row }">
          <el-text class="pointer" type="primary" text @click="buyerDetailsHandle(row.buyerId)">{{ row.buyerName || "-" }}</el-text>
        </template>

        <!-- 卖家名称 -->
        <template #sellerName="{ row }">
          <el-text class="pointer" type="primary" text @click="sellerDetailsHandle(row)">{{ row.sellerName || "-" }}</el-text>
        </template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == 0" type="primary">议价中</el-tag>
          <el-tag v-if="row.state == 1" type="success">已同意</el-tag>
          <el-tag v-else-if="row.state == 2" type="danger">已拒绝</el-tag>
          <el-tag v-else-if="row.state == 3" type="info">已过期</el-tag>
        </template>

        <template #operation="{ row }">
          <el-button v-if="row.state == 0" type="primary" text bg @click="negotiationReviewHandle(row.id)"> 议价审核 </el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(row.id)"> 删除 </el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 买家详情 -->
    <buyer-details ref="buyerDetailsRef" :key="buyerDetailsKey"></buyer-details>

    <!-- 卖家详情 -->
    <seller-detials ref="sellerDetialsRef" :key="sellerDetialsKey"></seller-detials>

    <!-- 议价审核 -->
    <negotiation-review ref="negotiationReviewRef" :key="negotiationReviewKey" @refresh="state.getDataList()"></negotiation-review>

    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import BuyerDetails from "./components/BuyerDetails.vue";
import SellerDetials from "./components/SellerDetials.vue";
import NegotiationReview from "./components/NegotiationReview.vue";
import ShopInfo from "../shop-info.vue";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: 102
  },
  {
    prop: "shopTitle",
    label: "商品标题",
    minWidth: 184
  },
  {
    prop: "gameTitle",
    label: "游戏名称",
    minWidth: 184
  },
  {
    prop: "buyerName",
    label: "买家名称",
    minWidth: 152
  },
  {
    prop: "sellerName",
    label: "卖家名称",
    minWidth: 104
  },
  {
    prop: "shopPrice",
    label: "原价(元)",
    minWidth: 136,
    sortable: "custom"
  },
  {
    prop: "buyerPrice",
    label: "买家出价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "sellerPrice",
    label: "卖家出价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "agreePrice",
    label: "同意价格(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "createDate",
    label: "发起时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "buyerOfferTime",
    label: "买家出价时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "sellerOfferTime",
    label: "卖家出价时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "agreeTime",
    label: "同意时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "refuseTime",
    label: "拒绝时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "bargainExpireTime",
    label: "有效期",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "state",
    label: "状态",
    width: 111
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 170
  }
]);

const view = reactive({
  getDataListURL: "/sub/bargain/page",
  getDataListIsPage: true,
  deleteURL: "/sub/bargain/delete",
  deleteIsBatch: true,
  dataForm: {
    gameId: "",
    state: "",
    keywords: "",
    startTime: "",
    endTime: "",
    order: "desc",
    orderField: "create_date"
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  { label: "议价中", value: "0" },
  { label: "已同意", value: "1" },
  { label: "已拒绝", value: "2" },
  { label: "已过期", value: "3" }
];

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] : "";
};

// 查看买家详情
const buyerDetailsRef = ref();
const buyerDetailsKey = ref(0);
const buyerDetailsHandle = async (id: number | string) => {
  buyerDetailsKey.value++;
  await nextTick();
  buyerDetailsRef.value.init(id);
};

// 查看卖家详情
const sellerDetialsRef = ref();
const sellerDetialsKey = ref(0);
const sellerDetailsHandle = async (obj: any) => {
  sellerDetialsKey.value++;
  await nextTick();
  sellerDetialsRef.value.init(obj.sellerId, obj.sellerType);
};

// 议价审核
const negotiationReviewRef = ref();
const negotiationReviewKey = ref(0);
const negotiationReviewHandle = async (id: number | string) => {
  negotiationReviewKey.value++;
  await nextTick();
  negotiationReviewRef.value.init(id);
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const goodsDetailsHandle = async (row: any) => {
  shopInfoKey.value++;
  await nextTick();

  let data = JSON.parse(JSON.stringify(row));
  data = {
    ...data,
    code: data.shopCode,
    gameName: data.gameTitle,
    title: data.shopTitle,
    price: data.shopPrice
  };

  shopInfoRef.value.init(data);
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
onMounted(() => {
  getGamesList();
});
watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
  }
);
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
</style>