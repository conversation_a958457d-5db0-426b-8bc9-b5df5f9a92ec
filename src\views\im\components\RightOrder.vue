<template>
  <div class="right-detail flx-column" v-loading="dataLoading">
    <div class="header" v-if="orderLog && orderLog.length">
      <div class="title">{{ orderLog[0].name }}</div>
      <div class="desc">{{ orderLog[0].content }}</div>
    </div>

    <div class="order-detail">
      <el-image v-if="orderInfo.log" class="order-img" :src="orderInfo.log"></el-image>
      <div class="order-info">
        <div class="info-item">
          <div class="label">订单编号：</div>
          <div class="value text-primary">{{ orderInfo.sn || orderInfo.id }}</div>
        </div>
        <div class="info-item">
          <div class="label">创建时间：</div>
          <div class="value">{{ orderInfo.createDate || orderInfo.orderCreateDate }}</div>
        </div>
        <div class="info-item">
          <div class="label">游戏名称：</div>
          <div class="value">{{ orderInfo.gameName }}</div>
        </div>
        <div class="info-item" v-if="orderInfo.saleType">
          <div class="label">订单类型：</div>
          <div class="value text-primary">{{ getDictLabel(store.state.dicts, "sale_type", orderInfo.saleType) }}</div>
        </div>
        <div class="info-item" v-if="guaranteeInformation && guaranteeInformation.length">
          <div class="label">包赔类型：</div>
          <div class="value">{{ guaranteeInformation.join(",") }}</div>
        </div>
        <div class="info-item">
          <div class="label">成交价：</div>
          <div class="value">￥{{ orderInfo.dealAmount || orderInfo.amount }}</div>
        </div>
      </div>
    </div>

    <el-scrollbar class="order-log">
      <el-timeline style="max-width: 600px">
        <el-timeline-item v-for="(item, index) in orderLog" :key="index" :hide-timestamp="true" :color="item.color">
          <div class="title">{{ item.name }}</div>
          <div class="flx-justify-between content">
            <div class="desc sle">{{ item.content }}</div>
            <dir class="time">{{ dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss") }}</dir>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch } from "vue";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store";
import { getDictLabel } from "@/utils/utils";
import baseService from "@/service/baseService";
import dayjs from "dayjs";

const store = useAppStore();
const imStore = useImStore();

// 订单日志
const orderLog = ref([]);

// 包赔信息
const guaranteeInformation = ref([]);

// 订单信息
const orderInfo = ref(<any>{});

const dataLoading = ref(false);

// 查询订单交易进度
const getOrderFlow = () => {
  if (imStore.currentConversation.conversationType != 3) return;
  dataLoading.value = true;
  orderLog.value = [];
  baseService
    .get("/api/im/query/orderFlow?groupId=" + imStore.currentConversation.targetId)
    .then((res) => {
      orderInfo.value = imStore.currentConversation.sessionData.type == 4 ? res.data.purchaseVo : { ...res.data.saleOrder, creator: res.data.shop.creator || res.data.saleOrder.creator };

      imStore.currentConversation.sessionData.orderFlow = res.data.orderFlow;

      // 订单卖家id
      imStore.orderBuyerId = orderInfo.value.creator;
      imStore.orderInfo = orderInfo.value;

      let imFlows = res.data.imFlows.filter((item: any) => item.createDate);

      let log = imFlows.slice(0, res.data.orderFlow);
      log[log.length - 1].color = "var(--el-color-primary)";
      orderLog.value = log.reverse();
      if (res.data.guaranteeEntities && res.data.guaranteeEntities.length) {
        guaranteeInformation.value = res.data.guaranteeEntities.map((item) => item.title);
      }
      nextTick(() => {
        let data = log[log.length - 1].children[0];
        let submitImUid = data.submitImUid && data.submitImUid.length ? data.submitImUid : [];

        imStore.orderTransactionProgress = [];

        res.data.imFlows.map((item) => {
          imStore.orderTransactionProgress["sort-" + item.sort] = item.children && item.children.length && item.children[0].submitImUid ? item.children[0].submitImUid : [];
          if (item.sort == 5) {
            let list = res.data.imFlows[3];
            imStore.orderTransactionProgress["sort-" + item.sort] = list.children && list.children.length && list.children[1].submitImUid ? list.children[1].submitImUid : [];
          }
        });
      });
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

let timer = ref();

watch(
  () => imStore.currentConversation,
  (newVal, oldVal) => {
    if (imStore.currentConversation && imStore.currentConversation.targetId && newVal.targetId != oldVal?.targetId) {
      timer.value = setTimeout(() => {
        getOrderFlow();
      }, 1000);
    } else if (newVal.latestMessage?.content?.msgType == "CU:flow") {
      clearTimeout(timer.value);
      timer.value = setTimeout(() => {
        getOrderFlow();
      }, 1000);
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.right-detail {
  width: 348px;
  box-shadow: inset 4px 0px 4px 0px rgba(0, 0, 0, 0.08);
  border-left: 1px solid #e4e7ed;
  position: relative;
  background: #fff;

  .header {
    height: 92px;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px;

    .title {
      font-size: 16px;
      line-height: 14px;
    }

    .desc {
      color: #909399;
      padding-top: 24px;
    }
  }

  .order-detail {
    display: flex;
    padding: 20px;

    .order-img {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      margin-right: 16px;
    }

    .order-info {
      .info-item {
        padding-bottom: 12px;
        line-height: 1;

        .label {
          color: #909399;
        }

        .value {
          padding-top: 4px;
        }
      }
    }
  }

  .order-log {
    border-top: 1px solid #e4e7ed;
    flex: 1;
    padding: 20px 20px 20px 0;

    .title {
      font-weight: bold;
    }

    .el-timeline-item {
      padding-bottom: 24px;
    }

    .content {
      font-size: 12px;
      line-height: 24px;
      color: #909399;

      .desc {
        flex: 1;
      }

      .time {
        width: 112px;
        padding: 0;
        margin-left: 10px;
      }
    }
  }
}
</style>
