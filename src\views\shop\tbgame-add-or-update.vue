<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
    :close-on-press-escape="false" class="shopSetDialog">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
      label-width="120px">
      <el-form-item label="游戏名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="运行设备" prop="type">
        <ny-radio v-model="dataForm.type" :datas="radioData"></ny-radio>
      </el-form-item>
      <el-form-item label="图标" prop="image">
        <ny-upload v-model="dataForm.image" v-model:headUrl="dataForm.image" :imageUrl="dataForm.image"
          :fileList="fileList" :limit="1" :listNumber="1" file-size="1024" accept="image/*"
          @urlArr="getPic"></ny-upload>
      </el-form-item>
      <el-form-item label="游戏区服" prop="gameAreaList"></el-form-item>
      <el-scrollbar max-height="300px">
        <div class="listDatas">
          <div v-for="(item, index) in dataForm.gameAreaList" :key="index" class="listDatasItem">
            <div class="flexs fatherCon">
              <el-icon color="#999" size="16" @click="changeFold(index)"
                :style="{ transform: item.fold ? `rotate(0deg)` : `rotate(90deg)`, transition: `all 0.3s` }">
                <CaretRight />
              </el-icon>
              <el-input v-model="item.name" />
              <el-button type="primary" @click="handleAddFather()">添加一级分类</el-button>
              <div style="width: 60px; margin-left: 12px;">
                <el-button type="danger" v-if="dataForm.gameAreaList.length > 1"
                  @click="handleDelFather(item, index)">删除</el-button>
              </div>
            </div>
            <div class="sonCon" v-if="!item.fold">
              <div v-for="(item2, index2) in item.children" :key="index2">
                <div class="flexs sonItem">
                  <div style="width: 31px; height: 55px;">
                    <img :src="brokeLine" style="width: 31px; height: 32px;">
                  </div>
                  <el-input v-model="item2.name" />
                  <el-button color="#668AC4" @click="handleAddSon(item2, index)">添加二级分类</el-button>
                  <div style="width: 60px; margin-left: 12px;">
                    <el-button color="#FF7D84" v-if="item.children.length > 1"
                      @click="handleDelSon(index2, index)">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { uuid } from "@/components/ny-flowable/package/utils";
import brokeLine from '@/assets/images/brokeLine.png';
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const radioData = reactive([{ label: '端游', value: '2' }, { label: '手游', value: '1' }]);
const gameCode = ref();
const gameId = ref();
const fileList: any = ref([]);

const dataForm = reactive({
  id: "",
  code: "",
  name: "",
  status: "",
  image: "",
  type: "",
  sort: "",
  ishot: "",
  isShfl: "",
  sort2: "",
  userId: "",
  isDelete: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
  gameAreaList: [
    { id: '', code: '', name: '', pid: '0', gameCode: gameCode.value, gameId: gameId.value, fold: true, children: [{ id: 'listSon' + uuid(), pid: '', code: '', name: '', gameCode: gameCode.value, gameId: gameId.value }] }
  ]
});

const rules = ref({
  name: [{ required: true, message: '请输入游戏名称', trigger: ['blur', 'change'] }],
  type: [{ required: true, message: '请选择运行设备', trigger: ['blur', 'change'] }],
  image: [{ required: true, message: '请上传图标', trigger: ['blur', 'change'] }],
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取图标链接
const getPic = (value: any) => {
  dataForm.image = value[0].response.data.src.trim();
}

// 折叠按钮
const changeFold = (index: any) => {
  dataForm.gameAreaList[index].fold = !dataForm.gameAreaList[index].fold;
}
// 添加一级分类
const handleAddFather = () => {
  dataForm.gameAreaList.push({ id: '', code: '', name: '', pid: '0', gameCode: gameCode.value, gameId: gameId.value, fold: true, children: [{ id: 'listSon' + uuid(), pid: '', code: '', name: '', gameCode: gameCode.value, gameId: gameId.value }] });
}

// 添加二级分类
const handleAddSon = (row: any, index: any) => {
  dataForm.gameAreaList[index].children.push({ id: 'listSon' + uuid(), code: '', name: '', pid: row.id || '', gameCode: gameCode.value, gameId: gameId.value })
}

// 删除一级分类
const handleDelFather = (row: any, index: any) => {
  dataForm.gameAreaList.splice(index, 1)
}

// 删除二级分类
const handleDelSon = (index2: any, index: any) => {
  dataForm.gameAreaList[index].children.splice(index2, 1);
}

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbgame/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    gameCode.value = dataForm.code || '';
    gameId.value = dataForm.id || '';
    fileList.value = [{ url: res.data.image }];
    if (dataForm.gameAreaList.length == 0) {
      dataForm.gameAreaList.push({ id: '', code: '', name: '', pid: '0', gameCode: gameCode.value, gameId: gameId.value, fold: true, children: [{ id: 'listSon' + uuid(), pid: '', code: '', name: '', }] });
    }
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  let arrs: any = Object.assign({}, dataForm);
  let arrs2: any = JSON.parse(JSON.stringify(dataForm.gameAreaList));
  arrs2.forEach((element: any) => {
    delete element.fold;
    if (element.children && element.children.length > 0) {
      element.children.forEach((element: any, index2: number) => {
        if (element.id.includes('listSon')) {
          element.id = '';
        }
      });
    }
  });
  let arrs3: any = [];
  arrs2.forEach((ele: any) => {
    if (ele.name != '') {
      arrs3.push(ele)
    }
  })
  arrs.gameAreaList = arrs3;
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/shop/tbgame", arrs).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});

</script>

<style lang="less" scoped>
.shopSetDialog {
  .el-table__indent {
    padding-left: 0px !important;
  }
}

.listDatas {
  width: 100%;
  padding: 0 10%;
  box-sizing: border-box;

  :deep(.el-input) {
    flex: 1;
    margin: 0 10px;
  }

  :deep(.el-button) {
    color: #fff;
  }
}

.listDatasItem {
  width: 100%;
}

.flexs {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fatherCon {
  width: 100%;
  margin-bottom: 12px;
}

.sonCon {
  width: 100%;
  padding-left: 30px;
  margin-top: 12px;

  :deep(.el-input__wrapper) {
    background-color: #F2F2F2;
    box-shadow: 0 0 0 1px #F2F2F2 inset;
  }

  &:last-child {
    margin-top: 0;
  }
}
</style>