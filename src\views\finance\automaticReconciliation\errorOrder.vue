<template>
  <el-dialog v-model="dialogVisible" title="异常订单处理" width="480">
    <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
      <div class="cardDescriptions" style="padding: 0">
        <el-descriptions style="width: 100%" class="descriptions" border :column="1">
          <el-descriptions-item>
            <template #label><span>游戏名称</span></template>
            {{ dataForm.gameName || "-" }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>商品编码</span></template>
            {{ dataForm.shopCode || "-" }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>须{{ orderType == '1' ? '追' : '付' }}金额(元)</span></template>
            {{ dataForm.difference || "-" }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>已{{ orderType == '1' ? '追' : '付' }}金额(元)<span style="color: red">*</span></span></template>
            <el-form-item label="已{{ orderType == '1' ? '追' : '付' }}金额(元)" prop="madeUpDifference">
              <el-input-number class="numIpt" style="width: 100%" type="number" :controls="false" :max="99999999" :precision="2" v-model="dataForm.madeUpDifference" placeholder="请输入金额"></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label><span>付款凭证<span style="color: red">*</span></span></template>
            <el-form-item label="付款凭证" prop="paymentVoucher">
              <ny-upload v-model:imageUrl="dataForm.paymentVoucher" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="btnLoading" @click="dialogVisible = false">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="submitForm()">确认处理完成</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { ref, reactive } from "vue";
const dataForm = ref({} as any);

const rules = reactive({
  paymentVoucher: [{ required: true, message: "请选择付款凭证", trigger: "change" }],
  madeUpDifference: [{ required: true, message: "请输入已追金额", trigger: "blur" }]
});
const emit = defineEmits(["refresh"]);
const dialogVisible = ref(false);
const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!dataForm.value.paymentVoucher) {
      ElMessage.warning("请上传付款凭证！");
    }
    if (!valid) return;
    btnLoading.value = true;
    let param = { ...dataForm.value };
    ElMessageBox.confirm(
    `请确认已${orderType.value == '1'? '追' : '付'}金额：${dataForm.value.madeUpDifference}元?`,
    '确认金额',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 如需要处理参数，自行处理
      baseService.post("/automaticReconciliation/autoreconciliation/exceptionOrderHandle", param).then((res) => {
        if (res.code == 0) {
          btnLoading.value = false;
          ElMessage.success("保存成功");
          dialogVisible.value = false;
          emit("refresh");
        }
      });
    })
    .catch(() => {
      btnLoading.value = false;
    })
    
  });
};

const orderType = ref('');

const init = (row: any, billType: any) => {
  dialogVisible.value = true;
  dataForm.value.id = row.id;
  orderType.value = billType;
  // 如需要处理参数，自行处理
  baseService.get("/automaticReconciliation/autoreconciliation/exceptionOrderEcho", {id:row.id}).then(res=>{
    Object.assign(dataForm.value,res.data);
  })
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.numIpt {
  :deep(.el-input__inner) {
    text-align: left;
  }
}
:deep(.el-form-item) {
  &.is-error {
    .el-input__inner {
      &::placeholder {
        color: var(--el-color-danger);
      }
    }
    .el-select__placeholder {
      color: var(--el-color-danger);
    }
    .el-form-item__error {
      display: none !important;
      opacity: 0 !important;
    }
  }
  &.is-success {
    .el-form-item__error {
      transition: none !important;
      opacity: 0 !important;
    }
  }
}
</style>
