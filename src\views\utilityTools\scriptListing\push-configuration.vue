<template>
  <el-drawer v-model="visible" size="944" class="infoDrawerConfig">
    <template #header>
      <div class="drawer_title">推送配置</div>
    </template>
    <el-tabs style="margin-bottom: 0" class="accountTabsscriptpush" v-model="currentTypeIndex" @tab-change="tabsTypeChange">
      <el-tab-pane v-for="item in allTypeList" :key="item.typeCode" :label="item.typeName" :name="item.typeCode"></el-tab-pane>
    </el-tabs>
    <el-scrollbar v-loading="requestLoading">
      <div class="shop_page">
        <div class="shop_page_basic">
          <!-- 商品信息 -->
          <el-form :model="dataForm" :rules="rules" ref="formRef" label-position="top" style="padding: 12px">
            <el-row :gutter="12">
              <el-col :span="24">
                <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                  <el-descriptions-item label="合作商">{{ dataForm.partnerName }}</el-descriptions-item>
                  <el-descriptions-item label="店铺名称">{{ shopName }}</el-descriptions-item>
                </el-descriptions>
              </el-col>
              <template v-if="currentTypeIndex == 1">
                <el-col :span="24">
                  <el-form-item label="游戏倍率配置" prop="gameIdList">
                    <el-table :data="dataForm.configGameList" border height="280" style="width: 100%">
                      <el-table-column prop="gameName" label="游戏名称" align="center" />
                      <el-table-column prop="priceRate" label="推送加价倍率" align="center">
                        <template #header>
                          <span style="margin-right: 8px">推送加价倍率</span>
                          <el-tooltip effect="dark" content="倍率超过100为加价,低于100为打折" placement="top-start">
                            <el-icon color="#909399" size="16"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <template #default="{ row }">
                          <div class="flx-justify-between">
                            <el-input type="number" v-model="row.priceRate" clearable size="small" />
                            <span style="margin-left: 4px">%</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="priceAmount" label="加价金额" align="center">
                        <template #header>
                          <span style="margin-right: 8px">加价金额</span>
                          <el-tooltip effect="dark" content="若同时进行加价倍率与加价金额的设置，以“原价*加价倍率+加价金额”计算" placement="top-start">
                            <el-icon color="#909399" size="16"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <template #default="{ row }">
                          <div class="flx-justify-between">
                            <el-input type="number" v-model="row.priceAmount" clearable size="small" />
                            <span style="margin-left: 4px">元</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="whetherPush" label="是否推送" align="center">
                        <template #default="{ row }">
                          <el-switch v-model="row.whetherPush" :active-value="true" :inactive-value="false" active-text="是" inactive-text="否" inline-prompt />
                        </template>
                      </el-table-column>
                      <!-- 空状态 -->
                      <template #empty>
                        <div style="padding: 68px 0">
                          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                        </div>
                      </template>
                    </el-table>
                  </el-form-item>
                  <!-- <el-form-item label="游戏" prop="gameIdList">
                    <div class="checkBox">
                      <el-checkbox-group v-model="dataForm.gameIdList">
                        <el-checkbox style="margin-bottom: 12px; margin-right: 12px" v-for="(radioItem, radioItemIndex) in gameData" :key="radioItemIndex" :value="radioItem.gameId" :label="+radioItem.gameId" border>{{ radioItem.gameName }}</el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </el-form-item> -->
                </el-col>
                <el-col :span="24">
                  <el-form-item label="商品来源" prop="productSources">
                    <el-checkbox-group v-model="dataForm.productSources" @change="productSourcesChange">
                      <el-checkbox label="1" :value="1">平台自营</el-checkbox>
                      <el-checkbox label="2" :value="2">合作商推送</el-checkbox>
                    </el-checkbox-group>
                    <div class="checkBox" v-if="dataForm.productSources.includes(2)">
                      <el-checkbox style="margin-bottom: 12px; margin-right: 12px" v-for="(radioItem, radioItemIndex) in dataForm.configTenantList" :key="radioItemIndex" :value="radioItem.tenantId" :label="+radioItem.tenantId" border v-model="radioItem.whetherSelect">{{
                        radioItem.tenantName
                      }}</el-checkbox>
                    </div>
                    <!-- <el-radio-group v-model="dataForm.productSource">
                      <el-radio :value="1" :label="1">平台自营</el-radio>
                      <el-radio :value="2" :label="2">合作商推送</el-radio>
                    </el-radio-group> -->
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="商品间隔时间(分钟)" prop="pushIntervalMinutes">
                    <el-input style="width: 442px" type="number" v-model="dataForm.pushIntervalMinutes" :min="0" :precision="0" placeholder="请输入商品间隔时间" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="每日推送数量上限(个)" prop="dailyPushLimit">
                    <el-input style="width: 442px" type="number" v-model="dataForm.dailyPushLimit" :min="0" :precision="0" placeholder="请输入每日上限" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="账号规则" prop="accountRules">
                    <el-radio-group v-model="dataForm.accountRules">
                      <el-radio value="1" label="1">真实</el-radio>
                      <el-radio value="2" label="2">虚拟</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="同步价格范围(元)" prop="priceMin">
                    <div style="width: 442px; display: flex; align-items: center">
                      <el-input type="number" v-model="dataForm.priceMin" :min="0" :precision="0" placeholder="请输入最低价" style="width: 100%" />
                      <span style="padding: 0 2px">~</span>
                      <el-input type="number" v-model="dataForm.priceMax" :min="0" :precision="0" placeholder="请输入最高价" style="width: 100%" />
                    </div>
                  </el-form-item>
                </el-col>
              </template>
              <el-col :span="24">
                <el-form-item label="是否生效" prop="isActive">
                  <el-switch v-model="dataForm.isActive" active-text="生效" inactive-text="不生效" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle(formRef)">确定</el-button>
      </div>
    </template>
  </el-drawer>
  <!-- </el-dialog> -->
</template>

<script lang="ts" setup>
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import { Shop, Pointer, Back } from "@element-plus/icons-vue";
import { ref, defineEmits, defineExpose, reactive } from "vue";
import { FormItemRule, FormInstance, ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";
const { t } = useI18n();

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const rules = reactive({
  partnerName: [
    {
      required: true,
      message: "请选择合作商",
      trigger: "blur"
    }
  ],
  // gameIdList: [
  //   {
  //     required: true,
  //     message: "请选择游戏",
  //     trigger: "blur"
  //   }
  // ],
  productSources: [
    {
      required: true,
      message: "请选择商品来源",
      trigger: "blur"
    }
  ],
  accountRules: [
    {
      required: true,
      message: "请选择账号规则",
      trigger: "blur"
    }
  ],
  pushIntervalMinutes: [
    {
      required: true,
      message: "请输入商品间隔时间",
      trigger: "blur"
    }
  ],
  dailyPushLimit: [
    {
      required: true,
      message: "每日推送数量上限",
      trigger: "blur"
    }
  ],
  priceMin: [
    {
      required: true,
      message: "请输入同步价格范围",
      trigger: "blur"
    },
    {
      validator: (rule: any, val: string, callback: Function) => {
        if (dataForm.priceMax && dataForm.priceMin) {
          if (dataForm.priceMax < dataForm.priceMin) {
            callback(new Error("最高价不能低于最低价！"));
          } else {
            callback();
          }
        } else {
          callback(new Error("请输入同步价格范围"));
        }
        callback();
      },
      trigger: "blur"
    }
  ],
  isActive: [
    {
      required: true,
      message: "请选择",
      trigger: "blur"
    }
  ]
});
let dataForm = reactive(<any>{
  partnerId: null,
  partnerName: null,
  pushType: null,
  gameIdList: null,
  productSources: [],
  pushIntervalMinutes: null,
  dailyPushLimit: null,
  accountRules: null,
  priceMin: null,
  priceMax: null,
  isActive: null,
  configGameList: null,
  configTenantList: null
});
const formRef = ref<FormInstance>();
//tab
const allTypeList = ref([]);
const curPartnerData = ref(<any>[]); //合作商列表
const currentTypeIndex = ref(1);
const tabsTypeChange = () => {
  hanlePushdata();
};

// 游戏下拉框
const gameData = ref([]);
const resetForm = () => {
  if (currentTypeIndex.value == 1) {
    dataForm.gameIdList = [];
    dataForm.productSources = [];
    dataForm.pushIntervalMinutes = undefined;
    dataForm.dailyPushLimit = undefined;
    dataForm.accountRules = "1";
    dataForm.priceMin = undefined;
    dataForm.priceMax = undefined;
    dataForm.isActive = false;
  } else {
    dataForm.isActive = false;
  }
};
// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = async (formEl: FormInstance | undefined) => {
  //编辑
  await formEl.validate((valid, fields) => {
    if (valid) {
      const params = JSON.parse(JSON.stringify(dataForm));
      if (currentTypeIndex.value == 2 || currentTypeIndex.value == 3) {
        delete params.gameIdList;
        delete params.productSources;
        delete params.pushIntervalMinutes;
        delete params.dailyPushLimit;
        delete params.accountRules;
        delete params.priceMin;
        delete params.priceMax;
      } else {
        params.gameIdList = params.gameIdList.join(",");
      }
      params.isActive = params.isActive ? 2 : 1;
      btnLoading.value = true;
      baseService
        .put("/script/sysscriptpushconfig/refresh", {
          ...params,
          pushType: currentTypeIndex.value
        })
        .then((res) => {
          if (res.code == 0) {
            btnLoading.value = false;
            ElMessage.success("配置成功");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
const getGameList = async () => {
  let res_ = await baseService.get("/mapping/sysgameinfomapping/getOfficialMappingGameList", {
    partnerId: curPartnerData.value.id
  });
  if (res_.code == 0) {
    gameData.value = res_.data;
  }
  hanlePushdata();
};
const hanlePushdata = () => {
  dataForm.gameIdList = [];
  baseService
    .get("/script/sysscriptpushconfig/configDetail", {
      partnerId: curPartnerData.value.id,
      pushType: currentTypeIndex.value,
      scriptUserId: curPartnerData.value.scriptUserId
    })
    .then((res) => {
      if (res.code == 0) {
        if (res.data == null) {
          formRef.value?.resetFields();
          dataForm.partnerId = curPartnerData.value.id;
          dataForm.partnerName = curPartnerData.value.name;
          // 给生效一个默认值
          dataForm.isActive = false;
          dataForm.gameIdList = [];
          dataForm.productSources = [];
          dataForm.configGameList = [];
          dataForm.configTenantList = [];
          dataForm.pushIntervalMinutes = "";
          dataForm.dailyPushLimit = "";
          dataForm.priceMin = "";
          dataForm.priceMax = "";
        } else {
          for (const key in res.data) {
            dataForm[key] = res.data[key];
          }
          dataForm.scriptUserId = curPartnerData.value.scriptUserId;
          dataForm.productSources = dataForm.productSources && dataForm.productSources.length ? dataForm.productSources : [];
          dataForm.partnerId = dataForm.partnerId ? dataForm.partnerId : curPartnerData.value.id;
          dataForm.partnerName = dataForm.partnerName || curPartnerData.value.name;
          dataForm.priceMin = dataForm.priceMin + "";
          dataForm.priceMax = dataForm.priceMax + "";
          dataForm.isActive = dataForm.isActive == 2 ? true : false;
          dataForm.gameIdList = (dataForm.gameIdList || "").split(",").map((ele: any) => ele);
        }
      }
    });
};
// 表单初始化
const shopName = ref(""); // 店铺名称
const init = (data: any, id: any, name: any) => {
  visible.value = true;
  shopName.value = name;
  curPartnerData.value = { ...data, scriptUserId: id };
  dataForm.scriptUserId = id;
  allTypeList.value = [];
  currentTypeIndex.value = 0;
  baseService.get("/script/sysscriptpushtask/getTaskType", { partnerId: curPartnerData.value.id }).then((res) => {
    if (res.code == 0) {
      if (res.data.length > 0) {
        allTypeList.value = res.data;
        currentTypeIndex.value = res.data[0].typeCode;
        getGameList();
      }
    }
  });
};

// 合作商勾选
const productSourcesChange = (value: any) => {
  const show = value.includes(2);
  if (show) {
    dataForm.configTenantList[0].whetherSelect = true;
  } else {
    dataForm.configTenantList.map((item: any) => (item.whetherSelect = false));
  }
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.accountTabsscriptpush {
  background: #fff;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
  // :deep(.el-tabs__item) {
  //     margin: 0;
  //     padding: 0 20px !important;
  //     color: var(--el-text-color-regular);
  // }
  // :deep(.el-tabs__active-bar) .is-top {
  //     transform: translateX(20px) !important;
  // }
}
.shop_page {
  padding: 12px;
}
.games {
  padding: 20px 6px 10px 6px;
  .gamesList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    .gamesItem {
      min-width: 120px;
      height: 28px;
      font-size: 12px;
      line-height: 28px;
      background-color: #fff;
      border: 1px solid var(--el-color-primary);
      border-radius: 4px;
      text-align: center;
      margin: 0px 10px 10px 10px;
      cursor: pointer;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .active {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }
  }
  .icons {
    padding: 6px 10px 10px 10px;
    .el-icon {
      font-size: 16px;
    }
  }
}

.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
}
.shop_page_details {
  .shop_page_details_card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.mytag {
  background: #fcf2bb;
  border-radius: 20px;
  color: #f6930a;
  display: inline-block;
  font-size: 12px;
  height: 25px;
  line-height: 25px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px 0 30px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;

  .topImg {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 0;
    left: 3px;
  }
}
.flod_tab {
  display: flex;
  align-items: flex-start;
  width: 100%;
  .flod_tab_cont {
    flex: 1;
    overflow: hidden;
    :deep(.el-radio) {
      margin-right: 20px;
    }
    :deep(.el-radio.is-bordered.el-radio--small) {
      margin-bottom: 10px;
    }
  }
}
.oper {
  width: 120px;
  height: 28px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .resetFont {
    cursor: pointer;
    margin-right: 6px;
  }

  .foledBtn {
    float: right;
    padding: 12px 10px;
    margin-right: 0;
  }
}
.shop_info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  .shop_info_left {
    display: flex;
    align-items: center;
    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #909399;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
  .shop_info_right {
    display: flex;
    align-items: center;
  }
}
.proportion {
  margin-bottom: 12px;
  :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
    width: 220px;
  }
}
</style>
<style lang="scss">
.infoDrawerConfig {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
    background: #f0f2f5;
  }
  .flod_tab_li {
    font-weight: 400 !important;
    font-size: 14px !important;
    padding: 8px 20px !important;
  }
  .checkBox {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ebeef5;
    padding: 12px 0px 0px 12px;
    max-height: 100px;
    min-height: 40px;
    width: 100%;
    overflow-y: scroll;
  }
}

.radius {
  .el-checkbox {
    margin-right: 24px;
  }
  .el-checkbox__inner {
    border-radius: 4px;
  }
}
</style>
