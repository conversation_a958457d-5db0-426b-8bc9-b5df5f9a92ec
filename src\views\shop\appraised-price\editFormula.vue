<template>
  <el-dialog v-model="visible" title="公式编辑器" width="640" style="padding: 0px">
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog_header">公式编辑器</div>
    </template>
    <div class="formula">
      <el-input v-model="dataForm.formulaName" class="formula_pane_editor" placeholder="请输入公式名称" maxlength="20" show-word-limit />
      <el-input v-model="formulaVlaue" class="formula_pane_editor" :rows="5" type="textarea" placeholder="请输入公式" @blur="handleBlur" ref="inputRef" />
    </div>
    <div class="symbolList">
      <div class="symbol1" @click="appendFormula('()')">括号 ()</div>
      <div class="symbol2" @click="appendFormula('+')">加号 +</div>
      <div class="symbol3" @click="appendFormula('-')">减号 -</div>
      <div class="symbol4" @click="appendFormula('*')">乘号 *</div>
      <div class="symbol5" @click="appendFormula('/')">除号 /</div>
      <div class="symbol6" @click="appendFormula('>')">大于 ></div>
      <div class="symbol7" @click="appendFormula('<')">小于 <</div>
      <div class="symbol8" @click="appendFormula('=')">等于 =</div>
      <div class="symbol1" @click="appendFormula(',')">逗号 ,</div>
    </div>
    <div class="labelList">
      <div @click="appendFormula('单属性价格')">单属性价格</div>
      <div @click="appendFormula('多属性价格')">多属性价格</div>
      <div @click="appendFormula('当前估价')">当前估价</div>
      <div @click="appendFormula('总价格')">总价格</div>
      <div @click="appendFormula('估价结果')">估价结果</div>
    </div>
    <div class="flx-justify-between" style="padding: 12px 24px">
      <div class="result flx">
        <span style="color: #909399">结果值=</span>
        <div v-if="executionInfo.show == '1'">
          <el-text class="mx-1" type="success">公式解析成功;</el-text>
        </div>
        <div v-if="executionInfo.show == '0'">
          <el-text class="mx-1" type="danger">{{ executionInfo.error || "公式解析错误，语法错误;" }}</el-text>
        </div>
      </div>
    </div>
    <div class="field">
      <div class="field_header">引用当前表格字段</div>
      <div class="field_list" v-loading="loadingData">
        <el-input v-model="fieldValue" class="field_search_input" placeholder="搜索属性名称" clearable :prefix-icon="Search" @change="fieldSearch" @clear="fieldSearch" />
        <div style="display: flex; margin-top: 12px; gap: 8px">
          <div style="flex: 1">
            <div style="margin-left: 8px">属性名称</div>
            <div style="max-height: 116px; overflow-y: scroll">
              <div
                class="items flx-justify-between"
                @click="
                  appendFormula(item.name);
                  currentChildrenAtttibute(item.id);
                "
                v-for="(item, index) in priceData.attributeList"
                :key="index"
              >
                <span>{{ item.name }}</span
                ><span class="opt">选择</span>
              </div>
            </div>
          </div>
          <div style="flex: 1">
            <div style="margin-left: 8px">属性明细名称</div>
            <div style="max-height: 116px; overflow-y: scroll">
              <div class="items flx-justify-between" @click="appendFormula(item.name)" v-for="(item, index) in childrenAtttibute" :key="index">
                <span>{{ item.name }}</span
                ><span class="opt">选择</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div style="padding: 0 12px 12px">
        <el-button v-loading="btnLoading" @click="visible = false">取消</el-button>
        <el-button v-loading="btnLoading" type="primary" @click="submit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";
const priceData = reactive({
  attributeList: <any>[],
  hisData: <any>[]
});
const pattern = ref(/^([\u4e00-\u9fa5A-Za-z0-9]+)(=|>=?|<=?|>|<)([\u4e00-\u9fa5A-Za-z0-9]+),([\u4e00-\u9fa5]+|\d+)([+\-*/])([\u4e00-\u9fa5]+|\d+)$/);
const extraFlag = ["单属性价格", "多属性价格", "当前估价", "总价格", "估价结果"];
const emit = defineEmits(["formulaChange"]);
const visible = ref(false);
const btnLoading = ref(false);
const inputRef = ref();
const formulaVlaue = ref("");
const fieldValue = ref("");
const dataForm = reactive({
  formulaName: "",
  id: null
});
const cursorPos = ref(); // 光标位置
const handleBlur = (e: any) => {
  cursorPos.value = e.srcElement.selectionStart;
};

// 插入文本
const appendFormula = (str: any) => {
  const start = formulaVlaue.value.substring(0, cursorPos.value);
  const end = formulaVlaue.value.substring(cursorPos.value);
  formulaVlaue.value = `${start}${str} ${end}`;
  cursorPos.value += str.length + 1;
};

// 公式运行结果
const executionInfo = ref({
  show: "", // 1：运行结果正确 ， 0：运行结果错误
  error: ""
});
// 公式验证
const formulaVerify = (str: string) => {
  executionInfo.value.show = "";
  executionInfo.value.error = "";
  if (!str) {
    return;
  }
  // 正则检验 和 文字正确性验证 珍品传说 = 鲁班七号江户川柯南 , 多属性价格 + 20
  let patternstr = str.replace(/，/g, ",").replace(/ /g, "");
  const match = patternstr.match(pattern.value);
  if (!match || extraFlag.indexOf(match[4]) == -1 || handleAttribute(match[1]) == -1 || handleAttribute(match[3]) == -1) {
    executionInfo.value.show = "0";
    executionInfo.value.error = "公式解析错误， 文本不匹配;";
    return;
  }
  // 1. 检查相邻重复
  const strArr = str.split(" ");
  const data = reactive({
    isAdjacentDuplicate: computed(() => {
      for (let i = 1; i < strArr.length; i++) {
        if (strArr[i] === strArr[i - 1]) {
          return true;
        }
      }
      return false;
    })
  });
  if (data.isAdjacentDuplicate) {
    executionInfo.value.show = "0";
    executionInfo.value.error = "公式解析错误，语法错误;";
    return;
  }

  // 2. 替换变量为数字
  str = str.replace(/ /g, "");
  const chinese = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
  const english = new RegExp("[A-Za-z]+");
  for (let i = 0; i < str.length; i++) {
    if (chinese.test(str[i]) || english.test(str[i])) {
      str = str.replaceAll(str[i], 1);
    }
  }

  // 3. 检查是否有运算符
  if (!str.match(/[+\-*/]/)) {
    executionInfo.value.show = "0";
    executionInfo.value.error = "公式解析错误，语法错误;";
    return;
  }

  // 只验证后面部分
  let fStrArr = str.split(",");
  let fStr = "";
  if (fStrArr.length > 1) {
    fStr = fStrArr[1];
  }
  // 4. 检查表达式语法
  try {
    let makeFun = new Function("return " + fStr);
    try {
      makeFun();
      executionInfo.value.show = "1";
    } catch (error: any) {
      executionInfo.value.show = "0";
      executionInfo.value.error = error.message;
    }
  } catch (errorInfo: any) {
    executionInfo.value.show = "0";
    executionInfo.value.error = errorInfo.message;
  }
};
// 属性判断+属性Id返回
const handleAttribute = (str: any) => {
  // 是数值直接返回， 不是则是属性处理id
  if (/^\d+$/.test(str)) {
    return str;
  }
  let id = -1;
  priceData.attributeList.forEach((ele: any) => {
    if (ele.name == str) {
      id = ele.id;
      return;
    }
    if (ele.children) {
      ele.children.forEach((ele_: any) => {
        if (ele_.name == str) {
          id = ele_.id;
          return;
        }
      });
    }
  });
  return id;
};
// 监听输入公式
watch(
  () => formulaVlaue.value,
  (newValue) => {
    // console.log('监听用户输入', newValue)
    setTimeout(() => {
      formulaVerify(newValue);
    }, 1000);
  }
);

// 字段搜索
const fieldSearch = () => {
  if (fieldValue.value) {
    priceData.attributeList = priceData.hisData.filter((ele) => ele.name.includes(fieldValue.value));
  } else {
    priceData.attributeList = priceData.hisData;
  }
};

// 提交公式
const formulaList = ref(<any>[]);
const submit = () => {
  let str = JSON.parse(JSON.stringify(formulaVlaue.value));
  if (!dataForm.formulaName) {
    ElMessage.warning("请输入公式名称");
    return;
  }
  if (!str) {
    ElMessage.warning("请输入公式");
    return;
  }

  if (executionInfo.value.show == "0") {
    ElMessage.warning("公式解析错误，请修改");
    return;
  }
  btnLoading.value = true;
  str = str.replace(/ /g, "");
  str = str.replace(/([^\u4e00-\u9fa5])/g, " $1 ");
  const list = JSON.parse(JSON.stringify(str.split(" ")));
  const formula: any = []; // 公式；
  const columns: any = []; // 字段；
  list.map((item: any) => {
    let info = formulaList.value.find((ele: any) => ele.name == item);
    if (info) {
      formula.push(info.dynamicKey);
      columns.push(info.dynamicKey);
    } else {
      formula.push(item);
    }
  });
  const params = {
    id: dataForm.id,
    gameId: gameId.value,
    formulaName: dataForm.formulaName,
    formulaJson: handleFormulaParams(formula.filter((item: any) => item != "").join(""))
  };
  emit("formulaChange", params);
};

const closeLoading = () => {
  btnLoading.value = false;
};
const close = () => {
  visible.value = false;
  btnLoading.value = false;
  formulaVlaue.value = "";
  Object.assign(dataForm, {});
};

// 處理提交參數， 接口在父組件處理
const handleFormulaParams = (str: any) => {
  const match = str.match(pattern.value);
  // 逗号前属性值是否為數值判断， logicSign， logicValue 赋null
  let params = {
    appraiseAttributeId: /^\d+$/.test(match[3]) ? handleAttribute(match[1]) : handleAttribute(match[3]),
    logicSign: /^\d+$/.test(match[3]) ? match[2] : null,
    logicValue: /^\d+$/.test(match[3]) ? handleAttribute(match[3]) : null,
    extra: ["single", "multi", "current", "total", "result"][extraFlag.indexOf(match[4])],
    extraFlag: ["}", "+", "-", "*", "/"].indexOf(match[5]),
    extraValue: match[6]
  };
  return JSON.stringify(params);
};

const gameId = ref("");
const targetList = ref(<any>[]);
const loadingData = ref(false);
const init = (row: any, gId: any) => {
  gameId.value = gId;
  visible.value = true;
  loadingData.value = true;
  formulaVlaue.value = "";
  dataForm.formulaName = "";
  dataForm.id = "";
  Object.assign(dataForm, {});
  if (row && row.id) {
    formulaVlaue.value = row.formulaContent;
    dataForm.formulaName = row.formulaName;
    dataForm.id = row.id;
  }

  // 获取当前表格字段
  baseService.post("/appraise/attribute/listTree", { gameId: gameId.value }).then((res) => {
    // 不匹配的不显示
    priceData.attributeList = (res.data || []).filter((ele: any) => ele.id != null);
    priceData.hisData = JSON.parse(JSON.stringify(priceData.attributeList));
    loadingData.value = false;
  });
  nextTick(() => {
    inputRef.value.focus();
  });
};

const childrenAtttibute = ref();
const currentChildrenAtttibute = (id: any) => {
  if (id) {
    let obj = priceData.attributeList.find((ele: any) => ele.id == id);
    childrenAtttibute.value = obj.children || [];
    return;
  }
  childrenAtttibute.value = [];
};
watch(
  () => priceData.attributeList,
  () => {
    if (priceData.attributeList && priceData.attributeList?.length > 0) {
      childrenAtttibute.value = priceData.attributeList[0].children || [];
    }
  },
  { immediate: true, deep: true }
);
defineExpose({
  init,
  close,
  closeLoading
});
</script>
<style lang="scss" scoped>
.dialog_header {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  padding-top: 15px;
  padding-left: 16px;
}
.editFormData {
  width: 100%;
  height: 274px;
  padding: 12px;
}
.formula {
  padding: 24px;
  padding-top: 0;
}
.formula_pane_editor {
  :deep(.el-textarea__inner) {
    box-shadow: none;
    border-radius: 8px;
    padding-top: 10px;
    padding-left: 0;
  }
  :deep(.el-input__wrapper) {
    height: 40px;
    padding-left: 0;
    box-shadow: none;
    border-bottom: 1px solid #eee;
  }
}
.result {
  font-weight: 400;
  font-size: 14px;
  color: #303133;
  line-height: 26px;
  // span{
  //     color: #909399;
  // }
}
.symbolList {
  padding-bottom: 18px;
  padding-left: 24px;

  > div {
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    cursor: pointer;
    margin-right: 16px;
  }
}
.labelList {
  padding-bottom: 12px;
  padding-left: 24px;
  border-bottom: 1px solid #ebeef5;

  > div {
    border-radius: 2px 2px 2px 2px;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    cursor: pointer;
    margin-right: 16px;
    background-color: #f2f3f5;
  }
}
.symbol1 {
  background: #daeeff;
  color: #3366ff;
}
.symbol2 {
  background: #fcf6ec;
  color: #e6a23c;
}
.symbol3 {
  background: #daffe6;
  color: #00c568;
}
.symbol4 {
  background: #dddcff;
  color: #5654ff;
}
.symbol5 {
  background: #cdf0ff;
  color: #00a5ef;
}
.symbol6 {
  background: #ffece8;
  color: #f53f3f;
}
.symbol7 {
  background: #fff3e8;
  color: #f77234;
}
.symbol8 {
  background: #ffe8fb;
  color: #d91ad9;
}

.field {
  background-color: #fafafa;
  min-height: 200px;
  border-radius: 0px 0px 4px 4px;
  .field_header {
    padding: 12px;
    font-weight: 500;
    font-size: 14px;
    color: #303133;
    line-height: 22px;
    border-bottom: 1px solid #ebeef5;
  }
  .field_list {
    padding: 8px 12px;
    .field_search_input {
    }
    .items {
      font-weight: 400;
      font-size: 14px;
      color: #606266;
      line-height: 22px;
      margin-top: 8px;
      cursor: pointer;
      padding: 4px 8px;

      .opt {
        display: none;
      }
    }

    .items:hover {
      background-color: #ecf0fb;
      border-radius: 4px 4px 4px 4px;
      color: #4165d7;
      .opt {
        display: block;
        color: #4165d7;
      }
    }
  }
}
</style>
