<template>
  <div class="default-main ba-table-box" style="background-color: var(--ba-bg-color-overlay); border-radius: 0">
    <div>
      <div class="cardTop">
        <div class="flx-justify-between" style="width: 100%">
          <div class="flx">
            <el-dropdown>
              <el-button link
                >{{ currentUserName }}<el-icon style="margin-left: 8px"><ArrowDown /></el-icon
              ></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in shopList"
                    @click="
                      currentUserName = item.name;
                      changeShop(item.id);
                    "
                    >{{ item.name }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="flx" v-if="state.dataForm.partnerId">
            <el-button style="border: 1px solid #F44A29;color: #F44A29;" color="#FFFFFF" v-if="isVerificationCodeShow" @click="verificationCodeChange">
              <el-icon color="#F44A29" size="14" style="margin-right: 5px;"><WarningFilled /></el-icon>发送验证码
            </el-button>
            <el-button type="primary" @click="automatic">自动推送设置</el-button>
            <el-button type="warning" @click="storeSettingsFn">店铺设置</el-button>
            <el-button type="success" @click="setIPHandle">IP设置</el-button>
          </div>
        </div>
      </div>
      <div class="setUptable">
        <!-- 推送商品 -->
        <pushGoods ref="pushGoodsRef" :partnerName="dataObj.partnerName"></pushGoods>
      </div>
    </div>
    <!-- IP设置 -->
    <IPSet @refresh="getAllPartnerList" ref="IPSetRef"></IPSet>
    <!-- 店铺设置 -->
    <storeSettings ref="storeSettingsRef" @refreshDataList="getShopList"></storeSettings>
    <!-- 自动上架设置 -->
    <automaticShelvingSettings ref="automaticShelvingSettingsRef"></automaticShelvingSettings>
    <!-- 同步上架短信验证码 -->
    <SendVerificationCode ref="SendVerificationCodeRef" ></SendVerificationCode>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
import { useI18n } from "vue-i18n";
// 组件引入
import IPSet from "./IPset.vue";
import pushGoods from "./push-goods.vue";
import storeSettings from "./store-settings.vue";
import automaticShelvingSettings from "@/views/shop/partnerPushDetails/automatic-shelving-settings.vue";
import baseService from "@/service/baseService";
import SendVerificationCode from "./SendVerificationCode.vue";

const currentUserName = ref();
const { t } = useI18n();
const IPSetRef = ref();
const pushGoodsRef = ref();
const storeSettingsRef = ref();
const dataObj: {
  gameList: any[];
  curAllPartnerList: any[];
  partnerName: string;
} = reactive({
  gameList: [],
  curAllPartnerList: [],
  partnerName: ""
});
const state = reactive({
  // 传参
  dataForm: {
    partnerId: "0", //平台
    gameId: "0", // 游戏
    outPname: "",
    scriptUserId: "" // 店铺Id
  },
  sortForm: {
    order: null,
    column: null
  },
  selectionList: [],
  dataList: [],
  count: 0
});

const platformList = ref(<any>[]);
const tableLoading = ref(false);
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  limit: 10
});

// 父组件改变参数
const changeDataForm = async (partnerId: any, gameId: any, changePartner = false, changeGame = false) => {
  platformList.value = [];
  state.selectionList = [];
  state.dataForm.gameId = gameId;
  state.dataForm.partnerId = partnerId;
  state.dataForm.outPname = "";
  queryParams.outName = "";
  if (changePartner) {
    await getAllPartnerList();
    // 改合作商更店铺数据和反馈更新游戏数据
    await getShopList();
  } else if (changeGame) {
    // 改游戏+选店铺更推送数据
    pushGoodsHandle();
  }
};

// 获取店铺数据
const shopList = ref(<any>[]);
const getShopList = () => {
  baseService.get("/script/sysscriptuser/page", { scriptPartnerId: state.dataForm.partnerId }).then((res) => {
    if (res.data) {
      shopList.value = res.data.list;
      state.dataForm.scriptUserId = shopList.value.some((item: any) => item.id == state.dataForm.scriptUserId) ? state.dataForm.scriptUserId : shopList.value[0].id;
      let obj = shopList.value.find((item: any) => item.id == state.dataForm.scriptUserId);
      currentUserName.value = obj ? obj.name : shopList.value[0].name;
      emit("upDateScriptUserId", state.dataForm.scriptUserId);
      pushGoodsHandle();
      isVerificationCode();
    }
  });
};
const emit = defineEmits(["upDateScriptUserId"]);
// 店铺tab点击回调
const changeShop = (value: any) => {
  state.dataForm.scriptUserId = value;
  isVerificationCode();
  emit("upDateScriptUserId", value);
};

// 判断是否有验证码
const isVerificationCodeShow = ref(false);
const isVerificationCode = () =>{
  baseService.post("/script/sysscriptpushtaskdetails/checkPhoneCode",{
    partnerId: state.dataForm.partnerId,
    scriptUserId: state.dataForm.scriptUserId
  }).then(res=>{
    if(res.code == 0){
      isVerificationCodeShow.value = res.data.whetherAppearCode
      VerificationInfo.value = res.data
    }
  })
}

// 验证码弹窗
const SendVerificationCodeRef = ref();
const VerificationInfo = ref();
const verificationCodeChange = () =>{
  SendVerificationCodeRef.value.init(VerificationInfo.value);
}

// ---------------------- 组件调佣
// 更新推送数据
const pushGoodsHandle = async () => {
  await nextTick();
  pushGoodsRef.value.init(state.dataForm.scriptUserId, state.dataForm.partnerId, state.dataForm.gameId);
  isVerificationCode();
};
// 自动上架
const automaticShelvingSettingsRef = ref();
const automatic = () => {
  automaticShelvingSettingsRef.value.init(1);
};
// IP设置
const setIPHandle = async (id: number | string) => {
  await nextTick();
  let data = dataObj.curAllPartnerList.find((ele) => ele.id == state.dataForm.partnerId);
  IPSetRef.value.init(data);
};

// 店铺设置
const storeSettingsFn = async () => {
  await nextTick();
  let data = dataObj.curAllPartnerList.find((ele) => ele.id == state.dataForm.partnerId);
  storeSettingsRef.value.init(data);
};

const getAllPartnerList = () => {
  baseService.get("/script/sysscriptpartnerinfo/allList").then((res) => {
    if (res.code == 0) {
      dataObj.curAllPartnerList = res.data || [];
      let data = dataObj.curAllPartnerList.find((ele) => ele.id == state.dataForm.partnerId);
      dataObj.partnerName = data.name;
    }
  });
};

onMounted(() => {});
onUnmounted(() => {});
defineExpose({ changeDataForm });
</script>
<style lang="scss" scoped>
.default-main {
  border: 1px solid #ebeef5;
  .cardTop {
    padding: 12px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f7f8fa;
  }
}
.accountTabsscript {
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
}
.setUptable {
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.el-tag {
  border: 1px solid;
}
.primaryDisabled {
  background-color: var(--el-button-disabled-bg-color);
  border-color: var(--el-button-disabled-bg-color);
}
.el-button:focus-visible {
  outline: none !important;
}
:deep(.el-dropdown-menu__item) {
  justify-content: center;
}
.collapseSty {
  border: none;
  :deep(.el-collapse-item__arrow) {
    display: none;
  }
  .el-collapse-item,
  :deep(.el-collapse-item__header) {
    height: fit-content;
  }
  .el-collapse-item {
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;

    :deep(.el-collapse-item__content) {
      border: 1px solid var(--el-border-color-lighter);
      border-top: 0;
      border-bottom: 0;
    }
    :deep(.el-collapse-item__header) {
      border: none;
    }
  }
  .collapseStyTitle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--color-primary-light);
    > div {
      padding: 13px;
      display: flex;
      align-items: center;
    }
  }
  :deep(.el-result) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-right: 24px;
    padding: 0;
    .el-result__icon {
      display: none;
    }
    .el-result__title {
      margin-top: 0;
      p {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
      }
    }

    .el-result__subtitle {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #303133;
      margin-top: 2px;
    }
  }
}
</style>
