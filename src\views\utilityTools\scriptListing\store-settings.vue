<template>
  <el-drawer v-model="visible" size="944" class="infoDrawerConfig" @close="close">
    <template #header>
      <div class="drawer_title">店铺设置</div>
    </template>
    <div class="storePage">
      <div class="storeCard">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-table :data="tableData" border show-overflow-tooltip style="width: 100%; margin-top: 16px">
          <el-table-column prop="name" label="店铺名称" align="center">
            <template #default="{ row }">
              <el-input v-model="row.name" placeholder="请输入店铺名称" clearable v-if="row.isEdit" />
              <span v-else>{{ row.name }}</span>
            </template>
          </el-table-column>
          <template v-if="isCookie">
            <el-table-column prop="other" :label="info.name == '螃蟹' ? 'token' : 'cookie'" align="center">
              <template #default="{ row }">
                <el-input v-model="row.other" placeholder="请输入cookie" clearable v-if="row.isEdit" />
                <span v-else>{{ row.other }}</span>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="userAccount" label="登录账号" align="center">
              <template #default="{ row }">
                <el-input v-model="row.userAccount" placeholder="请输入登录账号" clearable v-if="row.isEdit" />
                <span v-else>{{ row.userAccount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="userPassword" label="登录密码" align="center">
              <template #default="{ row }">
                <el-input v-model="row.userPassword" placeholder="请输入登录密码" clearable v-if="row.isEdit" />
                <div class="flx-justify-around" v-else>
                  <span>{{ row.isShowGamePassword ? row.userPassword : "******" }}</span>
                  <el-icon class="pointer" @click="row.isShowGamePassword = !row.isShowGamePassword">
                    <View v-if="!row.isShowGamePassword" />
                    <Hide v-if="row.isShowGamePassword" />
                  </el-icon>
                </div>
              </template>
            </el-table-column>
          </template>

          <el-table-column fixed="right" label="操作" align="center" width="210">
            <template #default="{ row, $index }">
              <template v-if="!row.isEdit">
                <el-button type="primary" size="small" link @click="row.isEdit = true">编辑</el-button>
                <el-button type="primary" size="small" link @click="pushConfiguratioHandle(row)">推送配置</el-button>
                <el-button type="primary" size="small" link @click="copyInfo(row)">复制</el-button>
              </template>
              <el-button type="primary" :loading="submitLoading" size="small" link @click="sysscriptuser(row)" v-else>保存</el-button>
              <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定删除该店铺吗？" @confirm="deleteFn(row, $index)">
                <template #reference>
                  <el-button type="danger" size="small" link>删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </div>
    </div>
  </el-drawer>
  <!-- 推送配置 -->
  <pushConfiguration ref="pushConfigurationRef"></pushConfiguration>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import pushConfiguration from "./push-configuration.vue";

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐
const info = ref(); // 合作商信息
const pushConfigurationRef = ref();

const tableData = ref(<any>[]);
const total = ref(0);

// 表格请求参数
const requestParams = ref({
  page: 1,
  limit: 10
});

// 请求列表数据
const getList = () => {
  baseService.get("/script/sysscriptuser/page", { scriptPartnerId: info.value.id }).then((res) => {
    tableData.value = res.data.list;
    total.value = res.data.total;
  });
};

// 新增表格
const addTable = () => {
  tableData.value.push({
    name: "",
    userAccount: "",
    userPassword: "",
    other: "",
    isEdit: true
  });
};

// 保存表格
const submitLoading = ref(false);
const sysscriptuser = (row: any) => {
  submitLoading.value = true;
  (!row.id ? baseService.post : baseService.put)("/script/sysscriptuser", {
    id: row.id,
    name: row.name,
    userAccount: row.userAccount,
    userPassword: row.userPassword,
    other: row.other,
    scriptPartnerId: info.value.id
  })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success({
          message: "保存成功",
          duration: 500
        });
        getList();
      }
    })
    .finally(() => {
      row.isEdit = false;
      submitLoading.value = false;
    });
};

// 复制
const copyInfo = (row: any) => {
  baseService.get("/script/sysscriptuser/copy", { id: row.id }).then((res) => {
    if (res.code == 0) {
      ElMessage.success({
        message: "复制成功",
        duration: 500
      });
      getList();
    }
  });
};

// 删除表格
const deleteFn = (row: any, index: any) => {
  if (!row.id) {
    tableData.value.splice(index, 1);
    ElMessage.success({
      message: "删除成功",
      duration: 500
    });
    return;
  }
  baseService.delete("/script/sysscriptuser", [row.id]).then((res) => {
    if (res.code == 0) {
      ElMessage.success({
        message: "删除成功",
        duration: 500
      });
      tableData.value.splice(index, 1);
      //   getList();
    }
  });
};

// 推送配置
const pushConfiguratioHandle = async (row: any) => {
  await nextTick();
  pushConfigurationRef.value.init(info.value, row.id, row.name);
};

// 关闭回调
const close = () => {
  emit("refreshDataList");
};

// 表单初始化
const isCookie = ref(false);
const scriptList = ["转转", "闲鱼"];
const init = (data: any) => {
  visible.value = true;
  info.value = data;
  isCookie.value = data ? scriptList.includes(data.name) : false;
  tableData.value = [];
  getList();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.storePage {
  padding: 12px;
  .storeCard {
    background: #ffffff;
    border-radius: 4px;
    padding: 12px;
  }
}
</style>
