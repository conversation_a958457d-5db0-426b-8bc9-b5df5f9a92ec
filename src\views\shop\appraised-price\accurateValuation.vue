<template>
  <div style="overflow-x: scroll">
    <div class="flx accurateValuation" style="gap: 12px">
      <div style="width: 400px">
        <div class="stepTitle">
          <span class="stepBtn">第一步</span>
          <span>选择估价游戏</span>
        </div>
        <div class="selectGame">
          <el-select clearable v-model="dataForm.gameId" placeholder="请选择游戏" @change="changeGame">
            <el-option v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </div>
        <div class="cardPart cardDescriptions">
          <div class="stepTitle">快速获取属性</div>
          <template v-if="dataForm.gameName == '王者荣耀'">
            <el-descriptions :column="1" border class="descriptions" style="margin: 12px 0">
              <el-descriptions-item>
                <template #label><span>营地号</span></template>
                <el-form-item label="营地号">
                  <el-input v-model="campsiteForm.campsiteId" placeholder="营地号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item v-if="gameRoleList.length">
                <template #label><span>选择角色</span></template>
                <el-form-item>
                  <template #label>
                    选择角色
                    <el-text type="warning" style="margin-left: 12px"
                      ><el-icon><InfoFilled /></el-icon>先加载营地数据，再选择角色</el-text
                    >
                  </template>
                  <el-select v-model="currentRoleId" placeholder="选择角色" :disabled="gameRoleList.length == 0" clearable @change="campsiteChange">
                    <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-button type="primary" :disabled="!campsiteForm.campsiteId" :loading="campsiteButLoad" @click="loadCampsite()">加载营地数据</el-button>
                <el-button type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" @click="getWzryAttributesList()">自动获取</el-button>
              </el-col>
              <el-col style="margin-top: 6px" v-if="unrecognizedList && unrecognizedList.length">
                <el-button type="info" @click="showFailureAttribute">查看识别失败属性</el-button>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <!-- 扫码授权 -->
            <el-descriptions :column="1" border class="descriptions" style="margin: 12px 0; width: 100%" v-if="campsiteForm.gameName != '王者荣耀'">
              <el-descriptions-item>
                <template #label><span>账号角色</span></template>
                <el-form-item label="账号角色">
                  <el-select style="width: 100%" v-model="currentRoleId" clearable :disabled="!gameRoleList || gameRoleList.length == 0" placeholder="账号角色" @clear="roleClear">
                    <el-option v-for="(item, index) in gameRoleList" :key="index" :label="item.roleName" :value="item.roleId" />
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <scan-code-auth :code="dataForm.gameCode" :gtype="dataForm.gameType" :unrecognizedList="unrecognizedList" :gameRoleList="gameRoleList" :buttonPermissions="buttonPermissions" @confirm="getRoleInfo">
              <template #button><el-button type="primary" :disabled="!currentRoleId" :loading="campsiteButLoad" v-if="gameRoleList && gameRoleList.length && campsiteForm.gameName != '王者荣耀'" @click="getGameRoleAttributes()">自动获取</el-button></template>
            </scan-code-auth>
          </template>
        </div>
        <div class="cardPart">
          <div class="stepTitle">估价网站</div>
          <el-input type="textarea" :value="link" disabled class="copy-input-width"></el-input>
          <div class="flx-align-center pb-8">
            <el-button type="primary" v-copy="link" plain>复制</el-button>
            <el-button @click="jump">跳转</el-button>
          </div>
        </div>
        <div class="cardPart" v-if="dataForm.gameName == '王者荣耀'">
          <div class="stepTitle">估价报告</div>
          <el-input type="textarea" :value="link2" disabled class="copy-input-width"></el-input>
          <div class="flx-align-center pb-8">
            <el-button type="primary" v-copy="link2" plain>复制</el-button>
            <el-button @click="jump2">跳转</el-button>
          </div>
        </div>
      </div>
      <div class="Attribute">
        <div class="flx-justify-between" style="width: 100%">
          <div class="stepTitle">
            <span class="stepBtn">第二步</span>
            <span>勾选属性估价</span>
          </div>
          <!-- 勾选了就出现重置， 否则另一个 -->
          <el-button style="width: 60px" size="small" @click="resetCheck" v-if="checkSelected()">重置</el-button>
          <el-button
            size="small"
            type="primary"
            plain
            @click="
              dialogForm.visible = true;
              dataForm.appraisedContent = '';
            "
            v-else
            >一键解析</el-button
          >
        </div>
        <div v-loading="requestLoading" style="width: 100%; min-height: 200px">
          <div class="container">
            <div class="menu_left">
              <div class="menu_li" :class="{ menu_active: index == menuIndex }" v-for="(item, index) in dataForm.attributesList" :key="index" @click="scrollTo(index)">{{ item.name }}</div>
            </div>
            <div class="menu_right">
              <template v-if="dataForm.attributesList.length > 0">
                <div class="menu_card" ref="sectionRefs" v-for="(item, index) in dataForm.attributesList" :key="index">
                  <div class="flx-justify-between" style="margin-bottom: 9px; font-size: 12px">
                    <div class="menu_title">{{ item.name }}</div>
                    <div>
                      属性数量=<span style="color: var(--el-color-primary); margin-right: 12px">{{ item.type == 2 && item.attributeIds ? item.attributeIds.length : item.type == 1 && item.attributeIds ? 1 : 0 }}</span> 总价=<span style="color: var(--el-color-primary)">{{
                        handleAttributePrice(index)
                      }}</span>
                    </div>
                  </div>
                  <el-table v-if="item.children" :cell-style="{ fontSize: '14px' }" size="small" :data="item.children.slice((item.currentPage - 1) * 5, 5 * item.currentPage)" border style="width: 100%">
                    <el-table-column prop="name" label="属性" align="center" />
                    <el-table-column prop="num" label="数量" align="center">
                      <template #header>
                        <div style="display: flex; align-items: center; gap: 4px">
                          <el-checkbox v-if="item.type == 2 && !requestLoading" v-model="item.checkAll" :indeterminate="item.attributeIds.length > 0 && !item.checkAll" @change="handleCheckAllChange(index, item.checkAll)"> </el-checkbox><span>数量</span>
                        </div>
                      </template>
                      <template #default="scope">
                        <el-checkbox @click="changeValue(scope.row.attributeId, item.attributeIds, index)" :model-value="getCheckStatus(scope.row.attributeId, item.attributeIds)" label="" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="price" label="总价" align="center">
                      <template #default="scope">
                        {{ item.type == 2 && item.attributeIds && item.attributeIds.length > 1 && scope.row.multiPrice ? scope.row.multiPrice : scope.row.singlePrice }}
                      </template>
                    </el-table-column>
                    <!-- 空状态 -->
                    <template #empty>
                      <div style="padding: 68px 0">
                        <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                      </div>
                    </template>
                  </el-table>
                  <div>
                    <el-pagination hide-on-single-page style="width: fit-content; margin: auto; margin-top: 8px !important" v-model:current-page="item.currentPage" :page-size="5" layout="total, prev, pager, next" :total="item.children ? item.children.length : 0" />
                  </div>
                </div>
              </template>
              <div style="padding: 68px 0; margin-left: 20%" v-else>
                <img style="width: 170px" src="@/components/ny-table/src/components/noResult.png" alt="" />
              </div>
            </div>
          </div>
        </div>
        <div class="valuateBtn">
          <el-button type="primary" style="width: 208px; height: 40px" @click="handleSubmit()">立即估价</el-button>
        </div>
      </div>
      <div class="right-price">
        <div class="stepTitle">
          <span class="stepBtn">第三步</span>
          <span>查看估价结果</span>
        </div>
        <div class="appraised-price flx-column flx-center">
          <div class="price-result">
            <div class="price-wrap flx-align-end flx-justify-center">
              <span class="symbol">￥</span>
              <span class="price">{{ valuation ? valuation.split(".")[0] : 0 }}.</span>
              <span class="price2">{{ valuation ? valuation.split(".")[1] : "00" }}</span>
            </div>
          </div>
          <canvas ref="canvas" class="fireworks-canvas"></canvas>
        </div>
        <template v-if="valuationFinish">
          <div class="text">估价时间：{{ valuationFinishTime }}</div>
          <!-- 估价后显示 -->
          <el-button plain type="primary" style="margin: auto" @click="congigSethandle">保存为扫号规则</el-button>
        </template>
      </div>

      <el-dialog v-model="dialogForm.visible" width="500" title="属性一键解析">
        <el-input v-model="dataForm.appraisedContent" type="textarea" placeholder="请粘贴回收信息，进行一键估价" :rows="10"></el-input>
        <template #footer>
          <div class="dialog-footer">
            <el-button
              @click="
                dialogForm.visible = false;
                dataForm.appraisedContent = '';
              "
              >取消</el-button
            >
            <el-button type="primary" @click="analysisFn"> 一键解析 </el-button>
          </div>
        </template>
      </el-dialog>
      <div class="loadingPage" v-if="priceLoading">
        <div class="loaderBar"></div>
        <div class="loaderText">
          <span v-for="(char, index) in ['估', '价', '中', '.', '.', '.']" :key="index" :style="{ animationDelay: `${index * 0.3}s` }" class="jumping-text">
            {{ char }}
          </span>
        </div>
      </div>
      <congigSet ref="congigSetRef" />
      <!-- 属性识别结果 -->
      <attribute-recognition-result ref="attributeRecognitionResultRef" @confirm="autoChecktAttributes" @showFailureAttributeEmit="showFailureAttribute"></attribute-recognition-result>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted, ref, reactive, nextTick, provide, computed } from "vue";
import baseService from "@/service/baseService";
import { useRouter } from "vue-router";
import { useSettingStore } from "@/store/setting";
import { IObject } from "@/types/interface";
import { generateUUID, districtServiceConversionName } from "@/utils/utils";
import ScanCodeAuth from "../components/ScanCodeAuth.vue";
import AttributeRecognitionResult from "../components/AttributeRecognitionResult.vue";
import congigSet from "@/views/utilityTools/autoScan/congigSet.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
provide(
  "propGamrList",
  computed(() => gameList.value)
);
const settingStore = useSettingStore();
const dataForm = ref({
  // 游戏id
  gameId: "",
  // 一键解析内容
  appraisedContent: "",
  // 估算价格
  price: 0,
  // === new
  attributesList: <any>[]
});
const dialogForm = reactive({
  visible: false
});

// **********************  初始化页面数据 **********************
// 获取游戏下拉
const gameList = ref(<IObject>[]);
const getgameList = () => {
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    gameList.value = (res.data || []).map((ele: any) => {
      return {
        ...ele,
        gameId: ele.id,
        gameName: ele.title
      };
    });
    if (gameList.value.length > 0) {
      dataForm.value.gameId = res.data[0].id;
      changeGame();
    }
  });
};
getgameList();

// 选择游戏
const changeGame = () => {
  currentRoleId.value = "";
  gameRoleList.value = [];
  let curGame = gameList.value.find((ele) => ele.id == dataForm.value.gameId);
  dataForm.value.gameName = curGame.title;
  dataForm.value.gameCode = curGame.gcode;
  getAttribute();
};
// **********************  锚点 **********************
// 瞄点菜单
const menuIndex = ref(0);
const sectionRefs = ref([]);
// 滚动到指定区域
const scrollTo = async (index: any) => {
  menuIndex.value = index;
  await nextTick(); // 等待DOM更新
  sectionRefs.value[index]?.scrollIntoView({
    behavior: "smooth",
    block: "start"
  });
};

// **********************  外链 估价网站 **********************
const link = `${settingStore.info.websiteUrl}recycleView?ny=${generateUUID()}`;
const link2 = `${settingStore.info.mobileWebsiteUrl}#/pages/appraisedPrice/WZRY/index`;
// 跳转估价网站
const jump = () => {
  window.open(link, "_blank");
};
// 跳转估价报告
const jump2 = () => {
  window.open(link2, "_blank");
};

// **********************  一键解析 **********************
const analysisFn = (type?: string) => {
  const strToArr = dataForm.value.appraisedContent.split("\n");
  const infoArr = arrayToKeyValuePairs(strToArr);

  if (typeof infoArr == "string") {
    return;
  }
  infoArr.forEach((element: any) => {
    dataForm.value.attributesList.forEach((item: any) => {
      if (item.name == element.key) {
        if (item.type == 1) {
          // 单选
          const ids = nameQueryId(element.value, item.children);
          item.attributeIds = ids.join("");
        }
        if (item.type == 2) {
          // 多选
          const ids = nameQueryId(element.value, item.children);
          item.attributeIds = ids;
        }
        if (item.type == 3) {
          // 文本
          item.attributeText = element.value;
        }
      }
    });
  });
  if (type) ElMessage.success("解析成功！");
  dialogForm.visible = false;
};

// 文本内容转换成数组
const arrayToKeyValuePairs = (strArray: any) => {
  try {
    let arr: { key: any; value: any }[] = [];
    strArray.forEach((item: any) => {
      const [key, value] = item.split("：");
      if (value == undefined) {
        throw "解析失败，商品简介文本内容结构错误。请检查后重新解析；";
      }
      arr.push({ key: key, value: value });
    });
    return arr;
  } catch (err: any) {
    ElMessage.warning(err);
    return err;
  }
};

// **********************  勾选属性回调 **********************
// 当前属性是否被勾选 表格勾选状态 return
const getCheckStatus = (item: any, list: any) => {
  return list.includes(item);
};
// 全选处理
const handleCheckAllChange = (index: any, check: any) => {
  if (check) {
    dataForm.value.attributesList[index].attributeIds = dataForm.value.attributesList[index].children.map((ele) => ele.attributeId);
  } else {
    dataForm.value.attributesList[index].attributeIds = [];
  }
};
// 单点处理
const changeValue = (item: any, list: any, index: any) => {
  if (dataForm.value.attributesList[index].type == 2) {
    if (getCheckStatus(item, list)) {
      dataForm.value.attributesList[index].attributeIds = dataForm.value.attributesList[index].attributeIds.filter((ele) => ele != item);
    } else {
      dataForm.value.attributesList[index].attributeIds.push(item);
    }
    if (dataForm.value.attributesList[index].attributeIds.length == dataForm.value.attributesList[index].children.length) {
      dataForm.value.attributesList[index].checkAll = true;
    } else {
      dataForm.value.attributesList[index].checkAll = false;
    }
  } else if (dataForm.value.attributesList[index].type == 1) {
    dataForm.value.attributesList[index].attributeIds = item;
  }
  checkSelected();
};
// 重置
const resetCheck = () => {
  dataForm.value.attributesList.forEach((ele) => {
    ele.attributeIds = ele.type == 2 ? [] : "";
    ele.checkAll = false;
  });
};
// 是否有勾选的内容 return
const checkSelected = () => {
  return dataForm.value.attributesList.some((ele) => ele.attributeIds && ele.attributeIds.length > 0);
};
//  总价处理
const handleAttributePrice = (index: any) => {
  // 当前数组
  let allArr = dataForm.value.attributesList[index].children;
  let checkData = dataForm.value.attributesList[index].attributeIds;
  if (!checkData) {
    return 0.0;
  }
  if (dataForm.value.attributesList[index].type == 1) {
    // 單選
    let obj = allArr.find((ele) => ele.attributeId == checkData);
    return obj.singlePrice || 0;
  } else if (checkData.length == 1) {
    // 多選且單屬性
    let obj = allArr.find((ele) => ele.attributeId == checkData[0]);
    return obj.singlePrice || 0;
  } else {
    // 多選且多屬性
    let checkArr = allArr.filter((ele) => checkData.indexOf(ele.attributeId) > -1);
    let total = 0;
    checkArr.forEach((ele) => {
      // 若沒有設置多屬性價格就用單屬性的
      total = +total + (ele.multiPrice ? +ele.multiPrice : +ele.singlePrice);
    });
    return total.toFixed(2);
  }
};
// **********************  快速获取属性  **********************
const requestLoading = ref(false);
// 获取游戏属性信息
const getAttribute = () => {
  requestLoading.value = true;
  gameUserInfo.value = {};
  baseService
    .post("/appraise/attribute/listTree", { gameId: dataForm.value.gameId })
    .then((res) => {
      if (res.data.length == 0) {
        ElMessage({
          type: "warning",
          message: "没有查询到当前游戏属性信息请编辑后在来新增商品！"
        });
      } else {
        dataForm.value.attributesList = [];
        res.data.map((item: any) => {
          //  不匹配的不显示 文本不显示
          if (item.type == 3 || item.id == null) return;
          dataForm.value.attributesList.push({
            typeId: item.attributeId,
            priceId: item.id,
            attributeIds: item.type == 2 ? [] : "",
            attributeText: "",
            children: item.children,
            type: item.type,
            name: item.name,
            currentPage: 1,
            checkAll: false
          });
        });
        menuIndex.value = 0;
        nextTick(); // 等待DOM更新
        sectionRefs.value[0]?.scrollIntoView({
          behavior: "smooth",
          block: "start"
        });
        requestLoading.value = false;
        resetCheck();
      }
    })
    .catch((err) => {
      requestLoading.value = false;
    });
};
// 授权按钮根据游戏 去显示
const buttonPermissions = {
  DZPD: ["qq", "wx"],
  CYHX: ["qq"],
  CYHXSY: ["qq", "wx"],
  YXLM: ["qq"],
  YXLMSY: ["qq", "wx"],
  JCCZZ: ["qq", "wx"],
  HPJY: ["qq", "wx"],
  QQFC: ["qq"],
  QQFCSY: ["qq", "wx"],
  WWQY: ["qq", "wx"],
  HYRZ: ["qq", "wx"],
  YS: [],
  NZ: ["qq"]
};
const campsiteForm = reactive({
  gameName: "",
  campsiteId: "",
  roleId: "",
  roleList: <any>[]
});
// 扫码完成之后查询属性
const attributeLoading = ref(false);
// 查询到的 用户信息
const gameUserInfo = ref(<any>{});
// 当前选中的角色
const currentRoleId = ref("");
// 获取角色信息
const gameRoleList = ref([]);
const gameCodeStr = ref("");
const qrcodeAuthType = ref("qq");
const getRoleInfo = async (info: any, gameCode: string, authType: string) => {
  qrcodeAuthType.value = authType;
  unrecognizedList.value = [];

  gameUserInfo.value = info;
  gameCodeStr.value = gameCode;

  // 无畏契约 没有角色列表
  if (gameCode == "WWQY") {
    // console.log(info,'===== 无畏契约扫码回调 ========');
    const roleIndex = dataForm.value.attributesList.findIndex((obj: any) => obj.typeId == "2025032209");
    if (roleIndex < 0) {
      dataForm.value.attributesList.push({
        typeId: "2025032209",
        attributeText: info.openId
      });
    } else {
      dataForm.value.attributesList[roleIndex].attributeText = info.openId;
    }

    return getGameRoleAttributes();
  }

  let res: any = await baseService.post("/autoCheck/autoCheck/getRole", {
    ...gameUserInfo.value,
    gameCode: gameCodeStr.value
  });

  if (res.code == 0) {
    if (!res.data || !res.data.length) {
      attributeLoading.value = false;
      return;
    }
    gameRoleList.value = res.data.map((item: any) => {
      return {
        ...item,
        areaName: districtServiceConversionName(dataForm.gameCode, item.areaName, qrcodeAuthType.value)
      };
    });
    console.log(gameRoleList.value);

    currentRoleId.value = gameRoleList.value[0].roleId;
  }
};
// 选择角色
const campsiteChange = (value: any) => {
  // 角色ID位置
  const roleIndex = dataForm.value.attributesList.findIndex((obj: any) => obj.typeId == "2024112316");
  // 营地ID位置
  const campIndex = dataForm.value.attributesList.findIndex((obj: any) => obj.typeId == "2024112610");
  if (roleIndex < 0) {
    // // 营地角色ID
    // dataForm.value.attributesList.push({
    //   typeId: "2024112316",
    //   attributeText: value
    // });
    // // 营地ID
    // dataForm.value.attributesList.push({
    //   typeId: "2024112610",
    //   attributeText: campsiteForm.campsiteId
    // });
  } else {
    dataForm.value.attributesList[roleIndex].attributeText = value;
    dataForm.value.attributesList[campIndex].attributeText = campsiteForm.campsiteId;
  }
};
// 清空账号角色回调
const roleClear = () => {
  gameRoleList.value = [];
};
// 未识别属性列表
const unrecognizedList = ref(<any>[]);
// 已识别属性列表
const recognizedList = ref(<any>[]);

// 根据角色获取 游戏资产属性 自动勾选
const getGameRoleAttributes = async () => {
  attributeLoading.value = true;
  let currentRole: any = "";
  if (currentRoleId.value) currentRole = gameRoleList.value.find((item: any) => item.roleId == currentRoleId.value);

  try {
    let res: any = await baseService.post("/autoCheck/autoCheck/getAttr", {
      gameCode: gameCodeStr.value,
      roleIdList: [currentRoleId.value],
      ...gameUserInfo.value,
      ...currentRole
    });

    if (res.code == 0) {
      attributeLoading.value = false;
      unrecognizedList.value = [];
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      let cfpcSpecialWeapon = res.data.cfpcSpecialWeapon ? res.data.cfpcSpecialWeapon : [];
      recognizedList.value = [...attrList, ...cfpcSpecialWeapon];
      autoChecktAttributes();
    }
  } catch (error) {
    attributeLoading.value = false;
  }
};
// 扫码成功 自动勾选商品属性
const autoChecktAttributes = (attributeList?: any) => {
  let arr = attributeList ? [...attributeList, ...recognizedList.value] : recognizedList.value;
  let list = arr.join(",");

  dataForm.value.attributesList.map((item: any) => {
    if (item.type == 1) {
      // 单选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids.join("");
    }
    if (item.type == 2) {
      // 多选
      const ids = nameQueryId(list, item.children);
      item.attributeIds = ids;
    }
    console.log(item.attributeIds);
  });
};
// 属性名称查询属性ID
const nameQueryId = (value: string, data: any[]) => {
  const valToArr = value.split(/,|，/);
  const ids: any = [];
  valToArr.forEach((name: string) => {
    data.forEach((item: any) => {
      if (item.name == name.trim()) {
        ids.push(item.attributeId);
      }
    });
  });
  return ids;
};
// 加载营地数据
const campsiteButLoad = ref(false);
const loadCampsite = (roleId?: string) => {
  campsiteButLoad.value = true;
  currentRoleId.value = "";
  baseService
    .post("/shop/shop/roleByCampsiteId", { campsiteId: campsiteForm.campsiteId })
    .then((res) => {
      if (res.code == 0) {
        gameRoleList.value = res.data.map((item: any) => {
          return {
            ...item,
            areaName: districtServiceConversionName(dataForm.gameCode, item.areaName)
          };
        });
      }
    })
    .finally(() => {
      campsiteButLoad.value = false;
    });
};
// 王者荣耀游戏数据
const getWzryAttributesList = () => {
  attributeLoading.value = true;
  campsiteButLoad.value = true;
  baseService
    .get("/autoCheck/autoCheck/wzry/getWzryAttrByCampsiteId", {
      campsiteId: campsiteForm.campsiteId,
      roleId: currentRoleId.value
    })
    .then((res: any) => {
      unrecognizedList.value = [];
      attributeLoading.value = false;
      campsiteButLoad.value = false;
      if (res.data.noMappingAttrList && res.data.noMappingAttrList.length) {
        unrecognizedList.value = res.data.noMappingAttrList;
      }

      let attrList = res.data.attrList.map((item) => item.name);
      recognizedList.value = attrList;
      autoChecktAttributes();
    })
    .finally(() => {
      attributeLoading.value = false;
      campsiteButLoad.value = false;
    });
};
// 属性识别失败
const attributeRecognitionResultRef = ref();
const showFailureAttribute = () => {
  attributeRecognitionResultRef.value.init(unrecognizedList.value);
};

// **********************  保存规则配置： 回显属性  **********************
const congigSetRef = ref();
const congigSethandle = () => {
  let attributesTexts = <any>[];
  let attributesTextIds = <any>[];
  let totalAttributeText = <any>[];
  dataForm.value.attributesList.forEach((ele: any) => {
    if (ele.type == 1 && ele.attributeIds && ele.attributeIds.length > 0) {
      attributesTextIds.push(ele.attributeIds);
      ele.children.forEach((cEle: any) => {
        if (cEle.attributeId == ele.attributeIds) {
          attributesTexts.push(cEle.name);
        }
      });
      totalAttributeText.push(ele.name + "*1");
    } else if (ele.type == 2 && ele.attributeIds && ele.attributeIds.length > 0) {
      attributesTextIds.push(...ele.attributeIds);
      ele.children.forEach((cEle: any) => {
        if (ele.attributeIds.indexOf(cEle.attributeId) > -1) {
          attributesTexts.push(cEle.name);
        }
      });
      totalAttributeText.push(ele.name + "*" + ele.attributeIds.length);
    }
  });
  let params = {
    gameId: dataForm.value.gameId,
    gameName: "",
    configId: null,
    attributesTexts: attributesTexts,
    attributesTextIds: attributesTextIds,
    totalAttributeText: totalAttributeText.join("+"),
    maxPrice: valuation.value
  };
  nextTick(() => {
    congigSetRef.value.initPrice(params);
  });
};

// ********************** 提交估价 **********************
// 估價加載
const priceLoading = ref(false);
// 估价
const valuationFinish = ref(false);
const valuationFinishTime = ref();
const valuation = ref("");
const handleSubmit = () => {
  let params = <any>[];
  dataForm.value.attributesList.forEach((ele: any) => {
    if (ele.type == 1 && ele.attributeIds && ele.attributeIds.length > 0) {
      let obj = ele.children.find((cEle: any) => cEle.attributeId == ele.attributeIds);
      params.push({
        pid: ele.priceId,
        children: [obj.id]
      });
    } else if (ele.type == 2 && ele.attributeIds && ele.attributeIds.length > 0) {
      let objArr = ele.children.filter((cEle: any) => ele.attributeIds.indexOf(cEle.attributeId) > -1);
      objArr = objArr.map((oEle: any) => oEle.id);
      params.push({
        pid: ele.priceId,
        children: objArr
      });
    }
    // else if (ele.type == 3 && ele.attributeText && ele.attributeText.length > 0) {
    //   params.push(ele.priceId);
    // }
  });
  if (params.length == 0) {
    ElMessage.warning("请选择属性！");
    return;
  }
  ElMessageBox.confirm("是否立即估价？", "估价确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    priceLoading.value = true;
    valuationFinish.value = false;
    valuationFinishTime.value = "";
    console.log(params);
    baseService.post("/appraise/newFormula/attrAppraise", { appraiseAttributeIds: params, gameId: dataForm.value.gameId }).then((res) => {
      console.log(res);
      if (res.code == 0) {
        setTimeout(() => {
          ElMessage.success("估价成功！请查看估价结果");
          valuation.value = res.data.total.toFixed(2);
          valuationFinish.value = true;
          valuationFinishTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
          priceLoading.value = false;
          handleFirework();
        }, 2000);
      }
    });
  });
};

//  ********************** 烟花特效 **********************
const canvas = ref(null);
let ctx = null;
let fireworks = [];
let animationFrameId = null;

// 烟花粒子类
class Particle {
  constructor(x, y) {
    this.x = x;
    this.y = y;
    this.angle = Math.random() * Math.PI * 50;
    this.speed = Math.random() * 20 + 2;
    this.vx = Math.cos(this.angle) * this.speed;
    this.vy = Math.sin(this.angle) * this.speed;
    this.radius = Math.random() * 12 + 1;
    this.color = `hsl(${Math.random() * 360}, 100%, 50%)`;
    this.alpha = 1;
  }

  update() {
    this.vy += 0.08;
    this.x += this.vx;
    this.y += this.vy;
    this.alpha -= 0.01;
  }

  draw() {
    ctx.save();
    ctx.globalAlpha = this.alpha;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
    ctx.fillStyle = this.color;
    ctx.fill();
    ctx.restore();
  }
}

// 烟花类
class Firework {
  constructor(x, y) {
    this.particles = [];
    for (let i = 0; i < 100; i++) {
      this.particles.push(new Particle(x, y));
    }
  }

  update() {
    this.particles.forEach((particle, index) => {
      particle.update();
      if (particle.alpha <= 0) {
        this.particles.splice(index, 1);
      }
    });
  }

  draw() {
    this.particles.forEach((particle) => particle.draw());
  }
}

// 设置canvas大小
const resizeCanvas = () => {
  if (canvas.value) {
    canvas.value.width = window.innerWidth;
    canvas.value.height = window.innerHeight;
  }
};

// 动画循环
const animate = () => {
  if (!ctx) return;
  // 清除画布时使用透明色
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
  // ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
  //     ctx.fillRect(0, 0, canvas.value.width, canvas.value.height);
  fireworks.forEach((firework, index) => {
    firework.update();
    firework.draw();
    if (firework.particles.length === 0) {
      fireworks.splice(index, 1);
    }
  });

  animationFrameId = requestAnimationFrame(animate);
};

// 点击触发烟花
const handleFirework = () => {
  fireworks.push(new Firework(300, 300));
  setTimeout(() => {
    fireworks.push(new Firework(500, 500));
    fireworks.push(new Firework(1500, 200));
  }, 200);
  setTimeout(() => {
    fireworks.push(new Firework(1500, 400));
  }, 100);
};

onMounted(() => {
  ctx = canvas.value.getContext("2d");
  resizeCanvas();
  animate();
  window.addEventListener("resize", resizeCanvas);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeCanvas);
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
});
</script>
<style lang="scss" scoped>
.copy-input-width {
  margin-bottom: 8px;
  margin-top: 12px;
}
.right-price {
  width: 400px;

  .appraised-price {
    width: 100%;
    height: 240px;
    background-image: url("@/assets/images/gj.png");
    background-size: 100% 100%;
    position: relative;
    .price-result {
      .price-wrap {
        display: flex;
        align-items: flex-end;
        color: var(---, #462905);
        text-align: center;
        font-family: OPPOSans;
        font-size: 36px;
        line-height: 36px;
        font-style: normal;
        font-weight: 700;

        .price {
          font-size: 56px;
          line-height: 60px;
          height: 56px;
        }
      }
    }
  }

  .text {
    margin: auto;
  }
}
.Attribute {
  width: 761px;

  position: relative;

  .valuateBtn {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 64px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    background: #fff;
    justify-content: center;
    z-index: 99;
    border-radius: 0px 0px 3px 3px;
    box-shadow: 0px 1px 0px 0px #e5e6e8 inset;
  }
}
.accurateValuation {
  position: relative;
  width: fit-content;
  > div {
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e5e6eb;
    background: #f7f8fa;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    height: fit-content;
    max-height: calc(100vh - 240px);
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .stepTitle {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      height: 24px;
      line-height: 24px;
      font-style: normal;
      font-weight: 700;
      color: #303133;

      .stepBtn {
        border-radius: 2px;
        background: var(--el-color-primary);
        padding: 0 8px;
        color: #fff;
      }
    }
  }

  .selectGame {
    height: 56px;
    width: 100%;

    .el-select,
    :deep(.el-select__wrapper) {
      height: 56px;
      line-height: 56px;
    }
  }

  .cardPart {
    width: 100%;
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
    background: linear-gradient(253deg, rgba(255, 255, 255, 0) 27.8%, #fff 64.2%), var(--Fill-Blank, #fff);
  }
}
.container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  .menu_left {
    width: 160px;
    border-top: 1px solid #ebeef5;
    border-radius: 4px;
    padding-bottom: 70px;
    max-height: calc(100vh - 300px);
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
    .menu_li {
      background: #fff;
      border: 1px solid #ebeef5;
      border-top: 0;
      border-bottom: 0;
      padding: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #303133;
      cursor: pointer;
      &:last-child {
        border-bottom: 1px solid #ebeef5;
      }
    }
    .menu_active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }
  .menu_right {
    // border: 1px solid red;
    flex: 1;
    max-height: calc(100vh - 300px);
    padding-bottom: 70px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: auto;
    .menu_card {
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      background: #fff;
      padding: 12px;
      .menu_title {
        font-size: 12px;
        font-weight: 700;
        line-height: 12px;
      }
      .menu_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .name {
          color: #606266;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
        .operate {
        }
      }
      .menu_center {
        :deep(.el-checkbox__label) {
          width: 160px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
/* From Uiverse.io by dylanharriscameron */
.accurateValuation .loadingPage {
  width: calc(100% - 286px);
  max-height: calc(100% - 100px);
  height: calc(100% - 100px);
  background-color: rgba(255, 255, 255, 1);
  position: fixed;
  z-index: 999;
  right: 16px;
  top: 86px;
}
.loaderText {
  position: absolute;
  overflow: hidden;
  margin: auto;
  top: 42%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;

  .jumping-text {
    display: inline-block;
    animation: jumping 1.6s infinite ease-in-out;
    margin-left: 2px;
  }

  @keyframes jumping {
    0%,
    100% {
      transform: translateY(0) rotate(0deg); /* 初始状态 */
    }
    25% {
      transform: translateY(-5px) rotate(-5deg); /* 向上跳动并倾斜 */
    }
    50% {
      transform: translateY(0) rotate(0deg); /* 回到初始状态 */
    }
    75% {
      transform: translateY(-5px) rotate(5deg); /* 向上跳动并倾斜 */
    }
  }
}
.loaderBar {
  width: 400px;
  height: 20px;
  background: #f9f9f9;
  border-radius: 10px;
  border: 1px solid #006dfe;
  position: absolute;
  overflow: hidden;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loaderBar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 5px;
  background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
  animation: fillProgress 6s ease-in-out infinite, lightEffect 1s infinite linear;
  animation-fill-mode: forwards;
}
.fireworks-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  pointer-events: auto;
}

@keyframes fillProgress {
  0% {
    width: 0;
  }

  33% {
    width: 33.333%;
  }

  66% {
    width: 66.67%;
  }

  100% {
    width: 100%;
  }
}

@keyframes lightEffect {
  0%,
  20%,
  40%,
  60%,
  80%,
  100% {
    background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px, rgba(255, 255, 255, 0.3) 0 40px) right/200% 100%;
  }
}
</style>
