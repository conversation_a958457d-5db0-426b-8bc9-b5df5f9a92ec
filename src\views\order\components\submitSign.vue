<template>
  <operation-log ref="operationRef" :show="show" :showFooter="false" @close="show = false" title="确认签署" type="primary">
    <template #default>
      <div>
        <span>确认签署后合同将锁定！</span>
        <div style="display: flex; justify-content: flex-end">
          <el-button @click="show = false">取消</el-button>
          <el-button v-loading="loading" @click="creatUrl" type="primary">确认签署</el-button>
        </div>
      </div>
    </template>
  </operation-log>
</template>
  
  <script lang="ts" setup>
import { ref, defineExpose } from "vue";
import OperationLog from "./SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
const emit = defineEmits(["refreshdata"]);
const propObj = ref();
const show = ref(false);
const loading = ref(false);
const init = (obj: any) => {
  show.value = true;
  propObj.value = obj;
  propObj.value.bestsignContractId = obj.bestsignContractId;
  propObj.value.templateId = undefined;
};
const creatUrl = () => {
  loading.value = true;
  baseService
    .get("/bestsign/confirmContract", {
      bestsignContractId: propObj.value.bestsignContractId
    })
    .then((res: any) => {
      loading.value = false;
      ElMessage.success("签署成功！");
      emit("refreshdata");
      show.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

defineExpose({
  init
});
</script>