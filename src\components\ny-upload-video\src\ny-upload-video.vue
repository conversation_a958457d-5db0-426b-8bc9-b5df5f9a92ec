<template>
    <el-upload class="upload-demo" action="#" accept=".mp4" drag multiple :auto-upload="false" :show-file-list="true"
      v-model:file-list="fileList" :on-change="handleChange" :on-remove="handleRemove">
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        拖拽文件到这里或者<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
            请上传
            <template v-if="props.fileSize">
                大小不超过 <b style="color: #f56c6c">{{ props.fileSize }}MB</b>
            </template>
            <!-- <template v-if="props.fileType">
                格式为 <b style="color: #f56c6c">{{ props.fileType.join("，") }}</b>
            </template> -->
            的文件
        </div>
      </template>
    </el-upload>
</template>
<script lang="ts">
import baseService from '@/service/baseService';
import { ref, reactive, computed, defineComponent, watch } from 'vue';
import {ElMessage, UploadProps, UploadUserFile} from 'element-plus'
export default defineComponent({
    name: 'NyUploadVideo',
    props: ({
        // 大小限制(MB)
        fileSize: {
            type: Number,
            default: 20,
        },
        // 文件类型
        fileType: {
            type: Array,
            default: () => ['video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb', 'video/mov'],
        },
    }),
    setup(props) { 
        return {
            props
        }
    }
})
</script>
<style>
</style>