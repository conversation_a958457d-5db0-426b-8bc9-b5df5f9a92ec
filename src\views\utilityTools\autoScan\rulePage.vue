<template>
  <div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <el-button type="primary" @click="addOrUpdateHandle()">规则配置</el-button>
        <el-button type="warning" @click="handleSearch">立即执行</el-button>
      </template>
      <template #header-right>
        <el-input :prefix-icon="Search" style="width: 220px; margin-left: 12px" v-model="view.dataForm.configName" placeholder="请输入规则名称"></el-input>
        <el-date-picker
          v-model="timeInterval"
          @change="
            () => {
              view.dataForm.start = timeInterval ? timeInterval[0] + ' 00:00:00' : '';
              view.dataForm.end = timeInterval ? timeInterval[1] + ' 23:59:59' : '';
            }
          "
          type="daterange"
          range-separator="到"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          unlink-panels
          style="width: 220px; margin-left: 12px"
        />
        <el-button type="primary" @click="queryFn">查询</el-button>
        <el-button @click="resetFn">重置</el-button>
      </template>
      <template #minPrice="{ row }">
        <span style="color: #f56c6c">{{ row.minPrice }}~{{ row.maxPrice }}</span>
      </template>
      <template #takeEffect="{ row }"> <el-switch @click="editData(row)" v-model="row.takeEffect" :active-value="1" :inactive-value="0" /> </template>
      <template #createDate="{ row }">
        {{ formatDate(row.createDate, undefined) }}
      </template>
      <template #operate="{ row }">
        <el-button @click="addOrUpdateHandle(row)" link type="primary">编辑</el-button>
        <el-button @click="delRule(row)" link type="danger">删除</el-button>
      </template>
    </ny-table>
    <congigSet ref="addOrUpdateRef" @refreshDataList="state.getDataList()" />
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { Search } from "@element-plus/icons-vue";
import { formatDate, formatCurrency } from "@/utils/method";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import congigSet from "./congigSet.vue";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import baseService from "@/service/baseService";
const props = defineProps({
  propsData: <any>{}
});
const timeInterval = ref([]);
const view = reactive({
  // createdIsNeed: false,
  getDataListURL: "/scan/autoScanConfig/page",
  getDataListIsPage: true,
  deleteURL: "/scan/autoScanConfig",
  deleteIsBatch: false,
  dataForm: {
    configName: "",
    start: "",
    end: "",
    gameId: "",
    partnerCode: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
watch(
  () => props.propsData,
  () => {
    if (props.propsData.partnerCode) {
      view.dataForm.gameId = props.propsData.gameId;
      view.dataForm.partnerCode = props.propsData.partnerCode;
      state.getDataList();
    }
  },
  { deep: true, immediate: true }
);
const columns = reactive([
  {
    prop: "configName",
    label: "规则名称"
  },
  {
    prop: "gameName",
    label: "游戏名称"
  },
  {
    prop: "serverName",
    label: "游戏区服"
  },
  {
    prop: "minPrice",
    label: "价格区间(元)"
  },
  {
    prop: "createDate",
    label: "创建时间"
  },
  {
    prop: "takeEffect",
    label: "是否开启"
  },
  {
    prop: "operate",
    label: "操作"
  }
]);
// 查询
const queryFn = () => {
  state.getDataList();
};
// 重置
const resetFn = () => {
  state.dataForm.start = "";
  state.dataForm.end = "";
  state.dataForm.configName = "";
  timeInterval.value = [];
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (row?: any) => {
  nextTick(() => {
    addOrUpdateRef.value.init({ ...props.propsData, ...row, configId: row?.id || null });
  });
};
const editData = (row: any) => {
  baseService.put("/scan/autoScanConfig", row).then((res) => {
    ElMessage.success("编辑成功！");
    state.getDataList();
  });
};
const delRule = (row: any) => {
  ElMessageBox.confirm("确认删除【" + row.configName + "】规则?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.delete("/scan/autoScanConfig", { id: row.id }).then((res) => {
      ElMessage.success("删除成功！");
      state.getDataList();
    });
  });
};
const handleSearch = async () => {
  ElMessage.success("开始执行...");
  await baseService.get("/scan/autoScanLog/task").then((res) => {
    if (res.data) {
      ElNotification({
        title: "执行结束",
        message: "自动扫描执行结束，请在执行日志中查询结果！",
        type: "success"
      });
    } else {
      ElNotification({
        title: "任务执行中",
        message: "自动扫描任务执行时间较久，请稍后在执行日志中查询结果！",
        type: "success"
      });
    }
  });
};
</script>

<style lang="less" scoped></style>
