<template>
  <el-dialog v-model="dialogVisible" title="分期设置" width="480">
    <div class="cardDescriptions" style="padding: 0px">
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
        <el-descriptions :column="1" border class="descriptions">
          <!-- <el-descriptions-item>
            <template #label>
              <span>首付款比例(%)<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="downPaymentRatio">
              <el-input-number v-model="dataForm.downPaymentRatio" placeholder="请输入首付款比例" :min="1" :max="100" style="width: 100%" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>最低分期金额(元)<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="minStagesMoney">
              <el-input v-model="dataForm.minStagesMoney" style="width: 100%" placeholder="请输入最低分期金额" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>分期费率设置<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="rateList">
              <div class="rateList">
                <div v-for="(item, index) in dataForm.rateList" :key="index" class="rateListItem">
                  <span>{{ index == 0 ? "1个月内" : index == 1 ? "3个月" : "4~6个月" }} </span>
                  <el-input v-model="item.rate" style="flex: 1" @input="rateInput(item.rate, index)" placeholder="请输入">
                    <template #append>%</template>
                  </el-input>
                </div>
              </div>
            </el-form-item>
          </el-descriptions-item> -->
          <el-descriptions-item>
            <template #label>
              <span>逾期日利率(%)<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="overdueDayRate">
              <el-input-number v-model="dataForm.overdueDayRate" placeholder="请输入逾期日利率" :precision="2" :step="0.01" :min="0" :max="1" style="width: 100%" />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
const dialogVisible = ref(false);
const dataForm = ref(<any>{
  overdueDayRate: 0.05,
  minStagesMoney: 1000,
  rateList: [
    { startMonth: 1, endMonth: 1, rate: "" },
    { startMonth: 2, endMonth: 3, rate: "" },
    { startMonth: 4, endMonth: 6, rate: "" }
  ]
});
const rules = reactive({
  downPaymentRatio: [{ required: true, message: "请输入首付款比例", trigger: "blur" }],
  minStagesMoney: [{ required: true, message: "请输入最低分期金额", trigger: "blur" }],
  overdueDayRate: [{ required: true, message: "请输入逾期日利率", trigger: "blur" }]
});

const rateInput = (value: string | number, index: number) => {
  if (Number(value) < 0) {
    dataForm.value.rateList[index].rate = 0;
  } else if (Number(value) > 100) {
    dataForm.value.rateList[index].rate = 100;
  }
};

// 提交
const formRef = ref();
const submit = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;

    // try {
    //   dataForm.value.rateList.forEach((item: any) => {
    //     if (item.rate == "" || item.rate == null) {
    //       throw new Error("");
    //     }
    //   });
      (!dataForm.value.id ? baseService.post : baseService.put)("/sale/saleStagesConfig", dataForm.value).then((res) => {
        if(res.code == 0){
          ElMessage.success("保存成功");
          dialogVisible.value = false;
        }
      });
    // } catch (e) {
    //   ElMessage.warning("请完成分期费率设置");
    // }
  });
};

const init = () => {
  dialogVisible.value = true;
  baseService.get("/sale/saleStagesConfig/getConfig").then((res) => {
    Object.assign(dataForm.value, res.data);
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.rateList {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
  .rateListItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    span {
      flex: 1;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      color: #303133;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
