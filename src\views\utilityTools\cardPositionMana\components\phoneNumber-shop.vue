<template>
  <el-drawer @close="visible = false" :footer="null" v-model="visible" size="944" class="infoDrawer">
    <template #header>
      <div class="drawer_title">手机号绑定商品数量</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="basicInfoSty">
        <div class="title">基本信息</div>
        <el-descriptions :column="2" border class="descriptions descriptions-label-140">
          <el-descriptions-item label-class-name="title">
            <template #label> <div>手机号码</div> </template>
            {{ phoneShopInfo.phone }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>绑定数量</div> </template>
            {{ phoneShopInfo.total || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>腾讯系</div> </template>
            QQ{{ phoneShopInfo.qq || '0' }}、微信{{ phoneShopInfo.wx || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>网易系</div> </template>
            {{ phoneShopInfo.wy || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>米哈游系</div> </template>
            {{ phoneShopInfo.mhy || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>steam系</div> </template>
            {{ phoneShopInfo.steam || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>世纪天成</div> </template>
            {{ phoneShopInfo.sjtc || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>其他</div> </template>
            {{ phoneShopInfo.qt || '0' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty" style="min-height: 73vh">
        <div class="title" style="display: flex; align-items: center; justify-content: space-between">
          <span>绑定商品数量</span>
          <div v-show="state.showOperation">
            <el-button style="height: 20px" @click="changeBtn(false)">取消</el-button>
            <el-button style="height: 20px" @click="handleSave" plain type="primary">保存</el-button>
          </div>
          <!-- <div v-show="!state.showOperation">
            <el-button style="height: 20px" @click="changeBtn(true)" plain type="primary">纠偏</el-button>
          </div> -->
        </div>
        <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane :label="`腾讯系 ${phoneShopInfo.qq + phoneShopInfo.wx}`" name="1"></el-tab-pane>
          <el-tab-pane :label="`网易系 ${phoneShopInfo.wy || '0'}`" name="2"></el-tab-pane>
          <el-tab-pane :label="`米哈游系 ${phoneShopInfo.mhy || '0'}`" name="3"></el-tab-pane>
          <el-tab-pane :label="`steam系 ${phoneShopInfo.steam || '0'}`" name="4"></el-tab-pane>
          <el-tab-pane :label="`世纪天成 ${phoneShopInfo.sjtc || '0'}`" name="5"></el-tab-pane>
          <el-tab-pane :label="`其他 ${phoneShopInfo.qt || '0'}`" name="6"></el-tab-pane>
        </el-tabs>
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px">
          <div>
            <ny-button-group :list="systemTbs" v-model="systemTbsValue" @change="systemHandleClick" v-show="activeName == '1'"></ny-button-group>
          </div>
          <el-form :inline="true">
            <el-form-item style="margin-bottom: 0px">
              <el-input style="width: 280px !important" v-model="gameCode" placeholder="请输入商品编码" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item style="margin-bottom: 0px">
              <el-select style="width: 160px" clearable v-model="gameName" placeholder="请选择游戏">
                <el-option v-for="item in gamesList" :key="item.id" :label="item.title" :value="item.title" />
              </el-select
            ></el-form-item>
            <el-button type="primary" @click="getQuery">查询</el-button>
            <el-button class="ml-8" @click="getResetting">重置</el-button>
          </el-form>
        </div>
        <div class="setUptable">
          <el-table border ref="tableRef" :data="paginatedData" style="width: 100%">
            <el-table-column show-overflow-tooltip prop="code" label="商品编码" align="center" />
            <el-table-column show-overflow-tooltip prop="gameName" label="游戏名称" align="center" />
            <el-table-column width="360" show-overflow-tooltip prop="shopTitle" label="商品信息" align="center">
              <template #default="{ row }">
                <div class="shoping">
                  <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
                  <div class="info">
                    <div class="gametitle mle" v-html="row.title" @click="jumpGoodsDetails(row)"></div>
                    <div class="sle">
                      {{ `${row.gameName} / ${row.serverName}` }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="商品状态" align="center">
              <template #default="scope">
                <template v-if="scope.row.status">
                  <el-tag :type="shopStatus[+scope.row.status].type">{{ shopStatus[+scope.row.status].name }}</el-tag>
                </template>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column v-if="state.showOperation" width="140" prop="type" label="操作" align="center">
              <template #default="scope">
                <!-- 状态 -->
                <el-link :underline="false" style="margin-right: 12px" @click="editHandle(scope.row)" type="primary">更换</el-link>
                <el-link :underline="false" @click="delData(scope.row.phoneGameId)" type="danger">删除</el-link>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
          <el-pagination 
            :current-page="pagination.page" 
            :page-sizes="[10, 20, 50, 100, 500, 1000]" 
            :page-size="pagination.size" :total="total" 
            layout="total, sizes, prev, pager, next, jumper" 
            :hide-on-single-page="true" 
            @size-change="sizeChange" 
            @current-change="currentChange"
          ></el-pagination>
        </div>
      </div>
    </el-scrollbar>
    <changeBind @refreshDataList="refreshdata" ref="changeBindRef"></changeBind>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
  </el-drawer>
</template>

<script lang="ts" setup>
import { Edit, Search } from "@element-plus/icons-vue";
import { nextTick, reactive, ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { TableInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import ShopInfo from "@/views/shop/shop-info.vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { usePagination } from "@/views/dataAnalysis/pagination";
// 组件引入
import changeBind from "@/views/basicInfo/phoneMana/change-bind-goods.vue";
const changeBindRef = ref();

const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const tableRef = ref<TableInstance>();
const visible = ref(false); // 对话框显隐
const shopStatus = ref([
  {
    name: "",
    type: "info"
  },
  {
    name: "待上架",
    type: "warning"
  },
  {
    name: "已上架",
    type: "primary"
  },
  {
    name: "已下架",
    type: "danger"
  },
  {
    name: "已出售",
    type: "success"
  },
  {
    name: "问题账号",
    type: "warning"
  },
  {
    name: "作废账号",
    type: "danger"
  },
  {
    name: "交易中",
    type: "primary"
  }
]);

const state = reactive({
  showOperation: false
});

const systemTbs = ref([
  { dictLabel: "QQ", dictValue: "0" },
  { dictLabel: "微信", dictValue: "1" }
]);
const systemTbsValue = ref("0");
const operatorsGameList = ref([]);
const activeName = ref("1");
// 表单初始化
const init = async (phone: any) => {
  visible.value = true;
  getShopCount(phone);
  getGamesList();
};

const tableData = ref(<any>[]);
const total = ref();
// 分页
const pagination = ref({
  page: 1,
  size: 10
});

// 计算分页数据
const paginatedData = computed(() =>
  usePagination({
    currentPage: pagination.value.page,
    pageSize: pagination.value.size,
    data: tableData.value
  })
);

// 获取数据信息
const phoneShopInfo = ref(<any>{});
const requestLoading = ref(false); // 详情加载
const getShopCount = (phone: any) => {
  requestLoading.value = true;
  baseService.get("/mobile/mobileCard/getShopCount/" + phone).then((res) => {
    if (res.code == 0) {
      phoneShopInfo.value = res.data;
      tableData.value = res.data.qqShopList;
      total.value = tableData.value.length
      systemTbs.value[0].dictLabel = `QQ${res.data.qq || '0'}`;
      systemTbs.value[1].dictLabel = `微信${res.data.wx || '0'}`;
    }
  }).finally(()=>{
    requestLoading.value = false;
  })
};

// 分页方法
const sizeChange = (number: any) => {
  pagination.value.size = number;
  pagination.value.page = 1;
};

const currentChange = (number: any) => {
  pagination.value.page = number;
};

// QQ、微信切换
const systemHandleClick = () => {
  if (systemTbsValue.value == "0") {
    tableData.value = phoneShopInfo.value.qqShopList;
  } else {
    tableData.value = phoneShopInfo.value.wxShopList;
  }
  total.value = tableData.value.length
};

// 游戏厂商切换
const handleClick = (e: any) => {
  switch (e.props.name) {
    case "1":
      tableData.value = phoneShopInfo.value.qqShopList;
      systemTbsValue.value = '0';
      break;
    case "2":
      tableData.value = phoneShopInfo.value.wyShopList;
      break;
    case "3":
      tableData.value = phoneShopInfo.value.mhyShopList;
      break;
    case "4":
      tableData.value = phoneShopInfo.value.steamShopList;
      break;
    case "5":
      tableData.value = phoneShopInfo.value.sjtcShopList;
      break;
    case "6":
      tableData.value = phoneShopInfo.value.qtShopList;
      break;
  }
  total.value = tableData.value.length
};

const gameCode = ref("");
const gameName = ref("");

// 查询
const getQuery = () =>{
  var list = <any>[];
  switch (activeName.value) {
    case "1":
      list = systemTbsValue.value == '0' ? phoneShopInfo.value.qqShopList : phoneShopInfo.value.wxShopList;
      break;
    case "2":
      list = phoneShopInfo.value.wyShopList;
      break;
    case "3":
      list = phoneShopInfo.value.mhyShopList;
      break;
    case "4":
      list = phoneShopInfo.value.steamShopList;
      break;
    case "5":
      list = phoneShopInfo.value.sjtcShopList;
      break;
    case "6":
      list = phoneShopInfo.value.qtShopList;
      break;
  }

  tableData.value = list.filter((item:any)=>{
    return (
      (!gameCode.value ||item.code.includes(gameCode.value)) && 
      (!gameName.value || item.gameName == gameName.value)
    )
  })
  total.value = tableData.value.length
}

// 重置
const getResetting = () => {
  gameCode.value = "";
  gameName.value = "";
  switch (activeName.value) {
    case "1":
      tableData.value = systemTbsValue.value == '0' ? phoneShopInfo.value.qqShopList : phoneShopInfo.value.wxShopList
      break;
    case "2":
      tableData.value = phoneShopInfo.value.wyShopList;
      break;
    case "3":
      tableData.value = phoneShopInfo.value.mhyShopList;
      break;
    case "4":
      tableData.value = phoneShopInfo.value.steamShopList;
      break;
    case "5":
      tableData.value = phoneShopInfo.value.sjtcShopList;
      break;
    case "6":
      tableData.value = phoneShopInfo.value.qtShopList;
      break;
  }
};

// 纠偏保存
const handleSave = () => {
  ElMessage.success("保存成功！");
  state.showOperation = false;
};
const refreshdata = () => {
  emit("refreshDataList");
  getShopCount(phoneShopInfo.value.phone);
};

const delData = (id: any) => {
  ElMessageBox.confirm(t("prompt.info", { handle: t("delete") }), t("prompt.title"), {
    confirmButtonText: t("confirm"),
    cancelButtonText: t("cancel"),
    type: "warning"
  }).then(() => {
    baseService.delete("/phone/sysphonegame", [id]).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          refreshdata();
        }
      });
    });
  });
};

// 更换
const editHandle = async (id: any) => {
  await nextTick();
  changeBindRef.value.init(id);
};
// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.id);
  if (res.data) {
    shopInfoKey.value++;
    await nextTick();
    shopInfoRef.value.init(res?.data || {});
  }
};
const changeBtn = (show: boolean) => {
  state.showOperation = show;
};

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
  });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 340px;

  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .gametitle {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
.button-group {
  width: fit-content;
  background: #f2f2f2;
  border-radius: 6px;
  margin-bottom: 12px;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
.setUptable {
  :deep(.el-table) {
    overflow: visible !important;
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .title {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }

  :deep(.el-tabs--card>.el-tabs__header .el-tabs__item){
    background-color: #F7F8FA;
  }
  :deep(.el-tabs--card>.el-tabs__header .el-tabs__item:first-child){
    border-radius: 4px 0 0 0;
  }
  :deep(.el-tabs--card>.el-tabs__header .el-tabs__item:last-child){
    border-radius: 0 4px 0 0;
  }
  :deep(.el-tabs--card>.el-tabs__header .el-tabs__item.is-active){
    background-color: #FFF;
  }
}
</style>
<style lang="scss">
.infoDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
