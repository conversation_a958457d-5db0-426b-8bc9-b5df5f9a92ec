<template>
  <div class="card_page">
    <div class="router_info">
      <span class="info_name" v-if="currentTypeIndex == 0">插件功能</span>
      <span
        class="info_name"
        style="cursor: pointer"
        v-else
        @click="
          currentTypeIndex = 0;
          tabsTypeChange();
        "
        ><el-icon color="#303133" size="18" style="margin-right: 8px"><Back /></el-icon>已开通功能</span
      >
      <span class="info_line">|</span>
      <span class="info_blurb">恒星开放平台，让账号交易更安全</span>
    </div>
    <template v-if="currentTypeIndex == 0">
      <div class="page_header">
        <div class="titile">智能商品管理功能</div>
        <div class="introduce">助力商家实现账号资源的高效变现，推动商家业务的持续增长</div>
        <div class="buts">
          <div class="fun_tabs">
            <div class="fun_tabs_item" :class="{ active: view.dataForm.label == item.value }" v-for="(item, index) in tabsList" :key="index" @click="tabsChange(item)">{{ item.label }}</div>
          </div>
          <el-input v-model="state.dataForm.name" class="input" placeholder="搜索功能" @input="state.getDataList()" :prefix-icon="Search" />
        </div>
        <div
          class="alreadyActivated"
          @click="
            currentTypeIndex = 1;
            tabsTypeChange();
          "
        >
          已开通功能<el-icon color="var(--el-color-primary)" size="14" style="margin-left: 8px"><ArrowRight /></el-icon>
        </div>
      </div>

      <div class="card-list">
        <div class="card-item" v-for="(item, index) in state.dataList" :key="index">
          <div body-class="card-item-content" class="card-content" @click="onEditAction(item.id, 'open')">
            <div class="image-wrap flx-center">
              <el-image v-if="item.paidFunctionOptions.coverImg" class="image" :src="item.paidFunctionOptions.coverImg" fit="cover" />
            </div>
            <div class="info">
              <div class="flx-align-center title-wrap method-tag">
                <div class="title">{{ item.name }}</div>
                <span class="method" v-if="isConfigureTrial(item)">试用</span>
                <span class="method-1" v-else-if="item.paidFunctionOptions.chargingMethod == 1">按次</span>
                <span class="method-2" v-else-if="item.paidFunctionOptions.chargingMethod == 2">包月</span>
                <span class="method-3" v-else-if="item.paidFunctionOptions.chargingMethod == 3">永久</span>
              </div>
              <div class="introduce sle">{{ item.introduce }}</div>
              <div class="flx-between mt-10">
                <div class="flx-align-end price-wrap" v-if="item.paidFunctionOptions.price == 0">
                  <span class="price">免费</span>
                </div>
                <div class="flx-align-end price-wrap" v-else>
                  <span class="price">{{ isConfigureTrial(item) ? "免费" : item.paidFunctionOptions.price }}</span>
                  <span class="symbol" v-if="!isConfigureTrial(item)">星币</span>
                  <span class="after" v-if="!isConfigureTrial(item) && item.paidFunctionOptions.chargingMethod == 1">/次</span>
                  <span class="after" v-if="!isConfigureTrial(item) && item.paidFunctionOptions.chargingMethod == 2">/月</span>
                </div>
                <div class="open-btn" @click.stop="onEditAction(item.id, 'open')">立即开通</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ny-no-data type="3" v-if="!state.dataList || !state.dataList.length" />
      <div style="display: flex; justify-content: center; padding-bottom: 10px">
        <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout=" prev, pager, next" :hide-on-single-page="true" background @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle">
        </el-pagination>
      </div>
    </template>
    <div class="TableXScrollSty" v-if="currentTypeIndex == 1">
      <div class="pb-20">
        <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
          <template #header>
            <ny-button-group :list="chargingMethodList" v-model="state.dataForm.chargingMethod" @change="stateChange"></ny-button-group>
          </template>
          <template #header-right>
            <el-input v-model="state.dataForm.name" placeholder="功能名称" clearable>
              <template #prefix>
                <el-icon class="el-input__icon">
                  <search />
                </el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
          </template>

          <template #type="{ row }">
            <span>全平台</span>
          </template>

          <template #haveBeenUsed="{ row }">
            <template v-if="row.optionType == 0">
              <el-link @click="viewUseDetail(row)" type="primary" v-if="row!.trialType == '0' "> {{ row.total - row.remainCount }}/{{ row.total || 0 }}{{ "次" }}</el-link>
              <span v-else> {{ row.usage || 0 }}/{{ row.total || 0 }}月</span>
            </template>
            <el-link @click="viewUseDetail(row)" type="primary" v-if="row.optionType == 1">{{ row.total - row.remainCount }}/{{ row.total || 0 }}{{ "次" }}</el-link>
            <span v-if="row.optionType == 2">{{ row.usage || 0 }}/{{ row.total || 0 }}月</span>
            <span v-if="row.optionType == 3">永久 </span>
          </template>

          <template #payAmount="{ row }">
            <span v-if="row.optionType == 0">免费</span>
            <span v-else>{{ row.payAmount || "-" }}</span>
          </template>

          <template #optionType="{ row }">
            <el-tag v-if="row.chargingMethod == 1" color="#CDF0FF" style="color: #00a5ef">按次</el-tag>
            <el-tag v-if="row.chargingMethod == 2" type="primary">包月</el-tag>
            <el-tag v-if="row.chargingMethod == 3" color="#DDDCFF" style="color: #5654ff">永久</el-tag>
          </template>

          <template #state="{ row }">
            <template v-if="row.optionType == 0">
              <el-tag v-if="row.trialType == 0 && row.total - row.remainCount >= row.total" type="warning">已用完</el-tag>
              <el-tag v-else-if="row.trialType == 1 && isExpired(row.expireTime)" type="warning">已到期</el-tag>
              <el-tag v-else color="#DAFFE6" style="color: #00c568">试用中</el-tag>
            </template>
            <el-tag v-else-if="row.optionType == 1 && row.total - row.remainCount >= row.total" type="warning">已用完</el-tag>
            <el-tag v-else-if="row.optionType == 2 && isExpired(row.expireTime)" type="danger">已过期</el-tag>
            <el-tag v-else type="primary">使用中</el-tag>
          </template>

          <template #operation="{ row }">
            <el-button type="primary" bg text @click="onEditAction(row.functionId, 'renewal')">续费</el-button>
          </template>

          <template #footer>
            <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
              <span style="font-weight: bold; color: #1d2129">开通金额</span>
              <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
              <span>合计={{ getSummaries() }}</span>
            </div>
          </template>
        </ny-table>
      </div>
    </div>
  </div>

  <!-- 开通 -->
  <opened :key="addKey" ref="openedRef" @refreshDataList="state.query"></opened>

  <!-- 详情 -->
  <detail ref="detailRef" @open="onEditAction"></detail>

  <!-- 使用明细 -->
  <paidUsed ref="usedetailRef"></paidUsed>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { computed, nextTick, reactive, ref, toRefs, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { BigNumber } from "bignumber.js";
import dayjs from "dayjs";
import Opened from "./paidfunction-opened.vue";
import Detail from "./paidfunction-detail.vue";
import paidUsed from "./paidfunction-used.vue";

const store = useAppStore();

const view = reactive({
  getDataListURL: "/paid/function/page",
  getDataListIsPage: true,
  limit: 12,
  dataForm: {
    platform: import.meta.env.VITE_PLATFORM_MODE,
    name: "",
    type: "",
    optionType: "",
    chargingMethod: "",
    label: ""
  },
  createdIsNeed: true
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const tabsList = reactive([
  { label: "全部功能", value: "" },
  { label: "系统功能", value: 1 },
  { label: "自动获取", value: 2 },
  { label: "一键截图", value: 3 }
  // { label: "自动上号", value: 4 }
]);

const currentTypeIndex = ref(0);
const tabsTypeChange = () => {
  state.dataList = [];
  state.dataForm.name = "";
  state.dataForm.label = "";
  state.dataForm.optionType = "";
  state.dataForm.chargingMethod = "";
  // console.log(currentTypeIndex.value);

  // 已开通功能列表
  view.getDataListURL = currentTypeIndex.value == 0 ? "/paid/function/page" : "paid/function/openedFunctionList";
  // console.log(currentTypeIndex.value, view.getDataListURL);
  state.getDataList();
};

// 收费方式
const chargingMethodList = computed(() => {
  let list = getDictDataList(store.state.dicts, "paid_function_charging_method").filter((item) => item.dictValue != 0);
  return [{ dictValue: "", dictLabel: "全部" }, ...list];
});

// 选择收费方式
const stateChange = (value: any) => {
  state.dataForm.chargingMethod = value;
  state.getDataList();
};

// 重置操作
const getResetting = () => {
  state.dataForm.name = "";
  state.dataForm.type = "";
  state.dataForm.optionType = "";
  state.dataForm.chargingMethod = "";
  state.getDataList();
};

// 开通
const addKey = ref(0);
const openedRef = ref();
const onEditAction = (id: any, type?: string) => {
  addKey.value++;
  nextTick(() => {
    openedRef.value.init(id, type);
  });
};

// 查看详情
const detailRef = ref();
const viewDetail = (data: any) => {
  detailRef.value.init(data);
};
// 查看明细
const usedetailRef = ref();
const viewUseDetail = (data: any) => {
  usedetailRef.value.init(data.functionId);
};

// table columns
const columns = reactive([
  {
    label: "功能名称",
    prop: "name",
    minWidth: "190"
  },
  {
    label: "功能介绍",
    prop: "introduce",
    minWidth: "200"
  },
  // {
  //   label: "游戏范围",
  //   prop: "type",
  //   minWidth: 106
  // },
  {
    label: "开通时间",
    prop: "createDate",
    minWidth: 168
  },
  {
    label: "已使用/开通范围",
    prop: "haveBeenUsed",
    minWidth: 128
  },
  {
    label: "到期时间",
    prop: "expireTime",
    minWidth: 168
  },
  {
    label: "续费时间",
    prop: "renewTime",
    minWidth: 168
  },
  {
    label: "开通金额",
    prop: "payAmount",
    minWidth: 128
  },
  {
    label: "收费方式",
    prop: "optionType",
    minWidth: 110
  },
  {
    label: "功能状态",
    prop: "state",
    minWidth: 110
  },
  {
    label: "操作",
    prop: "operation",
    fixed: "right",
    width: 90
  }
]);

// 是否到期
const isExpired = (expireTime: any) => {
  return expireTime ? dayjs(expireTime).isBefore(dayjs().format("YYYY-MM-DD HH:mm:ss")) : false;
};

// 是否配置试用
const isConfigureTrial = (item: any) => {
  const hasTrial = (item?.paidFunctionOptionsVo?.trialType === 0 || item?.paidFunctionOptionsVo?.trialType === 1) && item.isTrial != 1 ? true : false;
  return hasTrial && item?.paidFunctionOptionsVo?.trialOption && item.remainCount === null && item.expireTime === null;

  // // 没有试用
  // if(!item?.paidFunctionOptionsVo?.trialOption) return true;
  // // 剩余试用次数
  // if(item?.paidFunctionOptionsVo?.trialOption && item?.paidFunctionOptionsVo?.trialType == 0){
  // 	return item.remainCount <= 0
  // }
  // // 试用过期时间
  // return item.expireTime ? dayjs(item.expireTime).isBefore(dayjs().format("YYYY-MM-DD HH:mm:ss")) : false;
};

// tabs切换
const tabsChange = (e: any) => {
  view.dataForm.label = e.value;
  state.getDataList();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.payAmount) total += item?.payAmount || 0;
  });
  return total.toFixed(2);
};
</script>

<style lang="less" scoped>
.router_info {
  display: flex;
  align-items: center;
  padding-top: 24px;
  padding-bottom: 15px;
  //   padding: 20px 20px 0px 20px;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.card_page {
  padding: 0 24px;
  background-color: #fff;
  height: calc(100vh - 104px);
  overflow-y: auto;
  .page_header {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .titile {
      color: #1d2129;
      font-size: 32px;
      font-weight: 600;
      line-height: normal;
    }
    .introduce {
      color: #86909c;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      margin-top: 4px;
    }
    .buts {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 12px;
      .fun_tabs {
        display: flex;
        align-items: center;
        gap: 12px;
        .fun_tabs_item {
          padding: 5px 12px;
          border-radius: 16px;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
          cursor: pointer;
        }
        .active {
          color: #fff;
          background-color: var(--el-color-primary);
        }
      }
      .input {
        width: 132px;
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px var(--el-color-primary);
          border-radius: 16px;
          background-color: var(--el-color-primary-light-9);
        }
        :deep(.el-icon) {
          color: var(--el-color-primary);
        }
        :deep(.el-input__inner) {
          height: 28px;
        }
        :deep(.el-input__inner::placeholder) {
          color: var(--el-color-primary);
        }
      }
    }
    .alreadyActivated {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 14px;
      line-height: 22px;
      font-weight: 500;
      color: var(--el-color-primary);
      border: 1px solid var(--el-color-primary);
      padding: 5px 8px 5px 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, 340px);
  justify-content: center;
  gap: 16px;
  margin: 0 auto;
  margin-top: 12px;
  padding: 0 16px;

  .card-item {
    width: 340px;
  }
  .image-wrap {
    height: 130px;
    background: #ffffff;
    border-radius: 8px 8px 0px 0px;
    overflow: hidden;
  }

  .image {
    width: 100%;
    height: 100%;
  }

  :deep(.card-item-content) {
    padding: 0;
  }

  .card-content {
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    border: 1px solid #e4e7ed;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0px 12px 42px 0px rgba(38, 38, 38, 0.24);
    }

    .info {
      padding: 12px;
      .introduce {
        font-size: 12px;
        color: #86909c;
        line-height: 16px;
        margin-top: 4px;
      }
    }

    .title-wrap {
      .title {
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
        margin-right: 4px;
      }
    }

    .price-wrap {
      color: #f44a29;

      .symbol {
        font-size: 12px;
      }

      .price {
        font-size: 24px;
        line-height: 20px;
        font-weight: bold;
      }

      .after {
        font-size: 12px;
        color: #606266;
        margin-left: 3px;
      }
    }

    .open-btn {
      background: var(--el-color-primary-light-9);
      border-radius: 4px;
      color: var(--el-color-primary);
      width: 104px;
      line-height: 32px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

.method-tag {
  span {
    display: flex;
    height: 22px;
    line-height: 1;
    align-items: center;
    padding: 0 4px;
    border-radius: 2px;
    margin-right: 4px;
    font-size: 14px;
  }

  .method {
    background: #daffe6;
    color: #00c568;
  }

  .method-1 {
    background: #cdf0ff;
    color: #00a5ef;
  }

  .method-2 {
    background: #daeeff;
    color: #3366ff;
  }

  .method-3 {
    background: #dddcff;
    color: #481dff;
  }
}
</style>
