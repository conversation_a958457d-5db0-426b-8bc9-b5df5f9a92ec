<template>
  <div style="width: 100%">
    <div class="title">开卡人管理</div>
    <div class="TableXScrollSty">
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <div style="display: flex; gap: 8px">
            <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
            <el-button type="warning" @click="importShow = true">导入</el-button>
            <el-button type="info" @click="exportHandle()">导出</el-button>
          </div>
        </template>
        <template #header-right>
          <div style="display: flex; gap: 8px">
            <el-input :prefix-icon="Search" style="width: 320px !important" v-model="state.dataForm.info" placeholder="请输入办卡人/联系方式/微信/支付账号/备注" clearable></el-input>
            <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            <el-button @click="filterHandle">高级筛选</el-button>
          </div>
        </template>
        <template #status="{ row }">
          <el-tag type="danger" v-if="row.status == 0">已注销</el-tag>
          <el-tag type="success" v-if="row.status == 1">正常</el-tag>
          <el-tag type="warning" v-if="row.status == 2">意向注销</el-tag>
          <el-tag type="primary" v-if="row.status == 3">急需注销</el-tag>
          <el-tag type="info" v-if="row.status == 4">失联</el-tag>
        </template>
        <template #idCardFront="{ row }">
          <div class="flx" style="gap: 8px">
            <el-image style="width: 48px" v-if="row.idCardFront" :src="row.idCardFront" :preview-src-list="[row.idCardFront]" preview-teleported fit="cover" />
            <el-image style="width: 48px" v-if="row.idCardBack" :src="row.idCardBack" :preview-src-list="[row.idCardBack]" preview-teleported fit="cover" />
          </div>
        </template>
        <template #createDate="{ row }">
          <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
        </template>
        <template #contract="{ row }">
          <el-button v-if="row.contract" @click="previewContract(row)" type="primary" link>查看</el-button>
          <span v-else>-</span>
        </template>
        <template #operation="{ row }">
          <el-button type="primary" link @click="addOrUpdateHandle(row.id)">编辑</el-button>
          <el-button type="danger" link @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    </div>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importShow" title="导入文件" class="ba-upload-preview" @close="closeMoneyPreview" width="35%">
      <div>
        <p style="margin: 0 0 12px 0">
          为了保证数据导入顺利，请先
          <el-button v-blur @click="getExportMode()" plain style="margin: 0; padding: 0; color: var(--el-color-primary); border: none"> 下载导入模板 </el-button>
          ，并按照规范示例导入数据
        </p>
        <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs">
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            style="margin-right: 8px"
            @click="
              importShow = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { IObject } from "@/types/interface";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./add-update-opencard.vue";
import filterCom from "./filter.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { fileExport } from "@/utils/utils";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileCardIssuer/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileCardIssuer",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "userName",
    label: "办卡人",
    minWidth: 112
  },
  {
    prop: "cardQuantity",
    label: "办卡数",
    minWidth: 80
  },
  {
    prop: "contactInformation",
    label: "联系方式",
    minWidth: 140
  },
  {
    prop: "wx",
    label: "微信",
    minWidth: 120
  },
  {
    prop: "personInCharge",
    label: "负责人",
    minWidth: 100
  },
  {
    prop: "alipayAccount",
    label: "支付账号",
    minWidth: 140
  },
  {
    prop: "idCardFront",
    label: "身份证",
    minWidth: 140
  },
  {
    prop: "contract",
    label: "合同",
    minWidth: 80
  },
  {
    prop: "singleDivideInto",
    label: "单卡分成(%)",
    minWidth: 120
  },
  {
    prop: "sumDivideInto",
    label: "合计分成(元)",
    minWidth: 120
  },
  {
    prop: "relationship",
    label: "与负责人关系",
    minWidth: 144
  },
  {
    prop: "provideUserType",
    label: "供卡人类型",
    minWidth: 120
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 160
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: 140
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 160
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: 120
  }
]);

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm = { info: "" };
  createDate.value = [];
  filterRef.value.reset();
  state.page = 1;
  state.getDataList();
};

const getParamsInfo = (params: any) => {
  delete params.createDate;
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

// 预览合同
const previewContract = (row: any) => {
  window.open(row.contract);
};

// 导入
const importShow = ref(false);
const fileList = ref([] as IObject);

// 关闭对话框
const closeMoneyPreview = () => {
  importShow.value = false;
};
// -----------------------数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};
// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: any) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 文件上传   确认导入按钮
const uploadExcel = async (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  const url = `/mobile/mobileCardIssuer/import`;
  await baseService
    .post(url, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res: any) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
        state.getDataList();
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      fileList.value = [];
      importShow.value = false;
      state.getDataList();
    });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/mobile/mobileCardIssuer/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `开卡人管理`);
  });
};

// 下载模板
const getExportMode = () => {
  baseService
    .get("/mobile/mobileCardIssuer/exportMode")
    .then((res) => {
      if (res) {
        let blob = new Blob([res], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8"
        });
        let a = document.createElement("a");
        let url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = "开卡人管理导入模板";
        document.body.appendChild(a);
        a.style.display = "none";
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        ElMessage.error("下载失败！");
      }
    })
    .catch((err) => {
      ElMessage.error("操作失败！");
    });
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(4);
  });
};
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 503px;
}
</style>
