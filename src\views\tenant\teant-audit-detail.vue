<template>
    <el-drawer v-model="visible"  size="50%" class="ny-drawer">
        <template #header>
            <div class="drawer_title">合作商审核</div>
        </template>
        <div class="shop_page">
            <div class="drawer-card">
                <div class="p-title mt-0">身份信息</div>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140">
                    <el-descriptions-item>
                        <template #label>商家名称</template>
                        {{ resData.tenantName }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>联系人姓名</template>
                        {{ resData.realName }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>身份证号</template>
                        {{ resData.idCard }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>手机号</template>
                        {{ resData.mobile }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="drawer-card mt-12" v-if="resData.companyName">
                <div class="p-title mt-0">企业信息</div>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140">
                    <el-descriptions-item>
                        <template #label>公司名称</template>
                        {{ resData.companyName }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>统一社会信用代码</template>
                        {{ resData.creditCode }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>营业执照</template>
                        <el-image 
                            class="business-license"
                            :src="resData.businessLicense"
                            :preview-src-list="[resData.businessLicense]"
                            preview-teleport>
                        </el-image>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="drawer-card mt-12">
                <div class="p-title mt-0">申请信息</div>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140">
                    <el-descriptions-item>
                        <template #label>申请类型</template>
                        {{ resData.companyName ? '企业商家' : '个人商家' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>申请时间</template>
                        {{ resData.createDate }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>申请状态</template>
                        <el-tag v-if="resData.status === 1" type="success">已通过</el-tag>
                        <el-tag v-else-if="resData.status === 2" type="error">已拒绝</el-tag>
                        <el-tag v-else type="warning">待审核</el-tag>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="drawer-card mt-12">
                <div class="p-title mt-0">商家审核</div>
                <el-form ref="dataFormRef" :model="dataForm" :rules="dataRule" label-position="top">
                    <el-form-item label="审核操作" prop="status">
                        <el-radio-group v-model="dataForm.status" :disabled="currentType=='detail'">
                            <el-radio :label="1">通过</el-radio>
                            <el-radio :label="2">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="拒绝原因" prop="remarks" v-if="dataForm.status == 2">
                        <el-input type="textarea" :rows="3" v-model="dataForm.remarks" placeholder="请输入拒绝原因" :disabled="currentType=='detail'"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <template v-slot:footer v-if="currentType != 'detail'">
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
        </template>
    </el-drawer>

</template>

<script lang="ts" setup>
    import { ref, defineExpose, defineEmits } from 'vue';
    import { ElMessage } from 'element-plus';
    import baseService from "@/service/baseService";

    const visible = ref(false);

    const dataForm = ref(<any>{});
    const emit = defineEmits(['refreshData']);

    const dataRule = ref({
        status: [
            { required: true, message: '请选择审核操作', trigger: 'blur' }
        ],
        remarks: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
    });


    const dataFormRef = ref();
    const teantId = ref(0);
    const currentType = ref('');

    const init = (id: any, type: string) => {
        teantId.value = id;
        currentType.value = type;
        visible.value = true;
        getInfo();
    }
    const resData = ref(<any>{});
    // 获取详情
    const getInfo = () => {
        baseService.get(`/tenant/tenantregistered/${teantId.value}`).then((res: any) => {
            resData.value = res.data;

            dataForm.value = {
                status: res.data.status,
                remarks: res.data.remarks
            }
        });
    }


    // 审核提交
    const btnLoading = ref(false)
    const dataFormSubmitHandle = () => {
        dataFormRef.value.validate((valid: any) => {
            if(!valid) return;

            btnLoading.value = true;
            baseService.put("/tenant/tenantregistered", {
                ...resData.value,
                ...dataForm.value
            }).then((res: any) => {
                ElMessage.success({
                    message: '审核成功',
                    duration: 500,
                    onClose: () => {
                        visible.value = false;
                        btnLoading.value = false;
                    }
                })
                emit('refreshData');
        }).catch(() => {
                btnLoading.value = false;
            });

            visible.value = false;
        });
    }

    defineExpose({
        init
    })

</script>

<style lang="scss" scoped>
    .business-license{
        width: 96px;
        height: 96px;
        border-radius: 4px;
        background: #eee;
    }
</style>