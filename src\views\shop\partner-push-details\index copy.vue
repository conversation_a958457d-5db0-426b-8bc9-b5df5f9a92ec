<template>
  <div class="partner-push-details-wrap">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card" style="margin-bottom: 15px">
      <!-- <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab> -->
      <gameSelect :list="gamesList" v-model="view.dataForm.gameId"></gameSelect>
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <el-form-item>
          <el-input class="input-300" v-model="state.dataForm.search" placeholder="请输入商品编码/商品标题" clearable :prefix-icon="Search"></el-input>
        </el-form-item>
        <el-form-item>
            <el-select class="input-300" clearable v-model="state.dataForm.acquisitionUser" placeholder="请选择合作商" @change="state.getDataList()">
                <el-option
                    v-for="item in partnersList"
                    :key="item.userId"  
                    :label="item.tenantName"
                    :value="item.userId"
                />
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
        </el-form-item>
        <el-form-item>
          <div class="retail-price">
            <el-input-number :controls="false" placeholder="最低零售价" v-model="state.dataForm.priceStart"></el-input-number>
            <span>至</span>
            <el-input-number :controls="false" placeholder="最高零售价" v-model="state.dataForm.priceEnd"></el-input-number>
          </div>
        </el-form-item>
      </el-form>

      <el-collapse class="collapse" v-model="state.collapseVal" @change="handleStatsChange">
        <el-collapse-item name="1">
          <template #title>
            <div class="p-title">筛选属性</div>
          </template>
          <div v-for="(item, index) in propertyList" :key="index" class="propertyList flx-justify-between">
            <el-form label-suffix="：" v-if="item.type == 2" style="flex: 1">
              <el-form-item :label="item.name">
                <el-checkbox-group v-model="state.attributesList[item.id]">
                  <el-checkbox v-for="(sub, sindex) in item.children" :key="sindex" :label="sub.id">{{ sub.name }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
            <el-form label-suffix="：" v-if="item.type == 1" style="flex: 1">
              <el-form-item :label="item.name">
                <el-radio-group v-model="state.radioList[item.id]">
                  <el-radio v-for="(sub, sindex) in item.children" :key="sindex" :label="sub.id">{{ sub.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-form label-suffix="：" v-if="item.type == 3 && item.isSection == 0" style="flex: 1">
              <el-form-item :label="item.name">
                <div class="retail-price">
                  <el-input-number :controls="false" placeholder="最低" v-model="state.textlist[item.id].start"></el-input-number>
                  <span>至</span>
                  <el-input-number :controls="false" placeholder="最高" v-model="state.textlist[item.id].end"></el-input-number>
                </div>
              </el-form-item>
            </el-form>

            <div style="width: 50px">
              <span style="color: var(--el-color-primary); cursor: pointer" v-if="(item.type == 1) && state!.radioList[item.id]" @click="state!.radioList[item.id] = null">重置</span>
              <span style="color: var(--el-color-primary); cursor: pointer" v-else-if="item.type == 2 && state!.attributesList[item.id].length" @click="state!.attributesList[item.id] = []">重置</span>
              <span style="color: var(--el-color-primary); cursor: pointer" v-else-if="item.type == 3 && (state!.textlist[item.id].start || state!.textlist[item.id].end)" @click="state!.textlist[item.id] = { start: null, end: null, typeId: item.id }">重置</span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>

      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button type="primary" @click="searchHandle">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>

        <!-- 商品编码 -->
        <!-- <template #code="{ row }">
          <span v-html="row.code" class="parsing"></span>
        </template> -->

        <!-- 商户联系方式 -->
        <template #phone="{ row }">
          <span v-html="row.phone" class="parsing"></span>
        </template>

        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #title="{ row }">
          <el-button text type="primary" @click="toDetails(row)">
            <span v-html="row.title" class="parsing"></span>
          </el-button>
        </template>
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '1'">待上架</el-tag>
          <el-tag type="primary" v-if="row.status == '2'">已上架</el-tag>
          <div v-if="row.status == '3'" class="flx-column">
            <el-tag type="danger">已下架</el-tag>
            <span style="font-size: 12px">{{ `（${row.delistingRemark}）` }}</span>
          </div>
          <el-tag type="success" v-if="row.status == '4'">已出售</el-tag>
        </template>
        <template #operation="{ row }">
          <el-button type="warning" text bg @click="contactMerchants(row.creatorImUserId)">联系商户</el-button>
          <el-button type="primary" text bg v-if="row.status == '2'" @click="delistClick(row)">下架</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 查看商品详情 -->
    <info ref="infoRef"></info>
    <!-- 下架 -->
    <delist ref="delistRef" @refreshDataList="state.getDataList();getGamesList();"></delist>
  </div>
</template>

<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
import { useImStore } from "@/store/im";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";
import { useRouter } from "vue-router";
import Info from "../shop-info.vue";
import delist from "../shop-delist.vue";
import gameSelect from "./components/gameSelect.vue";
import { getConversation, getUserInfo } from "@/utils/imTool";
const store = useAppStore();
const imStore = useImStore();
const router = useRouter();
const { t } = useI18n();

const gamesList = ref(<any>[]); // 游戏列表
const infoRef = ref(); // 查看商品详情

const view = reactive({
  getDataListURL: "/shop/shop/search",
  listRequestMethod: "post",
  getDataListIsPage: true,
  createdIsNeed: false,
  getDataListIsPageSize: true,
  exportURL: "/shop/shop/export",
  deleteURL: "/shop/shop",
  deleteIsBatch: true,
  dataForm: {
    queryType: "2",
    status: '100',
    acquisitionUser: "", // 合作商id
    search: "", // 标题
    gameId: null, // 游戏id
    start: "", // 开始时间
    end: "", // 结束时间
    priceStart: null, // 最低零售价
    priceEnd: null, // 最高零售价

    attributesList: [],
    attributesTexts: []
  },
  attributesList: {},
  radioList: {},
  textlist: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "code",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "serverName",
    label: "系统区服",
    minWidth: "200"
  },
  {
    prop: "title",
    label: "商品标题",
    minWidth: "400"
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: "120",
    sortable: true
  },
  {
    prop: "originalPrice",
    label: "原价(元)",
    minWidth: "120",
    sortable: true
  },
  {
    prop: "status",
    label: "状态",
    minWidth: "100"
  },
  {
    prop: "creatorName",
    label: "商户名",
    minWidth: "100"
  },
  {
    prop: "phone",
    label: "商户联系方式",
    minWidth: "120"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "200"
  },

  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "150"
  }
]);

const collapseVal = ref("");

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    state.getDataList();
    gameAreChange();
    getAttributeList();
  }
);

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    view.dataForm.gameId = res.data ? res.data[0].id : "";
    gamesList.value = res.data;
    getAttributeList();
  });
};

// 合作商列表
const partnersList = ref([]);
const getPartnersList = () => {
  baseService.get("/sys/tenant/page", { limit: 999, page: 1 }).then((res) => {
      if (res.code == 0) {
          partnersList.value = res.data?.list || [];
      }
  });
}


const delistRef = ref(); // 下架
// 商品下架
const delistClick = (row: any, sold?: string) => {
  nextTick(() => {
    delistRef.value.init(row.id);
  });
};
// 根据游戏id 查询对应的区服
const sysgameChildrenList = ref([] as IObject);
const gameAreChange = () => {
  sysgameChildrenList.value = [];
  view.dataForm.server = "";
  const are = gamesList.value.filter((item: any) => item.id == view.dataForm.gameId)[0];
  if (are.areaList.length > 0) {
    sysgameChildrenList.value = are.areaList;
  } else {
    view.dataForm.server = "";
  }
};

// 属性列表
const propertyList = ref([]);
const getAttributeList = async () => {
  let res = await baseService.get("/game/attribute/page", { gameId: view.dataForm.gameId });
  propertyList.value = res.data;
  // attributesList: [],
  // radioList: [],
  // textlist: [],
  initPropertySearch();
};

// 初始化 属性筛选
const initPropertySearch = () => {
  state.dataForm.attributesList = [];
  state.dataForm.attributesTexts = [];
  state.attributesList = {};
  state.radioList = {};
  state.textlist = {};
  propertyList.value.map((item: any) => {
    if (item.type == 1) {
      state.radioList[item.id] = "";
    } else if (item.type == 2) {
      state.attributesList[item.id] = [];
    } else if (item.type == 3) {
      state.textlist[item.id] = { start: null, end: null, typeId: item.id };
    }
  });
};

// 选择时间
const createDate = ref([]);
const createDateChange = () => {
  view.dataForm.start = createDate.value.length ? createDate.value[0] : "";
  view.dataForm.end = createDate.value.length ? createDate.value[1] : "";
};

// 点击标题查看详情
const toDetails = (row: any) => {
  nextTick(() => {
    infoRef.value.init(row);
  });
};

// 筛选
const searchHandle = () => {
  // attributesList: [],
  // radioList: [],
  // textlist: [],
  state.dataForm.attributesList = [];
  propertyList.value.map((item: any) => {
    if (item.type == 1 && state.radioList[item.id]) {
      state.dataForm.attributesList.push(state.radioList[item.id]);
    } else if (item.type == 2) {
      state.dataForm.attributesList = [...state.dataForm.attributesList, ...state.attributesList[item.id]];
    } else if (item.type == 3 && (state.textlist[item.id].start || state.textlist[item.id].end)) {
      state.dataForm.attributesTexts.push(state.textlist[item.id]);
    }
  });

  state.getDataList();
};

// 重置操作
const getResetting = () => {
  createDate.value = [];
  state.dataForm.search = "";
  state.dataForm.server = "";
  state.dataForm.start = "";
  state.dataForm.end = "";
  state.dataForm.priceStart = null;
  state.dataForm.priceEnd = null;
  state.dataForm.attributesList = [];
  state.dataForm.attributesTexts = [];

  initPropertySearch();

  state.getDataList();
};

// 跳转到im
const contactMerchants = async (imUid: string) => {
  if(!imUid) return;
  let url = router.resolve({ 
      path: '/im',
      query: {
          targetId: imUid
      }
  })

  window.open(url.href, imStore.imUid)
}


onMounted(() => {
  getGamesList();
  getPartnersList();
});
</script>

<style lang='less' scoped>
.retail-price {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;

  :deep(.el-input-number) {
    width: 167px;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }
}

.partner-push-details-wrap {
  .input-300 {
    width: 300px;
  }

  .parsing {
    display: flex;

    :deep(p) {
      margin: 0;
    }
  }

  .collapse {
    border: 1px solid var(--el-collapse-border-color);
    margin-bottom: 12px;
    overflow: hidden;

    .p-title {
      margin-left: 10px;
      color: var(--el-color-primary);
    }

    .propertyList {
      padding: 0 15px;
      display: flex;
      align-items: first baseline;
      justify-content: space-between;
    }
  }
}
</style>