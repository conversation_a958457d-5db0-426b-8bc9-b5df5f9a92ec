<template>
  <div class="rr-login">
    <div class="login-bg">
      <div class="picture-merge">
        <img :src="loginBg1" class="bg1" />
        <img :src="loginBg2" class="bg2" />
      </div>
    </div>
    <div class="rr-login-wrap">
      <!-- <div class="rr-login-left hidden-sm-and-down">
        <p class="rr-login-left-title">{{ $t("ui.app.productName") }}</p>
      </div> -->

      <div class="rr-login-right">
        <div class="rr-login-right-main">
          <!-- <h4 class="rr-login-right-main-title">
            {{ $t("ui.login.systemName") }}
            <span class="rr-login-right-main-lang">
              <lang>
                <svg-icon name="fanyiline"></svg-icon>
              </lang>
            </span>
          </h4> -->
          <div class="top-title">
            <img class="logo-img" :src="state.logoImg" alt="logo" />
            <div class="loginTit">
              <span>{{ state.title1 }}</span>
              <span class="loader_animation_name">{{ state.title2 }}</span>
              <span>{{ state.title3 }}</span>
            </div>
          </div>
          <div class="tip-title">Welcome back to the star</div>

          <el-form ref="formRef" label-width="80px" :status-icon="true" :model="login" :rules="rules" label-position="top" hide-required-asterisk @keyup.enter="onLogin" style="width: 70%; margin-left: 30px">
            <!-- <el-form-item v-if="state.tenantMode === 'code'" label-width="0" prop="tenantCode">
              <el-select v-model="state.tenantCode" :placeholder="$t('tenant.select')" style="width: 100%" @change="onChange">
                <el-option v-for="tenant in state.tenantList" :key="tenant.tenantCode" :label="tenant.tenantName" :value="tenant.tenantCode"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="用户名" prop="username">
              <el-input v-model="login.username" :placeholder="$t('ui.login.userNamePlaceholder')" autocomplete="off" class="customInput"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input :placeholder="$t('ui.login.passwordPlaceholder')" v-model="login.password" autocomplete="off" show-password class="customInput"></el-input>
            </el-form-item>
            <!-- <el-form-item label-width="0" prop="captcha">
              <el-space class="rr-login-right-main-code">
                <el-input v-model="login.captcha" :placeholder="$t('ui.login.captchaPlaceholder')" class="customInput"></el-input>
                <img style="vertical-align: middle; height: 40px; cursor: pointer" :src="state.captchaUrl" @click="onRefreshCode" alt="" />
              </el-space>
            </el-form-item> -->
            <el-form-item label-width="0">
              <el-button type="primary" size="small" :disabled="state.loading" @click="onLogin" class="rr-login-right-main-btn">
                {{ $t("ui.login.loginBtn") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="login-footer">
      <!-- <p>
        <a href="https://demo.nyyyds.com/security-enterprise" target="_blank">{{ $t("login.demo") }}</a>
      </p> -->
      <!-- <p>
        <a href="https://www.nyyyds.com/" target="_blank">{{ $t("ui.app.name") }}</a
        >{{ state.year }} © {{ $t("ui.app.copyright") }}
      </p> -->
      <p>
        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ sttingInfo.info.pcIcp }}</a>
      </p>
      <p>{{ sttingInfo.info.pcFooterInfo }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";
import { CacheTenantCode, CacheToken } from "@/constants/cacheKey";
import Lang from "@/components/base/lang/index";
import baseService from "@/service/baseService";
import { getCache, setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import SvgIcon from "@/components/base/svg-icon/index";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import loginBg1 from "@/assets/images/login_bg1.png";
import loginBg2 from "@/assets/images/login_bg2.png";
import logo from "@/assets/images/logo.png";
import { useSettingStore } from "@/store/setting";

const store = useAppStore();
const sttingInfo = useSettingStore();

const router = useRouter();
const { t } = useI18n();

const state = reactive({
  captchaUrl: "",
  loading: false,
  tenantList: [] as any[],
  tenantCode: "",
  tenantMode: "",
  year: new Date().getFullYear(),
  logoImg: "",
  title1: "",
  title2: "",
  title3: ""
});

const login = reactive({ username: "", password: "", captcha: "1", uuid: getUuid() });

onMounted(async () => {
  //清理数据
  // store.logout();
  // getCaptchaUrl();
  state.tenantMode = app.tenantMode;

  if (app.tenantMode === "code") {
    await getTenantList();
  }
  state.tenantCode = getCache(CacheTenantCode, { isParse: false }) || "10000";

   // 监听登录成功
  const logChannel = new BroadcastChannel('login')
  logChannel.onmessage = (e) => {
      if (e.data.name === 'login') {
        router.push(e.data.path);
      }
  }
});

watch(
  () => sttingInfo.info.backendLogo,
  (val) => {
    state.logoImg = sttingInfo.info.backendLogo;
    if (sttingInfo.info.backendTitle && sttingInfo.info.backendTitle.indexOf("商家管理系统") > -1) {
      let ars = sttingInfo.info.backendTitle.split("商家管理系统");
      state.title1 = ars ? ars[0].slice(0, ars[0].length - 1) : "";
      state.title2 = ars ? ars[0].slice(-1) : "";
      state.title3 = "商家管理系统" + ars[1];
    } else {
      let ars = sttingInfo.info.backendTitle;
      state.title1 = ars?.slice(0, ars.length - 1);
      state.title2 = ars?.slice(-1);
    }
  },
  { immediate: true, deep: true }
);

const formRef = ref();
const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            setCache(CacheToken, res.data, false);
            ElMessage.success(t("ui.login.loginOk"));

            baseService.get("/sys/user/info").then((ele) => {

              const logOut = new BroadcastChannel('login')
              logOut.postMessage({
                name: 'login',
                path: ele.data.tenantCode == "10000" ? '/' : '/tenantMenu/shopCenter/shop/shop'
              })

              if (ele.data.tenantCode == "10000") {
                router.push("/");
              } else {
                router.push("/tenantMenu/shopCenter/shop/shop");
              }
            });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          // onRefreshCode();
        });
    }
  });
};
const getTenantList = async () => {
  const res = await baseService.get("/sys/tenant/list");
  state.tenantList = res.data;
};
const onChange = (value: string) => {
  setCache(CacheTenantCode, value + "");
};

// const getCaptchaUrl = () => {
//   login.uuid = getUuid();
//   state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
// };

// const onRefreshCode = () => {
//   getCaptchaUrl();
// };

const rules = ref({
  username: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  password: [{ required: true, message: t("validate.required"), trigger: "blur" }]
  // captcha: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});
</script>

<style lang="less" scoped>
@import url("@/assets/theme/base.less");

.rr-login {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: end;
  justify-content: center;

  .login-bg {
    position: absolute;
    top: 0;
    left: -54vw;
    width: 110%;
    height: 100%;
    overflow: hidden;
    .picture-merge {
      position: relative;
      .bg1 {
        width: 100vw;
        height: 210vh;
        animation: rotate 40s linear infinite;
        position: absolute;
        top: -85vh;
      }
      .bg2 {
        width: 100vw;
        height: 210vh;
        transform: rotate(40deg);
        animation: rotate 60s linear infinite;
        position: absolute;
        top: -85vh;
      }
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(-360deg);
        }
      }
    }
  }

  @media only screen and (max-width: 992px) {
    .rr-login-wrap {
      width: 96% !important;
    }

    .rr-login-right {
      width: 100% !important;
    }
  }

  &-wrap {
    // margin: 0 auto;
    margin-right: 10%;
    // width: 500px;
    // box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);
    animation-duration: 1s;
    animation-fill-mode: both;
    // border-radius: 5px;
    overflow: hidden;
  }

  &-left {
    justify-content: center;
    flex-direction: column;
    background-color: @--color-primary;
    color: #fff;
    float: left;
    width: 50%;

    &-title {
      text-align: center;
      color: #fff;
      font-weight: 300;
      letter-spacing: 2px;
      font-size: 32px;
    }
  }

  &-right {
    border-left: none;
    color: #fff;
    background-color: #fff;
    width: 100%;
    float: left;

    &-main {
      // margin: 0 auto;
      // width: 65%;
      min-width: 300px;

      &-title {
        color: #2c3142;
        margin-bottom: 40px;
        font-weight: 500;
        font-size: 38px;
        text-align: center;
        letter-spacing: 4px;
      }

      &-lang .iconfont {
        font-size: 20px;
        color: #606266;
        font-weight: 800;
        width: 20px;
        height: 20px;
      }

      .el-input__inner {
        border-width: 0;
        border-radius: 0;
        border-bottom: 1px solid #dcdfe6;
      }

      &-code {
        width: 100%;

        .el-space__item:first-child {
          flex: 1;
        }
      }

      &-btn {
        width: 100%;
        height: 45px;
        font-size: 18px !important;
        letter-spacing: 2px;
        font-weight: 300 !important;
        cursor: pointer;
        margin-top: 30px;
        font-family: neo, sans-serif;
        transition: 0.25s;
      }
    }
  }

  .login-footer {
    text-align: center;
    position: absolute;
    bottom: 0;
    padding: 20px;
    color: #666;
    font-size: 14px;
    margin-right: calc((100vw - 400px) / 2);
    p {
      margin: 10px 0;
    }

    a {
      padding: 0 5px;
      color: #666;

      &:focus,
      &:hover {
        color: #666;
      }
    }
  }

  &-left,
  &-right {
    position: relative;
    // min-height: 500px;
    align-items: center;
    display: flex;
  }

  @keyframes animate-down {
    0%,
    60%,
    75%,
    90%,
    to {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }
    0% {
      opacity: 0;
      transform: translate3d(0, -3000px, 0);
    }
    60% {
      opacity: 1;
      transform: translate3d(0, 25px, 0);
    }
    75% {
      transform: translate3d(0, -10px, 0);
    }
    90% {
      transform: translate3d(0, 5px, 0);
    }
    to {
      transform: none;
    }
  }

  .animate-down {
    animation-name: animate-down;
  }
}

// 自定义样式 ----- start -----
.top-title {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
}
.logo-img {
  height: 80px;
  // border-radius: 80px;
  margin-right: 10px;
}
.loginTit {
  display: flex;
  justify-content: center;
  margin-right: 80px;
  span {
    // padding: 70px 0 20px;
    display: inline-block;
    font-size: 38px;
    font-weight: 700;
    line-height: 1;
    letter-spacing: 3px;
    color: #2c3142;
  }
}
/* 标题文字抖动 -s */
.loader_animation_name {
  animation: dou 1s infinite linear;
}

@keyframes dou {
  0% {
    transform: rotate(0);
  }

  11% {
    transform: rotate(7.61deg);
  }

  23% {
    transform: rotate(-5.8deg);
  }

  36% {
    transform: rotate(3.35deg);
  }

  49% {
    transform: rotate(-1.9deg);
  }

  62% {
    transform: rotate(1.12deg);
  }

  75% {
    transform: rotate(-0.64deg);
  }

  88% {
    transform: rotate(0.37deg);
  }

  100% {
    transform: rotate(-0.28deg);
  }
}

/* 标题文字抖动 -e */
.tip-title {
  width: 70%;
  margin-left: 30px;
  color: #2c3142;
  font-size: 15px;
  text-align: center;
  letter-spacing: 1px;
  margin-bottom: 50px;
}
// input样式
.customInput {
  height: 45px;
  width: 100%;
  box-sizing: border-box;
  font-size: 17px;
  background-color: #f8f8f8;
  border-radius: 6px;
  position: relative;

  :deep(.el-input__wrapper) {
    border: none !important;
    box-shadow: none !important;
    background-color: #f8f8f8;
  }
}
// 自定义样式 ----- end -----
</style>
