<template>
  <div class="popular">
    <div class="above">
      <div class="left">
        <span class="title">热门搜索</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
      </div>
    </div>
    <el-table :data="tableData" border style="width: 100%; margin-top: 12px">
      <el-table-column prop="rank" label="排名" align="center" width="60">
        <template #default="{ row, $index }">
          <img class="img" src="../../assets/images/rank1.png" v-if="$index == 0 && state.params.page == 1" />
          <img class="img" src="../../assets/images/rank2.png" v-else-if="$index == 1 && state.params.page == 1" />
          <img class="img" src="../../assets/images/rank3.png" v-else-if="$index == 2 && state.params.page == 1" />
          <span v-else>{{ state.params.limit * (state.params.page - 1) + 1 + $index }}</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="gameTitle" label="游戏" align="center" />
      <el-table-column show-overflow-tooltip prop="searchKeywords" label="搜索关键词" align="center" width="120" />
      <el-table-column prop="nums" label="总点击" align="center" />
      <el-table-column prop="todayNums" label="今日点击" align="center" />
      <el-table-column prop="rate" label="搜索量占比" align="center" width="120">
        <template #default="{ row }">
          <el-progress :percentage="(+row.rate * 100).toFixed(0)" />
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <div class="flx-center">
      <el-pagination v-model:current-page="state.params.page" :page-size="state.params.limit" @change="getData" layout="prev, pager, next" :total="state.totalCount" hide-on-single-page />
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted } from "vue";
import commonData from "@/views/desk/index.ts";
const tableData = ref([]);
const state = reactive({
  totalCount: 0,
  params: {
    page: 1,
    limit: 15
  }
});
const getData = async () => {
  tableData.value = [];
  let res = await commonData.getDeskPopulardata(state.params);
  tableData.value = res.data?.list || [];
  state.totalCount = res.data?.total || 0;
};
onMounted(async () => {
  getData();
});
</script>

<style lang="less" scoped>
.popular {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.img {
  height: 30px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
