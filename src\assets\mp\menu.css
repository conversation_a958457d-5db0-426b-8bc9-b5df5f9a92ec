@charset "utf-8";
*{
	box-sizing: border-box;
}
body{
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
}
ul{
	padding:0;
}
li{
	list-style:none;
}
[v-cloak]{
	display: none;
}
#app-menu{
	overflow: hidden;
	width:950px;
}
.weixin-preview{
	position: relative;
    width: 320px;
    height: 540px;
	float: left;
	margin-right:10px;
    border: 1px solid #e7e7eb;
}
.weixin-preview a{
	text-decoration: none;
	color: #616161;
}
.weixin-preview .weixin-hd{
	color: #fff;
    text-align: center;
	position: relative;
	top:0px;
	left:0px;
    width: 320px;
	height:64px;
	background: transparent url(menu_head.png) no-repeat 0 0;
	background-position: 0 0;
}
.weixin-preview .weixin-hd .weixin-title{
	color:#fff;
	font-size:15px;
	width:100%;
	text-align: center;
	position:absolute;
	top: 33px;
    left: 0px;
}

.weixin-preview .weixin-bd{

}
.weixin-preview .weixin-menu{
	position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #e7e7e7;
    background: transparent url(menu_foot.png) no-repeat 0 0;
    background-position: 0 0;
    background-repeat: no-repeat;
    padding-left: 43px;
	margin-bottom:0px;
}
/*一级*/
.weixin-preview .weixin-menu .menu-item{
	position: relative;
    float: left;
    line-height: 50px;
	height:50px;
    text-align: center;
	width:33.33%;
	border-left: 1px solid #e7e7e7;
	cursor: pointer;
	color:#616161;
}
.weixin-preview .weixin-menu .menu-item:first-child{
	/*border-left-width:0px;*/
}

/*二级*/
.weixin-preview .weixin-sub-menu{
	position: absolute;
	bottom: 60px;
	left: 0;
	right: 0;
	border-top: 1px solid #d0d0d0;
	margin-bottom:0px;
	background: #fafafa;
	display: block;
	padding:0;
}
.weixin-preview .weixin-sub-menu .menu-sub-item{
	line-height: 50px;
	height:50px;
	text-align: center;
	width:100%;
	border: 1px solid #d0d0d0;
	border-top-width: 0px;
	cursor: pointer;
	position: relative;
	color:#616161;
}
.weixin-preview .menu-arrow{
	position: absolute;
	left: 50%;
	margin-left: -6px;
}
.weixin-preview .arrow_in{
	bottom: -4px;
	display: inline-block;
	width: 0px;
	height: 0px;
	border-width: 6px 6px 0px;
	border-style: solid dashed dashed;
	border-color: #fafafa  transparent transparent;
}
.weixin-preview .arrow_out{
	bottom: -5px;
	display: inline-block;
	width: 0px;
	height: 0px;
	border-width: 6px 6px 0px;
	border-style: solid dashed dashed;
	border-color: #d0d0d0 transparent transparent;
}

.weixin-preview .menu-item .menu-item-title,.weixin-preview .menu-sub-item .menu-item-title{
	width:100%;
	overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
	box-sizing: border-box;
}


.weixin-preview .menu-item.current,.weixin-preview .menu-sub-item.current{
	border: 1px solid #44b549;
	background: #fff;
	color:#44b549;
}
.weixin-preview .weixin-sub-menu.show{
	display:block;
}
.weixin-preview .icon_menu_dot{
	background: url(index_z354723.png) 0px -36px no-repeat;
	width: 7px;
	height: 7px;
	vertical-align: middle;
	display: inline-block;
	margin-right: 2px;
	margin-top: -2px;
}
.weixin-preview .icon14_menu_add{
	background: url(index_z354723.png) 0px 0px no-repeat;
	width: 14px;
	height: 14px;
	vertical-align: middle;
	display: inline-block;
	margin-top: -2px;
}
.weixin-preview li:hover .icon14_menu_add{
	background: url(index_z354723.png) 0px -18px no-repeat;
}

.weixin-preview .menu-item:hover{
	color: #000;
}
.weixin-preview .menu-sub-item:hover{
	background: #eee;
}

.weixin-preview li.current:hover{
	background: #fff;
	color: #44b549;
}

/*菜单内容*/
.weixin-menu-detail{
	width: 600px;
	padding: 0px 20px 5px;
	background-color: #f4f5f9;
	border: 1px solid #e7e7eb;
	float: left;
	min-height: 540px;
}
.weixin-menu-detail .menu-name{
	float: left;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
}
.weixin-menu-detail .menu-del{
	float: right;
    height: 40px;
    line-height: 40px;
    color: #459ae9;
	cursor:pointer;
}
.weixin-menu-detail .menu-input-group{
	width:540px;
	margin:10px 0 30px 0;
	overflow: hidden;
}
.weixin-menu-detail .menu-label{
	float: left;
	height: 30px;
	line-height: 30px;
	width:80px;
	text-align: right;
  padding-right: 10px;
}
.weixin-menu-detail .menu-input{
	float:left;
	width: 380px
}
.weixin-menu-detail .menu-input-text{
	border: 0px;
	outline: 0px;
	background: #fff;
	width: 300px;
	padding: 5px 0px 5px 0px;
	margin-left: 10px;
	text-indent: 10px;
	height: 35px;
}
.weixin-menu-detail .menu-tips{
	color: #8d8d8d;
	padding-top: 4px;
	margin:0;
}
.weixin-menu-detail .menu-tips.cursor{
	color: #459ae9;
    cursor: pointer;
}

.weixin-menu-detail .menu-input .menu-tips{
	margin:0 0 0 10px;
}
.weixin-menu-detail .menu-content{
	padding: 16px 20px;
	border: 1px solid #e7e7eb;
	background-color: #fff;
}
.weixin-menu-detail .menu-content .menu-input-group{
	margin: 0px 0 10px 0;
}
.weixin-menu-detail .menu-content .menu-label{
	text-align: left;
	width:100px;
}
.weixin-menu-detail .menu-content .menu-input-text{
	border: 1px solid #e7e7eb;
}
.weixin-menu-detail .menu-content .menu-tips{
	padding-bottom: 10px;
}

.weixin-menu-detail .menu-msg-content{
	padding: 0;
	border: 1px solid #e7e7eb;
	background-color: #fff;
}
.weixin-menu-detail .menu-msg-content .menu-msg-head{
	overflow: hidden;
	border-bottom: 1px solid #e7e7eb;
	line-height: 38px;
	height: 38px;
	padding: 0 20px;
}
.weixin-menu-detail .menu-msg-content .menu-msg-panel{
	padding: 30px 50px;
}
.weixin-menu-detail .menu-msg-content .menu-msg-select{
	padding: 40px 20px;
	border: 2px dotted #d9dadc;
	text-align: center;
	display: flex;
    justify-content: center;
    align-items: center;
}
.weixin-menu-detail .menu-msg-content .menu-msg-select:hover{
	border-color: #b3b3b3;
}
.weixin-menu-detail .menu-msg-content strong{
	display: block;
	padding-top: 3px;
	font-weight: 400;
	font-style: normal;
}
.weixin-menu-detail .menu-msg-content .menu-msg-title{
	float: left;
	width: 310px;
	height: 30px;
	line-height: 30px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.icon36_common{
	width: 36px;
	height: 36px;
	vertical-align: middle;
	display: inline-block;
}
.icon36_common.add_gray{
	background: url(base_z381ecd.png) 0 -2548px no-repeat;
}
.icon_msg_sender{
	margin-right: 3px;
	margin-top: -2px;
	width: 20px;
	height: 20px;
	vertical-align: middle;
	display: inline-block;
	background: url(msg_tab_z25df2d.png) 0 -270px no-repeat;
}

.weixin-btn-group{
	text-align: center;
	width:950px;
	margin:30px 0px;
	overflow: hidden;
}
.weixin-btn-group .btn{
	width: 100px;
	border-radius: 0px;
}

#material-list{
	padding:20px;
	overflow-y:scroll;
	height: 558px;
}
#news-list{
	padding:20px;
	overflow-y:scroll;
	height: 558px;
}
#material-list table{
	width:100%;
}

.mp_tree {
  margin-top: 10px;
  padding: 5px 3px;
  color: #353535;
  font-size: 14px;
}
.mp_tree .el-tree {
  margin-top: 15px;
}
.menu_setting_area {
  width: 1000px;
  padding: 20px;
  color: #353535;
  font-size: 14px;
}
