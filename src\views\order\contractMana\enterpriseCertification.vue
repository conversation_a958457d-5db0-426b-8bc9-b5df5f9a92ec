<template>
  <el-dialog class="contractTemplateDialog" v-model="visible" width="480" title="企业认证" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="margin-bottom: 38px" :model="dataForm" :rules="rules" ref="dataFormRef" v-if="isEdit">
      <el-form-item style="margin-bottom: 8px" prop="enterpriseName" label="企业名称">
        <el-input clearable v-model="dataForm.enterpriseName" placeholder="请输入企业名称"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="licenseCode" label="工商注册号或统一社会信用代码">
        <el-input clearable v-model="dataForm.licenseCode" placeholder="请输入工商注册号或统一社会信用代码"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="legalPersonName" label="法定代表人姓名">
        <el-input clearable v-model="dataForm.legalPersonName" placeholder="请输入法定代表人姓名"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="legalPersonIdNo" label="法定代表人证件号">
        <el-input clearable v-model="dataForm.legalPersonIdNo" placeholder="请输入法定代表人证件号"></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom: 8px" prop="legalPersonPhone" label="法定代表人手机号">
        <el-input clearable v-model="dataForm.legalPersonPhone" placeholder="请输入法定代表人手机号"></el-input>
      </el-form-item>
    </el-form>
    <el-descriptions style="margin-bottom: 66px; margin-top: 8px" v-else class="descriptions-label-140" :column="1" border>
      <el-descriptions-item label="认证状态">
        <el-text v-if="dataForm.regStatus == '5'" type="success">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
        <el-text v-else-if="dataForm.regStatus == '4'" type="danger">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
        <el-text v-else type="primary">{{ statusList[+dataForm.regStatus] || "-" }}</el-text>
      </el-descriptions-item>
      <el-descriptions-item label="企业名称"
        ><el-text type="sucess">{{ dataForm.enterpriseName || "-" }}</el-text></el-descriptions-item
      >
      <el-descriptions-item label="工商注册号或统一社会信用代码">{{ dataForm.licenseCode || "-" }}</el-descriptions-item>
      <el-descriptions-item label="法定代表人姓名">{{ dataForm.legalPersonName || "-" }}</el-descriptions-item>
      <el-descriptions-item label="法定代表人证件号">{{ dataForm.legalPersonIdNo || "-" }}</el-descriptions-item>
      <el-descriptions-item label="法定代表人手机号">{{ dataForm.legalPersonPhone || "-" }}</el-descriptions-item>
      <el-descriptions-item label="签章">
        <el-image v-if="dataForm.stampUrl != null" style="width: 100px" :src="dataForm.stampUrl" :preview-src-list="[dataForm.stampUrl]" :preview-teleported="true" fit="cover"></el-image>
      </el-descriptions-item>
    </el-descriptions>
    <template v-slot:footer>
      <template v-if="isEdit">
        <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">提交认证</el-button>
      </template>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const isEdit = ref(false);
let dataForm = reactive({
  enterpriseName: undefined,
  legalPersonName: undefined,
  legalPersonIdNo: undefined,
  legalPersonPhone: undefined,
  licenseCode: undefined
});

const rules = ref({
  enterpriseName: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
  legalPersonName: [{ required: true, message: "请输入法定代表人姓名", trigger: "blur" }],
  legalPersonIdNo: [{ required: true, message: "请输入法定代表人证件号", trigger: "blur" }],
  legalPersonPhone: [
    { required: true, message: "请输入法定代表人手机号", trigger: "blur" },
    {
      required: true,
      pattern: /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ],
  licenseCode: [{ required: true, message: "请输入工商注册号或统一社会信用代码", trigger: "blur" }]
});
const statusList = ref(["taskId不存在或已过期", "新申请", "申请中", "超时", "认证失败", "认证成功", "失败重试"]);

const init = (obj?: any, isedit = true) => {
  visible.value = true;
  isEdit.value = isedit;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  delete dataForm.id;

  if (obj.id) {
    dataForm = Object.assign(dataForm, obj);
    dataForm.enterpriseName = obj.bestsignUserName;
    dataForm.legalPersonPhone = obj.bestsignMobile;
  }
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let form_ = { ...dataForm };
    if (dataForm.id) {
      form_.bestsignUserName = dataForm.enterpriseName;
      form_.bestsignMobile = dataForm.legalPersonPhone;
      delete dataForm.enterpriseName;
      delete dataForm.legalPersonPhone;
    }
    (!dataForm.id ? baseService.post : baseService.put)(!dataForm.id ? "/bestsign/createBestsignSelfEnpUser" : "/bestsign/editUser", { ...form_, bestsignUserType: 2 }).then((res) => {
      ElMessage.success({
        message: t("提交成功！请等待认证"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
<style lang="scss">
.contractTemplateDialog {
  padding: 24px;
  .el-dialog__headerbtn {
    transform: translate(-25%, 25%);
  }
  .el-form-item {
    display: block;
  }
}
</style>
