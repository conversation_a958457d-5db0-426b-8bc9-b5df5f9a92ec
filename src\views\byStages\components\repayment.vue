<template>
  <el-dialog v-model="dialogVisible" :title="dataForm.type == 1 ? '还款' :'提前还款'" width="480">
    <div class="cardDescriptions" style="padding: 0px">
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef" v-loading="infoLoading">
        <el-descriptions :column="1" border class="descriptions">
          <el-descriptions-item>
            <template #label>
              <span>商品编码</span>
            </template>
            <span>{{ dataForm.shopCode }}</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ dataForm.type == 1 ? '分期期数' : '还款方式'}}</span>
            </template>
            <span v-if="dataForm.type == 1">{{ dataForm.nowPeriods }}/{{ dataForm.byStagesPeriods }}</span>
            <span v-else>全额还款</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ dataForm.type == 1 ? '本期还款' : '应还总金额(元)'}}</span>
            </template>
            <div>
              <span style="color: red">{{ dataForm.repaymentMoney }}</span>
              <span v-if="dataForm.type == 2"> (含手续费{{ dataForm.monthRepaymentCommission }})</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>付款方式<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="payType" :key="1">
              <el-select v-model="dataForm.payType" clearable placeholder="请选择付款方式">
                <el-option v-for="(item, index) in ['支付宝', '微信', '银行卡', '淘宝']" :label="item" :value="index + 1" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="dataForm.payType == 1">
            <template #label>
              <span>支付宝订单号<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="alipayOrderNo" :key="2">
              <el-input v-model="dataForm.alipayOrderNo" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>付款凭证<span style="color: red">*</span></span>
            </template>
            <el-form-item prop="payCredentials" :key="3">
              <ny-upload v-model:imageUrl="dataForm.payCredentials" :limit="1" :fileSize="5" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" :loading="btLoading">取消</el-button>
        <el-button type="primary" @click="submit" :loading="btLoading"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);
const dialogVisible = ref(false);
const dataForm = ref(<any>{});
const rules = reactive({
  payType: [{ required: true, message: "请选择付款方式", trigger: "change" }],
  alipayOrderNo: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }],
  payCredentials: [{ required: true, message: "请上传付款凭证", trigger: "blur" }]
});

// 提交
const formRef = ref();
const btLoading = ref(false);
const submit = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btLoading.value = true;
    if (dataForm.value.type == 1) {
      baseService
      .put("/sale/saleOrderByStages/repayment", dataForm.value)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("还款成功");
          dialogVisible.value = false;
          emits("refresh");
        }
      })
      .finally(() => {
        btLoading.value = false;
      });
    }
    if (dataForm.value.type == 2) {
      baseService
      .put("/sale/saleOrderByStages/inAdvanceRepayment", dataForm.value)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提前还款成功");
          dialogVisible.value = false;
          emits("refresh");
        }
      })
      .finally(() => {
        btLoading.value = false;
      });
    }


      
  });
};

const infoLoading = ref(false);
const init = (row: any, type: number) => {
  dialogVisible.value = true;
  dataForm.value.shopCode = row.shopCode;
  dataForm.value.byStagesPeriods = row.byStagesPeriods;
  dataForm.value.type = type;
  infoLoading.value = true;

  // type: 1 还款 2 提前还款
  if (type == 1) {
    baseService
      .get("/sale/saleOrderByStages/getPayment/" + row.id)
      .then((res) => {
        if (res.code == 0) {
          Object.assign(dataForm.value, res.data);
          dataForm.value.payType = !res.data.payType ? undefined : res.data.payType
        }
      })
      .finally(() => {
        infoLoading.value = false;
      });
  }
  if (type == 2) {
    baseService
      .get("/sale/saleOrderByStages/getInAdvanceRepayment/" + row.id)
      .then((res) => {
        if (res.code == 0) {
          Object.assign(dataForm.value, res.data);
          dataForm.value.payType = !res.data.payType ? undefined : res.data.payType
        }
      })
      .finally(() => {
        infoLoading.value = false;
      });
  }
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.rateList {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
  .rateListItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    span {
      flex: 1;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      color: #303133;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
