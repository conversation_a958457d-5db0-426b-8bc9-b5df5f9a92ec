<template>
    <div class="business_page">
        <div class="business_header flx-align-center">
            <el-button type="info" :loading="exportclickLoading" @click="exportclick">导出数据</el-button>
            <el-radio-group v-model="dataForm.type" style="margin-left: 24px;" @change="radioChange">
                <el-radio value="1">被销员工</el-radio>
                <!-- <el-radio value="2">回收渠道</el-radio> -->
                <el-radio value="3">游戏</el-radio>
            </el-radio-group>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">滞销分布柱状图</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="legend">
                        <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData"
                            :key="index" @change="changeShow"></el-checkbox>
                    </div>
                    <!-- <div class="analysis_type_item" style="margin-left: 10px;">取当前数据进行比对</div> -->
                </div>
            </div>
            <div class="header_describe">在库商品数据</div>
            <div class="charts">
                <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
            </div>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">滞销分布明细表</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <!-- <div class="analysis_type_item" style="margin-left: 10px;">取当前数据进行比对</div> -->
                </div>
            </div>
            <div style="padding: 0px 20px 10px 20px;">
                <el-table :data="paginatedData" style="width: 100%;margin-top: 12px;" cell-class-name="ch-56" border
                    class="business_table" @sort-change="sortChange">
                    <template v-for="item in columns" :key="item">
                        <el-table-column :prop="item.prop" :label="item.label" :sortable="item.prop != 'name' ? 'custom' : false"
                            :min-width="item.minWidth" align="center">
                            <template #header v-if="item.prop == 'name'">
                                <span v-if="dataForm.type == '1'">被销员工</span>
                                <span v-if="dataForm.type == '2'">回收渠道</span>
                                <span v-if="dataForm.type == '3'">游戏</span>
                            </template>
                            <template #default="{ row, $index }">
                                {{ row[item.prop] }}
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 空状态 -->
                    <template #empty>
                        <div style="padding: 68px 0">
                            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                        </div>
                    </template>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="pagination.size" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
            </div>

        </div>
    </div>
</template>

<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, computed, onMounted } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { usePagination, useSortList } from "@/views/dataAnalysis/pagination";

const tableData = ref(<any>[]);

// 分页
const pagination = ref({
  page: 1,
  size: 10
});
const total = ref();

// 计算分页数据
const paginatedData = computed(() =>
  usePagination({
    currentPage: pagination.value.page,
    pageSize: pagination.value.size,
    data: tableData.value
  })
);

// 表格配置项
const columns = reactive([
    {
        prop: "name",
        label: "被销员工",
        minWidth: 112
    },
    {
        prop: "zeroToFiveDays",
        label: "0~5天",
        minWidth: 80
    },
    {
        prop: "fiveToTenDays",
        label: "5~10天",
        minWidth: 80
    },
    {
        prop: "tenToFifteenDays",
        label: "10~15天",
        minWidth: 100
    },
    {
        prop: "fifteenToThirtyDays",
        label: "15~30天",
        minWidth: 100
    },
    {
        prop: "thirtyToSixtyDays",
        label: "30~60天",
        minWidth: 100
    },
    {
        prop: "sixtyToNinetyDays",
        label: "60~90天",
        minWidth: 100
    },
    {
        prop: "ninetyDaysAbove",
        label: "90天以上",
        minWidth: 100
    },
]);

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    purchaseEmployeeId: "",
    type: "1"
});

// 图例数据
const legendData = ref([
    { name: '0~5天', color: '#4165D7', show: true },
    { name: '5~10天', color: '#00C568', show: true },
    { name: '10~15天', color: '#722ED1', show: true },
    { name: '15~30天', color: '#F77234', show: true },
    { name: '30~60天', color: '#0FC6C2', show: true },
    { name: '60~90天', color: '#F53F3F', show: true },
    { name: '90天以上', color: '#86909C', show: true },
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '0~5天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#4165D7"
        },
        barMaxWidth: 30,
        data: [],
    },
    {
        name: '5~10天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#00B42A"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '10~15天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#722ED1"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '15~30天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#F77234"
        },
        barMaxWidth: 30,
        data: [],
    },
    {
        name: '30~60天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#0FC6C2"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '60~90天',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#F53F3F"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '90天以上',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
            color: "#86909C"
        },
        barMaxWidth: 30,
        data: []
    }
])

// 游戏库存折线图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '3%',
            top: '10%',
            bottom: '16%',
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '商品(个)',
                min: 0,
                max: 'dataMax',
                interval: 500,
                axisLabel: {
                    formatter: '{value} 个'
                }
            },
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 30,
            bottom: "4%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 9
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 9
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

// 滞销分布柱状图
const getStagnationDistribution = () => {
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/stagnationDistribution", dataForm.value).then(res => {
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

// 滞销分布明细表
const getStagnationDistributionDetail = () => {
    baseService.post("/dataAnalysis/stagnationDistributionDetail", dataForm.value).then(res => {
        tableData.value = res.data;
        total.value = tableData.value.length;
    })
}

const radioChange = () => {
    getStagnationDistribution();
    getStagnationDistributionDetail();
}

const exportclickLoading = ref(false);
const exportclick = () => {
    exportclickLoading.value = true;
    baseService
        .get("/dataAnalysis/stagnationDistributionDetailExport", dataForm.value)
        .then((res) => {
            if (res) {
                fileExport(res, "滞销分布明细表");
            }
        })
        .finally(() => {
            exportclickLoading.value = false;
        });
};

// 分页方法
const sizeChange = (number: any) => {
  pagination.value.size = number;
  pagination.value.page = 1;
};

const currentChange = (number: any) => {
  pagination.value.page = number;
};

// 排序事件
const sortChange = (column: any) => {
    if(column.order != null){
        tableData.value = useSortList({
            prop: column.prop,
            order: column.order,
            data: tableData.value
        })
    }
};

const echartsZoom = ref('1');

onMounted(() => {
    getStagnationDistribution();
    getStagnationDistributionDetail();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getStagnationDistribution();
    getStagnationDistributionDetail();
};

defineExpose({
    init
});

</script>

<style lang='less' scoped>
.business_page {
    margin-top: 12px;
}

.business_header {}

.business_center {}

.business_table {
    :deep(th .cell) {
        background: none !important;
    }

    // 表头
    :deep(th:nth-child(n+2):nth-child(-n+2)) {
        background-color: rgba(65, 101, 215, 0.1) !important;

        .cell {
            color: #4165D7;
        }
    }

    :deep(th:nth-child(n+3):nth-child(-n+3)) {
        background-color: rgba(0, 180, 42, 0.1) !important;

        .cell {
            color: #00C568;
        }
    }

    :deep(th:nth-child(n+4):nth-child(-n+4)) {
        background-color: rgba(114, 46, 209, 0.1) !important;

        .cell {
            color: #722ED1;
        }
    }

    :deep(th:nth-child(n+5):nth-child(-n+5)) {
        background-color: rgba(247, 114, 52, 0.1) !important;

        .cell {
            color: #F77234;
        }
    }

    :deep(th:nth-child(n+6):nth-child(-n+6)) {
        background-color: rgba(15, 198, 194, 0.1) !important;

        .cell {
            color: #0FC6C2;
        }
    }

    :deep(th:nth-child(n+7):nth-child(-n+7)) {
        background-color: rgba(245, 63, 63, 0.1) !important;

        .cell {
            color: #F53F3F;
        }
    }

    :deep(th:nth-child(n+8):nth-child(-n+8)) {
        background-color: rgba(134, 144, 156, 0.1) !important;

        .cell {
            color: #86909C;
        }
    }

    // 内容
    :deep(td:nth-child(n+2):nth-child(-n+3)) {
        background-color: rgba(65, 101, 215, 0.05) !important;
    }

    :deep(td:nth-child(n+3):nth-child(-n+4)) {
        background-color: rgba(0, 180, 42, 0.05) !important;
    }

    :deep(td:nth-child(n+4):nth-child(-n+5)) {
        background-color: rgba(114, 46, 209, 0.05) !important;
    }

    :deep(td:nth-child(n+5):nth-child(-n+6)) {
        background-color: rgba(247, 114, 52, 0.05) !important;
    }

    :deep(td:nth-child(n+6):nth-child(-n+7)) {
        background-color: rgba(15, 198, 194, 0.05) !important;
    }

    :deep(td:nth-child(n+7):nth-child(-n+8)) {
        background-color: rgba(245, 63, 63, 0.05) !important;
    }

    :deep(td:nth-child(n+8):nth-child(-n+9)) {
        background-color: rgba(134, 144, 156, 0.05) !important;
    }
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165D7;
                        border-color: #4165D7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #00B42A;
                        border-color: #00B42A;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ED1;
                        border-color: #722ED1;
                    }
                }

                .el-checkbox:nth-child(4) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F77234;
                        border-color: #F77234;
                    }
                }

                .el-checkbox:nth-child(5) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #0FC6C2;
                        border-color: #0FC6C2;
                    }
                }

                .el-checkbox:nth-child(6) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F53F3F;
                        border-color: #F53F3F;
                    }
                }

                .el-checkbox:nth-child(7) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #86909C;
                        border-color: #86909C;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.analysis_type_item {
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    line-height: 22px;
    padding: 3px 12px;
    background: #FFFFFF;
    border-radius: 24px;
    border: 1px solid #D4D7DE;
}
</style>