<template>
    <el-dialog v-model="visible" title="公式编辑器"  width="758" class="dialog_page" style="padding: 0px;">
        <template #header="{ close, titleId, titleClass }">
            <div class="dialog_header">公式编辑器</div>
        </template>
        <div class="formula">
            <el-input
                v-model="formulaVlaue"
                class="formula_pane_editor"
                :rows="5"
                type="textarea"
                placeholder="请输入函数公式"
                @blur="handleBlur"
                ref="inputRef"
            /> 
        </div>
        <div class="symbolList">
            <div class="symbol1" @click="appendFormula('()')">括号()</div>
            <div class="symbol2" @click="appendFormula('+')">加号+</div>
            <div class="symbol3" @click="appendFormula('-')">减号-</div>
            <div class="symbol4" @click="appendFormula('*')">乘号*</div>
            <div class="symbol5" @click="appendFormula('/')">除号/</div>
        </div>
        <div class="flx-justify-between" style="padding: 12px 24px;">
            <div class="result flx">
                <span style="color: #909399;">结果值=</span>
                <div v-if="executionInfo.show == '1'">
                    <el-text class="mx-1" type="success">公式解析成功;</el-text>
                </div>
                <div v-if="executionInfo.show == '0'">
                    <el-text class="mx-1" type="danger" >公式解析错误，语法错误;</el-text>
                </div>
            </div>
            <el-button type="primary" @click="submit">确定</el-button>
        </div>
        <div class="field">
            <div class="field_header">引用当前表格字段</div>
            <div class="field_list">
                <el-input
                    v-model="fieldValue"
                    class="field_search_input"
                    size="small"
                    placeholder="搜索字段"
                    clearable
                    :prefix-icon="Search"
                    @change="fieldSearch"
                    @clear="fieldSearch"
                />
                <div class="items flx-justify-between" @click="appendFormula(item.name)" v-for="(item,index) in targetList" :key="index">
                    <span>{{ item.name }}</span><span class="opt">选择</span>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import baseService from "@/service/baseService";
import { Search } from '@element-plus/icons-vue';

const emit = defineEmits(["formulaChange"]);
const visible = ref(false);
const inputRef = ref();
const formulaVlaue = ref('');
const fieldValue = ref('');

const cursorPos = ref(); // 光标位置
const handleBlur = (e: any) => {
    cursorPos.value = e.srcElement.selectionStart;
};

// 插入文本
const appendFormula = (str: any) => {
    const start = formulaVlaue.value.substring(0, cursorPos.value);
    const end = formulaVlaue.value.substring(cursorPos.value);
    formulaVlaue.value = `${start}${str} ${end}`;
    cursorPos.value += str.length + 1
};

// 公式运行结果
const executionInfo = ref({
    show: '', // 1：运行结果正确 ， 0：运行结果错误
    error:''
})
// 公式验证
const formulaVerify = (str: string) => {
    executionInfo.value.show = ''; // 验证状态
    executionInfo.value.error = '';
    if (!str) {
        ElMessage.warning('请输入公式')
        return
    }
    
    const strArr = str.split(' ');
    const data = reactive({
      isAdjacentDuplicate: computed(() => {
        for (let i = 1; i < strArr.length; i++) {
          if (strArr[i] === strArr[i - 1]) {
            return true;
          }
        }
        return false;
      })
    });

    if(data.isAdjacentDuplicate){
        executionInfo.value.show = '0';
        return
    }
    
    str = str.replace(/ /g, '') // 去掉空格
    const chinese = /[\u4E00-\u9FA5\uF900-\uFA2D]/ // 汉字
    const english = new RegExp('[A-Za-z]+') // 字母
    /*
        1、forEach 不能遍历字符串
        2、for循环找出字符串中的汉字与字母
    */
    for (let i = 0; i < str.length; i++) {
        if (chinese.test(str[i]) || english.test(str[i])) {
            // 找出汉字与字母并替换成 数字 1
            str = str.replaceAll(str[i], 1)
        }
    }
    // 验证是否存在加减乘除
    if (!str.match(/\*|-|[÷]|[+]/)) {
        executionInfo.value.show = '0'
        return
    }
    // 如果公式错误会出现报错信息
    try {
        // 构造函数
        // 执行字符串
        let makeFun = new Function('return ' + str)
        try {
            makeFun();
            executionInfo.value.show = '1'
            // console.log('语法正确', makeFun())
        } catch (error:any) {
            executionInfo.value.show = '0'
            executionInfo.value.error = error.message
        }
    } catch (errorInfo: any) {
        executionInfo.value.show = '0'
        executionInfo.value.error = errorInfo.message
    }
}

// 监听输入公式
watch(
    () => formulaVlaue.value,
    (newValue) => {
        // console.log('监听用户输入', newValue)
        setTimeout(() => {
            formulaVerify(newValue)
        }, 1000);
    }
)

// 字段搜索
const fieldSearch = () => {
    baseService.get('/report/reportcolumn/all',{indexName:tableIndexName.value}).then(res=>{
        const List = res.data.filter((item:any) => item.type == 2 );
        const regular = new RegExp(fieldValue.value, 'i');
        const regList = List.filter((item: any) => regular.test(item.name));
        targetList.value = regList;
    })
}

// 提交公式
const formulaList = ref(<any>[])
const submit = () =>{
    let str = JSON.parse(JSON.stringify(formulaVlaue.value));
    if(!str){
        ElMessage.warning('请输入公式');
        return
    }
    str = str.replace(/ /g, '') 
    str = str.replace(/([^\u4e00-\u9fa5])/g, ' $1 ');
    const list = JSON.parse(JSON.stringify(str.split(' ')));
    const formula:any = []; // 公式；
    const columns:any = []; // 字段；
    list.map((item:any)=>{
        let info = formulaList.value.find((ele:any) => ele.name == item);
        if(info){
            formula.push(info.dynamicKey);
            columns.push(info.dynamicKey);
        }else{
            formula.push(item)
        }
    })

    const params = {
        formula: formula.filter((item:any)=> item != '').join(''),
        columns: Array.from(new Set(columns)).join(',')
    }
    visible.value = false;
    emit('formulaChange',params);

    console.log(params,'==== formula ====')
}


const tableIndexName = ref('')
const targetList = ref(<any>[]);
const init = (indexName:any,formula:any) => {
    tableIndexName.value = indexName
    visible.value = true;
    // 获取当前表格字段
    baseService.get('/report/reportcolumn/all',{indexName:indexName}).then(res=>{
        targetList.value = res.data.filter((item:any) => item.type == 2 );
        formulaList.value = res.data.filter((item:any) => item.type == 2 );
        // 公式回显,别名替换成中文
        if(formula){
            let str = formula.formula
            formula.columns.split(',').map((item:any)=>{
                let info = formulaList.value.find((ele:any) => ele.dynamicKey == item);
                if(info){
                    str = replaceWordWithChinese(str,item,info.name);
                }
            })
            formulaVlaue.value = str;
        }
    })
    nextTick(()=>{
        inputRef.value.focus();
    })
};

// 将指定字符串替换成其他字符串
const replaceWordWithChinese = (str:string, word:string, chinese:string) => {
  const regex = new RegExp(word, 'gi'); // 'gi' 表示全局搜索和忽略大小写
  return str.replace(regex, chinese);
}

defineExpose({
  init
});

</script>

<style lang='less' scoped>
.dialog_header{
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    padding-top: 15px;
    padding-left: 16px;
}
.editFormData{
    width: 100%;
    height: 274px;
    padding: 12px;
}
.formula{
    border-top: 1px solid #EBEEF5;
    padding-top: 10px;
}
.formula_pane_editor{
    :deep(.el-textarea__inner){
        box-shadow: none;
        border-radius: 8px;
    }
}
.result{
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    line-height: 26px;
    // span{
    //     color: #909399;
    // }
}
.symbolList{
    padding-bottom: 12px;
    padding-left: 24px;
    border-bottom: 1px solid #EBEEF5;
}
.symbol1{
    background: #DAEEFF;
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    color: #3366FF;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    cursor: pointer;
}
.symbol2{
    background: #FCF6EC;
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    color: #E6A23C;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    margin-left: 12px;
    cursor: pointer;
}
.symbol3{
    background: #DAFFE6;
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    color: #00C568;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    margin-left: 12px;
    cursor: pointer;
}
.symbol4{
    background: #DDDCFF;
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    color: #5654FF;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    margin-left: 12px;
    cursor: pointer;
}
.symbol5{
    background: #CDF0FF;
    border-radius: 2px 2px 2px 2px;
    font-weight: 500;
    font-size: 14px;
    color: #00A5EF;
    line-height: 18px;
    display: inline;
    padding: 2px 6px;
    margin-left: 12px;
    cursor: pointer;
}

.field{
    background-color: #F5F6F7;
    min-height: 372px;
    border-radius: 0px 0px 4px 4px;
    .field_header{
        padding: 12px;
        font-weight: 500;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
        border-bottom: 1px solid #EBEEF5;
    }
    .field_list{
        padding: 8px 12px;
        .field_search_input{
        }
        .items{
            font-weight: 400;
            font-size: 14px;
            color: #606266;
            line-height: 22px;
            margin-top: 8px;
            cursor: pointer;
            padding: 4px 8px;
            .opt{
                display: none;
            }
        }
        
        .items:hover{
            background-color: #ECF0FB;
            border-radius: 4px 4px 4px 4px;
            .opt{
                display: block;
                color: #4165D7;
            }
        }
    }
    
}
</style>