import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import Sortable from 'sortablejs';

/**
 * @description 操作table表格排序
 * @param {String} api 操作数据接口的api (必传)
 * @param {Object} state 携带的操作数据参数
 * @param {String} method 请求方式 (不必传,默认为 get)
 * @returns {Promise}
 */

type MethodType = "get" | "put" | "post";

export const useTableSort = (
  state: any = {},
  api: string,
  method: MethodType = "get",
) => {
  return new Promise(async (resolve, reject) => {
    let el: any = document.querySelector('.draggable-table .el-table__body-wrapper tbody');
    Sortable.create(el, {
      handle: '.dragBtn',
      ghostClass: "",
      setData: (dataTransfer) => { },
      onEnd: ({ newIndex = 0, oldIndex = 0 }) => {
        const newId = state.dataList[newIndex].id;
        const oldId = state.dataList[oldIndex].id;
        baseService[method](api, { sourceId: oldId, targetId: newId }).then((res) => {
          if (res.code == 0) {
            ElMessage.success('操作成功！');
            state.getDataList();
            resolve(true);
          } else {
            ElMessage.error('操作失败！');
            reject(false);
          }
        }).catch((err) => {
          ElMessage.error('操作失败！');
          reject(false);
        });
      }
    });
  });
};
