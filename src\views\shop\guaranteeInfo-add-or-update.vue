<template>
  <el-drawer v-model="visible" size="40%" class="drawerGuaranteeInfo">
    <template #header>
      <div class="drawer_title">{{ !dataForm.id ? $t("add") + "包赔收集信息" : $t("update") + "包赔收集信息" }}</div>
    </template>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" @keyup.enter="">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="信息名称" prop="title">
            <el-input v-model="dataForm.title" placeholder="信息名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段配置格式" prop="infoType">
            <el-select v-model="dataForm.infoType" placeholder="字段配置格式">
              <el-option label="文本" :value="0" />
              <el-option label="图片" :value="1" />
              <el-option label="数字" :value="2" />
              <el-option label="单选" :value="3" />
              <el-option label="多选" :value="4" />
              <el-option label="城市" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="dataForm.infoType == '3' || dataForm.infoType == '4'">
          <el-form-item label="选项内容（输入选项内容，以“、”或“，”隔开）" prop="infoOptions">
            <el-input v-model="dataForm.infoOptions" placeholder="选项内容"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信息分组" prop="guaranteenGroupName">
            <el-select v-model="dataForm.guaranteenGroupId" placeholder="信息分组" allow-create filterable clearable @change="selectChange">
              <el-option :label="item.title" :value="item.id" v-for="(item, index) in guaranteeList" :key="index">
                <div class="flx-align-center deleteCentrer">
                  <span class="flx-1">{{ item.title }}</span>
                  <el-button type="danger" :icon="Delete" :disabled="item.isDefault == 1" plain size="small" class="deleteIcon" @click="guaranteenGroupIdDelete(item)" />
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortNum">
            <el-input-number v-model="dataForm.sortNum" :min="0" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否必填" prop="required">
            <!-- 0：否 1：是 -->
            <el-switch v-model="dataForm.required" :active-value="1" :inactive-value="0" active-text="必填" inactive-text="非必填" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否显示" prop="isShow">
            <!-- 0：否 1：是 -->
            <el-switch v-model="dataForm.isShow" :active-value="1" :inactive-value="0" active-text="显示" inactive-text="不显示" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Delete } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const visible = ref(false); // 对话框显隐
const dataFormRef = ref(); // 表单ref

const dataForm = reactive({
  // 表单变量
  id: null,
  title: "",
  infoType: "",
  infoOptions: "",
  guaranteenGroupId: "",
  guaranteenGroupName: "",
  sortNum: "",
  required: "0",
  isShow: "0"
});
const rules = ref({
  // 表单必填项
  title: [{ required: true, message: "请输入信息名称", trigger: "blur" }],
  infoType: [{ required: true, message: "请选择字段配置格式", trigger: "change" }],
  infoOptions: [{ required: true, message: "请输入选项内容", trigger: "blur" }],
  guaranteenGroupName: [{ required: true, message: "请输入信息分组", trigger: "change" }],
  sortNum: [{ required: true, message: "请输入排序", trigger: "blur" }],
  required: [{ required: true, message: "请选择是否必填", trigger: "change" }],
  isShow: [{ required: true, message: "请选择是否显示", trigger: "change" }]
});

// 表单初始化
const init = (id?: any) => {
  visible.value = true;
  dataForm.id = id ? id : null;
  guaranteeInfoGroup();
  // 获取表单详情
  if (id) {
    getInfo(id);
  }
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};

// 获取信息分组列表
const guaranteeList = ref(<any>[]);
const guaranteeInfoGroup = () => {
  baseService.get("/shop/guaranteeinfogroup/list").then((res) => {
    guaranteeList.value = res.data;
  });
};

// 获取详情
const getInfo = (id: number) => {
  baseService.get("/shop/guaranteeinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    console.log("====== guaranteeList =======", guaranteeList.value);
  });
};

// 信息分组选择事件
const selectChange = (value: any) => {
  const changeItem = guaranteeList.value.filter((item: any) => item.id == value);
  if (changeItem.length) {
    dataForm.guaranteenGroupId = changeItem[0].id;
    dataForm.guaranteenGroupName = changeItem[0].title;
    console.log(changeItem, value);
  } else {
    dataForm.guaranteenGroupId = value;
    dataForm.guaranteenGroupName = value;
    console.log(changeItem, value);
  }
};

// 信息分组删除
const guaranteenGroupIdDelete = (value: any) => {
  ElMessageBox.confirm("确定删除该信息分组?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      baseService.delete("/shop/guaranteeinfogroup", [value.id]).then((res) => {
        ElMessage.success("删除成功");
        dataForm.guaranteenGroupId = "";
        guaranteeInfoGroup();
      });
    })
    .catch(() => {});
};

// 表单提交
const btnLoading = ref(false);
const emit = defineEmits(["refreshDataList"]);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    const changeItem = guaranteeList.value.filter((item: any) => item.id == dataForm.guaranteenGroupId);
    if (!changeItem.length) {
      dataForm.guaranteenGroupId = "";
    }
    console.log("======= datafrom =====", dataForm);
    // return
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/shop/guaranteeinfo", dataForm)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.deleteIcon {
  display: none;
}
.deleteCentrer:hover .deleteIcon {
  display: block;
}
</style>
<style lang="less">
.drawerGuaranteeInfo {
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
</style>
