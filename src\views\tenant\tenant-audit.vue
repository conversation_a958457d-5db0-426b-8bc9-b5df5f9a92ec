<template>
  <div class="mod-tenant__tenant">
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
      <ny-table :state="state" :columns="columns" routePath="/tenant/audit" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button :disabled="!state.dataListSelections || !state.dataListSelections.length" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
        </template>
        <template #header-right>
          <el-input v-model="state.dataForm.tenantName" placeholder="请输入合作商名称" :prefix-icon="Search" clearable></el-input>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>

        <!-- 申请类型 -->
        <template #applicationType="{ row }">
          <span v-if="row.companyName">企业商家</span>
          <span v-else>个人商家</span>
        </template>


        <!-- 审核状态 -->
        <template #status="{ row }">
          <el-tag v-if="row.status === 1" type="success">已通过</el-tag>
          <el-tag v-else-if="row.status === 2" type="error">已拒绝</el-tag>
          <el-tag v-else type="warning">待审核</el-tag>
        </template>

        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button v-if="!row.status" type="primary" text bg @click="auditHandle(row.id, 'audit')">
            审核
          </el-button>
          <el-button v-if="row.status==1" type="warning" text bg @click="addOrUpdateHandle(row.id)">
            完善信息
          </el-button>
          <el-button v-if="row.status" type="primary" text bg @click="auditHandle(row.id, 'detail')">
            查看
          </el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(row.id)">
            删除
          </el-button>
        </template>
      </ny-table>
    <!-- </el-card> -->

      <!-- 审核 -->
      <teant-audit-detail ref="teantAuditDetailRef" :key="teantAuditDetailKey" @refreshData="state.query()"></teant-audit-detail>

    <!-- 完善信息 -->
    <add-or-update ref="addOrUpdateRef" :key="addOrUpdateKey" @refreshDataList="state.getDataList"></add-or-update>

  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, nextTick } from "vue";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import TeantAuditDetail from './teant-audit-detail.vue'
import AddOrUpdate from "./tenant-improve-info.vue";

const view = reactive({
  getDataListURL: "/tenant/tenantregistered/page",
  getDataListIsPage: true,
  deleteURL: "/tenant/tenantregistered",
  dataForm: {
    tenantName: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "tenantName",
    label: "合作商名称",
    minWidth: 120
  },
  {
    prop: "applicationType",
    label: "申请类型",
    minWidth: 120
  },
  {
    prop: "realName",
    label: "联系人姓名",
    minWidth: 120
  },
  {
    prop: "mobile",
    label: "联系人手机号",
    minWidth: 120
  },
  {
    prop: "status",
    label: "申请状态",
    minWidth: 120
  },
  {
    prop: "createDate",
    label: "申请时间",
    sortable: true,
    minWidth: 150
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 180
  }
]);


// 重置操作
const getResetting = () => {
  view.dataForm.tenantName = "";
  state.getDataList();
};

const teantAuditDetailRef = ref();
const teantAuditDetailKey = ref(0);
const auditHandle = async (id: number, type: string) => {
  teantAuditDetailKey.value++;
  await nextTick();
  teantAuditDetailRef.value.init(id, type);
};

const addOrUpdateRef = ref();
const addOrUpdateKey = ref(0);
const addOrUpdateHandle = async (id?: number) => {
  addOrUpdateKey.value++;
  await nextTick();
  addOrUpdateRef.value.init(id);
};

</script>
<style lang="less" scoped>

</style>