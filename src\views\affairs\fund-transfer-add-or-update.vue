<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '资金调拨' : '编辑'" :close-on-click-modal="false" :close-on-press-escape="false" width="900px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="100px">
      <!-- 收款信息 -->
      <div class="form-section">
        <h4 class="section-title">收款信息</h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="单据日期" prop="transferDate">
              <el-date-picker v-model="dataForm.transferDate" type="date" placeholder="请选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="作废备注" prop="voidRemark">
              <el-input v-model="dataForm.voidRemark" placeholder="请输入作废备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单据类型" prop="documentType">
              <el-radio-group v-model="dataForm.documentType">
                <el-radio label="1">借款</el-radio>
                <el-radio label="2">还款</el-radio>
                <el-radio label="3">资金调拨</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="转入人员" prop="transferPerson">
              <el-input v-model="dataForm.transferPerson" placeholder="请输入转入人员"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户名" prop="accountName">
              <el-input v-model="dataForm.accountName" placeholder="请输入户名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户余额" prop="accountBalance">
              <el-input v-model="dataForm.accountBalance" placeholder="请输入账户余额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注用途" prop="purpose">
              <el-input v-model="dataForm.purpose" placeholder="请输入备注用途"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="转入金额" prop="amount">
              <el-input-number v-model="dataForm.amount" :precision="2" :min="0" placeholder="请输入金额" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div style="text-align: center; margin-top: 30px;">
              <el-button type="primary" @click="addTransferItem">
                <el-icon><Plus /></el-icon>
                附件
              </el-button>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="转出账号" prop="transferOutNumber">
              <el-input v-model="dataForm.transferOutNumber" placeholder="请输入转出账号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户名1" prop="accountName1">
              <el-input v-model="dataForm.accountName1" placeholder="请输入户名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="转入账户1" prop="transferInAccount1">
              <el-input v-model="dataForm.transferInAccount1" placeholder="请输入转入账户"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="账户余额1" prop="accountBalance1">
              <el-input v-model="dataForm.accountBalance1" placeholder="请输入账户余额"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支付宝交易号" prop="alipayTradeNumber">
              <el-input v-model="dataForm.alipayTradeNumber" placeholder="请输入支付宝交易号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用途" prop="transferPurpose">
              <el-input v-model="dataForm.transferPurpose" placeholder="请输入用途"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="审核状态" prop="auditStatus">
              <el-input v-model="dataForm.auditStatus" placeholder="请输入审核状态"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  transferDate: "",
  transferNumber: "",
  transferPerson: "",
  accountName: "",
  accountType: "",
  accountPurpose: "",
  amount: 0,
  remark: "",
  attachment: "",
  transferOutNumber: "",
  accountName1: "",
  accountType1: "",
  accountPurpose1: "",
  alipayTradeNumber: "",
  summary: "",
  voidRemark: "",
  documentType: "3",
  accountBalance: "",
  purpose: "",
  transferInAccount1: "",
  accountBalance1: "",
  transferPurpose: "",
  auditStatus: "0"
});

const rules = ref({
  transferDate: [{ required: true, message: "单据日期不能为空", trigger: "change" }],
  transferPerson: [{ required: true, message: "转入人员不能为空", trigger: "blur" }],
  amount: [{ required: true, message: "转入金额不能为空", trigger: "blur" }],
  documentType: [{ required: true, message: "单据类型不能为空", trigger: "change" }]
});

const init = (id?: number) => {
  visible.value = true;

  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    transferDate: "",
    voidRemark: "",
    documentType: "3",
    transferPerson: "",
    accountName: "",
    accountBalance: "",
    purpose: "",
    amount: 0,
    remark: "",
    transferOutNumber: "",
    accountName1: "",
    transferInAccount1: "",
    accountBalance1: "",
    alipayTradeNumber: "",
    transferPurpose: "",
    auditStatus: ""
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get(`/finance/transfer/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    const request = !dataForm.id ?
      baseService.post("/finance/transfer", dataForm) :
      baseService.put("/finance/transfer", dataForm);

    request.then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 添加附件
const addTransferItem = () => {
  ElMessage.info("附件功能待开发");
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.form-section {
  margin-bottom: 20px;

  .section-title {
    margin: 0 0 15px 0;
    padding: 8px 12px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}
</style>
