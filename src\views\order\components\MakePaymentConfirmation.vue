<template>
  <el-drawer v-model="visible" title="打款信息" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%" class="ny-drawer">
    <el-card class="make-payment-confirm" v-loading="dataLoading">
      <div class="p-title mt-0">打款信息</div>
      <el-form label-position="top">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="付款金额(元)">
              <el-input v-model="payInfo.amount" placeholder="请输入付款金额" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款账号">
              <el-input v-model="payInfo.account" placeholder="请输入付款账号" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款单号">
              <el-input v-model="payInfo.orderCode" placeholder="请输入付款单号" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款方式">
              <el-input :value="payInfo.accountType == '3' ? '银行卡' : '支付宝'" placeholder="请输入付款方式" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="付款凭证">
          <el-image class="image" :src="payInfo.payImage" :preview-src-list="[payInfo.payImage]"></el-image>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" :disabled="!payInfo || !payInfo.amount" type="primary" @click="submitForm">确定打款无误</el-button>
    </template> -->
  </el-drawer>
</template>  

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);
const visible = ref(false);
// 订单id
const orderId = ref();
// 订单类型  sell: 销售订单
const orderType = ref("");
const init = (id: number, type?: any) => {
  orderId.value = id;
  orderType.value = type;
  visible.value = true;
  getPayInfo();
};

const dataLoading = ref(false);
// 查看打款信息
const payInfo = ref(<any>{});
const getPayInfo = async () => {
  dataLoading.value = true;
  let requestApi = orderType.value == "sell" ? "/sale/checkPayment/" : "/purchase/checkApplyPay/";
  let res = await baseService.get(requestApi + orderId.value);
  payInfo.value = res.data || {};
  dataLoading.value = false;
};

// 提交
const btnLoading = ref(false);
const submitForm = () => {
  btnLoading.value = true;
  let requestApi = orderType.value == "sell" ? `/sale/confirmPayment/${orderId.value}` : "/purchase/confirmApplyPay";
  baseService[orderType.value == "sell" ? "get" : "put"](requestApi, { orderId: orderId.value })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("提交成功");
        visible.value = false;
        emits("refresh");
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.make-payment-confirm {
  .image {
    width: 148px;
    height: 148px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e4e7ed;
  }
}
</style>