<template>
  <el-drawer v-model="visible" title="商品标记售后" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%" class="ny-drawer article-add-or-update">
    <el-card class="article-add-or-update-form" v-loading="dataLoading">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions class="descriptions-label-140" :column="2" border>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏大区">{{ resData.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="售价"
          ><el-text type="danger">￥{{ resData.transactionPrice || "-" }}</el-text></el-descriptions-item
        >
        <el-descriptions-item label="包赔费">￥{{ resData.guaranteeAmount || "-" }}</el-descriptions-item>
        <template v-if="!showLog">
          <el-descriptions-item label="创建时间" :span="2">{{ resData.createDate }}</el-descriptions-item>
          <el-descriptions-item label="商品类型" :span="2">{{ resData.accountSourceName }}</el-descriptions-item>
          <!-- todo  -->
          <el-descriptions-item label="打款时间">{{ resData.payTime || "-" }}</el-descriptions-item>
          <el-descriptions-item label="换绑时间">{{ resData.changeDate || "-" }}</el-descriptions-item>
          <!-- todo  -->
          <el-descriptions-item label="回收成功时间">{{ resData.dealDate || "-" }}</el-descriptions-item>
          <el-descriptions-item label="回收人">{{ resData.acquisitionName || "-" }}</el-descriptions-item>
          <el-descriptions-item label="卖方手机号" :span="2">{{ resData.phone || "-" }}</el-descriptions-item>
          <el-descriptions-item label="游戏密码" :span="2">
            <div class="flx-justify-between" v-if="resData.gamePassword">
              <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="买方手机号">{{ resData.tbUserPhone || "-" }}</el-descriptions-item>
        </template>
      </el-descriptions>
    </el-card>

    <el-card class="mt-12">
      <div class="p-title mt-0">标记售后</div>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="售后类型" prop="delistingCause">
              <ny-select v-model="dataForm.delistingCause" dict-type="after_sales_type" placeholder="请选择售后类型"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="售后过期时间" prop="expirationTime">
              <el-date-picker popper-class="date_picke" :disabled-date="disabledDate" v-model="dataForm.expirationTime" type="datetime" placeholder="请选择售后过期时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级原因" prop="subDelistingCause" v-if="dataForm.delistingCause == 'ACCOUNT_RETURN'">
              <ny-select v-model="dataForm.subDelistingCause" :dict-type="resData.saleStatus == '0' ? 'retrieve_type_unsold' : 'retrieve_type_sale'" placeholder="请选择二级原因"></ny-select>
            </el-form-item>
            <el-form-item label="其他原因备注" prop="saleAfterRemark" v-if="dataForm.delistingCause == 'OTHER'">
              <el-input v-model="dataForm.saleAfterRemark" placeholder="请输入其他原因备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="请选择处理人" prop="aftermarketProcessor">
              <ny-select-search v-model="dataForm.aftermarketProcessor" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择处理人" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题截图" prop="issueImg">
              <ny-upload v-model:imageUrl="dataForm.issueImg" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";

const emit = defineEmits(["refresh"]);

// 标记售后
const dataForm = ref(<any>{
  aftermarketProcessor: "",
  issueImg: ""
});
const visible = ref(false);

const orderId = ref("");

const init = (data: any, type: string) => {
  visible.value = true;
  resData.value = data;
  orderId.value = data.id;
  dataForm.value = data;
};
const disabledDate = (time: any) => {
  // 禁用今天之前的日期
  return time.getTime() < new Date().getTime();
};
const resData = ref(<any>{});
const dataLoading = ref(false);
// 是否显示游戏密码
const isShowGamePassword = ref(false);

const rules = reactive({
  delistingCause: [{ required: true, message: "请选择售后类型", trigger: "change" }],
  subDelistingCause: [{ required: true, message: "请选择二级原因", trigger: "change" }],
  aftermarketProcessor: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
  expirationTime: [{ required: true, message: "请选择过期时间", trigger: "change" }],
  issueImg: [{ required: true, message: "请上传问题截图", trigger: "change" }]
});

const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let data = JSON.parse(JSON.stringify(dataForm.value));
    data.status = 5;
    baseService
      .post("/shop/shop/signAfterSale", data)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          if (dataForm.value.delistingCause == "ACCOUNT_RETURN") {
            // 账号找回加黑名单
            addBlackData();
          }
          emit("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
const addBlackData = () => {
  baseService.post("/blacklist/blacklist", {
    id: null,
    account: resData.value.gameAccount,
    gameName: resData.value.gameName,
    idCard: "-",
    name: "-",
    phone: resData.value.phone,
    platform: "-",
    remarks: resultoptions.value[+dataForm.value.subDelistingCause - 1].label
  });
};
const resultoptions = ref([
  {
    label: "未售未放款被找回",
    value: "1"
  },
  {
    label: "未售已放款被找回",
    value: "2"
  },
  {
    label: "已售未放款被找回",
    value: "3"
  },
  {
    label: "已售已放款被找回",
    value: "4"
  },
  {
    label: "未售未放款疑似被找回",
    value: "5"
  },
  {
    label: "未售已放款疑似被找回",
    value: "6"
  },
  {
    label: "已售未放款疑似被找回",
    value: "7"
  },
  {
    label: "已售已放款疑似被找回",
    value: "8"
  }
]);
defineExpose({
  init
});
</script>
<style lang="scss">
.date_picke {
  .el-picker-panel__footer {
    display: none !important;
  }
}
</style>
