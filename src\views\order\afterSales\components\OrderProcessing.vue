<template>
  <el-drawer v-model="visible" :title="'售后订单处理'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="944" class="ny-drawer">
    <div class="order-processing-form">
      <el-card v-loading="dataLoading">
        <div class="p-title mt-0">基本信息</div>
        <el-descriptions class="descriptions-label-140" :column="2" border>
          <el-descriptions-item label="订单号">{{ resData.sn }}</el-descriptions-item>
          <el-descriptions-item label="商品编码">{{ resData.shopCode }}</el-descriptions-item>
          <el-descriptions-item label="游戏名称">{{ resData.gameName }}</el-descriptions-item>
          <el-descriptions-item label="游戏账号">{{ resData.gameAccount }}</el-descriptions-item>
          <el-descriptions-item label="回收价(元)">￥{{ resData.purchaseAmount }}</el-descriptions-item>
          <el-descriptions-item label="成交价(元)"
            ><el-text type="danger">￥{{ resData.dealAmount }}</el-text></el-descriptions-item
          >
          <el-descriptions-item label="包赔费">￥{{ resData.fee || "-" }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ resData.createDate }}</el-descriptions-item>
          <el-descriptions-item label="打款时间">{{ resData.payTime }}</el-descriptions-item>
          <el-descriptions-item label="回收成功时间">{{ resData.dealDate }}</el-descriptions-item>
          <el-descriptions-item label="出售类型">{{ resData.saleType }}</el-descriptions-item>
          <el-descriptions-item label="回收人" :span="2">{{ resData.acquisitionName }}</el-descriptions-item>
          <!-- <el-descriptions-item label="卖方手机号" :span="2">{{ resData.sellerPhone }}</el-descriptions-item> -->
          <el-descriptions-item label="游戏密码" :span="2">
            <div class="flx-justify-between" v-if="resData.gamePassword">
              <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="售后类型">{{ resData.saleAfterType }}</el-descriptions-item>
          <el-descriptions-item label="账号来源">{{ resData.orderSource == "出售订单" ? "销售订单" : resData.orderSource }}</el-descriptions-item>
          <el-descriptions-item v-if="resData.saleAfterType == '其他'" :span="2" label="其他原因备注">{{ resData.saleAfterRemark || "-" }}</el-descriptions-item>
          <el-descriptions-item label="问题截图" :span="2">
            <el-image class="srceenshot" style="width: 54px; height: 54px" :src="resData.saleAfterPics" :preview-src-list="[resData.saleAfterPics]"></el-image>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <!-- 拒绝&&，卖家未提交审核时 -->
        <el-card v-if="dataForm.salesAfterAudit == 2 || dataForm.salesAfterAudit == null" class="mt-12 cardDescriptions" style="padding: 0">
          <!-- 平台直售  第三方和其他商家售后处理 -->
          <!-- 已支付 取消订单 售后处理 -->
          <div class="p-title mt-0">{{ resData.saleType == "平台直售" ? "第三方和其他商家售后处理" : "售后处理" }}</div>
          <el-row class="descriptions-label-140" :column="2" border>
            <el-col :span="12" v-if="dataForm.salesAfterAudit == 2">
              <el-form-item label="审批结果">
                <el-tag type="danger">审批已拒绝，请重新提交</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item :class-name="dataForm.salesAfterTypeCode == 'RETURN_ALL' || dataForm.salesAfterTypeCode == 'RETURN_PART' ? '' : 'noneSelfRight'">
              <template #label
                ><span>售后结果<span style="color: red">*</span></span></template
              >
              <el-form-item label="售后结果" prop="salesAfterTypeCode">
                <el-select v-model="dataForm.salesAfterTypeCode" filterable placeholder="请选择售后结果" @change="saleAfterResultChange">
                  <el-option v-for="(item, index) in afterSalesResults" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <template v-if="dataForm.salesAfterTypeCode == 'RETURN_ALL' || dataForm.salesAfterTypeCode == 'RETURN_PART'">
              <el-descriptions-item>
                <template #label
                  ><span>退款金额(元)<span style="color: red">*</span></span></template
                >
                <el-form-item label="退款金额(元)" prop="salesAfterRefund">
                  <el-input v-model="dataForm.salesAfterRefund" placeholder="请输入退款金额"></el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label
                  ><span>收款账户<span style="color: red">*</span></span></template
                >
                <el-form-item label="收款账户" prop="salesAfterAccountId">
                  <el-select v-model="dataForm.salesAfterAccountId" filterable placeholder="请选择收款账户" @change="saleAccountChange">
                    <el-option v-for="(item, index) in accountList" :key="index" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label
                  ><span>支付宝订单号</span></template
                >
                <el-form-item label="支付宝订单号" prop="alipayOrderNo">
                  <el-input v-model="dataForm.alipayOrderNo" placeholder="请输入支付宝订单号" />
                </el-form-item>
              </el-descriptions-item>
            </template>
            <el-descriptions-item v-else label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
            <el-descriptions-item :class-name="dataForm.salesAfterTypeCode == 'RETURN_ALL' || dataForm.salesAfterTypeCode == 'RETURN_PART' ? '' : 'noneSelfRight'">
              <template #label
                ><span>售后处理记录<span style="color: red">*</span></span></template
              >
              <el-form-item label="售后处理记录" prop="salesAfterPics" style="margin-bottom: 0px">
                <ny-upload-file
                  uploadPreText="将图片拖到此处或"
                  :limit="20"
                  listType="picture-card"
                  v-model:fileSrc="dataForm.salesAfterPics"
                  tip="<div style='font-weight: 400;font-size: 12px;color: #909399;line-height: 20px;'>未买包赔:担保信息图、通话记录图、群聊记录图、要回金额图等。<div>已买包赔:群聊记录图、要回金额图等。</div></div>"
                  accept="image/*"
                ></ny-upload-file>
              </el-form-item>
            </el-descriptions-item>
            <template v-if="dataForm.salesAfterTypeCode == 'RETURN_ALL' || dataForm.salesAfterTypeCode == 'RETURN_PART'">
              <el-descriptions-item>
                <template #label
                  ><span>付款凭证<span style="color: red">*</span></span></template
                >
                <el-form-item label="付款凭证" prop="sellersPayImage">
                  <ny-upload :limit="1" v-model:imageUrl="dataForm.sellersPayImage" tip=" " accept="image/*"></ny-upload>
                </el-form-item>
              </el-descriptions-item>
            </template>
            <el-descriptions-item v-else label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template #label><span>备注</span></template>
              <el-form-item label="备注">
                <el-input v-model="dataForm.salesAfterRemark" type="textarea" :rows="3" maxlength="255" show-word-limit placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card v-else class="mt-12">
          <div class="p-title mt-0">卖家售后处理</div>
          <el-descriptions class="descriptions-label-140" :column="2" border>
            <el-descriptions-item label="售后结果">{{ resData.salesAfterType }}</el-descriptions-item>
            <template v-if="resData.salesAfterType && resData.salesAfterType.includes('赔付')">
              <el-descriptions-item label="退款金额(元)">{{ resData.salesAfterRefund }}</el-descriptions-item>
              <el-descriptions-item label="收款账户" :span="2">{{ resData.salesAfterAccountName || "-" }}</el-descriptions-item>
            </template>
            <el-descriptions-item label="售后处理记录" :span="2">
              <el-image v-for="item in resData.salesAfterPics" :key="item" class="srceenshot" style="width: 54px; height: 54px; margin-right: 4px" :src="item" :preview-src-list="resData.salesAfterPics"></el-image>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ resData.salesAfterRemark || "-" }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 已售 && 卖家通过时 展示 -->
        <template v-if="resData.shopState == '1' && dataForm.salesAfterAudit == 1">
          <!-- 拒绝&&，买家未提交审核时 -->
          <el-card v-if="dataForm.buyersAudit == 2 || resData.buyersTypeCode == null" class="mt-12">
            <div class="p-title mt-0">买家售后处理</div>
            <el-row :gutter="12">
              <el-col :span="12" v-if="dataForm.buyersAudit == 2">
                <el-form-item label="审批结果">
                  <el-tag type="danger">审批已拒绝，请重新提交</el-tag>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <!-- 审核通过时，默认展示全额退款   买家全额-审核-->
                <el-form-item label="售后结果" prop="buyersTypeCode">
                  <el-select v-model="dataForm.buyersTypeCode" filterable placeholder="请选择售后结果" @change="buyAfterResultChange">
                    <el-option v-for="(item, index) in afterSalesResults" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <template v-if="dataForm.buyersTypeCode == 'RETURN_ALL' || dataForm.buyersTypeCode == 'RETURN_PART'">
                <el-col :span="12">
                  <el-form-item label="补偿金额(元)" prop="buyersRefund">
                    <el-input v-model="dataForm.buyersRefund" placeholder="请输入补偿金额"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="收款方账户类型" prop="buyersAccountType">
                    <el-radio-group v-model="dataForm.buyersAccountType">
                      <el-radio label="3">银行卡</el-radio>
                      <el-radio label="1">支付宝</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账号名称" prop="buyersAccountName">
                    <el-input v-model="dataForm.buyersAccountName" placeholder="请输入账户名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账号" prop="buyersAccount">
                    <el-input v-model="dataForm.buyersAccount" placeholder="请输入账号"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="dataForm.buyersAccountType === '3'">
                  <el-form-item label="开户行名称" prop="buyersAccountBank">
                    <el-input v-model="dataForm.buyersAccountBank" placeholder="请输入开户行名称"></el-input>
                  </el-form-item>
                </el-col>
              </template>
              <el-col :span="24">
                <el-form-item label="售后处理记录" prop="buyersPics" style="margin-bottom: 0px">
                  <ny-upload-file
                    listType="picture-card"
                    uploadPreText="将图片拖到此处或"
                    v-model:fileSrc="dataForm.buyersPics"
                    :limit="20"
                    tip="<div style='font-weight: 400;font-size: 12px;color: #909399;line-height: 20px;'>未买包赔:担保信息图、通话记录图、群聊记录图、要回金额图等。<div>已买包赔:群聊记录图、要回金额图等。</div></div>"
                    accept="image/*"
                  ></ny-upload-file>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input v-model="dataForm.buyersRemark" type="textarea" :rows="3" maxlength="255" show-word-limit placeholder="请输入备注"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-else class="mt-12">
            <div class="p-title mt-0">买家售后处理</div>
            <el-descriptions class="descriptions-label-140" :column="2" border>
              <el-descriptions-item label="售后结果">{{ resData.buyersType }}</el-descriptions-item>
              <template v-if="resData.buyersType && resData.buyersType.includes('赔付')">
                <el-descriptions-item label="退款金额(元)">{{ resData.buyersRefund }}</el-descriptions-item>
                <el-descriptions-item label="账号名称" :span="2">{{ resData.buyersAccountName || "-" }}</el-descriptions-item>
              </template>
              <el-descriptions-item label="售后处理记录" :span="2">
                <el-image v-for="item in resData.buyersPics" :key="item" class="srceenshot" style="width: 54px; height: 54px; margin-right: 4px" :src="item" :preview-src-list="resData.buyersPics"></el-image>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ resData.buyersRemark || "-" }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </template>
        <!-- 流程进度 -->
        <el-card class="mt-12" v-if="processingData.length > 0">
          <div class="basicInfoSty">
            <div class="titleSty">流程进度</div>
            <el-scrollbar max-height="600px">
              <el-steps direction="vertical" :active="payStatus == 2 ? activeLength + 1 : activeLength">
                <template :key="index_" v-for="(activity, index_) in processingData">
                  <el-step title="申请人" :key="index_" v-if="activity.taskIndex == 1">
                    <template #description>
                      <div class="cardInfo">
                        <div>
                          申请人：<span style="color: var(--el-color-primary)">{{ activity.creatorName }}</span>
                        </div>
                        <div>
                          申请时间：
                          <span>{{ formatTimeStamp(activity.createDate) }}</span>
                        </div>
                      </div>
                    </template>
                  </el-step>
                  <el-step v-else :title="activity.name">
                    <template #description>
                      <div class="cardInfo">
                        <div>
                          审批人：<span style="color: var(--el-color-primary)">{{ getUserName(activity.stages) }}</span>
                        </div>
                        <div>
                          处理时间：
                          <span>{{ formatTimeStamp(activity.createDate) || "-" }}</span>
                        </div>
                        <template v-if="+activity.status > 0">
                          <div style="margin-top: 6px">
                            最终审批人：<span>{{ activity.userName || "-" }}</span>
                          </div>
                          <div>
                            审批结果：
                            <span :style="{ color: +activity.status == 1 ? '#67C23A' : '#F56C6C' }">{{ ["", "审核通过", "审核拒绝"][+activity.status] }}</span>
                          </div>
                          <div v-if="activity.status == 2">
                            拒绝原因：<span>{{ activity.remarks }}</span>
                          </div>
                        </template>
                      </div>
                    </template>
                  </el-step>
                </template>
                <el-step v-if="payStatus != 0" title="支付状态">
                  <template #description>
                    <div class="cardInfo">
                      <div :style="{ color: +payStatus == 2 ? '#67C23A' : '#F56C6C' }">{{ ["", "未付款", "已付款"][+payStatus] }}</div>
                    </div>
                  </template>
                </el-step>
              </el-steps>
            </el-scrollbar>
          </div>
        </el-card>
      </el-form>
    </div>
    <template #footer>
      <template v-if="showBtn">
        <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
        <el-button :loading="btnLoading" v-if="resData.salesAfterAudit == 2 || resData.salesAfterTypeCode == null || (resData.salesAfterAudit == 1 && (resData.buyersAudit == 2 || resData.buyersTypeCode == null))" type="primary" @click="submitForm">确定</el-button>
      </template>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { useAppStore } from "@/store";
const store = useAppStore();
const emits = defineEmits(["refresh"]);

const visible = ref(false);
// 0 不需要显示，1 显示待付款，2 显示已付款
const payStatus = ref(0);
// 售后结果
const afterSalesResults = [
  { label: "赔付(全额退款)", value: "RETURN_ALL" },
  { label: "赔付(部分退款)", value: "RETURN_PART" },
  { label: "还号", value: "RETURN_ACCOUNT" },
  { label: "换号", value: "EXCHANGE_ACCOUNT" },
  { label: "退号", value: "GIVE_BACK" },
  { label: "不予处理", value: "REJECT" },
  { label: "其他", value: "OTHER" }
];
const resData = ref(<any>{});
const dataLoading = ref(false);
const showBtn = ref(false);
// 是否显示游戏密码
const isShowGamePassword = ref(false);

// 标记售后
const dataForm = ref(<any>{});

const rules = reactive({
  salesAfterTypeCode: [{ required: true, message: "请选择售后结果", trigger: "change" }],
  buyersTypeCode: [{ required: true, message: "请选择售后结果", trigger: "change" }],
  salesAfterRefund: [{ required: true, message: "请输入退款金额", trigger: "blur" }],
  buyersRefund: [{ required: true, message: "请输入退款金额", trigger: "blur" }],
  salesAfterPics: [{ required: true, message: "请上传处理记录", trigger: "change" }],
  buyersPics: [{ required: true, message: "请上传处理记录", trigger: "change" }],
  salesAfterAccountType: [{ required: true, message: "请选择收款方账户类型", trigger: "change" }],
  buyersAccountType: [{ required: true, message: "请选择收款方账户类型", trigger: "change" }],
  salesAfterAccountName: [{ required: true, message: "请输入账户名称", trigger: "change" }],
  buyersAccountName: [{ required: true, message: "请输入账户名称", trigger: "change" }],
  salesAfterAccountId: [{ required: true, message: "请选择账户", trigger: "change" }],
  salesAfterAccount: [{ required: true, message: "请输入账号", trigger: "change" }],
  buyersAccount: [{ required: true, message: "请输入账号", trigger: "change" }],
  // alipayOrderNo: [{ required: true, message: "请输入支付宝订单号", trigger: "change" }],
  sellersPayImage: [{ required: true, message: "请选择付款凭证", trigger: "change" }]
});

const init = (data?: any) => {
  resData.value = {};
  dataForm.value = {};
  showBtn.value = false;
  // 赋值
  visible.value = true;
  dataForm.value.orderId = data.id;
  dataForm.value.shopId = data.shopId;
  dataForm.value.salesAfterAccountType = "1";
  dataForm.value.salesAfterAudit = null;
  resData.value = data;
  getDetail();
  getAccount();
};

const processingData = ref(<any>{});
const activeLength = ref(0);
const getDetail = () => {
  processingData.value = [];
  activeLength.value = 0;
  baseService.get("/order/sysorderdisposedatainfo/info", { orderId: dataForm.value.orderId, orderDisposeType: "SAO" }).then((res) => {
    resData.value = Object.assign(resData.value, res.data?.data);
    resData.value.buyersPics = resData.value.buyersPics ? resData.value.buyersPics.split(",") : [];
    resData.value.salesAfterPics = resData.value.salesAfterPics ? resData.value.salesAfterPics.split(",") : [];
    dataForm.value = Object.assign(dataForm.value, res.data?.data);
    dataForm.value.orderDisposeDataInfoId = dataForm.value.id ? dataForm.value.id : undefined;
    // console.log(resData.value);
    showBtn.value = true;
    delete dataForm.value.id;
    // 处理流程数据
    let arr = <any>[];
    let flowArr = [];
    if (res.data?.buyersAuditData && res.data?.buyersAuditData.length > 0 && res.data?.buyersAudit != 1) {
      flowArr = res.data?.buyersAuditData;
      payStatus.value = 1;
      // 未进入买家处理是显示卖家
    } else if (resData.value.buyersType == null && resData.value?.salesAfterAudit != 1 && res.data?.sellAuditData && res.data?.sellAuditData.length > 0) {
      flowArr = res.data?.sellAuditData;
    }
    if (flowArr.length > 0) {
      flowArr.forEach((ele: any, index: number) => {
        ele.tasks = [
          {
            createDate: ele.createDate,
            creatorName: ele.creatorName,
            taskIndex: 1
          },
          ...ele.tasks
        ];
        arr = [...arr, ...ele.tasks];
        // 退款已付款 流程最后一步
        if (flowArr.some((f: any) => f.type == 2 && f.payStatus == 1)) {
          payStatus.value = 2;
        }
        ele.tasks?.forEach((ele_: any) => {
          if (index != flowArr.length - 1 || (index == flowArr.length - 1 && +ele_.status > 0)) {
            activeLength.value++;
          }
        });
      });
    }
    processingData.value = arr;
  });
};
// 卖家账号下拉
const accountList = ref([]);
const getAccount = () => {
  baseService.get("/wallet/tenantaccount/list", { tenantCode: store.state.user.tenantCode }).then((res) => {
    accountList.value = res?.data || [];
  });
};
const saleAccountChange = (e) => {
  if (!e) {
    dataForm.value.salesAfterAccountType = undefined;
    dataForm.value.salesAfterAccountName = undefined;
    dataForm.value.salesAfterAccount = undefined;
    return;
  }
  let obj = accountList.value.find((ele) => ele.id == dataForm.value.salesAfterAccountId);
  dataForm.value.salesAfterAccountType = obj?.type;
  dataForm.value.salesAfterAccountName = obj?.name;
  dataForm.value.salesAfterAccount = obj?.account;
};
// 选择售后结果
const saleAfterResultChange = () => {
  if (dataForm.value.salesAfterTypeCode == "RETURN_ALL" || dataForm.value.salesAfterTypeCode == "RETURN_PART") {
    dataForm.value.salesAfterRefund = resData.value.saleAmount;
  } else {
    dataForm.value.salesAfterRefund = undefined;
  }
};
// 选择售后结果
const buyAfterResultChange = () => {
  if (dataForm.value.buyersTypeCode == "RETURN_ALL" || dataForm.value.buyersTypeCode == "RETURN_PART") {
    dataForm.value.buyersRefund = resData.value.saleAmount;
  } else {
    dataForm.value.buyersRefund = undefined;
  }
};

const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    let form_ = { ...dataForm.value };
    delete form_.buyersAudit;
    delete form_.salesAfterAudit;
    btnLoading.value = true;
    baseService.post("/saleAfter/processNew", form_).then((res) => {
      if (res.code == 0) {
        // 卖家处理不涉及金额&&已售&&买家没处理
        if (resData.value.shopState == "1" && dataForm.value.salesAfterTypeCode != "RETURN_ALL" && dataForm.value.salesAfterTypeCode != "RETURN_PART" && (dataForm.value.buyersTypeCode == null || !dataForm.value.buyersTypeCode)) {
          ElMessage.success("卖家提交成功，请处理买家售后信息！");
          emits("refresh");
          let obj = dataForm.value;
          dataForm.value = {};
          dataForm.value.orderId = obj.orderId;
          dataForm.value.shopId = obj.shopId;
          getDetail();
          btnLoading.value = false;
        } else {
          ElMessage.success("处理成功");
          emits("refresh");
          visible.value = false;
          btnLoading.value = false;
        }
      }
    });
  });
};

const getUserName = (arr: any) => {
  return arr?.map((ele) => ele.stageName).join("，");
};

defineExpose({
  init
});
</script>

<style lang="scss">
.el-descriptions__content {
  display: flex;
  align-items: center;
}
.order-processing-form {
  .screenshots {
    width: 96px;
    height: 96px;
    border-radius: 4px;
  }

  .ny-steps {
    .el-step__icon {
      background: #f0f2f5;
      border-color: #f0f2f5;
      color: #909399;
    }

    .el-step__head.is-finish .el-step__icon {
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: #fff;
    }

    .el-step__title {
      font-size: 14px;
    }

    .step-desc-card {
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #ebeef5;
      padding: 12px;
      margin: 4px 0 10px 0;
      font-size: 12px;
      line-height: 16px;
      color: #606266;

      .text {
        padding: 2px 0;

        .el-text {
          font-size: 12px;
        }
      }
    }
  }
}
.loadingSty {
  .el-loading-spinner .circular {
    display: none;
  }
}
.basicInfoSty {
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}

.el-step__head {
  &.is-process,
  &.is-finish {
    .el-step__icon {
      background: var(--el-color-primary);
      color: #fff;
      border-color: #fff;
      .el-step__icon-inner {
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 14px;
      }
    }
  }
  .el-step__icon {
    background: #f0f2f5;
    color: #909399;
    border-color: #fff;
    .el-step__icon-inner {
      font-weight: 400;
      font-size: 12px;
      color: #909399;
      line-height: 14px;
    }
  }

  .el-step__line {
    width: 1px;
    .el-step__line-inner {
      border: 1px solid #c0c4cc;
      border-width: 0px !important;
    }
  }
}
.el-step__main .el-step__title {
  font-family: OPPOSans, OPPOSans;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 24px;
}
.cardInfo {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ebeef5;
  padding: 16px 12px;
  font-family: OPPOSans, OPPOSans;
  font-weight: 400;
  font-size: 12px;
  color: #606266;
  line-height: 16px;
  margin-bottom: 10px;
  > div {
    margin-bottom: 2px;
  }
}
</style>
