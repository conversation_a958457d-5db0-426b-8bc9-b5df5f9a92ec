<template>
  <div class="supplier" style="margin-bottom: 12px">
    <div class="above">
      <div class="left">
        <span class="title">员工KPI管理</span>
      </div>
      <div class="right">
        <div class="stock_top_item">
          <div class="but">
            <span
              @click="
                state.timeType = item.value;
                handleDateRange();
              "
              :class="{ active: state.timeType == item.value }"
              :key="item.value"
              v-for="item in [
                { label: '本周', value: '1' },
                { label: '本月', value: '2' },
                { label: '本年', value: '3' }
              ]"
              >{{ item.label }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <ny-flod-tab
      :list="[
        { label: '出售统计', value: '1' },
        { label: '回收统计', value: '0' }
      ]"
      class="newTabSty"
      v-model="state.params.type"
      value="value"
      label="label"
      @change="getData"
    >
    </ny-flod-tab>
    <el-table :data="tableData" border style="width: 100%; margin-top: 12px">
      <el-table-column prop="rank" label="排名" align="center" width="60">
        <template #default="{ row, $index }">
          <img src="../../assets/images/ranking1.png" class="img" v-if="$index == 0 && state.params.page == 1" />
          <img src="../../assets/images/ranking2.png" class="img" v-else-if="$index == 1 && state.params.page == 1" />
          <img src="../../assets/images/ranking3.png" class="img" v-else-if="$index == 2 && state.params.page == 1" />
          <span v-else>{{ state.params.limit * (state.params.page - 1) + 1 + $index }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="员工姓名" width="90" align="center" />
      <template v-if="state.params.type == '1'">
        <el-table-column prop="saleNums" label="销售量" width="70" align="center" />
        <el-table-column prop="saleAmounts" label="销售额(元)" align="center" />
        <el-table-column prop="porfitAmounts" label="利润(元)" align="center"> </el-table-column>
      </template>
      <template v-else>
        <el-table-column prop="purchaseNums" label="回收量" align="center" />
        <el-table-column prop="purchaseAmounts" label="成本价(元)" align="center" />
      </template>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <div class="flx-center">
      <el-pagination :hide-on-single-page="true" v-model:current-page="state.params.page" @change="getData" layout="prev, pager, next" :total="state.totalCount" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
const state = reactive({
  timeType: "1",
  totalCount: 0,
  params: {
    startTime: undefined,
    endTime: undefined,
    type: "1",
    page: 1,
    limit: 10
  }
});
const tableData = ref([]);
/**
 * 2024-12-17
 * 处理时间范围
 */
const handleDateRange = () => {
  const now = new Date();
  if (state.timeType == "1") {
    const dayOfWeek = now.getDay(); // 0 (Sunday) to 6 (Saturday)
    const startDate = new Date(now);
    // 调整为本周的第一天（周一）
    startDate.setDate(startDate.getDate() - dayOfWeek + 1);
    state.params.startTime = formatDate(startDate, "YYYY-MM-DD");
  } else if (state.timeType == "2") {
    let startDate = formatDate(new Date(), "YYYY-MM");
    state.params.startTime = startDate + "-01";
  } else if (state.timeType == "3") {
    let startDate = formatDate(new Date(), "YYYY");
    state.params.startTime = startDate + "-01-01";
  }
  let endDate = formatDate(new Date(), "YYYY-MM-DD");
  state.params.startTime = state.params.startTime + " 00:00:00";
  state.params.endTime = endDate + " 23:59:59";
  getData();
};
// 获取table数据
const getData = () => {
  tableData.value = [];
  baseService
    .get("/console/kpi", {
      ...state.params
    })
    .then((res) => {
      tableData.value = res.data.list || [];
      state.totalCount = res.data?.total || 0;
    });
};
onMounted(() => {
  handleDateRange();
  getData();
});
</script>

<style lang="less" scoped>
.supplier {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.img {
  height: 30px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.stock_top_item {
  display: flex;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
  }
  span {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .but {
    display: flex;
    span {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 12px;
      cursor: pointer;
    }
    .active {
      color: var(--el-color-primary);
    }
  }
}

.stock_button {
  padding: 4px 16px 12px 16px;
  display: flex;
  align-items: center;
  .stock_button_count {
    width: 40%;
    .number {
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
