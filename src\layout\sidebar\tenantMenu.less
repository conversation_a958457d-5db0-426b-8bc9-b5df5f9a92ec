.rr-sidebar {
    .tenant-menu{
        .el-menu{
            
            border-radius: 0px;
        }
        .el-sub-menu{
            
            .el-sub-menu__title{
                height: 56px;
                
            }
            .el-menu--inline{
                
                display: flex;
                // flex-direction: column;
                padding: 0px 0px !important;
                .el-menu-item{
                    height: 56px;
                    width: 100%;
                    // border: 1px solid red;
                    a{
                        width: 100%;
                        height: 56px;
                        display: flex;
                        align-items: center;
                        padding: 0px 35px;
                        color: #303133;
                    }
                }
                .is-active{
                    
                    background-color: var(--el-menu-active-color) !important;
                    a{
                        color: #FFFFFF !important;
                    }
                }
            }
        }
    }
}