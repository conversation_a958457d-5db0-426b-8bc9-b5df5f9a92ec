<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '包赔服务管理', value: '包赔服务管理' },
          { label: '包赔信息收集', value: '包赔信息收集' }
        ]"
        v-model="activeName"
        value="value"
        label="label"
      ></ny-flod-tab>
      <template v-if="activeName == '包赔服务管理'">
        <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
          <template #header>
            <el-button type="primary" v-if="state.hasPermission('shop:guarantee:save')" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            <el-button type="danger" v-if="state.hasPermission('shop:guarantee:delete')" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          </template>
          <template #header-right>
            <div>
              <el-input v-model="view.dataForm.title" placeholder="请输入名称" :prefix-icon="Search" style="width: 220px" />
              <el-select v-model="view.dataForm.type" placeholder="请输入包赔类型" style="width: 220px; margin: 0px 12px">
                <el-option label="包赔权益" :value="1" />
                <el-option label="增值包赔" :value="2" />
              </el-select>
              <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="" />
            </div>

            <el-button type="primary" @click="queryFn">查询</el-button>
            <el-button @click="resetFn">重置</el-button>
          </template>
          <template #type="{ row }">
            <span v-if="row.type == 1">包赔权益</span>
            <span v-if="row.type == 2">增值包赔</span>
          </template>
          <template #createDate="{ row }">
            <span>{{ formatTimeStamp(row.createDate) }}</span>
          </template>
          <template #updateDate="{ row }">
            <span>{{ formatTimeStamp(row.updateDate) }}</span>
          </template>
          <template #operation="{ row }">
            <el-button v-if="state.hasPermission('shop:guarantee:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)" size="small">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('shop:guarantee:delete')" type="danger" text bg @click="state.deleteHandle(row.id)" size="small">{{ $t("delete") }}</el-button>
          </template>
        </ny-table>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
      </template>
      <template v-if="activeName == '包赔信息收集'">
        <guaranteeInfo></guaranteeInfo>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./compensationServe-add-or-update.vue";
import guaranteeInfo from "./guaranteeInfo.vue";
import { formatTimeStamp } from "@/utils/method";
import { Search } from "@element-plus/icons-vue";
import { getDictDataList } from "@/utils/utils";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
import { useI18n } from "vue-i18n";
const store = useAppStore();
const { t } = useI18n();

const timeInterval = ref(); // 时间区间
const activeName = ref("包赔服务管理");

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "title",
    label: "名称",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "type",
    label: "类型",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "info",
    label: "包赔说明",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "rate",
    label: "费率(%)",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "maxIndemnityRate",
    label: "最高赔付(%)",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "sort",
    label: "排序",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "120"
  }
]);

const view = reactive({
  getDataListURL: "/shop/guarantee/page",
  getDataListIsPage: true,
  deleteURL: "/shop/guarantee",
  deleteIsBatch: true,
  dataForm: {
    title: "", // 名称
    type: "", // 包赔类型
    start: "", // 开始时间
    end: "" // 结束时间
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 查询
const queryFn = () => {
  view.dataForm.start = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.end = timeInterval.value ? timeInterval.value[1] : "";
  state.getDataList();
};
// 重置
const resetFn = () => {
  view.dataForm.title = "";
  view.dataForm.type = "";
  view.dataForm.start = "";
  view.dataForm.end = "";
  timeInterval.value = "";
  state.getDataList();
};
</script>

<style lang="less" scoped></style>
