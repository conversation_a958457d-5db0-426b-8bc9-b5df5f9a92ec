import { color } from "echarts";

// 柱状图 - 默认
export const option1 = {
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
    },
    legend: {
        show: true,
        top: 'top',
        orient: 'horizontal'
    },
    grid: {
        left: '8%',
        right: '8%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: []
    },
    yAxis: {
        type: 'value',
        axisLabel: {
          // 使用 formatter 自定义标签显示
          formatter: function(value:any) {
            // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
            if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万';
            }
            return value;
          }
        }
    },
    series: <any>[],
    dataZoom: [{
        type: 'slider', 
        start: 0,
        end: 100,
        bottom:"6%",
        height: 15
    }]
}

// 柱状图 - 堆叠
export const option2 = {
    index: 2,
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
    },
    legend: {
        show: true,
        top: 'top',
        orient: 'horizontal'
    },
    grid: {
        left: '8%',
        right: '8%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: []
    },
    yAxis: {
        type: 'value',
        axisLabel: {
          // 使用 formatter 自定义标签显示
          formatter: function(value:any) {
            // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
            if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万';
            }
            return value;
          }
        }
    },
    series: <any>[],
    dataZoom: [{
        type: 'slider', 
        start: 0,
        end: 100,
        bottom:"6%",
        height: 15,
    }]
}

// 斜线图 - 基础
export const option3 = {
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      data: <any>[], // 图例名称
    },
    grid: {
      left: '8%',
      right: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        // 使用 formatter 自定义标签显示
        formatter: function(value:any) {
          // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
          if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万';
          }
          return value;
        }
      }
    },
    series: <any>[],
    dataZoom: [{
        type: 'slider', 
        start: 0,
        end: 100,
        bottom:"6%",
        height: 15,
    }]
};

// 斜线图 - 平滑
export const option4 = {
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      data: <any>[], // 图例名称
    },
    grid: {
      left: '8%',
      right: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        // 使用 formatter 自定义标签显示
        formatter: function(value:any) {
          // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
          if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万';
          }
          return value;
        }
      }
    },
    series: <any>[],
    dataZoom: [{
        type: 'slider', 
        start: 0,
        end: 100,
        bottom:"6%",
        height: 15,
    }]
};


// 饼图 - 基础
export const option5 = {
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
        trigger: 'item'
    },
    legend: {
        orient: 'horizontal',
        left: 'center',
        show: true,
    },
    series: [
        {
          name: '',
          type: 'pie',
          radius: '50%',
          data: [],
          label: {
            normal: {
              formatter: '{b}({d}%)',
              show: false,
            },
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
        }
    ]
}

// 饼图 - 环形
export const option6 = {
    color: ['#4165D7', '#E6A23C', '#14C0FF', '#FFC60A', '#34C624', '#8E949D'],
    tooltip: {
        trigger: 'item'
    },
    legend: {
        top: '5%',
        left: 'center',
        show: true,
    },
    series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            normal: {
              formatter: '{b}({d}%)',
              show: false,
            },
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
    ]
}

// 主题色
export const ThemeColor = [
    {name:'主题1',value:[{color:'#4165D7'},{color:'#E6A23C'},{color:'#14C0FF'},{color:'#FFC60A'},{color:'#34C624'},{color:'#8E949D'}]},
    {name:'主题2',value:[{color:'#4165D7'},{color:'#50CEFB'},{color:'#14E1C6'},{color:'#62D256'},{color:'#C3DD40'},{color:'#FAD355'}]},
    {name:'主题3',value:[{color:'#4165D7'},{color:'#50CEFB'},{color:'#935AF6'},{color:'#FAD355'},{color:'#F76964'},{color:'#FFA53D'}]},
]

// 根据类型判断返回参数值
export const isTypeObject = (type:any) =>{
  switch (type) {
    case "option1":
        return {
            data: [],
            name: '',
            type: 'bar',
            label:{
              show:false,
              position: 'top',
            }
        }
    case "option2":
        return {
            data: [],
            name: '',
            type: 'bar',
            stack: 'Ad' // 堆叠
        }
    case "option3":
        return {
          name: '',
          type: 'line',
          stack: 'Total',
          label: {
            show: false,
            position: 'top'
          },
          data: []
        }
    case "option4":
        return {
          name: '',
          type: 'line',
          stack: 'Total',
          smooth: true,
          label: {
            show: false,
            position: 'top'
          },
          data: []
        }
}
}