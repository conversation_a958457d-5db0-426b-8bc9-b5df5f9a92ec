<template>
  <div>
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab class="newTabSty" :list="gamesList" v-model="dataForm.gameId" value="id" label="title" @change="flodTabChange"></ny-flod-tab>
      <el-alert :title="alertText" show-icon type="info" close-text="我已了解" style="margin-bottom: 12px" />
      <div class="flx-justify-between" style="margin-top: 12px; border-bottom: 1px solid #dcdfe6">
        <div style="display: flex; align-items: center">
          <el-tabs v-model="dataForm.selectId" type="card" class="demo-tabs" closable @tab-remove="handleTabsRemove" @tab-change="handleTabsChange">
            <el-tab-pane v-for="item in editableTabs" :key="item.id" :label="item.title" :name="item.id">
              <template #label>
                <span @dblclick="addOrEditHandle('edit', item)">{{ item.title }}</span>
              </template>
            </el-tab-pane>
          </el-tabs>
          <el-button style="margin-left: 8px; width: 20px; height: 20px; padding: 0" :icon="Plus" size="small" @click="addOrEditHandle('add')"> </el-button>
        </div>
        <div style="display: flex; align-items: center">
          <el-select v-model="dataForm.partnerId" placeholder="请选择商户" clearable style="width: 240px; margin-right: 12px">
            <el-option v-for="item in PartnerList" :key="item.tenantCode" :label="item.tenantName" :value="item.tenantCode" />
          </el-select>

          <el-button type="primary" @click="getDataList()">查询</el-button>
          <el-button
            @click="
              dataForm.partnerId = null;
              getDataList();
            "
            >重置</el-button
          >
        </div>
      </div>
      <div class="flx" style="margin: 16px 0; align-items: center">
        <el-button type="warning" @click="copyExternal">复制网址</el-button>
        <!-- <el-button @click="addOrEditHandle('edit')">编辑</el-button> -->
        <el-tooltip effect="dark" content="提示：折扣9折（输入：90）；原价（输入：100）；加价10%（输入：110）" placement="right">
          <el-icon size="16" color="#909399" style="margin-left: 12px"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
        <el-table-column prop="companyName" label="商户名" align="center" min-width="90" />
        <el-table-column prop="sort" label="账号排序" align="center" min-width="100">
          <template #default="{ row }">
            <el-input v-model="row.sort" :disabled="row.isMyself" placeholder="请输入排序" @blur="saveFn(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="pricePercent" label="价格比率" align="center" min-width="100">
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <el-input v-model="row.pricePercent" placeholder="请输入价格比率" @blur="saveFn(row)">
                <template #append>%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="商品数量" sortable align="center" min-width="100" />
        <el-table-column prop="isShow" label="是否在选号网站展示" align="center" min-width="100">
          <template #default="{ row }">
            <el-switch v-model="row.isShow" :active-value="1" :inactive-value="0" :loading="row.loading" @change="switchFn(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="updateDate" label="更新时间" align="center" min-width="100">
          <template #default="{ row }">
            {{ row.updateDate ? formatTimeStamp(row.updateDate) : "-" }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" min-width="80">
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="saveFn(row)"> {{ row.isEdit ? "保存" : "编辑" }}</el-button>
            <el-button type="primary" link @click="row.isEdit = false" v-if="row.isEdit">取消</el-button>
          </template>
        </el-table-column> -->
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
    </el-card>
    <!-- 复制选号网址 -->
    <copuUrl ref="copuRulRef" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp } from "@/utils/method";
import copuUrl from "./syspartnerpush-copy-url.vue";
import baseService from "@/service/baseService";
import { getDictLabel } from "@/utils/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAppStore } from "@/store";
import { useSettingStore } from "@/store/setting";
import { Plus } from "@element-plus/icons-vue";
import useClipboard from "vue-clipboard3";
const { toClipboard } = useClipboard();

const copuRulRef = ref();
const store = useAppStore();
const settingStore = useSettingStore();
const gamesList = ref(<any>[]); // 游戏列表
const PartnerList = ref(<any>[]); // 商户列表
const alertText = ref("选号网默认优先展示自己处在上架状态的账号，还可选择其他商户账号一起展示，你可选择在其他账号价格基础上进行加价出售，从而赚取利润，添加展示商户前请先与对方进行联系，通知平台进行开通");

const tableData = ref(<any>[]); // 页面表格
const dataForm = reactive({
  gameId: null, // 游戏ID
  partnerId: null, // 商户ID
  type: "2",
  selectId: ""
});

// const editableTabsValue = ref('');  // 选择页签
const editableTabs = ref(<any>[]); // 页签列表

// 合作商列表
const getPartnerList = () => {
  // baseService.get("/partner/partner/page", { limit: 9999 }).then((res) => {
  //   PartnerList.value = res.data.list;
  // });
};

const getTenantList = () => {
  baseService.get("/sys/tenant/list").then((res) => {
    PartnerList.value = res.data.map((item) => {
      return {
        ...item,
        pricePercent: 1,
        isShow: 0,
        id: "",
        tenantId: item.tenantCode
      };
    });
  });
};

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    dataForm.gameId = res.data ? res.data[0].id : "";
    gamesList.value = [...res.data];
  });
};

// 表格列表
const tableLoading = ref(false);
const getDataList = () => {
  // tableData.value = [];
  if (!dataForm.selectId || !dataForm.gameId) return;
  tableLoading.value = true;
  baseService
    .post("/shop/partnerpush/page", dataForm)
    .then((res) => {
      let tenantIds = res.data.map((item) => item.tenantId);
      PartnerList.value.map((item) => {
        if (!tenantIds.includes(item.tenantId)) {
          res.data.push(item);
        }
      });

      // console.log(res.data, PartnerList.value)
      tableData.value = res.data.map((item) => {
        return {
          ...item,
          pricePercent: item.pricePercent ? Math.round(item.pricePercent * 100) : 100
        };
      });
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

// 保存
const saveFn = (row: any) => {
  let params = JSON.parse(JSON.stringify(row));
  params.gameId = dataForm.gameId;
  params.sort = Number(params.sort);
  params.selectId = params.selectId ? params.selectId : dataForm.selectId;
  params.companyType = params.companyType ? params.companyType : "2";

  // params.expiredTime = Number(params.expiredTime);
  params.pricePercent = Number(params.pricePercent) / 100;
  baseService
    .post("/shop/partnerpush", params)
    .then((res) => {
      ElMessage.success("保存成功！");
      getDataList();
    })
    .finally(() => {
      row.isEdit = false;
    });
};

// 开关
const switchFn = (row: any) => {
  let params = JSON.parse(JSON.stringify(row));
  params.gameId = dataForm.gameId;
  params.sort = Number(params.sort);
  params.selectId = params.selectId ? params.selectId : dataForm.selectId;
  params.companyType = params.companyType ? params.companyType : "2";
  // params.expiredTime = Number(params.expiredTime);
  params.pricePercent = Number(params.pricePercent) / 100;
  baseService
    .post("/shop/partnerpush", params)
    .then((res) => {
      ElMessage.success("保存成功！");
    })
    .finally(() => {
      getDataList();
    });
};

// 选号网址
const copyUrlFn = () => {
  nextTick(() => {
    copuRulRef.value.init();
  });
};

// 游戏切换点击事件
const flodTabChange = (value: any) => {
  dataForm.gameId = value;
  getDataList();
};

onMounted(() => {
  Promise.all([getGamesList(), getTenantList(), getSysShopTabs()]).then(() => {
    setTimeout(() => {
      getDataList();
    }, 1000);
  });
});

// 获取页签列表
const getSysShopTabs = (load = false, type: any) => {
  editableTabs.value = [];
  baseService.get("/shop/sysselectshop/page", { tenantCode: store.state.user.tenantCode }).then((res) => {
    dataForm.selectId = dataForm.selectId ? dataForm.selectId : res.data[0]?.id;
    editableTabs.value = res.data || [];
    // 编辑不更新，新增第一个时更新，删除更新
    if (type == "edit") return;
    if (load || editableTabs.value.length == 1) {
      getDataList();
    }
    // getSysShopList();
  });
};

// 新增/编辑页签
const addOrEditHandle = (type: any, item?: any) => {
  let tabItem: any = "";
  if (type == "edit") {
    tabItem = item || editableTabs.value.filter((item: any) => item.id == dataForm.selectId)[0];
  }

  ElMessageBox.prompt("名称", type == "add" ? "新增" : "编辑", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: type == "add" ? "" : tabItem.title,
    inputValidator(value) {
      if (!value) {
        return "请输入名称";
      }
    }
  }).then((ele) => {
    if (type == "add") {
      baseService.post("/shop/sysselectshop", { title: ele.value }).then((res) => {
        ElMessage.success("新增成功！");
        getSysShopTabs();
      });
    } else {
      baseService.put("/shop/sysselectshop", { id: tabItem.id, title: ele.value }).then((res) => {
        ElMessage.success("修改成功！");
        getSysShopTabs(false, "edit");
      });
    }
  });
};

// 删除标签页
const handleTabsRemove = (e: any) => {
  ElMessageBox.confirm("确定删除该数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.delete("/shop/sysselectshop", [e]).then((res) => {
      ElMessage.success("删除成功！");
      dataForm.selectId = "";
      getSysShopTabs(true);
    });
  });
};

// 切换标签页
const handleTabsChange = (name: any) => {
  getDataList();
};

// 复制外部选号网址
const copyExternal = () => {
  const url = editableTabs.value.filter((item: any) => item.id == dataForm.selectId)[0].url;
  const openUrl = `${settingStore.info.websiteUrl}selectShop?url=${url}`;
  copyInfo(openUrl);
};

// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 20px;
  color: #303133;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.el-alert--info {
  margin-bottom: 20px;
  &.is-light {
    color: var(--el-color-primary);
    background-color: var(--color-primary-light);
  }
  :deep(.el-alert__close-btn) {
    color: var(--el-color-primary);
  }
}
.demo-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
</style>
