<template>
    <div>
        <div style="display: flex;margin-top: 10px;">
            <ny-button-group :list="groupList" v-model="setUpValue"></ny-button-group>
        </div>
        <!-- 设置表格列名 -->
        <setUpList v-if="setUpValue == '1'" :menuInfo="JSON.parse(JSON.stringify(props.menuInfo))"></setUpList>
        <!-- 设置统计图 -->
        <statList v-if="setUpValue == '2'" :menuInfo="JSON.parse(JSON.stringify(props.menuInfo))"></statList>
    </div>
</template>

<script lang='ts' setup>
import { ref,reactive, onMounted } from 'vue';
import { RefreshRight } from '@element-plus/icons-vue';
import setUpList from './setUpList.vue';
import statList from './statList.vue';

interface Props {
    menuInfo: any
}
const props = withDefaults(defineProps<Props>(), {
    menuInfo:''
})

const setUpValue = ref('1');
const groupList = ref([
  { dictLabel: "设置表格列名", dictValue: "1" },
  { dictLabel: "设置统计图", dictValue: "2" },
]);



</script>

<style lang='less' scoped>

</style>