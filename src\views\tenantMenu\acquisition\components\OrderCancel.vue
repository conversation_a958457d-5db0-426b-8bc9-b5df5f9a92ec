<template>
    <operation-log 
        ref="operationRef" 
        :show="show" 
        @close="show=false" 
        title="订单取消确认" 
        confirmText="确认取消"
        type="danger"
        :loading="btnLoading"
        @confirm="confirm">   
        <template #default>
            <div class="text">
                <p>您即将取消回收订单：{{ orderInfo.sn }}</p>
                <p v-if="orderType == 'sell'">取消订单该订单将终止交易，是否确认取消？</p>
                <p v-else> 取消订单与该订单相关的所有信息将恢复至待回收状态，是否确认取消？</p>
            </div>
        </template>
    </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits } from "vue";
import OperationLog from "./SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refresh"]);
const show = ref(false);
const orderInfo = ref(<any>{});
const orderId = ref();

// 订单类型
const orderType = ref("");

const init = (data: any, type?: any) => {
    orderInfo.value = data;
    orderType.value = type;
    orderId.value = data.orderId || data.id;
    show.value = true;
}
 
// 确认取消
const btnLoading = ref(false);
const confirm = () => {
    btnLoading.value = true;
    const requestApi = orderType.value == "sell" ? "/sale/cancel/" : "/purchase/cancelOrder/";
    baseService.get(requestApi +  orderId.value, {orderId: orderId.value}).then(res => {
        if (res.code == 0) {
            ElMessage.success("取消成功");
            show.value = false;
            emit("refresh");
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}

defineExpose({
    init
})

</script>