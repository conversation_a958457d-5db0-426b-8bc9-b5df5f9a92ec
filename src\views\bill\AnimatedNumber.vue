<template>
    <div class="animated-number">
        {{ displayNumber.toLocaleString() }}
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from 'vue';

export default defineComponent({
    name: 'AnimatedNumber',
    props: {
        value: {
            type: Number,
            required: true,
            default: 0
        },
        duration: {
            type: Number,
            default: 2000 // 动画持续时间，单位毫秒
        },
        easing: {
            type: String,
            default: 'easeOutQuad' // 缓动函数类型
        }
    },
    setup(props) {
        const displayNumber = ref(0);
        let animationFrameId: number | null = null;
        let startTimestamp: number | null = null;
        const startValue = ref(0);

        // 缓动函数
        const easingFunctions = {
            linear: (t: number) => t,
            easeInQuad: (t: number) => t * t,
            easeOutQuad: (t: number) => t * (2 - t),
            easeInOutQuad: (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
            easeInCubic: (t: number) => t * t * t,
            easeOutCubic: (t: number) => (--t) * t * t + 1,
            easeInOutCubic: (t: number) => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),
        };

        const animateNumber = (timestamp: number) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const elapsed = timestamp - startTimestamp;
            const progress = Math.min(elapsed / props.duration, 1);

            // 使用缓动函数计算当前进度
            const easedProgress = easingFunctions[props.easing as keyof typeof easingFunctions](progress);

            // 计算当前显示的数字
            displayNumber.value = startValue.value + (props.value - startValue.value) * easedProgress;

            if (progress < 1) {
                animationFrameId = requestAnimationFrame(animateNumber);
            } else {
                animationFrameId = null;
            }
        };

        const startAnimation = () => {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            startValue.value = displayNumber.value;
            startTimestamp = null;
            animationFrameId = requestAnimationFrame(animateNumber);
        };

        watch(() => props.value, (newVal, oldVal) => {
            startAnimation();
        });

        onMounted(() => {
            displayNumber.value = props.value;
        });

        return {
            displayNumber
        };
    }
});
</script>

<style scoped>
.animated-number {
    font-weight: 400;
    font-size: 32px;
    line-height: 32px;
    font-family: ZenDots-Regular;
    background: linear-gradient(90deg, #63451E 0.01%, #060503 14.27%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
</style>