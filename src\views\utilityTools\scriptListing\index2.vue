<template>
  <div class="mainBox">
    <div class="leftPart">
      <div class="title">选择合作商</div>
      <el-select filterable v-model="state.dataForm.partnerId" style="width: 186px; margin-bottom: 10px" @change="handleselectPartner(state.dataForm.partnerId)">
        <el-option v-for="item in state.curAllPartnerList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <div class="title">选择游戏</div>
      <div class="scrollWrap lower">
        <ul class="menuUl">
          <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in state.allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
            <span>{{ item.gameName }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="rightPart">
      <shopPushTable ref="shopPushTableRef" @upDateScriptUserId="upDateScriptUserId" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
import shopPushTable from "./shopPushTable.vue";
import { useRouter } from "vue-router";
const router = useRouter();
const props = defineProps({
  curAllPartnerList: <any>[]
});
const shopPushTableRef = ref();
const state = reactive({
  curAllPartnerList: [
    {
      name: "全部合作商",
      id: null
    }
  ],
  allSelectArr: [
    {
      title: "选择游戏",
      data: [],
      addHandle: () => {},
      hasButton: false,
      buttonText: "",
      selectType: "radio"
    }
  ],
  dataForm: {
    partnerId: null, //合作商id
    gameId: undefined //游戏id
  }
});

const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  changeDataForm(false, true);
};
const scriptUserId = ref(null);
// 切换合作商获取游戏+更新店铺
const handleselectPartner = (id: any) => {
  state.dataForm.partnerId = id;
  state.dataForm.gameId = null;
  changeDataForm(true, false);
};
const upDateScriptUserId = (id: any) => {
  scriptUserId.value = id;
  state.allSelectArr = [];
  baseService.get("/script/sysscriptpartnerinfo/selectGameList", { partnerId: state.dataForm.partnerId, scriptUserId: scriptUserId.value }).then((res_) => {
    if (res_.code == 0) {
      state.allSelectArr = res_.data || [];
      if (state.allSelectArr.length > 0) {
        state.dataForm.gameId = state.allSelectArr[0].gameId;
      } else {
        state.dataForm.gameId = "";
      }
      // 获取列表
      changeDataForm(false, true);
    }
  });
};
// 向列表发送更新信号  使用： handleselectGame handleselectPartner
const changeDataForm = (changePartner = false, changeGame = false) => {
  nextTick(() => {
    shopPushTableRef.value.changeDataForm(state.dataForm.partnerId, state.dataForm.gameId, changePartner, changeGame);
  });
};
watch(
  () => props.curAllPartnerList,
  () => {
    console.log(props.curAllPartnerList);

    state.curAllPartnerList = [...props.curAllPartnerList];
    if (state.curAllPartnerList && state.curAllPartnerList.length > 0) {
      state.dataForm.partnerId = state.curAllPartnerList[0].id;
      //   获取店铺数据
      changeDataForm(true, false);
    }
  },
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rr-view-ctx-card {
  padding: 12px 24px;
  min-height: calc(100vh - 154px);

  :deep(.el-card__body) {
    padding: 0;
  }

  .cards {
    display: flex;
    margin-bottom: 16px;

    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }

      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .mainBox {
    display: flex;

    .leftPart {
      width: 186px;
      margin-right: 12px;

      .title {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 22px;
        margin-bottom: 2px;
      }

      display: flex;
      flex-direction: column;

      .scrollWrap {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        height: calc(100vh - 260px);
        overflow: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .menuUl {
          .menuLi {
            cursor: pointer;
            padding: 20px;
            word-break: keep-all;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Inter, Inter;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            &.active {
              background-color: var(--color-primary-light);
              color: var(--color-primary);
            }
          }
        }

        &.lower {
          height: calc(100vh - 330px);
        }
      }
    }

    .rightPart {
      flex: 1;
      overflow: hidden;
    }
  }
}
.router_info {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
</style>
