import baseService from "@/service/baseService";
// 时间戳格式化
// 生成一段代码，用于时间戳格式化
export const formatTimeStamp = (time: number) => {
  if (!time) return "";
  const date = new Date(Number(time));
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 生成一段代码，用于将时间转换为时间戳
export const timeStamp = (time: string) => {
  if (!time) return "";
  const date = new Date(time);
  return date.getTime();
};
// 文件导出
export const fileExport = (fileDate: any, fileName: string) => {
  return new Promise((resolve, reject) => {
    const blob = new Blob([fileDate], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // type: 'application/vnd.ms-excel'
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
    return resolve("导出成功");
  });
};

// 驼峰命名转下划线
export const camelToUnderscore = (name: any) => {
  return name.replace(/([A-Z])/g, "_$1").toLowerCase();
};

// 金额格式化
export const formatCurrency = (number: number) => {
  if (isNaN(number) || number === null) return "";
  const numStr = number.toString();
  const decimalIndex = numStr.indexOf(".");
  const integerNum = decimalIndex >= 0 ? numStr.substring(0, decimalIndex) : numStr;
  const integerStr = integerNum.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return decimalIndex >= 0 ? integerStr + numStr.substring(decimalIndex) : integerStr;
};
// 获取游戏密码 有权限
export const getAccountPassword = (id: any): Promise<string> => {
  return baseService
    .get("shop/shop/password?id=" + id)
    .then((res: any) => (res.code == 0 ? res.data : "暂无权限"))
    .catch(() => "-");
};

// 格式化日期
export const formatDate = (value: any, type?: any) => {
  // 计算日期相关值
  let time = typeof value == "number" ? new Date(value) : value;
  let Y = time.getFullYear();
  let M = time.getMonth() + 1;
  let D = time.getDate();
  let h = time.getHours();
  let m = time.getMinutes();
  let s = time.getSeconds();
  let week = time.getDay();

  if (type == undefined) {
    return (
      Y +
      "-" +
      (M < 10 ? "0" + M : M) +
      "-" +
      (D < 10 ? "0" + D : D) +
      " " +
      (h < 10 ? "0" + h : h) +
      ":" +
      (m < 10 ? "0" + m : m) +
      ":" +
      (s < 10 ? "0" + s : s)
    );
  } else if (type == "week") {
    return week + 1;
  } else if (type == "YYYY-MM-DD") {
    return Y + "-" + (M < 10 ? "0" + M : M) + "-" + (D < 10 ? "0" + D : D);
  } else if (type == "YYYY-MM") {
    return Y + "-" + (M < 10 ? "0" + M : M);
  } else if (type == "YYYY") {
    return Y;
  }
};

//  身份证号 校验规则
export function isCardNo(value: any) {
  return validateIdent.IdentityCodeValid(value.toUpperCase());
}

const validateIdent: any = {
  aIdentityCode_City: {
    // 城市代码列表
    11: "北京",
    12: "天津",
    13: "河北",
    14: "山西",
    15: "内蒙古",
    21: "辽宁",
    22: "吉林",
    23: "黑龙江 ",
    31: "上海",
    32: "江苏",
    33: "浙江",
    34: "安徽",
    35: "福建",
    36: "江西",
    37: "山东",
    41: "河南",
    42: "湖北 ",
    43: "湖南",
    44: "广东",
    45: "广西",
    46: "海南",
    50: "重庆",
    51: "四川",
    52: "贵州",
    53: "云南",
    54: "西藏 ",
    61: "陕西",
    62: "甘肃",
    63: "青海",
    64: "宁夏",
    65: "新疆",
    71: "台湾",
    81: "香港",
    82: "澳门",
    91: "国外 "
  },
  IdentityCode_isCardNo(card: any) {
    //检查号码是否符合规范，包括长度，类型
    var reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/; //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    if (reg.test(card) === false) {
      return false;
    }
    return true;
  },
  IdentityCode_checkProvince(card: any) {
    //取身份证前两位，校验省份
    var province = card.substr(0, 2);
    if (validateIdent.aIdentityCode_City[province] == undefined) {
      return false;
    }
    return true;
  },
  IdentityCode_checkBirthday(card: any) {
    //检查生日是否正确，15位以'19'年份来进行补齐。
    var len = card.length;
    //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
    if (len == "15") {
      var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
      var arr_data = card.match(re_fifteen); // 正则取号码内所含出年月日数据
      var year = arr_data[2];
      var month = arr_data[3];
      var day = arr_data[4];
      var birthday = new Date("19" + year + "/" + month + "/" + day);
      return validateIdent.IdentityCode_verifyBirthday("19" + year, month, day, birthday);
    }
    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
    if (len == "18") {
      var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
      var arr_data = card.match(re_eighteen); // 正则取号码内所含出年月日数据
      var year = arr_data[2];
      var month = arr_data[3];
      var day = arr_data[4];
      var birthday = new Date(year + "/" + month + "/" + day);
      return validateIdent.IdentityCode_verifyBirthday(year, month, day, birthday);
    }
    return false;
  },
  IdentityCode_verifyBirthday(year: any, month: any, day: any, birthday: any) {
    //校验日期 ，15位以'19'年份来进行补齐。
    var now = new Date();
    var now_year = now.getFullYear();
    //年月日是否合理
    if (
      birthday.getFullYear() == year &&
      birthday.getMonth() + 1 == month &&
      birthday.getDate() == day
    ) {
      //判断年份的范围（3岁到150岁之间)
      var time = now_year - year;
      if (time >= 3 && time <= 150) {
        return true;
      }
      return false;
    }
    return false;
  },
  IdentityCode_checkParity(card: any) {
    //校验位的检测
    card = validateIdent.IdentityCode_changeFivteenToEighteen(card); // 15位转18位
    var len = card.length;
    if (len == "18") {
      var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
      var arrCh = new Array("1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2");
      var cardTemp = 0,
        i,
        valnum;
      for (i = 0; i < 17; i++) {
        cardTemp += card.substr(i, 1) * arrInt[i];
      }
      valnum = arrCh[cardTemp % 11];
      if (valnum == card.substr(17, 1)) {
        return true;
      }
      return false;
    }
    return false;
  },
  IdentityCode_changeFivteenToEighteen(card: any) {
    //15位转18位身份证号
    if (card.length == "15") {
      var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
      var arrCh = new Array("1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2");
      var cardTemp = 0,
        i;
      card = card.substr(0, 6) + "19" + card.substr(6, card.length - 6);
      for (i = 0; i < 17; i++) {
        cardTemp += card.substr(i, 1) * arrInt[i];
      }
      card += arrCh[cardTemp % 11];
      return card;
    }
    return card;
  },
  IdentityCodeValid(card: any) {
    //   身份证号码检验主入口
    let pass = true;
    let sex = "";
    //是否为空
    if (pass && card === "") pass = false;
    //校验长度，类型
    if (pass && validateIdent.IdentityCode_isCardNo(card) === false) pass = false;
    //检查省份
    if (pass && validateIdent.IdentityCode_checkProvince(card) === false) pass = false;
    //校验生日
    if (pass && validateIdent.IdentityCode_checkBirthday(card) === false) pass = false;
    //检验位的检测
    if (pass && validateIdent.IdentityCode_checkParity(card) === false) pass = false;
    if (pass) {
      var iCard = validateIdent.IdentityCode_changeFivteenToEighteen(card);
      if (parseInt(iCard.charAt(16)) % 2 == 0) {
        sex = "0"; // 女生
      } else {
        sex = "1"; // 男生
      }
      return true;
    } else {
      return false;
    }
  }
};

/**
 * 对象深拷贝
 */
export const deepClone = (data: any) => {
  const type = getObjType(data);
  let obj;
  if (type === "array") {
    obj = [];
  } else if (type === "object") {
    obj = {};
  } else {
    //不再具有下一层次
    return data;
  }
  if (type === "array") {
    for (let i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === "object") {
    for (const key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
};

export const getObjType = (obj: any) => {
  const toString = Object.prototype.toString;
  const map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object"
  };
  if (obj instanceof Element) {
    return "element";
  }
  return map[toString.call(obj)];
};

/**
 * window.localStorage
 * @method set 设置
 * @method get 获取
 * @method remove 移除
 * @method clear 移除全部
 */
export const Local = {
  set(key: string, val: any) {
    window.localStorage.setItem(key, JSON.stringify(val));
  },
  get(key: string) {
    const json: any = window.localStorage.getItem(key);
    return JSON.parse(json);
  },
  remove(key: string) {
    window.localStorage.removeItem(key);
  },
  clear() {
    window.localStorage.clear();
  }
};

// 检查form表单未填项并滚动到该位置
export const scrollToFirstError = (formRef: any) => {
  // 等待DOM更新
  setTimeout(() => {
    const firstError = document.querySelector(".el-form-item.is-error");
    if (firstError) {
      firstError.scrollIntoView({
        behavior: "smooth",
        block: "center"
      });
      // 聚焦到第一个错误的输入框
      const input = firstError.querySelector("input, textarea, select");
      if (input) {
        input.focus();
      }
    }
  }, 100);
};
// utils/stringSimilarity.ts
export interface SimilarityResult<T = string> {
  name: T;
  similarity: number;
}

/**
 * 计算两个字符串的相似度 (0-1)
 */
export function calculateSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1;

  // 实现一个简单的相似度算法（可根据需要替换为更复杂的算法）
  const set1 = new Set(str1);
  const set2 = new Set(str2);
  const intersection = new Set([...set1].filter((char) => set2.has(char)));
  const union = new Set([...set1, ...set2]);

  return intersection.size / union.size;
}

/**
 * 查找与目标字符串相似的所有项
 */
export function findSimilarItems<T extends string>(
  items: T[],
  target: string,
  threshold: number = 0.6
): SimilarityResult<T>[] {
  return items
    .map((item) => ({
      name: item,
      similarity: calculateSimilarity(item, target)
    }))
    .filter((result) => result.similarity >= threshold)
    .sort((a, b) => b.similarity - a.similarity);
}
