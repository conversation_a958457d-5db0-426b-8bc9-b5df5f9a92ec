<template>
    <el-drawer v-model="visible"  size="40%" class="drawer">
        <template #header>
            <div class="drawer_title">{{ !dataForm.id ? $t('add') + '黑名单账号' : $t('update') + '黑名单账号' }}</div>
        </template>
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" @keyup.enter="">
            <el-row :gutter="12">
                <el-col :span="12">
                    <el-form-item label="游戏账号" prop="account">
                        <el-input v-model="dataForm.account" placeholder="游戏账号"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="游戏名称" prop="gameName">
                        <el-select v-model="dataForm.gameName">
                            <el-option
                                v-for="item in gamesList"
                                :key="item.id"
                                :label="item.title"
                                :value="item.title"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="身份证号" prop="idCard">
                        <el-input v-model="dataForm.idCard" type="text" placeholder="身份证号" maxlength="18" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="姓名" prop="name">
                        <el-input v-model="dataForm.name" placeholder="姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="手机号" prop="phone">
                        <el-input v-model="dataForm.phone" type="text" placeholder="手机号码" maxlength="11" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="平台名称" prop="platform">
                        <el-input v-model="dataForm.platform" placeholder="平台名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="拉黑原因" prop="remarks">
                        <el-input v-model="dataForm.remarks" maxlength="200" :rows="4" placeholder="拉黑原因"  show-word-limit type="textarea"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { isCardNo } from "@/utils/method";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const visible = ref(false);  // 对话框显隐
const dataFormRef = ref();  // 表单ref
const dataForm = reactive({  // 表单变量
    id: null,
    account: '',
    gameName: '',
    idCard: '',
    name: '',
    phone: '',
    platform: '',
    remarks: '',
});

// 自定义身份证校验规则
const idCardValidator = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请输入正确身份证号'))
    } else {
        if (!isCardNo(value)) {
            callback(new Error('身份证号格式错误'))
        } else {
            callback()
        }
    }
}

const rules = ref({  // 表单必填项
    account: [{ required: true, message: '游戏账号不能为空', trigger: 'blur', },],
    gameName: [{ required: true, message: '游戏名称不能为空', trigger: 'change', },],
    name: [{ required: true, message: '姓名不能为空', trigger: 'blur', },],
    idCard: [
        { required: true, message: '请输入身份证号', trigger: 'blur' },
        { validator: idCardValidator, trigger: 'blur', },],
    phone: [
        { required: false, message: '请输入手机号码', trigger: 'blur' },
        {
            required: false,
            pattern: /^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
        },
    ],
    remarks: [{ required: true, message: '拉黑原因不能为空', trigger: 'blur', },],
});

// 表单提交
const btnLoading = ref(false);
const emit = defineEmits(["refreshDataList"]);
const submitForm = () => {
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        btnLoading.value = true;
        (!dataForm.id ? baseService.post : baseService.put)("/blacklist/blacklist", dataForm).then((res) => {
            ElMessage.success({
                message: t("prompt.success"),
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        }).finally(() => {
            btnLoading.value = false;
        })
    });
}


// 表单初始化
const init = (id?: any) => {
    visible.value = true;
    dataForm.id = id ? id : null;
    getGamesList();
    // 获取表单详情
    if (id) {
        getInfo(id);
    }
    // 重置表单数据
    if (dataFormRef.value) {
        dataFormRef.value.resetFields();
    }
};

const getInfo = (id: number) => {
    baseService.get("/blacklist/blacklist/" + id).then((res) => {
        Object.assign(dataForm, res.data);
    });
}

// 获取游戏列表
const gamesList = ref(<any>[]);  // 游戏列表
const getGamesList = () => {
    baseService.get("/game/sysgame/listGames").then((res) => {
        gamesList.value = res.data
    });
};

defineExpose({
    init
});
</script>

<style lang='less' scoped>

</style>
<style lang='less'>
.drawer{
    .el-drawer__header{
        margin-bottom: 0px;
    }
    。el-drawer__body {
        padding: 20px !important;
    }
    .drawer_title{
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>