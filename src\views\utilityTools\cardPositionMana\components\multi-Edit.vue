<template>
  <el-dialog :footer="null" v-model="visible" title="批量编辑" width="480">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <el-descriptions style="width: 100%" border :column="2">
        <el-descriptions-item :span="2" label="修改话费余额">
          <el-form-item label="修改话费余额" prop="value4">
            <el-input v-model="dataForm.value4" placeholder="请输入要修改的话费余额">
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-dialog>
</template>  
        
<script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";

const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);
const selectRows = ref(<any>[]);
const rules = {};

const init = (rows?: number) => {
  visible.value = true;
  selectRows.value = rows;
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/basicInfo/tbannouncement", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          selectRows.value = [];
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
        
      <style lang="scss" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>