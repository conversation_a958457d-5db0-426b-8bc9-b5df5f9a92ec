import Layout from "@/layout/layout.vue";
import Error from "@/views/error.vue";
import { RouteRecordRaw } from "vue-router";
import Login from "@/views/login.vue";
// 收银台
import cashRegister from "@/views/cashRegister.vue";
import themeMana from "@/views/sys/themeMana/index.vue";
import Iframe from "@/views/iframe.vue";
import userCenter from "@/views/user/center.vue";

/**
 * 框架基础路由
 */
const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    component: () => import("@/views/home.vue"),
    meta: { title: "ui.router.pageHome", icon: "icon-home", isNavigationMenu: false }
  },
  // {
  //   path: "/",
  //   component: Layout,
  //   redirect: "/home",
  //   meta: { title: "ui.router.pageWorkbench", icon: "icon-desktop" },
  //   children: [
  //     {
  //       path: "/home",
  //       component: () => import("@/views/home.vue"),
  //       meta: { title: "ui.router.pageHome", icon: "icon-home" }
  //     }
  //   ]
  // },
  {
    path: "/login",
    component: Login,
    meta: { title: "ui.router.pageLogin", isNavigationMenu: false }
  },
  {
    path: "/user/password",
    component: () => import("@/views/sys/user-update-password.vue"),
    meta: { title: "ui.user.links.editPassword", requiresAuth: true, isNavigationMenu: false }
  },
  {
    path: "/iframe/:id?",
    component: Iframe,
    meta: { title: "iframe", isNavigationMenu: false }
  },
  {
    path: "/error",
    name: "error",
    component: Error,
    meta: { title: "ui.router.pageError", isNavigationMenu: false }
  },
  {
    path: "/cashRegister",
    name: "cashRegister",
    component: cashRegister,
    meta: { title: "ui.router.cashRegister", isNavigationMenu: false }
  },
  {
    path: "/sys/themeMana/index",
    name: "themeMana",
    component: themeMana,
    meta: { title: "ui.router.themeMana", isNavigationMenu: false }
  },
  {
    path: "/user/center",
    name: "userCenter",
    component: userCenter,
    meta: { title: "ui.user.links.userCenter", isNavigationMenu: false }
  },
  {
    path: "/im",
    name: "im",
    component: () => import("@/views/im/index.vue"),
    meta: { title: "", isNavigationMenu: false }
  },
  {
    path: "/realnameContract",
    name: "realnameContract",
    component: () => import("@/views/realnameContract/index.vue"),
    meta: { title: "", isNavigationMenu: false }
  }
];

export const errorRoute: Array<RouteRecordRaw> = [
  {
    path: "/:path(.*)*",
    redirect: { path: "/error", query: { to: 404 }, replace: true },
    meta: { isNavigationMenu: false }
  }
];

export default routes;
