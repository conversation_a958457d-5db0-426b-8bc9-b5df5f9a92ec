<template>
    <div>
        <div class="menu_title">库存周转评估</div>
        <div class="top">
            <ny-button-group :list="[
                { dictLabel: '库存概况', dictValue: '0' },
                { dictLabel: '库存明细', dictValue: '1' },
                { dictLabel: '滞销分布', dictValue: '2' },
                { dictLabel: '对接明细', dictValue: '3' }
            ]" v-model="typeValue" @change=""></ny-button-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId" :list="recyclingChanneList" labelKey="channelName"
                    valueKey="channelId" placeholder="回收渠道" clearable></NyDropdownMenu> -->
                <NyDropdownMenu v-model="dataForm.purchaseEmployeeId" :list="employeeList" labelKey="employeeName"
                    valueKey="employeeId" placeholder="选择回收员工" clearable></NyDropdownMenu>
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <!-- 库存概况 -->
        <InventoryOverview ref="InventoryOverviewRef" v-if="typeValue == '0'"></InventoryOverview>
        <!-- 库存明细 -->
        <InventoryDetails ref="InventoryDetailsRef" v-if="typeValue == '1'"></InventoryDetails>
        <!-- 滞销分布 -->
        <UnsalableDistribution ref="UnsalableDistributionRef" v-if="typeValue == '2'"></UnsalableDistribution>
        <!-- 对接明细 -->
        <DockingDetails ref="DockingDetailsRef" v-if="typeValue == '3'"></DockingDetails>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import InventoryOverview from "./menuPageList/InventoryOverview.vue";
import InventoryDetails from "./menuPageList/InventoryDetails.vue";
import UnsalableDistribution from "./menuPageList/UnsalableDistribution.vue";
import DockingDetails from "./menuPageList/DockingDetails.vue";
import baseService from "@/service/baseService";

const typeValue = ref("0");
const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    purchaseEmployeeId: ""
});

const gameList = ref(); // 游戏列表
const recyclingChanneList = ref(); // 回收渠道列表
const employeeList = ref(); // 选择员工列表

const InventoryOverviewRef = ref();
const InventoryDetailsRef = ref();
const UnsalableDistributionRef = ref();
const DockingDetailsRef = ref();

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) => {
    baseService.get("/dataAnalysis/channelSearchList", { channelType }).then((res) => {
        recyclingChanneList.value = res.data;
    });
};

// 员工列表
const getEmployeeList = () => {
    baseService.get("/dataAnalysis/employeeSearchList").then((res) => {
        employeeList.value = res.data;
    });
};

// 查询
const queryChagne = () => {
    if (InventoryOverviewRef.value) {
        InventoryOverviewRef.value.init(dataForm.value);
    }
    if (InventoryDetailsRef.value) {
        InventoryDetailsRef.value.init(dataForm.value);
    }
    if (UnsalableDistributionRef.value) {
        UnsalableDistributionRef.value.init(dataForm.value)
    }
    if (DockingDetailsRef.value) {
        DockingDetailsRef.value.init(dataForm.value);
    }
};

// 重置
const resettingChange = () => {
    dataForm.value.gameId = "";
    dataForm.value.recyclingChannelId = "";
    dataForm.value.purchaseEmployeeId = "";

    if (InventoryOverviewRef.value) {
        InventoryOverviewRef.value.init(dataForm.value);
    }
    if (InventoryDetailsRef.value) {
        InventoryDetailsRef.value.init(dataForm.value);
    }
    if (UnsalableDistributionRef.value) {
        UnsalableDistributionRef.value.init(dataForm.value);
    }
    if (DockingDetailsRef.value) {
        DockingDetailsRef.value.init(dataForm.value);
    }
};

onMounted(() => {
    getGameList();
    getRecyclingChanneList(1);
    getEmployeeList();
});
</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
