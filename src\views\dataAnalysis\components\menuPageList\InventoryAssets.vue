<template>
  <div class="analysis_page">
    <div class="card_analysis mt-12" style="background: #f7f8fa">
      <div class="header_analysis" style="padding: 22px 20px 20px 20px">
        <div class="header_analysis_left flx-align-center">
          <div class="header_analysis_title" style="font-size: 20px; margin-left: 0px">库存资产明细</div>
        </div>
        <div class="header_analysis_right flx-align-center">
          <div class="tip">保存当前设置为默认“库存资产”</div>
          <!-- <div class="analysis_type_item">保存设置</div> -->
          <el-button round :loading="refreshInventoryLoading" @click="getRefreshInventory">保存设置</el-button>
        </div>
      </div>
      <div style="padding: 0px 20px 20px 20px">
        <el-table :data="tableData" class="analysis_table" ref="tableRef" border :summary-method="getSummaries"
          show-summary @selection-change="dataListSelectionChangeHandle">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="inventoryType" label="库存类型" align="center" />
          <el-table-column prop="inventoryNumber" label="库存数量" sortable align="center" />
          <el-table-column prop="totalCost" label="总成本(元)" sortable align="center" />
          <el-table-column prop="totalValuation" label="总估值(元)" sortable align="center" />
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted, nextTick } from "vue";
import { BigNumber } from "bignumber.js";
import { ElMessage } from "element-plus";

const tableData = ref(<any>[]);
const tableRef = ref();

const dataForm = ref({
  gameId: "",
  recyclingChannelId: "",
  employeeId: "",
  startTime: "",
  endTime: ""
});

onMounted(() => {
  getInventoryAssetDetail();
})

// 库存资产明细
const getInventoryAssetDetail = () => {
  baseService.post("/dataAnalysis/inventoryAssetDetail", dataForm.value).then(res => {
    tableData.value = res.data;
    nextTick(() => {
      tableData.value.forEach((item: any) => {
        if (item.isSelect) {
          tableRef.value?.toggleRowSelection(item, true);
        }
      })
    })

  })
}

const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    // 第一列 显示文字
    if (index === 0) {
      return (sums[index] = "合计:");
    } else if (column.property == "inventoryNumber") {
      let total: any = 0;
      tableData.value.map((item: any) => {
        if (item.inventoryNumber) total = BigNumber(total).plus(BigNumber(item.inventoryNumber));
      });
      return (sums[index] = total + "");
    } else if (column.property == "totalCost") {
      let total: any = 0;
      tableData.value.map((item: any) => {
        if (item.totalCost) total = BigNumber(total).plus(BigNumber(item.totalCost));
      });
      return (sums[index] = total + "");
    } else if (column.property == "totalValuation") {
      let total: any = 0;
      tableData.value.map((item: any) => {
        if (item.totalValuation) total = BigNumber(total).plus(BigNumber(item.totalValuation));
      });
      return (sums[index] = total + "");
    }
  });
  return sums;
}

// 保存设置
const refreshInventoryLoading = ref(false)
const getRefreshInventory = () => {
  if (!selectList.value.length) {
    ElMessage.warning('请选择需要保存的库存资产');
    return;
  }
  refreshInventoryLoading.value = true;
  baseService.post("/dataAnalysis/refreshInventory", {statusList: selectList.value.map((item:any) => item.shopStatus)}).then(res => {
    ElMessage.success('库存资产保存成功');
    getInventoryAssetDetail();
  }).finally(() => {
    refreshInventoryLoading.value = false;
  })
}

// 多选
const selectList = ref(<any>[]);
const dataListSelectionChangeHandle = (list: any[]) => {
  selectList.value = list
};

const init = (form: any) => {
  Object.assign(dataForm.value, form);
  getInventoryAssetDetail();
};

defineExpose({
  init
});

</script>

<style lang="less" scoped>
.card_analysis {
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e6eb;

  .header_analysis {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 20px;

    .header_analysis_left {
      .header_analysis_title {
        font-weight: 500;
        font-size: 16px;
        color: #1d252f;
        line-height: 24px;
        margin-left: 4px;
      }
    }

    .header_analysis_right {
      .tip {
        font-weight: 400;
        font-size: 14px;
        color: #4e5969;
        line-height: 22px;
        margin-right: 10px;
      }
    }
  }

  .header_describe {
    font-weight: 400;
    font-size: 13px;
    color: #4e5969;
    line-height: 22px;
    padding: 0px 20px;
  }

  .center_analysis {
    padding: 12px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .listMap {
      width: 200px;

      .listMap_label {
        span {
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          line-height: 22px;
          margin-right: 2px;
        }
      }

      .listMap_value {
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
      }
    }
  }
}

.analysis_type {
  display: flex;
  align-items: center;
  gap: 10px;
}

.analysis_type_item {
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  padding: 3px 12px;
  background: #ffffff;
  border-radius: 24px;
  border: 1px solid #d4d7de;
  cursor: pointer;
}

.active {
  background: #4165d7;
  color: #ffffff;
  border: 1px solid #4165d7;
}

.analysis_table {
  width: 100%;

  :deep(th .cell) {
    background: none !important;
    font-weight: bold;
    font-size: 14px;
    // color: #909399;
    line-height: 22px;
  }

  // :deep(th:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.1) !important;
  // }

  // :deep(td:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.05) !important;
  // }
}

.docking_line {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .line_left {
    display: flex;
    align-items: center;
    gap: 4px;

    .partners {
      width: 32px;
      height: 40px;
      position: relative;

      img {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid #606266;
      }

      .mask {
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        border-radius: 6px;
        width: 32px;
        height: 32px;
        top: 0;
        left: 0;
        z-index: 2;
      }

      .selected {
        position: absolute;
        left: 9px;
        bottom: -7px;
        z-index: 2;
      }
    }
  }

  .line_right {
    font-weight: 400;
    font-size: 12px;
    color: #303133;
    line-height: 14px;

    span {
      color: var(--el-color-primary);
    }
  }
}
</style>
