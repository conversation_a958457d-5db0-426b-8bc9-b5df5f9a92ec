<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="游戏编码" prop="gameCode">
        <el-select v-model="dataForm.gameCode" placeholder="游戏编码" @change="gameSelectChange">
          <el-option v-for="item in gameList" :key="item.id" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="属性名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="属性名称"></el-input>
      </el-form-item>
      <el-form-item label="属性状态" prop="status">
        <ny-radio-group v-model="dataForm.status" dict-type="game_properties_status"></ny-radio-group>
      </el-form-item>
      <el-form-item label="属性类型" prop="type">
        <ny-radio-group v-model="dataForm.type" dict-type="game_properties_type"></ny-radio-group>
      </el-form-item>
      <el-form-item label="属性粘贴填充">
        <el-input v-model="attributeValue" type="textarea" placeholder="属性之间以逗号隔开。如：XXX，XXX" :autosize="{ minRows: 2, maxRows: 4 }" @input="inputChange"></el-input>
      </el-form-item>
      <el-form-item label="属性明细">
        <el-table size="small" :data="dataForm.detailsList" border style="width: 100%; margin-bottom: 20px" row-key="gsId">
          <el-table-column prop="gadValue" align="center" label="游戏属性" min-width="100">
            <template #default="scope">
              <el-input size="small" v-model="scope.row.gadValue" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #header>
              <el-button @click="addColumn()" size="small" class="table-header-operate" type="primary">新增</el-button>
            </template>
            <template #default="scope">
              <el-button class="table-operate" size="small" type="danger" text @click="DelColumn(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  pid: "",
  id: "",
  name: "",
  status: "1",
  type: "",
  gameCode: "",
  areaCode: "",
  code: "",
  parentCode: "",
  sort: "",
  rquired: "",
  propertyField: "",
  propertiesType: "",
  isDelete: 0,
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
  detailsList: []
});

const rules = ref({
  gameCode: [{ required: true, message: t("validate.required"), trigger: "change" }],
  name: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  status: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  type: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});

const attributeValue = ref(""); // 属性拆分
const time = ref(0); // 定时器
const gameList = ref([]); // 游戏列表

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
  getGameList();
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbgameproperties/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    if (dataForm.detailsList == null) {
      dataForm.detailsList = [];
    }
  });
};

// 获取游戏列表
const getGameList = () => {
  baseService.get("/shop/tbgame/gameList").then((res) => {
    gameList.value = res.data;
  });
};

// 游戏列表选择事件
const gameSelectChange = (value: any) => {
  dataForm.pid = gameList.value.filter((item) => item.code == value)[0].id;
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    console.log("======== 表单提交 =========", dataForm);
    (!dataForm.id ? baseService.post : baseService.put)("/shop/tbgameproperties", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 属性拆分
const inputChange = (res: any) => {
  if (res != "") {
    clearTimeout(time.value);
    time.value = setTimeout(() => {
      let array = [];
      array = res.split(/,|，|\n|" "|\s/);
      array.forEach((v: any) => {
        if (v != undefined && v != "" && Object.keys(v).length > 0) {
          let index = dataForm.detailsList.findIndex((v1: any) => v1.name == v);
          if (index < 0) {
            dataForm.detailsList.push({ gadValue: v });
          }
        }
      });
      attributeValue.value = "";
    }, 1200);
  }
};

// 属性明细 - 新增
const addColumn = () => {
  console.log(dataForm.detailsList);
  dataForm.detailsList.push({});
};
// 属性明细 - 删除
const DelColumn = (index: number, row: any) => {
  dataForm.detailsList.splice(index, 1);
};

defineExpose({
  init
});
</script>
