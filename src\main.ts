import "@/assets/icons/iconfont/iconfont.js";
import NyDeptTree from "src/components/ny-dept-tree";
import NyProcessDetail from "src/components/ny-process-detail";
import NyProcessRunning from "src/components/ny-process-running";
import NyRadioGroup from "src/components/ny-radio-group";
import NyRegionTree from "src/components/ny-region-tree";
import NySelect from "src/components/ny-select";
import NySelectSearch from "src/components/ny-select-search";
import NyUpload from "src/components/ny-upload";
import NyUploadFile from "src/components/ny-upload-file";
import nyUploadVideo from "src/components/ny-upload-video";
import NyFormSlot from "src/components/ny-form-slot"
import NyTitle from "src/components/ny-title"
import NyRadio from "src/components/ny-radio"
import NyTable from "src/components/ny-table";
import NyImport from "src/components/ny-import";
import nyFlodTab from "src/components/ny-flod-tab";
import NyButtonGroup from "src/components/ny-button-group";
import NyShopImageUpload from "@/components/ny-shop-image-upload"
import NyNoData from "@/components/ny-no-data"
import NyDropdownMenu from "src/components/ny-dropdown-menu";
import ElementPlus from "element-plus";
import "element-plus/theme-chalk/display.css";
import "element-plus/theme-chalk/index.css";
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css';
import { createApp } from "vue";
import { createPinia } from "pinia";
import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
import "xe-utils";
import App from "./App.vue";
import { initI18n } from "./i18n";
import router from "./router";
import directives from "@/directives/index"
import * as ElementPlusIcons from "@element-plus/icons-vue";

import VForm3 from "@/../lib/vform/designer.umd.js";
import "../lib/vform/designer.style.css";

import axios from "axios";
import "virtual:svg-icons-register";

VXETable.setup({
  zIndex: 3000,
  select: {
    transfer: true
  }
});

const app = createApp(App);
Object.keys(ElementPlusIcons).forEach((iconName) => {
  app.component(iconName, ElementPlusIcons[iconName as keyof typeof ElementPlusIcons]);
});

// flowable 流程设计器
import "@/components/ny-flowable/package/theme/index.scss";
import NyFlowable from "@/components/ny-flowable/package/index.js";
app.use(NyFlowable);

app
  .use(createPinia())
  .use(router)
  .use(directives)
  .use(NyRadioGroup)
  .use(NySelect)
  .use(NySelectSearch)
  .use(NyFormSlot)
  .use(NyTitle)
  .use(NyRadio)
  .use(NyUpload)
  .use(NyUploadFile)
  .use(nyUploadVideo)
  .use(NyDeptTree)
  .use(NyRegionTree)
  .use(NyProcessRunning)
  .use(NyProcessDetail)
  .use(NyTable)
  .use(nyFlodTab)
  .use(NyButtonGroup)
  .use(NyImport)
  .use(NyShopImageUpload)
  .use(NyNoData)
  .use(NyDropdownMenu)
  .use(ElementPlus, { size: "default" })
  .use(VForm3)
  .use(VXETable)
  .use(initI18n)
  .mount("#app");

window.axios = axios;
