<template>
  <el-dialog class="contractTemplateDialog" v-model="visible" width="480" title="企业认证" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #title>
      <span style="font-weight: bold; font-size: 18px; color: #303133">变量设置</span>
      <el-tooltip class="box-item" effect="dark" content="用于模板变量与平台字段相统一，创建合同时可自动生成对应内容" placement="top">
        <el-icon style="margin-left: 8px"><Warning /></el-icon>
      </el-tooltip>
    </template>
    <el-table max-height="400px" v-loading="state.dataListLoading" :data="state.dataList" border>
      <el-table-column prop="name" label="变量名称" header-align="center" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="phone" label="平台字段名称" header-align="center" align="center">
        <!-- 类型 -->
        <template v-slot="{ row, $index }">
          <el-select v-model="row.fieldKey" @change="changeKey($index)" placeholder="请选择对应平台字段" clearable>
            <el-option v-for="item in varList" :key="item.colName" :label="item.colComment" :value="item.colName"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
import useView from "@/hooks/useView";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const view = reactive({
  getDataListURL: "/wallet/tenantaccount/page",
  getDataListIsPage: true,
  createdIsNeed: false,
  deleteURL: "/wallet/tenantaccount",
  deleteIsBatch: true,
  dataList: []
});
const templateId = ref();
const state = reactive({ ...useView(view), ...toRefs(view) });
const init = (id?: number) => {
  visible.value = true;
  if (id) {
    templateId.value = id;
    getInfo(id);
  }
};
const varList = ref([]);
const changeKey = (i: any) => {
  let obj = varList.value.find((ele) => ele.colName == state.dataList[i].fieldKey);
  state.dataList[i].fieldComment = obj?.colComment;
  state.dataList[i].isFixed = obj?.isFixed;
};
// 获取信息
const getInfo = (id: number) => {
  state.dataList = [];
  state.dataListLoading = true;
  baseService.get("/bestsign/listMappingByTemplateId/" + id).then((res) => {
    // 已存字段
    let arr = (res.data || []).map((ele: any) => {
      ele.name = ele.fieldName;
      return ele;
    });
    baseService
      .get("/bestsign/listTempVars/" + id)
      .then((res) => {
        // 最新模板字段：字段名是唯一的
        let arr_ = (res.data || []).filter((ele) => ele.type != "20" && ele.type != "40" && ele.type != "50");
        arr_.forEach((item: any, index: any) => {
          // 如果模板字段已存在库里，赋值库里内容，不存在则不处理
          if (arr.findIndex((ele: any) => ele.name == item.name) > -1) {
            let index_ = arr.findIndex((ele: any) => ele.name == item.name);
            arr_[index] = arr[index_];
          }
        });
        state.dataList = [...arr_];
      })
      .finally(() => {
        state.dataListLoading = false;
      });
  });
  baseService.get("/bestsign/listTableFields/" + id).then((res) => {
    varList.value = res.data;
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  let arr = [...state.dataList].map((item: any) => {
    return {
      id: item.id,
      templateId: templateId.value,
      fieldName: item.name,
      fieldComment: item.fieldComment,
      fieldKey: item.fieldKey,
      fieldType: item.type ? item.type : item.fieldType,
      isFixed: item.isFixed
    };
  });
  if (arr.some((ele) => !ele.fieldKey)) {
    ElMessage.warning("请完善变量设置！");
    return;
  }
  baseService.post("/bestsign/saveTemplateMappingVars", arr).then((res) => {
    ElMessage.success({
      message: t("prompt.success"),
      duration: 500,
      onClose: () => {
        visible.value = false;
        emit("refreshDataList");
      }
    });
  });
};
defineExpose({
  init
});
</script>
<style lang="scss">
.contractTemplateDialog {
  padding: 24px;
  .el-dialog__headerbtn {
    transform: translate(-25%, 25%);
  }
  .el-form-item {
    display: block;
  }
}
</style>
