<template>
    <el-dialog :footer="null" v-model="visibleShow" title="设置" :close-on-click-modal="false" width="480">
      <ny-button-group
        :list="[
          { dictLabel: '话费阈值', dictValue: '1' },
          { dictLabel: '冷却天数', dictValue: '2' }
        ]"
        v-model="visibleTab"
        style="display: inline-block; margin-bottom: 6px"
        @change="getThreshold"
      ></ny-button-group>
      <template v-if="visibleTab == '1'">
        <el-descriptions style="width: 100%" border :column="1">
          <el-descriptions-item>
            <template #label>
              <span>阈值余额(元)<span style="color: red">*</span></span>
            </template>
            <el-input v-model="params.paramValue" placeholder="请输入"></el-input>
          </el-descriptions-item>
        </el-descriptions>
        <div style="display: flex; align-items: center; gap: 5px; margin-top: 5px">
          <el-icon color="var(--el-color-primary)" size="18"><InfoFilled /></el-icon>
          <el-text type="primary">低于此阈值，查询话费时将进行警示</el-text>
        </div>
      </template>
      <template v-else>
        <el-descriptions style="width: 100%" border :column="1">
          <el-descriptions-item>
            <template #label>
              <span>绑定冷却天数</span>
            </template>
            <el-input v-model="params.paramValue" placeholder="请输入"></el-input>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <div style="height: 60px;"></div>
      <template #footer>
        <el-button :loading="submitLoading" @click="visibleShow = false">{{ $t("cancel") }}</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submitThreshold()">确定</el-button>
      </template>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);

// 设置
const visibleTab = ref("1");
const visibleShow = ref(false);
const params = ref(<any>{});
const getThreshold = () => {
  baseService.get( visibleTab.value == '1' ? "/sys/params/getByCode/mobileThreshold" : "/sys/params/getByCode/mobileCoolingDays").then((res) => {
    if (res.code == 0 && res.data) {
      params.value = res.data;
    }
  });
};

const submitLoading = ref(false);
const submitThreshold = () => {
  if ( !params.value.paramValue && params.value.paramCode == "mobileThreshold" ) {
    ElMessage.warning("请输入话费阈值");
    return;
  }

  submitLoading.value = true;
  baseService
    .put("/sys/params", params.value)
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("保存成功");
        visibleShow.value = false;
        emits("refresh");
      }
    })
    .finally(()=>{
        submitLoading.value = false;
    })
};

const init = () => {
  visibleShow.value = true;
  getThreshold();
};

defineExpose({
  init
});
</script>

<style lang='less' scoped>

</style>