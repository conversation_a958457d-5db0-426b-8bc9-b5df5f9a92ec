<template>
    <el-dialog v-model="visible" :title=" !dataForm.id ? '新增列' : '编辑列'"  width="560" @close="closeFn">
        <el-form :model="dataForm" label-position="top" :rules="rules" ref="dataFormRef">
            <el-form-item label="列名称" prop="name">
                <el-input v-model="dataForm.name" placeholder="请输入" :disabled="isEdit"/>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="dataForm.type" placeholder="请选择类型" :disabled="isEdit" style="width: 100%;">
                    <el-option label="文本" :value="1" />
                    <el-option label="数值" :value="2" />
                    <el-option label="下拉引用" :value="3" />
                    <el-option label="日期" :value="4" />
                    <el-option label="图片上传" :value="5" />
                </el-select>
            </el-form-item>
            <el-form-item label="选项" required v-if=" dataForm.type == '3'">
                <div class="custom">
                    <div class="custom_item">
                        <div class="custom_item_add">
                            <el-input v-model="optionValue" placeholder="选项名称" style="margin-right: 10px;"/>
                            <el-button type="primary" @click="optionAdd">新增选项</el-button>
                        </div>
                        <div class="custom_item_ul">
                            <div v-for=" (item,index) in optionList " class="custom_item_li" >
                                <span>{{ item }}</span>
                                <el-icon color="#F56C6C" size="18" style=" cursor: pointer;" @click.stop="optionDelete(index)"><DeleteFilled /></el-icon>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </el-form-item>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="是否必填" prop="isMust">
                        <el-switch 
                            v-model="dataForm.isMust" 
                            inline-prompt
                            active-text="是"
                            inactive-text="否"/>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="开启筛选" prop="isSelect" v-if=" dataForm.type != '5' ">
                        <el-switch 
                            v-model="dataForm.isSelect" 
                            inline-prompt
                            active-text="开启"
                            inactive-text="关闭"/>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if=" dataForm.type == '2' ">
                    <el-form-item label="启用聚合" prop="isSum" >
                        <el-switch 
                            v-model="dataForm.isSum" 
                            inline-prompt
                            active-text="开启"
                            inactive-text="关闭"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if=" dataForm.type == '1' || dataForm.type == '2' ">
                <el-col :span="dataForm.defaultType != 0 ? 18 :24">
                    <el-form-item label="默认值类型" prop="defaultType" >
                        <el-select v-model="dataForm.defaultType" placeholder="请选择默认值类型" style="width: 100%;" @change="defaultTypeChange">
                            <el-option label="默认" :value="0" />
                            <el-option label="数据联动" :value="1" />
                            <el-option label="公式编辑" :value="2" v-if="dataForm.type == '2'"/>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="dataForm.defaultType != 0">
                    <el-button @click="dataLinkageFn" style="margin-top: 30px;width: 100%;" v-if="dataForm.defaultType == 1">编辑联动</el-button>
                    <el-button @click="editFormulaFn" style="margin-top: 30px;width: 100%;" v-if="dataForm.defaultType == 2">编辑公式</el-button>
                </el-col>
            </el-row>
            <el-form-item label="排序" prop="sort">
                <el-input v-model="dataForm.sort" placeholder="请输入"/>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" :loading="butLoading" @click="submit">提交</el-button>
        </template>
    </el-dialog>
    <!-- 数据联动 -->
    <dataLinkage ref="dataLinkageRef" @dataLinkageChange="dataChange"></dataLinkage>
    <!-- 公式编辑器 -->
    <editFormula ref="editFormulaRef" @formulaChange="formulaChange"></editFormula>
</template>

<script lang='ts' setup>
import { ref, reactive, nextTick } from 'vue';
import baseService from "@/service/baseService";
import dataLinkage from './dataLinkage.vue';
import editFormula from './editFormula.vue';
import { ElMessage,ElMessageBox } from "element-plus";

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);

const dataFormRef = ref();
const dataForm = reactive({
    id:'',
    name: '',
    type: null,
    isMust: false,
    isSum: false,
    isSelect: false,
    sort: '',
    defaultType: 0,
    options: '',
    indexName: '',
    rule: <any>null,
    formula: <any>{}
})

const rules = ref({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' },],
    type: [{ required: true, message: '请选择类型', trigger: 'blur' },],
});

// 提交
const butLoading = ref(false);
const submit = () =>{
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }

        
        if(dataForm.type == 3){
            if(!optionList.value.length){
                ElMessage.warning('选项不能为空,请新增选项后提交!');
                return
            }
            dataForm.options = optionList.value.join(',');
        }

        // 未设置数据联动配置则删除 rule
        if((dataForm.type == 1 || dataForm.type == 2) && dataForm.defaultType == 1 && dataForm.rule && !Object.keys(dataForm.rule).length){
            return ElMessage.warning('数据联动不能为空,请编辑后提交!');
        }
        // 未设置数据联动配置则删除 formula
        if((dataForm.type == 1 || dataForm.type == 2) && dataForm.defaultType == 2 && dataForm.formula && !dataForm.formula.formula){
            return ElMessage.warning('公式编辑不能为空,请编辑后提交!');
        }
        
        console.log(dataForm,'====== dataForm ======');
        butLoading.value = true;
        (!dataForm.id ? baseService.post : baseService.put)('/report/reportcolumn',dataForm).then(res=>{
            ElMessage.success({
                message: !dataForm.id ? "新增成功" : '编辑成功',
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        }).finally(()=>{
            butLoading.value = false;
        })
    })
}

// 弹窗关闭回调
const closeFn = () =>{
    isEdit.value = false;
}

const isEdit = ref(false);
const init = (indexName:any,id?: number) => {
  visible.value = true;
  dataForm.indexName = indexName;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    optionValue.value = "";
    dataForm.id = "";
    dataForm.rule = null;
    dataForm.formula = {};
    dataForm.defaultType = 0;
    delete dataForm.dynamicKey;
    optionList.value = []
  }
  if(id){
    isEdit.value = true;
    getInfo(id);
  }
};

const getInfo = (id:any) =>{
    baseService.get('/report/reportcolumn/'+ id).then(res=>{
        if(res.data.rule){
            res.data.rule.ruleList = JSON.parse(res.data.rule.rules);
        }
        if(res.data.formula){
            res.data.formula.formula = JSON.parse(res.data.formula.formulaConfig);
        }
        if(res.data.options){
            optionList.value = res.data.options.split(',');
        }
        Object.assign(dataForm,res.data)
        console.log(dataForm);
    })
}


// 下拉选项
const optionValue = ref('');
const optionList = ref(<any>[]);
const optionAdd = () =>{
    if (!optionValue.value) {
        ElMessage({
            message: '选项名称不能为空！',
            type: 'warning',
        })
        return;
    }
    optionList.value.push(optionValue.value);
    optionValue.value = '';
}
const optionDelete = (index:any) =>{
    optionList.value.splice(index,1);
}

defineExpose({
  init
});

// 默认值类型切换
const defaultTypeChange = (value:any) =>{
    if(value == 0){
        dataForm.rule = null;
        dataForm.formula = {};
    }else if(value == 1){
        dataForm.formula = {};
    }else if(value == 2){
        dataForm.rule = null;
        dataForm.formula = {
            indexName: dataForm.indexName
        }
    }
}


// 数据联动
const dataLinkageRef = ref();
const dataLinkageFn = () =>{
    if(!dataForm.name){
        ElMessage.warning('请输入列名称');
        return;
    }
    nextTick(()=>{
        dataLinkageRef.value.init(dataForm.indexName,dataForm.name,dataForm.type,dataForm.rule);
    })
}

// 数据联动回调
const dataChange = (value:any) =>{
    dataForm.rule = value;
}

// 公式编辑
const editFormulaRef = ref()
const editFormulaFn = () =>{
    nextTick(()=>{
        editFormulaRef.value.init(dataForm.indexName,dataForm.formula.formula)
    })
}

// 公式编辑回调
const formulaChange = (value:any) =>{
    dataForm.formula.formula = value
}

</script>

<style lang='less' scoped>
.custom{
    width: 100%;
    .custom_item{
        width: 100%;
        .custom_item_ul{
            max-height: 150px;
            overflow: auto;
            .custom_item_li{
                display: flex;
                align-items: center;
                padding: 2px 5px;
                margin-bottom: 5px;
                border: 1px solid #f0f0f0;
                border-radius: 5px;
                span{
                    flex: 1;
                    line-height: 22px;
                }
            }
        }
        
        .custom_item_add{
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
    }
}
</style>