<template>
  <el-drawer v-model="visible" :title="markerAfterSale ? '订单标记售后' : !showLog ? '订单详情' : '操作日志'" size="40%" class="ny-drawer article-add-or-update">
    <el-card class="article-add-or-update-form" v-loading="dataLoading">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions class="descriptions-label-140" :column="1" border>
        <el-descriptions-item label="订单编号">{{ resData.sn || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏大区">{{ resData.serverName ? resData.serverName : orderInfo.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="卖方手机号">{{ resData.customerPhone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="账号价格"
          ><el-text type="danger">￥{{ orderInfo?.acquisitionPrice || resData.amount || resData.recyclePrice || "-" }}</el-text></el-descriptions-item
        >
        <el-descriptions-item label="包赔费">￥{{ orderInfo.guaranteeAmount ? orderInfo.guaranteeAmount : resData.guaranteePrice ? resData.guaranteePrice : "-" }}</el-descriptions-item>
        <template v-if="!showLog">
          <el-descriptions-item label="创建时间">{{ resData.createDate }}</el-descriptions-item>
          <el-descriptions-item label="打款时间">{{ resData.payDate || "-" }}</el-descriptions-item>
          <el-descriptions-item label="换绑时间">{{ changeInfo.createDate || "-" }}</el-descriptions-item>
          <el-descriptions-item label="回收成功时间">{{ resData.dealDate || "-" }}</el-descriptions-item>
          <el-descriptions-item label="回收人">{{ resData.purchaseUserName || "-" }}</el-descriptions-item>

          <el-descriptions-item v-if="!showLog" label="买方手机号">{{ changeInfo.ourBindPhone || "-" }}</el-descriptions-item>
          <el-descriptions-item label="游戏密码">
            <div class="flx-justify-between" v-if="changeInfo.ourPassword">
              <span>{{ isShowGamePassword ? changeInfo.ourPassword : "******" }}</span>
              <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                <View v-if="!isShowGamePassword" />
                <Hide v-if="isShowGamePassword" />
              </el-icon>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="属性明细" v-if="resData.state == '待收购'">
            <template #label>
              <div style="display: flex; align-items: center; gap: 4px; cursor: pointer" @click="copyInfo(resData.shopInfo)">
                <span>属性明细</span><el-icon><DocumentCopy /></el-icon>
              </div>
            </template>
            <div style="cursor: pointer; max-height: 500px; overflow-y: scroll" @dblclick="copyInfo(resData.shopInfo)">
              {{ resData.shopInfo }}
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </el-card>
    <el-card class="mt-12" v-if="markerAfterSale">
      <div class="p-title mt-0">标记售后</div>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="售后类型" prop="delistingCause">
              <ny-select v-model="dataForm.delistingCause" dict-type="after_sales_type" placeholder="请选择售后类型" @change="dataForm.subDelistingCause = ''" filterValue="CANCEL"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.delistingCause == 'ACCOUNT_RETURN' || dataForm.delistingCause == 'CANCEL'">
            <el-form-item label="二级原因" prop="subDelistingCause" v-if="dataForm.delistingCause == 'ACCOUNT_RETURN'">
              <ny-select v-model="dataForm.subDelistingCause" dict-type="retrieve_type_sale" placeholder="请选择二级原因"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.delistingCause == 'OTHER' || (dataForm.delistingCause == 'CANCEL' && dataForm.subDelistingCause == '0')">
            <el-form-item label="其他原因备注" prop="saleAfterRemark">
              <el-input v-model="dataForm.saleAfterRemark" placeholder="请输入其他原因备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请选择处理人" prop="aftermarketProcessor">
              <ny-select-search v-model="dataForm.aftermarketProcessor" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择处理人" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题截图" prop="issueImg">
              <ny-upload v-model:imageUrl="dataForm.issueImg" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <template #footer v-if="markerAfterSale">
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
    </template>

    <el-card class="mt-12" v-if="showLog && state.hasPermission('purchase:purchaseorder:logPage') && !markerAfterSale">
      <div class="p-title mt-0">操作日志</div>
      <ny-table cellHeight="ch-40" :state="state" :columns="columns" :pagination="true" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle"> </ny-table>
    </el-card>
  </el-drawer>
</template>

<script lang="ts" setup>
import useClipboard from "vue-clipboard3";
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refresh"]);
const view = reactive({
  getDataListURL: "/purchase/logPage",
  createdIsNeed: false,
  getDataListIsPage: true,
  dataForm: {
    orderId: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 是否订单标记售后
const markerAfterSale = ref(false);

const columns = reactive([
  {
    prop: "optionalUserName",
    label: "操作人"
  },
  {
    prop: "optionalTitle",
    label: "操作行为"
  },
  {
    prop: "createDate",
    label: "操作时间"
  }
]);
const visible = ref(false);

// 是否显示日志
const showLog = ref(false);

// 订单信息
const orderInfo = ref(<any>{});

// 换绑信息
const changeInfo = ref(<any>{});

const orderId = ref("");
const init = (id: any, marker?: boolean, type?: string, row?: any) => {
  orderInfo.value = row && row.orderInfo ? row.orderInfo : {};
  changeInfo.value = row && row.changeInfo ? row.changeInfo : {};
  console.log(row);
  
  resData.value = {
    serverName: row.serverName,
    customerPhone: row.customerPhone,
    guaranteePrice: row.guaranteePrice,
    gameAccount: row.gameAccount,
    amount: row.amount,
    createDate: row.createDate,
    purchaseUserName: row.realName,
    dealDate: row.inboundTime, // 入库时间
    payDate: row.payTime, // 支付时间
    ...row
  };
  dataForm.value.saleOrderId = id;
  markerAfterSale.value = marker ? true : false;
  visible.value = true;
  showLog.value = type == "log" ? true : false;
  orderId.value = id;
  view.dataForm.orderId = id;
  getDetails();
  state.getDataList();
};
const resData = ref(<any>{});
const dataLoading = ref(false);
const getDetails = () => {
  dataLoading.value = true;
  baseService
    .get("/purchase/orderLog/" + orderId.value)
    .then((res) => {
      if (res.code === 0) {
        resData.value = {
          ...resData.value,
          ...res.data,
          dealDate: resData.value.inboundTime, // 入库时间
          payDate: resData.value.payTime // 支付时间
        };
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 是否显示游戏密码
const isShowGamePassword = ref(false);

// 标记售后
const dataForm = ref(<any>{
  aftermarketProcessor: "",
  issueImg: ""
});

const rules = reactive({
  delistingCause: [{ required: true, message: "请选择售后类型", trigger: "change" }],
  subDelistingCause: [{ required: true, message: "请选择二级原因", trigger: "change" }],
  aftermarketProcessor: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
  issueImg: [{ required: true, message: "请上传问题截图", trigger: "change" }]
});

//
const resultoptions = ref([
  {
    label: "未售未放款被找回",
    value: "1"
  },
  {
    label: "未售已放款被找回",
    value: "2"
  },
  {
    label: "已售未放款被找回",
    value: "3"
  },
  {
    label: "已售已放款被找回",
    value: "4"
  },
  {
    label: "未售未放款疑似被找回",
    value: "5"
  },
  {
    label: "未售已放款疑似被找回",
    value: "6"
  },
  {
    label: "已售未放款疑似被找回",
    value: "7"
  },
  {
    label: "已售已放款疑似被找回",
    value: "8"
  }
]);
const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let data = JSON.parse(JSON.stringify(dataForm.value));
    data.orderId = orderId.value;
    console.log(data);

    baseService
      .post("/shop/shop/signAfterSale", data)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          if (dataForm.value.delistingCause == "ACCOUNT_RETURN") {
            // 账号找回加黑名单
            addBlackData();
          }
          emit("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
const addBlackData = () => {
  baseService.post("/blacklist/blacklist", {
    id: null,
    account: resData.value.gameAccount,
    gameName: resData.value.gameName,
    idCard: "-",
    name: "-",
    phone: resData.value.phone,
    platform: "-",
    remarks: resultoptions.value[+dataForm.value.subDelistingCause - 1].label
  });
};
const { toClipboard } = useClipboard();
// 复制到粘贴板
const copyInfo = async (info: any) => {
  try {
    await toClipboard(info);
    ElMessage.success("复制成功");
  } catch (e: any) {
    ElMessage.warning("您的浏览器不支持复制：", e);
  }
};
defineExpose({
  init
});
</script>
