<template>
  <div class="mod-sys__dept TableXScrollSty">
    <!-- <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:dept:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
    </el-form> -->
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('sys:dept:save')" type="primary" @click="addOrUpdateHandle()">新增部门</el-button>
        </template>
        <template #header-right>
          <div style="display: flex">
            <el-input v-model="state.dataForm.username" :prefix-icon="Search" placeholder="请输入部门名称" clearable style="width: 280px !important; margin-right: 12px"></el-input>
            <el-button type="primary" @click="state.getDataList()" style="margin-right: 12px">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
          </div>
        </template>
        <template #isEnable="{ row }">
          <el-switch :active-value="1" :inactive-value="0" v-model="row.isEnable" @click="changeStatus(row)" />
        </template>

        <!-- 操作 -->
        <template #operation="scope">
          <el-button v-if="state.hasPermission('sys:dept:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button v-if="state.hasPermission('sys:dept:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    <!-- </el-card> -->

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./dept-add-or-update.vue";
import useView from "@/hooks/useView";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const view = reactive({
  getDataListURL: "/sys/dept/list",
  deleteURL: "/sys/dept",
  dataForm: {
    username: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
  {
    prop: "name",
    label: "部门名称",
    minWidth: 150
  },
  {
    prop: "TODO1",
    label: "部门人数",
    minWidth: 150
  },
  {
    prop: "leaderName",
    label: "负责人",
    minWidth: 80
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 80
  },
  {
    prop: "isEnable",
    label: "是否启用",
    minWidth: 80
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 140
  }
]);
const getResetting = () => {
  state.dataForm.username = "";
};
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const changeStatus = (row: any) => {
//   row.isEnable = row.isEnable == 1 ? 0 : 1;
  baseService.put("/sys/dept", row).then((res) => {
    ElMessage.success("操作成功！");
    state.getDataList();
  });
};
</script>
