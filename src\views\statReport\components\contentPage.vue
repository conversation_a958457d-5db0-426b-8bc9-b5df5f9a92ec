<template>
    <div>
        <div class="content_header flx-justify-between">
            <div class="title">{{ props.menuInfo.title }}{{ isSetUp ? '设置' : '' }}</div>
            <div class="butList" v-if="!isSetUp">
                <!-- <el-button>更新统计图</el-button> -->
                <el-button @click="isSetUp = ! isSetUp">前往设置</el-button>
            </div>
            <div v-else>
                <el-button @click="isSetUp = ! isSetUp">返回</el-button>
            </div>
        </div>

        <!-- 设置列名 -->
        <setUpPage v-if="isSetUp" :menuInfo="JSON.parse(JSON.stringify(props.menuInfo))"></setUpPage>
        <!-- 数据表 -->
        <tablesPage v-else :menuInfo="JSON.parse(JSON.stringify(props.menuInfo))"></tablesPage>
    </div>
</template>

<script lang='ts' setup>
import { ref,reactive, onMounted } from 'vue';
import tablesPage from './tablesPage.vue';
import setUpPage from './setUpPage.vue';

interface Props {
    menuInfo: any
}
const props = withDefaults(defineProps<Props>(), {
    menuInfo:''
})

const isSetUp = ref(false);


onMounted(()=>{
    console.log(props.menuInfo)
})


</script>

<style lang='less' scoped>
.content_header{
    // border: 1px solid orange;

    .title{
        font-weight: 500;
        font-size: 16px;
        color: #303133;
        line-height: 24px;
    }
    
}
</style>