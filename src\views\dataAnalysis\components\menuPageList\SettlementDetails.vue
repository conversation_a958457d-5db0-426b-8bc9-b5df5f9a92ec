<template>
  <div class="analysis_page">
    <div class="card_analysis mt-12" style="background: #f7f8fa">
      <div class="header_analysis" style="padding: 22px 20px 20px 20px">
        <div class="header_analysis_left flx-align-center">
          <div class="header_analysis_title" style="font-size: 20px; margin-left: 0px">结算流水明细</div>
        </div>
        <div class="header_analysis_right flx-align-center">
          <el-button round :loading="exportclickLoading" @click="exportclick">导出数据</el-button>
        </div>
      </div>
      <div style="padding: 0px 20px 20px 20px">
        <el-table :data="tableData" class="analysis_table" border @sort-change="sortChange">
          <el-table-column prop="department" align="center">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.deptId" :list="departmentList" placeholderColor="#303133" clearable isSolid :isBorder="false" labelKey="departmentName" valueKey="departmentId" placeholder="部门" @change="getSettlementFlowDetail"></NyDropdownMenu>
            </template>
            <template #default="{ row }">
              <span>{{ row.department || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderTypeName" align="center">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.orderType" placeholderColor="#303133" :list="orderList" isSolid :isBorder="false" clearable labelKey="label" valueKey="value" placeholder="订单" @change="getSettlementFlowDetail"></NyDropdownMenu>
            </template>
          </el-table-column>
          <el-table-column prop="billType" label="类型" align="center">
            <template #default="{ row }">
              {{ row.orderType == "1" ? "收入" : "支出" }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额(元)" sortable="custom" width="180" align="center" />
          <el-table-column prop="settlementPerson" align="center">
            <template #header>
              <NyDropdownMenu class="analysis_table_Menu" v-model="dataForm.employeeId" placeholderColor="#303133" :list="employeeList" isSolid :isBorder="false" clearable labelKey="employeeName" valueKey="employeeId" placeholder="结算人" @change="getSettlementFlowDetail"></NyDropdownMenu>
            </template>
            <template #default="{ row }">
              <span>{{ row.settlementPerson || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="settlementTime" label="结算时间" sortable="custom" width="180" align="center" />
          <!-- <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="primary" text bg >查看</el-button>
            </template>
          </el-table-column> -->
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
        <el-pagination :current-page="dataForm.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="dataForm.size" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted } from "vue";
import { fileExport } from "@/utils/utils";

const tableData = ref([]);
const total = ref();

const dataForm = ref({
  gameId: 0,
  recyclingChannelId: 0,
  employeeId: 0,
  startTime: "",
  endTime: "",
  orderType: "",
  page: 1,
  size: 10,
  deptId: ""
});

const departmentList = ref(); // 部门列表
// 部门列表
const getDepartmentSearchList = () => {
  baseService.get("/dataAnalysis/departmentSearchList").then((res) => {
    departmentList.value = res.data;
  });
};

const employeeList = ref(); // 选择员工列表
// 员工列表
const getEmployeeList = () => {
  baseService.get("/dataAnalysis/employeeSearchList").then((res) => {
    employeeList.value = res.data;
  });
};

const orderList = ref([
  { label: "回收订单", value: "0" },
  { label: "销售订单", value: "1" },
  { label: "售后订单", value: "2" }
]);

onMounted(() => {
  getDepartmentSearchList();
  getEmployeeList();
  getSettlementFlowDetail();
});

const getSettlementFlowDetail = () => {
  baseService.post("/dataAnalysis/settlementFlowDetail", dataForm.value).then((res) => {
    total.value = res.data.total;
    tableData.value = res.data.list;
  });
};

const sizeChange = (number: any) => {
  dataForm.value.size = number;
  getSettlementFlowDetail();
};

const currentChange = (number: any) => {
  dataForm.value.page = number;
  getSettlementFlowDetail();
};

// 排序事件
const sortChange = (column: any) => {
  console.log(column);
  if (column.order) {
    dataForm.value.orderItem = {
      column: column.prop,
      asc: column.order == "ascending" ? true : false
    };
  }else{
    delete dataForm.value.orderItem
  }
  getSettlementFlowDetail();
};

const exportclickLoading = ref(false);
const exportclick = () => {
  exportclickLoading.value = true;
  baseService
    .get("/dataAnalysis/settlementFlowDetailExport", dataForm.value)
    .then((res) => {
      if (res) {
        fileExport(res, "结算流水明细");
      }
    })
    .finally(() => {
      exportclickLoading.value = false;
    });
};

const init = (form: any) => {
  Object.assign(dataForm.value, form);
  getSettlementFlowDetail();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.analysis_table_Menu{
  :deep(.placeholder){
    font-weight: 600;
  }
  :deep(.clickValue){
    font-weight: 600;
  }
}
.card_analysis {
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e5e6eb;

  .header_analysis {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 20px;

    .header_analysis_left {
      .header_analysis_title {
        font-weight: 500;
        font-size: 16px;
        color: #1d252f;
        line-height: 24px;
        margin-left: 4px;
      }
    }

    .header_analysis_right {
      .legend {
        margin-right: 16px;

        :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
          color: #1d2129;
        }

        .el-checkbox:nth-child(1) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #4165d7;
            border-color: #4165d7;
          }
        }

        .el-checkbox:nth-child(2) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #00b42a;
            border-color: #00b42a;
          }
        }

        .el-checkbox:nth-child(3) {
          :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
            background-color: #722ed1;
            border-color: #722ed1;
          }
        }
      }
    }
  }

  .header_describe {
    font-weight: 400;
    font-size: 13px;
    color: #4e5969;
    line-height: 22px;
    padding: 0px 20px;
  }

  .center_analysis {
    padding: 12px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .listMap {
      width: 200px;

      .listMap_label {
        span {
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          line-height: 22px;
          margin-right: 2px;
        }
      }

      .listMap_value {
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
      }
    }
  }
}

.analysis_type {
  display: flex;
  align-items: center;
  gap: 10px;
}

.analysis_type_item {
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  padding: 3px 12px;
  background: #ffffff;
  border-radius: 24px;
  border: 1px solid #d4d7de;
  cursor: pointer;
}

.active {
  background: #4165d7;
  color: #ffffff;
  border: 1px solid #4165d7;
}

.analysis_table {
  width: 100%;

  :deep(th .cell) {
    background: none !important;
    font-weight: bold;
    font-size: 14px;
    // color: #909399;
    line-height: 22px;
  }

  // :deep(th:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.1) !important;
  // }

  // :deep(td:nth-child(n+2):nth-child(-n+4)) {
  //     background-color: rgba(65,101,215,0.05) !important;
  // }
}

.docking_line {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .line_left {
    display: flex;
    align-items: center;
    gap: 4px;

    .partners {
      width: 32px;
      height: 40px;
      position: relative;

      img {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid #606266;
      }

      .mask {
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        border-radius: 6px;
        width: 32px;
        height: 32px;
        top: 0;
        left: 0;
        z-index: 2;
      }

      .selected {
        position: absolute;
        left: 9px;
        bottom: -7px;
        z-index: 2;
      }
    }
  }

  .line_right {
    font-weight: 400;
    font-size: 12px;
    color: #303133;
    line-height: 14px;

    span {
      color: var(--el-color-primary);
    }
  }
}
</style>
