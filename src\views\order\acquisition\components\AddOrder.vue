<template>
  <el-drawer v-model="visible" :title="currentType == 'create' ? '创建回收单' : '新增回收订单'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="944" class="ny-drawer">
    <el-card class="cardDescriptions" style="padding: 0">
      <div class="ny-button-group" style="margin-bottom: 10px">
        <div class="button-group">
          <div
            class="button-item"
            v-for="(item, index) in [
              { label: '简易回收', value: 1 },
              { label: '全流程回收', value: 2 }
            ]"
            :key="index"
            :class="{ active: acquisitionType == item.value }"
            @click="acquisitionType = item.value"
          >
            <span class="flx-center" style="gap: 4px">
              <span>{{ item.label }} </span>
              <el-tooltip class="box-item" effect="dark" :content="index == 0 ? '只需一步操作，快速完成账号商品回收，适合三方平台商品回收场景' : '从账号线索、收集包赔、换绑、签署合同、打款全流程商品回收，适合本平台内商品回收场景'" placement="top">
                <el-icon size="14"><QuestionFilled /></el-icon> </el-tooltip
            ></span>
          </div>
        </div>
      </div>
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">
        <template v-if="acquisitionType == 2">
          <div class="titleSty">回收信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label
                ><span>回收渠道<span style="color: red">*</span></span></template
              >
              <el-form-item label="回收渠道" prop="channelId">
                <el-cascader style="width: 100%" :show-all-levels="false" clearable v-model="selectedOptions" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择出售渠道" @change="handleChange" />
                <!-- <ny-select-search v-model="dataForm.channelId" labelKey="title" valueKey="id" url="/channel/channel/page" :param="{ limit: 9999, state: 0, channelType: 1 }" placeholder="选择回收渠道" @changeReturnEntity="getChannelName" /> -->
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>游戏名称<span style="color: red">*</span></span></template
              >
              <el-form-item label="游戏名称" prop="gameId">
                <el-select v-model="dataForm.gameId" filterable placeholder="请选择游戏名" @change="gameChange">
                  <el-option v-for="(item, index) in gamesList" :key="index" :label="item.title" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item v-if="dataForm.gameId">
              <template #label
                ><span>选择大区<span style="color: red">*</span></span></template
              >
              <el-form-item label="选择大区" prop="district">
                <el-select v-model="dataForm.district" placeholder="选择大区" @change="(dataForm.server = ''), getServerList()">
                  <el-option v-for="item in districtList" :key="item.id" :label="item.title" :value="item.id + '-' + item.title"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item v-if="serverList && serverList.length">
              <template #label
                ><span>选择大区<span style="color: red">*</span></span></template
              >
              <el-form-item label="选择服务器" prop="server">
                <el-select v-model="dataForm.server" placeholder="选择服务器">
                  <el-option v-for="item in serverList" :key="item.id" :label="item.title" :value="item.id + '-' + item.title"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>游戏账号<span style="color: red">*</span></span></template
              >
              <el-form-item label="游戏账号" prop="gameAccount">
                <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收价格(元)<span style="color: red">*</span></span></template
              >
              <el-form-item label="回收价格(元)" prop="purchasePrice">
                <el-input-number class="numIpt" style="width: 100%" type="number" :controls="false" :max="********" :precision="2" v-model="dataForm.purchasePrice" placeholder="请输入回收价格"></el-input-number>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>卖方手机号<span style="color: red">*</span></span></template
              >
              <el-form-item label="卖方手机号" prop="customerPhone">
                <el-input v-model="dataForm.customerPhone" placeholder="请输入卖方手机号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>估价客服</span></template>
              <el-form-item label="估价客服">
                <ny-select-search v-model="dataForm.appraiseUserId" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="选择估价客服" />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template v-else>
          <div class="titleSty">回收信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label
                ><span>游戏名称<span style="color: red">*</span></span></template
              >
              <el-form-item label="游戏名称" prop="gameId">
                <el-select v-model="dataForm.gameId" filterable placeholder="请选择游戏名" @change="gameChange">
                  <el-option v-for="(item, index) in gamesList" :key="index" :label="item.title" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收渠道<span style="color: red">*</span></span></template
              >
              <el-form-item label="回收渠道" prop="channelId">
                <el-cascader style="width: 100%" :show-all-levels="false" clearable v-model="selectedOptions" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择出售渠道" @change="handleChange" />
                <!-- <ny-select-search v-model="dataForm.channelId" labelKey="title" valueKey="id" url="/channel/channel/page" :param="{ limit: 9999, state: 0, channelType: 1 }" placeholder="请选择回收渠道" @changeReturnEntity="getChannelName" /> -->
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>游戏账号<span style="color: red">*</span></span></template
              >
              <el-form-item label="游戏账号" prop="gameAccount">
                <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>游戏密码<span style="color: red">*</span></span></template
              >
              <el-form-item label="游戏密码" prop="gamePassword">
                <el-input v-model="dataForm.gamePassword" type="password" show-password placeholder="请输入游戏密码" autocomplete="new-password"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收人<span style="color: red">*</span></span></template
              >
              <el-form-item label="回收人" prop="recycleUser">
                <ny-select-search v-model="dataForm.recycleUser" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择回收人" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>绑定手机号码<span style="color: red">*</span></span></template
              >
              <el-form-item label="绑定手机号码" prop="bindingPhone">
                <el-input v-model="dataForm.bindingPhone" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>回收价(元)<span style="color: red">*</span></span></template
              >
              <el-form-item label="回收价(元)" prop="recyclePrice">
                <el-input-number class="numIpt" style="width: 100%" type="number" :controls="false" :max="********" :precision="2" v-model="dataForm.recyclePrice" placeholder="请输入回收价"></el-input-number>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>支付方式<span style="color: red">*</span></span></template
              >
              <el-form-item label="支付方式" prop="payWay">
                <el-select v-model="dataForm.payWay" clearable placeholder="请选择支付方式">
                  <el-option v-for="(item, index) in ['支付宝', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>支付宝订单号</span></template>
              <el-form-item label="支付宝订单号" prop="alipayOrderNo">
                <el-input v-model="dataForm.alipayOrderNo" placeholder="请输入支付宝订单号" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>

            <!-- 未上传付款截图则状态为已换绑 else 已付款 -->
            <el-descriptions-item>
              <template #label><span>付款截图</span></template>
              <el-form-item label="付款截图" prop="payImg">
                <ny-upload v-model:imageUrl="dataForm.payImg" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>二次实名截图</span></template>
              <el-form-item label="二次实名截图" prop="realNameImg">
                <ny-upload v-model:imageUrl="dataForm.realNameImg" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template #label><span>换绑凭证</span></template>
              <el-form-item label="换绑凭证" prop="changeBindingVoucher">
                <ny-upload v-model:imageUrl="dataForm.changeBindingVoucher" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>自编码</span></template
              >
              <el-form-item label="自编码" prop="ownCoding">
                <el-input v-model="dataForm.ownCoding" placeholder="自编码"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>应急手机号</span></template
              >
              <el-form-item label="应急手机号" prop="emergencyPhone">
                <el-input v-model="dataForm.emergencyPhone" placeholder="应急手机号"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>合同签署</span></template>
              <el-form-item label="合同签署" prop="contractSign">
                <el-select v-model="dataForm.contractSign" clearable placeholder="请选择合同签署">
                  <el-option v-for="(item, index) in ['未签署', '已签署']" :label="item" :value="index" :key="index"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>合同附件</span></template>
              <el-form-item label="合同附件" prop="contractAttachment">
                <ny-upload-file style="margin-bottom: -10px" tip="" v-model:fileSrc="dataForm.contractAttachment" :isDrag="false" :limit="1" :fileSize="5" :isSelfSty="false">
                  <template #content>
                    <el-button :icon="Plus">上传</el-button>
                  </template>
                </ny-upload-file>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template #label><span>备注</span></template>
              <el-form-item label="备注" prop="remark">
                <el-input type="textarea" v-model="dataForm.remark" placeholder="请输入备注" :rows="5"></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <div class="titleSty" style="margin-top: 10px">包赔信息</div>
          <el-descriptions :column="2" border class="descriptions">
            <el-descriptions-item>
              <template #label
                ><span>卖方姓名<span style="color: red">*</span></span></template
              >
              <el-form-item label="卖方姓名" prop="saleUserName"> <el-input v-model="dataForm.saleUserName" placeholder="请输入卖方姓名"></el-input> </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label
                ><span>卖方手机号<span style="color: red">*</span></span></template
              >
              <el-form-item label="卖方手机号" prop="customerPhone"> <el-input v-model="dataForm.customerPhone" placeholder="请输入卖方手机号"></el-input> </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>卖方身份证</span></template>
              <el-form-item label="卖方身份证" prop="sellerIdCard"> <el-input v-model="dataForm.sellerIdCard" placeholder="请输入卖方身份证"></el-input> </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>微信号</span></template>
              <el-form-item label="微信号" prop="wxNo"> <el-input v-model="dataForm.wxNo" placeholder="请输入微信号"></el-input> </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>所在城市</span></template>
              <el-form-item label="所在城市" prop="city">
                <el-cascader style="width: 100%" v-model="dataForm.city" :options="regionData" placeholder="请选择所在城市" clearable @change="handleCityChange" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>详细地址</span></template>
              <el-form-item label="详细地址" prop="detailedAddress">
                <el-input v-model="dataForm.detailedAddress" placeholder="请输入详细地址" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>身份证正面</span></template>
              <el-form-item label="身份证正面" prop="idCardFront">
                <ny-upload v-model:imageUrl="dataForm.idCardFront" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>身份证反面</span></template>
              <el-form-item label="身份证反面" prop="idCardBack">
                <ny-upload v-model:imageUrl="dataForm.idCardBack" :limit="99" :fileSize="5" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>紧急联系人1</span></template>
              <el-form-item label="紧急联系人1" prop="emergencyContacts1Type">
                <el-radio-group v-model="dataForm.emergencyContacts1Type">
                  <el-radio :value="1">父亲</el-radio>
                  <el-radio :value="2">母亲</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>紧急联系人1名称</span></template>
              <el-form-item label="紧急联系人1名称" prop="emergencyContacts1Name">
                <el-input v-model="dataForm.emergencyContacts1Name" placeholder="请输入紧急联系人1名称" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>紧急联系人1手机</span></template>
              <el-form-item label="紧急联系人1手机" prop="emergencyContacts1Phone">
                <el-input v-model="dataForm.emergencyContacts1Phone" placeholder="请输入紧急联系人1手机" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
            <el-descriptions-item class-name="noneSelfRight">
              <template #label><span>紧急联系人2</span></template>
              <el-form-item label="紧急联系人2" prop="emergencyContacts2Type">
                <el-radio-group v-model="dataForm.emergencyContacts2Type">
                  <el-radio :value="1">父亲</el-radio>
                  <el-radio :value="2">母亲</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>紧急联系人2名称</span></template>
              <el-form-item label="紧急联系人2名称" prop="emergencyContacts2Name">
                <el-input v-model="dataForm.emergencyContacts2Name" placeholder="请输入紧急联系人2名称" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label><span>紧急联系人2手机</span></template>
              <el-form-item label="紧急联系人2手机" prop="emergencyContacts2Phone">
                <el-input v-model="dataForm.emergencyContacts2Phone" placeholder="请输入紧急联系人2手机" />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="" class-name="noneSelfLeft" label-class-name="noneSelfLabel"> </el-descriptions-item>
          </el-descriptions>
        </template>
      </el-form>
    </el-card>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" v-if="acquisitionType == 2" @click="submitForm()">提交</el-button>
      <template v-else>
        <el-button plain :loading="btnLoading" type="primary" @click="submitForm(true)">保存并完善信息</el-button>
        <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
      </template>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { Plus } from "@element-plus/icons-vue";
import { useAppStore } from "@/store";
import { regionData } from "element-china-area-data";
import { id } from "element-plus/es/locale";
const store = useAppStore();
const emits = defineEmits(["refresh", "completeInfo"]);
const acquisitionType = ref(1); // 表单类型
const visible = ref(false);
const currentType = ref(<string | undefined>"");
const gamesList = ref(<any>[]);
const init = (type?: string, list?: any[], row?: any, gameId?: any) => {
  visible.value = true;
  currentType.value = type;
  dataForm.value.contractSign = 0;
  dataForm.value.gameId = gameId;
  getChannelTree();
  selectedOptions.value = [];
  if (row) {
    if (row.id) {
      dataForm.value.purchaseOrderId = row.id;
      dataForm.value.gameId = row.gameId;
      dataForm.value.serverId = row.serverId;
      dataForm.value.serverName = row.serverName;
    }
    if (row.serverId) gameChange();
    if (row.gameId) gameChange();
    if (row.gameAccount) dataForm.value.gameAccount = row.gameAccount;
  }

  gamesList.value = list?.filter((item) => item.id);
};

const dataForm = ref(<any>{
  acquisitionType: 1,
  isFromShop: false
});

const rules = reactive({
  channelId: [{ required: true, message: "请选择回收渠道", trigger: "change" }],
  gameId: [{ required: true, message: "请输入游戏名称", trigger: "change" }],
  district: [{ required: true, message: "请选择大区", trigger: "change" }],
  server: [{ required: true, message: "请选择服务器", trigger: "change" }],
  gameAccount: [{ required: true, message: "请输入游戏账号", trigger: "blur" }],
  gamePassword: [{ required: true, message: "请输入游戏密码", trigger: "blur" }],
  purchasePrice: [{ required: true, message: "请输入回收价格", trigger: "blur" }],
  recycleUser: [{ required: true, message: "请选择回收人", trigger: "change" }],
  bindingPhone: [
    { required: true, message: "请输入绑定手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  emergencyContacts1Phone: [{ pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }],
  emergencyContacts2Phone: [{ pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }],
  recyclePrice: [{ required: true, message: "请输入回收价", trigger: "blur" }],
  payWay: [{ required: true, message: "请选择支付方式", trigger: "change" }],
  saleUserName: [{ required: true, message: "请输入卖方姓名", trigger: "blur" }],
  customerPhone: [
    { required: true, message: "请输入卖方手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  emergencyPhone: [
    { required: false, message: "请输入应急手机号", trigger: "blur" },
    { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ]
});
// 处理城市选择变化
const handleCityChange = (value: any) => {
  console.log("选择的城市：", value); // value 是一个数组，例如 ['省', '市', '区']
};
// 回收渠道名称
const getChannelName = (data: any) => {
  dataForm.value.channelName = data.title;
};

// 获取出售渠道
const ChannelTreeList = ref(<any>[]);
const getChannelTree = () => {
  baseService.get("/channel/channel/getChannelTree", { type: "1" }).then((res) => {
    res.data.map((item: any) => {
      if (item.children.length == 0) {
        item.disabled = true;
      }
    });
    ChannelTreeList.value = res.data;
  });
};
const selectedOptions = ref([]);
const handleChange = (selectedData: any) => {
  if (selectedData && selectedData.length > 0) {
    dataForm.value.channelId = selectedData[selectedData.length - 1];
    baseService.get("/channel/channel/" + dataForm.value.channelId).then((res) => {
      if (res.data) {
        dataForm.value.channelName = res.data.title;
      }
    });
  } else {
    dataForm.value.channelId = "";
    dataForm.value.channelName = "";
  }
};

// 选择游戏
const gameChange = () => {
  dataForm.value.district = "";
  dataForm.value.server = "";
  getdistrictList();
};

// 大区列表
const districtList = ref(<any>[]);
const getdistrictList = () => {
  baseService.get("/game/sysgame/get/" + dataForm.value.gameId).then((res: any) => {
    districtList.value = res.data?.areaDtoList;

    if (dataForm.value.serverId) {
      if (!districtList.value[0].children || !districtList.value[0].children.length) {
        dataForm.value.district = dataForm.value.serverId + "-" + dataForm.value.serverName;
        return;
      }

      districtList.value.map((item: any) => {
        item.children.map((sub: any) => {
          if (sub.id == dataForm.value.serverId) {
            dataForm.value.district = item.id + "-" + item.title;
            dataForm.value.server = sub.id + "-" + sub.title;
          }
        });
      });
    }
  });
};

// 服务列表
const serverList = ref(<any>[]);
const getServerList = () => {
  serverList.value = districtList.value.find((item: any) => item.id == dataForm.value.district.split("-")[0]).children;
};

// 提交
const formRef = ref();
const btnLoading = ref(false);
const submitForm = (compete?: any) => {
  formRef.value.validate(async (valid: boolean) => {
    console.log(valid,'===== valid =====');
    
    if (!valid) return;
    if (compete && !dataForm.value.payImg) {
      ElMessage.warning("完善信息需先上传付款截图!");
      return;
    }
    btnLoading.value = true;
    let params = JSON.parse(JSON.stringify(dataForm.value));
    params.gameName = gamesList.value.find((item: any) => item.id == params.gameId)?.title;
    // /purchase/addOrder仅新增回收订单        /purchase/add需生成账号池 和 回收订单  回收订单 列表,新增,完善基本信息,订单详情,日志,换绑接口对接完成

    if (params.server) {
      params.serverId = params.server.split("-")[0];
      params.serverName = params.district.split("-")[1] + "-" + params.server.split("-")[1];
    } else if (params.district) {
      params.serverId = params.district.split("-")[0];
      params.serverName = params.district.split("-")[1];
    }
    delete params.server;
    delete params.district;

    // 简易回收
    params.orderType = acquisitionType.value == 1 ? 2 : 1;
    if (params.orderType == 2) {
      params.state = dataForm.value.payImg ? "NOT_INBOUND_BUT_REMIT" : "ALREADY_CHANGE_BINDING";
      params.city = params.city ? params.city.join(",") : "";
    } else {
      params.recycleUser = store.state.user.id;
    }

    baseService
      .post(params.purchaseOrderId ? "/purchase/addByWaitForPurchase" : "/purchase/add", params)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          emits("refresh");
          visible.value = false;
          let dataInfo_ = { ...params };
          dataInfo_.id = res.data.id;
          dataInfo_.amount = params.recyclePrice;
          compete ? emits("completeInfo", dataInfo_) : "";
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
.ny-button-group {
  background: #f2f2f2;
  border-radius: 6px;
  margin-right: 10px;
  width: fit-content;
  margin: auto;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
.numIpt {
  :deep(.el-input__inner) {
    text-align: left;
  }
}
:deep(.el-form-item) {
  &.is-error {
    .el-input__inner {
      &::placeholder {
        color: var(--el-color-danger);
      }
    }
    .el-select__placeholder {
      color: var(--el-color-danger);
    }
    .el-form-item__error {
      display: none !important;
      opacity: 0 !important;
    }
  }
  &.is-success {
    .el-form-item__error {
      transition: none !important;
      opacity: 0 !important;
    }
  }
}
</style>
