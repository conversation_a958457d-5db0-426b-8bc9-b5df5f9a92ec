<template>
  <!-- 售后订单 -->
  <div class="container sell-order-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab class="newTabSty" :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title"></ny-flod-tab>

      <div class="stat_card">
        <div class="stat_item" style="background: linear-gradient(174deg, rgba(230, 244, 254, 0.84) 0%, rgba(217, 224, 247, 0.17) 100%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">待处理售后</span>
            </div>
          </div>
          <div class="stat_middle">{{ totalRebackData.pendingProcessingQuantity || 0 }}</div>
          <div class="stat_below">
            <span class="sum_label">今日待处理：</span>
            <!-- 即将到期订单数量 -->
            <template v-if="totalRebackData.upcomingQuantity && totalRebackData.upcomingQuantity != 0">
              <span class="sum_value" style="color: #4165d7">{{ totalRebackData.upcomingQuantity }}</span>
              <el-button type="primary" link style="font-size: 12px; margin-left: 8px; color: #4165d7" @click="handleFilterTime()">
                马上处理 <el-icon><arrowRight /></el-icon>
              </el-button>
            </template>
            <span v-else style="color: #4165d7">-</span>
          </div>
        </div>

        <div class="stat_item" :style="windowWidth < 1901 ? 'width: 270px; flex: auto; max-width: 290px;' : ''" style="background: linear-gradient(174deg, rgba(255, 226, 226, 0.84) 0%, rgba(217, 224, 247, 0.17) 100%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">即将到期订单</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.dueDate" type="date" format="MM/DD" value-format="YYYY-MM-DD" style="width: 70px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle" style="color: #f53f3f">{{ totalRebackData.upcomingQuantity || 0 }}</div>
          <div class="stat_below">
            <span class="sum_label">最快到期时间：</span>
            <span v-if="totalRebackData.upcomingQuantity && totalRebackData.upcomingQuantity > 0 && totalRebackData.fastestExpirationTime">
              <span class="sum_value" style="color: #f53f3f">{{ windowWidth < 1901 ? dayjs(totalRebackData.fastestExpirationTime).format("YY/MM/DD hh:ss:mm") : formatDate(totalRebackData.fastestExpirationTime) }}</span>
              <el-button type="danger" link style="font-size: 12px; margin-left: 8px" @click="handleFilterTime(true)"
                >马上处理 <el-icon><arrowRight /></el-icon
              ></el-button>
            </span>
            <span v-else style="color: #f53f3f">-</span>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(174deg, rgba(230, 254, 234, 0.84) 0%, rgba(217, 224, 247, 0.17) 100%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">今日处理售后</span>
            </div>
            <div class="stat_top_time">
              <el-date-picker v-model="totalParams.handlingDate" type="date" format="MM/DD" value-format="YYYY-MM-DD" style="width: 70px" :clearable="false" @change="getTotalData" />
            </div>
          </div>
          <div class="stat_middle">{{ totalRebackData.todayHandleQuantity || 0 }}</div>
          <div class="stat_below">
            <span class="sum_label">本年总处理：</span>
            <span class="sum_value" style="color: #00b42a">{{ totalRebackData.yearHandleQuantity || 0 }}</span>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(174deg, rgba(254, 241, 230, 0.84) 0%, rgba(217, 224, 247, 0.17) 100%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">赔付金额(元)</span>
            </div>
            <div class="stat_top_time">
              <NyDropdownMenu
                :isBorder="false"
                @change="getTotalData"
                v-model="totalParams.compensationType"
                :list="[
                  { label: '本年', value: 4 },
                  { label: '本月', value: 3 },
                  { label: '本周', value: 2 }
                ]"
                labelKey="label"
                valueKey="value"
                style="height: 20px; line-height: 20px"
              ></NyDropdownMenu>
            </div>
          </div>
          <div class="stat_middle">{{ formatCurrency(totalRebackData.compensationMoney || 0) }}</div>
          <div class="stat_below">
            <span class="sum_label">总赔付：</span>
            <span class="sum_value" style="color: #ff9a2e">{{ formatCurrency(totalRebackData.totalCompensationMoney || 0) }}</span>
          </div>
        </div>
        <div class="stat_item" style="background: linear-gradient(174deg, rgba(230, 238, 254, 0.84) 0%, rgba(217, 224, 247, 0.17) 100%), #ffffff">
          <div class="stat_top flx-justify-between">
            <div class="stat_top_title flx-align-center">
              <span class="name">收到赔付(元)</span>
            </div>
            <div class="stat_top_time">
              <NyDropdownMenu
                :isBorder="false"
                @change="getTotalData"
                v-model="totalParams.receiveCompensationType"
                :list="[
                  { label: '本年', value: 4 },
                  { label: '本月', value: 3 },
                  { label: '本周', value: 2 }
                ]"
                labelKey="label"
                valueKey="value"
              ></NyDropdownMenu>
            </div>
          </div>
          <div class="stat_middle">{{ formatCurrency(totalRebackData.receiveCompensationMoney || 0) }}</div>
          <div class="stat_below">
            <span class="sum_label">总收赔付：</span>
            <span class="sum_value" style="color: #165dff">{{ formatCurrency(totalRebackData.receiveTotalCompensationMoney || 0) }}</span>
          </div>
        </div>
      </div>
      <ny-table noDataType="3" class="nyTableSearchFormFitable" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.state" @change="state.getDataList"></ny-button-group>
        </template>

        <template #header-right>
          <el-form
            class="nyTableSearchForm"
            :inline="true"
            :model="state.dataForm"
            @keyup.enter="
              state.getDataList();
              getTotalData();
            "
          >
            <el-form-item>
              <el-input v-model="state.dataForm.keywords" placeholder="商品编码/游戏账号/自编码/应急手机号" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
            </el-form-item>
            <el-form-item>
              <el-select v-model="state.dataForm.saleAfterType" filterable placeholder="请选择售后类型">
                <el-option v-for="(item, index) in typeoptions" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-button
              type="primary"
              @click="
                state.getDataList();
                getTotalData();
              "
              >{{ $t("query") }}</el-button
            >
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>

        <template #header-custom>
          <div class="mb-12">
            <!-- <el-button v-if="state.hasPermission('sale:saleAfterOrder:export')" type="primary" @click="state.exportHandle">导出</el-button> -->
            <!-- <el-button v-if="state.hasPermission('sale:saleAfterOrder:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="orderDeleteHandle">删除</el-button> -->
          </div>
        </template>

        <!-- 商品标题 -->
        <template #shopTitle="{ row }">
          <div class="shoping">
            <el-image style="height: 68px; width: 120px" :src="row.log" :preview-src-list="[row.log]" preview-teleported fit="cover" />
            <div class="info">
              <div class="title mle" v-html="row.shopTitle" @click="jumpGoodsDetails(row)"></div>
              <div class="sle">
                {{ `${row.gameName} / ${row.serverName}` }}
              </div>
            </div>
          </div>
        </template>
        <!-- 账号来源 -->
        <template #orderSource="{ row }"> {{ row.orderSource == "出售订单" ? "销售订单" : row.orderSource }}</template>
        <!-- 售后结果 -->
        <template #saleAfterResults="{ row }">
          <div v-if="row.saleAfterResults && row.saleAfterResults.length > 0">卖家：{{ row.saleAfterResults[0] }}</div>
          <div v-if="row.saleAfterResults && row.saleAfterResults.length == 2">买家：{{ row.saleAfterResults[1] }}</div>
        </template>
        <!-- 问题截图 -->
        <template #saleAfterPics="{ row }">
          <el-image :src="row.saleAfterPics" style="width: 54px" :preview-teleported="true" :preview-src-list="[row.saleAfterPics]"></el-image>
        </template>
        <!-- 支付时间 -->
        <template #payTime="{ row }">
          {{ row.payTime ? formatTimeStamp(row.payTime) : "-" }}
        </template>
        <!-- 过期时间 -->
        <template #expirationTime="{ row }">
          {{ row.expirationTime ? formatTimeStamp(row.expirationTime) : "-" }}
        </template>
        <!-- 状态 -->
        <template #state="{ row }">
          <el-tag v-if="row.state == '待处理'" type="danger">待处理</el-tag>
          <!-- 只要提交审核了就是处理中， 买家还未选择处理结果就是卖家处理中 -->
          <template v-if="row.state == '处理中'">
            <el-tag v-if="row.saleAfterTypeAudit != 1" type="primary">卖家处理中</el-tag>
            <el-tag class="noBorder" v-else type="primary">买家处理中 </el-tag>
            <div>
              <el-text style="font-size: 12px" type="danger" v-if="row.buyersTypeAudit == 2 || row.saleAfterTypeAudit == 2">(审批拒绝)</el-text>
            </div>
          </template>
          <el-tag v-if="row.state == '已处理'" type="success">已处理</el-tag>
          <el-tag v-if="row.state == '已取消'" type="success">已取消</el-tag>
          <el-tag v-if="row.state == '已过期'" type="info">已过期</el-tag>
          <div v-if="row.expireState == 1 && (row.state == '处理中' || row.state == '待处理')">
            <el-text type="danger" style="font-size: 12px">(即将过期)</el-text>
          </div>
        </template>
        <template #orderRemark="{ row }">
          <div style="white-space: pre; max-height: 200px; overflow: hidden; text-align: -webkit-auto; text-overflow: ellipsis" v-if="row.orderRemark">{{ row.orderRemark }}</div>
          <span v-else>-</span>
        </template>
        <template #operation="{ row }">
          <!-- 待处理 -->
          <el-button v-if="(row.state == '待处理' || row.state == '处理中') && state.hasPermission('sale:saleAfterOrder:process')" type="primary" link @click="orderProcessingHandle({ ...row })">处理</el-button>
          <el-button type="primary" link @click="editRemark(row)">订单备注</el-button>
          <el-button v-if="state.hasPermission('sale:saleAfterOrder:optionalLog')" type="info" link @click="processingCompleteHandle({ ...row })">日志</el-button>
        </template>

        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span class="tableSort">
              <NyDropdownMenu
                :isBorder="false"
                v-model="SummariesParams"
                :list="[
                  { label: '赔付金额', value: 1 },
                  { label: '收到赔付', value: 2 }
                ]"
                labelKey="label"
                valueKey="value"
                isTop
              ></NyDropdownMenu>
            </span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>
    <el-drawer v-model="dataForm.visible" title="售后订单备注" size="40%" class="ny-drawer">
      <el-input type="textarea" :rows="20" v-if="dataForm.visible" style="height: 500px" v-model="dataForm.orderRemark"></el-input>
      <template v-slot:footer>
        <el-button @click="dataForm.visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
      </template>
    </el-drawer>
    <!-- 售后订单处理 -->
    <order-processing
      ref="orderProcessingRef"
      :key="orderProcessingKey"
      @refresh="
        state.getDataList();
        getTotalData();
      "
    ></order-processing>

    <!-- 日志 -->
    <ProcessingComplete ref="processingCompleteRef" :key="processingCompleteKey"></ProcessingComplete>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>

    <!-- 删除订单 -->
    <order-delete
      ref="orderDeleteRef"
      :key="orderDeleteKey"
      @refresh="
        state.getDataList();
        getTotalData();
      "
    ></order-delete>
  </div>
</template>

<script lang="ts" setup>
import WangEditor from "@/components/wang-editor/index.vue";
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { formatDate, formatCurrency } from "@/utils/method";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import OrderProcessing from "./components/OrderProcessing.vue";
import ProcessingComplete from "./components/ProcessingComplete.vue";
import ShopInfo from "@/views/shop/shop-info.vue";
import OrderDelete from "../components/OrderDelete.vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import dayjs from "dayjs";
// 表格配置项
const columns = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 120
  },
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: 120
  },
  {
    prop: "shopTitle",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "gameAccount",
    label: "游戏账号",
    minWidth: 120
  },
  {
    prop: "state",
    label: "处理状态",
    width: 150
  },
  {
    prop: "orderSource",
    label: "商品来源",
    width: 120
  },
  {
    prop: "saleAfterType",
    label: "售后类型",
    width: 120
  },
  {
    prop: "saleAfterPics",
    label: "问题截图",
    width: 98
  },
  {
    prop: "expirationTime",
    label: "过期时间",
    width: 180
  },
  {
    prop: "createDate",
    label: "创建时间",
    width: 180
  },
  {
    prop: "purchaseAmount",
    label: "回收价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "purchaseUserName",
    label: "回收人",
    width: 120
  },
  {
    prop: "dealAmount",
    label: "成交价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "createName",
    label: "创建人",
    width: 120
  },
  {
    prop: "saleAfterResults",
    label: "售后结果",
    width: 160
  },
  {
    prop: "buyersAmount",
    label: "赔付金额(元)",
    width: 136
    // sortable: "custom"
  },
  {
    prop: "saleAfterAmount",
    label: "收到赔付(元)",
    width: 136
    // sortable: "custom"
  },
  {
    prop: "payTime",
    label: "支付时间",
    width: 190,
    sortable: "custom"
  },
  {
    prop: "saleAfterType",
    label: "退款方式",
    width: 136
  },
  {
    prop: "remark",
    label: "处理备注",
    width: 190
  },
  {
    prop: "orderRemark",
    label: "订单备注",
    width: 140,
    fixed: "right"
  },
  {
    prop: "ownCoding",
    label: "自编码",
    width: 120
  },
  {
    prop: "emergencyPhone",
    label: "应急手机号",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 200
  }
]);

const view = reactive({
  getDataListURL: "/saleAfter/page",
  deleteURL: "/saleAfter/delete",
  exportURL: "/saleAfter/export",
  deleteIsBatch: true,
  getDataListIsPage: true,
  dataForm: {
    gameId: "",
    state: "",
    keywords: "",
    saleAfterType: "",
    startTime: "",
    endTime: "",
    order: "",
    orderField: "",
    stateType: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const dataForm = reactive({
  orderRemark: "",
  id: "",
  visible: false
});
const editRemark = (row: any) => {
  dataForm.visible = true;
  dataForm.id = row.id;
  dataForm.orderRemark = row.orderRemark;
};
const dataFormSubmitHandle = () => {
  baseService.put("/saleAfter/updateRemark", { id: dataForm.id, orderRemark: dataForm.orderRemark }).then((res) => {
    if (res.code == 0) {
      ElMessage.success("编辑备注成功！");
      dataForm.visible = false;
      dataForm.id = "";
      dataForm.orderRemark = "";
      state.query();
    }
  });
};
// 重置操作
const getResetting = () => {
  // state.dataForm.gameId = "";
  // totalParams.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.saleAfterType = "";
  createDate.value = [];
  state.getDataList();
  getTotalData();
};

// 状态
const stateList = [
  { label: "全部", value: "" },
  { label: "待处理", value: "WAIT_FOR_PROCESS" },
  { label: "处理中", value: "PROCESSING" },
  { label: "已处理", value: "DONE" },
  { label: "已过期", value: "EXPIRE" }
  // { label: "已取消", value: "CANCEL" }
];
const typeoptions = ref([
  {
    label: "账号找回",
    value: "ACCOUNT_RETURN"
  },
  {
    label: "账号被封",
    value: "ACCOUNT_WAS_BANNED"
  },
  {
    label: "防沉迷",
    value: "ANTI_ADDICTION"
  },
  {
    label: "冻结",
    value: "FROZEN"
  },
  {
    label: "人脸",
    value: "FACE"
  },
  {
    label: "其他原因",
    value: "OTHER"
  }
]);
// const handleFilter = () => {
//   state.dataForm = {};
//   state.dataForm.state = "";
//   state.dataForm.stateType = "pendingProcessing";
//   state.getDataList();
//   state.dataForm.stateType = "";
// };
const handleFilterTime = (detail?: any) => {
  // state.dataForm.gameId = "";
  state.dataForm.state = "";
  state.dataForm.keywords = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.saleAfterType = "";
  state.dataForm.state = "";
  createDate.value = [];
  let params = {};
  params.order = "asc";
  params.orderField = "expirationTime";
  params.stateType = "pendingProcessing";
  params.startExpirationTime = totalParams.dueDate;
  params.gameId = state.dataForm.gameId;
  baseService
    .get(state.getDataListURL, {
      page: state.page,
      limit: state.limit,
      size: state.limit,
      ...params
    })
    .then((res) => {
      if (detail) {
        if (res.data && res.data.total > 0) {
          orderProcessingHandle({ ...res.data.list[0] });
        }
      } else {
        state.dataListLoading = false;
        state.dataList = [];
        state.total = 0;
        state.dataList = res.data.list;
        state.total = res.data.total;
      }
    });
};
// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ id: "", title: "全部" }, ...res.data];
  });
};
getGamesList();

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item.saleAfterAmount) if (item.saleAfterAmount) total = total + (item.saleAfterAmount || 0);
    });
  } else if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item.buyersAmount) if (item.buyersAmount) total = total + (item.buyersAmount || 0);
    });
  }
  return total.toFixed(2);
};

watch(
  () => view.dataForm.gameId,
  (newVal) => {
    totalParams.gameId = view.dataForm.gameId;
    state.getDataList();
    getTotalData();
  }
);

// 处理售后订单
const orderProcessingRef = ref();
const orderProcessingKey = ref(0);
const orderProcessingHandle = async (row: any, show?: boolean) => {
  orderProcessingKey.value++;
  await nextTick();
  orderProcessingRef.value.init(row, show);
};

// 处理完成
const processingCompleteRef = ref();
const processingCompleteKey = ref(0);
const processingCompleteHandle = async (row: any) => {
  processingCompleteKey.value++;
  await nextTick();
  processingCompleteRef.value.init(row, true, true, true);
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};

// 删除订单
const orderDeleteRef = ref();
const orderDeleteKey = ref(0);
const orderDeleteHandle = async (id?: number) => {
  orderDeleteKey.value++;
  await nextTick();
  let ids: any[] = [];

  if (!id && state.dataListSelections && state.dataListSelections.length) {
    state.dataList?.map((item) => {
      if (state.dataListSelections.includes(item.id) && item.state == "已取消") {
        ids.push(item.id);
      }
    });
  }

  if (state.dataListSelections && state.dataListSelections.length && !ids.length) {
    return ElMessage.error("请选择已取消状态的订单进行删除!");
  }

  orderDeleteRef.value.init(id ? [id] : ids, "afterSales");
};

// 数据统计
const totalParams = reactive({
  // 1:日,2:周,3:月,4:年
  dueDate: dayjs().format("YYYY-MM-DD"),
  handlingDate: dayjs().format("YYYY-MM-DD"),
  receiveCompensationType: 4,
  compensationType: 4,
  gameId: ""
});
const totalRebackData = reactive({
  pendingProcessingQuantity: 0, //待处理售后数量
  todayPendingProcessingQuantity: 0, //今日待处理售后数量
  upcomingQuantity: 0, //即将到期数量
  fastestExpirationTime: 0, //最快到期时间
  todayHandleQuantity: 0, //今日处理售后数量
  yearHandleQuantity: 0, //本年总处理数量
  compensationMoney: 0, //赔付金额
  totalCompensationMoney: 0, //赔付金额
  receiveCompensationMoney: 0, //收到赔付金额
  receiveTotalCompensationMoney: 0 //收到总赔付金额
});
const getTotalData = () => {
  baseService.get("/saleAfter/report", totalParams).then((res) => {
    Object.assign(totalRebackData, res.data);
  });
};
getTotalData();
const windowWidth = ref(0);
onMounted(() => {
  windowWidth.value = window.innerWidth;
  window.addEventListener("resize", () => {
    windowWidth.value = window.innerWidth;
  });
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
:deep(.shopTitleSty) {
  .el-link__inner {
    width: inherit;
    overflow: hidden;
    text-align: left;
    text-overflow: ellipsis;
    display: block;
    padding-right: 20px;
  }
  width: inherit;
}
.el-tag {
  border: 1px solid;
  &.noBorder {
    border: 1px dotted;
  }
}
.stat_card {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
  .stat_item {
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    height: 120px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: 1px solid #e5e6eb;
    .stat_top {
      padding: 12px;
      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }
    .stat_middle {
      padding: 4px 16px;
      font-weight: 500;
      font-size: 20px;
      color: #303133;
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .stat_below {
      display: flex;
      align-items: center;
      padding: 4px 16px 12px 16px;
      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
