<template>
  <el-dialog v-model="visible" width="480" :title="'新增汇款'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-suffix="：">
      <el-descriptions title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>支付宝账户主体<span style="color: red">*</span></div>
          </template>
          {{ dataForm.receiveAccount }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>支付宝账号<span style="color: red">*</span></div>
          </template>
          {{ dataForm.receiveNumber }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>汇款金额(元)<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="amount" label="汇款金额(元)" required>
            <el-input v-model="dataForm.amount" placeholder="请输入汇款金额" type="number" controls-position="right" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>选择账户<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="accountId" label="选择账户" required>
            <el-select filterable :filter-method="dataFilter" @change="changeAccount" v-model="dataForm.accountId" placeholder="选择收款账户" clearable>
              <el-option v-for="(item, index) in selectData" :key="index" :label="item.id < 0 ? item.name + '-' + item.typeName : item.name" :value="item.id"></el-option>
            </el-select> </el-form-item
        ></el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>汇款凭证</div>
          </template>
          <el-form-item label="汇款凭证" prop="paymentVoucher"> <ny-upload :limit="1" v-model:imageUrl="dataForm.paymentVoucher" accept="image/*"></ny-upload></el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "提交审核" }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive, toRefs } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const emit = defineEmits(["close", "refreshDataList"]);

const visible = ref(false);
const rules = {
  receiveAccount: [{ required: false, message: "请输入必填项", trigger: "blur" }],
  receiveNumber: [{ required: false, message: "请输入必填项", trigger: "change" }],
  amount: [{ required: true, message: "请输入必填项", trigger: "change" }],
  type: [{ required: true, message: "请输入必填项", trigger: "change" }],
  accountId: [{ required: true, message: "请输入必填项", trigger: "change" }],
  payAccount: [{ required: true, message: "请输入必填项", trigger: "change" }]
};
const dataFormRef = ref();
const dataForm = ref(<any>{
  amount: undefined,
  remark: undefined,
  accountId: undefined
});
// 详情id
const detailInfo = ref({
  count: 0,
  amountTotal: 0
});

const init = (info: any) => {
  visible.value = true;
  detailInfo.value = info;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    dataForm.value.amount = undefined;
  }
  dataForm.value.receiveNumber = "<EMAIL>";
  dataForm.value.receiveAccount = "枣庄努运企业管理有限公司";
  getInfo();
};
const selectData = ref(<any>[]);
const selectDataPro = ref([]);
// 获取详情
const getInfo = async () => {
  let res = await baseService.get("/wallet/tenantaccount/page", { limit: 9999 });
  selectData.value = res.data.list;
  selectDataPro.value = JSON.parse(JSON.stringify(res.data.list));
};
const changeAccount = () => {
  if (dataForm.value.accountId) {
    let obj: any = {};
    obj = selectData.value.find((ele: any) => ele.id == dataForm.value.accountId);
    dataForm.value.payAccount = obj.account;
    dataForm.value.type = obj.type;
  }
};
const dataFilter = (val: string) => {
  if (val) {
    selectData.value = selectDataPro.value.filter((item: any) => item.account.includes(val));
    if (selectData.value.length < 1) {
      selectData.value = [
        {
          id: -1,
          account: val,
          name: val,
          type: 1,
          typeName: "支付宝"
        },
        {
          id: -2,
          account: val,
          name: val,
          type: 2,
          typeName: "微信"
        },
        {
          id: -3,
          account: val,
          name: val,
          type: 3,
          typeName: "银行卡"
        }
      ];
    }
  } else {
    selectData.value = selectDataPro.value;
  }
};
// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = async () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let params = { ...dataForm.value };
    delete params.accountId;
    replyLoading.value = true;
    baseService.post("/wallet/bill/offline/save", params).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          replyLoading.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emit("close");
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
  display: flex;
}
:deep(.el-form-item--default) {
  margin-bottom: 12px;
  display: block;
}
.fontSty {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.el-statistic__content) {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #e6a23c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
