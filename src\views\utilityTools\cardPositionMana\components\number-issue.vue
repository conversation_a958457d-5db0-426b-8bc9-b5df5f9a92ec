<template>
  <el-dialog :footer="null" v-model="visible" title="手机号问题记录" width="800">
    <div style="display: flex; gap: 8px; justify-content: flex-end">
      <el-input :prefix-icon="Search" style="width: 240px" v-model="state.dataForm.info" placeholder="请输入手机号/问题备注" clearable></el-input>
      <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
      <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
    </div>
    <ny-table :showColSetting="false" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #status="{ row }">
        <el-tag type="warning">待处理</el-tag>
      </template>
      <template #createDate="{ row }">
        <span>{{ row.createDate ? formatTimeStamp(row.createDate) : "-" }}</span>
      </template>
    </ny-table>
  </el-dialog>
</template>
    
    <script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { formatTimeStamp } from "@/utils/method";
const view = reactive({
  getDataListURL: "/mobile/mobileProblemFeedback/page",
  createdIsNeed: false,
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  // {
  //   prop: "id",
  //   label: "ID",
  //   minWidth: 80
  // },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 136
  },
  {
    prop: "remark",
    label: "问题备注",
    minWidth: 136
  },
  // {
  //   prop: "status",
  //   label: "问题状态",
  //   minWidth: 136
  // },
  {
    prop: "createName",
    label: "创建者",
    // sortable: "custom",
    minWidth: 136
  },
  {
    prop: "createDate",
    label: "创建时间",
    // sortable: "custom",
    minWidth: 136
  }
]);

// 重置操作
const getResetting = () => {
  state.dataForm.info = "";
  state.page = 1;
  state.getDataList();
};

const visible = ref(false);
const init = (type?: any) => {
  visible.value = true;
  state.getDataList();
};

defineExpose({
  init
});
</script>
  <style lang="scss" scoped>
</style>
    