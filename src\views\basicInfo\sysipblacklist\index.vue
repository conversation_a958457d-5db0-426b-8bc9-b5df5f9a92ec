<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <el-tabs v-model="view.dataForm.type" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="黑名单" name="1"></el-tab-pane>
      <el-tab-pane label="白名单" name="2"></el-tab-pane>
    </el-tabs>
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:ipBlackList:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:ipBlackList:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="ipAddress" label="IP地址" header-align="center" align="center"></el-table-column>
      <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('sys:ipBlackList:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('sys:ipBlackList:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-pagination hide-on-single-page :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </el-card>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./ipblacklist-add-or-update.vue";
import app from "@/constants/app";
const VITE_APP_API = app.api;

const view = reactive({
  getDataListURL: "/sys/ipBlackList/page",
  getDataListIsPage: true,
  exportURL: "/sys/ipBlackList/export",
  deleteURL: "/sys/ipBlackList",
  deleteIsBatch: true,
  dataForm: {
    type: "1"
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const handleClick = () => {
  nextTick(() => {
    state.getDataList();
  });
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(view.dataForm.type, id);
  });
};
</script>
