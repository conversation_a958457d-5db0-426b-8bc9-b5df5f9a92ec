<template>
    <div class="mod-sys__params" style="margin-top: 12px;">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button v-if="state.hasPermission('sys:params:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:params:delete')" type="danger"
                        @click="state.deleteHandle()">{{$t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <el-input v-model="state.dataForm.paramCode" :placeholder="$t('params.paramCode')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button v-if="state.hasPermission('sys:params:update')" type="primary" text bg
                        @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="state.hasPermission('sys:params:delete')" type="danger" text bg
                        @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
                </template>
            </ny-table>
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./params-add-or-update.vue";

const view = reactive({
    getDataListURL: "/sys/params/page",
    getDataListIsPage: true,
    deleteURL: "/sys/params",
    deleteIsBatch: true,
    dataForm: {
        paramCode: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "paramCode",
        label: "编码",
        minWidth: 120
    },
    {
        prop: "paramValue",
        label: "值",
        minWidth: 120
    },
    {
        prop: "remark",
        label: "备注",
        minWidth: 120
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 150
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.paramCode = "";
    state.getDataList();
}

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
