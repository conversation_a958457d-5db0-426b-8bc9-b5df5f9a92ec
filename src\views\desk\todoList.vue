<template>
  <div class="notice_cart" style="margin: 12px 0">
    <div class="above">
      <div class="left">
        <span class="title">待办事项</span>
      </div>
    </div>
    <div class="notice_list">
      <div class="notice_list_item" @click="jumpRouter('/shop/bargain/index', true)">
        <img style="width: 48px; height: 48px; margin-right: 10px" src="https://oss.nyyyds.com/upload/20241210/41cf52cb1b8f42f595b3f95d218d46b4.png" alt="" />
        <div class="notice_title sle">
          <div style="font-family: Inter, Inter; font-weight: 400; font-size: 14px; color: #909399; line-height: 22px">议价待处理数</div>
          <div style="font-weight: bold; font-size: 20px; color: #1d2129">{{ state.allData.bargainNums || 0 }}</div>
        </div>
      </div>
      <div class="notice_list_item" @click="jumpRouter('/shop/shoPaudit', true)">
        <img style="width: 48px; height: 48px; margin-right: 10px" src="https://oss.nyyyds.com/upload/20241210/a9beac07de654413a08c65968befa1bb.png" alt="" />
        <div class="notice_title sle">
          <div style="font-family: Inter, Inter; font-weight: 400; font-size: 14px; color: #909399; line-height: 22px">商品待审核数</div>
          <div style="font-weight: bold; font-size: 20px; color: #1d2129">{{ state.allData.gameAuditNums || 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, ref, watch, watchEffect } from "vue";
import baseService from "@/service/baseService";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import commonData from "./index.ts";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { formatDate, formatCurrency, Local } from "@/utils/method";
const router = useRouter();
const state = reactive({
  allData: {}
});
const jumpRouter = (path: any, inpage?: Boolean) => {
  if (inpage) {
    router.push(path);
  } else {
    window.open(path);
  }
};
onMounted(async () => {
  state.allData = await commonData.getTodoData();
});
</script>
  
<style lang="less" scoped>
.notice_cart {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;

  .notice_list {
    display: flex;
    padding: 0 12px;
    flex: 1;
    align-items: center;
    .notice_list_item {
      flex: 1;
      cursor: pointer;
      display: flex;
    }
  }
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
</style>