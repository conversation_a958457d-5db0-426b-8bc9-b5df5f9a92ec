<template>
  <div>
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
    <ny-table :showColSetting="false" :state="state" :columns="columns" routePath="/game/mapping/attribute" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <el-button type="primary" :loading="syncLoading" @click="handlesync()">自动同步</el-button>
        <el-button type="warning" @click="importShow = true">导入</el-button>
        <el-button type="primary" @click="exportHandle">导出</el-button>
        <el-button v-if="state.hasPermission('game:sysgame:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
        <el-text type="danger" v-if="!syncLoading"> 首次同步需要2-5分钟,请稍后查看 </el-text>
        <el-text type="danger" v-if="syncLoading"> 同步中...({{ synchronizedCount }} / {{ syncTotal }}) </el-text>
      </template>

      <template #header-right>
        <el-select v-model="state.dataForm.outPname" placeholder="请选择官方属性分类" @change="state.getDataList()" clearable style="width: 186px"> <el-option v-for="item in selectList" :key="item.outPid" :label="item.outPname" :value="item.outPid" /> </el-select>
        <el-input style="width: 280px !important" v-model="view.dataForm.attributeName" placeholder="请输入官方属性/平台属性" clearable :prefix-icon="Search"></el-input>
        <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
        <el-button @click="resetHandle">{{ $t("resetting") }}</el-button>
      </template>

      <!-- 当前平台 -->
      <template #imageUrl="{ row }">
        <el-image fit="cover" style="width: 56px; height: 56px" :src="row.imageUrl" :preview-src-list="[row.imageUrl]" :preview-teleported="true"></el-image>
      </template>

      <!-- 合作商游戏属性名称 -->
      <template #partnerAttributeName="{ row, $index }"> {{ row.partnerAttributeName }}<el-text type="danger" v-if="row.required == 0">（必填）</el-text> </template>

      <template #outPname="{ row }"> {{ toCn[row.outPname] || row.outPname }}</template>

      <!-- 平台属性 -->
      <template #platformName="{ row, $index }">
        <el-select :remote-method="(query: any)=>{remoteMethod(query, row)}" @change="partnerAttributeNameFn(row)" v-model="row.platformName" placeholder="请选择关联属性" clearable remote reserve-keyword filterable>
          <el-option v-for="(item, index) in externalAttribute" :key="index" :label="item.platformName" :value="item.platformName" />
        </el-select>
      </template>
    </ny-table>
    <!-- </el-card> -->

    <!-- 导入弹窗 -->
    <el-dialog v-model="importShow" class="ba-upload-preview" title="" @close="closeMoneyPreview" width="35%">
      <template #header>导入文件</template>
      <div>
        <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs">
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              importShow = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

  <script lang="ts" setup>
import { reactive, ref, toRefs, defineExpose } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { fileExport } from "@/utils/utils";
import { IObject } from "@/types/interface";
import { Search } from "@element-plus/icons-vue";
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 100
  },
  {
    prop: "outPname",
    label: "官方属性分类",
    minWidth: 120
  },
  {
    prop: "outName",
    label: "官方属性",
    minWidth: 120
  },
  {
    prop: "imageUrl",
    label: "皮肤图片",
    minWidth: 120
  },
  {
    prop: "platformPname",
    label: "平台属性分类",
    minWidth: 120
  },
  {
    prop: "similar",
    label: "预览",
    minWidth: 120
  },
  {
    prop: "platformName",
    label: "平台属性",
    minWidth: 120
  }
]);
const toCn = {
  person: "英雄",
  prop: "道具",
  weapon: "武器",
  qiangxie: "枪械",
  shizhuang: "时装",
  zhanbei: "战备",
  zaiju: "载具",
  zhuangbei: "装备",
  tiny: "小小英雄",
  map: "竞技场",
  attach: "攻击特效",
  car: "车辆",
  pet: "宠物",
  box: "宝箱",
  fashion: "时装",
  skin: "皮肤",
  seat: "座椅",
  hero: "英雄",
  icon: "召唤师图标",
  xuancai: "炫彩",
  zhencai: "臻彩"
};

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/mapping/sysgameinfomapping/page",
  getDataListIsPage: true,
  deleteURL: "/mapping/sysgameinfomapping",
  deleteIsBatch: true,
  dataForm: {
    type: 1,
    partners: "",
    partnerId: "",
    gameId: "",
    attribute: "",
    attributeName: "",
    outPname: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const selectList = ref([]);
const getList = (params: IObject) => {
  state.dataForm = {
    ...state.dataForm,
    ...params
  };
  state.getDataList();
  if(state.dataForm.gameId){
    baseService.get("/mapping/sysgameinfomapping/getOutPname", { gameId: state.dataForm.gameId }).then((res) => {
      selectList.value = res.data;
    });
  }
  
};

const externalAttribute = ref(<any>[]); // 平台属性
// 搜索关联平台属性
const remoteMethod = (query: string, row: any) => {
  externalAttribute.value = [];
  if (query) {
    baseService
      .post("/mapping/sysgameinfomapping/getPlatformInfo", {
        mappingId: row.id,
        searchParam: query == null ? "" : query
      })
      .then((res) => {
        externalAttribute.value = res.data;
      });
  }
};

// 选择关联平台属性
const partnerAttributeNameFn = (row: any) => {
  let data = row;
  let itemdata: any = externalAttribute.value.find((item: any) => item.platformName == row.platformName);
  data.platformId = itemdata ? itemdata.platformId : "";
  baseService
    .put("/mapping/sysgameinfomapping", {
      id: row.id,
      platformId: data.platformId
    })
    .then((res) => {
      ElMessage.success("提交成功");
      state.query();
    });
};

const importShow = ref(false);
const fileList = ref([] as IObject);

// 关闭对话框
const closeMoneyPreview = () => {
  importShow.value = false;
};
// -----------------------数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};
// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: any) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 文件上传   确认导入按钮
const uploadExcel = async (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  console.log(fileList.value, file);
  const url = `/mapping/sysgameinfomapping/import?type=${1}&gameId=${state.dataForm.gameId}`;
  await baseService
    .post(url, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res: any) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      fileList.value = [];
      importShow.value = false;
      state.getDataList();
    });
};

// --------------------------导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  params.page = -1;
  params.type = 1;
  baseService.get("/mapping/sysgameinfomapping/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `官方属性映射`);
  });
};

// -----------------------------同步
const syncLoading = ref(false);

// 自动同步总数量
const syncTotal = ref(0);
// 已同步数量
const synchronizedCount = ref(0);
const handlesync = () => {
  syncLoading.value = true;
  baseService
    .post("/mapping/sysgameinfomapping/sync", {
      type: 1,
      gameId: state.dataForm.gameId
    })
    .then((res) => {
      state.getDataList();
    })
    .finally(() => {
      syncLoading.value = false;
    });
};
const resetHandle = () => {
  view.dataForm.partners = "";
  view.dataForm.outPname = "";
  view.dataForm.attribute = "";
  view.dataForm.attributeName = "";
  state.getDataList();
};
const changeDataForm = (partners: any, gameId?: any) => {
  state.dataForm.partners = partners;
  state.dataForm.gameId = gameId;
  getList({});
};

defineExpose({
  getList,
  changeDataForm
});
</script>
<style lang="less" scoped>
:deep(.table-header) {
  flex-wrap: wrap;
  margin-bottom: 0;

  .header-button-lf,
  .header-button-ri {
    margin-bottom: 12px;
  }
}
</style>