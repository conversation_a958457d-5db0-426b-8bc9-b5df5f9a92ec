<template>
  <div class="mod-fieldtype">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-form-slot>
        <template v-slot:content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.columnType" placeholder="字段类型" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="state.dataForm.attrType" placeholder="属性类型" clearable></el-input>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:button>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="columnType" label="字段类型" header-align="center" align="center"></el-table-column>
        <el-table-column prop="attrType" label="属性类型" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packageName" label="属性包名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="180">
          <template v-slot="scope">
            <el-button type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
            <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./fieldtype-add-or-update.vue";

const view = reactive({
  getDataListURL: "/devtools/fieldtype/page",
  getDataListIsPage: true,
  deleteURL: "/devtools/fieldtype",
  deleteIsBatch: true,
  dataForm: {
    columnType: "",
    attrType: "",
    packageName: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  view.dataForm.columnType = "";
  view.dataForm.attrType = "";
  view.dataForm.packageName = "";
  state.getDataList();
}

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
