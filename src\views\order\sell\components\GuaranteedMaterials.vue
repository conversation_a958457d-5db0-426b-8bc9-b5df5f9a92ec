<template>
    <el-drawer
        v-model="visible"
        title="包赔材料"
        :close-on-click-moformuladal="false"
        :close-on-press-escape="false"
        
        size="40%"
        class="ny-drawer"
    >   

        <el-form label-position="top" :model="dataForm" ref="formRef" class="guaranteed-materials" v-loading="dataLoading">
            <ny-no-data type="3" v-if="Object.keys(resData).length == 0" />
            <el-card v-for="(group, key) in resData" :key="key" class="mb-12">
                <div class="p-title mt-0">{{ key }}</div> 
                <el-row :gutter="12">
                    <template v-for="(item, index) in group" :key="index">
                        <!-- 文本 -->
                        <el-col :span="item.title == '详细地址' ? 24 : 12" v-if="item.infoType == 0">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <el-input v-model="item.infoOptionsValue" :placeholder="`请输入${item.title}`"></el-input>
                            </el-form-item>
                        </el-col>

                        <!-- 图片 -->
                        <el-col :span="12" v-if="item.infoType == 1">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <ny-upload v-model:imageUrl="item.infoOptionsValue" :limit="1" :fileSize="2" accept="image/*" ></ny-upload>
                            </el-form-item>
                        </el-col>

                        <!-- 数字 -->
                        <el-col :span="12" v-if="item.infoType == 2">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <el-input type="number" v-model="item.infoOptionsValue" :placeholder="`请输入${item.title}`"></el-input>
                            </el-form-item>
                        </el-col>

                        <!-- 单选 -->
                        <el-col :span="24" v-if="item.infoType == 3">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <el-radio-group v-model="item.infoOptionsValue" :disabled="item.isDisabled">
                                    <el-radio v-for="(option, index) in item.infoOptions.split(',')" :value="index" :key="index">{{ option }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <!-- 多选 -->
                        <el-col :span="24" v-if="item.infoType == 4">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <el-checkbox-group v-model="item.infoOptionsValue" :disabled="item.isDisabled">
                                    <el-checkbox v-for="(option, index) in item.infoOptions.split(',')" :label="option" :value="option" :key="index"></el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        </el-col>

                        <!-- 城市 -->
                        <el-col :span="24" v-if="item.infoType == 5">
                            <el-form-item :label="item.title" :class="{ 'is-required': item.required }">
                                <el-row :gutter="12" class="w-100">
                                    <el-col :span="6">
                                        <el-select v-model="item.infoOptionsValue[0]" filterable clearable placeholder="请选择省" @change="getCityList(item.infoOptionsValue[0]),item.infoOptionsValue[1]='',item.infoOptionsValue[2]=''">
                                            <el-option v-for="(item, index) in provinceList" :label='item.name' :value="item.name" :key="index"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-select v-model="item.infoOptionsValue[1]" filterable clearable placeholder="请选择市" @change="getCountyList(item.infoOptionsValue[1]),item.infoOptionsValue[2]=''">
                                            <el-option v-for="(item, index) in cityList" :label='item.name' :value="item.name" :key="index"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-select v-model="item.infoOptionsValue[2]" filterable clearable placeholder="请选择区">
                                            <el-option v-for="(item, index) in countyList" :label='item.name' :value="item.name" :key="index"></el-option>
                                        </el-select>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                    
                    </template>
                </el-row>
            </el-card>


        </el-form>

        <template #footer v-if="hasPermission('purchase:purchaseorder:updateGuaranteeInfoCollect')">
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button v-if="Object.keys(resData).length" :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
        </template>

    </el-drawer>
</template>  

<script lang="ts" setup>
    import { ref, reactive, toRefs, defineExpose, nextTick, defineEmits } from "vue";
    import { ElMessage } from "element-plus";
    import { useAppStore } from "@/store";
    import { checkPermission } from "@/utils/utils";
    import baseService from "@/service/baseService";

    const store = useAppStore();
    const emits = defineEmits(["refresh"]);
    const visible = ref(false);
    const init = (id?: number) => {
        visible.value = true;
        dataForm.value.orderId = id;
        getProvince();
        getInfo();
    }

    // 获取省
    const provinceList = ref(<any>[]);
    const getProvince = async () => {
        const res = await baseService.get("/sys/region/list");
        provinceList.value = res.data;
        dataForm.value.city = "";
        dataForm.value.county = "";
    }

    // 根据省id 获取市
    const cityList = ref(<any>[]);
    const getCityList = async (name: string, cityName?: string) => {
        await nextTick();
        let pid = provinceList.value.find((item: any) => item.name === name)?.id;
        const res = await baseService.get("/sys/region/list", { pid: pid });
        cityList.value = res.data;

        if(cityName){
            getCountyList(cityName);
        }
    }

    // 根据市id 获取区
    const countyList = ref(<any>[]);
    const getCountyList= async (name: string) => {
        await nextTick();
        let pid = cityList.value.find((item: any) => item.name === name)?.id;
        const res = await baseService.get("/sys/region/list", { pid: pid });
        countyList.value = res.data;
    }


    const dataForm = ref(<any>{});

    const resData = ref(<any>{})


    // 获取包赔信息
    const dataLoading = ref(false);
    const getInfo = async () => {
        dataLoading.value = true;
        const res = await baseService.get("/sale/getGuaranteeInfoCollect", { orderId: dataForm.value.orderId });
        dataLoading.value = false;
        if(!res || !res.data) return
        for (const key in res.data) {
            let list = res.data[key].sort((a: any, b: any) => {
                            return a.sortNum - b.sortNum;
                        });
            
            list.map((item: any) => {
                // 单选
                if(item.infoType == 3 && item.infoOptions != ""){
                    item.infoOptionsValue = item.infoOptions.split(",").findIndex((val:any) => val == item.infoOptionsValue);
                }
                // 多选
                if(item.infoType == 4){
                    item.infoOptionsValue = item.infoOptionsValue.split(",") || [];
                }
                // 城市
                if(item.infoType == 5){
                    item.infoOptionsValue = item.infoOptionsValue.split("/") || [];
                    if(item.infoOptionsValue[0]){
                        getCityList(item.infoOptionsValue[0], item.infoOptionsValue[1]);
                    }
                }
            })
        }
        resData.value = res.data;
    }


    // 提交
    const formRef = ref();
    const btnLoading = ref(false);
    const submitForm = () => {
        let params:any = [];
        let obj = JSON.parse(JSON.stringify(resData.value));
        let reg = /^1[3456789]\d{9}$/;
        let idCard = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        let result = true;
        
        nyLoop:
        for (const key in obj) {
            let list = obj[key];
            for(let i = 0; i < list.length; i++){
                let item = list[i];
                let infoOptionsValue = item.infoOptionsValue;

                // 手机号文本
                if(item.infoType == 0 && item.title == '手机号' && !reg.test(item.infoOptionsValue)){
                    ElMessage.error(item.title + " 格式不正确!");
                    result = false;
                    // 跳出循环
                    break nyLoop
                }

                // 身份证文本
                if(item.infoType == 0 && item.title == '身份证号' && !idCard.test(item.infoOptionsValue)){
                    ElMessage.error(item.title + " 格式不正确!");
                    result = false;
                    // 跳出循环
                    break nyLoop
                }
                
                if(item.infoType == 3 && item.infoOptions){
                    infoOptionsValue = item.infoOptions.split(",")[item.infoOptionsValue];
                }
                if(item.infoType == 4){
                    infoOptionsValue = item.infoOptionsValue.join(",");
                }
                if(item.infoType == 5){
                    infoOptionsValue = item.infoOptionsValue.join("/");
                }
                
                if(item.required == 1 && !infoOptionsValue){
                    result = false;
                    ElMessage.error(item.title + "不能为空!");
                    // 跳出循环
                    break nyLoop
                }
                params.push({
                    id: item.id,
                    orderId: item.orderId,
                    infoOptionsValue
                })
            }
        }

        if(!result) return;
        
        formRef.value.validate((valid: boolean) => {
            if(!valid) return;
        })
        btnLoading.value = true;
        baseService.put("/sale/updateGuaranteeInfoCollect", params).then(res => {
            if(res.code == 0){
                ElMessage.success("提交成功");
                visible.value = false;
                emits("refresh");
            }
        }).finally(() => {
            btnLoading.value = false;
        })
    }

    const hasPermission = (key: string) => {
      return checkPermission(store.state.permissions as string[], key);
    }
    
    
    defineExpose({
        init
    })

</script>

<style lang="scss" scoped>
    .guaranteed-materials{
        min-height: 200px;
        .p-title{
            margin-top: 0;
        }
    }
</style>  
