<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title" @change="gamesChange"></ny-flod-tab>
      <ny-table noDataType="4" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group :list="groupList" v-model="view.dataForm.status" @change="handleStatusClick"></ny-button-group>
        </template>
        <template #header-right>
          <div>
            <el-input v-model="state.dataForm.search" placeholder="请输入商品编码/用户ID" :prefix-icon="Search" style="width: 280px" clearable></el-input>
            <el-date-picker v-model="timeInterval" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px; margin-left: 12px" />
          </div>
          <el-button type="primary" @click="queryFn">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #log="{ row }">
          <el-image style="height: 40px" :src="row.log" :preview-src-list="[row.log]" preview-teleported v-if="row.log" />
          <span v-else>-</span>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #status="{ row }">
          <el-tag type="warning" v-if="row.status == '0'">待审核</el-tag>
          <el-tag type="success" v-if="row.status == '1'">审核通过</el-tag>
          <el-tag type="danger" v-if="row.status == '2'">审核拒绝</el-tag>
          <el-tag type="success" v-if="row.status == '3'">已完善</el-tag>
        </template>
        <template #operation="{ row }">
          <el-button type="warning" text bg v-if="row.status == '0'" @click="toExamine(row)">审核</el-button>
          <!-- <el-button type="primary" text bg v-else-if="row.status == '1'" @click="toUpdate(row)">完善信息</el-button> -->
          <el-button type="primary" text bg v-else @click="toInfo(row)">详情</el-button>
          <el-button v-if="row.status == 1" type="primary" text bg @click="toUpdate(row)">完善信息</el-button>
        </template>
      </ny-table>
    </el-card>
    <!-- 详情 -->
    <info ref="infoRef"></info>
    <!-- 完善信息 -->
    <update ref="updateRef" @refreshDataList="state.getDataList"></update>
    <!-- 商品审核 -->
    <Examine ref="examineRef" @refreshDataList="state.getDataList"></Examine>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import Info from "./shoPaudit-info.vue";
import Update from "./shoPaudit-update.vue";
import Examine from "./shoPaudit-examine.vue";

const gamesList = ref(<any>[]); // 游戏列表
const timeInterval = ref(); // 时间区间
const infoRef = ref(); // 查看商品详情
const updateRef = ref(); // 完善信息
const rechargedrawer = ref(false); // 审核商品

// 是否显示游戏密码
const isShowGamePassword = ref(false);

const view = reactive({
  getDataListURL: "/shop/shopaudit/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    limit: 20,
    gameId: "", // 游戏ID
    orderField: "create_date", // 排序字段
    order: "desc", // 排序方式
    status: "", // 状态
    search: "", // 搜索
    start: "", // 开始时间
    end: "" // 结束时间
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格设置
const columns = reactive([
  {
    prop: "code",
    label: "商品编码",
    minWidth: "100"
  },
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: "200"
  },
  {
    prop: "title",
    label: "商品标题",
    minWidth: "400",
    align: "left"
  },
  {
    prop: "log",
    label: "游戏主图",
    minWidth: "100"
  },
  {
    prop: "price",
    label: "商品价格(元)",
    minWidth: "160"
  },
  {
    prop: "creator",
    label: "用户ID",
    minWidth: "200"
  },
  {
    prop: "nickname",
    label: "用户名",
    minWidth: "120"
  },
  {
    prop: "createDate",
    label: "用户发起时间",
    minWidth: "200",
    sortable: "custom"
  },
  {
    prop: "status",
    label: "状态",
    fixed: "right",
    minWidth: "100"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "100"
  }
]);

// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = [{ title: "全部游戏", id: "" }, ...res.data];
  });
};

// 状态
const groupList = ref([
  { dictLabel: "全部", dictValue: "" },
  { dictLabel: "待审核", dictValue: "0" },
  { dictLabel: "审核通过", dictValue: "1" },
  { dictLabel: "审核拒绝", dictValue: "2" },
  { dictLabel: "已完善", dictValue: "3" }
]);

// 游戏切换
const gamesChange = (val: any) => {
  view.dataForm.gameId = val;
  refreshFn();
};
// 切换状态
const handleStatusClick = (tab: any) => {
  view.dataForm.status = tab;
  refreshFn();
};

// 表格列排序
const sortableChange = ({ order, prop }: any) => {
  if (order == "ascending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "asc";
  }
  if (order == "descending") {
    view.dataForm.orderField = camelToUnderscore(prop);
    view.dataForm.order = "desc";
  }
  if (order == null) {
    view.dataForm.orderField = "";
    view.dataForm.order = "";
  }
  state.getDataList();
};

// 查询
const queryFn = () => {
  view.dataForm.start = timeInterval.value ? timeInterval.value[0] : "";
  view.dataForm.end = timeInterval.value ? timeInterval.value[1] : "";
  refreshFn();
};
// 重置
const resetFn = () => {
  view.dataForm.search = "";
  view.dataForm.start = "";
  view.dataForm.end = "";
  timeInterval.value = "";
  refreshFn();
};
// 刷新页面
const refreshFn = () => {
  state.getDataList();
};

// 商品审核
const examineRef = ref();
const examineKey = ref(0);
const toExamine = (row: any) => {
  examineKey.value++;
  nextTick(() => {
    examineRef.value.init(row.id);
  });
};

// 完善信息
const toUpdate = (row: any) => {
  nextTick(() => {
    updateRef.value.init(row.gameId, row.id);
  });
};

// 查看详情
const toInfo = (row: any) => {
  nextTick(() => {
    infoRef.value.init(row.id);
  });
};

onMounted(() => {
  getGamesList();
});
</script>

<style lang="less" scoped></style>
