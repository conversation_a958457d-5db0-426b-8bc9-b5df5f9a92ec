body {
  --color-primary: #4165d7;
  --color-primary-light: rgba(64, 158, 255, 0.08);
  --el-text-color-regular: #303133;
}
@font-face {
  font-family: 'MyCustomFont';
  src: url('../fonts/MyCustomFont.woff2') format('woff2');
  font-display: swap;
}
@font-face {
  font-family: 'ZenDots-Regular';
  src: url('../fonts/ZenDots-Regular.ttf') format('truetype');
  font-display: swap;
}
*,
:after,
:before {
  box-sizing: border-box;
}
html,
body {
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  color: #303133;
  background: #f6f9ff;
  background-image: url("../images/body_img.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
html .text-2,
body .text-2 {
  color: #8c8c8c;
}
html .text-3,
body .text-3 {
  color: #606266;
}
html .text-center,
body .text-center {
  text-align: center;
}
html a,
body a {
  color: var(--color-primary);
  text-decoration: none;
}
html a:focus,
body a:focus,
html a:hover,
body a:hover {
  color: var(--color-primary);
}
.iconfont {
  cursor: pointer;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: text-bottom;
  display: inline-block;
  fill: currentColor;
  width: 17px;
  height: 17px;
}
.icon-svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
}
.el-badge__content {
  height: 16px;
  line-height: 16px;
  padding: 0 5px;
  border: none;
  background: #ff4d4f !important;
}
.ele-badge-static {
  line-height: 0;
}
.ele-badge-static .el-badge__content {
  position: static;
  transform: none;
}
.ele-alert-border.is-light.el-alert--warning {
  border: 1px solid #faad144d !important;
}
.el-alert--warning.is-light {
  background-color: #fff7e8 !important;
  color: #faad14 !important;
}
.ele-alert-border.is-light .el-alert__title {
  color: #262626 !important;
  font-size: 14px !important;
}
.el-alert__content {
  padding: 0;
}
.el-menu-item a,
.el-menu-item span,
.el-sub-menu > .el-sub-menu__title a,
.el-sub-menu > .el-sub-menu__title span {
  color: #fff;
  text-decoration: none;
  display: inline-flex;
  width: 100%;
}
.rr-sidebar-menu.el-menu--horizontal > .el-menu-item {
  padding: 0 12px;
  height: 62px;
  line-height: 62px;
}
.rr-sidebar-menu-pop-dark,
.rr-sidebar-menu-pop-light {
  box-shadow: none !important;
  border-width: 0 !important;
}
.el-sub-menu__icon-arrow {
  font-weight: bold;
}
.el-popper.is-dark a {
  color: #fff;
  text-decoration: none;
}
.el-popover.el-popper {
  max-height: 300px;
  overflow: auto;
}
.el-table thead {
  color: #303133 !important;
}
.el-table thead th {
  background-color: #f5f7fa !important;
}
.el-table__fixed-right::before {
  background: transparent !important;
}
.el-form--inline .el-form-item {
  margin-right: 16px !important;
}
.el-pagination {
  margin-top: 15px !important;
  justify-content: right;
}
.tox-tinymce-aux {
  z-index: 3000 !important;
}
.popover-pop {
  padding: 10px 0 5px 5px !important;
}
.popover-pop-body {
  max-height: 255px;
  overflow: auto;
}
.rr-dialog {
  min-width: 600px;
}
.rr {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.rr-loading {
  z-index: 9999;
}
.rr-fullscreen {
  width: 100%;
  height: 100%;
}
.rr-fullscreen.new-pop-window > div {
  padding: 15px;
  margin: 15px;
  background: #fff;
  border-radius: 4px;
}
.rr-error {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #fff;
  z-index: 1200;
}
.rr-drawer .el-drawer__header {
  color: #595959;
  font-size: 15px;
  margin-bottom: 0;
  padding: 13px 16px;
  border-bottom: 1px solid #f4f4f4;
}
.rr-drawer .el-drawer__body {
  padding: 15px;
  overflow: auto;
}
.rr-header {
  background: #fff;
  padding: 0 !important;
  z-index: 200;
}
.rr-header-ctx {
  display: flex;
  height: 70px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}
.rr-header-ctx-logo {
  display: flex;
  color: #ffffff;
  background-color: #191a23;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 1.5px;
  height: 80px;
  overflow: hidden;
  white-space: nowrap;
  justify-content: center;
  font-family: Avenir, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  align-items: center;
  position: relative;
  transition: width 0.3s;
  padding: 0 15px;
}
.rr-header-ctx-logo-img {
  width: 56px;
  height: 56px;
  display: inline-block;
  flex-shrink: 0;
}
.rr-header-ctx-logo-img-wrap {
  display: flex;
  align-items: center;
  margin-left: 16px;
}
.rr-header-ctx-logo-img-wrap.enabled-logo-false {
  display: none;
}
.rr-header-ctx-logo-name {
  display: flex;
  align-items: center;
  margin-left: 10px;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.rr-header-ctx-logo-line {
  display: inline-block;
  width: 10px;
  height: 1px;
}
.rr-header-ctx-logo-text {
  display: inline-block;
  line-height: 1;
  overflow: hidden;
  text-transform: uppercase;
  font-weight: 700;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.rr-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.rr-sidebar {
  overflow-x: hidden !important;
  transition: width 0.3s;
  z-index: 120;
  scrollbar-width: none;
}
.rr-sidebar-menu {
  transition: width 0.3s;
  overflow: hidden;
}
.rr-sidebar-menu.el-menu--horizontal {
  border-bottom: none !important;
}
.rr-sidebar-menu .el-menu-item {
  transition: none !important;
}
.rr-sidebar::-webkit-scrollbar {
  display: none;
}
.rr-sidebar .el-menu {
  border-right: 0 !important;
}
.rr-sidebar .el-menu-item {
  height: 45px;
  line-height: 45px;
  margin: 2px 0;
}
.rr-sidebar .el-menu-item,
.rr-sidebar .el-menu .el-sub-menu__title {
  background: transparent !important;
}
.rr-sidebar .el-menu-item:focus,
.rr-sidebar .el-menu .el-sub-menu__title:focus {
  background: transparent !important;
}
.rr-sidebar .el-menu-item,
.rr-sidebar .el-menu .el-sub-menu__title,
.rr-sidebar .el-menu-item-group__title {
  font-size: 14px;
}
.rr-sidebar .el-menu .el-sub-menu {
  margin-top: 10px;
  width: 100%;
}
.rr-sidebar .el-menu .el-sub-menu .el-sub-menu__title i {
  color: inherit !important;
}
.rr-sidebar .el-menu .el-menu-item,
.rr-sidebar .el-menu .el-sub-menu .el-sub-menu__title {
  margin: 0;
  height: 36px;
  line-height: 36px;
  padding: 0px 16px !important;
}
.rr-sidebar .el-menu .el-sub-menu .el-menu-item {
  padding-left: 0px !important;
  height: 28px;
  line-height: 28px;
  padding: 0px;
  width: 50%;
  font-size: 12px;
}
.rr-sidebar .el-menu .el-menu-item [class^="el-icon"],
.rr-sidebar .el-menu .el-sub-menu > .el-sub-menu__title [class^="el-icon"] {
  font-size: 17px;
  margin-right: 0;
  width: auto;
}
.rr-sidebar .el-menu .el-menu-item a > a,
.rr-sidebar .el-menu .el-menu-item span > a,
.rr-sidebar .el-menu .el-sub-menu > .el-sub-menu__title a > a,
.rr-sidebar .el-menu .el-sub-menu > .el-sub-menu__title span > a {
  margin-left: 0;
}
.rr-view {
  flex: 1;
  display: flex !important;
  flex-direction: column;
  padding: 0 !important;
  border-top: 1px solid #f4f4f4 !important;
}
.rr-view-container {
  margin-top: 50px;
}
.rr-view-wrap {
  display: flex;
  flex-direction: column;
}
.rr-view-ctx {
  padding: 16px 16px 16px 16px !important;
  flex: 1;
  height: 100%;
}
.rr-view-ctx-card {
  min-height: calc(100% - 5px);
  border-width: 0 !important;
}
.rr-view-tab {
  background: #fff;
  width: 100%;
  height: 39px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}
.rr-view-tab__header:hover {
  background: inherit !important;
}
.rr-view-tab-wrap {
  position: fixed;
  top: 50px;
  left: 255px;
  right: 0;
  display: flex;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  z-index: 100;
  transition: left 0.3s;
}
.rr-view-tab-ops {
  width: 40px;
  flex-shrink: 0;
  background: #fff;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #f4f4f4;
  cursor: pointer;
  text-align: center;
  color: #8c8c8c !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  margin-right: 5px;
}
.rr-view-tab .el-tabs__active-bar {
  height: 0;
}
.rr-view-tab .el-tabs__nav-prev .el-icon,
.rr-view-tab .el-tabs__nav-next .el-icon {
  display: none;
}
.rr-view-tab .el-tabs__nav .el-tabs__item {
  padding: 0 15px !important;
  border-right: 1px solid #f4f4f4;
  user-select: none;
  color: #8c8c8c;
}
.rr-view-tab .el-tabs__nav .el-tabs__item:hover {
  color: #262626;
  background-color: rgba(0, 0, 0, 0.02);
}
.rr-view-tab .el-tabs__nav .el-tabs__item .is-icon-close {
  transition: none !important;
}
.rr-view-tab .el-tabs__nav .el-tabs__item .is-icon-close:hover {
  color: #fff;
  background-color: #ff4d4f;
}
.rr-view-tab .el-tabs__nav .el-tabs__item::before {
  content: "";
  width: 9px;
  height: 9px;
  margin-right: 8px;
  display: inline-block;
  background-color: #ddd;
  border-radius: 50%;
}
.rr-view-tab .el-tabs__nav .el-tabs__item.is-active {
  color: var(--color-primary-light);
  background-color: var(--color-primary-light) !important;
}
.rr-view-tab .el-tabs__nav .el-tabs__item.is-active:before {
  background-color: var(--color-primary-light);
}
.rr-view-tab .el-tabs__nav .el-tabs__item:nth-child(2)::before {
  content: none;
}
.rr-view-tab .el-tabs__nav-wrap {
  padding: 0px 39px 0 40px !important;
}
.rr-view-tab .el-tabs__nav-wrap::before,
.rr-view-tab .el-tabs__nav-wrap::after {
  width: 40px;
  height: 40px;
  line-height: 44px;
  text-align: center;
  box-sizing: border-box;
  font-size: 16px;
  color: #8c8c8c;
  transition: background-color 0.2s;
  position: absolute;
  top: 0;
  left: 0;
  font-family: element-icons !important;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: not-allowed;
}
.rr-view-tab .el-tabs__nav-wrap::before {
  content: url('data:image/svg+xml;charset=utf-8,<svg width="16" height="16" color="rgb(140 140 140)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M609.408 149.376L277.76 489.6a32 32 0 000 44.672l331.648 340.352a29.12 29.12 0 0041.728 0 30.592 30.592 0 000-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 000-42.688 29.12 29.12 0 00-41.728 0z"></path></svg>');
  border-right: 1px solid #f4f4f4;
}
.rr-view-tab .el-tabs__nav-wrap::after {
  content: url('data:image/svg+xml;charset=utf-8,<svg width="16" height="16" color="rgb(140 140 140)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 000 42.752L652.736 512 340.864 831.872a30.592 30.592 0 000 42.752 29.12 29.12 0 0041.728 0L714.24 534.336a32 32 0 000-44.672L382.592 149.376a29.12 29.12 0 00-41.728 0z"></path></svg>');
  right: 0;
  left: auto;
  bottom: auto;
  height: auto;
  background-color: transparent;
  border-left: 1px solid #f4f4f4;
}
.rr-view-tab .el-tabs__nav-next,
.rr-view-tab .el-tabs__nav-prev {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  box-sizing: border-box;
  font-size: 16px;
  color: #8c8c8c;
  transition: background-color 0.2s;
  z-index: 10;
}
.rr-view-tab .el-tabs__nav-next i,
.rr-view-tab .el-tabs__nav-prev i {
  vertical-align: middle;
  margin-top: -4px;
}
.rr-view-tab .el-tabs__nav-next:hover,
.rr-view-tab .el-tabs__nav-prev:hover {
  background: rgba(0, 0, 0, 0.02);
}
.rr-view-tab .el-tabs__nav-prev {
  border-right: 1px solid #f4f4f4;
}
.ql-toolbar.ql-snow {
  width: 100% !important;
}
.el-form--inline .el-form-item > .el-input,
.el-form--inline .el-form-item .el-cascader,
.el-form--inline .el-form-item .el-select,
.el-form--inline .el-form-item .el-date-editor,
.el-form--inline .el-form-item .el-autocomplete {
  min-width: 200px;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  /* 文字颜色 */
  /* 背景色 */
  /* 边框颜色 */
  /* 背景色 */
  transition: background-color 50000s ease-in-out 0s !important;
  /* 防止背景色改变 */
}
.ny_form_card {
  margin-bottom: 15px;
}
.ny_form_card .el-card__body {
  padding: 20px 20px 2px 20px;
}
.ny-table-button-list {
  padding-bottom: 18px;
}
.flx {
  display: flex;
}
.flx-1 {
  flex: 1;
}
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-between {
  display: flex;
  justify-content: space-between;
}
.flx-justify-center {
  display: flex;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-justify-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flx-justify-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flx-align-center {
  display: flex;
  align-items: center;
}
.flx-align-end {
  display: flex;
  align-items: flex-end;
}
.flx-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.flx-nowrap {
  flex-wrap: nowrap;
}
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}
.mt-1 {
  margin-top: 1px !important;
}
.mr-1 {
  margin-right: 1px !important;
}
.mb-1 {
  margin-bottom: 1px !important;
}
.ml-1 {
  margin-left: 1px !important;
}
.pt-1 {
  padding-top: 1px !important;
}
.pr-1 {
  padding-right: 1px !important;
}
.pb-1 {
  padding-bottom: 1px !important;
}
.pl-1 {
  padding-left: 1px !important;
}
.mt-2 {
  margin-top: 2px !important;
}
.mr-2 {
  margin-right: 2px !important;
}
.mb-2 {
  margin-bottom: 2px !important;
}
.ml-2 {
  margin-left: 2px !important;
}
.pt-2 {
  padding-top: 2px !important;
}
.pr-2 {
  padding-right: 2px !important;
}
.pb-2 {
  padding-bottom: 2px !important;
}
.pl-2 {
  padding-left: 2px !important;
}
.mt-3 {
  margin-top: 3px !important;
}
.mr-3 {
  margin-right: 3px !important;
}
.mb-3 {
  margin-bottom: 3px !important;
}
.ml-3 {
  margin-left: 3px !important;
}
.pt-3 {
  padding-top: 3px !important;
}
.pr-3 {
  padding-right: 3px !important;
}
.pb-3 {
  padding-bottom: 3px !important;
}
.pl-3 {
  padding-left: 3px !important;
}
.mt-4 {
  margin-top: 4px !important;
}
.mr-4 {
  margin-right: 4px !important;
}
.mb-4 {
  margin-bottom: 4px !important;
}
.ml-4 {
  margin-left: 4px !important;
}
.pt-4 {
  padding-top: 4px !important;
}
.pr-4 {
  padding-right: 4px !important;
}
.pb-4 {
  padding-bottom: 4px !important;
}
.pl-4 {
  padding-left: 4px !important;
}
.mt-5 {
  margin-top: 5px !important;
}
.mr-5 {
  margin-right: 5px !important;
}
.mb-5 {
  margin-bottom: 5px !important;
}
.ml-5 {
  margin-left: 5px !important;
}
.pt-5 {
  padding-top: 5px !important;
}
.pr-5 {
  padding-right: 5px !important;
}
.pb-5 {
  padding-bottom: 5px !important;
}
.pl-5 {
  padding-left: 5px !important;
}
.mt-6 {
  margin-top: 6px !important;
}
.mr-6 {
  margin-right: 6px !important;
}
.mb-6 {
  margin-bottom: 6px !important;
}
.ml-6 {
  margin-left: 6px !important;
}
.pt-6 {
  padding-top: 6px !important;
}
.pr-6 {
  padding-right: 6px !important;
}
.pb-6 {
  padding-bottom: 6px !important;
}
.pl-6 {
  padding-left: 6px !important;
}
.mt-7 {
  margin-top: 7px !important;
}
.mr-7 {
  margin-right: 7px !important;
}
.mb-7 {
  margin-bottom: 7px !important;
}
.ml-7 {
  margin-left: 7px !important;
}
.pt-7 {
  padding-top: 7px !important;
}
.pr-7 {
  padding-right: 7px !important;
}
.pb-7 {
  padding-bottom: 7px !important;
}
.pl-7 {
  padding-left: 7px !important;
}
.mt-8 {
  margin-top: 8px !important;
}
.mr-8 {
  margin-right: 8px !important;
}
.mb-8 {
  margin-bottom: 8px !important;
}
.ml-8 {
  margin-left: 8px !important;
}
.pt-8 {
  padding-top: 8px !important;
}
.pr-8 {
  padding-right: 8px !important;
}
.pb-8 {
  padding-bottom: 8px !important;
}
.pl-8 {
  padding-left: 8px !important;
}
.mt-9 {
  margin-top: 9px !important;
}
.mr-9 {
  margin-right: 9px !important;
}
.mb-9 {
  margin-bottom: 9px !important;
}
.ml-9 {
  margin-left: 9px !important;
}
.pt-9 {
  padding-top: 9px !important;
}
.pr-9 {
  padding-right: 9px !important;
}
.pb-9 {
  padding-bottom: 9px !important;
}
.pl-9 {
  padding-left: 9px !important;
}
.mt-10 {
  margin-top: 10px !important;
}
.mr-10 {
  margin-right: 10px !important;
}
.mb-10 {
  margin-bottom: 10px !important;
}
.ml-10 {
  margin-left: 10px !important;
}
.pt-10 {
  padding-top: 10px !important;
}
.pr-10 {
  padding-right: 10px !important;
}
.pb-10 {
  padding-bottom: 10px !important;
}
.pl-10 {
  padding-left: 10px !important;
}
.mt-11 {
  margin-top: 11px !important;
}
.mr-11 {
  margin-right: 11px !important;
}
.mb-11 {
  margin-bottom: 11px !important;
}
.ml-11 {
  margin-left: 11px !important;
}
.pt-11 {
  padding-top: 11px !important;
}
.pr-11 {
  padding-right: 11px !important;
}
.pb-11 {
  padding-bottom: 11px !important;
}
.pl-11 {
  padding-left: 11px !important;
}
.mt-12 {
  margin-top: 12px !important;
}
.mr-12 {
  margin-right: 12px !important;
}
.mb-12 {
  margin-bottom: 12px !important;
}
.ml-12 {
  margin-left: 12px !important;
}
.pt-12 {
  padding-top: 12px !important;
}
.pr-12 {
  padding-right: 12px !important;
}
.pb-12 {
  padding-bottom: 12px !important;
}
.pl-12 {
  padding-left: 12px !important;
}
.mt-13 {
  margin-top: 13px !important;
}
.mr-13 {
  margin-right: 13px !important;
}
.mb-13 {
  margin-bottom: 13px !important;
}
.ml-13 {
  margin-left: 13px !important;
}
.pt-13 {
  padding-top: 13px !important;
}
.pr-13 {
  padding-right: 13px !important;
}
.pb-13 {
  padding-bottom: 13px !important;
}
.pl-13 {
  padding-left: 13px !important;
}
.mt-14 {
  margin-top: 14px !important;
}
.mr-14 {
  margin-right: 14px !important;
}
.mb-14 {
  margin-bottom: 14px !important;
}
.ml-14 {
  margin-left: 14px !important;
}
.pt-14 {
  padding-top: 14px !important;
}
.pr-14 {
  padding-right: 14px !important;
}
.pb-14 {
  padding-bottom: 14px !important;
}
.pl-14 {
  padding-left: 14px !important;
}
.mt-15 {
  margin-top: 15px !important;
}
.mr-15 {
  margin-right: 15px !important;
}
.mb-15 {
  margin-bottom: 15px !important;
}
.ml-15 {
  margin-left: 15px !important;
}
.pt-15 {
  padding-top: 15px !important;
}
.pr-15 {
  padding-right: 15px !important;
}
.pb-15 {
  padding-bottom: 15px !important;
}
.pl-15 {
  padding-left: 15px !important;
}
.mt-16 {
  margin-top: 16px !important;
}
.mr-16 {
  margin-right: 16px !important;
}
.mb-16 {
  margin-bottom: 16px !important;
}
.ml-16 {
  margin-left: 16px !important;
}
.pt-16 {
  padding-top: 16px !important;
}
.pr-16 {
  padding-right: 16px !important;
}
.pb-16 {
  padding-bottom: 16px !important;
}
.pl-16 {
  padding-left: 16px !important;
}
.mt-17 {
  margin-top: 17px !important;
}
.mr-17 {
  margin-right: 17px !important;
}
.mb-17 {
  margin-bottom: 17px !important;
}
.ml-17 {
  margin-left: 17px !important;
}
.pt-17 {
  padding-top: 17px !important;
}
.pr-17 {
  padding-right: 17px !important;
}
.pb-17 {
  padding-bottom: 17px !important;
}
.pl-17 {
  padding-left: 17px !important;
}
.mt-18 {
  margin-top: 18px !important;
}
.mr-18 {
  margin-right: 18px !important;
}
.mb-18 {
  margin-bottom: 18px !important;
}
.ml-18 {
  margin-left: 18px !important;
}
.pt-18 {
  padding-top: 18px !important;
}
.pr-18 {
  padding-right: 18px !important;
}
.pb-18 {
  padding-bottom: 18px !important;
}
.pl-18 {
  padding-left: 18px !important;
}
.mt-19 {
  margin-top: 19px !important;
}
.mr-19 {
  margin-right: 19px !important;
}
.mb-19 {
  margin-bottom: 19px !important;
}
.ml-19 {
  margin-left: 19px !important;
}
.pt-19 {
  padding-top: 19px !important;
}
.pr-19 {
  padding-right: 19px !important;
}
.pb-19 {
  padding-bottom: 19px !important;
}
.pl-19 {
  padding-left: 19px !important;
}
.mt-20 {
  margin-top: 20px !important;
}
.mr-20 {
  margin-right: 20px !important;
}
.mb-20 {
  margin-bottom: 20px !important;
}
.ml-20 {
  margin-left: 20px !important;
}
.pt-20 {
  padding-top: 20px !important;
}
.pr-20 {
  padding-right: 20px !important;
}
.pb-20 {
  padding-bottom: 20px !important;
}
.pl-20 {
  padding-left: 20px !important;
}
.mt-21 {
  margin-top: 21px !important;
}
.mr-21 {
  margin-right: 21px !important;
}
.mb-21 {
  margin-bottom: 21px !important;
}
.ml-21 {
  margin-left: 21px !important;
}
.pt-21 {
  padding-top: 21px !important;
}
.pr-21 {
  padding-right: 21px !important;
}
.pb-21 {
  padding-bottom: 21px !important;
}
.pl-21 {
  padding-left: 21px !important;
}
.mt-22 {
  margin-top: 22px !important;
}
.mr-22 {
  margin-right: 22px !important;
}
.mb-22 {
  margin-bottom: 22px !important;
}
.ml-22 {
  margin-left: 22px !important;
}
.pt-22 {
  padding-top: 22px !important;
}
.pr-22 {
  padding-right: 22px !important;
}
.pb-22 {
  padding-bottom: 22px !important;
}
.pl-22 {
  padding-left: 22px !important;
}
.mt-23 {
  margin-top: 23px !important;
}
.mr-23 {
  margin-right: 23px !important;
}
.mb-23 {
  margin-bottom: 23px !important;
}
.ml-23 {
  margin-left: 23px !important;
}
.pt-23 {
  padding-top: 23px !important;
}
.pr-23 {
  padding-right: 23px !important;
}
.pb-23 {
  padding-bottom: 23px !important;
}
.pl-23 {
  padding-left: 23px !important;
}
.mt-24 {
  margin-top: 24px !important;
}
.mr-24 {
  margin-right: 24px !important;
}
.mb-24 {
  margin-bottom: 24px !important;
}
.ml-24 {
  margin-left: 24px !important;
}
.pt-24 {
  padding-top: 24px !important;
}
.pr-24 {
  padding-right: 24px !important;
}
.pb-24 {
  padding-bottom: 24px !important;
}
.pl-24 {
  padding-left: 24px !important;
}
.mt-25 {
  margin-top: 25px !important;
}
.mr-25 {
  margin-right: 25px !important;
}
.mb-25 {
  margin-bottom: 25px !important;
}
.ml-25 {
  margin-left: 25px !important;
}
.pt-25 {
  padding-top: 25px !important;
}
.pr-25 {
  padding-right: 25px !important;
}
.pb-25 {
  padding-bottom: 25px !important;
}
.pl-25 {
  padding-left: 25px !important;
}
.mt-26 {
  margin-top: 26px !important;
}
.mr-26 {
  margin-right: 26px !important;
}
.mb-26 {
  margin-bottom: 26px !important;
}
.ml-26 {
  margin-left: 26px !important;
}
.pt-26 {
  padding-top: 26px !important;
}
.pr-26 {
  padding-right: 26px !important;
}
.pb-26 {
  padding-bottom: 26px !important;
}
.pl-26 {
  padding-left: 26px !important;
}
.mt-27 {
  margin-top: 27px !important;
}
.mr-27 {
  margin-right: 27px !important;
}
.mb-27 {
  margin-bottom: 27px !important;
}
.ml-27 {
  margin-left: 27px !important;
}
.pt-27 {
  padding-top: 27px !important;
}
.pr-27 {
  padding-right: 27px !important;
}
.pb-27 {
  padding-bottom: 27px !important;
}
.pl-27 {
  padding-left: 27px !important;
}
.mt-28 {
  margin-top: 28px !important;
}
.mr-28 {
  margin-right: 28px !important;
}
.mb-28 {
  margin-bottom: 28px !important;
}
.ml-28 {
  margin-left: 28px !important;
}
.pt-28 {
  padding-top: 28px !important;
}
.pr-28 {
  padding-right: 28px !important;
}
.pb-28 {
  padding-bottom: 28px !important;
}
.pl-28 {
  padding-left: 28px !important;
}
.mt-29 {
  margin-top: 29px !important;
}
.mr-29 {
  margin-right: 29px !important;
}
.mb-29 {
  margin-bottom: 29px !important;
}
.ml-29 {
  margin-left: 29px !important;
}
.pt-29 {
  padding-top: 29px !important;
}
.pr-29 {
  padding-right: 29px !important;
}
.pb-29 {
  padding-bottom: 29px !important;
}
.pl-29 {
  padding-left: 29px !important;
}
.mt-30 {
  margin-top: 30px !important;
}
.mr-30 {
  margin-right: 30px !important;
}
.mb-30 {
  margin-bottom: 30px !important;
}
.ml-30 {
  margin-left: 30px !important;
}
.pt-30 {
  padding-top: 30px !important;
}
.pr-30 {
  padding-right: 30px !important;
}
.pb-30 {
  padding-bottom: 30px !important;
}
.pl-30 {
  padding-left: 30px !important;
}
.mt-31 {
  margin-top: 31px !important;
}
.mr-31 {
  margin-right: 31px !important;
}
.mb-31 {
  margin-bottom: 31px !important;
}
.ml-31 {
  margin-left: 31px !important;
}
.pt-31 {
  padding-top: 31px !important;
}
.pr-31 {
  padding-right: 31px !important;
}
.pb-31 {
  padding-bottom: 31px !important;
}
.pl-31 {
  padding-left: 31px !important;
}
.mt-32 {
  margin-top: 32px !important;
}
.mr-32 {
  margin-right: 32px !important;
}
.mb-32 {
  margin-bottom: 32px !important;
}
.ml-32 {
  margin-left: 32px !important;
}
.pt-32 {
  padding-top: 32px !important;
}
.pr-32 {
  padding-right: 32px !important;
}
.pb-32 {
  padding-bottom: 32px !important;
}
.pl-32 {
  padding-left: 32px !important;
}
.mt-33 {
  margin-top: 33px !important;
}
.mr-33 {
  margin-right: 33px !important;
}
.mb-33 {
  margin-bottom: 33px !important;
}
.ml-33 {
  margin-left: 33px !important;
}
.pt-33 {
  padding-top: 33px !important;
}
.pr-33 {
  padding-right: 33px !important;
}
.pb-33 {
  padding-bottom: 33px !important;
}
.pl-33 {
  padding-left: 33px !important;
}
.mt-34 {
  margin-top: 34px !important;
}
.mr-34 {
  margin-right: 34px !important;
}
.mb-34 {
  margin-bottom: 34px !important;
}
.ml-34 {
  margin-left: 34px !important;
}
.pt-34 {
  padding-top: 34px !important;
}
.pr-34 {
  padding-right: 34px !important;
}
.pb-34 {
  padding-bottom: 34px !important;
}
.pl-34 {
  padding-left: 34px !important;
}
.mt-35 {
  margin-top: 35px !important;
}
.mr-35 {
  margin-right: 35px !important;
}
.mb-35 {
  margin-bottom: 35px !important;
}
.ml-35 {
  margin-left: 35px !important;
}
.pt-35 {
  padding-top: 35px !important;
}
.pr-35 {
  padding-right: 35px !important;
}
.pb-35 {
  padding-bottom: 35px !important;
}
.pl-35 {
  padding-left: 35px !important;
}
.mt-36 {
  margin-top: 36px !important;
}
.mr-36 {
  margin-right: 36px !important;
}
.mb-36 {
  margin-bottom: 36px !important;
}
.ml-36 {
  margin-left: 36px !important;
}
.pt-36 {
  padding-top: 36px !important;
}
.pr-36 {
  padding-right: 36px !important;
}
.pb-36 {
  padding-bottom: 36px !important;
}
.pl-36 {
  padding-left: 36px !important;
}
.mt-37 {
  margin-top: 37px !important;
}
.mr-37 {
  margin-right: 37px !important;
}
.mb-37 {
  margin-bottom: 37px !important;
}
.ml-37 {
  margin-left: 37px !important;
}
.pt-37 {
  padding-top: 37px !important;
}
.pr-37 {
  padding-right: 37px !important;
}
.pb-37 {
  padding-bottom: 37px !important;
}
.pl-37 {
  padding-left: 37px !important;
}
.mt-38 {
  margin-top: 38px !important;
}
.mr-38 {
  margin-right: 38px !important;
}
.mb-38 {
  margin-bottom: 38px !important;
}
.ml-38 {
  margin-left: 38px !important;
}
.pt-38 {
  padding-top: 38px !important;
}
.pr-38 {
  padding-right: 38px !important;
}
.pb-38 {
  padding-bottom: 38px !important;
}
.pl-38 {
  padding-left: 38px !important;
}
.mt-39 {
  margin-top: 39px !important;
}
.mr-39 {
  margin-right: 39px !important;
}
.mb-39 {
  margin-bottom: 39px !important;
}
.ml-39 {
  margin-left: 39px !important;
}
.pt-39 {
  padding-top: 39px !important;
}
.pr-39 {
  padding-right: 39px !important;
}
.pb-39 {
  padding-bottom: 39px !important;
}
.pl-39 {
  padding-left: 39px !important;
}
.mt-40 {
  margin-top: 40px !important;
}
.mr-40 {
  margin-right: 40px !important;
}
.mb-40 {
  margin-bottom: 40px !important;
}
.ml-40 {
  margin-left: 40px !important;
}
.pt-40 {
  padding-top: 40px !important;
}
.pr-40 {
  padding-right: 40px !important;
}
.pb-40 {
  padding-bottom: 40px !important;
}
.pl-40 {
  padding-left: 40px !important;
}
.mt-41 {
  margin-top: 41px !important;
}
.mr-41 {
  margin-right: 41px !important;
}
.mb-41 {
  margin-bottom: 41px !important;
}
.ml-41 {
  margin-left: 41px !important;
}
.pt-41 {
  padding-top: 41px !important;
}
.pr-41 {
  padding-right: 41px !important;
}
.pb-41 {
  padding-bottom: 41px !important;
}
.pl-41 {
  padding-left: 41px !important;
}
.mt-42 {
  margin-top: 42px !important;
}
.mr-42 {
  margin-right: 42px !important;
}
.mb-42 {
  margin-bottom: 42px !important;
}
.ml-42 {
  margin-left: 42px !important;
}
.pt-42 {
  padding-top: 42px !important;
}
.pr-42 {
  padding-right: 42px !important;
}
.pb-42 {
  padding-bottom: 42px !important;
}
.pl-42 {
  padding-left: 42px !important;
}
.mt-43 {
  margin-top: 43px !important;
}
.mr-43 {
  margin-right: 43px !important;
}
.mb-43 {
  margin-bottom: 43px !important;
}
.ml-43 {
  margin-left: 43px !important;
}
.pt-43 {
  padding-top: 43px !important;
}
.pr-43 {
  padding-right: 43px !important;
}
.pb-43 {
  padding-bottom: 43px !important;
}
.pl-43 {
  padding-left: 43px !important;
}
.mt-44 {
  margin-top: 44px !important;
}
.mr-44 {
  margin-right: 44px !important;
}
.mb-44 {
  margin-bottom: 44px !important;
}
.ml-44 {
  margin-left: 44px !important;
}
.pt-44 {
  padding-top: 44px !important;
}
.pr-44 {
  padding-right: 44px !important;
}
.pb-44 {
  padding-bottom: 44px !important;
}
.pl-44 {
  padding-left: 44px !important;
}
.mt-45 {
  margin-top: 45px !important;
}
.mr-45 {
  margin-right: 45px !important;
}
.mb-45 {
  margin-bottom: 45px !important;
}
.ml-45 {
  margin-left: 45px !important;
}
.pt-45 {
  padding-top: 45px !important;
}
.pr-45 {
  padding-right: 45px !important;
}
.pb-45 {
  padding-bottom: 45px !important;
}
.pl-45 {
  padding-left: 45px !important;
}
.mt-46 {
  margin-top: 46px !important;
}
.mr-46 {
  margin-right: 46px !important;
}
.mb-46 {
  margin-bottom: 46px !important;
}
.ml-46 {
  margin-left: 46px !important;
}
.pt-46 {
  padding-top: 46px !important;
}
.pr-46 {
  padding-right: 46px !important;
}
.pb-46 {
  padding-bottom: 46px !important;
}
.pl-46 {
  padding-left: 46px !important;
}
.mt-47 {
  margin-top: 47px !important;
}
.mr-47 {
  margin-right: 47px !important;
}
.mb-47 {
  margin-bottom: 47px !important;
}
.ml-47 {
  margin-left: 47px !important;
}
.pt-47 {
  padding-top: 47px !important;
}
.pr-47 {
  padding-right: 47px !important;
}
.pb-47 {
  padding-bottom: 47px !important;
}
.pl-47 {
  padding-left: 47px !important;
}
.mt-48 {
  margin-top: 48px !important;
}
.mr-48 {
  margin-right: 48px !important;
}
.mb-48 {
  margin-bottom: 48px !important;
}
.ml-48 {
  margin-left: 48px !important;
}
.pt-48 {
  padding-top: 48px !important;
}
.pr-48 {
  padding-right: 48px !important;
}
.pb-48 {
  padding-bottom: 48px !important;
}
.pl-48 {
  padding-left: 48px !important;
}
.mt-49 {
  margin-top: 49px !important;
}
.mr-49 {
  margin-right: 49px !important;
}
.mb-49 {
  margin-bottom: 49px !important;
}
.ml-49 {
  margin-left: 49px !important;
}
.pt-49 {
  padding-top: 49px !important;
}
.pr-49 {
  padding-right: 49px !important;
}
.pb-49 {
  padding-bottom: 49px !important;
}
.pl-49 {
  padding-left: 49px !important;
}
.mt-50 {
  margin-top: 50px !important;
}
.mr-50 {
  margin-right: 50px !important;
}
.mb-50 {
  margin-bottom: 50px !important;
}
.ml-50 {
  margin-left: 50px !important;
}
.pt-50 {
  padding-top: 50px !important;
}
.pr-50 {
  padding-right: 50px !important;
}
.pb-50 {
  padding-bottom: 50px !important;
}
.pl-50 {
  padding-left: 50px !important;
}
.mt-51 {
  margin-top: 51px !important;
}
.mr-51 {
  margin-right: 51px !important;
}
.mb-51 {
  margin-bottom: 51px !important;
}
.ml-51 {
  margin-left: 51px !important;
}
.pt-51 {
  padding-top: 51px !important;
}
.pr-51 {
  padding-right: 51px !important;
}
.pb-51 {
  padding-bottom: 51px !important;
}
.pl-51 {
  padding-left: 51px !important;
}
.mt-52 {
  margin-top: 52px !important;
}
.mr-52 {
  margin-right: 52px !important;
}
.mb-52 {
  margin-bottom: 52px !important;
}
.ml-52 {
  margin-left: 52px !important;
}
.pt-52 {
  padding-top: 52px !important;
}
.pr-52 {
  padding-right: 52px !important;
}
.pb-52 {
  padding-bottom: 52px !important;
}
.pl-52 {
  padding-left: 52px !important;
}
.mt-53 {
  margin-top: 53px !important;
}
.mr-53 {
  margin-right: 53px !important;
}
.mb-53 {
  margin-bottom: 53px !important;
}
.ml-53 {
  margin-left: 53px !important;
}
.pt-53 {
  padding-top: 53px !important;
}
.pr-53 {
  padding-right: 53px !important;
}
.pb-53 {
  padding-bottom: 53px !important;
}
.pl-53 {
  padding-left: 53px !important;
}
.mt-54 {
  margin-top: 54px !important;
}
.mr-54 {
  margin-right: 54px !important;
}
.mb-54 {
  margin-bottom: 54px !important;
}
.ml-54 {
  margin-left: 54px !important;
}
.pt-54 {
  padding-top: 54px !important;
}
.pr-54 {
  padding-right: 54px !important;
}
.pb-54 {
  padding-bottom: 54px !important;
}
.pl-54 {
  padding-left: 54px !important;
}
.mt-55 {
  margin-top: 55px !important;
}
.mr-55 {
  margin-right: 55px !important;
}
.mb-55 {
  margin-bottom: 55px !important;
}
.ml-55 {
  margin-left: 55px !important;
}
.pt-55 {
  padding-top: 55px !important;
}
.pr-55 {
  padding-right: 55px !important;
}
.pb-55 {
  padding-bottom: 55px !important;
}
.pl-55 {
  padding-left: 55px !important;
}
.mt-56 {
  margin-top: 56px !important;
}
.mr-56 {
  margin-right: 56px !important;
}
.mb-56 {
  margin-bottom: 56px !important;
}
.ml-56 {
  margin-left: 56px !important;
}
.pt-56 {
  padding-top: 56px !important;
}
.pr-56 {
  padding-right: 56px !important;
}
.pb-56 {
  padding-bottom: 56px !important;
}
.pl-56 {
  padding-left: 56px !important;
}
.mt-57 {
  margin-top: 57px !important;
}
.mr-57 {
  margin-right: 57px !important;
}
.mb-57 {
  margin-bottom: 57px !important;
}
.ml-57 {
  margin-left: 57px !important;
}
.pt-57 {
  padding-top: 57px !important;
}
.pr-57 {
  padding-right: 57px !important;
}
.pb-57 {
  padding-bottom: 57px !important;
}
.pl-57 {
  padding-left: 57px !important;
}
.mt-58 {
  margin-top: 58px !important;
}
.mr-58 {
  margin-right: 58px !important;
}
.mb-58 {
  margin-bottom: 58px !important;
}
.ml-58 {
  margin-left: 58px !important;
}
.pt-58 {
  padding-top: 58px !important;
}
.pr-58 {
  padding-right: 58px !important;
}
.pb-58 {
  padding-bottom: 58px !important;
}
.pl-58 {
  padding-left: 58px !important;
}
.mt-59 {
  margin-top: 59px !important;
}
.mr-59 {
  margin-right: 59px !important;
}
.mb-59 {
  margin-bottom: 59px !important;
}
.ml-59 {
  margin-left: 59px !important;
}
.pt-59 {
  padding-top: 59px !important;
}
.pr-59 {
  padding-right: 59px !important;
}
.pb-59 {
  padding-bottom: 59px !important;
}
.pl-59 {
  padding-left: 59px !important;
}
.mt-60 {
  margin-top: 60px !important;
}
.mr-60 {
  margin-right: 60px !important;
}
.mb-60 {
  margin-bottom: 60px !important;
}
.ml-60 {
  margin-left: 60px !important;
}
.pt-60 {
  padding-top: 60px !important;
}
.pr-60 {
  padding-right: 60px !important;
}
.pb-60 {
  padding-bottom: 60px !important;
}
.pl-60 {
  padding-left: 60px !important;
}
.mt-61 {
  margin-top: 61px !important;
}
.mr-61 {
  margin-right: 61px !important;
}
.mb-61 {
  margin-bottom: 61px !important;
}
.ml-61 {
  margin-left: 61px !important;
}
.pt-61 {
  padding-top: 61px !important;
}
.pr-61 {
  padding-right: 61px !important;
}
.pb-61 {
  padding-bottom: 61px !important;
}
.pl-61 {
  padding-left: 61px !important;
}
.mt-62 {
  margin-top: 62px !important;
}
.mr-62 {
  margin-right: 62px !important;
}
.mb-62 {
  margin-bottom: 62px !important;
}
.ml-62 {
  margin-left: 62px !important;
}
.pt-62 {
  padding-top: 62px !important;
}
.pr-62 {
  padding-right: 62px !important;
}
.pb-62 {
  padding-bottom: 62px !important;
}
.pl-62 {
  padding-left: 62px !important;
}
.mt-63 {
  margin-top: 63px !important;
}
.mr-63 {
  margin-right: 63px !important;
}
.mb-63 {
  margin-bottom: 63px !important;
}
.ml-63 {
  margin-left: 63px !important;
}
.pt-63 {
  padding-top: 63px !important;
}
.pr-63 {
  padding-right: 63px !important;
}
.pb-63 {
  padding-bottom: 63px !important;
}
.pl-63 {
  padding-left: 63px !important;
}
.mt-64 {
  margin-top: 64px !important;
}
.mr-64 {
  margin-right: 64px !important;
}
.mb-64 {
  margin-bottom: 64px !important;
}
.ml-64 {
  margin-left: 64px !important;
}
.pt-64 {
  padding-top: 64px !important;
}
.pr-64 {
  padding-right: 64px !important;
}
.pb-64 {
  padding-bottom: 64px !important;
}
.pl-64 {
  padding-left: 64px !important;
}
.mt-65 {
  margin-top: 65px !important;
}
.mr-65 {
  margin-right: 65px !important;
}
.mb-65 {
  margin-bottom: 65px !important;
}
.ml-65 {
  margin-left: 65px !important;
}
.pt-65 {
  padding-top: 65px !important;
}
.pr-65 {
  padding-right: 65px !important;
}
.pb-65 {
  padding-bottom: 65px !important;
}
.pl-65 {
  padding-left: 65px !important;
}
.mt-66 {
  margin-top: 66px !important;
}
.mr-66 {
  margin-right: 66px !important;
}
.mb-66 {
  margin-bottom: 66px !important;
}
.ml-66 {
  margin-left: 66px !important;
}
.pt-66 {
  padding-top: 66px !important;
}
.pr-66 {
  padding-right: 66px !important;
}
.pb-66 {
  padding-bottom: 66px !important;
}
.pl-66 {
  padding-left: 66px !important;
}
.mt-67 {
  margin-top: 67px !important;
}
.mr-67 {
  margin-right: 67px !important;
}
.mb-67 {
  margin-bottom: 67px !important;
}
.ml-67 {
  margin-left: 67px !important;
}
.pt-67 {
  padding-top: 67px !important;
}
.pr-67 {
  padding-right: 67px !important;
}
.pb-67 {
  padding-bottom: 67px !important;
}
.pl-67 {
  padding-left: 67px !important;
}
.mt-68 {
  margin-top: 68px !important;
}
.mr-68 {
  margin-right: 68px !important;
}
.mb-68 {
  margin-bottom: 68px !important;
}
.ml-68 {
  margin-left: 68px !important;
}
.pt-68 {
  padding-top: 68px !important;
}
.pr-68 {
  padding-right: 68px !important;
}
.pb-68 {
  padding-bottom: 68px !important;
}
.pl-68 {
  padding-left: 68px !important;
}
.mt-69 {
  margin-top: 69px !important;
}
.mr-69 {
  margin-right: 69px !important;
}
.mb-69 {
  margin-bottom: 69px !important;
}
.ml-69 {
  margin-left: 69px !important;
}
.pt-69 {
  padding-top: 69px !important;
}
.pr-69 {
  padding-right: 69px !important;
}
.pb-69 {
  padding-bottom: 69px !important;
}
.pl-69 {
  padding-left: 69px !important;
}
.mt-70 {
  margin-top: 70px !important;
}
.mr-70 {
  margin-right: 70px !important;
}
.mb-70 {
  margin-bottom: 70px !important;
}
.ml-70 {
  margin-left: 70px !important;
}
.pt-70 {
  padding-top: 70px !important;
}
.pr-70 {
  padding-right: 70px !important;
}
.pb-70 {
  padding-bottom: 70px !important;
}
.pl-70 {
  padding-left: 70px !important;
}
.mt-71 {
  margin-top: 71px !important;
}
.mr-71 {
  margin-right: 71px !important;
}
.mb-71 {
  margin-bottom: 71px !important;
}
.ml-71 {
  margin-left: 71px !important;
}
.pt-71 {
  padding-top: 71px !important;
}
.pr-71 {
  padding-right: 71px !important;
}
.pb-71 {
  padding-bottom: 71px !important;
}
.pl-71 {
  padding-left: 71px !important;
}
.mt-72 {
  margin-top: 72px !important;
}
.mr-72 {
  margin-right: 72px !important;
}
.mb-72 {
  margin-bottom: 72px !important;
}
.ml-72 {
  margin-left: 72px !important;
}
.pt-72 {
  padding-top: 72px !important;
}
.pr-72 {
  padding-right: 72px !important;
}
.pb-72 {
  padding-bottom: 72px !important;
}
.pl-72 {
  padding-left: 72px !important;
}
.mt-73 {
  margin-top: 73px !important;
}
.mr-73 {
  margin-right: 73px !important;
}
.mb-73 {
  margin-bottom: 73px !important;
}
.ml-73 {
  margin-left: 73px !important;
}
.pt-73 {
  padding-top: 73px !important;
}
.pr-73 {
  padding-right: 73px !important;
}
.pb-73 {
  padding-bottom: 73px !important;
}
.pl-73 {
  padding-left: 73px !important;
}
.mt-74 {
  margin-top: 74px !important;
}
.mr-74 {
  margin-right: 74px !important;
}
.mb-74 {
  margin-bottom: 74px !important;
}
.ml-74 {
  margin-left: 74px !important;
}
.pt-74 {
  padding-top: 74px !important;
}
.pr-74 {
  padding-right: 74px !important;
}
.pb-74 {
  padding-bottom: 74px !important;
}
.pl-74 {
  padding-left: 74px !important;
}
.mt-75 {
  margin-top: 75px !important;
}
.mr-75 {
  margin-right: 75px !important;
}
.mb-75 {
  margin-bottom: 75px !important;
}
.ml-75 {
  margin-left: 75px !important;
}
.pt-75 {
  padding-top: 75px !important;
}
.pr-75 {
  padding-right: 75px !important;
}
.pb-75 {
  padding-bottom: 75px !important;
}
.pl-75 {
  padding-left: 75px !important;
}
.mt-76 {
  margin-top: 76px !important;
}
.mr-76 {
  margin-right: 76px !important;
}
.mb-76 {
  margin-bottom: 76px !important;
}
.ml-76 {
  margin-left: 76px !important;
}
.pt-76 {
  padding-top: 76px !important;
}
.pr-76 {
  padding-right: 76px !important;
}
.pb-76 {
  padding-bottom: 76px !important;
}
.pl-76 {
  padding-left: 76px !important;
}
.mt-77 {
  margin-top: 77px !important;
}
.mr-77 {
  margin-right: 77px !important;
}
.mb-77 {
  margin-bottom: 77px !important;
}
.ml-77 {
  margin-left: 77px !important;
}
.pt-77 {
  padding-top: 77px !important;
}
.pr-77 {
  padding-right: 77px !important;
}
.pb-77 {
  padding-bottom: 77px !important;
}
.pl-77 {
  padding-left: 77px !important;
}
.mt-78 {
  margin-top: 78px !important;
}
.mr-78 {
  margin-right: 78px !important;
}
.mb-78 {
  margin-bottom: 78px !important;
}
.ml-78 {
  margin-left: 78px !important;
}
.pt-78 {
  padding-top: 78px !important;
}
.pr-78 {
  padding-right: 78px !important;
}
.pb-78 {
  padding-bottom: 78px !important;
}
.pl-78 {
  padding-left: 78px !important;
}
.mt-79 {
  margin-top: 79px !important;
}
.mr-79 {
  margin-right: 79px !important;
}
.mb-79 {
  margin-bottom: 79px !important;
}
.ml-79 {
  margin-left: 79px !important;
}
.pt-79 {
  padding-top: 79px !important;
}
.pr-79 {
  padding-right: 79px !important;
}
.pb-79 {
  padding-bottom: 79px !important;
}
.pl-79 {
  padding-left: 79px !important;
}
.mt-80 {
  margin-top: 80px !important;
}
.mr-80 {
  margin-right: 80px !important;
}
.mb-80 {
  margin-bottom: 80px !important;
}
.ml-80 {
  margin-left: 80px !important;
}
.pt-80 {
  padding-top: 80px !important;
}
.pr-80 {
  padding-right: 80px !important;
}
.pb-80 {
  padding-bottom: 80px !important;
}
.pl-80 {
  padding-left: 80px !important;
}
.mt-81 {
  margin-top: 81px !important;
}
.mr-81 {
  margin-right: 81px !important;
}
.mb-81 {
  margin-bottom: 81px !important;
}
.ml-81 {
  margin-left: 81px !important;
}
.pt-81 {
  padding-top: 81px !important;
}
.pr-81 {
  padding-right: 81px !important;
}
.pb-81 {
  padding-bottom: 81px !important;
}
.pl-81 {
  padding-left: 81px !important;
}
.mt-82 {
  margin-top: 82px !important;
}
.mr-82 {
  margin-right: 82px !important;
}
.mb-82 {
  margin-bottom: 82px !important;
}
.ml-82 {
  margin-left: 82px !important;
}
.pt-82 {
  padding-top: 82px !important;
}
.pr-82 {
  padding-right: 82px !important;
}
.pb-82 {
  padding-bottom: 82px !important;
}
.pl-82 {
  padding-left: 82px !important;
}
.mt-83 {
  margin-top: 83px !important;
}
.mr-83 {
  margin-right: 83px !important;
}
.mb-83 {
  margin-bottom: 83px !important;
}
.ml-83 {
  margin-left: 83px !important;
}
.pt-83 {
  padding-top: 83px !important;
}
.pr-83 {
  padding-right: 83px !important;
}
.pb-83 {
  padding-bottom: 83px !important;
}
.pl-83 {
  padding-left: 83px !important;
}
.mt-84 {
  margin-top: 84px !important;
}
.mr-84 {
  margin-right: 84px !important;
}
.mb-84 {
  margin-bottom: 84px !important;
}
.ml-84 {
  margin-left: 84px !important;
}
.pt-84 {
  padding-top: 84px !important;
}
.pr-84 {
  padding-right: 84px !important;
}
.pb-84 {
  padding-bottom: 84px !important;
}
.pl-84 {
  padding-left: 84px !important;
}
.mt-85 {
  margin-top: 85px !important;
}
.mr-85 {
  margin-right: 85px !important;
}
.mb-85 {
  margin-bottom: 85px !important;
}
.ml-85 {
  margin-left: 85px !important;
}
.pt-85 {
  padding-top: 85px !important;
}
.pr-85 {
  padding-right: 85px !important;
}
.pb-85 {
  padding-bottom: 85px !important;
}
.pl-85 {
  padding-left: 85px !important;
}
.mt-86 {
  margin-top: 86px !important;
}
.mr-86 {
  margin-right: 86px !important;
}
.mb-86 {
  margin-bottom: 86px !important;
}
.ml-86 {
  margin-left: 86px !important;
}
.pt-86 {
  padding-top: 86px !important;
}
.pr-86 {
  padding-right: 86px !important;
}
.pb-86 {
  padding-bottom: 86px !important;
}
.pl-86 {
  padding-left: 86px !important;
}
.mt-87 {
  margin-top: 87px !important;
}
.mr-87 {
  margin-right: 87px !important;
}
.mb-87 {
  margin-bottom: 87px !important;
}
.ml-87 {
  margin-left: 87px !important;
}
.pt-87 {
  padding-top: 87px !important;
}
.pr-87 {
  padding-right: 87px !important;
}
.pb-87 {
  padding-bottom: 87px !important;
}
.pl-87 {
  padding-left: 87px !important;
}
.mt-88 {
  margin-top: 88px !important;
}
.mr-88 {
  margin-right: 88px !important;
}
.mb-88 {
  margin-bottom: 88px !important;
}
.ml-88 {
  margin-left: 88px !important;
}
.pt-88 {
  padding-top: 88px !important;
}
.pr-88 {
  padding-right: 88px !important;
}
.pb-88 {
  padding-bottom: 88px !important;
}
.pl-88 {
  padding-left: 88px !important;
}
.mt-89 {
  margin-top: 89px !important;
}
.mr-89 {
  margin-right: 89px !important;
}
.mb-89 {
  margin-bottom: 89px !important;
}
.ml-89 {
  margin-left: 89px !important;
}
.pt-89 {
  padding-top: 89px !important;
}
.pr-89 {
  padding-right: 89px !important;
}
.pb-89 {
  padding-bottom: 89px !important;
}
.pl-89 {
  padding-left: 89px !important;
}
.mt-90 {
  margin-top: 90px !important;
}
.mr-90 {
  margin-right: 90px !important;
}
.mb-90 {
  margin-bottom: 90px !important;
}
.ml-90 {
  margin-left: 90px !important;
}
.pt-90 {
  padding-top: 90px !important;
}
.pr-90 {
  padding-right: 90px !important;
}
.pb-90 {
  padding-bottom: 90px !important;
}
.pl-90 {
  padding-left: 90px !important;
}
.mt-91 {
  margin-top: 91px !important;
}
.mr-91 {
  margin-right: 91px !important;
}
.mb-91 {
  margin-bottom: 91px !important;
}
.ml-91 {
  margin-left: 91px !important;
}
.pt-91 {
  padding-top: 91px !important;
}
.pr-91 {
  padding-right: 91px !important;
}
.pb-91 {
  padding-bottom: 91px !important;
}
.pl-91 {
  padding-left: 91px !important;
}
.mt-92 {
  margin-top: 92px !important;
}
.mr-92 {
  margin-right: 92px !important;
}
.mb-92 {
  margin-bottom: 92px !important;
}
.ml-92 {
  margin-left: 92px !important;
}
.pt-92 {
  padding-top: 92px !important;
}
.pr-92 {
  padding-right: 92px !important;
}
.pb-92 {
  padding-bottom: 92px !important;
}
.pl-92 {
  padding-left: 92px !important;
}
.mt-93 {
  margin-top: 93px !important;
}
.mr-93 {
  margin-right: 93px !important;
}
.mb-93 {
  margin-bottom: 93px !important;
}
.ml-93 {
  margin-left: 93px !important;
}
.pt-93 {
  padding-top: 93px !important;
}
.pr-93 {
  padding-right: 93px !important;
}
.pb-93 {
  padding-bottom: 93px !important;
}
.pl-93 {
  padding-left: 93px !important;
}
.mt-94 {
  margin-top: 94px !important;
}
.mr-94 {
  margin-right: 94px !important;
}
.mb-94 {
  margin-bottom: 94px !important;
}
.ml-94 {
  margin-left: 94px !important;
}
.pt-94 {
  padding-top: 94px !important;
}
.pr-94 {
  padding-right: 94px !important;
}
.pb-94 {
  padding-bottom: 94px !important;
}
.pl-94 {
  padding-left: 94px !important;
}
.mt-95 {
  margin-top: 95px !important;
}
.mr-95 {
  margin-right: 95px !important;
}
.mb-95 {
  margin-bottom: 95px !important;
}
.ml-95 {
  margin-left: 95px !important;
}
.pt-95 {
  padding-top: 95px !important;
}
.pr-95 {
  padding-right: 95px !important;
}
.pb-95 {
  padding-bottom: 95px !important;
}
.pl-95 {
  padding-left: 95px !important;
}
.mt-96 {
  margin-top: 96px !important;
}
.mr-96 {
  margin-right: 96px !important;
}
.mb-96 {
  margin-bottom: 96px !important;
}
.ml-96 {
  margin-left: 96px !important;
}
.pt-96 {
  padding-top: 96px !important;
}
.pr-96 {
  padding-right: 96px !important;
}
.pb-96 {
  padding-bottom: 96px !important;
}
.pl-96 {
  padding-left: 96px !important;
}
.mt-97 {
  margin-top: 97px !important;
}
.mr-97 {
  margin-right: 97px !important;
}
.mb-97 {
  margin-bottom: 97px !important;
}
.ml-97 {
  margin-left: 97px !important;
}
.pt-97 {
  padding-top: 97px !important;
}
.pr-97 {
  padding-right: 97px !important;
}
.pb-97 {
  padding-bottom: 97px !important;
}
.pl-97 {
  padding-left: 97px !important;
}
.mt-98 {
  margin-top: 98px !important;
}
.mr-98 {
  margin-right: 98px !important;
}
.mb-98 {
  margin-bottom: 98px !important;
}
.ml-98 {
  margin-left: 98px !important;
}
.pt-98 {
  padding-top: 98px !important;
}
.pr-98 {
  padding-right: 98px !important;
}
.pb-98 {
  padding-bottom: 98px !important;
}
.pl-98 {
  padding-left: 98px !important;
}
.mt-99 {
  margin-top: 99px !important;
}
.mr-99 {
  margin-right: 99px !important;
}
.mb-99 {
  margin-bottom: 99px !important;
}
.ml-99 {
  margin-left: 99px !important;
}
.pt-99 {
  padding-top: 99px !important;
}
.pr-99 {
  padding-right: 99px !important;
}
.pb-99 {
  padding-bottom: 99px !important;
}
.pl-99 {
  padding-left: 99px !important;
}
.mt-100 {
  margin-top: 100px !important;
}
.mr-100 {
  margin-right: 100px !important;
}
.mb-100 {
  margin-bottom: 100px !important;
}
.ml-100 {
  margin-left: 100px !important;
}
.pt-100 {
  padding-top: 100px !important;
}
.pr-100 {
  padding-right: 100px !important;
}
.pb-100 {
  padding-bottom: 100px !important;
}
.pl-100 {
  padding-left: 100px !important;
}
.mt-0 {
  margin-top: 0;
}
.w-100 {
  width: 100% !important;
}
.h-100 {
  height: 100% !important;
}
.input-240 {
  width: 240px !important;
}
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
}
.buttonlink {
  padding: 7px 12px !important;
  background-color: #f4f6f9 !important;
}
.el-input-text-left {
  text-align: left;
}
.el-input-text-left .el-input__inner {
  text-align: left !important;
}
.text-primary {
  color: var(--el-color-primary) !important;
}
.el-table thead th {
  padding: 0 !important;
  height: 1px;
}
.el-table thead th .cell {
  padding: 8px 12px;
  height: 100% !important;
  width: 100%;
  font-weight: bold;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #1d2129;
}
.el-table thead th.is-center .cell {
  justify-content: center;
}
.el-table thead th.is-right .cell {
  justify-content: flex-end;
}
.el-table-column--selection {
  text-align: center;
}
.el-table-column--selection .cell {
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pointer {
  cursor: pointer;
}
.bold {
  font-weight: bold;
}
.text-size-small {
  font-size: 12px;
}
.text-size-13 {
  font-size: 13px;
}
.text-size-base {
  font-size: 14px;
}
.text-size-large {
  font-size: 16px;
}
.p-title {
  color: #303133;
  display: flex;
  line-height: 20px;
  margin: 12px 0;
  font-size: 14px;
  font-weight: bold;
  align-items: center;
}
.p-title.mt-0 {
  margin-top: 0;
}
.p-title::before {
  content: "";
  display: inline-block;
  width: 2px;
  height: 20px;
  background: var(--el-color-primary);
  margin-right: 8px;
}
.el-drawer__header {
  padding: 20px 20px 8px 20px !important;
  margin: 0 !important;
  line-height: 24px;
  box-sizing: border-box;
  font-weight: bold;
}
.el-drawer__header .el-drawer__title {
  font-size: 18px;
  color: #303133;
}
.el-table .el-button.is-text,
.el-table .el-button.is-text.is-has-bg {
  height: 22px;
  background-color: transparent !important;
  padding: 0 4px;
}
.el-table .el-button + .el-button {
  margin-left: 8px;
}
.el-table .el-table__body .ch-96 {
  height: 96px;
  line-height: 96px;
}
.el-table .el-table__body .ch-80 {
  height: 80px;
  line-height: 80px;
}
.el-table .el-table__body .ch-56 {
  height: 56px;
  line-height: 56px;
}
.el-table .el-table__body .ch-40 {
  height: 40px;
  line-height: 40px;
}
.descriptions-label-140 tr {
  display: flex;
}
.descriptions-label-140 .el-descriptions__label.el-descriptions__cell.is-bordered-label {
  width: 140px;
  color: #606266;
  font-weight: 500;
  white-space: normal;
  word-break: break-all;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.descriptions-label-140 .el-descriptions__cell.el-descriptions__content.is-bordered-content {
  flex: 1;
}
.ny-drawer .drawer_title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.ny-drawer .el-drawer__body {
  background: #f0f2f5;
  padding: 12px;
}
.ny-drawer .el-drawer__body .el-card.is-always-shadow {
  box-shadow: none;
  border: none;
}
.ny-drawer .drawer-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}
.el-dialog__title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
}
.drawer .el-drawer__header {
  margin-bottom: 0px;
}
.drawer .drawer_title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.el-dialog.im-custom-dialog {
  padding: 0;
}
.el-dialog.im-custom-dialog .el-dialog__header {
  padding-right: 0;
}
.im-custom-dialog-header {
  height: 56px;
  line-height: 56px;
  box-shadow: inset 0px -1px 0px 0px #f0f0f0;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: bold;
  padding-right: 0;
}
.im-custom-dialog-header .el-dialog__headerbtn {
  height: 56px;
}
.notification-wrap {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.notification-wrap .notification-img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}
.notification-wrap .notification-charat {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: #eee;
  color: #999;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-wrap .notification-title {
  font-size: 16px;
  font-weight: bold;
}
.notification-wrap .notification-content {
  width: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 10px;
}
.notification-wrap .notification-desc {
  color: #999;
}
.notification-wrap .group {
  width: 42px;
  height: 42px;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  padding-left: 0;
  margin: 0;
  /* 3个图片 */
  /* 4个图片 */
  /* 5个或8个图片 */
  /* 6个图片 */
  /* 7个图片 */
}
.notification-wrap .group li {
  list-style: none;
  margin: 0;
  padding: 0;
}
.notification-wrap .group li {
  outline: 1px solid #fff;
  display: flex;
  height: 12px;
}
.notification-wrap .group li .avatar {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin: 1px;
}
.notification-wrap .group li:first-child:nth-last-child(3),
.notification-wrap .group li:first-child:nth-last-child(3) ~ li {
  width: 50%;
  height: 18px;
}
.notification-wrap .group li:first-child:nth-last-child(3) .avatar,
.notification-wrap .group li:first-child:nth-last-child(3) ~ li .avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
  margin: 1px;
}
.notification-wrap .group li:first-child:nth-last-child(3) {
  margin: auto;
}
.notification-wrap .group li:first-child:nth-last-child(3) ~ li {
  float: left;
}
.notification-wrap .group li:first-child:nth-last-child(4),
.notification-wrap .group li:first-child:nth-last-child(4) ~ li {
  width: 50%;
  height: 18px;
  float: left;
}
.notification-wrap .group li:first-child:nth-last-child(4) .avatar,
.notification-wrap .group li:first-child:nth-last-child(4) ~ li .avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
}
.notification-wrap .group li:first-child:nth-last-child(5),
.notification-wrap .group li:first-child:nth-last-child(5) ~ li,
.notification-wrap .group li:first-child:nth-last-child(8),
.notification-wrap .group li:first-child:nth-last-child(8) ~ li {
  width: 33%;
  float: left;
}
.notification-wrap .group li:first-child:nth-last-child(5),
.notification-wrap .group li:first-child:nth-last-child(5) + li {
  margin-top: 16%;
}
.notification-wrap .group li:first-child:nth-last-child(5),
.notification-wrap .group li:first-child:nth-last-child(8) {
  margin-left: 15%;
}
.notification-wrap .group li:first-child:nth-last-child(6),
.notification-wrap .group li:first-child:nth-last-child(6) ~ li {
  width: 33%;
  float: left;
}
.notification-wrap .group li:first-child:nth-last-child(6),
.notification-wrap .group li:first-child:nth-last-child(6) + li,
.notification-wrap .group li:first-child:nth-last-child(6) + li + li {
  margin-top: 16%;
}
.notification-wrap .group li:first-child:nth-last-child(7),
.notification-wrap .group li:first-child:nth-last-child(9),
.notification-wrap .group li:first-child:nth-last-child(7) ~ li,
.notification-wrap .group li:first-child:nth-last-child(9) ~ li {
  width: 33%;
  float: left;
}
.notification-wrap .group li:first-child:nth-last-child(7) {
  float: none;
  margin: auto;
}
.el-dropdown-link:focus,
.el-tooltip__trigger:focus {
  outline: none;
}
.descriptionsDrawer .el-drawer__header {
  margin-bottom: 0px;
}
.descriptionsDrawer .drawer_title {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.descriptionsDrawer .el-drawer__body {
  padding: 0px;
}
.descriptionsDrawer .partBg {
  background-color: #f0f2f5;
  padding: 12px;
}
.descriptionsDrawer .detailcard {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}
.cardDescriptions {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
}
.cardDescriptions .titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
.cardDescriptions .el-descriptions__body {
  display: flex;
  justify-content: space-between;
  border: 1px solid #ebeef5;
  border-bottom: 0;
  border-right: 0;
}
.cardDescriptions .el-descriptions__body tbody {
  display: flex;
  flex-direction: column;
}
.cardDescriptions .el-descriptions__body tbody tr {
  display: flex;
  flex: 1;
}
.cardDescriptions .el-descriptions__body tbody tr .el-descriptions__label {
  display: flex;
  align-items: center;
  font-weight: normal;
  width: 144px;
  border: 0 !important;
  border-right: 1px solid #ebeef5 !important;
  border-bottom: 1px solid #ebeef5 !important;
}
.cardDescriptions .el-descriptions__body tbody tr .el-descriptions__content {
  display: flex;
  align-items: center;
  min-height: 48px;
  flex: 1;
  padding: 12px !important;
  border: 0 !important;
  border-right: 1px solid #ebeef5 !important;
  border-bottom: 1px solid #ebeef5 !important;
}
.cardDescriptions .el-descriptions__body tbody tr .el-descriptions__content > div {
  width: 100%;
}
.cardDescriptions .el-descriptions__body tbody tr .el-descriptions__content .el-form-item__label {
  display: none;
}
.cardDescriptions .el-descriptions__body tbody tr .el-descriptions__content .el-form-item {
  margin-bottom: 0;
}
.cardDescriptions .el-descriptions__body tbody tr .noneSelfRight {
  border-right: 0 !important;
}
.cardDescriptions .el-descriptions__body tbody tr .noneSelfLeft {
  border-left: 0 !important;
}
.cardDescriptions .el-descriptions__body tbody tr .noneSelfLabel {
  background: none;
  border-left: 0 !important;
  border-right: 0 !important;
}
.TableXScrollSty .el-card {
  overflow: visible !important;
}
.TableXScrollSty .el-table {
  overflow: visible !important;
}
.TableXScrollSty .el-table .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 99;
}
.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal {
  display: block !important;
  height: 16px;
  position: fixed;
  bottom: 80px;
  left: 292px;
  z-index: 99;
  opacity: 1;
}
.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal .el-scrollbar__thumb {
  background: #c9cdd4 !important;
  opacity: 1;
}
.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-vertical {
  display: none !important;
}
.TableXScrollSty .tableSort {
  color: #1d2129;
  font-weight: bold;
}
.TableXScrollSty .tableSort .el-dropdown {
  cursor: pointer;
}
.TableXScrollSty .tableSort .el-dropdown .clickValue {
  font-weight: bold;
  color: #1d2129;
}
.TableXScrollSty .tableSort .el-dropdown .el-icon {
  color: #1d2129;
}
.TableXScrollSty .el-popper > span:first-child {
  display: block;
  max-width: 600px;
  max-height: 300px;
  margin-bottom: 5px;
  overflow-y: scroll;
  padding-right: 2px;
}
.TableXScrollSty .el-popper > span:first-child::-webkit-scrollbar {
  width: 6px;
}
.TableXScrollSty .el-popper > span:first-child::-webkit-scrollbar-thumb {
  background-color: hsla(0, 0%, 54.9%);
}
.TableXScrollSty .table-body {
  padding-bottom: 60px;
}
.TableXScrollSty .NYpagination {
  position: fixed;
  bottom: 16px;
  right: 36px;
  background-color: #fff;
  z-index: 99;
  height: 64px;
  padding: 16px;
}
.TableXScrollSty .NYpagination .el-pagination {
  margin-top: 0 !important;
}
.nyTableSearchFormFitable .table-header .header-button-lf {
  width: fit-content;
  max-width: 30%;
}
.nyTableSearchFormFitable .table-header .header-button-lf .ny-button-group {
  margin-right: 0;
}
.nyTableSearchFormFitable .table-header .header-button-ri {
  flex: 1;
  margin-left: 48px;
}
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm {
  display: flex;
  align-items: center;
  width: 100%;
}
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm .el-form-item {
  flex: 1;
}
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm .el-form-item .el-form-item__content {
  width: 100%;
}
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm .el-form-item .el-form-item__content .el-select,
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm .el-form-item .el-form-item__content .el-input {
  max-width: 280px;
  min-width: 100px;
  width: 100% !important;
}
.nyTableSearchFormFitable .table-header .header-button-ri .nyTableSearchForm .el-form-item .el-form-item__content .el-date-editor {
  min-width: 180px;
  width: 100%;
  max-width: 320px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
inpit {
  border: none;
}
