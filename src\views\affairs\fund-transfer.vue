<template>
  <div class="mod-finance__fund-transfer">
    <el-card shadow="never" class="rr-view-ctx-card cardTop ny_form_card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.djbh" placeholder="请输入单据编号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.hm" placeholder="请输入转入人员" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.zczh" placeholder="请输入转出账号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.zfbjyh" placeholder="请输入支付宝交易号" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="state.dataForm.bz" placeholder="请输入用途" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.zt" placeholder="请选择审核状态" clearable style="width: 100%">
                <el-option label="待审核" value="待审核"></el-option>
                <el-option label="同意" value="同意"></el-option>
                <el-option label="拒绝" value="拒绝"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker v-model="createTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange" clearable style="width: 100%"> </el-date-picker>
            </el-col>
            <el-col :span="6">
              <el-button @click="state.getDataList()" type="primary">查询</el-button>
              <el-button @click="getResetting">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>
        <!-- 自定义列内容 -->
        <template #zt="scope">
          <el-tag :type="getStatusTagType(scope.row.zt)" size="small">
            {{ getStatusText(scope.row.zt) }}
          </el-tag>
        </template>

        <template #zrje="scope">
          <span class="amount-text">{{ formatCurrency(scope.row.zrje) }}</span>
        </template>

        <template #cjsj="scope">
          <span>{{ formatTimeStamp(scope.row.cjsj) }}</span>
        </template>

        <template #fj="scope">
          <el-button v-if="scope.row.fj && scope.row.fj.length > 0" type="primary" link size="small" @click="viewAttachment(scope.row.fj)">
            查看附件({{ scope.row.fj.length }})
          </el-button>
          <span v-else>-</span>
        </template>

        <template #operation="scope">
          <el-button type="info" link size="small" @click="viewDetailHandle(scope.row)">详情</el-button>
          <el-button type="primary" link size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="scope.row.zt === '待审核'" type="success" link size="small" @click="approveHandle(scope.row)">审核</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./fund-transfer-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "djrq",
    label: "单据日期",
    width: 100
  },
  {
    prop: "djbh",
    label: "单据编号",
    width: 140
  },
  {
    prop: "hm",
    label: "转入人员",
    width: 100
  },
  {
    prop: "hm01",
    label: "户名",
    width: 120
  },
  {
    prop: "zhlx",
    label: "账户类型",
    width: 100
  },
  {
    prop: "zhyt",
    label: "账户用途",
    width: 120
  },
  {
    prop: "zrje",
    label: "转入金额",
    width: 100
  },
  {
    prop: "bz",
    label: "备注",
    width: 120
  },
  {
    prop: "fj",
    label: "附件",
    width: 80
  },
  {
    prop: "zczh",
    label: "转出账号",
    width: 140
  },
  {
    prop: "hm01_jnpfId",
    label: "户名1",
    width: 100
  },
  {
    prop: "zhlx01",
    label: "账户类型1",
    width: 100
  },
  {
    prop: "zhyt1",
    label: "账户用途1",
    width: 120
  },
  {
    prop: "zfbjyh",
    label: "支付宝交易号",
    width: 140
  },
  {
    prop: "zy",
    label: "摘要",
    width: 120
  },
  {
    prop: "cjsj",
    label: "创建时间",
    width: 160
  },
  {
    prop: "xgsj",
    label: "修改时间",
    width: 160
  },
  {
    prop: "cjry",
    label: "创建人员",
    width: 100
  },
  {
    prop: "xgry",
    label: "修改人员",
    width: 100
  },
  {
    prop: "zt",
    label: "审核状态",
    width: 100
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/finance/transfer/page",
  getDataListIsPage: true,
  exportURL: "/finance/transfer/export",
  deleteURL: "/finance/transfer",
  deleteIsBatch: true,
  dataForm: {
    djbh: "",
    hm: "",
    zczh: "",
    zfbjyh: "",
    bz: "",
    zt: "",
    source: "",
    startCreateDate: "",
    endCreateDate: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();

const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 时间选择变化
const handleDateChange = () => {
  if (createTimeRange.value) {
    state.dataForm.startCreateDate = createTimeRange.value[0] + " 00:00:00";
    state.dataForm.endCreateDate = createTimeRange.value[1] + " 23:59:59";
  } else {
    state.dataForm.startCreateDate = "";
    state.dataForm.endCreateDate = "";
  }
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    "待审核": "warning",
    "同意": "success",
    "拒绝": "danger"
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  return status || "未知";
};



// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 查看详情
const viewDetailHandle = (row: IObject) => {
  const routeParams = {
    path: `/finance/transfer-detail`,
    query: {
      id: row.id,
      _mt: `调拨详情 - ${row.djbh}`
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 审核操作
const approveHandle = (row: IObject) => {
  ElMessageBox.confirm(`确定要审核单据 ${row.djbh} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.put(`/finance/transfer/approve/${row.id}`).then(() => {
      ElMessage.success("审核成功");
      state.getDataList();
    });
  });
};

// 重置操作
const getResetting = () => {
  createTimeRange.value = undefined;
  state.dataForm.djbh = "";
  state.dataForm.hm = "";
  state.dataForm.zczh = "";
  state.dataForm.zfbjyh = "";
  state.dataForm.bz = "";
  state.dataForm.zt = "";
  state.dataForm.startCreateDate = "";
  state.dataForm.endCreateDate = "";
  state.getDataList();
};

// 导出
const exportHandle = () => {
  baseService.get("/finance/transfer/export", state.dataForm).then((res) => {
    if (res) {
      fileExport(res, "资金调拨单列表");
    }
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

// 查看附件
const viewAttachment = (attachments: any[]) => {
  if (attachments && attachments.length > 0) {
    // 这里可以实现附件查看逻辑，比如打开图片预览或下载
    attachments.forEach((attachment: any) => {
      if (attachment.url) {
        window.open(attachment.url, '_blank');
      }
    });
  }
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.cardTop {
  margin-bottom: 20px;
}

.ny-tabs {
  :deep(.el-tabs__header) {
    .el-tabs__item {
      width: 96px;
      padding: 0;
    }
  }
}

.amount-text {
  color: #e6a23c;
  font-weight: 500;
}
</style>
