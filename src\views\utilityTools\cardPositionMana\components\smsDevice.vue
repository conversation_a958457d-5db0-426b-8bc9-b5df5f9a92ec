<template>
  <div style="width: 100%">
    <div class="title">卡板设备管理</div>
    <div class="TableXScrollSty">
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <div style="display: flex; gap: 8px">
            <el-button type="primary" :loading="restartLoading" :disabled="!state.dataListSelections?.length" @click="handleOprate(1)">重启</el-button>
            <el-button color="#409EFF" :loading="flashLightLoading" :disabled="!state.dataListSelections?.length" style="color: #fff" @click="handleOprate(2)">闪灯</el-button>
            <!-- <el-button type="success" @click="handleOprate(3)">远程重启</el-button> -->
          </div></template
        >
        <template #header-right>
          <div style="display: flex; gap: 8px">
            <el-input :prefix-icon="Search" style="width: 240px" v-model="state.dataForm.info" placeholder="请输入手机号/备注" clearable></el-input>
            <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" /> -->
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            <el-button @click="filterHandle">高级筛选</el-button>
          </div>
        </template>
        <template #onlineStatus="{ row }">
          <el-tag type="danger" v-if="row.onlineStatus == 0">离线</el-tag>
          <el-tag type="success" v-else>在线</el-tag>
        </template>
        <template #enableStatus="{ row }">
          <el-button link type="danger" v-if="row.enableStatus == 0">禁用</el-button>
          <el-button link type="success" v-if="row.enableStatus == 1">正常</el-button>
        </template>
        <template #onlineTime="{ row }">
          <span>{{ row.onlineTime ? formatTimeStamp(row.onlineTime) : "-" }}</span>
        </template>
        <template #offlineTime="{ row }">
          <span>{{ row.offlineTime ? formatTimeStamp(row.offlineTime) : "-" }}</span>
        </template>
        <template #createDate="{ row }">
          <span>{{ row.createDate ? formatTimeStamp(row.createDate) : "-" }}</span>
        </template>
        <template #operation="{ row }">
          <el-button type="primary" link @click="addOrUpdateHandle(row)">编辑</el-button>
          <!-- <el-button type="danger" link @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button> -->
        </template>
      </ny-table>
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./update-SmsDevice.vue";
import filterCom from "./filter.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatTimeStamp } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileDevice/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileDevice",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  // {
  //   prop: "id",
  //   label: "ID",
  //   minWidth: 80
  // },
  {
    prop: "boardCode",
    label: "卡板编号",
    minWidth: 136
  },
  {
    prop: "positionCode",
    label: "卡位置",
    minWidth: 136
  },
  {
    prop: "tenantName",
    label: "所属租户",
    minWidth: 136
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 136
  },
  {
    prop: "iccid",
    label: "ICCID",
    minWidth: 136
  },
  // {
  //   prop: "info",
  //   label: "卡信息",
  //   minWidth: 136
  // },
  // {
  //   prop: "versionCode",
  //   label: "版本号",
  //   minWidth: 136
  // },
  {
    prop: "cardSignalName",
    label: "信号",
    minWidth: 136
  },
  {
    prop: "onlineStatus",
    label: "在线状态",
    minWidth: 136
  },
  // {
  //   prop: "deviceType",
  //   label: "设备类型",
  //   minWidth: 136
  // },
  {
    prop: "remark",
    label: "备注",
    minWidth: 136
  },
  {
    prop: "deptName",
    label: "部门",
    minWidth: 136
  },
  // {
  //   prop: "enableStatus",
  //   label: "启用状态",
  //   minWidth: 136
  // },
  {
    prop: "onlineTime",
    label: "上线时间",
    minWidth: 160
  },
  {
    prop: "offlineTime",
    label: "离线时间",
    minWidth: 160
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 160
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: 100
  }
]);

const restartLoading = ref(false); // 重启
const flashLightLoading = ref(false); // 闪灯
const handleOprate = (index: any, row?: any) => {
  if (index == 1) {
    // 重启
    restartLoading.value = true;
    const ids = state.dataListSelections?.map((item: any) => item.id);
    baseService
      .post("/mobile/mobileDevice/restart", ids)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        restartLoading.value = false;
      });
  } else if (index == 2) {
    // 闪灯
    flashLightLoading.value = true;
    const ids = state.dataListSelections?.map((item: any) => item.id);
    baseService
      .post("/mobile/mobileDevice/flashLight", ids)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        flashLightLoading.value = false;
      });
  } else if (index == 3) {
    // 远程重启
  }
};
// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm = { info: "" };
  createDate.value = [];
  filterRef.value.reset();
  state.page = 1;
  state.getDataList();
};

const getParamsInfo = (params: any) => {
  delete params.createDate;
  delete params.onlineTime;
  delete params.offlineTime;
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (row: any) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(row);
  });
};

const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(2);
  });
};
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 503px;
}
</style>
