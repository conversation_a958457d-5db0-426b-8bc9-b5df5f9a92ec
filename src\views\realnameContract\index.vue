<template>
  <div class="contractPage">
    <div class="h5Page">
      <div class="title">
        <el-icon v-if="signStep != 1" @click="backStep()" style="font-size: 24px; margin-left: 16px"><ArrowLeft /></el-icon>
        <span v-else style="font-size: 24px; margin-left: 16px"></span>
        <span style="margin-left: -40px">签署合同</span>
        <span></span>
      </div>
      <div class="content" :class="signStep == 1 ? (signType == 2 ? 'bg1-1' : 'bg1-0') : 'bg2'">
        <el-form v-if="signStep == 1" :model="dataForm" :rules="rules" ref="dataFormRef" label-width="90px" label-position="left">
          <div class="card">
            <div class="cTitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M1 5C1 4.44772 1.44772 4 2 4H22C22.5523 4 23 4.44772 23 5V19C23 19.5523 22.5523 20 22 20H2C1.44772 20 1 19.5523 1 19V5ZM9 8H7V10H9V8ZM7 13V16H9V11H6V13H7ZM13 9V11H19V9H13ZM18 13H13V15H18V13Z" fill="#165DFF" />
              </svg>
              <span>基本信息</span>
              <el-tag color="#E8F3FF" v-if="signType == 2" @click="autoAudit = !autoAudit" style="position: absolute; right: 24px; border-color: #165dff; color: #165dff">{{ !autoAudit ? "我已认证" : "返回认证" }}</el-tag>
            </div>
            <template v-if="signType == 1">
              <div class="formItem">
                <el-form-item prop="username" label="姓名">
                  <el-input style="width: 200px" v-model="dataForm.username" placeholder="请输入姓名"></el-input>
                </el-form-item>
              </div>
              <div class="formItem">
                <el-form-item prop="userPhone" label="手机号码">
                  <el-input style="width: 200px" v-model="dataForm.userPhone" placeholder="请输入手机号码"></el-input>
                </el-form-item>
              </div>
              <div class="formItem">
                <el-form-item prop="userIdNo" label="身份证号">
                  <el-input style="width: 200px" v-model="dataForm.userIdNo" placeholder="请输入身份证号"></el-input>
                </el-form-item>
              </div>
            </template>
            <!-- 企业已认证 -->
            <template v-if="signType == 2 && autoAudit">
              <div class="formItem">
                <el-form-item prop="licenseCode" label="统一社会信用代码">
                  <el-input style="width: 200px" v-model="dataForm.licenseCode" placeholder="请输入统一社会信用代码"></el-input>
                </el-form-item>
              </div>
              <div class="formItem">
                <el-form-item prop="legalPersonIdNo" label="法人身份证">
                  <el-input style="width: 200px" v-model="dataForm.legalPersonIdNo" placeholder="请输入法人身份证"></el-input>
                </el-form-item>
              </div>
            </template>
            <!-- 企业未认证 -->
            <template v-else-if="signType == 2 && !autoAudit">
              <div class="formItem">
                <el-form-item prop="enterpriseName" label="企业名称">
                  <el-input style="width: 200px" v-model="dataForm.enterpriseName" placeholder="请输入企业名称"></el-input>
                </el-form-item>
              </div>
              <div class="formItem">
                <el-form-item prop="licenseCode" label="统一社会信用代码">
                  <el-input style="width: 200px" v-model="dataForm.licenseCode" placeholder="请输入统一社会信用代码"></el-input>
                </el-form-item>
              </div>
              <div class="formItem">
                <el-form-item prop="bestsignMail" label="邮箱">
                  <el-input style="width: 200px" v-model="dataForm.bestsignMail" placeholder="请输入邮箱"></el-input>
                </el-form-item></div
            ></template>
          </div>
          <!-- 企业未认证 -->
          <div class="card" v-if="signType == 2 && !autoAudit">
            <div class="cTitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M3.78307 2.82598L12 1L20.2169 2.82598C20.6745 2.92766 21 3.33347 21 3.80217V13.7889C21 15.795 19.9974 17.6684 18.3282 18.7812L12 23L5.6718 18.7812C4.00261 17.6684 3 15.795 3 13.7889V3.80217C3 3.33347 3.32553 2.92766 3.78307 2.82598ZM12 11C13.3807 11 14.5 9.88071 14.5 8.5C14.5 7.11929 13.3807 6 12 6C10.6193 6 9.5 7.11929 9.5 8.5C9.5 9.88071 10.6193 11 12 11ZM7.52746 16H16.4725C16.2238 13.75 14.3163 12 12 12C9.68372 12 7.77619 13.75 7.52746 16Z"
                  fill="#165DFF"
                />
              </svg>
              <span>法人信息</span>
            </div>
            <div class="formItem">
              <el-form-item prop="legalPersonName" label="法人姓名">
                <el-input style="width: 200px" v-model="dataForm.legalPersonName" placeholder="请输入法人姓名"></el-input>
              </el-form-item>
            </div>
            <div class="formItem">
              <el-form-item prop="legalPersonIdNo" label="法人身份证">
                <el-input style="width: 200px" v-model="dataForm.legalPersonIdNo" placeholder="请输入法人身份证"></el-input>
              </el-form-item>
            </div>
            <div class="formItem">
              <el-form-item prop="legalPersonPhone" label="手机号码">
                <el-input style="width: 200px" v-model="dataForm.legalPersonPhone" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="card" style="display: flex; flex-direction: row; align-items: center">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
              <path
                d="M6.66667 13.6663C2.98477 13.6663 0 10.6815 0 6.99967C0 3.31777 2.98477 0.333008 6.66667 0.333008C10.3485 0.333008 13.3333 3.31777 13.3333 6.99967C13.3333 10.6815 10.3485 13.6663 6.66667 13.6663ZM6 6.33301V10.333H7.33333V6.33301H6ZM6 3.66634V4.99967H7.33333V3.66634H6Z"
                fill="#6AA1FF"
              />
            </svg>
            <span style="color: #86909c; flex: 1">实名仅用于您是否为真人用户，不会对信息做任何采集与保留，请放心使用。</span>
          </div>
        </el-form>
        <img v-if="signStep == 2" style="width: 100%; padding-bottom: 24px" src="https://oss.nyyyds.com/upload/20250617/b07c7ce2e4d241378ad8695951a9eb81.png" alt="" />
        <div v-if="signStep == 3" class="card" style="align-items: center; padding-top: 60px">
          <svg xmlns="http://www.w3.org/2000/svg" width="113" height="112" viewBox="0 0 113 112" fill="none">
            <path
              d="M56.4997 102.666C82.2727 102.666 103.166 81.7727 103.166 55.9997C103.166 30.2264 82.2727 9.33301 56.4997 9.33301C30.7264 9.33301 9.83301 30.2264 9.83301 55.9997C9.83301 81.7727 30.7264 102.666 56.4997 102.666ZM81.9661 44.1329L51.833 74.2659L32.1998 54.6328L38.7995 48.0332L51.833 61.0667L75.3665 37.5332L81.9661 44.1329Z"
              fill="#00C568"
            />
          </svg>
          <span style="color: #1d2129; font-size: 16px; font-weight: 400; line-height: 24px">已完成实名认证</span>
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-width="90px" label-position="left" style="width: 100%">
            <div class="formItem" style="margin-bottom: 12px">
              <el-form-item label="姓名">
                <el-input disabled style="width: 200px" v-model="dataForm.bestsignUserName" placeholder="请输入姓名"></el-input>
              </el-form-item>
            </div>
            <div class="formItem" style="margin-bottom: 12px">
              <el-form-item label="身份证号">
                <el-input disabled style="width: 200px" v-model="dataForm.idNo" placeholder="请输入身份证号"></el-input>
              </el-form-item>
            </div>
            <div class="formItem" style="margin-bottom: 12px">
              <el-form-item label="手机号码">
                <el-input disabled style="width: 200px" v-model="dataForm.bestsignMobile" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <template v-if="signType == 1">
        <div class="bottom" v-if="signStep == 1" @click="submit1" v-loading="btnLoading">
          <div class="btn">刷脸认证</div>
        </div>
        <div class="bottom" v-if="signStep == 2" @click="submit2">
          <div class="btn">开始刷脸拍照</div>
        </div>
        <div class="bottom" v-if="signStep == 3" @click="signContractHandle">
          <div class="btn">开始签署合同</div>
        </div>
      </template>
      <div class="bottom" v-else @click="submitCompany" v-loading="btnLoading">
        <div class="btn">开始签署合同</div>
      </div>
    </div>
    <!-- 加载页 -->
    <div v-if="loadingStatus" class="loaderPage">
      <div>
        <div class="loaderBar"></div>
        <div class="loadText">{{ loadingText }}<span :class="'dot-' + dot" v-for="dot in 3" :key="dot">.</span></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import baseService from "@/service/baseService";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useDebounce } from "@/utils/utils";
const route = useRoute();
const autoAudit = ref(false); // 是否已验证
const signType = ref(1); // 1 个人 2 企业
const signStep = ref(1); //1 信息填写 2 人脸识别 3 签署合同
const loadingStatus = ref(false); // 加载状态
const contractType = ref(""); // 合同类型
const contractId = ref(""); // 合同ID
const orderId = ref(""); // 订单ID
const loadingText = ref("实名认证中"); // 加载状态  正在跳转合同...
const btnLoading = ref(false);

watch(
  () => route.query,
  () => {
    contractType.value = route.query.contractType ? route.query.contractType : "";
    contractId.value = route.query.contractId ? route.query.contractId : "";
    orderId.value = route.query.orderId ? route.query.orderId : "";
    // 刷脸认证回来
    if (route.query.partAId) {
      baseService.get("/bestsign/getInfoByData", { id: route.query.partAId }).then((res) => {
        dataForm.username = res.data.bestsignUserName;
        dataForm.userPhone = res.data.bestsignMobile;
        dataForm.userIdNo = res.data.idNo;
        signType.value = res.data.bestsignUserType ? res.data.bestsignUserType : 1;
        // 查询验证状态
        baseService.get("/bestsign/getFaceAuthResult/" + res.data.orderNo).then((res_) => {
          if (res_.data == 1) {
            // 成功去签署
            baseService.get("/bestsign/queryRegStatus/" + dataForm.userPhone + "/1").then((_res) => {
              signStep.value = 3;
              Object.assign(dataForm, res.data);
            });
          }
        });
      });
      return;
    }
    signStep.value = 1;
    signType.value = route.query.type ? route.query.type : 1;
    console.log(signType.value, contractType.value, contractId.value);
  },
  {
    immediate: true
  }
);
const dataForm = reactive({});
const dataFormRef = ref(); // 表单ref
const rules = reactive({
  bestsignUserType: [{ required: true, message: "请选择身份", trigger: "change" }],
  username: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  userIdNo: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
  userPhone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  legalPersonPhone: [{ required: true, message: "请输入企业法人手机号", trigger: "blur" }],
  legalPersonIdNo: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
  enterpriseName: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
  licenseCode: [{ required: true, message: "请输入统一社会信用代码", trigger: "blur" }],
  bestsignMail: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
  legalPersonName: [{ required: true, message: "请输入企业法人名称", trigger: "blur" }]
});

const backStep = () => {
  signStep.value--;
};
// 个人验证
const submit1 = useDebounce(() => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    // *1. 查询用户是否已实名认证
    let params = {
      bestsignUserName: dataForm.username,
      bestsignMobile: dataForm.userPhone,
      idNo: dataForm.userIdNo
    };
    btnLoading.value = true;
    baseService.get("/bestsign/getInfoByData", { bestsignUserType: signType.value, ...params }).then((res) => {
      // *2. 没有注册， 已注册&未实名
      if (!res.data || res.data.isAudit != 1) {
        delete dataForm.id;
        dataForm.contractId = contractId.value;
        dataForm.orderId = orderId.value;
        dataForm.contractType = contractType.value;
        dataForm.bestsignUserType = signType.value;
        // *3. 企业，个人未注册， 去创建用户
        baseService
          .post("/bestsign/createBestsignPersonalUser", dataForm)
          .then((res2) => {
            signStep.value = 2;
            Object.assign(dataForm, res2.data);
          })
          .finally(() => {
            btnLoading.value = false;
          });
      } else {
        // *2. 已认证， 去签署页面
        btnLoading.value = false;
        signStep.value = 3;
        Object.assign(dataForm, res.data);
        baseService.get("/bestsign/queryRegStatus/" + dataForm.userPhone + "/1");
      }
    });
  });
});
// 企业验证
const submitCompany = useDebounce(() => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    // *1. 查询用户是否已实名认证
    let params = {
      legalPersonIdNo: dataForm.legalPersonIdNo,
      licenseCode: dataForm.licenseCode
    };
    btnLoading.value = true;
    baseService
      .get("/bestsign/getInfoByData", { bestsignUserType: signType.value, ...params })
      .then((res) => {
        // *2. 没有注册
        if (!res.data) {
          if (signType.value == 2 && autoAudit.value) {
            // *3. 勾选已认证但是企业未认证， 去填写信
            ElMessage.warning("未查询到认证信息，请重新认证！");
            autoAudit.value = false;
            return;
          }
          btnLoading.value = true;
          delete dataForm.id;
          dataForm.contractId = contractId.value;
          dataForm.orderId = orderId.value;
          dataForm.contractType = contractType.value;
          dataForm.bestsignUserType = signType.value;
          // *3. 企业未注册， 去创建用户
          baseService
            .post("/bestsign/createBestsignUser", dataForm)
            .then((res2) => {
              if (res.data.isAudit != 1) {
                // *4. 认证不通过
                ElMessage.warning("未通过认证，请重新认证！");
              } else {
                // *4. 已认证， 去签署
                signContractHandle();
                Object.assign(dataForm, res2.data);
              }
            })
            .finally(() => {
              btnLoading.value = false;
            });
        } else if (res.data.isAudit != 1) {
          // *2. 认证不通过
          ElMessage.warning("未通过认证，请重新认证！");
        } else {
          // *2. 已认证， 去签署
          Object.assign(dataForm, res.data);
          baseService.get("/bestsign/queryRegStatus/" + dataForm.bestsignMobile + "/2");
          signContractHandle();
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
});
// 去刷脸
const submit2 = () => {
  window.open(dataForm.url);
};
// 签署合同
const signContractHandle = useDebounce(() => {
  loadingStatus.value = true;
  loadingText.value = "正在跳转合同...";
  baseService
    .post("/bestsign/getContractLink", {
      contractType: contractType.value,
      contractId: contractId.value,
      partyAId: dataForm.id
    })
    .then((res) => {
      loadingStatus.value = false;
      window.open(res.data);
    })
    .catch((res) => {
      ElMessage.warning(res.msg);
    })
    .finally(() => {
      loadingStatus.value = false;
    });
});
</script>
<style lang="less" scoped>
.contractPage {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;

  .h5Page {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100vh;
    max-width: 500px;
    height: 100%;
    background: #f2f3f5;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: var(---10, #000);
      text-align: center;
      font-family: Inter;
      font-size: 18px;
      font-weight: 500;
      height: 60px;
      background: #fff;
      padding: 12px 0px;
    }
    .content {
      width: 100%;
      flex: 1;
      background-size: contain;
      background-repeat: no-repeat;
      background-color: #f2f3f5;
      &.bg1-0 {
        background-image: url(https://oss.nyyyds.com/upload/20250617/115b0b21614d41648a3d5e36ba6ba874.png);
      }
      &.bg1-1 {
        background-image: url(https://oss.nyyyds.com/upload/20250619/3b74979f72ea4ac9bc6059136c9afb7a.webp);
      }
      &.bg2 {
        background-image: url(https://oss.nyyyds.com/upload/20250619/2ff0e79aefd941a79c35e7090f1553f1.webp);
      }

      padding-top: 175px;
      padding-bottom: 25px;

      .card {
        width: calc(100% - 24px);
        box-sizing: border-box;
        display: flex;
        padding: 12px;
        margin: 12px;
        flex-direction: column;
        gap: 12px;
        border-radius: 12px;
        background: #fff;

        .cTitle {
          display: flex;
          align-items: center;
          gap: 4px;
          overflow: hidden;
          color: rgba(48, 49, 51, 1);
          font-family: OPPOSans;
          font-size: 16px;
          font-weight: 400;

          span {
            line-height: 25px;
          }
        }

        .formItem {
          display: flex;
          align-items: center;
          border-radius: 8px;
          padding: 10px 16px;
          background: rgba(240, 242, 245, 1);

          .el-form-item {
            margin-bottom: 0px;
            align-items: center;

            :deep(.el-input__wrapper) {
              box-shadow: none !important;
              background-color: transparent;
            }

            :deep(.el-form-item__label) {
              height: fit-content;
              line-height: 24px;
              padding-right: 0;
            }
          }
        }
      }
    }
    .bottom {
      display: flex;
      padding: 12px 16px 24px 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      background-color: #fff;

      .btn {
        cursor: pointer;
        display: flex;
        width: 100%;
        padding: 13px 24px;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        background: #165dff;
        color: #fff;
        font-family: Inter;
        font-size: 16px;
        font-weight: 400;
      }
    }
  }
}
.loaderPage {
  width: 100%;
  height: 100vh;
  background: #f9f9f9;
  position: absolute;
  top: 0;

  > div {
    position: absolute;
    width: fit-content;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .loadText {
    font-weight: 400;
    font-size: 16px;
    color: #2b2d3e;
    line-height: 23px;
    margin-top: 20px;

    span {
      font-size: 30px;
      animation: blink 1.5s infinite;

      &.dot-1 {
        animation-delay: 0s;
      }
      &.dot-2 {
        animation-delay: 0.3s;
      }
      &.dot-3 {
        animation-delay: 0.6s;
      }
    }
  }

  .loaderBar {
    width: calc(160px / 0.707);
    height: 10px;
    background: #f9f9f9;
    border-radius: 10px;
    border: 1px solid #006dfe;
    position: relative;
    overflow: hidden;
  }

  .loaderBar::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-radius: 5px;
    background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
    animation: fillProgress 6s ease-in-out infinite, lightEffect 1s infinite linear;
    animation-fill-mode: forwards;
  }
}

@keyframes blink {
  0%,
  20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes fillProgress {
  0% {
    width: 0;
  }

  33% {
    width: 33.333%;
  }

  66% {
    width: 66.67%;
  }

  100% {
    width: 100%;
  }
}
@keyframes lightEffect {
  0%,
  20%,
  40%,
  60%,
  80%,
  100% {
    background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px) right/200% 100%;
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    background: repeating-linear-gradient(45deg, #0031f2 0 30px, #006dfe 0 40px, rgba(255, 255, 255, 0.3) 0 40px) right/200% 100%;
  }
}
</style>
