<template>
  <div class="ny_dropdown_menu_page">
    <el-dropdown @command="handleCommand" @visible-change="handleDropdownVisibleChange" :hide-on-click="false" :popper-options="{ modifiers: [{ name: 'computeStyles', options: { adaptive: false } }] }">
      <div :class="isBorder ? 'ny_dropdown_menu_border' : 'ny_dropdown_menu' " :style="{ width: width ? width + 'px' : isBorder ? '140px' : '', cursor: 'pointer' }">
        <div v-if="modelValue" class="clickValue">{{ labelKeyOrValue(list, modelValue) }}</div>
        <div v-else class="placeholder" :style="{ color: placeholderColor}">{{ placeholder || "请选择" }}</div>
        <el-icon color="#A8ABB2" style="cursor: pointer" @click="clear" v-if="modelValue && clearable"><CircleClose /></el-icon>
        <el-icon color="#A8ABB2" v-if="!isSolid && (!modelValue || !clearable)"><arrow-down/></el-icon>
        <el-icon color="#303133" v-if="isSolid && (!modelValue || !clearable)"><CaretTop v-if="isTop"/><CaretBottom v-else/></el-icon>
      </div>
      <template #dropdown>
        <el-scrollbar max-height="300px">
          <el-dropdown-menu :style="{ width: width ? width + 'px' : '140px' }">
            <el-dropdown-item v-for="(item, index) in list" :key="index" :command="item[valueKey]">{{ item[labelKey] }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-scrollbar>
      </template>
    </el-dropdown>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, PropType, onMounted, watch } from "vue";
import { IObject } from "@/types/interface";

export default defineComponent({
  name: "NyDropdownMenu",
  props: {
    // 绑定赋值
    modelValue: [Number, String],
    // 下拉label显示的key
    labelKey: { type: String, required: true },
    // 下拉绑定值得key
    valueKey: { type: String, required: true },
    //  选项列表
    list: Array as PropType<IObject[]>,
    //  提示语
    placeholder: String,
    // 自定义宽度
    width: [Number, String],
    // 是否带边框
    isBorder: {
      type: Boolean,
      default: true
    },
    // 是否实心icon
    isSolid: {
      type: Boolean,
      default: false
    },
    // 是否向上
    isTop: {
      type: Boolean,
      default: false
    },
    // 是否清空
    clearable: {
      type: Boolean,
      default: false
    },
    // 
    placeholderColor:{
      type: String,
      default: "#a8abb2"
    }
  },
  setup(props, { emit }) {
    const labelKeyOrValue = (list: any, value: any) => {
      if (value) {
        return list.find((item: any) => item[props.valueKey] == value)[props.labelKey];
      }
    };
    const handleCommand = (e: any) => {
      emit("update:modelValue", e);
      emit("change")
    };
    const handleDropdownVisibleChange = (visible: boolean) => {
      if (!visible) {
        // 强制移除可能残留的焦点
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement?.classList?.contains("el-dropdown-menu__item")) {
          activeElement.blur();
        }
      }
    };
    // 清空回调
    const clear = () => {
      emit("update:modelValue", null);
      emit("clear");
      emit("change")
    };
    return {
      labelKeyOrValue,
      handleCommand,
      handleDropdownVisibleChange,
      clear
    };
  }
});
</script>

<style lang="less" scoped>
.ny_dropdown_menu_border {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 140px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #dcdfe6;
  padding: 4px 8px;
  .clickValue {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    line-height: 22px;
  }
  .placeholder {
    font-weight: 400;
    font-size: 14px;
    color: #a8abb2;
    line-height: 22px;
  }
}
.ny_dropdown_menu {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // min-width: 140px;
  border-radius: 4px 4px 4px 4px;
  padding: 4px 0px;
  .clickValue {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    line-height: 22px;
    margin-right: 8px;
  }
  .placeholder {
    font-weight: 400;
    font-size: 14px;
    // color: #a8abb2;
    line-height: 22px;
  }
}

.ny_dropdown_menu_page + .ny_dropdown_menu_page {
  margin-left: 12px;
}
</style>
