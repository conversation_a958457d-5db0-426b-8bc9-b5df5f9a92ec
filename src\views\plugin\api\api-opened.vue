<template>
  <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" width="1018px" class="api-opened">
    <template #header="{ close }">
      <div class="my-header flx-justify-between">
        <div class="title bold">{{ (currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0 ? "试用API" : "开通API" }}</div>
        <div class="close pointer" @click="visible = false">
          <el-icon size="16"><Close /></el-icon>
        </div>
      </div>
    </template>

    <div class="dialog-content flx">
      <el-scrollbar class="left flx-1">
        <div class="p-title-line mb-8">选择API</div>
        <div class="api_item_top">
          <span class="api_name">{{ currentRow.companyName }}</span>
          <div class="api_tab">
            <template v-if="currentRow.mainPartnerSetting">
              <span v-if="currentRow.mainPartnerSetting.apiType == 0">仅接收</span>
              <span v-else-if="currentRow.mainPartnerSetting.apiType == 1">仅推送</span>
              <span v-else-if="currentRow.mainPartnerSetting.apiType == 2">互推</span>
              <span v-else-if="currentRow.mainPartnerSetting.apiType == 3">未对接</span>
            </template>
            <span v-else>未对接</span>
          </div>
        </div>
        <div class="tip">
          <el-icon color="#E6A23C" size="16" style="margin-right: 5px"><WarnTriangleFilled /></el-icon>
          <span>支付前需提前联系对接合作商办理入驻并拿到对接密钥，联系技术进行对接后，并开通相关服务。</span>
        </div>
        <div class="p-title-line mb-8">收费方式</div>
        <div class="item-card-list mb-24">
          <div class="item-card flx-column flx-center active">
            <el-text class="item-name bold" type="primary">包月</el-text>
            <div class="font-size-small">
              <el-text type="danger">{{ currentRow.openState == 0 || currentRow.openState == null || currentRow.openState == 3 ? currentRow.feeStandards : currentRow.renew }}星币</el-text> /
              {{ currentRow.openState == 0 || currentRow.openState == null || currentRow.openState == 3 ? "首月" : "每月" }}
            </div>
          </div>
        </div>
        <div class="p-title-line">购买数量</div>
        <div class="item-card-list flx flx-nowrap">
          <template v-if="(currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0">
            <div class="item-card flx-column flx-center" v-for="(item, index) in trialMonthList" :key="index" :class="{ active: item.value == dataForm.trialMonth }" @click="dataForm.trialMonth = item.value">
              <el-text class="item-name bold" :type="item.value == dataForm.trialMonth ? 'primary' : ''">
                {{ item.label }}
              </el-text>
            </div>
          </template>
          <template v-else>
            <div class="item-card flx-column flx-center" v-for="(item, index) in currentRow.expireMonth" :key="index" :class="{ active: item.value == dataForm.openingDuration }" @click="dataForm.openingDuration = item.value">
              <el-text class="item-name bold" :type="item.value == dataForm.openingDuration ? 'primary' : ''">
                {{ item.label }}
              </el-text>
            </div>
          </template>
        </div>
      </el-scrollbar>
      <div class="right">
        <div class="p-title-line mb-8">订单详情</div>
        <div class="info-card mb-12">
          <div class="info-item">
            <div class="label">订单类型：</div>
            <div class="value">消费</div>
          </div>
          <div class="info-item">
            <div class="label">开通方式：</div>
            <div class="value">包月</div>
          </div>
          <div class="info-item">
            <div class="label">开通数量：</div>
            <div class="value">{{ (currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0 ? dataForm.trialMonth : dataForm.openingDuration }}个月</div>
          </div>
        </div>
        <div class="info-card flx-justify-between mb-12">
          <div class="info-item">
            <div class="label">账户余额：</div>
            <el-text type="danger" class="bold">{{ BigNumber(balanceVlaue) }}星币</el-text>
            <div class="label flx-center ml-25" v-if="insufficientBalance">
              <el-icon size="14" class="mr-5 ml-10"><Warning /></el-icon>余额不足
            </div>
          </div>
          <el-button type="primary" @click="toBillBalance">充值</el-button>
        </div>

        <div class="info-card mb-12">
          <div class="flx-justify-between">
            <div class="info-item">
              <div class="label">订单金额：</div>
              <div v-if="(currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0"></div>
              <div class="label flx-align-center" v-else>
                <el-tooltip placement="top">
                  <template #content>
                    <span v-if="currentRow.openState == 0 || currentRow.openState == null">
                      首月({{ currentRow.feeStandards }}) * 数量(1) + 单价({{ currentRow.renew }}) * 数量({{ Number(dataForm.openingDuration) - 1 }}) = {{ amount }}星币
                      <br />
                      总计：{{ amount }}星币
                    </span>
                    <span v-else>
                      单价({{ currentRow.renew }}) * 数量({{ dataForm.openingDuration }}) = {{ amount }}星币
                      <br />
                      总计：{{ amount }}星币
                    </span>
                  </template>
                  <el-icon size="14" class="mr-5 ml-10"><Warning /></el-icon>
                </el-tooltip>
                明细
              </div>
            </div>
            <el-text type="danger" class="bold" v-if="(currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0">免费</el-text>

            <el-text type="danger" class="bold" style="font-size: 20px" v-else> {{ amount }}星币 </el-text>
          </div>
          <div class="info-item">
            <div class="label">付款成功后，可在已开通功能中查看</div>
          </div>
        </div>
        <el-button class="open-btn" :loading="btnLoading" @click="handleSubmit">{{ (currentRow.openState == 0 || currentRow.openState == null) && currentRow.trialMonth > 0 ? "立即试用" : "确认开通" }}</el-button>
      </div>
    </div>
  </el-dialog>
  <!-- 充值 -->
  <el-dialog v-model="rechargedrawer" width="480" title="充值" :close-on-click-modal="false" :close-on-press-escape="false" @close="rechargedrawer = false">
    <el-descriptions class="descriptions" title="" :column="1" size="default" border>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>支付宝账号</div> </template>
        <EMAIL>
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>支付宝账户主体</div> </template>
        枣庄努运企业管理有限公司
      </el-descriptions-item>
    </el-descriptions>
    <el-form :model="surplusDataForm" :rules="rules" ref="dataFormRef" label-position="top">
      <el-form-item label="支付宝订单号" prop="orderNum">
        <template #label>
          <span style="margin-right: 10px">支付宝订单号</span>
          <el-text type="primary" @click="surplusDataForm.showImagePreview = true">如何查看订单号？</el-text>
        </template>
        <el-input v-model="surplusDataForm.orderNum" placeholder="请输入支付宝订单号"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="rechargedrawer = false">取消</el-button>
        <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
      </div>
    </template>
    <el-image-viewer v-if="surplusDataForm.showImagePreview" :url-list="['https://oss.nyyyds.com/upload/20250509/c2737ad597474204b6b78b255d63fc25.png']" hide-on-click-modal teleported @close="closePreview" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits, watch, reactive } from "vue";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { BigNumber } from "bignumber.js";
import { useAppStore } from "@/store";
import baseService from "@/service/baseService";
import { curryRight } from "lodash";
import { useRouter } from "vue-router";

const router = useRouter();
const store = useAppStore();
const emits = defineEmits(["refresh"]);
const visible = ref(false);

const currentRow = ref({} as IObject);

// 试用时长
const trialMonthList = ref([] as IObject);

// 是否余额不足
const insufficientBalance = ref(false);

// 账户余额
const balanceVlaue = ref(0);

// 总价
const amount = ref(<any>0);

const rechargedrawer = ref(false); // 充值

const dataForm = ref({
  // 开通时长
  openingDuration: "",
  trialMonth: null
});

// 计算总价
watch(
  () => dataForm.value.openingDuration,
  (newVal) => {
    if (currentRow.value.openState == null || currentRow.value.openState == 0 || currentRow.value.openState == 3 || currentRow.value.openState == 4) {
      amount.value = BigNumber(currentRow.value.feeStandards).plus(BigNumber(currentRow.value.renew).multipliedBy(BigNumber(dataForm.value.openingDuration).minus(1)));
    } else if (currentRow.value.openState == 1 || currentRow.value.openState == 2 || currentRow.value.openState == 5) {
      amount.value = BigNumber(currentRow.value.renew).multipliedBy(BigNumber(dataForm.value.openingDuration));
    }
    insufficientBalance.value = balanceVlaue.value < amount.value;
    console.log(dataForm.value.openingDuration, insufficientBalance.value, amount.value);
  }
);

// 开通类型 1开通 2续费   0试用
const apiOpenType = ref("");

const init = (row: IObject, type: any) => {
  apiOpenType.value = type;
  console.log(type);
  getBalance();
  console.log(row);
  currentRow.value = { ...row, expireMonth: row.expireMonth ? row.expireMonth.split(",") : [] };
  let arr: any = [];
  currentRow.value.expireMonth.map((item: any) => {
    arr.push({ label: item + "个月", value: item });
  });
  currentRow.value.expireMonth = arr;
  dataForm.value.openingDuration = currentRow.value.expireMonth[0].value;

  dataForm.value.trialMonth = currentRow.value.trialMonth ? currentRow.value.trialMonth.toString() : "1";
  visible.value = true;

  if (dataForm.value.trialMonth) {
    trialMonthList.value = [{ label: dataForm.value.trialMonth + "个月", value: dataForm.value.trialMonth }];
  }
};

// 获取账户余额
const getBalance = () => {
  baseService.get("/wallet/bill/balance").then((res) => {
    balanceVlaue.value = res.data;
    insufficientBalance.value = balanceVlaue.value < amount.value;
  });
};

// 开通
const btnLoading = ref(false);
const handleSubmit = async () => {
  btnLoading.value = true;
  try {
    let data = {
      id: currentRow.value.id,
      months: dataForm.value.openingDuration,
      companyType: currentRow.value.companyType,
      openType: apiOpenType.value
    };
    const res = await baseService.post("/partner/partner/openDockingPort", data);
    btnLoading.value = false;
    if (res.code == 0) {
      visible.value = false;
      ElMessage.success("开通成功");
      emits("refresh");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    btnLoading.value = false;
  }
};

// 余额表单变量
const surplusDataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: surplusDataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};

const closePreview = () => {
  surplusDataForm.showImagePreview = false;
};

// 打开充值页面
const toBillBalance = () => {
  // rechargedrawer.value = true;
  // surplusDataForm.orderNum = '';
  router.push("/bill/specification?open=1");
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
.api_item_top {
  padding: 12px;
  background: #f1f6ff;
  background-image: url("../../../assets/images/api_bg.png");
  background-repeat: no-repeat;
  background-position: right 0 bottom 0;
  background-size: 145px 78px;
  border-radius: 8px;
  .api_name {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .api_tab {
    margin-top: 10px;
    span {
      background: #ffffff;
      padding: 4px;
      border-radius: 2px;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    span + span {
      margin-left: 8px;
    }
  }
}
.tip {
  background: #fcf6ec;
  border-radius: 8px 8px 8px 8px;
  padding: 4px 16px;
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #e6a23c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  margin-top: 11px;
  margin-bottom: 27px;
}
</style>
<style lang="less">
.api-opened {
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  height: 485px;

  .my-header {
    height: 66px;
    background: linear-gradient(90deg, #eddbc9 0%, #c59d85 100%);
    padding-left: 20px;
    font-size: 18px;
    .close {
      padding: 20px;
    }
  }

  .el-dialog__header {
    padding: 0;
  }

  .dialog-content {
    padding: 20px;

    // .left,
    .right {
      width: calc(40% - 20px);
    }

    .left {
      width: calc(60% - 20px);
      padding-right: 20px;
      border-right: 1px solid #e4e7ed;
      height: 382px;
    }

    .p-title-line {
      color: #606266;
      display: flex;
      line-height: 20px;

      &::before {
        content: "";
        display: inline-block;
        width: 2px;
        height: 20px;
        background: var(--el-color-primary);
        margin-right: 8px;
      }
    }

    .info-card {
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      padding: 12px;

      .fun-name {
        font-size: 16px;
        line-height: 22px;
      }
      .desc {
        color: #a8abb2;
        font-size: 12px;
        line-height: 15px;
        padding-top: 4px;
        height: 30px;
      }
      .tag-wrap {
        padding-top: 12px;
      }

      .detail {
        padding-top: 8px;

        .detail-img {
          padding-top: 8px;

          .img {
            width: 216px;
            height: 138px;
            border-radius: 4px;
            margin-right: 8px;
          }
        }
      }
    }

    .item-card-list {
      margin-left: -10px;
    }
    .item-card {
      margin: 8px 0 0 10px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      width: 130px;
      height: 64px;
      cursor: pointer;
      position: relative;

      &.active {
        border: 1px solid var(--el-color-primary);
      }

      .discount {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        color: #5f4139;
        background: #ffecbb;
        border: 1px solid #ffdd8c;
        border-radius: 0px 4px 0px 4px;
      }

      .item-name {
        line-height: 18px;
      }
    }

    .info-item {
      line-height: 24px;
      display: flex;
      padding: 4px 0;
      font-size: 13px;

      .label {
        color: #909399;
      }

      .value {
        flex: 1;
      }
    }

    .right {
      padding-left: 20px;

      .open-btn {
        background: #ffecbb;
        border: 1px solid #ffdd8c;
        border-radius: 8px;
        width: 100%;
        height: 48px;
        font-size: 16px;
        color: #5f4139;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
}
</style>
