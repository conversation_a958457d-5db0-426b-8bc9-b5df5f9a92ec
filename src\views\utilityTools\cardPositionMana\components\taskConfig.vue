<template>
  <div style="width: 100%">
    <div class="title">任务配置</div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <div style="display: flex; gap: 8px">
          <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        </div>
      </template>
      <template #header-right>
        <div style="display: flex; gap: 8px">
          <el-select style="width: 144px" v-model="state.dataForm.type" placeholder="请选择任务类型" clearable>
            <el-option label="绑定数量查询" value="1" />
            <el-option label="自动查话费" value="2" />
            <el-option label="自动互拨电话" value="3" />
          </el-select>
          <el-select style="width: 144px" v-model="state.dataForm.status" placeholder="状态" clearable>
            <el-option label="禁用" value="0"></el-option>
            <el-option label="启用" value="1"></el-option>
          </el-select>
          <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </template>
      <template #type="{ row }">
        <span v-if="row.type == 1">绑定数量查询</span>
        <span v-if="row.type == 2">自动查话费</span>
        <span v-if="row.type == 3">自动互拨电话</span>
      </template>
      <template #status="{ row }">
        <el-tag type="danger" v-if="row.status == 0">禁用</el-tag>
        <el-tag type="success" v-else>启用</el-tag>
      </template>
      <template #executeTime="{ row }">
        <span>{{ row.executeTime ? formatDate(row.executeTime, "YYYY-MM-DD") : "-" }}</span>
      </template>
      <template #nextExecuteTime="{ row }">
        <span>{{ row.nextExecuteTime ? formatDate(row.nextExecuteTime, "YYYY-MM-DD") : "-" }}</span>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" link @click="addOrUpdateHandle(row.id)">编辑</el-button>
        <el-button type="primary" link @click="">同步</el-button>
        <el-button type="danger" link @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
      </template>
    </ny-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>
      
      <script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./add-update-taskconfig.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileTaskConfig/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileTaskConfig",
  deleteIsBatch: true,
  dataForm: {
    type: "",
    status: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "type",
    label: "任务类型",
    minWidth: 160
  },
  {
    prop: "status",
    label: "状态",
    sortable: "custom",
    minWidth: 160
  },
  {
    prop: "intervalDay",
    label: "间隔周期(天)",
    minWidth: 200
  },
  {
    prop: "executeTime",
    label: "执行时间",
    minWidth: 200
  },
  {
    prop: "nextExecuteTime",
    label: "下次执行时间",
    minWidth: 200
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: 200
  }
]);
// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm.status = "";
  state.dataForm.type = "";
  createDate.value = [];
  state.page = 1;
  state.getDataList();
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
      
      <style lang='less' scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
</style>