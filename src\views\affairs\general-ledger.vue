<template>
  <div class="mod-finance__receivable-ledger">
    <el-card shadow="never" class="rr-view-ctx-card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.relationFormField102" placeholder="结算往来单位" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.bz" placeholder="备注" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" :offset="18">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <ny-table
        :state="state"
        :columns="columns"
        :pagination="true"
        @selectionChange="state.dataListSelectionChangeHandle"
        @pageSizeChange="state.pageSizeChangeHandle"
        @pageCurrentChange="state.pageCurrentChangeHandle">

        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <template #relationFormField102="{ row }">
          <el-link type="primary" @click="viewDetailHandle(row)">{{ row.relationFormField102 }}</el-link>
        </template>

        <template #qcye="{ row }">
          <span class="amount-text">{{ formatCurrency(row.qcye) }}</span>
        </template>

        <template #bqys="{ row }">
          <span class="amount-text">{{ formatCurrency(row.bqys) }}</span>
        </template>

        <template #bqys01="{ row }">
          <span class="received-amount">{{ formatCurrency(row.bqys01) }}</span>
        </template>

        <template #qmye="{ row }">
          <span class="remaining-amount">{{ formatCurrency(row.qmye) }}</span>
        </template>

        <template #cjsj="{ row }">
          {{ formatDateTime(row.cjsj) }}
        </template>

        <template #xgsj="{ row }">
          {{ formatDateTime(row.xgsj) }}
        </template>

        <template #operation="{ row }">
          <el-button type="info" text bg @click="addOrUpdateHandle(row.id, true)">详情</el-button>
          <el-button type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
          <el-button type="success" text bg @click="receiveHandle(row)">收款</el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 / 详情 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 收款弹窗 -->
    <receive-payment :key="receiveKey" ref="receivePaymentRef" @refreshDataList="state.getDataList"></receive-payment>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./receivable-ledger-add-or-update.vue";
// import ReceivePayment from "./receivable-payment.vue";
// import ExcelImport from "./receivable-ledger-import.vue";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { registerDynamicToRouterAndNext } from "@/router";

const { t } = useI18n();

const view = reactive({
  getDataListURL: "/finance/receivable/page",
  getDataListIsPage: true,
  exportURL: "/finance/receivable/export",
  deleteURL: "/finance/receivable",
  deleteIsBatch: true,
  dataForm: {
    relationFormField102: "",
    bz: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "relationFormField102",
    label: "结算往来单位",
    minWidth: 150
  },
  {
    prop: "qcye",
    label: "期初余额",
    width: 120
  },
  {
    prop: "bqyf",
    label: "本期应付",
    width: 120
  },
  {
    prop: "bqyf001",
    label: "本期已付",
    width: 120
  },
  {
    prop: "qmye",
    label: "期末余额",
    width: 120
  },
  {
    prop: "bz",
    label: "备注",
    minWidth: 150
  },
  {
    prop: "cjsj",
    label: "创建时间",
    width: 160
  },
  {
    prop: "xgsj",
    label: "修改时间",
    width: 160
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 220
  }
]);

// 格式化金额
const formatCurrency = (amount: number | string) => {
  if (!amount && amount !== 0) return "0.00";
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(num)) return "0.00";
  return num.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 格式化日期时间
const formatDateTime = (datetime: string | number) => {
  if (!datetime) return "";
  try {
    const date = typeof datetime === 'number' ? new Date(datetime) : new Date(datetime);
    if (isNaN(date.getTime())) return "";
    return date.toLocaleString("zh-CN");
  } catch (error) {
    return "";
  }
};

// 查看详情 - 跳转到详情页面（需要确保路由存在）
const viewDetailHandle = (row: IObject) => {
  try {
    // 方案1：使用 router.push 跳转
    // import { useRouter } from 'vue-router';
    // const router = useRouter();
    // router.push({
    //   path: '/affairs/receivable-detail',
    //   query: {
    //     id: row.id,
    //     relationFormField102: row.relationFormField102
    //   }
    // });

    // 方案2：直接在当前页面打开详情弹窗（推荐）
    addOrUpdateHandle(row.id, true);
  } catch (error) {
    console.error('查看详情失败:', error);
    ElMessage.error('查看详情失败');
  }
};

// 导入
const importRef = ref();
const importHandle = () => {
  if (importRef.value && importRef.value.init) {
    importRef.value.init();
  }
};

// 导出
const exportHandle = () => {
  baseService.get("/finance/receivable/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "应收总账列表");
    }
  }).catch((error) => {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  });
};

// 重置操作
const getResetting = () => {
  state.dataForm.relationFormField102 = "";
  state.dataForm.bz = "";
  state.getDataList();
};

// 新增/编辑/详情
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number, isView?: boolean) => {
  addKey.value++;
  nextTick(() => {
    if (addOrUpdateRef.value && addOrUpdateRef.value.init) {
      addOrUpdateRef.value.init(id, isView);
    }
  });
};

// 收款处理
const receiveKey = ref(0);
const receivePaymentRef = ref();
const receiveHandle = (row: IObject) => {
  receiveKey.value++;
  nextTick(() => {
    if (receivePaymentRef.value && receivePaymentRef.value.init) {
      receivePaymentRef.value.init(row);
    }
  });
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.amount-text {
  color: #e6a23c;
  font-weight: 500;
}

.received-amount {
  color: #67c23a;
  font-weight: 500;
}

.remaining-amount {
  color: #f56c6c;
  font-weight: 500;
}
</style>




