<template>
  <div class="mod-game__attribute TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab :list="gamesList" v-model="view.dataForm.gameId" value="id" label="title" @change="flodTabChange"></ny-flod-tab>
      <ny-table cellHeight="ch-40" :state="state" :columns="columns" :pagination="false" treeChildren="false" @selectionChange="state.dataListSelectionChangeHandle" @dragSort="dragSortHandle" :selectable="checkSelectable">
        <template #header v-if="currentGame.official !== 1">
          <el-button v-if="state.hasPermission('game:attribute:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button v-if="state.hasPermission('game:attribute:delete')" type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
          <el-button type="info" @click="exportNewHandle()">导出最新属性</el-button>
        </template>
        <template #header-right>
          <ny-select v-model="state.dataForm.type" dict-type="game_properties_type" style="width: 200px" placeholder="类型"></ny-select>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
        <template #gameId="{ row }">
          {{ gamesOrLabel(row.gameId) }}
        </template>
        <template #type="{ row }">
          {{ state.getDictLabel("game_properties_type", row.type) }}
        </template>
        <template #isSection="{ row }">
          <el-switch v-model="row.isSection" :active-value="1" :inactive-value="0" :disabled="row.type != 3" :loading="row.loading" @click="switchChange(row)" v-if="row.type == 3"/>
        </template>
        <template #isShow="{ row }">
            <!-- 2025年4月21日17:38:24 改为正常数值判断 且商城和APP的属性由后台处理筛选返回 【jzj、ys】-->
          <el-switch v-model="row.isShow" :active-value="1" :inactive-value="0" :loading="row.loading" @click="switchChange(row)" />
        </template>
        <template #isTitle="{ row }">
          <el-switch v-model="row.isTitle" :active-value="0" :inactive-value="1" :loading="row.loading" @click="switchChange(row)" />
        </template>
        <template #operation="{ row }">
          <el-button v-if="state.hasPermission('game:attribute:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
          <template v-if="currentGame.official !== 1">
            <el-button v-if="state.hasPermission('game:attribute:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
          </template>
          <el-button v-if="state.hasPermission('game:sysgame:changeSort')" class="move" type="primary" text bg>
            <el-icon class="text-primary">
              <Rank />
            </el-icon>
          </el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./attribute-add-or-update.vue";
import ExcelImport from "./attribute-import.vue";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/game/attribute/page",
  getDataListIsPage: false,
  exportURL: "/game/attribute/export",
  deleteURL: "/game/attribute",
  deleteIsBatch: true,
  dataForm: {
    type: "",
    gameId: ""
  }
});

const gamesList = ref(<any>[]);

const state = reactive({ ...useView(view), ...toRefs(view) });

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "gameId",
    label: "游戏名称",
    minWidth: "100"
  },
  {
    prop: "name",
    label: "游戏属性",
    minWidth: "100"
  },
  {
    prop: "type",
    label: "类型",
    minWidth: "60"
  },
  {
    prop: "detailed",
    label: "属性明细",
    minWidth: "280"
  },
  {
    prop: "isSection",
    label: "区间搜索",
    minWidth: "50"
  },
  {
    prop: "isShow",
    label: "商城显示",
    minWidth: "50"
  },
  {
    prop: "isTitle",
    label: "标题生成",
    minWidth: "50"
  },
  {
    prop: "sort",
    label: "排序",
    minWidth: "50"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "100"
  }
]);

// 表格选择处理
const checkSelectable = (row:any) =>{
  if(row.isFixedAttr == 1){
    return false
  } else {
    return true
  }
}
const currentGame = ref({});
// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    view.dataForm.gameId = res.data ? res.data[0].id : "";
    currentGame.value = res.data ? res.data[0] : {};
    gamesList.value = res.data;
    setTimeout(() => {
      state.getDataList();
    }, 300);
  });
};

const flodTabChange = (value: any) => {
  view.dataForm.gameId = value;
  currentGame.value = gamesList.value.find((ele) => ele.id == value);
  state.getDataList();
};

// 根据ID获取游戏名称
const gamesOrLabel = (id: any) => {
  let name = gamesList.value.filter((item: any) => item.id == id);
  return name.length > 0 ? name[0].title : "";
};

// 排序
const dragSortHandle = async ({ newIndex, oldIndex }: { newIndex: number; oldIndex: number }) => {
  console.log(newIndex, oldIndex);
  console.log(state.dataList);
  // targetId 移动到目标游戏id
  // sourceId 选择移动游戏id

  let res = await baseService.post("/game/attribute/editSort", { sourceId: state.dataList[oldIndex].id, targetId: state.dataList[newIndex].id });
  if (res.code == 0) {
    ElMessage.success("排序成功");
  }
  state.getDataList();
  state.dataList?.sort((a, b) => a.sort - b.sort);
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init(view.dataForm.gameId);
};

// 开关保存
const switchChange = (row: any) => {
  if (row.id) {
    row.loading = true;
    baseService
      .put("/game/attribute", row)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
      })
      .finally(() => {
        row.loading = false;
      });
  }
};

// 导出
const exportHandle = () => {
  baseService.get("/game/attribute/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "游戏属性列表");
    }
  });
};

// 导出最新属性
const exportNewHandle = () => {
  baseService.get("/game/attribute/exportNew", { gameId: view.dataForm.gameId }).then((res) => {
    if (res) {
      fileExport(res, "最新游戏属性列表");
    }
  });
};

// 重置操作
const getResetting = () => {
  state.dataForm.type = "";
  state.getDataList();
};

onMounted(() => {
  getGamesList();
});

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, view.dataForm.gameId);
  });
};
</script>
