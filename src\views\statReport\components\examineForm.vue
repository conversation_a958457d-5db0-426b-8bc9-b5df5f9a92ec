<template>
  <el-drawer v-model="visible" size="50%" class="drawer_shop">
    <template #header>
      <div class="drawer_title">数据审批</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="examine_page">
        <div class="card">
          <div class="titleSty">数据信息</div>
          <el-table :data="tableData" style="width: 100%" border>
            <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="60" />
            <el-table-column v-for="(item, index) in tableHeader" :prop="item.dynamicKey" :label="item.name" align="center" :width="item.name.length < 10 ? 200 : item.name.length * 20" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(row[`${item.dynamicKey}`])">
                  <el-image style="width: 36px; height: 36px; border-radius: 4px" :src="row[`${item.dynamicKey}`]" :preview-src-list="[row[`${item.dynamicKey}`]]" preview-teleported fit="contain" />
                </span>
                <span v-else>{{ row[`${item.dynamicKey}`] }}</span>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
        </div>
        <div class="card" style="margin-top: 12px" v-if="!viewShow">
          <div class="titleSty">审批操作</div>
          <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top">
            <el-form-item label="审批操作" prop="status">
              <el-radio-group v-model="dataForm.status">
                <el-radio :value="1" border>审批同意</el-radio>
                <el-radio :value="2" border>审批拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="拒绝原因" prop="remark" style="margin-bottom: 0px" v-if="dataForm.status == 2">
              <el-input v-model="dataForm.remark" type="textarea" placeholder="拒绝原因" :rows="4"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="card" style="margin-top: 12px">
          <div class="titleSty">流程进度</div>
          <el-steps direction="vertical" :active="dataForm.auditFlow">
            <el-step v-for="(item, index) in audits">
              <template #title>
                <span class="step_title">数据提交</span>
                <div class="step_cont">
                  <div class="line flx">
                    <span class="label">申请人：</span>
                    <span class="value" style="color: var(--el-color-primary)">{{ item.userName }}</span>
                  </div>
                  <div class="line flx">
                    <span class="label">申请时间：</span>
                    <span class="value">{{ item.createDate ? formatTimeStamp(item.createDate) : "-" }}</span>
                  </div>
                  <div class="line flx">
                    <span class="label">审批结果：</span>
                    <span class="value" v-if="item.auditFlow > dataForm.auditFlow">-</span>
                    <span class="value" v-else-if="item.status == 0 && item.auditFlow == dataForm.auditFlow" style="color: #67c23a">待审核</span>
                    <span class="value" v-else-if="item.auditFlow != dataForm.auditFlow && item.status == 1" style="color: var(--el-color-primary)">审核中</span>
                    <span class="value" v-else-if="item.auditFlow == dataForm.auditFlow && item.status == 1" style="color: var(--el-color-primary)">审核通过</span>
                    <span class="value" v-else-if="item.status == 2" style="">审核拒绝</span>
                    <!-- <span class="value" v-else >未审核</span> -->
                  </div>
                  <div class="line flx" v-if="item.status == 2">
                    <span class="label">拒绝原因：</span>
                    <span class="value">{{ item.remark }}</span>
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </el-scrollbar>
    <template #footer v-if="!viewShow">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="butLoading" @click="submit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import { formatTimeStamp } from "@/utils/method";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

interface Props {
  menuInfo: any;
}
const props = withDefaults(defineProps<Props>(), {
  menuInfo: ""
});

const tableData = ref(<any>[]);

const dataFormRef = ref();
const dataForm = ref({
  status: null,
  remark: "",
  auditFlow: 1,
  ids: []
});

const rules = ref({
  status: [{ required: true, message: "请选择审批操作", trigger: "change" }],
  remark: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }]
});

// 序号
const indexMethod = (index: number) => {
  return index + 1;
};

// 动态表头
const tableHeader = ref(<any>[]);
const reportcolumnAll = () => {
  tableHeader.value = [];
  baseService.get("/report/reportcolumn/all", { indexName: props.menuInfo.indexName }).then((res) => {
    tableHeader.value = res.data;
  });
};

// 查询目录详情
const audits = ref(<any>[]);
// 查询报表详情
const getTableInfo = (id: any) => {
  audits.value = [];
  baseService.get("/report/report/table/get", { tableId: props.menuInfo.id, id: id }).then((res) => {
    console.log(res.data.auditRecord, "==== 查询报表详情 ====");
    audits.value = res.data.auditRecord;
  });
};

// 保存
const butLoading = ref(false);
const submit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    butLoading.value = true;
    baseService
      .post("/report/reportaudit", dataForm.value)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "审核成功",
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        }
      })
      .finally(() => {
        butLoading.value = false;
      });
  });
};
const viewShow = ref(false); // 查看
const requestLoading = ref(false);
const visible = ref(false);
const init = (array: any, info: any, isView: boolean) => {
  visible.value = true;
  viewShow.value = isView;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  reportcolumnAll();
  tableData.value = array;
  getTableInfo(tableData.value[0].id);
  dataForm.value.ids = array.map((it: any) => it.id);
  Object.assign(dataForm.value, info);
  console.log(dataForm.value, isView, tableData.value, "====== shuju =====");
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.examine_page {
  background-color: #f0f2f5;
  padding: 12px;
  height: 100%;

  .card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }
}
.step_title {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 24px;
}
.step_cont {
  padding: 12px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
  .line + .line {
    margin-top: 4px;
  }
  .label {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 14px;
  }
  .value {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 14px;
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
</style>
<style lang="less">
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }

  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .el-drawer__body {
    padding: 0px;
  }
}
</style>
