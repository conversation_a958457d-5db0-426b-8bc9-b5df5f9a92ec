<template>
  <div class="mod-finance__fund-capital">
    <el-card shadow="never" class="rr-view-ctx-card cardTop ny_form_card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.zh" placeholder="请输入账号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.zhlx" placeholder="请选择账户类型">
                <el-option label="支付宝" value="支付宝" />
                <el-option label="银行卡" value="银行卡" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.zhyt" placeholder="请选择账户用途">
                <el-option label="备用金账户" value="备用金账户" />
                <el-option label="店铺支付宝" value="店铺支付宝" />
                <el-option label="收款支付宝" value="收款支付宝" />
                <el-option label="店铺银行卡" value="店铺银行卡" />
                <el-option label="公户银行卡" value="公户银行卡" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.hm" placeholder="请输入户名" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="state.dataForm.khyh" placeholder="请输入开户银行" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.cjry" placeholder="请输入创建人员" clearable></el-input>
            </el-col>
            <el-col :span="6" >
              <el-date-picker v-model="createTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange" clearable style="width: 240px"> </el-date-picker>
            </el-col>
            <el-col  :span="6">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <!-- 账户分类标签 -->
      <el-tabs style="margin-top: -8px" v-model="state.dataForm.zhfl" class="ny-tabs" @tab-change="state.getDataList()">
        <el-tab-pane label="全部" name=""></el-tab-pane>
        <el-tab-pane label="财务借款" name="财务借款"></el-tab-pane>
        <el-tab-pane label="运营借款" name="运营借款"></el-tab-pane>
        <el-tab-pane label="资金账户" name="资金账户"></el-tab-pane>
      </el-tabs>

      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <template #cjsj="scope">
          <span>{{ formatTimeStamp(scope.row.cjsj) }}</span>
        </template>

        <template #xgsj="scope">
          <span>{{ formatTimeStamp(scope.row.xgsj) }}</span>
        </template>

        <template #operation="scope">
          <el-button type="info" link size="small" @click="addOrUpdateHandle(scope.row.id, true)">详情</el-button>
          <el-button type="primary" link size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
        </template>
      </ny-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./fund-capital-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import type { FormInstance } from "element-plus";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "hm",
    label: "户名",
    minWidth: 200
  },
  {
    prop: "zhlx",
    label: "账户类型",
    width: 100
  },
  {
    prop: "zh",
    label: "账号",
    width: 180
  },
  {
    prop: "khyh",
    label: "开户银行",
    width: 150
  },
  {
    prop: "zhyt",
    label: "账户用途",
    width: 120
  },
  {
    prop: "zhfl",
    label: "账户分类",
    width: 100
  },
  {
    prop: "cjry",
    label: "创建人员",
    width: 120
  },
  {
    prop: "cjsj",
    label: "创建时间",
    width: 160
  },
  {
    prop: "xgsj",
    label: "修改时间",
    width: 160
  },
  {
    prop: "operation",
    label: "操作",
    width: 150,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/dao/fdfundaccountledger/page",
  getDataListIsPage: true,
  exportURL: "/dao/fdfundaccountledger/export",
  deleteURL: "/dao/fdfundaccountledger",
  deleteIsBatch: true,
  dataForm: {
    zh: "",
    zhlx: "",
    zhyt: "",
    hm: "",
    khyh: "",
    cjry: "",
    zhfl: "",
    startCreateDate: "",
    endCreateDate: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();

const addOrUpdateHandle = (id?: number, isView?: boolean) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, isView);
  });
};

// 时间选择变化
const handleDateChange = () => {
  if (createTimeRange.value) {
    state.dataForm.startCreateDate = createTimeRange.value[0] + " 00:00:00";
    state.dataForm.endCreateDate = createTimeRange.value[1] + " 23:59:59";
  } else {
    state.dataForm.startCreateDate = "";
    state.dataForm.endCreateDate = "";
  }
};

// 重置操作
const getResetting = () => {
  createTimeRange.value = undefined;
  state.dataForm.zh = "";
  state.dataForm.zhlx = "";
  state.dataForm.zhyt = "";
  state.dataForm.hm = "";
  state.dataForm.khyh = "";
  state.dataForm.cjry = "";
  state.dataForm.zhfl = "";
  state.dataForm.startCreateDate = "";
  state.dataForm.endCreateDate = "";
  state.getDataList();
};

// 导出
const exportHandle = () => {
  baseService.get("/dao/fdfundaccountledger/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "资金账户列表");
    }
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.cardTop {
  margin-bottom: 20px;
}

.ny-tabs {
  :deep(.el-tabs__header) {
    .el-tabs__item {
      width: 96px;
      padding: 0;
    }
  }
}

.amount-text {
  color: #e6a23c;
  font-weight: 500;
}
</style>


