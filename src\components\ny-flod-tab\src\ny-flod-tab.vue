<template>
  <div class="flod_tab_page">
    <div class="flod_tab flx">
      <div class="flod_tab_cont flx-1" :style="{ height: state.foldTabs ? 'auto' : '40px' }">
        <div
          class="flod_tab_li flx-center"
          v-for="item in props.list"
          @click="
            $emit('update:modelValue', item[props.value]);
            $emit('change', item[props.value]);
          "
        >
          <div :class="item[props.value] == props.modelValue ? 'codeActive' : ''">{{ item[props.label] }}</div>
        </div>
      </div>
      <div class="button_icon flx-center" v-if="state.moreTabs" @click="state.foldTabs = !state.foldTabs">
        <el-icon v-show="!state.foldTabs"><ArrowDownBold /></el-icon>
        <el-icon v-show="state.foldTabs"><ArrowUpBold /></el-icon>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, PropType, onMounted, onUnmounted, watch } from "vue";
import { IObject } from "@/types/interface";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
const props = defineProps({
  modelValue: {
    type: [Number, String] as PropType<number | string>,
    required: true
  },
  list: {
    type: Array as PropType<IObject[]>,
    required: true
  },
  value: {
    type: String,
    required: false,
    default: "code"
  },
  label: {
    type: String,
    required: false,
    default: "name"
  }
});
watch(
  () => props.list,
  () => {
    if (props.list.length > 0) {
      handleResize();
    }
  }
);
const handleResize = () => {
  const sxWidth2: number = (document.querySelector(".flod_tab") as HTMLElement)?.offsetWidth;
  let allFontLen = 0;
  props.list.forEach((item: any) => {
    allFontLen += Number(item[props.label].length * 14 + 40);
  });
  if (Number((sxWidth2 / allFontLen).toFixed(2)) < 1) {
    state.moreTabs = true;
    state.foldTabs = false;
  } else {
    state.moreTabs = false;
    state.foldTabs = true;
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize); // 监听窗口大小变化
});
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
const state = reactive({
  moreTabs: false,
  foldTabs: false
});
</script>

<style lang="less" scoped>
.flod_tab_page {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 15px;
  // padding: 20px; 
}
.flod_tab {
  position: relative;
  &::after {
    content: ' ';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0px;
    z-index: 1;
    height: 2px;
    background: var(--el-border-color);
  }
  .flod_tab_cont {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    .flod_tab_li {
      height: 40px;
      padding: 0px 20px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
    .codeActive {
      display: flex;
      align-items: center;
      justify-content: center;
      // color: var(--el-color-primary);

      position: relative;
      &::after {
        content: ' ';
        position: absolute;
        left: 0;
        right: 0;
        bottom: -10px;
        z-index: 10;
        height: 2px;
        background: var(--el-color-primary);
        animation: slideOut 0.6s forwards;
        @keyframes slideOut {
          from {
            opacity: 0;
            transform: translateX(0%) scale(0);
          }
          to {
            opacity: 1;
            transform: translateX(0%) scale(1);
          }
        }
      }
    }
  }
}
.button_icon {
  cursor: pointer;
  width: 40px;
  height: 40px;
  .el-icon {
    font-size: 16px;
  }
}
.newTabSty {
  .flod_tab_li {
    padding: 0 !important;
    > div {
      padding: 0 20px;
      font-weight: normal;
    }
    .codeActive {
      color: var(--el-color-primary);
      font-weight: bold;
    }
  }
}
.flod_tab_li {
  padding: 0 !important;
  > div {
    padding: 0 20px;
    font-weight: normal;
  }
  .codeActive {
    color: var(--el-color-primary);
    font-weight: bold;
  }
}
</style>
