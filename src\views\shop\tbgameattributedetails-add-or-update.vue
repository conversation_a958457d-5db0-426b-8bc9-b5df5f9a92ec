<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="属性明细id" prop="id">
        <el-input v-model="dataForm.id" placeholder="属性明细id"></el-input>
      </el-form-item>
      <el-form-item label="属性明细名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="属性明细名称"></el-input>
      </el-form-item>
      <el-form-item label="属性明细状态" prop="status">
        <el-input v-model="dataForm.status" placeholder="属性明细状态"></el-input>
      </el-form-item>
      <el-form-item label="关联属性Code" prop="propertiesCode">
        <el-input v-model="dataForm.propertiesCode" placeholder="关联属性Code"></el-input>
      </el-form-item>
      <el-form-item label="属性明细值" prop="gadValue">
        <el-input v-model="dataForm.gadValue" placeholder="属性明细值"></el-input>
      </el-form-item>
      <el-form-item label="属性明细key" prop="gadKey">
        <el-input v-model="dataForm.gadKey" placeholder="属性明细key"></el-input>
      </el-form-item>
      <el-form-item label="是否删除 否0 是1" prop="isDelete">
        <el-input v-model="dataForm.isDelete" placeholder="是否删除 否0 是1"></el-input>
      </el-form-item>
      <el-form-item label="创建者" prop="creator">
        <el-input v-model="dataForm.creator" placeholder="创建者"></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createDate">
        <el-input v-model="dataForm.createDate" placeholder="创建时间"></el-input>
      </el-form-item>
      <el-form-item label="更新者" prop="updater">
        <el-input v-model="dataForm.updater" placeholder="更新者"></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updateDate">
        <el-input v-model="dataForm.updateDate" placeholder="更新时间"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  name: "",
  status: "",
  propertiesCode: "",
  gadValue: "",
  gadKey: "",
  isDelete: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbgameattributedetails/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/shop/tbgameattributedetails", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>