<template>
    <el-dialog
        v-model="visible" 
        :title="`确认开通${currentRow.openState == 0 || currentRow.openState == null ? '试用' : ''}`" 
        :close-on-click-modal="false" 
        :close-on-press-escape="false" 
    >
        <el-alert 
            class="mb-20"
            type="error" 
            :closable="false"
            title="支付前需提前联系对接合作商办理入驻并拿到对接密钥，联系技术进行对接后，并开通相关服务。"
        ></el-alert>
        <!-- <div class="flx">
            <div class="api-content-item">开通价格：<el-text type="danger" class="bold">{{ currentRow.feeStandards }}</el-text></div>
            <div class="api-content-item ml-10">续费价格：<el-text type="danger" class="bold">{{ currentRow.renew }}</el-text></div>
        </div> -->
        <el-form label-width="100" label-suffix="：">
            <el-form-item label-width="20">
                <div class="flx">
                    <div class="api-content-item">开通价格：<el-text type="danger" class="bold">{{ currentRow.feeStandards }}</el-text></div>
                    <div class="api-content-item ml-10">续费价格：<el-text type="danger" class="bold">{{ currentRow.renew }}</el-text></div>
                </div>
            </el-form-item>
            <el-form-item label="试用时长" v-if="currentRow.openState == 0 || currentRow.openState == null">
                <ny-radio-group v-model="dataForm.trialMonth" :list="trialMonthList" value="value" label="label"></ny-radio-group>
            </el-form-item>
            <template v-else>
                <el-form-item label="开通时长">
                    <ny-radio-group v-model="dataForm.openingDuration" :list="currentRow.expireMonth" value="value" label="label"></ny-radio-group>
                </el-form-item>
                <el-form-item label="开通金额">
                    <div v-if="currentRow.openState == 3 || currentRow.openState== 4">{{ BigNumber(currentRow.feeStandards).plus(BigNumber(currentRow.renew).multipliedBy(BigNumber(dataForm.openingDuration).minus(1))) }}元</div>
                    <div v-else-if="currentRow.openState==0">{{ BigNumber(currentRow.renew).multipliedBy(BigNumber(dataForm.openingDuration).minus(1)) }}元</div>
                    <div v-else>{{ BigNumber(currentRow.renew).multipliedBy(BigNumber(dataForm.openingDuration)) }}元</div>
                </el-form-item>
            </template>
            <el-form-item label="账户余额">
                {{ currentRow.balance }}元
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button :loading="btnLoading" @click="visible = false">{{ $t('cancel') }}</el-button>
            <el-button 
                :loading="btnLoading" 
                type="primary" 
                @click="handleSubmit"
            >
                {{ currentRow.openState == 0 ? '立即试用' : '确认开通' }}
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineEmits } from "vue";
    import { ElMessage } from "element-plus";
    import { IObject } from "@/types/interface";
    import { BigNumber } from "bignumber.js";
    import { useAppStore } from "@/store";
    import baseService from "@/service/baseService";
    
    const store = useAppStore();
    const emits = defineEmits(["refresh"]);
    const visible = ref(false);

    const currentRow = ref({} as IObject);
    
    // 试用时长
    const trialMonthList = ref([] as IObject);

    const dataForm = ref({
        // 开通时长
        openingDuration: "",
        trialMonth: null,
    });

    const init = (row: IObject) => {
        currentRow.value = {...row, renew: 50, expireMonth: row.expireMonth ? row.expireMonth.split(",") : []};
        let arr: any = [];
        currentRow.value.expireMonth.map((item: any) => {
            arr.push({label: item + '个月', value: item})
        })
        currentRow.value.expireMonth = arr;
        dataForm.value.openingDuration = currentRow.value.expireMonth[0].value;
        dataForm.value.trialMonth = currentRow.value.trialMonth ? currentRow.value.trialMonth.toString() : "1";
        visible.value = true;

        if(dataForm.value.trialMonth){
            trialMonthList.value = [{label: dataForm.value.trialMonth + '个月', value: dataForm.value.trialMonth}]
        }
    }

    // 开通
    const btnLoading = ref(false);
    const handleSubmit = async () => {
        btnLoading.value = true;
        try {
            let data = {
                id: currentRow.value.id,
                months: dataForm.value.openingDuration,
                companyType: currentRow.value.companyType
            }
            const res = await baseService.post("/partner/partner/openDockingPort", data);
            btnLoading.value = false;
            if (res.code == 0) {
                visible.value = false;
                ElMessage.success("开通成功");
                emits("refresh");
            } else {
                ElMessage.error(res.msg);
            }
        } catch (error) {
            btnLoading.value = false;
        }
    };

    defineExpose({
        init
    })

</script>