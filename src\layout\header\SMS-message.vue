<template>
    <!-- 自动上号验证码 -->
    <el-dialog v-model="dialogVisible" title="自动上号验证码" width="480" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="1">
            <el-descriptions-item label="游戏名称">{{ dataForm.gameName }}</el-descriptions-item>
            <el-descriptions-item label="游戏账号">{{ dataForm.userName }}</el-descriptions-item>
            <el-descriptions-item>
                <template #label><span>验证码<span style="color: red">*</span></span></template>
                <el-input type="number" v-model="dataForm.verifyCode" placeholder="验证码" style="width: 100%;" clearable ></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <el-button type="primary" v-loading="dialogSubmitLoading" @click="dialogSubmit">立即验证</el-button>
        </template>
    </el-dialog>
    <!-- 盼之推送验证码 -->
    <el-dialog v-model="pzdsShow" title="同步上架短信验证码" width="480" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="1">
            <el-descriptions-item label="游戏名称">{{ pzdsForm.goodsName }}</el-descriptions-item>
            <el-descriptions-item label="商品编码">{{ pzdsForm.shopCode }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ pzdsForm.phone }}</el-descriptions-item>
            <el-descriptions-item>
                <template #label><span>验证码<span style="color: red">*</span></span></template>
                <el-input type="number" v-model="pzdsForm.code" placeholder="验证码" style="width: 100%;" clearable ></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <el-button type="primary" v-loading="dialogSubmitLoading" @click="pzdsSubmit">提交验证码</el-button>
        </template>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref,reactive, h, watch, onUnmounted  } from 'vue';
import useWebSocket from "@/utils/websocket";
import { useAppStore } from "@/store";
import { ElNotification, ElMessage } from 'element-plus';
import { useRouter } from "vue-router";
import SEMMessageImg from "@/assets/images/SESMessage.png"
import app from "@/constants/app";
import baseService from '@/service/baseService';
const router = useRouter();
const store = useAppStore();

const websocketUrl = `${ app.wsApi }/ws/mobile/${store.state.user.id}`;

const header = {
    "type": "heartbeat"
}
const { message, status, close } = useWebSocket({
  url: websocketUrl,
  heartMessage: JSON.stringify(header) , // 心跳消息
})

const messageType = [
    { label:'短信消息', value: 1},
    { label:'自动扫描', value: 2},
    { label:'自动推送', value: 3},
    { label:'验证码', value: 4},
    { label:'自动上号结果', value: 5},
    { label:'一键截图结果', value: 6},
    { label:'盼之代售短信验证码', value: 7},
]

// 监听消息变化
watch(message, (newVal) => {
    const info = newVal ? JSON.parse(newVal) : {};
    console.log(info);
    if(info.type == 4){
        dataForm.id = info.id;
        dataForm.gameName = info.gameName;
        dataForm.userName = info.userName;
        ElMessage({
            message: info.message,
            type: 'warning',
            duration: 4000
        });
        dialogVisible.value = true;
        return;
    }

    if(info.type == 7){
        pzdsForm.value = info;
        ElMessage({
            message: info.message,
            type: 'warning',
            duration: 4000
        });
        pzdsShow.value = true;
        return;
    }
    
    
    ElNotification({
        message:h('div', { class: 'notification-wrap', style: 'align-items: flex-start !important;' },[
            h('img', { class: 'notification-img', src: SEMMessageImg }),
            h('div', { class: 'notification-content', style:'width: 280px'}, [
                h('div', { class: 'notification-title sle' }, messageType.find(item => item.value == info.type)?.label),
                // h('div', { class: 'notification-desc' }, info.message)
                h('div', { innerHTML: info.message})
            ])
        ]),
        customClass: 'custom-notification',
        duration: 5000,
        onClick: () => {
            if(info.type == 1){
                router.push('/utilityTools/cardPositionMana/index?activeName=' + '短信接收记录')
            }
            if(info.type == 2){
                router.push('/utilityTools/autoScan/index')
            }
        }
    })
})

// 监听连接状态
watch(status, (newVal) => {
//   console.log('连接状态变化:', newVal)
})

// onUnmounted(()=>{
//     close();
// })



// 小算验证码
const dialogVisible = ref(false);
const dataForm = reactive({
    id: null,
    gameName: "",
    userName: "",
    verifyCode: "",
})
const dialogSubmitLoading = ref(false);
const dialogSubmit = () =>{
    if(!dataForm.verifyCode){
        ElMessage.warning('请输入验证码');
        return
    }
    dialogSubmitLoading.value = true;
    baseService.post("/xiaosuan/task/submitVerifyCode",{
        id: dataForm.id,
        verifyCode: dataForm.verifyCode
    }).then(res=>{
        if(res.code == 0){
            ElMessage.success('提交成功');
        }
    }).finally(()=>{
        dialogSubmitLoading.value = false;
        dialogVisible.value = false;
    })
}


// 盼之推送验证码
const pzdsShow = ref(false);
const pzdsForm = ref();
const pzdsSubmit = () =>{
    if(!pzdsForm.value.code){
        ElMessage.warning('请输入验证码');
        return
    }
    dialogSubmitLoading.value = true;
    baseService.post("/script/sysscriptpushtaskdetails/submitPhoneCode",pzdsForm.value).then(res=>{
        if(res.code == 0){
            ElMessage.success('提交成功');
        }
    }).finally(()=>{
        dialogSubmitLoading.value = false;
        pzdsShow.value = false;
    })
}

</script>

<style lang='less'>
/* 覆盖通知框宽度 */
.custom-notification {
  width: 400px !important;
}
</style>