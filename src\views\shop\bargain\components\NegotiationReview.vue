<template>
    <!-- 议价审核 -->
    <el-drawer
        v-model="visible"
        title="议价审核"
        :close-on-click-moformuladal="false"
        :close-on-press-escape="false"
        
        size="40%"
    >   
        <div class="p-title mt-1">买家还价信息</div>
        <el-descriptions class="descriptions-label-140" :column="1" border v-loading="dataLoading">
            <el-descriptions-item label="商品编码">{{ resData.shopCode }}</el-descriptions-item>
            <el-descriptions-item label="商品标题">{{ resData.shopTitle }}</el-descriptions-item>
            <el-descriptions-item label="买家ID">{{ resData.buyerId }}</el-descriptions-item>
            <el-descriptions-item label="原价">
                <el-text type="primary" class="bold">￥{{ resData.shopPrice }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="买家出价">
                <el-text type="danger" class="bold">￥{{ resData.buyerPrice }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="买家出价时间">{{ resData.buyerOfferTime || '-' }}</el-descriptions-item>
            <el-descriptions-item label="有效期">{{ resData.bargainExpireTime || '-' }}</el-descriptions-item>
        </el-descriptions>

        <div class="p-title pt-8">卖家议价操作</div>
        <el-descriptions class="descriptions-label-140" :column="1" border>
            <el-descriptions-item label="议价">
                <el-radio-group v-model="dataForm.state">
                    <el-radio :value="0">还价</el-radio>
                    <el-radio :value="1">同意</el-radio>
                    <el-radio :value="2">拒绝</el-radio>
                </el-radio-group>
            </el-descriptions-item>
            <el-descriptions-item label="卖家出价" v-if="dataForm.state == 0">
                <div class="flx-align-center">
                    <el-input
                        type="number"
                        v-model="dataForm.sellerPrice"
                        style="width: 280px"
                        placeholder="请输入价格"
                        @blur="checkValue"
                    />
                    <span class="text-3 ml-4">元</span>
                </div>
            </el-descriptions-item>
        </el-descriptions>

        <template #footer>
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle">确定</el-button>
        </template>
        
    </el-drawer>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineEmits } from "vue";
    import baseService from "@/service/baseService";
    import { ElMessage } from "element-plus"

    const emits = defineEmits(["refresh"]);
    const visible = ref(false);
    
    const resData = ref({} as any)

    const dataForm = ref({
        id: null,
        // 0议价中，1已同意，2已拒绝，3已过期 默认0
        state: 0,
        sellerPrice: ""
    })
    
    const currentId = ref();
    const init = (id: number) => {
        visible.value = true;
        currentId.value = id;
        dataForm.value.id = id;
        getDetails();
    }

    const dataLoading = ref(false);
    const getDetails = async () => {
        dataLoading.value = true;
        let res = await baseService.get("/sub/bargain/get/" + currentId.value );
        dataLoading.value = false;
        resData.value = res.data;
    }

    // 表单提交
    const btnLoading = ref(false);
    const dataFormSubmitHandle = async () => {
        if(dataForm.value.state == 0 && !dataForm.value.sellerPrice){
            return ElMessage.warning("请输入价格");
        }
        if(dataForm.value.sellerPrice > resData.value.shopPrice){
            return ElMessage.warning("卖家出价不能大于原价");
        }
        if(!dataForm.value.sellerPrice) dataForm.value.sellerPrice = "0";
        if(dataForm.value.state == 1) dataForm.value.sellerPrice = resData.value.buyerPrice;
        btnLoading.value = true;
        let res = await baseService.put("/sub/bargain/update", dataForm.value)
        btnLoading.value = false;
        if(res.code == 0) {
            ElMessage.success("操作成功");
            visible.value = false;
            emits("refresh");
        }
    }

    // 校验
    const checkValue = (event:any) =>{
        if(dataForm.value.sellerPrice > resData.value.shopPrice){
            ElMessage.warning("卖家出价不能大于原价");
        }
    }
    
    defineExpose({
        init
    })
</script>