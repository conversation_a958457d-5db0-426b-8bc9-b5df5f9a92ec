<template>
  <div class="TableXScrollSty">
    <el-card  :shadow="false" class="rr-view-ctx-card">
      <ny-table cellHeight="ch-56" :showColSetting="false" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header-custom>
          <div style="margin-bottom: 12px">
            <el-button v-if="state.hasPermission('sys:ipBlackList:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          </div>
        </template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-link v-if="state.hasPermission('sys:ipBlackList:update')" :underline="false" style="margin-right: 12px" @click="addOrUpdateHandle(row.id)" type="primary">编辑</el-link>
          <el-link v-if="state.hasPermission('sys:ipBlackList:delete')" :underline="false" @click="state.deleteHandle(row.id)" type="danger">删除</el-link>
        </template>
      </ny-table>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./quickInformation-add-or-update.vue";

const view = reactive({
  getDataListURL: "/im/imquickphrases/page",
  getDataListIsPage: true,
  deleteURL: "/im/imquickphrases",
  deleteIsBatch: true,
  dataForm: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const tableColums = reactive([
  {
    prop: "gameName",
    label: "游戏名称",
    minWidth: 184
  },
  {
    prop: "msg1",
    label: "快捷消息1"
  },
  {
    prop: "msg2",
    label: "快捷消息2"
  },
  {
    prop: "msg3",
    label: "快捷消息3"
  },
  {
    prop: "operation",
    label: "操作",
    width: 140
  }
]);
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
