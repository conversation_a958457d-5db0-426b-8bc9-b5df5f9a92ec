<template>
  <div class="basicInfoSty cardDescriptions">
    <div class="titleSty">基本信息</div>
    <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
      <el-descriptions-item label-class-name="title">
        <template #label><div>游戏名称</div> </template>
        {{ props.orderInfo.gameName || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>游戏账号</div> </template>
        {{ props.orderInfo.gameAccount || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>商品编码</div> </template>
        {{ props.orderInfo.shopCode || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>成交价(元)</div> </template>
        {{ props.orderInfo.dealAmount || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>回收价(元)</div> </template>
        {{ props.orderInfo.purchaseAmount || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>包赔费(元)</div> </template>
        {{ props.orderInfo.fee || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>创建时间</div> </template>
        {{ props.orderInfo.createDate || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>售出时间</div> </template>
        {{ formatTimeStamp(props.orderInfo.sellTime) || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>回收人</div> </template>
        {{ props.orderInfo.acquisitionName  || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>售出人</div> </template>
        {{ props.orderInfo.saleUserName || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title" :span="2">
        <template #label><div>卖方手机号</div> </template>
        {{ props.orderInfo.saleUserPhone || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title" :span="2">
        <template #label><div>账号来源</div> </template>
        {{ props.orderInfo.orderSource || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>商品类型</div> </template>
        {{ props.orderInfo.saleType || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>售后类型</div> </template>
        {{ props.orderInfo.saleAfterType || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title" :span="2">
        <template #label><div>问题截图</div> </template>
        <el-image v-if="props.orderInfo" class="srceenshot" style="width: 54px; height: 54px" :src="props.orderInfo.saleAfterPics" :preview-src-list="[props.orderInfo.saleAfterPics]"></el-image>
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div class="basicInfoSty cardDescriptions">
    <div class="titleSty">收款信息</div>
    <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
      <el-descriptions-item label-class-name="title">
        <template #label><div>三方订单编号</div> </template>
        {{ props.orderInfo.thirdPartyOrderCode }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>应收金额(元)</div> </template>
        {{ props.orderInfo.disposeDataInfo && props.orderInfo.disposeDataInfo.salesAfterRefund }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>付款方式</div> </template>
        {{ props.orderInfo.disposeDataInfo && props.orderInfo.disposeDataInfo.salesAfterAccountName }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>支付宝订单号</div> </template>
        {{ props.orderInfo.disposeDataInfo && props.orderInfo.disposeDataInfo.sellersAlipayOrderNo }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label><div>付款凭证</div> </template>
        <el-image style="height: 4.25rem; width: 4.25rem" :src="props.orderInfo.disposeDataInfo && props.orderInfo.disposeDataInfo.sellersPayImage" :preview-src-list="[props.orderInfo.disposeDataInfo && props.orderInfo.disposeDataInfo.sellersPayImage]" preview-teleported fit="cover" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 12px; display: flex; justify-content: flex-end" v-if="props.orderInfo.autoReconciliationStatus">
      <el-tag :type="props.orderInfo.autoReconciliationStatus == 1 ? 'success' : 'danger'" size="large">
        <div style="display: flex; align-items: center; gap: 9px">
          <el-icon v-if="props.orderInfo.autoReconciliationStatus == 1"><CircleCheckFilled /></el-icon>
          <el-icon v-else><CircleCloseFilled /></el-icon>
          <span style="font-weight: 500; font-size: 14px">{{ props.orderInfo.autoReconciliationStatus == 1 ? "对账成功" : "对账异常" }}</span>
        </div>
      </el-tag>
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";

interface Props {
  orderInfo: any;
  title: any;
}
const props = withDefaults(defineProps<Props>(), {
  orderInfo: "",
  title: "",
});
</script>

<style lang="less" scoped>
.basicInfoSty {
  margin-bottom: 12px;
}
</style>
