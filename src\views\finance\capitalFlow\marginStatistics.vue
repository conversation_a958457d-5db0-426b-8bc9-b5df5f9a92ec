<template>
  <div class="container bargain-wrap">
    <ny-table cellHeight="ch-56" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
      <template #header>
        <ny-button-group label="label" value="value" :list="stateList" v-model="state.dataForm.type" @change="state.getDataList"></ny-button-group>
      </template>

      <template #header-right>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-form-item>
            <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
          </el-form-item>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
        </el-form>
      </template>

      <template #header-custom>
        <div class="mb-12">
          <el-button v-if="state.hasPermission('finance:billingInformation:export')" type="primary" @click="getExport">{{ $t("export") }}</el-button>
        </div>
      </template>

      <template #type="{ row }">
        <span>{{ row.type == "7" ? "保证金充值" : "保证金退款" }}</span>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span style="font-weight: bold; color: #1d2129">交易金额（元）</span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
  </div>
</template>

<script lang="ts" setup>
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { nextTick, reactive, ref, toRefs, watch, defineExpose } from "vue";
import { ElMessage } from "element-plus";
import { Search, DocumentCopy } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import { BigNumber } from "bignumber.js";

// 表格配置项
const tableColums = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderCode",
    label: "交易订单号",
    minWidth: 200
  },
  {
    prop: "tenantName",
    label: "合作商名称",
    minWidth: 220
  },
  {
    prop: "tradingHour",
    label: "交易时间",
    minWidth: 190
  },
  {
    prop: "type",
    label: "交易类型",
    minWidth: 220
  },
  {
    prop: "accountType",
    label: "支付方式",
    minWidth: 220
  },
  {
    prop: "serialNumber",
    label: "流水号",
    minWidth: 220
  },
  {
    prop: "realAmount",
    label: "交易金额(元)",
    width: 224,
    sortable: "custom"
  }
]);

const view = reactive({
  getDataListURL: "/finance/tenantfinance/getCashPledgePage",
  getDataListIsPage: true,
  // listRequestMethod: 'post',
  dataForm: {
    type: "7",
    start: "",
    end: "",
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 重置操作
const getResetting = () => {
  state.dataForm.start = "";
  state.dataForm.end = "";
  createDate.value = [];
  state.getDataList();
};

// 状态
const stateList = [
  { label: "收入流水", value: "7" },
  { label: "支出流水", value: "8" }
];

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 导出
const getExport = () => {
  baseService.get("/finance/tenantfinance/export/cashPledge", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, view.dataForm.type == "7" ? "保证金统计-收入流水" : "保证金统计-支出流水");
    }
  });
};
// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.realAmount) total = total + +item.realAmount;
  });
  return total.toFixed(2);
};
defineExpose({
  getResetting
});
</script>

<style lang="scss" scoped>
ul,
li {
  padding: 0;
  list-style: none;
}
.flex {
  display: flex;
  align-items: cenetr;
}

.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}

.headCardUl {
  display: flex;
  align-items: cenetr;
  flex-wrap: nowrap;

  .headCardLi {
    width: 20%;
    margin-right: 20px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;

    &:last-child {
      margin-right: 0;
    }

    .head {
      justify-content: space-between;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
    }

    .middle {
      justify-content: space-between;
      span {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      :deep(.el-statistic__content) {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bottom {
      margin-top: 16px;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      :deep(.el-link__inner) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 14px;
        height: 11px;
        margin-left: 5px;
      }
    }
  }
}
.linkSty {
  display: flex;
  align-items: center;
  text-align: center;
  width: fit-content;
  margin: auto;
  .copyIcon {
    display: none;
    width: 1px;
  }
  &:hover {
    .copyIcon {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
</style>
