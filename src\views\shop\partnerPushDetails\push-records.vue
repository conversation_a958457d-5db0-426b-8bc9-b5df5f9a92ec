<template>
  <div>
    <div style="display: flex; justify-content: space-between; align-items: center">
      <ny-button-group
        label="label"
        value="value"
        :list="[
          { label: '上架记录', value: '1' },
          { label: '下架记录', value: '2' }
        ]"
        v-model="dataForm.pushType"
        @change="getListData"
      ></ny-button-group>
      <div>
        <!-- <el-input v-model="dataForm.searchParam" placeholder="请输入商品标题/游戏账号/商品编码" :prefix-icon="Search" style="width: 280px !important" clearable></el-input> -->
      </div>
    </div>
    <div style="padding: 12px 0px; overflow: auto">
      <el-collapse class="collapseSty" v-model="collapseValue" accordion v-if="state.dataList.length > 0">
        <el-collapse-item v-for="(item, itemIndex) in state.dataList" :key="itemIndex" :name="itemIndex">
          <template #title>
            <div class="collapseStyTitle">
              <div>
                <el-icon style="margin-right: 30px; color: #909399; font-size: 20px"><CaretBottom v-if="collapseValue === itemIndex" /><CaretRight v-else /></el-icon>
                <el-result title="推送时间">
                  <template #sub-title>
                    {{ item.createDate }}
                  </template>
                </el-result>
              </div>
              <div>
                <el-result title="推送商品总数" :sub-title="(item.countAll || '0') + ''"> </el-result>
                <el-result title="执行中" :sub-title="(item.executionCount || '0') + ''"> </el-result>
                <el-result title="成功数量">
                  <template #sub-title>
                    {{ item.countAll > 0 ? (item.successCount || 0) + "/" + item.countAll : "-" }}
                  </template>
                </el-result>
                <el-result title="状态">
                  <template #sub-title>
                    <el-tag v-if="item.pushStatus == 3" type="warning">{{ item.pushStatusName }}</el-tag>
                    <el-tag v-else-if="item.pushStatus == 1" type="success">{{ item.pushStatusName }}</el-tag>
                    <el-tag v-else-if="item.pushStatus == 0" type="primary">{{ item.pushStatusName }}</el-tag>
                    <el-tag v-else-if="item.pushStatus == 2" type="info">{{ item.pushStatusName }}</el-tag>
                    <el-tag v-else-if="item.pushStatus == 4" type="warning">{{ item.pushStatusName }}</el-tag>
                    <span v-else>-</span>
                  </template>
                </el-result>
              </div>
            </div>
          </template>
          <div>
            <!-- 推送信息 -->
            <pushInfo v-if="collapseValue === itemIndex" ref="pushInfoRef" :propData="{ ...item }" @pushChange="getListData"></pushInfo>
          </div>
        </el-collapse-item>
      </el-collapse>
      <el-empty v-else />
      <el-pagination
        v-if="!tableLoading"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 50, 100, 500, 1000]"
        :page-size="queryParams.limit"
        :total="state.count"
        :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="TableSizeChangeFn"
        @current-change="TableCurrentChangeFn"
      ></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from "vue";
import baseService from "@/service/baseService";
import { timeFormat, fileExport } from "@/utils/utils";
import pushInfo from "./push-info.vue";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  params: {
    type: Object,
    required: true
  }
});
const collapseValue = ref(-1);
const dataForm = ref({
  pushType: "1",
  searchParam: ""
});

const state = ref({
  dataForm: {
    partnerId: null, //合作商id
    gameId: undefined, //游戏id
    scriptUserId: "" // 店铺Id
  },
  dataList: <any>[],
  count: 0
});

const queryParams: { [key: string]: any } = reactive({
  page: 1,
  limit: 10
});

// 获取列表信息&切换游戏
const tableLoading = ref(false);
const getListData = () => {
  tableLoading.value = true;
  state.value.dataList = [];
  if(!props.params.gameId) return;

  // 推送记录
  baseService
    .get("/sync/sysPartnerShopPushAllRecord/page", {
      partnerId: props.params.partnerId,
      gameId: props.params.gameId,
      pushType: dataForm.value.pushType,
      ...queryParams
    })
    .then((res_) => {
      if (res_.code == 0) {
        console.log(res_.data.list);
        
        state.value.dataList = res_.data.list || [];
        tableLoading.value = false;
        state.value.count = res_.data.total;
        collapseValue.value = 0;
      }
    })
    .catch((err) => {
      tableLoading.value = false;
      state.value.count = 0;
    });
};
// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  getListData();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getListData();
};

watch(
  () => props.params,
  (newVal,oldVal) => {
    if((JSON.stringify(newVal) != JSON.stringify(oldVal))){
      getListData();
    }
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  getListData();
});

defineExpose({
  getListData
});
</script>

<style lang="less" scoped>
.collapseSty {
  border: none;
  :deep(.el-collapse-item__arrow) {
    display: none;
  }
  .el-collapse-item,
  :deep(.el-collapse-item__header) {
    height: fit-content;
  }
  .el-collapse-item {
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;

    :deep(.el-collapse-item__content) {
      border: 1px solid var(--el-border-color-lighter);
      border-top: 0;
      border-bottom: 0;
    }
    :deep(.el-collapse-item__header) {
      border: none;
    }
  }
  .collapseStyTitle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f5f7fa;
    > div {
      padding: 12px;
      display: flex;
      align-items: center;
    }
  }
  :deep(.el-result) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-right: 24px;
    padding: 0;
    .el-result__icon {
      display: none;
    }
    .el-result__title {
      margin-top: 0;
      p {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
      }
    }

    .el-result__subtitle {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #303133;
      margin-top: 2px;
    }
  }
}
</style>
