<template>
  <el-dialog v-model="visible" title="查看日志" :close-on-click-modal="false" :close-on-press-escape="false" width="50%">
    <el-tabs v-model="activeTabName">
      <el-tab-pane label="操作日志" name="operation">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="creatorName" label="操作人" width="180" align="center" />
          <el-table-column prop="shopCode" label="商品编码" width="180" align="center" />
          <el-table-column prop="params" label="修改内容" align="center" />
          <el-table-column prop="createDate" label="操作时间" width="180" align="center">
            <template #default="scope">
              {{ formatTimeStamp(scope.row.createDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="operation" label="操作行为" width="180" align="center" />
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
        <el-pagination :hide-on-single-page="false" background :page-sizes="[10, 20, 50, 100, 500, 1000]" layout="sizes, total, prev, pager, next, jumper" :total="count" @size-change="TableSizeChangeFn" @current-change="TableCurrentChangeFn" />
      </el-tab-pane>

      <el-tab-pane label="推送日志" name="push">
        <!-- <ny-flod-tab class="newTabSty" @change="viewLog.dataForm.pushStatus ='',stateLog.getDataList()" :list="statusList" v-model="viewLog.dataForm.recordType" value="value" label="label"> </ny-flod-tab> -->
        <ny-table :state="stateLog" :showColSetting="false" :columns="columnsLog" @pageSizeChange="stateLog.pageSizeChangeHandle" @pageCurrentChange="stateLog.pageCurrentChangeHandle" @selectionChange="stateLog.dataListSelectionChangeHandle">
          <template #header>
            <ny-button-group
              :list="[
                { dictLabel: '上架记录', dictValue: '1' },
                { dictLabel: '下架记录', dictValue: '2' }
              ]"
              v-model="viewLog.dataForm.recordType"
              @change="(viewLog.dataForm.pushStatus = ''), stateLog.getDataList()"
            ></ny-button-group>
          </template>
          <template #header-right>
            <el-form :inline="true" :model="viewLog.dataForm" @keyup.enter="stateLog.getDataList()">
              <el-form-item>
                <el-select v-if="viewLog.dataForm.recordType == 1" v-model="viewLog.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                  <el-option label="未推送" value="0"></el-option>
                  <el-option label="推送成功" value="1"></el-option>
                  <el-option label="推送失败" value="2"></el-option>
                </el-select>

                <el-select v-else v-model="viewLog.dataForm.pushStatus" placeholder="请选择推送状态" clearable>
                  <el-option label="未下架" value="0"></el-option>
                  <el-option label="下架成功" value="1"></el-option>
                  <el-option label="下架失败" value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" @click="stateLog.getDataList()">{{ $t("query") }}</el-button>
              <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-form>
          </template>
          <template #errorMessage="{ row }">
            <div style="display: flex" v-if="row.errorMessage">
              <div style="width: 240px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ row.errorMessage }}</div>
              <el-button @click="handleOpen('推送信息', row.errorMessage)" type="primary" link>查看</el-button>
            </div>
            <span v-else>-</span>
          </template>
          <template #requestParam="{ row }">
            <div style="display: flex" v-if="row.requestParam">
              <div class="sle">{{ row.requestParam }}</div>
              <el-button @click="handleOpen('请求参数', row.requestParam)" type="primary" link>查看</el-button>
            </div>
          </template>
          <template #responseBody="{ row }">
            <div style="display: flex" v-if="row.responseBody">
              <div class="sle">{{ row.responseBody }}</div>
              <el-button @click="handleOpen('返回参数', row.responseBody)" type="primary" link>查看</el-button>
            </div>
          </template>
          <template #requestUrl="{ row }">
            <div style="display: flex" v-if="row.requestUrl">
              <div class="sle">{{ row.requestUrl }}</div>
              <el-button @click="handleOpen('请求地址', row.requestUrl)" type="primary" link>查看</el-button>
            </div>
          </template>
          <template #successedStatus="{ row }">
            <block v-if="viewLog.dataForm.recordType == 1">
              <el-tag v-if="row.successedStatus == '未推送'" type="warning">未推送</el-tag>
              <el-tag v-if="row.successedStatus == '推送成功'" type="primary">推送成功</el-tag>
              <el-tag v-if="row.successedStatus == '推送失败'" type="danger">推送失败</el-tag>
            </block>
            <block v-else>
              <el-tag v-if="row.successedStatus == '未推送'" type="warning">未下架</el-tag>
              <el-tag v-if="row.successedStatus == '推送成功'" type="primary">下架成功</el-tag>
              <el-tag v-if="row.successedStatus == '推送失败'" type="danger">下架失败</el-tag>
            </block>
          </template>
          <template #createDate="{ row }">
            {{ formatTimeStamp(+row.createDate) }}
          </template>
        </ny-table>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script lang="ts" setup>
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { ref, reactive, toRefs } from "vue";

const visible = ref(false); // 对话框显隐
const tableData = ref(<any>[]); // 页面表格数据
const count = ref(""); // 数据总条数
const queryParams = reactive({
  page: 1,
  limit: 10,
  shopId: null
});

const activeTabName = ref("operation");

// 弹窗初始化
const init = (shopId: any) => {
  visible.value = true;
  activeTabName.value = "operation";
  queryParams.shopId = shopId;
  queryParams.page = 1;
  stateLog.dataForm.shopId = shopId;
  getInfo();
  stateLog.getDataList();
};

// 获取数据
const getInfo = () => {
  baseService.get("/shop/shop/log", queryParams).then((res) => {
    count.value = res.data.total;
    tableData.value = res.data.list;
  });
};

// 表格分页
const TableSizeChangeFn = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  getInfo();
};

const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getInfo();
};

const columnsLog = reactive([
  {
    prop: "companyName",
    label: "合作商",
    minWidth: 100
  },
  {
    prop: "errorMessage",
    label: "推送信息",
    minWidth: 200,
    showOverflowTooltip: false
  },
  {
    prop: "requestParam",
    label: "请求参数",
    minWidth: 120
  },
  {
    prop: "responseBody",
    label: "返回参数",
    minWidth: 120
  },
  {
    prop: "requestUrl",
    label: "请求地址",
    minWidth: 120
  },
  {
    prop: "successedStatus",
    label: "推送状态",
    minWidth: 100
  },
  {
    prop: "createDate",
    label: "推送时间",
    minWidth: 140
  }
]);

const viewLog = reactive({
  createdIsNeed: false,
  getDataListURL: "/shop/shop/logPush",
  getDataListIsPageSize: true,
  getDataListIsPage: true,
  listRequestMethod: "post",
  dataForm: {
    searchParam: "",
    pushStatus: "",
    shopId: "",
    recordType: 1
  }
});

const stateLog = reactive({ ...useView(viewLog), ...toRefs(viewLog) });
const dialogForm = reactive({
  visible: false,
  title: "",
  content: ""
});
const handleOpen = (title: any, content: any) => {
  dialogForm.visible = true;
  dialogForm.title = title;
  dialogForm.content = content;
};

const handleClose = () => {
  dialogForm.visible = false;
  dialogForm.content = "";
};

// 重置
const getResetting = () => {
  viewLog.dataForm.searchParam = "";
  viewLog.dataForm.pushStatus = "";
  stateLog.getDataList();
};

defineExpose({
  init
});
</script>

<style lang="less" scoped></style>
