<template>
    <el-dialog width="480px" v-model="dialogVisible" modal-class="im-dialog" class="im-custom-dialog" @close="close" :modal="false">
        <template #header="{ close, titleId, titleClass }">
            <div class="im-custom-dialog-header">
                <p class="dialog-title">交易账密提交</p>
            </div>
        </template>
        
        <div class="account-password-dialog-content flx-column">
            <el-form ref="formRef" :model="dataForm" :rules="rules" label-position="top" class="w-100">
                <el-form-item label="游戏账号" prop="gameAccount">
                    <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
                </el-form-item>
                <el-form-item label="游戏密码" prop="gamePassword">
                    <el-input v-model="dataForm.gamePassword" placeholder="请输入游戏密码"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="dataForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
            </el-form>
        </div>

        <template #footer>
            <div class="account-password-dialog-footer">
                <el-button @close="close">取消</el-button>
                <el-button type="primary" @click="submitHandle">提交</el-button>
            </div>
        </template>
        
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, defineExpose, defineEmits } from 'vue';
    
    const emit = defineEmits(['accountPasswordSubmit']);

    const dialogVisible = ref(false);

    const dataForm = ref(<any>{});

    const rules = {
        gameAccount: [
            { required: true, message: '请输入游戏账号', trigger: 'blur' }
        ],
        gamePassword: [
            { required: true, message: '请输入游戏密码', trigger: 'blur' }
        ],
        phone: [
            { required: true, message: '请输入手机号码', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ]
    }

    const init = () => {
        dialogVisible.value = true;
    }

    const formRef = ref();
    const submitHandle = () => {
        formRef.value.validate(async (valid: boolean) => {
            if (!valid) return;
            emit('accountPasswordSubmit', dataForm.value);
            dialogVisible.value = false;
        })
    }


    const close = () => {
        
    }

    defineExpose({
        init
    })
</script>

<style lang="scss">

    .account-password-dialog-content{
        padding: 0 16px 16px 16px;
        align-items: center;
    }

    .account-password-dialog-footer{
        padding: 16px;
    }

</style>