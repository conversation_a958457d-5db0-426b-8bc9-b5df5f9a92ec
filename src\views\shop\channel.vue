<template>
  <div class="TableXScrollSty channelPage">
    <ny-flod-tab
      :list="[
        { label: '回收渠道', value: '1' },
        { label: '销售渠道', value: '0' }
      ]"
      v-model="activeName"
      value="value"
      label="label"
    ></ny-flod-tab>
    <div class="channelBody">
      <div class="channelBody_left">
        <div class="left_top_but">
          <el-button @click="dialogVisible = true">新增渠道分类</el-button>
        </div>
        <div class="left_top_menu">
          <div class="left_top_item" :class="{ active: item.id == menuId }" v-for="(item, index) in menuList" :key="index" @click="menuChange(item.id)" @mouseenter="item.mouseShow = true" @mouseleave="item.mouseShow = false">
            <div class="name">{{ item.className }}</div>
            <div class="operate" v-show="item.mouseShow == true">
              <el-icon size="14" color="var(--el-color-primary)" @click.stop="editChange(item)"><Edit /></el-icon>
              <el-icon size="14" color="var(--el-color-primary)" @click.stop="handleTabsRemove(item.id)"><Delete /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="channelBody_right TableXScrollSty">
        <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
          <template #header>
            <el-button type="primary" v-if="state.hasPermission('channel:channel:save')" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            <!-- <el-button type="danger" v-if="state.hasPermission('channel:channel:delete')" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button> -->
          </template>
          <template #header-right>
            <el-input v-model="state.dataForm.title" placeholder="请输入渠道名称" style="width: 200px" clearable></el-input>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="rechargeChange">{{ $t("resetting") }}</el-button>
          </template>
          <template #channelType="{ row }">
            <span v-if="row.channelType == '0'">出售</span>
            <span v-if="row.channelType == '1'">回收</span>
            <span v-if="row.channelType == '2'">售后</span>
            <span v-if="row.channelType == '3'">合作商出售</span>
          </template>
          <template #state="{ row }">
            <el-switch v-model="row.state" :active-value="0" :inactive-value="1" :loading="row.loading" @change="isShowChange(row)" />
          </template>
          <template #createDate="{ row }">
            <span>{{ formatTimeStamp(row.createDate) }}</span>
          </template>
          <template #updateDate="{ row }">
            <span>{{ formatTimeStamp(row.updateDate) }}</span>
          </template>
          <template #operation="{ row }">
            <el-button v-if="state.hasPermission('channel:channel:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('channel:channel:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
          </template>
        </ny-table>
      </div>
    </div>

    <!-- 列表 - 新增、编辑 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>

    <!-- 渠道分类新增、编辑 -->
    <el-dialog v-model="dialogVisible" :title="dialogInputValue.id ? '编辑' : '新增'" width="500" @close="cancelChange">
      <el-descriptions :column="1" border class="descriptions descriptions-label-140" style="margin-bottom: 12px">
        <el-descriptions-item>
          <template #label><span>渠道分类名称<span style="color: red">*</span></span></template>
          <el-input v-model="dialogInputValue.className" placeholder="请输入"></el-input>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <div>
            <el-button type="danger" plain v-if="dialogInputValue.id" @click="handleTabsRemove(dialogInputValue.id)">删除</el-button>
          </div>
          <div>
            <el-button @click="cancelChange">取消</el-button>
            <el-button type="primary" :loading="addOrEditSubmitLoading" @click="addOrEditSubmit(dialogInputValue.id ? 'edit' : 'add')">保存</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./channel-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const activeName = ref("1");

const menuId = ref("");
const menuList = ref(<any>[]);

const columns = reactive([
  // {
  //   type: "selection",
  //   width: 50
  // },
  {
    prop: "title",
    label: "渠道名称",
    minWidth: "150",
    align: "center"
  },
  {
    prop: "childTitles",
    label: "二级渠道",
    minWidth: "150",
    align: "center"
  },
  {
    prop: "remark",
    label: "渠道说明",
    minWidth: "300",
    align: "center"
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "updateDate",
    label: "修改时间",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "state",
    label: "是否启用",
    minWidth: "80",
    align: "center"
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: "100"
  }
]);

const view = reactive({
  getDataListURL: "/channel/channel/page",
  getDataListIsPage: true,
  deleteURL: "/channel/channel",
  deleteIsBatch: true,
  dataForm: {
    title: "",
    classId: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  if(menuList.value.length == 0){
    ElMessage.warning('请先新增渠道分类');
    return;
  }
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(activeName.value,menuId.value,id);
  });
};

// 是否显示点击事件
const isShowChange = (row: any) => {
  if (row.id) {
    row.loading = true;
    baseService
      .put("/channel/channel", row)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
      })
      .finally(() => {
        row.loading = false;
      });
  }
};

onMounted(() => {
  getChannelClass();
});

// 重置操作
const rechargeChange = () =>{
  state.dataForm.title = "";
  state.getDataList();
}

// 菜单切换
const menuChange = (id:string) =>{
  menuId.value = id;
  state.dataForm.classId = id;
  state.getDataList();
}

// 获取渠道分类
const getChannelClass = () => {
  baseService.get("/channel/channelClass/list", { type: activeName.value }).then((res) => {
    if (res.code == 0) {
      menuList.value = res.data;
      if(!menuId.value){
        menuId.value = menuList.value && menuList.value.length ? menuList.value[0].id : '';
        state.dataForm.classId = menuList.value && menuList.value.length ? menuList.value[0].id : '';
      }
      state.getDataList();
    }
  });
};

// 新增/编辑页签
const dialogVisible = ref(false);
const dialogInputValue = ref({
  id: "",
  className: ""
});

// 新增/编辑保存
const addOrEditSubmitLoading = ref(false);
const addOrEditSubmit = (type: string) => {
  if (!dialogInputValue.value.className) {
    ElMessage.warning("请输入渠道分类名称");
    return;
  }
  addOrEditSubmitLoading.value = true;
  (type == "add" ? baseService.post : baseService.put)("/channel/channelClass", {
    ...dialogInputValue.value,
    type: activeName.value
  })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
        getChannelClass();
        dialogVisible.value = false;
      }
    })
    .finally(() => {
      addOrEditSubmitLoading.value = false;
      cancelChange();
    });
};

// 编辑
const editChange = (e: any) => {
  Object.assign(dialogInputValue.value, e);
  dialogVisible.value = true;
};

// 删除标签页
const handleTabsRemove = (e: any) => {
  ElMessageBox.confirm("确定删除该数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    baseService.delete("/channel/channelClass", [e]).then((res) => {
      if (res.code == 0) {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
        menuId.value = "";
        getChannelClass();
        dialogVisible.value = false;
      }
    });
  });
};

// 取消关闭按钮
const cancelChange = () =>{
  dialogInputValue.value.id = "";
  dialogInputValue.value.className = "";
  dialogVisible.value = false;
}

// 监听tab切换，刷新渠道分类
watch(
  ()=>activeName.value,
  (newValue)=>{
    console.log('变化了');
    menuId.value = "";
    getChannelClass();
  }
)

</script>

<style lang="less" scoped>
.channelPage {
  padding: 0px 24px;
}
.channelBody {
  display: flex;
  gap: 12px;
  .channelBody_left {
    width: 186px;
    height: calc(100vh - 228px);
    border-radius: 4px;
    border: 1px solid #ebeef5;
    .left_top_but {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .left_top_menu {
      .left_top_item {
        padding: 18px 12px 17px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .name {
          font-weight: 400;
          font-size: 12px;
          color: #303133;
          width: 100px;
          line-height: 22px;
        }
        .operate {
          display: flex;
          gap: 12px;
        }
        &:hover {
          .name {
            color: var(--el-color-primary);
          }
          background-color: var(--el-color-primary-light-9);
        }
      }
      .active {
        .name {
          color: var(--el-color-primary);
        }
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
  .channelBody_right {
    flex: 1;
    // border: 1px solid red;
  }
}
.TableXScrollSty {
  :deep(.NYpagination) {
    right: 48px;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
