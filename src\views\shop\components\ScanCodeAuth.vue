<template>
    <!-- 扫码授权 自动勾选游戏属性 -->
    <div class="sancode-check" v-if="gameCode && buttonPermissions[gameCode]">
        <div class="auth-text">
            <el-descriptions :column="1" border class="descriptions descriptions-label-140" v-if="gameCode == 'YS'">
                <el-descriptions-item>
                    <template #label><span>账号</span></template>
                    <el-input v-model="dataYs.account" placeholder="请输入账号" />
                </el-descriptions-item>
                <el-descriptions-item>
                    <template #label><span>密码</span></template>
                    <el-input v-model="dataYs.password" placeholder="请输入密码" />
                </el-descriptions-item>
            </el-descriptions>
            <div class="right">
                <template v-if="gameCode == 'YS'">
                    <div style="display: flex;align-items: center;margin-top:12px;">
                        <slot name="button"></slot>
                        <el-button type="primary" :loading="oneClickAccessLoading" @click="oneClickAccess">一键获取</el-button>
                    </div>
                </template>
                <template v-else>
                    <div style="display: flex;align-items: center;margin-top:12px;">
                        <template v-if="!gameRoleList.length">
                            <el-button type="primary" v-if="buttonPermissions[props.code].includes('qq')" @click="qrcodeAuthHandle('qq')">QQ授权</el-button>
                            <el-button type="success" v-if="buttonPermissions[props.code].includes('wx')" @click="qrcodeAuthHandle('wx')">微信授权</el-button>
                        </template>
                        <slot name="button"></slot>
                        <el-button 
                            type="success" 
                            v-if="(gameRoleList.length || ScannedCode) && buttonPermissions[props.code].includes('screenshot')" 
                            :loading="screenshotLoading"
                            @click="screenshotHandle"
                        >
                            一键截图
                        </el-button>
                        
                        <div class="recognition-fail flx-center mr-24" v-if="unrecognizedList && unrecognizedList.length" @click="showFailureAttribute">
                            <el-icon color="#FF7D00"><WarningFilled /></el-icon>
                            <el-link type="warning" style="color: #FF7D00;">查看识别失败属性</el-link>
                        </div>
                        <div class="recognition-fail" v-if="!gameRoleList.length">
                            <el-icon color="#FF7D00"><WarningFilled /></el-icon>
                            <text>先扫码授权，再选择角色</text>
                        </div>
                    </div>
                    
                </template>
            </div>
        </div>


        <!-- 账号授权 -->
        <el-dialog v-model="showQrcode" title="扫码授权" width="480px" :close-on-click-modal="false" @close="closeQrcode">
            <div class="qrcode-content">
                <div class="qrcode-auth">
                    <div class="qrcode-wrap f-c-c" v-loading="!qrcodeUrl">
                        <img :src="qrcodeUrl" v-if="qrcodeUrl">
                    </div>
                    <div class="qrcode-text">请使用{{ authType == 'qq' ? 'QQ' : '微信' }}扫码授权</div>
                    <div class="qrcode-text-tip">请注意，仅限使用设备对二维码进行扫描以完成授权</div>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script setup lang="ts"> 
    import { ref, watch, defineProps, defineEmits } from 'vue';
    import { WarningFilled } from '@element-plus/icons-vue';
    import { ElMessage } from 'element-plus';
    import baseService from "@/service/baseService";

    const props = defineProps({
        code: String,
        gtype: String,
        gameRoleList: Array,
        screenshotLoading: Boolean,
        buttonPermissions: Boolean
    })

    const emit = defineEmits(['confirm', 'showFailureAttributeEmit', 'screenshotHandle']);

    // 游戏code
    const gameCode = ref('');
    // 游戏类型 1 手游  2端游
    const gameType = ref();

    // 显示扫码授权
    const showQrcode = ref(false);
    // 授权类型
    const authType = ref('qq');
    const qrcodeAuthHandle = (type: string) => {
        authType.value = type;
        showQrcode.value = true;
        getAuthQrcode();
        // emit("confirm", {}, gameCode.value);
    }

    // 二维码地址
    const qrcodeUrl = ref('');

    // 是否扫码成功
    const ScannedCode = ref(false);

    // 获取授权二维码
    const qrcodeResultId = ref();
    const qrcodeTimer = ref();
    const getAuthQrcode = () => {
        qrcodeUrl.value = '';
        baseService.get("/autoCheck/autoCheck/getQrCode", { type: authType.value, gameCode: gameCode.value }).then((res: any) => {
            if(res.code == 0){
                let { resultId, base64Data } = res.data;
                qrcodeUrl.value = 'data:image/*;base64,'+base64Data;
                qrcodeResultId.value = resultId;

                checkAuthStatus();
            }
        }).catch(() => {
            closeQrcode()
        })

        // let data = {
        //     "userId": "f9c370a0-8347-47d6-9a2a-4162b7fe55c0",
        //     "openId": "347-47d6-9a2a-4162b7fe55c0_10011",
        //     "token": null,
        //     "accessToken": "87_i_RdfxV0MnKIs4K8NgJKUUBngvSQaUfCYe7JVefvtZvVtZaIoZpXDx0zecJrnts2KN5Kr8mIt1vM-C2qkuh7xRU6pVkm3ydSS2ELwLIxRPA",
        //     "wt": "A236175097CD105E17F748A216075B8A19BE6061E55C0088F77C7430DF8871E38B841686A9AABB98CC271E2C25EB0FA4187A4BE93C811701D1F4478799A3CD72D78ACE82496CEE56D6F33EFF1C825945150F773429E73D66F8ADF25DC920BD99976F916EA54BE6B8D06169BD44679AE2AFF8D30C9FA4A09885BFFD6C56B124949F483FBFE55BD75406C3074641D9FB382B619D850D642D266E437CADD93524AAFD5EEF8D6F8F3FDC06EA94EB6806131D"
        // }

        // 和平精英
//         let data ={
//   "gameCode": "WWQY",
//   "roleIdList": [
//     ""
//   ],
//   "userId": "JA-98ed0d4cc0304755-9194df89fa97c627",
//   "openId": "AE6D4A9673503AB586492CDAD93A12E3",
//   "token": null,
//   "accessToken": "63B22910493B183EDDCB504140D832C8",
//   "wt": null,
//   "clientType": "9",
//   "uin": "o1081650926",
//   "appId": "*********",
//   "accType": "qc",
//   "accountType": "5",
//   "tid": "2FFE69AAA41EDAFB592CF9A5A1F13CC7F5539EDFA9E0DC752AAAC726BC0D43849DA0EDF331D297DD3182703721A1A7EA87C75140F8209F105475E4582583CC64A860F3C58A74FCF7E0B338C9586EEE0DE640AF67351D423884FAEB673DB5BA0EF00791B70E6E8B13A88FAD5EA77E54C560C8E67884D84EF64E95064FAE94A95D705894D704330A0488AF1926F10E7330468BBD0CE712035F11F29738038B41FACA32572D784BE029BA5032DD2889FE59",
//   "scene": "v3_sJUHrAIELSAJyy_xkojaUdGzEV6oSuiKlVwyrzDMJ-dweVX9rFeZ0E_lJ8TRP0Omn9u6wlWDaf_dvObcWWOfwQd55grVbqwYRIuGdfUxN5Uqhh8rj0j1d4FpZRjvdqTFQZBHqp9Zbbe4jRWFdOQUIZ8p1mtKnI5VS1gxT6xEGh6rRoJ-4f4KrF0IVW5IyPwdY6re231tDGvMWof-oyFKeg=="
// }
// emit("confirm",data, gameCode.value, authType.value);

        qrcodeTimer.value = setTimeout(() => {
            getAuthQrcode();
        }, 60 * 1000);

    }

    // 查询扫描二维码结果
    const timer = ref();
    const checkAuthStatus = () => {
        clearTimeout(timer.value);
        baseService.get("/autoCheck/autoCheck/getQrCodeResult", { 
            resultId: qrcodeResultId.value,
            type: authType.value,
            gameCode: gameCode.value
        }, ).then((res: any) => {
            if(res.code == 0){
                if(res.data.userId || res.data.openId){
                    ElMessage.success("授权成功");
                    emit("confirm", res.data, gameCode.value, authType.value);
                    closeQrcode();

                    // 没有角色游戏单独判断
                    if(['WWQY'].includes(gameCode.value)){
                        ScannedCode.value = true;
                    }
                    
                }else{
                    timer.value = setTimeout(() => {
                        checkAuthStatus();
                    }, 2000);
                }
            }
        })
    }

    // 关闭二维码弹窗
    const closeQrcode = () => {
        showQrcode.value = false;
        clearTimeout(timer.value);
        clearTimeout(qrcodeTimer.value);
    }


    // 查看失败属性
    const showFailureAttribute = () => {
        // attributeRecognitionResultRef.value.init(props.unrecognizedList);
        emit("showFailureAttributeEmit");
    }

    // 一键截图
    const screenshotHandle = () => {
        emit("screenshotHandle");
    }


    // 原神获取数据
    const dataYs = ref({
        account: '',
        password: '',
    })
    const oneClickAccessLoading = ref(false);
    const oneClickAccess = () =>{
        oneClickAccessLoading.value = true;
        baseService.post("/autoCheck/autoCheck/login",{gameCode:gameCode.value,...dataYs.value}).then(res=>{
            if(res.code == 0){
                ElMessage.success("授权成功");
                console.log(res.data);
                
                emit("confirm", res.data, gameCode.value, authType.value);
            }
        }).finally(()=>{
            oneClickAccessLoading.value = false;
        })
    }

    watch(() => props.code, (val: any) => {
        gameCode.value = val;
    }, { immediate: true })
    watch(() => props.gtype, (val: any) => {
        gameType.value = val;
    }, { immediate: true })
    


</script>

<style lang="scss" scoped>
    .sancode-check{
        // margin-top: 12px;

        .auth-text{
            width: 100%;
            // height: 67px;
            // background: url('../../../assets/images/attributes_auth_bg.png') center no-repeat;
            // background-size: 100% 100%;
            // padding: 14px 24px;
            // border: 1px solid red;
            
            .left{
                line-height: 18px;
                .title{
                    font-weight: bold;
                    padding-bottom: 4px;
                }

                .desc{
                    color: #909399;
                }
            }

            .recognition-fail{
                color: var(--el-color-primary);
                display: flex;
                align-items: center;
                margin-left: 12px;

                text{
                    font-weight: 400;
                    font-size: 13px;
                    line-height: 22px;
                    color: #FF7D00;
                    margin-left: 4px;
                }

            }
        }
    }

    .qrcode-content{
        width: 180px;
        text-align: center;
        margin: 58px auto;

        .qrcode-wrap{
            width: 180px;
            height: 180px;
            border: 1px solid #EBEEF5;
            border-radius: 8px;

            img{
                width: 170px;
                height: 170px;
                background: #eee;
            }
        }

        .qrcode-text{
            font-size: 15px;
            color: #2B2D3E;
            line-height: 21px;
            padding: 8px 0;
        }

        .qrcode-text-tip{
            font-size: 12px;
            color: var(--el-color-primary);
            line-height: 18px;
            padding: 8px 0;
        }
    }
    :deep(.el-descriptions__body) {
        display: flex;
        justify-content: space-between;
        tbody {
            display: flex;
            flex-direction: column;

            tr {
                display: flex;
                flex: 1;
                .el-descriptions__label {
                display: flex;
                align-items: flex-start !important;
                font-weight: normal;
                width: 144px;
                }
                .el-descriptions__content {
                    display: flex;
                    align-items: center;
                    min-height: 48px;
                    flex: 1;
                > div {
                    width: 100%;
                }
                .el-form-item__label {
                    display: none;
                }
                .el-form-item {
                    margin-bottom: 0;
                }
                }
                .noneSelfRight {
                    border-right: 0 !important;
                }
                .noneSelfLeft {
                    border-left: 0 !important;
                }
                .noneSelfLabel {
                    background: none;
                    border-left: 0 !important;
                    border-right: 0 !important;
                }
            }
        }
    }
</style>