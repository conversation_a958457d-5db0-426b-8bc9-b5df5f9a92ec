<template>
  <div class="notice_cart">
    <div class="above">
      <div class="left">
        <span class="title">消息通知</span>
      </div>
      <div style="cursor: pointer" class="right" @click="onClickMessage">
        <div class="toPage">
          查看更多<el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <!-- 显示五条数据 -->
    <div class="notice_list">
      <div v-if="msgList.length == 0" style="display: flex; align-items: center; justify-content: center; width: 100%">
        <ny-no-data type="3" description="暂无消息" />
      </div>
      <template v-else>
        <div @click="getRouteUrl(item.type)" style="cursor: pointer" class="notice_list_item" v-for="item in msgList" :key="item.type">
          <div :class="getMsgClass(item.type, true)">{{ getMsgClass(item.type, false) }}</div>
          <div class="notice_title sle">{{ getRealName(item.message) }}</div>
        </div></template
      >
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";
import { onMounted, onUnmounted, reactive, ref, watch, watchEffect, computed } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import commonData from "@/views/desk/index.ts";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { formatDate, formatCurrency, Local } from "@/utils/method";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store";

const store = useAppStore();
const imStore = useImStore();

const getMsgClass = (type: any, isType: any) => {
  let allType = ["CU:cmdBargain", "CU:cmdPaidExpire,CU:cmdAPIExpire", "CU:cmdNotice,CU:cmdApiOpen,CU:cmdPaidOpen,CU:cmdBalance,CU:cmdRecharge", "CU:cmdContractPurchase,CU:cmdContractSale", "CU:cmdRetrieve", "CU:cmdSale"];
  let className = ["notice_sign_priamry", "notice_sign_yellow", "notice_sign_green", "notice_sign_orange", "notice_sign_red", "notice_sign_blue"];
  let titleName = ["议价审核", "付费提醒", "系统消息", "合同签署", "账号找回", "商品售出"];
  let typeIndex = allType.findIndex((ele) => ele.includes(type));
  return isType ? className[typeIndex] : titleName[typeIndex];
};
const router = useRouter();
const getRouteUrl = (type: any) => {
  let allType = ["CU:cmdAPIExpire", "CU:cmdPaidExpire", "CU:cmdApiOpen", "CU:cmdPaidOpen", "CU:cmdBalance", "CU:cmdRecharge", "CU:cmdSale", "CU:cmdBargain", "CU:cmdContractSale", "CU:cmdContractPurchase", "CU:cmdRetrieve"];
  let routeArr = ["/plugin/api/index", "/plugin/paid/paidfunction", "/plugin/api/index", "/plugin/paid/paidfunction", "/bill/specification", "/bill/specification", "/order/sell/index", "/shop/bargain/index", "/order/sell/index", "/order/acquisition/index", "/utilityTools/shopAccountFound/index"];
  let typeIndex = allType.findIndex((ele) => ele == type);
  jumpRoute(routeArr[typeIndex]);
};
const jumpRoute = (url: any, inPage = true) => {
  inPage ? router.push(url) : window.open(url);
};
const getRealName = (msg: string) => {
  return msg.replaceAll("nickname", store.state.user.nickname).replace(/<[^>]+>/g, '');
};

const msgList = computed(() => imStore.systemMessageList);

const onClickMessage = () => {
  imStore.showMessageNotification = true;
};
</script>

<style lang="less" scoped>
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.notice_cart {
  padding: 12px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  .notice_list {
    .notice_list_item {
      display: flex;
      align-items: center;
      margin-top: 12px;
      .notice_sign_priamry {
        background: var(--el-color-primary-light-9);
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: var(--el-color-primary);
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_sign_yellow {
        background: #fcf6ec;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #e6a23c;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_sign_green {
        background: #e8fffb;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #0fc6c2;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_sign_orange {
        background: #fff3e8;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #f77234;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_sign_red {
        background: #fef0f0;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #f56c6c;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_sign_blue {
        background: #e8f3ff;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #4165d7;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 0px 8px;
        margin-right: 4px;
      }
      .notice_title {
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        flex: 1;
      }
    }
  }
}
</style>