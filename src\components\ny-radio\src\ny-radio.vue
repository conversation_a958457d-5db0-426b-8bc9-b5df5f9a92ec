<template>
  <el-radio-group v-model="value" @change="$emit('update:modelValue', $event)" class="radioGroups">
    <el-radio v-for="(data, index) in dataList" :key="index" :label="data.value" class="radios">
      <span class="radioText">{{ data.label }}</span>
    </el-radio>
  </el-radio-group>
</template>
<script lang="ts">
import { computed, defineComponent } from "vue";
export default defineComponent({
  name: "NyRadio",
  props: {
    modelValue: [Number, String],
    datas: Object
  },
  setup(props) {
    return {
      value: computed(() => `${props.modelValue}`),
      dataList: props.datas
    };
  }
});
</script>


<style lang="less" scoped>
.radioGroups {
  width: 100%;
  margin-top: -15px;
  display: flex;
  align-items: center;
  flex-flow: wrap;
  box-sizing: border-box;
  overflow: hidden;
}

.radios {
  min-width: 95px;
  max-width: 200px;
  height: auto;
  min-height: 32px;
  padding: 0;
  margin: 15px 15px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F2F2;
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid #fff;
  position: relative;
  &.is-checked {
    background-color: #fff;
    border-color: var(--el-color-primary);
    &::after {
      content: "";
      width: 22px;
      height: 22px;
      position: absolute;
      bottom: 0;
      right: 0;
      background-image: url("../../../assets/images/选中-签.png");
      background-size: 100% 100%;
    }
  }
}

:deep(.el-radio__input) {
  width: 0px;
  opacity: 0;
}

:deep(.el-radio__label) {
  padding-left: 0px;

  .radioText {
    display: inline-block;
    padding: 0 15px;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 32px;
    word-break: break-all;
    white-space: normal;
  }
}
</style>