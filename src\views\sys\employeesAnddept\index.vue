<template>
  <!-- 售后订单 -->
  <div class="container sell-order-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          {
            id: 0,
            title: '员工管理'
          },
          {
            id: 1,
            title: '部门管理'
          }
        ]"
        v-model="dataForm.type"
        value="id"
        label="title"
      ></ny-flod-tab>
      <div>
        <!-- pParams 所有参数都在，如需要特殊处理，去对应的组件内参数接收的地方处理 -->
        <user v-if="dataForm.type == '0'" />
        <dept v-if="dataForm.type == '1'" />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
import user from "../user.vue";
import dept from "../dept.vue";
// 传参
const dataForm = reactive({
  type: 0 // 顶部类型
});
</script>

<style lang="scss" scoped>
.stat_card {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
  .stat_item {
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    height: 120px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: 1px solid #e5e6eb;
    .stat_top {
      padding: 12px;
      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }
    .stat_middle {
      padding: 4px 16px;
      font-weight: 500;
      font-size: 20px;
      color: #303133;
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .stat_below {
      display: flex;
      align-items: center;
      padding: 4px 16px 12px 16px;
      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
