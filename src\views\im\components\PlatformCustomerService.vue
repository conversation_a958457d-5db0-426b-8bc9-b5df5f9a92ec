<template>
    <div class="contact-tree-wrap">
        
        <div class="member-list">
            <div 
                class="member-item flx-justify-between" 
                :class="{ active: currentActive == user.id, hide: user.id == appStore.state.user.id }"
                v-for="(user, uIndex) in memberList"
                :key="uIndex"
                @click="memberItemHandle(user, user.name)"
            >
                <div class="member flx">
                    <el-image :src="user.headUrl" class="avatar"></el-image>
                    <div class="name">{{ user.nickname || user.realName }}</div>
                </div>
                <div class="message" @click="initiatedCOnversationHandle(user)">
                    <el-icon size="16"><ChatDotRound /></el-icon>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';
import { CaretRight, ChatDotRound } from '@element-plus/icons-vue';
import { useImStore } from '@/store/im';
import { useAppStore } from '@/store';
import { getConversation } from '@/utils/imTool';
import baseService from '@/service/baseService';

const imStore = useImStore();
const appStore = useAppStore();

const memberList = ref([]);

const getList = () => {
    baseService.post('/im/login/partner/contacts').then(res => {
        if(res.code == 0){
            memberList.value = res.data;
        }
    })
}

getList();

// 当前已选定的联系人
const currentActive = ref("");

// 点击联系人查看详情
const memberItemHandle = (user: any, deptName: string) => {
    currentActive.value = user.id;
    imStore.showContactInfo = true;
    user.deptName = deptName;
    imStore.currentContactInfo = user;
}

// 发起会话
const initiatedCOnversationHandle = (user: any) => {
    if(!user.imUid) return;
    getConversation(user.imUid).then((res: any) => {
        res.sessionData = user;
        imStore.currentConversation = res;
        imStore.showContactInfo = false;
        imStore.currentConversationType = '';
    })
}


</script>

<style lang="scss" scoped> 
    .contact-tree-wrap{
        
        .member-list{

            .member-item{
                display: flex;
                align-items: center;
                height: 44px;
                line-height: 44px;
                cursor: pointer;
                padding-left: 20px;
                padding-right: 10px;

                &.hide{
                    display: none;
                }
                
                &:hover{
                    background: #E6E6E6;
                    color: var(--el-color-primary);

                    .message{
                        display: inline-block;
                    }
                }

                &.active{
                    background: var(--el-color-primary);
                    color: #fff;
                    .message{
                        display: inline-block;
                    }
                }

                .member{
                    height: 44px;
                    align-items: center;
                }

                .avatar{
                    width: 24px;
                    height: 24px;
                    border-radius: 4px;
                    background: #fff;
                }

                .name{
                    margin-left: 10px;
                }

                .message{
                    display: none;
                }
            }
        }
    }
</style>