<template>
  <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" width="1018" class="function-opened">
    <template #header="{ close }">
      <div class="my-header flx-justify-between">
        <div class="title bold">{{ isRenewal ? "续费功能" : "开通功能" }}</div>
        <div class="close pointer" @click="visible = false">
          <el-icon size="16">
            <Close />
          </el-icon>
        </div>
      </div>
    </template>
    <div class="dialog-content flx" v-loading="dataLoading">
      <el-scrollbar class="left flx-1" style="height: 380px">
        <div style="min-height: 380px; display: flex; flex-direction: column; justify-content: space-between">
          <div>
            <div class="p-title-line mb-8">选择功能</div>
            <div class="paidInfo flx mb-24">
              <div class="image-wrap flx-center">
                <el-image v-if="dataForm.coverImg" class="image" :src="dataForm.coverImg" fit="cover" />
              </div>
              <div class="txt">
                <div class="fun-name bold">{{ dataForm.name }}</div>
                <div class="desc">{{ dataForm.introduce }}</div>
              </div>
            </div>
          </div>
          <!-- <div class="xstip" v-if="isAutomaticNumbering(dataForm.name)">
                    <el-icon color="#E6A23C" size="16" style="margin-right: 5px;"><WarnTriangleFilled /></el-icon>
                    <span>{{dataForm.introduce}}</span>
                </div> -->

          <div>
            <div class="p-title-line">收费方式</div>
            <div class="item-card-list mb-24 flx flx-nowrap">
              <template v-if="isAutomaticNumbering(dataForm.name)">
                <div class="item-card flx-column flx-center active">
                  <el-text class="item-name bold" type="primary">安卓系统</el-text>
                  <div class="font-size-small">
                    <el-text type="danger">30星币</el-text>
                  </div>
                </div>
                <div class="item-card flx-column flx-center active">
                  <el-text class="item-name bold" type="primary">苹果系统</el-text>
                  <div class="font-size-small">
                    <el-text type="danger">35星币</el-text>
                  </div>
                </div>
                <div class="item-card flx-column flx-center active">
                  <el-text class="item-name bold" type="primary">PC系统</el-text>
                  <div class="font-size-small">
                    <el-text type="danger">40星币</el-text>
                  </div>
                </div>
              </template>
              <template v-else>
                <!-- im 流量包  收费方式 -->
                <div class="item-card flx-column flx-center" :class="{ active: item.name == dataForm.imOptionName }" v-if="dataForm.type == 4" v-for="(item, index) in dataForm.paidFunctionOptions.activeOption" :key="index" @click="dataForm.imOptionName = item.name">
                  <el-text class="item-name bold" :type="item.name == dataForm.imOptionName ? 'primary' : ''">{{ item.name }}</el-text>
                  <div class="font-size-small" v-if="item.price">
                    <el-text type="danger">{{ item.price }}星币</el-text>
                    /月
                  </div>
                </div>

                <div class="item-card flx-column flx-center active" v-else>
                  <el-text class="item-name bold" type="primary">{{ getDictLabel(store.state.dicts, "paid_function_charging_method", dataForm.chargingMethod) }}</el-text>
                  <div class="font-size-small" v-if="dataForm.chargingMethod == 1 || dataForm.chargingMethod == 2">
                    <el-text type="danger">{{ dataForm.price }}星币</el-text>
                    /每{{ dataForm.chargingMethod == "2" ? "月" : "次" }}
                  </div>
                </div>
              </template>
            </div>
          </div>

          <div v-if="dataForm.chargingMethod">
            <div class="p-title-line" v-if="dataForm.chargingMethod != '3'">购买数量</div>

            <!-- im 流量包 -->
            <div class="item-card-list flx flx-nowrap" v-if="dataForm.type == 4">
              <div class="item-card flx-column flx-center" v-for="(item, index) in imFlowPackageCount" :key="index" :class="{ active: item.value == dataForm.imOptionType }" @click="dataForm.imOptionType = item.value">
                <el-text class="item-name bold" :type="item.value == dataForm.imOptionType ? 'primary' : ''">{{ item.label }}</el-text>
                <div class="discount" v-if="item.value == 0 && imFlowPackageDiscount.discount && imFlowPackageDiscount.discount != 10">{{ imFlowPackageDiscount.discount }}折</div>
                <div class="discount" v-if="item.value == 2 && imFlowPackageDiscount.continuityDiscount && imFlowPackageDiscount.continuityDiscount != 10">{{ imFlowPackageDiscount.continuityDiscount }}折</div>
              </div>
            </div>

            <div class="item-card-list flx flx-nowrap" v-else-if="(dataForm.chargingMethod == '1' || dataForm.chargingMethod == '2') && dataForm.paidFunctionOptions">
              <div class="item-card flx-column flx-center" v-for="(item, index) in dataForm.paidFunctionOptions[dataForm.chargingMethod == '1' ? 'countOption' : 'monthOption']" :key="index" :class="{ active: item.value == dataForm.amount }" @click="dataForm.amount = item.value">
                <!-- 连续包月  value == 0 && dataForm.chargingMethod == "2 -->
                <el-text v-if="item.value == 0 && dataForm.chargingMethod == '2'" class="item-name bold" :type="item.value == dataForm.amount ? 'primary' : ''"> 连续包月 </el-text>

                <el-text v-else class="item-name bold" :type="item.value == dataForm.amount ? 'primary' : ''">
                  {{ item.value }}
                  {{ dataForm.chargingMethod == "2" ? "个月" : dataForm.chargingMethod == "1" ? "次" : "" }}
                </el-text>
                <div class="discount" v-if="item.discount && item.discount != 10 && item.discount != 100 && item.discount > 0">{{ item.discount / 10 }}折</div>
              </div>
            </div>
            <!-- <div class="item-card-list flx flx-nowrap" v-else-if="dataForm.chargingMethod == '3'">
						<div class="item-card flx-column flx-center active">
							<el-text class="item-name bold" type="primary">1次</el-text>
						</div>
					</div> -->
          </div>
          <div v-else>
            <div class="p-title-line">试用方式</div>
            <div class="item-card-list flx flx-nowrap">
              <div class="item-card flx-column flx-center active">
                <el-text class="item-name bold" type="primary">
                  {{ trialsNumber }}
                </el-text>
              </div>
            </div>
          </div>

          <!-- 小算游戏 -->
          <div v-if="dataForm.name && dataForm.name.includes('自动上号')">
            <div class="p-title-line">支持游戏</div>
            <el-table :data="tableData" style="width: 100%; margin-top: 8px" border>
              <el-table-column prop="name" label="游戏名称" align="center" width="180" />
              <el-table-column prop="system1" label="安卓" align="center">
                <template #default="{ row }">
                  <el-icon color="var(--el-color-primary)" size="18" v-if="row.system1">
                    <SuccessFilled />
                  </el-icon>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="system2" label="苹果" align="center">
                <template #default="{ row }">
                  <el-icon color="var(--el-color-primary)" size="18" v-if="row.system2">
                    <SuccessFilled />
                  </el-icon>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="system3" label="PC" align="center">
                <template #default="{ row }">
                  <el-icon color="var(--el-color-primary)" size="18" v-if="row.system3">
                    <SuccessFilled />
                  </el-icon>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-scrollbar>

      <div class="right">
        <div class="p-title-line mb-8">订单详情</div>
        <div class="info-card mb-12">
          <div class="info-item">
            <div class="label">订单类型：</div>
            <div class="value">消费</div>
          </div>
          <div class="info-item">
            <div class="label">开通方式：</div>
            <div class="value">
              {{ getDictLabel(store.state.dicts, "paid_function_charging_method", dataForm.chargingMethod) }}
            </div>
          </div>
          <div class="info-item">
            <div class="label">开通数量：</div>
            <div class="value" v-if="dataForm.type == 4">
              {{ dataForm.imOptionType == 0 ? "1个月" : "连续包月" }}
            </div>
            <div class="value" v-else-if="dataForm.chargingMethod == '1' || dataForm.chargingMethod == '2'">{{ dataForm.amount }}{{ dataForm.chargingMethod == "2" ? "个月" : "次" }}</div>
            <div class="value" v-else-if="dataForm.chargingMethod == '3'">1次</div>
            <div class="value" v-else-if="!dataForm.chargingMethod">{{ trialsNumber }}</div>
          </div>
        </div>
        <div class="info-card flx-justify-between mb-12">
          <div class="info-item">
            <div class="label">当前账户余额：</div>
            <el-text type="danger" class="bold">{{ BigNumber(dataForm.balance) }}星币</el-text>
            <div class="label flx-center ml-25" v-if="insufficientBalance">
              <el-icon size="14" class="mr-5 ml-10"> <Warning /> </el-icon>余额不足
            </div>
          </div>
          <el-button style="width: 80px" plain type="primary" @click="toBillBalance">充值</el-button>
        </div>
        <div class="info-card mb-12">
          <div class="flx-justify-between">
            <div class="info-item">
              <div class="label">订单金额：</div>
              <div class="label flx-align-center" v-if="dataForm.type == 4">
                <el-tooltip :content="`${dataForm.price} * ${dataForm.amount} ${discountCompute} = ${dataForm.chargingMethod == '3' ? dataForm.price : total}`" placement="top">
                  <template #content>
                    单价({{ imPrice.price }}){{ imPrice.discount && imPrice.discount != 0 && imPrice.discount != 1 ? " * 折扣(" + imPrice.discount + ")" : "" }} = {{ imPrice.resultPrice }}星币
                    <br />
                    总计：{{ imPrice.resultPrice || dataForm.price }}星币
                  </template>
                  <el-icon size="14" class="mr-5 ml-10">
                    <Warning />
                  </el-icon>
                </el-tooltip>
                明细
              </div>

              <div class="label flx-align-center" v-else-if="dataForm.chargingMethod">
                <el-tooltip placement="top">
                  <template #content>
                    单价({{ dataForm.price }}) * 数量({{ dataForm.amount || 1 }}){{ discountCompute && discountCompute != 0 && discountCompute != 1 ? " * 折扣(" + discountCompute + ")" : "" }} = {{ dataForm.chargingMethod == "3" ? dataForm.price : total }}星币
                    <br />
                    总计：{{ dataForm.chargingMethod == "3" ? dataForm.price : total != 0 ? total : dataForm.price }}星币
                  </template>
                  <el-icon size="14" class="mr-5 ml-10">
                    <Warning />
                  </el-icon>
                </el-tooltip>
                明细
              </div>
            </div>
            <el-text type="danger" class="bold" v-if="!dataForm.chargingMethod">免费</el-text>
            <el-text type="danger" class="bold" v-else-if="dataForm.type == 4">{{ imPrice.resultPrice }}星币</el-text>
            <el-text type="danger" class="bold" style="font-size: 20px" v-else>{{ dataForm.chargingMethod == "3" ? dataForm.price : total != 0 ? total : dataForm.price }}星币</el-text>
          </div>
          <div class="info-item">
            <div class="label">付款成功后，可在已开通功能中查看</div>
          </div>
        </div>

        <el-button class="open-btn" type="primary" :loading="btnLoading" @click="openPaymentFunction">{{ isRenewal ? "立即续费" : "立即开通" }}</el-button>
      </div>
    </div>
  </el-dialog>
  <!-- 充值 -->
  <!-- <rechargeDialog ref="rechargeDialogRef" @refreshDataList="getBalance" /> -->
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits, defineExpose, reactive, nextTick } from "vue";
import { ArrowUp, ArrowDown, Warning } from "@element-plus/icons-vue";
import { timeFormat, getDictLabel } from "@/utils/utils";
import { useAppStore } from "@/store";
import { BigNumber } from "bignumber.js";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import rechargeDialog from "@/components/rechargeDialog/index.vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";

const router = useRouter();

const emit = defineEmits(["refreshDataList"]);
const store = useAppStore();

const visible = ref(false);
const dataFormRef = ref();

const dataForm = ref(<any>{});

// 试用数量
const trialsNumber = ref("");

// 指定游戏
const designatedGame = ref("");

// 是否续费
const isRenewal = ref(false);

// 是否余额不足
const insufficientBalance = ref(false);

const tableData = [
	{
		name: '穿越火线-枪战王者',
		system1: true,
		system2: true,
		system3: false
	},
	{
		name: '使命召唤手游',
		system1: true,
		system2: false,
		system3: false
	},
	{
		name: '三角洲行动',
		system1: false,
		system2: false,
		system3: true
	},
	{
		name: '永劫无间',
		system1: false,
		system2: false,
		system3: true
	},
]

// im 流量包 购买数量
const imFlowPackageCount = ref([
  { value: 0, label: "1个月" },
  { value: 2, label: "连续包月" }
]);

// 是否显示详情
const showDetail = ref(false);
const dataLoading = ref(false);
const init = async (id: number | string, type: string) => {
  dataLoading.value = true;
  visible.value = true;
  if (type == "renewal") isRenewal.value = true;
  let balanceData = await baseService.get("/wallet/bill/balance");
  let res = await baseService.get("/paid/function/info", { id, platform: import.meta.env.VITE_PLATFORM_MODE });
  dataLoading.value = false;
  Object.assign(dataForm.value, res.data);
  let data_ = { ...res.data.paidFunctionOptions, balance: balanceData.data };
  delete data_.id;
  Object.assign(dataForm.value, data_);
  let { countOption, monthOption, activeOption, gameList, trialOption, trialType } = dataForm.value.paidFunctionOptions ? dataForm.value.paidFunctionOptions : { countOption: [] };

  if (dataForm.value.paidFunctionOptions) {
    dataForm.value.paidFunctionOptions = {
      ...dataForm.value.paidFunctionOptions,
      countOption: countOption ? JSON.parse(countOption) : [],
      monthOption: monthOption ? JSON.parse(monthOption) : [],
      activeOption: activeOption ? JSON.parse(activeOption) : [],
      trialType: trialType
    };
  }
  dataForm.value.chargingMethod = isConfigureTrial() ? 0 : dataForm.value.chargingMethod;

  // 指定游戏
  let arr = gameList ? gameList.filter((item: any) => item.checked) : [];
  designatedGame.value = arr && arr.length ? arr.map((item: any) => item.name).join("、") : "";

  // 试用
  if (!dataForm.value.chargingMethod) {
    trialsNumber.value = trialOption + (trialType == "1" ? "个月" : "次");
    dataForm.value.amount = trialOption;
  }
  if (dataForm.value.chargingMethod == "1") {
    // 次数
    dataForm.value.amount = dataForm.value.paidFunctionOptions.countOption[0].value;
  } else if (dataForm.value.chargingMethod == "2") {
    // 月数
    dataForm.value.amount = dataForm.value.paidFunctionOptions.monthOption[0].value;
  }

  // im 流量包
  if (dataForm.value.type == 4) {
    dataForm.value.imOptionName = dataForm.value.paidFunctionOptions.activeOption[0].name;
    // 默认选择 1个月
    dataForm.value.imOptionType = 0;
  }
};

// im 流量包 折扣
const imFlowPackageDiscount = computed(() => {
  if (!dataForm.value.paidFunctionOptions) return 0;
  let { activeOption } = dataForm.value.paidFunctionOptions;
  let index = activeOption.findIndex((item: any) => item.name == dataForm.value.imOptionName);
  return {
    discount: activeOption[index].discount > 0 ? activeOption[index].discount : null,
    continuityDiscount: activeOption[index].continuityDiscount ? activeOption[index].continuityDiscount : null
  };
});

// im 流量包 价格
const imPrice = computed(() => {
  if (!dataForm.value.paidFunctionOptions) return 0;
  let { activeOption } = dataForm.value.paidFunctionOptions;
  let index = activeOption.findIndex((item: any) => item.name == dataForm.value.imOptionName);

  let discount = dataForm.value.imOptionType == 0 ? imFlowPackageDiscount.value.discount : imFlowPackageDiscount.value.continuityDiscount;
  discount = discount ? discount / 100 : 1;
  return {
    resultPrice: BigNumber(activeOption[index].price).multipliedBy(BigNumber(discount)),
    price: activeOption[index].price,
    discount: discount
  };
});

// 时间戳格式化
const timestampFormat = (dateTime: string | number | null = null) => {
  return timeFormat(dateTime);
};

const total: any = ref("");
// 计算总价 chargingMethod: 1【按次】 2【包月】 3【永久】
watch(
  () => dataForm.value.amount,
  (newVal) => {
    // debugger
    let { monthOption, countOption } = dataForm.value.paidFunctionOptions;
    if (dataForm.value.chargingMethod == "1") {
      let index = countOption.findIndex((item: any) => item.value == newVal);
      let discount = countOption[index].discount > 0 ? countOption[index].discount / 100 : 1;

      total.value = BigNumber(dataForm.value.amount).multipliedBy(BigNumber(dataForm.value.price)).multipliedBy(BigNumber(discount));
    } else if (dataForm.value.chargingMethod == "2") {
      let index = monthOption.findIndex((item: any) => item.value == newVal);
      let discount = monthOption[index].discount > 0 ? monthOption[index].discount / 100 : 1;

      total.value = BigNumber(dataForm.value.amount || 1)
        .multipliedBy(BigNumber(dataForm.value.price))
        .multipliedBy(BigNumber(discount));
    }

    let amount = dataForm.value.chargingMethod == "3" ? dataForm.value.price : total.vlaue;
    insufficientBalance.value = dataForm.value.balance < amount;
  }
);

// 当前折扣
const discountCompute = computed(() => {
  if (!dataForm.value.paidFunctionOptions) return "0";
  let { monthOption, countOption } = dataForm.value.paidFunctionOptions;
  if (dataForm.value.chargingMethod == "1" && countOption) {
    let index = countOption.findIndex((item) => item.value == dataForm.value.amount);
    return !countOption[index].discount ? "" : countOption[index].discount / 100;
  } else if (dataForm.value.chargingMethod == "2" && monthOption) {
    let index = monthOption.findIndex((item) => item.value == dataForm.value.amount);
    return !monthOption[index].discount ? "" : monthOption[index].discount / 100;
  } else {
    return "0";
  }
});

// 开通付费功能
let btnLoading = ref(false);
const openPaymentFunction = async () => {
  btnLoading.value = true;
  try {
    let params = {} as any;
    if (dataForm.value.type == 4) {
      params = {
        // 功能id
        functionId: dataForm.value.id,
        // 类型
        optionType: dataForm.value.chargingMethod,
        type: dataForm.value.type,
        // im流量包名称
        imOptionName: dataForm.value.imOptionName,
        // im流量包类型
        imOptionType: dataForm.value.imOptionType
      };
    } else {
      params = {
        // 功能id
        functionId: dataForm.value.id,
        // 类型
        optionType: dataForm.value.chargingMethod,
        // 数量
        optionValue: dataForm.value.amount,
        // 试用类型
        trialType: dataForm.value.paidFunctionOptions?.trialType
      };
    }
    params.optionId = dataForm.value.paidFunctionOptions?.id;
    let res = await baseService.get("/paid/function/openFunction", params);
    btnLoading.value = false;
    if (res.code == 0) {
      ElMessage.success({
        message: "开通成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    }
  } catch (error) {
    btnLoading.value = false;
  }
};

// 余额表单变量
const surplusDataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

// 充值提交
const surplusDataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargedrawer = ref(false); // 充值
const rechargeSubmit = () => {
  surplusDataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: surplusDataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};

const closePreview = () => {
  surplusDataForm.showImagePreview = false;
};

// 是否配置试用
const isConfigureTrial = () => {
  const hasTrial = (dataForm.value?.paidFunctionOptions?.trialType === 0 || dataForm.value?.paidFunctionOptions?.trialType === 1) && dataForm.value?.isTrial != 1 ? true : false;
  return hasTrial && dataForm.value?.paidFunctionOptions?.trialOption && dataForm.value?.remainCount === null && dataForm.value?.expireTime === null;
};

// 判断名称中是否包含自动上号
const isAutomaticNumbering = (name: string) => {
  if (name) {
    return name.includes("自动上号");
  }
};
// 打开充值页面
const toBillBalance = () => {
  // rechargedrawer.value = true;
  // surplusDataForm.orderNum = '';
  router.push("/bill/specification?open=1");
};
defineExpose({
  init
});
</script>
<style lang="less">
.function-opened {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden;
  height: fit-content;

  .my-header {
    height: 66px;
    background: #fff;
    padding-left: 20px;
    font-size: 18px;

    .close {
      padding: 20px;
    }
  }

  .el-dialog__header {
    padding: 0;
  }

  .dialog-content {
    padding: 20px;

    .right {
      width: 410px;
    }

    .left {
      padding-right: 20px;
      border-right: 1px solid #e4e7ed;
      //   height: 382px;
    }

    .p-title-line {
      color: #606266;
      display: flex;
      line-height: 20px;

      &::before {
        content: "";
        display: inline-block;
        width: 2px;
        height: 20px;
        background: var(--el-color-primary);
        margin-right: 8px;
      }
    }

    .info-card {
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      padding: 12px;

      .fun-name {
        font-size: 16px;
        line-height: 22px;
      }

      .desc {
        color: #a8abb2;
        font-size: 12px;
        line-height: 15px;
        padding-top: 4px;
        height: 30px;
      }

      .tag-wrap {
        padding-top: 12px;
      }

      .detail {
        padding-top: 8px;

        .detail-img {
          padding-top: 8px;

          .img {
            width: 216px;
            height: 138px;
            border-radius: 4px;
            margin-right: 8px;
          }
        }
      }
    }

    .item-card-list {
      margin-left: -10px;
    }

    .item-card {
      margin: 8px 0 0 10px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      width: 130px;
      height: 64px;
      cursor: pointer;
      position: relative;

      &.active {
        border: 1px solid var(--el-color-primary);
      }

      .discount {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 40px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        color: #5f4139;
        background: #ffecbb;
        border: 1px solid #ffdd8c;
        border-radius: 0px 4px 0px 4px;
        overflow: hidden;
        border-bottom-left-radius: 8px;
      }

      .item-name {
        line-height: 18px;
      }
    }

    .info-item {
      line-height: 24px;
      display: flex;
      padding: 4px 0;
      font-size: 13px;

      .label {
        color: #909399;
      }

      .value {
        flex: 1;
      }
    }

    .right {
      padding-left: 20px;

      .open-btn {
        border-radius: 8px;
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
}
.font-size-small {
  font-size: 12px;
  color: #a8abb2;
}
.paidInfo {
  height: 96px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;

  .image {
    height: 96px;
    border-radius: 12px;
  }

  .txt {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 12px;
    width: 296px;

    .bold {
      color: #1d2129;
      font-size: 16px;
      line-height: 22px;
      font-weight: 700;
    }
    .desc {
      color: #86909c;
      font-size: 12px;
      font-weight: 400;
    }
  }
}

.xstip {
  background: #fcf6ec;
  border-radius: 8px 8px 8px 8px;
  padding: 4px 16px;
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #e6a23c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
</style>
