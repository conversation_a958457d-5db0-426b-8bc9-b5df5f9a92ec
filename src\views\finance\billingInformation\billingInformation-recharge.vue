<template>
  <el-dialog v-model="visible" width="480" :title="currentType == '0' ? '充值到我的账户余额' : '充值到我的保证金'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <div class="tipinfo">完成充值后请刷新页面</div>
    <el-input type="number" v-model="dataForm.amount" placeholder="请输入金额" controls-position="right">
      <template #prepend>充值金额</template>
      <template #append>元</template>
    </el-input>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="refreshPage">{{ "刷新页面" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "去充值" }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { stringify } from "qs";
const router = useRouter();

const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);

const dataForm = ref(<any>{});

// 当前显示类型
const currentType = ref("view");
// 详情id
const detailId = ref("");

const init = (id: string, type: string) => {
  visible.value = true;
  detailId.value = id;
  orderInfo.value = {};
  currentType.value = type;
};

// 提交回复
const orderInfo = ref();
const replyLoading = ref(false);
const dataFormSubmitHandle = async () => {
  if (!dataForm.value.amount) {
    return ElMessage.error("请输入充值金额");
  }
  replyLoading.value = true;
  let res_ = await baseService.get("/finance/tenantfinance/recharge/create", { amount: dataForm.value.amount, type: currentType.value == "0" ? 2 : 3 });
  replyLoading.value = false;
  if (res_.code == 0) {
    orderInfo.value = res_.data;
    ElMessage.success("请刷新页面");
  }
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("refresh");
};
const refreshPage = () => {
  closeDialog();
  router.push("/cashRegister?orderId=" + orderInfo.value.orderId + "&payAmount=" + orderInfo.value.payAmount + "&amount=" + dataForm.value.amount+ "&backUrl=/finance/billingInformation/index");
  orderInfo.value = {};
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}
</style>
