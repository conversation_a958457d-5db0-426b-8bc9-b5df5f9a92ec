<template>
    <el-drawer v-model="visible"  size="50%" class="drawer_shop">
        <template #header>
            <div class="drawer_title">商品审核</div>
        </template>
        <el-scrollbar v-loading="requestLoading">
            <div class="shopPaudit_page">
                <div class="shopPaudit_card">
                    <ny-title title="基本信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>游戏名称</template>
                            {{ dataForm.gameName }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>系统区服</template>
                            {{ dataForm.serverName }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>商品编码</template>
                            {{ dataForm.code }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>截图方式</template>
                            {{ dataForm.cutImage == 1 ? '官方' : '自主' }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="1" border class="descriptions descriptions-label-140 ">
                        <el-descriptions-item>
                            <template #label>商品描述</template>
                            <span style="white-space: pre-wrap;">{{ dataForm.info }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>游戏主图</template>
                            <el-image 
                                style="height: 100px;border-radius: 6px;" 
                                :src="dataForm.log"
                                :preview-src-list="[dataForm.log]" 
                                preview-teleported
                                v-if="dataForm.log"
                            />
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>详情图片</template>
                            <el-image 
                                style="height: 100px;border-radius: 6px;margin-right: 12px;" v-for="(item,index) in dataForm.images" 
                                :src="item" 
                                :preview-src-list="dataForm.images" 
                                preview-teleported
                            />
                        </el-descriptions-item>
                        <template v-if="dataForm.basicInfo.length">
                            <el-descriptions-item v-for="(item,index) in dataForm.basicInfo" :key="index">
                                <template #label>{{ item.key }}</template>
                                <el-image 
                                    style="height: 100px;border-radius: 6px;margin-right: 12px;" v-for="(it,ind) in JSON.parse(item.values)" 
                                    :src="it" 
                                    :preview-src-list="JSON.parse(item.values)" 
                                    preview-teleported
                                    v-if="item.keyType == '3'"
                                />
                                <span v-else>{{ item.values }}</span>
                                
                            </el-descriptions-item>
                        </template>
                        
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card" style="margin: 12px 0px;">
                    <ny-title title="账号信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>游戏账号</template>
                            {{ dataForm.gameAccount }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label> <div>游戏密码</div> </template>
                            <div class="flx-justify-between" v-if="dataForm.gamePassword">
                                <span>{{ isShowGamePassword ? dataForm.gamePassword : "******" }}</span>
                                <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
                                    <View v-if="!isShowGamePassword" />
                                    <Hide v-if="isShowGamePassword" />
                                </el-icon>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>售价</template>
                            <el-text type="danger">￥{{ dataForm.price }}</el-text>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>用户发起时间</template>
                            <span>{{ formatTimeStamp(dataForm.createDate) }}</span>
                        </el-descriptions-item>
                        <template v-if="dataForm.accountInfo.length">
                            <el-descriptions-item v-for="(item,index) in dataForm.accountInfo" :key="index">
                                <template #label>{{ item.key }}</template>
                                {{ item.values }}
                            </el-descriptions-item>
                        </template>
                        
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card" style="margin: 12px 0px;">
                    <ny-title title="保障信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>联系QQ</template>
                            {{ dataForm.qq }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系微信</template>
                            {{ dataForm.wx }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系电话</template>
                            {{ dataForm.phone }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>是否支持议价</template>
                            {{ dataForm.bargain == 0 ? '否' : '是'  }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>用户方便交易时间</template>
                            {{ dataForm.startTime }} - {{ dataForm.endTime }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card" style="margin: 12px 0px;">
                    <ny-title title="自动降价" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>自动降价周期(天)</template>
                            {{ dataForm.priceReductionCycle || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>自动降价比例(%)</template>
                            {{ dataForm.priceReductionPercentage || '-'  }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>自动降价底价(元)</template>
                            {{ dataForm.minimumPrice || '-'  }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="shopPaudit_card">
                    <ny-title title="商品审核" style="padding: 0px 0px 12px 0px"/>
                    <el-form :model="ExamineForm" :rules="rules" ref="ExamineFormRef" label-position="top">
                        <el-form-item label="审核操作" prop="status">
                            <el-radio-group v-model="ExamineForm.status" size="small" @change="ExamineForm.remark = ''">
                                <el-radio value="1" border>审核通过</el-radio>
                                <el-radio value="2" border>审核拒绝</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="拒绝原因" prop="remark" v-if="ExamineForm.status == '2'">
                            <el-input v-model="ExamineForm.remark" type="textarea" placeholder="拒绝原因" :rows="4" maxlength="200" show-word-limit></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </el-scrollbar>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="visible = false">取消</el-button>
                <el-button :loading="ExamineLoading" type="primary" @click="Submit" >提交</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue'
const visible = ref(false);  // 对话框显隐
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
const emit = defineEmits(["refreshDataList"]);

const dataForm = reactive({  // 表单变量
    id: null,
    code: '',
    gameId: '',
    gameName: '',
    gameCode: '',
    highlights: '',
    title: '',
    price: 0,
    acquisitionPrice: 0,
    phone: '',
    gameAccount: '',
    gamePassword:'',
    log: [],
    images: [],
    info:'',
    gameAre: '',
    server: '',
    serverName: '',
    compensation: '',
    bargain: 1,
    topped: '',
    creator: '',
    nickname: '',
    wx: '',
    status: '',
    auditTime: 0,
    auditUserId: '',
    remark: '',
    realName: '',
    cutImage: 1,
    basicInfo: <any>[],
    accountInfo: <any>[]
});


const requestLoading = ref(false); // 详情加载
// 是否显示游戏密码
const isShowGamePassword = ref(false);

// 表单初始化
const init = (id: any) => {
    visible.value = true;
    dataForm.id = id;
    ExamineForm.id = id;
    getInfo();
};

// 审核详情
const getInfo = () =>{
    requestLoading.value = true
    baseService.get("/shop/shopaudit/" + dataForm.id).then((res) => {
        res.data.shopAuditDTO.images = res.data.shopAuditDTO.images ? JSON.parse(res.data.shopAuditDTO.images) : []
        res.data.shopAuditDTO.accountInfo = res.data.shopAuditDTO.accountInfo ? JSON.parse(res.data.shopAuditDTO.accountInfo) : [];
        res.data.shopAuditDTO.basicInfo = res.data.shopAuditDTO.basicInfo ? JSON.parse(res.data.shopAuditDTO.basicInfo) : [];
        Object.assign(dataForm,res.data.shopAuditDTO);
        console.log(dataForm,'====== dataForm =======')
    }).finally(()=>{
        requestLoading.value = false
    })
}

const ExamineFormRef = ref(); // 审核结果
const ExamineLoading = ref(false);
const ExamineForm = reactive({
    id: '',
    status: '',
    remark: '',
    gameAccount:'', // 游戏账号
    gamePassword: '', // 游戏密码
    price: '', // 价格
    nickname: '', // 用户名
    phone: '', // 联系电话
})
const rules = ref({
    status: [{ required: true, message: '审核操作不能为空', trigger: 'change', },],
    remark: [{ required: true, message: '拒绝原因不能为空', trigger: 'blur', },],
})

// 审核结果
const Submit = () =>{
    ExamineFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        ExamineLoading.value = true;
        baseService.post("/shop/shopaudit",{id:ExamineForm.id,status:ExamineForm.status,remark:ExamineForm.remark}).then(res => {
            ElMessage.success({
                message: '审核结果保存成功',
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        }).finally(() => {
            ExamineLoading.value = false;
        })
        
    })
}


defineExpose({
    init
});
</script>

<style lang='less' scoped>
.shopPaudit_page{
    background-color: #F0F2F5;
    padding: 12px;
    .shopPaudit_card{
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
        padding: 12px;
    }
}
.proportion{
    :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content){
        // width: 316px;
    }
}

</style>
<style lang='less'>
.drawer_shop{
    .el-drawer__header{
        margin-bottom: 0px;
    }
    .drawer_title{
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .el-drawer__body{
        padding: 0px;
    }
}
</style>