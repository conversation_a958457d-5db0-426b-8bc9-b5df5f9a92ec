<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table cellHeight="ch-40" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
        <template #header>
          <ny-button-group
            label="label"
            value="value"
            :list="stateList"
            v-model="state.dataForm.status"
            @change="
              () => {
                state.page = 1;
                state.getDataList();
              }
            "
          ></ny-button-group>
        </template>
        <template #header-custom>
          <div style="margin-bottom: 12px">
            <el-button type="primary" @click="addHandle()" v-if="state.hasPermission('phone:sysphone:save')">新增</el-button>
            <el-button type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()" v-if="state.hasPermission('phone:sysphone:delete')">删除</el-button>
            <el-button type="success" :loading="synchronizeLoading" @click="synchronizePhone">同步手机号</el-button>
            <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
            <el-button type="info" @click="exportNewHandle()">导出</el-button>
          </div>
        </template>
        <template #header-right>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input style="width: 280px !important" v-model="state.dataForm.phone" placeholder="请输入手机号码" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
          </el-form>
        </template>
        <!-- 手机号 -->
        <template #phone="{ row }">
          <el-text @click="editHandle(row, false)" type="primary" style="cursor: pointer">{{ row.phone }}</el-text>
        </template>
        <template #tx="{ row }">
          <el-text v-if="+row.qq > 0" @click="showDetail({ ...row, keywords: 'QQ' })" type="primary" style="cursor: pointer">QQ{{ row.qq }} <span v-if="+row.wx > 0">，</span> </el-text>
          <el-text v-if="+row.wx > 0" @click="showDetail({ ...row, keywords: '微信' })" type="primary" style="cursor: pointer">微信{{ row.wx }}</el-text>
          <span v-if="row.tx == 0">0</span>
        </template>
        <!-- 状态 -->
        <template #status="{ row }">
          <el-tag v-if="row.status == 0" type="danger">废弃</el-tag>
          <el-tag v-if="row.status == 1" type="success">正常</el-tag>
        </template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-link v-if="state.hasPermission('phone:sysphone:update')" :underline="false" style="margin-right: 12px" @click="addHandle(row.id)" type="primary">编辑</el-link>
          <el-link v-if="state.hasPermission('phone:sysphone:correct')" :underline="false" style="margin-right: 12px" @click="editHandle(row, true)" type="primary">纠偏</el-link>
          <el-link v-if="state.hasPermission('phone:sysphone:delete')" :underline="false" @click="state.deleteHandle(row.id)" type="danger">删除</el-link>
        </template>
      </ny-table>
      <ny-import ref="importRef" @refreshDataList="state.getDataList()" />
      <!-- 新增 -->
      <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
      <!-- 纠偏 -->
      <correction @refreshDataList="state.getDataList" ref="correctionRef"></correction>
      <!-- 查看详情系 -->
      <showDetailModal ref="showDetailModalRef"></showDetailModal>
    </el-card>
  </div>
</template>

<script lang="tsx" setup>
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { Edit, Search } from "@element-plus/icons-vue";
import { fileExport } from "@/utils/utils";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
// 组件引入
import AddOrUpdate from "./phone-add-or-update.vue";
import correction from "./correction.vue";
import showDetailModal from "./showDetailModal.vue";
const addOrUpdateRef = ref();
const correctionRef = ref();
const showDetailModalRef = ref();

const view = reactive({
  getDataListURL: "/phone/sysphone/page",
  getDataListIsPage: true,
  deleteURL: "/phone/sysphone",
  deleteIsBatch: true,
  order: "",
  orderField: "",
  // 传参
  dataForm: {
    status: null, //状态
    phone: null //手机号
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const tableColums = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "serialNumber",
    label: "编号",
    minWidth: 80
  },
  {
    prop: "phone",
    label: "手机号码",
    minWidth: 134
  },
  {
    prop: "total",
    label: "绑定数量",
    minWidth: 134,
    sortable: "custom",
    headerRender: () => {
      return (
        <div class="flx-center">
          <el-tooltip
            effect="dark"
            content="当商品点击出售时、删除和标记售后时，
绑定数量和绑定游戏才会进行改变，点击下
架操作不会进行改变。"
            placement="top-start"
          >
            <div style="margin-top:4px;margin-right:4px;">
              <el-icon>
                <InfoFilled />
              </el-icon>
            </div>
          </el-tooltip>
          <div>绑定数量</div>
        </div>
      );
    }
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: 134
  },
  {
    prop: "remark2",
    label: "备注2",
    minWidth: 134
  },
  {
    prop: "tx",
    label: "腾讯系",
    minWidth: 134,
    sortable: "custom"
  },

  {
    prop: "wy",
    label: "网易系",
    minWidth: 134,
    sortable: "custom"
  },
  {
    prop: "mhy",
    label: "米哈游系",
    minWidth: 134,
    sortable: "custom"
  },
  {
    prop: "steam",
    label: "steam系",
    minWidth: 134,
    sortable: "custom"
  },
  {
    prop: "sjtc",
    label: "世纪天成系",
    minWidth: 134,
    sortable: "custom"
  },
  {
    prop: "qt",
    label: "其他",
    minWidth: 134,
    sortable: "custom"
  },

  {
    prop: "status",
    label: "状态",
    fixed: "right",
    minWidth: 112
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 240
  }
]);
const stateList = ref([
  { value: null, label: "全部" },
  { value: 1, label: "正常" },
  { value: 0, label: "废弃" }
]);

// 操作
const addHandle = (id?: number) => {
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
// 纠偏&详情
const editHandle = async (id: any, showOperate?: Boolean) => {
  await nextTick();
  correctionRef.value.init(id, showOperate);
};
const showDetail = async (row?: Object) => {
  await nextTick();
  showDetailModalRef.value.init(row);
};
// 重置操作
const getResetting = () => {
  state.dataForm.phone = "";
  state.getDataList();
};
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};

// 同步手机号
const synchronizeLoading = ref(false);
const synchronizePhone = () => {
  synchronizeLoading.value = true;
  baseService
    .get("/phone/sysphonegame/sync")
    .then((res) => {
      if (res.code == 0) {
        state.getDataList();
      }
    })
    .finally(() => {
      synchronizeLoading.value = false;
    });
};
// 导入
const importRef = ref();
const importHandle = () => {
  nextTick(() => {
    importRef.value.init("/phone/sysphone/import");
  });
};
// 导出最新属性
const exportNewHandle = () => {
  baseService.get("/phone/sysphone/export").then((res) => {
    if (res) {
      fileExport(res, "手机号列表");
    }
  });
};
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.rr-view-ctx-card {
  padding: 12px 24px;
  :deep(.el-card__body) {
    padding: 0;
  }
  .cards {
    display: flex;
    margin-bottom: 16px;
    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }
      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
:deep(.el-link__inner) {
  width: 36px;
  height: 22px;
  background: #f5f7fa;
  border-radius: 2px 2px 2px 2px;
}
.el-tag {
  border: 1px solid;
}
</style>
