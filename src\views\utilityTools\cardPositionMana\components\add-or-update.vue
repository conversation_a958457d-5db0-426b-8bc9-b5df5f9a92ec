<template>
  <el-drawer v-model="visible" :title="dataForm.id ? '编辑设备' : '新增设备'" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="944px" class="ny-drawer article-add-or-update">
    <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
      <div class="card">
        <div class="titleSty">卡板信息</div>
        <el-descriptions style="width: 100%;" border :column="2">
          <el-descriptions-item class-name="noneSelfRight" label="卡板编码">
            <template #label>
              <span>卡板编码<span style="color: red">*</span></span>
            </template>
            <el-form-item label="卡板编码" prop="value1">
              <el-input v-model="dataForm.value1" placeholder="请输入卡板编码"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="租户">
            <template #label>
              <span>租户<span style="color: red">*</span></span>
            </template>
            <el-form-item label="租户" prop="value2">
              <el-select v-model="dataForm.value2" placeholder="请选择租户">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="启用">
            <el-form-item label="启用" prop="value3"> <el-switch  inline-prompt v-model="dataForm.value3" active-text="是" inactive-text="否" /> </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="备注">
            <el-form-item label="备注" prop="value4">
              <el-input v-model="dataForm.value4" placeholder="请输入备注" type="textarea" :rows="5"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="card">
        <div class="titleSty">卡位信息</div>
        <el-descriptions style="width: 100%;" border :column="2">
          <el-descriptions-item :label="`卡位${index + 1}`" :key="index" v-for="(item, index) in dataForm.value5">
            <el-form-item label="卡位">
              <el-input v-model="dataForm.value5[index]" placeholder="请输入卡位编码"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-form>

    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm()">确定</el-button>
    </template>
  </el-drawer>
</template>  
  
  <script lang="ts" setup>
import { IObject } from "@/types/interface";
import { computed, ref, defineExpose, defineEmits, watch, nextTick } from "vue";
import { getDictDataList } from "@/utils/utils";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useHandleData } from "@/hooks/useHandleData";

const store = useAppStore();
const emits = defineEmits(["refreshDataList"]);

const dataForm = ref({} as IObject);
const visible = ref(false);

const rules = {
  value1: [{ required: true, message: "请输入卡板编码", trigger: "blur" }],
  value2: [{ required: true, message: "请选择租户", trigger: "blur" }]
};

const init = (id?: number) => {
  visible.value = true;
  // getTenant()
  if (id) {
    getDetails(id);
    return;
  }
  // 初始化卡位
  dataForm.value.value5 = [];
  for (let index = 0; index < 16; index++) {
    dataForm.value.value5[index] = undefined;
  }
};

const dataLoading = ref(false);
const getDetails = (id: number) => {
  dataLoading.value = true;
  baseService
    .get("/basicInfo/tbannouncement/" + id)
    .then((res) => {
      if (res.code === 0) {
        dataForm.value = res.data;
      }
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 租户下拉
const options = ref([]);
const getTenant = () => {
  options.value = [];
  baseService.get("").then((res) => {
    if (res.code === 0) {
      options.value = res.data;
    }
  });
};

const dataFormRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/basicInfo/tbannouncement", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visible.value = false;
            ElMessage.success(res.msg);
            emits("refreshDataList");
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>
  
<style lang="scss" scoped>
.card {
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  padding: 12px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }
  :deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;
    tbody {
      display: flex;
      flex-direction: column;

      tr {
        display: flex;
        flex: 1;
        .el-descriptions__label {
          display: flex;
          align-items: center;
          font-weight: normal;
          width: 144px;
        }
        .el-descriptions__content {
          display: flex;
          align-items: center;
          min-height: 48px;
          flex: 1;
          > div {
            width: 100%;
          }
          .el-form-item__label {
            display: none;
          }
          .el-form-item {
            margin-bottom: 0;
          }
        }
        .noneSelfRight {
          border-right: 0 !important;
        }
        .noneSelfLeft {
          border-left: 0 !important;
        }
        .noneSelfLabel {
          background: none;
          border-left: 0 !important;
          border-right: 0 !important;
        }
      }
    }
  }
}
</style>