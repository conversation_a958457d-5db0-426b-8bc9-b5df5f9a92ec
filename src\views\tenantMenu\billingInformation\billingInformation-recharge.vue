<template>
  <el-dialog v-model="visible" width="480" :title="currentType == '0' ? '充值到我的账户余额' : '充值到我的保证金'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
      <el-descriptions-item label-width="144" label-class-name="title">
        <template #label> <div>账户名称</div> </template>
        恩悠
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>收款账户</div> </template>
        恩悠
      </el-descriptions-item>
    </el-descriptions>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" label-width="100%">
      <el-form-item label="支付宝订单号" prop="orderNum">
        <template #label>
          <span style="margin-right: 10px">支付宝订单号</span>
          <el-text type="primary" @click="dataForm.showImagePreview = true">如何查看订单号？</el-text>
        </template>
        <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号" style="width: 100%"></el-input>
      </el-form-item>
    </el-form>
    <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="dataForm.showImagePreview = false" />
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">取消</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { stringify } from "qs";
import { useSettingStore } from "@/store/setting";
const settingStore = useSettingStore();
const router = useRouter();
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});
const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);

const dataForm = ref(<any>{});
// 当前显示类型
const currentType = ref("view");
// 详情id
const detailId = ref("");

const init = (id: string, type: string) => {
  visible.value = true;
  detailId.value = id;
  orderInfo.value = {};
  currentType.value = type;
};
const dataFormRef = ref();
// 提交回复
const orderInfo = ref();
const replyLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    replyLoading.value = true;
    baseService
      .get("/finance/tenantfinance/recharge/create", { amount: dataForm.value.amount, type: currentType.value == "0" ? 2 : 3 })
      .then((res_) => {
        if (res_.code == 0) {
          orderInfo.value = res_.data;
          // ElMessage.success("请刷新页面");
          dataForm.value.amount = undefined;
          // refreshPage();
        }
      })
      .finally(() => {
        replyLoading.value = false;
        dataForm.value.amount = undefined;
      });
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("refresh");
};
const refreshPage = () => {
  closeDialog();
  window.open(settingStore.info.websiteUrl + "#/cashRegister?orderId=" + orderInfo.value.orderId + "&payAmount=" + orderInfo.value.payAmount + "&amount=" + dataForm.value.amount + "&backUrl=/tenantMenu/billingInformation/index");
  orderInfo.value = {};
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  font-family: Inter, Inter;
  font-weight: 400;
  font-size: 14px;
  color: #606266;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}
.el-form-item {
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-text) {
    float: right;
  }
}
:deep(.el-descriptions__cell) {
  &.title {
    width: 144px;
  }
}
</style>
