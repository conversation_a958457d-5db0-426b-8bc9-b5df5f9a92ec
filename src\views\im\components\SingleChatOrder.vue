<template>
    <div class="right-detail flx-column" v-loading="dataLoading">
        <div class="header">
            <div class="title">{{ orderInfo.state }}</div>
        </div>
        
        <div class="order-detail">
            <el-image v-if="orderInfo.shopVo && orderInfo.shopVo.log" class="order-img" :src="orderInfo.shopVo.log"></el-image>
            <div class="order-info">
                <div class="info-item">
                    <div class="label">订单编号：</div>
                    <div class="value text-primary">{{ orderInfo.orderId || orderInfo.id }}</div>
                </div>
                <div class="info-item">
                    <div class="label">创建时间：</div>
                    <div class="value">{{ orderInfo.createDate || orderInfo.orderCreateDate }}</div>
                </div>
                <div class="info-item">
                    <div class="label">游戏名称：</div>
                    <div class="value">{{ orderInfo.gameName }}</div>
                </div>
                <div class="info-item" v-if="orderInfo.saleOrder && orderInfo.saleOrder.saleType">
                    <div class="label">订单类型：</div>
                    <div class="value text-primary">{{ getDictLabel(
                            store.state.dicts,
                            "sale_type",
                            orderInfo.saleOrder.saleType
                        ) }}
                    </div>
                </div>
                <div class="info-item" v-if="guaranteeInformation && guaranteeInformation.length">
                    <div class="label">包赔类型：</div>
                    <div class="value">{{ guaranteeInformation.join(',') }}</div>
                </div>
                <div class="info-item">
                    <div class="label">成交价：</div>
                    <div class="value">￥{{ orderInfo.dealAmount || orderInfo.amount }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { nextTick, ref, defineExpose } from "vue";
    import { useImStore } from "@/store/im";
    import { useAppStore } from "@/store";
    import { getDictLabel } from "@/utils/utils";
    import baseService from "@/service/baseService";
    import dayjs from "dayjs";

    const store = useAppStore();
    const imStore = useImStore();

    // 订单日志
    const orderLog = ref([])

    // 订单信息
    const orderInfo = ref(<any>{});

    const dataLoading = ref(false);

    const init = (data: any) => {
        orderInfo.value = data;
        dataLoading.value = true;
        baseService.get('/sale/orderLog/' + orderInfo.value.id).then(res => {
            if (res.code === 0 && res.data) {
                orderInfo.value.state = res.data.state || '待支付';
            }
        }).finally(() => {
            dataLoading.value = false;
        });
    }

    defineExpose({
        init
    })

    
</script>

<style lang="scss" scoped>
    .right-detail{
        width: 348px;
        box-shadow: inset 4px 0px 4px 0px rgba(0,0,0,0.08);
        border-left: 1px solid #E4E7ED;
        position: relative;
        background: #fff;
        
        .header{
            height: 92px;
            border-bottom: 1px solid #E4E7ED;
            padding: 20px;

            .title{
                font-size: 16px;
                line-height: 52px;
            }

            .desc{
                color: #909399;
                padding-top: 24px;
            }
        }

        .order-detail{
            display: flex;
            padding: 20px;
            
            .order-img{
                width: 120px;
                height: 120px;
                border-radius: 8px;
                margin-right: 16px;
            }

            .order-info{
                .info-item{
                    padding-bottom: 12px;
                    line-height: 1;

                    .label{
                        color: #909399;
                    }
                    
                    .value{
                        padding-top: 4px;
                    }
                }
            }
        }

        .order-log{
            border-top: 1px solid #E4E7ED;
            flex: 1;
            padding: 20px 20px 20px 0;

            .title{
                font-weight: bold;
            }

            .el-timeline-item{
                padding-bottom: 24px;
            }

            .content{
                font-size: 12px;
                line-height: 24px;
                color: #909399;

                .desc{
                    flex: 1;
                }
                
                .time{
                    width: 112px;
                    padding: 0;
                    margin-left: 10px;
                }
            }
        }
    }
</style>