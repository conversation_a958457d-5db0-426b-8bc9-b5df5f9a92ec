<template>
  <div class="mainBox">
    <div class="leftPart">
      <div class="title">选择合作商</div>
      <el-select filterable v-model="state.dataForm.partnerId" style="width: 186px; margin-bottom: 10px" @change="handleselectPartner(state.dataForm.partnerId)">
        <el-option v-for="item in state.curAllPartnerList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <div class="title">选择游戏</div>
      <div class="scrollWrap lower">
        <ul class="menuUl">
          <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in state.allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
            <span>{{ item.gameName }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="rightPart">
      <div v-loading="tableLoading" class="default-main">
        <div class="cardTop">
          <div class="flx">
            <el-dropdown>
              <el-button link
                >{{ currentUserName }}<el-icon style="margin-left: 8px"><ArrowDown /></el-icon
              ></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in shopList"
                    @click="
                      currentUserName = item.name;
                      changeShop(item.id);
                    "
                    >{{ item.name }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        <div style="padding: 12px; height: calc(100vh - 330px); overflow: auto">
          <el-collapse class="collapseSty" v-model="collapseValue" accordion v-if="state.dataList.length > 0">
            <el-collapse-item v-for="(item, itemIndex) in state.dataList" :key="itemIndex" :name="itemIndex">
              <template #title>
                <div class="collapseStyTitle">
                  <div>
                    <el-icon style="margin-right: 30px; color: #909399; font-size: 20px"><CaretBottom v-if="collapseValue === itemIndex" /><CaretRight v-else /></el-icon>
                    <el-result title="推送时间">
                      <template #sub-title>
                        {{ timeFormat(item.pushStartTime) }}
                      </template>
                    </el-result>
                    <el-result title="任务类型">
                      <template #sub-title>
                        {{ getTaskText(item.taskType) }}
                      </template>
                    </el-result>
                    <el-result title="间隔时间">
                      <template #sub-title>{{ item.intervalTime }}分钟 </template>
                    </el-result>
                    <!-- <el-result title="每日上限" :sub-title="item.dailyLimit"> </el-result> -->
                  </div>
                  <div>
                    <el-result title="推送商品总数" :sub-title="(item.totalPushCount || '0') + ''"> </el-result>
                    <el-result title="今日上限" :sub-title="(item.pushLimit || '-') + ''"> </el-result>
                    <el-result title="执行中" :sub-title="(item.pushingNum || '0') + ''"> </el-result>
                    <el-result title="成功数量">
                      <template #sub-title>
                        {{ item.totalPushCount > 0 ? (item.pushSuccessCount || 0) + "/" + item.totalPushCount : "-" }}
                      </template>
                    </el-result>
                    <el-result title="状态">
                      <template #sub-title>
                        <el-tag v-if="item.pushStatus == 2" type="warning">推送中</el-tag>
                        <el-tag v-else-if="item.pushStatus == 3" type="success">推送完成</el-tag>
                        <el-tag v-else-if="item.pushStatus == 1" type="primary">未开始</el-tag>
                        <el-tag v-else-if="item.pushStatus == 4" type="info">暂停</el-tag>
                        <span v-else>-</span>
                      </template>
                    </el-result>
                  </div>
                </div>
              </template>
              <div>
                <!-- 推送信息 -->
                <pushInfo v-if="collapseValue === itemIndex" ref="pushInfoRef" :propData="{ ...item, scriptUserId: state.dataForm.scriptUserId }" @pushChange="getListData"></pushInfo>
              </div>
            </el-collapse-item>
          </el-collapse>
          <el-empty v-else />
        </div>
      </div>
      <el-pagination
        v-if="!tableLoading"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 50, 100, 500, 1000]"
        :page-size="queryParams.limit"
        :total="state.count"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="TableSizeChangeFn"
        @current-change="TableCurrentChangeFn"
      ></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
import { timeFormat, fileExport } from "@/utils/utils";
import pushInfo from "./push-info.vue";
import { useRouter } from "vue-router";

const emit = defineEmits(["refreshDataList"]);

const router = useRouter();
const props = defineProps({
  curAllPartnerList: <any>[]
});
const shopPushTableRef = ref();
const collapseValue = ref(-1);
const state = reactive({
  curAllPartnerList: [
    {
      name: "全部合作商",
      id: null
    }
  ],
  allSelectArr: [
    {
      title: "选择游戏",
      data: [],
      addHandle: () => {},
      hasButton: false,
      buttonText: "",
      selectType: "radio"
    }
  ],
  sortForm: {
    order: null,
    column: null
  },
  dataForm: {
    partnerId: null, //合作商id
    gameId: undefined, //游戏id
    scriptUserId: "" // 店铺Id
  },
  dataList: <any>[],
  count: 0
});

// 获取游戏列表
const upDateScriptUserId = (id: any) => {
  scriptUserId.value = id;
  state.allSelectArr = [];
  baseService.get("/script/sysscriptpartnerinfo/selectGameList", { partnerId: state.dataForm.partnerId, scriptUserId: scriptUserId.value }).then((res_) => {
    if (res_.code == 0) {
      state.allSelectArr = res_.data || [];
      if (state.allSelectArr.length > 0) {
        state.dataForm.gameId = state.allSelectArr[0].gameId;
        getListData(); // 刷新列表
      } else {
        state.dataForm.gameId = "";
      }
    }
  });
};
// 游戏点击事件
const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  getListData(); // 刷新列表
};
const scriptUserId = ref(null);
// 切换合作商获取游戏+更新店铺
const handleselectPartner = (id: any) => {
  state.dataForm.partnerId = id;
  getShopList();
};

const currentUserName = ref();
const queryParams: { [key: string]: any } = reactive({
  page: 1,
  limit: 10
});
// 请求店铺数据
const shopList = ref(<any>[]);
const getShopList = () => {
  baseService.get("/script/sysscriptuser/page", { scriptPartnerId: state.dataForm.partnerId }).then((res) => {
    if (res.data) {
      shopList.value = res.data.list;
      state.dataForm.scriptUserId = shopList.value.some((item: any) => item.id == state.dataForm.scriptUserId) ? state.dataForm.scriptUserId : shopList.value[0].id;
      let obj = shopList.value.find((item: any) => item.id == state.dataForm.scriptUserId);
      currentUserName.value = obj ? obj.name : shopList.value[0].name;
      upDateScriptUserId(state.dataForm.scriptUserId); // 刷新游戏列表
      
    }
  });
};

// 店铺tab点击回调
const changeShop = (value: any) => {
  state.dataForm.scriptUserId = value;
  upDateScriptUserId(state.dataForm.scriptUserId); // 刷新游戏列表
};

// 获取列表信息&切换游戏
const tableLoading = ref(false);
const getListData = () => {
  tableLoading.value = true;
  state.dataList = [];

  // 推送记录
  baseService
    .get("/script/sysscriptpushtask/page", {
      partnerId: state.dataForm.partnerId,
      gameId: state.dataForm.gameId,
      scriptUserId: state.dataForm.scriptUserId,
      ...queryParams,
      ...state.sortForm
    })
    .then((res_) => {
      if (res_.code == 0) {
        state.dataList = res_.data.list || [];
        collapseValue.value = 0;
        tableLoading.value = false;
        state.count = res_.data.total;
      }
    })
    .catch((err) => {
      tableLoading.value = false;
      state.count = 0;
    });
};
// 回显处理
const getTaskText = (type: number) => {
  let arr = ["寄售", "上架", "擦亮", "编辑", "下架"];
  return arr[type - 1] || "-";
};

// 表格分页条数切换
const TableSizeChangeFn = (val: number) => {
  queryParams.limit = val;
  queryParams.page = 1;
  getListData();
};
// 表格分页页码切换
const TableCurrentChangeFn = (val: number) => {
  queryParams.page = val;
  getListData();
};

watch(
  () => props.curAllPartnerList,
  () => {
    state.curAllPartnerList = [...props.curAllPartnerList];
    if (state.curAllPartnerList && state.curAllPartnerList.length > 0) {
      state.dataForm.partnerId = state.curAllPartnerList[0].id;
      getShopList();
    }
  },
  {
    immediate: true,
    deep: true
  }
);

onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rr-view-ctx-card {
  padding: 12px 24px;
  min-height: calc(100vh - 154px);

  :deep(.el-card__body) {
    padding: 0;
  }

  .cards {
    display: flex;
    margin-bottom: 16px;

    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }

      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .mainBox {
    display: flex;

    .leftPart {
      width: 186px;
      margin-right: 12px;

      .title {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 22px;
        margin-bottom: 2px;
      }

      display: flex;
      flex-direction: column;

      .scrollWrap {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        height: calc(100vh - 260px);
        overflow: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .menuUl {
          .menuLi {
            cursor: pointer;
            padding: 20px;
            word-break: keep-all;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Inter, Inter;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            &.active {
              background-color: var(--color-primary-light);
              color: var(--color-primary);
            }
          }
        }

        &.lower {
          height: calc(100vh - 330px);
        }
      }
    }

    .rightPart {
      flex: 1;
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
    }
  }
}
.router_info {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  .info_name {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .info_line {
    color: #e4e7ed;
    margin: 0px 12px;
  }
  .info_blurb {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.cardTop {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f8fa;
}

.collapseSty {
  border: none;
  :deep(.el-collapse-item__arrow) {
    display: none;
  }
  .el-collapse-item,
  :deep(.el-collapse-item__header) {
    height: fit-content;
  }
  .el-collapse-item {
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;

    :deep(.el-collapse-item__content) {
      border: 1px solid var(--el-border-color-lighter);
      border-top: 0;
      border-bottom: 0;
    }
    :deep(.el-collapse-item__header) {
      border: none;
    }
  }
  .collapseStyTitle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f5f7fa;
    > div {
      padding: 12px;
      display: flex;
      align-items: center;
    }
  }
  :deep(.el-result) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-right: 24px;
    padding: 0;
    .el-result__icon {
      display: none;
    }
    .el-result__title {
      margin-top: 0;
      p {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #909399;
        line-height: 22px;
      }
    }

    .el-result__subtitle {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #303133;
      margin-top: 2px;
    }
  }
}
</style>
