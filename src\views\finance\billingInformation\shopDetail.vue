<template>
  <el-drawer v-model="visible" :footer="null" :title="'商城保证金明细'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="768px" class="">
    <ny-table :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle"> </ny-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, reactive, toRefs } from "vue";
import useView from "@/hooks/useView";

const emits = defineEmits(["refresh"]);
const visible = ref(false);

const view = reactive({
  getDataListURL: "/finance/tenantfinance/page",
  getDataListIsPage: true,
  dataForm: {
    tab: "5",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
  {
    prop: "orderCode",
    label: "交易订单号",
    minWidth: 200
  },
  {
    prop: "type",
    label: "类型",
    minWidth: 120
  },
  {
    prop: "operation",
    label: "金额(元)",
    width: 120
  },
  {
    prop: "operation",
    label: "当前余额(元)",
    width: 120
  }
]);


const init = (id?: number) => {
  visible.value = true;
  if (id) {
    state.getDataList(id);
  }
};
defineExpose({
  init
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
</style>
