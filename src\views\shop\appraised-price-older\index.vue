<template>
  <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
    <ny-flod-tab
      :list="[
        { label: '一键估价', value: '0' },
        { label: '公式设定', value: '1' },
      ]"
      v-model="currentTypeIndex"
      value="value"
      label="label"
    ></ny-flod-tab>

    <!-- 一键估价 -->
    <valuation v-show="currentTypeIndex == 0"></valuation>

    <!-- 公式设定 -->
    <formula-set v-if="currentTypeIndex == 1"></formula-set>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Valuation from "./valuation.vue";
import FormulaSet from "./formula-set.vue";

const currentTypeIndex = ref(0);
</script>
