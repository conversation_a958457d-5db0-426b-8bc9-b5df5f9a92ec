<template>
  <el-dialog v-model="visible" width="480" title="申请开票" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #title>
      <span style="font-size: 18px; font-weight: 700; margin-right: 10px"> 申请开票</span>
      <el-text type="primary" style="font-size: 16px">
        <el-icon style="margin-right: 2px"><WarningFilled /></el-icon>仅支持申请普通增值税发票</el-text
      >
    </template>
    <el-form class="cardDescriptions" style="padding: 0" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-descriptions title="" :column="1" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>发票类型<span style="color: red">*</span></div>
          </template>
          普通增值税发票(电子)
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>选择抬头<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="headerName" label="选择抬头" required>
            <el-select v-model="dataForm.hid" placeholder="请选择抬头" @change="handleHeadData">
              <el-option v-for="item in headerList" :key="item.id" :label="item.headerName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title" v-if="dataForm.headerType == 1">
          <template #label>
            <div>税号<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="taxNumber" label="税号">
            <el-input v-model="dataForm.taxNumber" placeholder="请输入税号" disabled />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label>
            <div>抬头类型</div>
          </template>
          {{ dataForm.headerType == 1 ? "企业单位" : dataForm.headerType == 2 ? "个人" : "-" }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title" key="3">
          <template #label>
            <div>开票金额(元){{ dataForm.headerType == 2 ? "" : "" }}<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="invoiceAmount" label="开票金额(元)" key="1">
            <el-input v-model="dataForm.invoiceAmount" placeholder="请输入开票金额(元)" disabled />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title" key="4">
          <template #label>
            <div>发送至邮箱{{ dataForm.headerType == 2 ? "" : "" }}<span style="color: red">*</span></div>
          </template>
          <el-form-item prop="email" label="发送至邮箱" key="2">
            <el-input v-model="dataForm.email" placeholder="发送至邮箱" disabled />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: null,
  orderIds: "",
  invoiceType: 1,
  headerType: undefined,
  headerName: "",
  taxNumber: "",
  email: "",
  registerAddress: "",
  invoiceAmount: 0,
  hid: "" //非数据库字段
});
const rules = ref({
  headerName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  invoiceAmount: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  taxNumber: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});

const init = (row?: any) => {
  visible.value = true;
  Object.assign(dataForm, {
    id: null,
    orderIds: row.id,
    invoiceType: 1,
    headerType: undefined,
    headerName: "",
    taxNumber: "",
    email: "",
    registerAddress: "",
    invoiceAmount: row.spendingAmount,
    hid: "" //非数据库字段
  });
  // 重置表单数据
  getHeaderList();
};
const headerList = ref(<any>[]);
const getHeaderList = () => {
  baseService.get("/invoice/header/list").then((res) => {
    headerList.value = res.data;
  });
};
const handleHeadData = () => {
  let obj = headerList.value.find((ele) => ele.id == dataForm.hid);
  dataForm.headerType = obj.headerType;
  dataForm.headerName = obj.headerName;
  dataForm.taxNumber = obj.taxNumber;
  dataForm.email = obj.email;
  dataForm.registerAddress = obj.registerAddress;
};
const btnLoading = ref(false);
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    let params = { ...dataForm };
    delete params.hid;
    baseService[dataForm.id ? "put" : "post"]("/invoice/apply", params)
      .then((res) => {
        if (res.code === 0) {
          emit("refreshDataList");
          visible.value = false;
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
