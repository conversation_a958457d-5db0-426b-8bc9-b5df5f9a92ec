<template>
  <el-drawer v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" size="40%" class="ny-drawer">
    <el-card>
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="dataFormRef" v-loading="dataLoading">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="游戏名称" prop="title">
              <el-input v-model="dataForm.title" placeholder="请输入游戏名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运行设备" prop="gtype">
              <ny-radio-group v-model="dataForm.gtype" dict-type="game_type"></ny-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否热门游戏" prop="title">
              <ny-radio-group v-model="dataForm.isHot" dict-type="whether"></ny-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="移动端图标" prop="thumbnail">
              <ny-upload :listNumber="1" v-model:imageUrl="dataForm.thumbnail" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网站游戏列表图标" prop="listIcon">
              <ny-upload :listNumber="1" v-model:imageUrl="dataForm.listIcon" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网站首页小图标" prop="smallIcons">
              <ny-upload :listNumber="1" v-model:imageUrl="dataForm.smallIcons" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网站首页大图标" prop="largeIcon">
              <ny-upload :listNumber="1" v-model:imageUrl="dataForm.largeIcon" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="游戏运营商" prop="operatorsId">
              <el-select clearable v-model="dataForm.operatorsId" placeholder="请选择游戏运营商">
                <el-option v-for="item in operatorsGameList" :key="item.id" :label="item.operatorsName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="游戏区服" class="w-100">
              <el-scrollbar max-height="300px" class="w-100">
                <div class="list-datas">
                  <div v-for="(item, index) in dataForm.areaBoList" :key="index" class="w-100">
                    <div class="flx-justify-between w-100 mb-12">
                      <el-icon color="#999" size="16" @click="changeFold(index)" :style="{ transform: item.fold ? `rotate(0deg)` : `rotate(90deg)`, transition: `all 0.3s` }">
                        <CaretRight />
                      </el-icon>
                      <el-input v-model="item.title" />
                      <el-button type="primary" @click="handleAddFather()">添加一级分类</el-button>
                      <div style="width: 60px; margin-left: 12px">
                        <el-button type="danger" v-if="dataForm.areaBoList.length > 1" :icon="Delete" @click="handleDelFather(item, index)"></el-button>
                      </div>
                    </div>
                    <div class="ml-30" v-if="!item.fold">
                      <div v-for="(item2, index2) in item.children" :key="index2">
                        <div class="flx-justify-between sonItem">
                          <div style="width: 31px; height: 55px">
                            <img :src="brokeLine" style="width: 31px; height: 32px" v-if="index2 == 0" />
                          </div>
                          <el-input v-model="item2.title" />
                          <el-button color="#668AC4" @click="handleAddSon(item2, index)">添加二级分类</el-button>
                          <div style="width: 60px; margin-left: 12px">
                            <el-button type="danger" v-if="item.children.length > 1" :icon="Delete" @click="handleDelSon(index2, index)"></el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <template v-slot:footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits } from "vue";
import { Delete } from "@element-plus/icons-vue";
import { uuid } from "@/components/ny-flowable/package/utils";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";
import brokeLine from "@/assets/images/brokeLine.png";

const { t } = useI18n();
const emits = defineEmits(["refreshDataList"]);

const gameCode = ref("");
const gameId = ref("");

const visible = ref(false);
const dataForm = ref<any>({
  id: "",
  // 游戏名称
  title: "",
  // 是否热门游戏
  isHot: "",
  //   游戏运营商
  operatorsId: "",
  // 运行设备
  gtype: "",
  // 图标
  thumbnail: "",
  // 是否上架 0上架 1下架 默认0
  state: 0,
  // 游戏区服
  listAreaVo: [] as { name: string }[],
  areaBoList: [{ id: "", title: "", pid: "0", areaCode: "", fold: true, children: [{ id: "", pid: "", title: "", areaCode: "" }] }]
});
const rules = {
  title: [{ required: true, message: "请输入游戏名称", trigger: "blur" }],
  gtype: [{ required: true, message: "请选择运行设备", trigger: "change" }],
  operatorsId: [{ required: true, message: "请选择游戏运营商", trigger: "change" }],
  thumbnail: [{ required: true, message: "请上传图标", trigger: "change" }],
  listIcon: [{ required: true, message: "请上传游戏列表图标", trigger: "change" }],
  smallIcons: [{ required: true, message: "请上传游戏首页小图标", trigger: "change" }],
  largeIcon: [{ required: true, message: "请上传游戏首页大图标", trigger: "change" }]
};
const operatorsGameList = ref([]);

const init = async (id: number) => {
  visible.value = true;
  if (id) {
    getDetail(id);
  }
  //   获取运营商下拉数据
  let res = await baseService.get("/operators/sysoperators/page", { limit: 9999 });
  if (res.code == 0) {
    operatorsGameList.value = res.data.list || [];
  }
};

// 获取详情
const dataLoading = ref(false);
const getDetail = async (id: number) => {
  dataLoading.value = true;
  let res = await baseService.get("/game/sysgame/get/" + id);
  dataLoading.value = false;
  if (res.code == 0) {
    Object.assign(dataForm.value, res.data);
    dataForm.value.areaBoList = dataForm.value.areaDtoList;
    delete dataForm.value.areaList;
  }

  if (dataForm.value.areaBoList.length == 0) {
    dataForm.value.areaBoList.push({ id: "", code: "", name: "", pid: "0", gameCode: gameCode.value, gameId: gameId.value, fold: true, children: [{ id: "", pid: "", title: "" }] });
  } else {
    dataForm.value.areaBoList.forEach((item: any) => {
      if (!item.children || !item.children.length) {
        item.children = [{ id: "", pid: "", title: "", gameCode: gameCode.value, gameId: gameId.value }];
      }
    });
  }
};

// 折叠按钮
const changeFold = (index: any) => {
  dataForm.value.areaBoList[index].fold = !dataForm.value.areaBoList[index].fold;
};
// 添加一级分类
const handleAddFather = () => {
  dataForm.value.areaBoList.push({ id: "", title: "", pid: "0", gameCode: gameCode.value, gameId: gameId.value, fold: true, children: [{ id: "", pid: "", title: "", gameCode: gameCode.value, gameId: gameId.value }] });
};

// 添加二级分类
const handleAddSon = (row: any, index: any) => {
  dataForm.value.areaBoList[index].children.push({ id: "", code: "", title: "", pid: row.id || "", gameCode: gameCode.value, gameId: gameId.value });
};

// 删除一级分类
const handleDelFather = (row: any, index: any) => {
  dataForm.value.areaBoList.splice(index, 1);
};

// 删除二级分类
const handleDelSon = (index2: any, index: any) => {
  dataForm.value.areaBoList[index].children.splice(index2, 1);
};

// 提交
const dataFormRef = ref();
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    let arrs: any = Object.assign({}, dataForm.value);
    let arrs2: any = JSON.parse(JSON.stringify(dataForm.value.areaBoList));
    let operatorObj = operatorsGameList.value.find((ele) => ele.id == arrs.operatorsId);
    arrs.operatorsName = operatorObj?.operatorsName || "";

    arrs2.forEach((element: any) => {
      delete element.fold;
      if (element.children && element.children.length > 0) {
        element.children = element.children.filter((ele: any) => {
          return ele.title != "";
        });
      }
    });
    let arrs3: any = [];
    arrs2.forEach((ele: any) => {
      if (ele.title != "") {
        arrs3.push(ele);
      }
    });
    arrs.areaBoList = arrs3;
    btnLoading.value = true;
    (!dataForm.value.id ? baseService.post : baseService.put)(!dataForm.value.id ? "/game/sysgame/save" : "/game/sysgame/update", arrs)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emits("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.list-datas {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;

  :deep(.el-input) {
    flex: 1;
    margin: 0 10px;
  }

  :deep(.el-button) {
    color: #fff;
  }
}
</style>
