<template>
  <el-dialog v-model="visible" :title="!isEdit ? '新增信息' : '编辑信息'" width="80%" top="5vh" @close="closeFn">
    <el-table v-loading="loadingImg" element-loading-text="图片上传中···" :data="tableData" border class="elTable" style="width: 100%">
      <el-table-column align="center" :width="item.name.length < 10 ? 200 : item.name.length * 20" v-for="(item, index) in tableHeader">
        <!-- 表头 -->
        <template #header>
          <span style="color: red" v-if="item.isMust">*</span>
          <span>{{ item.name }}</span>
        </template>
        <!-- 内容 -->
        <template #default="{ row }">
          <el-input v-model="row[`${item.dynamicKey}`]" v-if="item.type == 1" />
          <el-input v-model="row[`${item.dynamicKey}`]" type="number" style="width: 100%" :disabled="item.rule != null" v-if="item.type == 2" />
          <el-select v-model="row[`${item.dynamicKey}`]" placeholder="请选择" style="width: 100%" v-if="item.type == 3">
            <el-option v-for="it in item.options.split(',')" :key="it" :label="it" :value="it" />
          </el-select>
          <el-date-picker v-model="row[`${item.dynamicKey}`]" type="date" placeholder="请选择日期" format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 100%" v-if="item.type == 4" />
          <div v-if="item.type == 5" style="display: flex; justify-content: center; width: 100%">
            <el-image v-if="row[`${item.dynamicKey}`]" style="width: 50px; height: 50px; border-radius: 4px" :src="row[`${item.dynamicKey}`]" fit="contain" />
            <el-upload class="tableUpload" ref="upload" :http-request="(option:any) => { httpRequest(option,row,item.dynamicKey) }" :show-file-list="false" :auto-upload="true" list-type="picture-card">
              <el-icon><Plus /></el-icon>
            </el-upload>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="180" v-if="!isEdit">
        <template #default="scope">
          <el-button type="primary" size="small" @click="copyItemFn(scope)">复制</el-button>
          <el-button type="danger" size="small" @click="deleteItemFn(scope)">删除</el-button>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-button type="primary" style="width: 10%; margin-top: 10px" @click="onAddItem" v-if="!isEdit">新增</el-button>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="butLoading" @click="submit">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);

const url = `${app.api}/sys/oss/upload?token=${getToken()}`; // 文件上传后端地址
const tableHeader = ref(<any>[]); // 表头数据
const tableData = ref(<any>[]); // 表格数据
const isEdit = ref(false); // 是否编辑
const loadingImg = ref(false); // 图片上传加载
const menuInfo = ref(<any>""); // 菜单信息

const getInfo = (indexName: any) => {
  baseService.get("/report/reportcolumn/all", { indexName: indexName }).then((res) => {
    tableHeader.value = res.data;
    onAddItem();
  });
};

// 新增表格
const onAddItem = () => {
  if (tableHeader.value.length == 0) {
    ElMessage({
      message: "请先创建字段列名，再进行添加数据!",
      type: "warning"
    });
    return;
  }
  if (!isEdit.value) {
    let addData: any = {};
    tableHeader.value.map((item: any) => {
      if (item.type == 2) {
        addData[`${item.dynamicKey}`] = 0;
      } else {
        addData[`${item.dynamicKey}`] = "";
      }
    });
    tableData.value.push(addData);
  }
};

// 复制对象
const copyItemFn = (data: any) => {
  const item = JSON.parse(JSON.stringify(tableData.value[data.$index]));
  tableData.value.splice(data.$index, 0, item);
};

// 删除对象
const deleteItemFn = (data: any) => {
  tableData.value.splice(data.$index, 1);
};

// 图片上传
const httpRequest = (option: any, row: any, name: any) => {
  let fd = new FormData();
  fd.append("file", option.file);
  const headers = { "Content-Type": "multipart/form-data" };
  loadingImg.value = true;
  baseService
    .post(url, fd, headers)
    .then((res) => {
      if (res.code == 0) {
        Reflect.set(row, name, res.data.src);
      }
    })
    .catch(() => {
      ElMessage({
        message: "图片上传失败！",
        type: "warning"
      });
    })
    .finally(() => {
      loadingImg.value = false;
    });
};

// 数据提交保存
const butLoading = ref(false);
const submit = () => {
  if (tableHeader.value.length == 0) {
    ElMessage({
      message: "请先创建字段列名，再进行添加数据!",
      type: "warning"
    });
    return;
  }
  const mustList = tableHeader.value.filter((item: any) => item.isMust);
  try {
    mustList.map((item: any, index: number) => {
      tableData.value.map((item2: any, index2: number) => {
        if (!item2[`${item.dynamicKey}`]) {
          throw { code: 500, name: item.name };
        }
      });
    });

    butLoading.value = true;

    if (isEdit.value) {
      baseService
        .post("/report/report/update/document", tableData.value[0])
        .then((res) => {
          if (res.code == 0) {
            ElMessage.success({
              message: "操作成功",
              duration: 500,
              onClose: () => {
                visible.value = false;
                emit("refreshDataList");
              }
            });
          }
        })
        .finally(() => {
          butLoading.value = false;
        });
    } else {
      // 将菜单的标识添加到数据中
      tableData.value.map((item: any) => {
        item.id = menuInfo.value.id;
        item.indexName = menuInfo.value.indexName;
      });
      baseService
        .post("/report/report/save/documents", tableData.value)
        .then((res) => {
          if (res.code == 0) {
            ElMessage.success({
              message: "操作成功",
              duration: 500,
              onClose: () => {
                visible.value = false;
                setTimeout(() => {
                  emit("refreshDataList");
                }, 1000);
              }
            });
          }
        })
        .finally(() => {
          butLoading.value = false;
        });
    }
  } catch (error: any) {
    ElMessage({
      message: error.name + "不能为空！",
      type: "warning"
    });
  }
};

// 弹窗关闭回调
const closeFn = () => {
  isEdit.value = false;
  tableData.value = [];
};

const init = (info: any, row?: any) => {
  visible.value = true;
  menuInfo.value = info;
  if (row) {
    isEdit.value = true;
    tableData.value.push(JSON.parse(JSON.stringify(row)));
  }
  if (info) {
    getInfo(info.indexName);
  }
};

defineExpose({
  init
});
</script>
<style lang="less" scoped></style>
<style lang="less">
.tableUpload {
  margin-left: 10px;
  .el-upload-list--picture-card .el-upload-list__item {
    width: 50px;
    height: 50px;
  }
  .el-upload--picture-card {
    width: 50px;
    height: 50px;
  }
}
</style>
