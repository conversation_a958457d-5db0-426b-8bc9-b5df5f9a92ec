<template>
    <div class="item">
        <div class="msg-time" v-if="item.time"><div class="time">{{ item.time }}</div></div>
        <div class="msg-wrap">
            <img class="avatar" src="" @click="avatarClickHandle" />
            <div class="msg-content">
                <div class="nickname" v-if="item.sender">
                    <span>{{ item.sender?.nickname }}</span>
                    <el-tag type="primary" size="small">恒星官方</el-tag>
                </div>
                
                <!-- 文本 -->
                <div class="msg-text" v-if="item.messageType=='RC:TxtMsg'">
                    {{ item.content.content }}
                </div>

                <!-- 图片 -->
                <div class="msg-file" v-else-if="item.messageType=='RC:ImgMsg'">
                    <el-image :src="item.content.imageUri" :preview-src-list="[item.content.imageUri]" />
                </div>
                
                <!-- 商品 -->
                <div class="shop" v-else-if="item.messageType == 'CU:shop'">
                    <div class="title">
                        <span class="text">商品编号：SW0ZCW</span>
                        <el-text type="primary" class="pointer">复制</el-text>
                    </div>
                    <div class="shop-info flx">
                        <img class="shop-img" src="">
                        <div class="shop-info-right">
                            <div class="mle shop-title">SW58669 号 【163已实名邮箱】【任剑逍遥】99万兽一线属性满攻易成长武器</div>
                            <div class="flx-justify-between">
                                <div class="price">￥799.00</div>
                                <el-tag type="primary" size="small">永久包赔</el-tag>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { ref, defineProps } from 'vue';

    defineProps({
        item: Object
    })
</script>