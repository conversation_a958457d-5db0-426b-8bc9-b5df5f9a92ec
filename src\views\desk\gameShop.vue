<template>
  <div class="gameShop">
    <div style="width: 100%; height: 100%" ref="gameShopChartRef"></div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted, watch, nextTick } from "vue";
import * as echarts from "echarts";
import commonData from "./index.ts";
const props = defineProps({
  allData: <any>{}
});
const gameShopChartRef = ref();
const charts = ref(<any>[]);
const state = reactive({
  pie1: [],
  pie2: []
});
const GameShopChart = () => {
  const userGrowthChart = echarts.init(gameShopChartRef.value);
  const option = {
    legend: {
      left: "center",
      bottom: "30",
      icon: "circle"
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)"
    },
    series: [
      {
        type: "pie",
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 2
        },
        radius: ["25%", "40%"],
        center: ["25%", "35%"],
        label: {
          show: true,
          formatter: "{d}%"
        },
        data: state.pie1 || []
      },
      {
        type: "pie",
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 2
        },
        radius: ["25%", "40%"],
        center: ["25%", "35%"],
        label: {
          normal: {
            show: true,
            position: "center", // 展示在中间位置
            formatter: () => {
              return `{a|出售}\n{a|游戏占比}`;
            },
            rich: {
              a: {
                fontWeight: 800
              }
            }
          }
        },
        data: state.pie1 || []
      },
      {
        type: "pie",
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 2
        },
        radius: ["25%", "40%"],
        center: ["75%", "35%"],
        label: {
          show: true,
          formatter: "{d}%"
        },
        data: state.pie2 || []
      },
      {
        type: "pie",
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 2
        },
        radius: ["25%", "40%"],
        center: ["75%", "35%"],
        label: {
          normal: {
            show: true,
            position: "center", // 展示在中间位置
            formatter: () => {
              return `{a|库存}\n{a|游戏占比}`;
            },
            rich: {
              a: {
                fontWeight: 800
              }
            }
          }
        },
        data: state.pie2 || []
      }
    ]
  };
  userGrowthChart.setOption(option);
  charts.value.push(userGrowthChart);
};
watch(
  () => props.allData,
  () => {
    if (props.allData) {
      if (props.allData.pieData1) {
        let pieData1 = props.allData.pieData1.slice(0, 10);
        state.pie1 = pieData1.map((ele: any) => {
          return {
            name: ele.title,
            value: ele.nums
          };
        });
      } else {
        state.pie1 = [];
      }
      if (props.allData.pieData2) {
        let pieData2 = props.allData.pieData2.slice(0, 10);
        state.pie2 = pieData2.map((ele: any) => {
          return {
            name: ele.title,
            value: ele.nums
          };
        });
      } else {
        state.pie2 = [];
      }
      nextTick(() => {
        GameShopChart();
      });
    }
  }
);
onMounted(async () => {});
</script>

<style lang='less' scoped>
.gameShop {
  width: 100%;
  height: 288px;
}
</style>