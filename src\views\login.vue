<template>
  <div class="header_video">
    <video :controls="false" autoplay width="100%" :height="videoHeight" loop muted playsinline class="video_bg">
      <source src="@/assets/video/login.mp4" type="video/mp4">
      您的浏览器不支持HTML5视频。
    </video>
    <div class="login_center">
      <div class="ad_center">
        <div class="lefts">
          <div class="slogan">
            <svg width="380" height="104" viewBox="0 0 380 104" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.424 10.268H1.4V4.316H5.72L6.44 1.1H12.584L11.912 4.316H19.88V10.268H10.616L8.456 20.348H10.376V13.1H15.896V20.348H19.784V26.396H15.896V30.476L19.88 29.852V36.044L15.896 36.668V44.3H9.8V37.628L0.92 39.02V32.828L9.8 31.436V26.396H0.872L4.424 10.268ZM20.408 37.868H29.672V29.036H21.608V23.132H43.592V29.036H35.816V37.868H45.08V43.772H20.408V37.868ZM20.744 15.068C26.536 14.108 30.984 11.628 34.088 7.628H21.704V1.676H43.448C42.36 5.996 40.552 9.628 38.024 12.572L44.648 15.308V21.356L33.272 16.844C29.784 19.212 25.608 20.668 20.744 21.212V15.068ZM66.104 44.06C69.4 35.548 71.96 26.988 73.784 18.38H80.168C78.568 25.548 76.792 32.124 74.84 38.108H84.536C83.544 34.236 82.616 30.092 81.752 25.676H87.704C88.824 31.628 90.392 37.756 92.408 44.06H66.104ZM48.872 40.988C50.12 32.444 50.744 23.436 50.744 13.964H55.736C55.736 24.172 55.16 33.18 54.008 40.988H48.872ZM49.928 6.044H57.56V0.859997H63.56V6.044H67.736V11.996H63.56V18.572L65.336 19.676C67.8 13.756 69.512 7.564 70.472 1.1H76.616C75.688 7.98 73.96 14.604 71.432 20.972H67.352V27.932L63.56 25.58V44.3H57.56V11.996H49.928V6.044ZM87.896 1.1C88.888 7.916 90.776 14.54 93.56 20.972H86.936C84.408 14.604 82.68 7.98 81.752 1.1H87.896ZM139.688 44.3H133.544V29.9H104.456V44.3H98.312V24.044H139.688V44.3ZM98.024 2.684H115.928V0.139997H122.072V2.684H139.976V8.54H98.024V2.684ZM102.968 10.412H135.032V22.172H102.968V10.412ZM108.2 31.724H129.8V42.668H108.2V31.724ZM128.792 17.612V15.308H109.208V17.612H128.792ZM123.848 38.3V36.092H114.152V38.3H123.848ZM168.68 44.252C164.424 42.556 160.728 40.3 157.592 37.484C154.168 40.588 149.992 43.02 145.064 44.78V38.108C148.04 36.988 150.664 35.436 152.936 33.452C150.248 29.868 148.52 25.58 147.752 20.588H153.608C154.312 23.98 155.64 26.956 157.592 29.516C158.456 28.332 159.144 27.036 159.656 25.628C160.2 24.188 160.68 22.508 161.096 20.588H166.952C166.536 23.244 165.96 25.628 165.224 27.74C164.488 29.82 163.496 31.724 162.248 33.452C164.52 35.436 167.144 36.988 170.12 38.108V41.996C172.232 38.508 174.136 34.796 175.832 30.86L172.712 21.26H167.192L167.576 20.06L159.416 16.94V10.988L168.152 14.348V18.284C169.048 15.34 169.784 12.492 170.36 9.74C170.936 6.988 171.4 3.98 171.752 0.715999H177.944C177.72 2.828 177.512 4.524 177.32 5.804H189.608V12.044H187.496C186.888 17.964 185.544 23.932 183.464 29.948L189.224 44.3H182.984L179.96 36.668C178.744 39.804 177.528 42.348 176.312 44.3H170.12V44.78L168.824 44.3H168.632L168.68 44.252ZM145.544 3.98H154.664V0.139997H160.904V3.98H169.16V10.028H145.544V3.98ZM145.592 14.348L155.288 10.988V16.94L145.592 20.3V14.348ZM179.24 23.612C180.488 19.612 181.336 15.756 181.784 12.044H176.216C176.216 12.172 176.184 12.364 176.12 12.62L179.24 23.612ZM199.496 5.564L200.12 0.859997H206.408L205.784 5.564H212.504V16.94C214.68 11.692 215.896 6.252 216.152 0.619997H222.584C222.552 2.092 222.424 3.74 222.2 5.564H235.88V44.3H224.984V38.06H229.688V11.708H221.096C220.488 14.236 219.736 16.492 218.84 18.476H212.504V43.628H194.024V5.564H199.496ZM206.504 37.724V27.164H200.024V37.724H206.504ZM206.504 21.404V11.468H200.024V21.404H206.504ZM222.968 19.58L225.992 31.292H219.656L216.632 19.58H222.968ZM8.744 102.3C10.312 98.94 11.416 95.66 12.056 92.46C12.696 89.228 13.016 85.628 13.016 81.66V69.852H10.712V63.66H15.656V59.1H21.704V63.66H26.456V69.852H18.968V74.7H26.264V102.3H18.008V96.252H20.984V80.796H18.344V81.66C18.344 85.5 18.072 89.084 17.528 92.412C17.016 95.708 16.184 99.004 15.032 102.3H8.744ZM0.584 102.3C2.376 95.42 3.544 88.604 4.088 81.852H10.04C9.4 89.756 8.36 96.572 6.92 102.3H0.584ZM1.064 70.188L10.184 72.396V78.684L1.064 76.572V70.188ZM1.832 59.1L9.944 61.116V67.548L1.832 65.532V59.1ZM26.984 83.868H34.664L36.68 78.78H27.848V72.924H44.552L40.424 83.868H45.32V89.724H39.896V102.3H29.432V96.3H33.896V89.724H26.984V83.868ZM29.192 59.1H35.576L34.616 63.9H44.984V69.852H27.032L29.192 59.1ZM68.648 94.332C70.088 93.884 71.368 93.292 72.488 92.556C73.64 91.788 74.744 90.844 75.8 89.724C74.168 85.884 73.352 81.196 73.352 75.66H67.928V69.804H73.256V58.86H79.4V69.804H91.88V75.66H79.544C79.576 77.836 79.672 79.724 79.832 81.324C79.992 82.924 80.296 84.38 80.744 85.692C82.056 83.42 83.08 80.844 83.816 77.964H89.624C88.472 83.116 86.68 87.532 84.248 91.212C86.392 93.196 89.336 94.796 93.08 96.012V103.116C87.128 101.612 82.52 99.004 79.256 95.292C76.312 98.076 72.776 100.14 68.648 101.484V94.332ZM49.352 93.996C51.528 92.3 53.4 90.396 54.968 88.284C53.4 85.02 52.312 81.628 51.704 78.108C51.128 74.556 50.872 71.1 50.936 67.74H50.072V62.172H67.016L66.68 67.356C66.488 71.708 65.976 75.628 65.144 79.116C64.344 82.572 63.112 85.788 61.448 88.764C62.888 90.62 64.632 92.316 66.68 93.852V101.292C63.32 99.244 60.488 96.684 58.184 93.612C55.88 96.524 52.936 99.324 49.352 102.012V93.996ZM56.312 67.74C56.344 70.844 56.488 73.58 56.744 75.948C57 78.284 57.464 80.46 58.136 82.476C59.736 78.508 60.664 73.596 60.92 67.74H56.312ZM86.504 59.916L89.144 68.268H83.48L80.84 59.916H86.504ZM106.232 70.428L104.648 66.492H97.4V60.54H115.832V58.14H122.168V60.54H140.6V66.492H133.352L131.768 70.428H139.4V102.3H133.304V86.94H129.224V99.852H108.776V86.94H104.696V102.3H98.6V70.428H106.232ZM110.6 77.292H117.08L113.528 82.812H124.472L120.92 77.292H127.4L133.304 86.412V76.284H104.696V86.412L110.6 77.292ZM113 70.428H125L126.584 66.492H111.416L113 70.428ZM123.224 94.38V88.284H114.776V94.38H123.224ZM146.12 60.732H163.832V58.14H170.168V60.732H187.88V71.004H182.072V74.364H169.736C170.44 76.38 171.048 78.54 171.56 80.844C173.352 81.708 174.984 82.636 176.456 83.628L181.208 75.516H187.976L182.216 85.308H178.616C183.384 89.372 186.792 95.036 188.84 102.3H181.736C180.168 96.028 177.144 91.468 172.664 88.62C172.952 91.98 173.096 96.54 173.096 102.3H166.856C166.856 100.412 166.84 99.02 166.808 98.124C163.928 98.988 160.744 99.756 157.256 100.428C153.768 101.132 149.816 101.804 145.4 102.444V96.3C152.952 95.308 159.992 93.852 166.52 91.932C166.488 91.292 166.408 90.332 166.28 89.052C161.352 90.556 154.776 91.948 146.552 93.228V86.796C152.792 85.9 159.016 84.54 165.224 82.716C165.16 82.364 164.984 81.58 164.696 80.364C160.28 81.644 154.584 82.828 147.608 83.916V77.964C153.592 77.1 158.68 76.044 162.872 74.796C162.808 74.604 162.744 74.46 162.68 74.364H151.928V71.004H146.12V60.732ZM181.64 69.084V66.54H152.36V69.084H181.64ZM193.4 95.004H212.168V85.452H196.616V79.5H212.168L212.12 70.668H202.088C201.416 72.876 200.68 74.876 199.88 76.668H192.728C195.416 70.908 197.144 65.212 197.912 59.58H204.296C204.072 61.436 203.816 63.116 203.528 64.62H212.12V58.62H218.456V64.62H235.496V70.668H218.456V79.5H233.384V85.452H218.456V95.004H236.6V101.34H193.4V95.004ZM252.104 83.868H258.152V96.348H269.192V90.396H275.144V102.108H252.104V83.868ZM240.92 94.14C243.672 92.092 245.24 88.844 245.624 84.396H251C250.84 88.556 249.912 92.012 248.216 94.764C246.52 97.516 244.088 99.676 240.92 101.244V94.14ZM241.4 76.236C249.176 75.02 255.224 72.14 259.544 67.596H242.504V61.692H259.832V58.14H266.168V61.692H283.496V67.596H266.456C270.776 72.14 276.824 75.02 284.6 76.236V82.524C275.032 81.084 267.832 77.82 263 72.732C261.688 74.14 260.056 75.484 258.104 76.764H261.656L263.096 82.188H256.856L255.752 78.108C251.752 80.22 246.968 81.692 241.4 82.524V76.236ZM266.312 83.004L267.992 92.796H261.992L260.312 83.004H266.312ZM280.376 84.396C280.76 88.844 282.328 92.092 285.08 94.14V101.244C281.912 99.676 279.48 97.516 277.784 94.764C276.088 92.012 275.16 88.556 275 84.396H280.376ZM288.92 81.132H307.88V65.964H289.928V59.916H332.072V65.964H314.12V81.132H333.08V87.18H314.12V102.3H307.88V87.18H288.92V81.132ZM299.144 68.22L302.168 78.972H295.784L292.76 68.22H299.144ZM322.856 68.22H329.24L326.216 78.972H319.832L322.856 68.22ZM376.904 101.628H341.096V81.228H376.904V101.628ZM339.176 71.196H343.4L351.752 59.1H359.912L351.56 71.196H368.648L365.528 65.1H373.208L379.448 77.34H339.176V71.196ZM370.616 95.628V87.276H347.384V95.628H370.616Z" fill="#303133"/>
            </svg>
          </div>
          <div class="slogan_info">我们致力于为商家构建一个专属的生态平台，并持续为商家在行业内 <br> 探索和提供更为优质的发展路径</div>
          <div class="tabs">
            <div class="tabItem">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.33268 10.0003C3.33268 6.31843 6.31745 3.33366 9.99935 3.33366C13.6813 3.33366 16.666 6.31843 16.666 10.0003C16.666 13.6822 13.6813 16.667 9.99935 16.667C6.31745 16.667 3.33268 13.6822 3.33268 10.0003ZM9.99935 1.66699C5.39697 1.66699 1.66602 5.39795 1.66602 10.0003C1.66602 14.6027 5.39697 18.3337 9.99935 18.3337C14.6017 18.3337 18.3327 14.6027 18.3327 10.0003C18.3327 5.39795 14.6017 1.66699 9.99935 1.66699ZM14.5469 7.88125L13.3684 6.70273L9.16602 10.9052L6.83861 8.57774L5.66009 9.75624L9.16602 13.2622L14.5469 7.88125Z" fill="var(--el-color-primary)"/>
              </svg>
              <span>安全高效</span>
            </div>
            <div class="tabItem">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.33268 10.0003C3.33268 6.31843 6.31745 3.33366 9.99935 3.33366C13.6813 3.33366 16.666 6.31843 16.666 10.0003C16.666 13.6822 13.6813 16.667 9.99935 16.667C6.31745 16.667 3.33268 13.6822 3.33268 10.0003ZM9.99935 1.66699C5.39697 1.66699 1.66602 5.39795 1.66602 10.0003C1.66602 14.6027 5.39697 18.3337 9.99935 18.3337C14.6017 18.3337 18.3327 14.6027 18.3327 10.0003C18.3327 5.39795 14.6017 1.66699 9.99935 1.66699ZM14.5469 7.88125L13.3684 6.70273L9.16602 10.9052L6.83861 8.57774L5.66009 9.75624L9.16602 13.2622L14.5469 7.88125Z" fill="var(--el-color-primary)"/>
              </svg>
              <span>稳定可靠</span>
            </div>
            <div class="tabItem">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.33268 10.0003C3.33268 6.31843 6.31745 3.33366 9.99935 3.33366C13.6813 3.33366 16.666 6.31843 16.666 10.0003C16.666 13.6822 13.6813 16.667 9.99935 16.667C6.31745 16.667 3.33268 13.6822 3.33268 10.0003ZM9.99935 1.66699C5.39697 1.66699 1.66602 5.39795 1.66602 10.0003C1.66602 14.6027 5.39697 18.3337 9.99935 18.3337C14.6017 18.3337 18.3327 14.6027 18.3327 10.0003C18.3327 5.39795 14.6017 1.66699 9.99935 1.66699ZM14.5469 7.88125L13.3684 6.70273L9.16602 10.9052L6.83861 8.57774L5.66009 9.75624L9.16602 13.2622L14.5469 7.88125Z" fill="var(--el-color-primary)"/>
              </svg>
              <span>优质体验</span>
            </div>
          </div>
        </div>
        <div class="rights">
          <div class="rights_cent">
            <img class="logo-img" :src="state.logoImg" alt="logo" />
            <div class="loginTit">
              <span>{{ state.title1 }}</span>
              <span class="loader_animation_name">{{ state.title2 }}</span>
              <span>{{ state.title3 }}</span>
            </div>
            <div class="frame">
              <div class="frameItem">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_570_61957)">
                  <path d="M9.16732 11.719V16.6673H10.834V11.719C14.1223 12.129 16.6673 14.934 16.6673 18.334H3.33398C3.33401 16.7101 3.92664 15.1421 5.00065 13.9242C6.07467 12.7063 7.55623 11.9221 9.16732 11.719ZM10.0007 10.834C7.23815 10.834 5.00065 8.59648 5.00065 5.83398C5.00065 3.07148 7.23815 0.833984 10.0007 0.833984C12.7632 0.833984 15.0007 3.07148 15.0007 5.83398C15.0007 8.59648 12.7632 10.834 10.0007 10.834Z" fill="var(--el-color-primary)"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_570_61957">
                      <rect width="20" height="20" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
                <el-input v-model="login.username" :placeholder="$t('ui.login.userNamePlaceholder')" autocomplete="off" class="customInput"></el-input>
              </div>
              <div class="frameItem" style="margin-top: 20px;">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_570_61969)">
                    <path d="M15.8333 8.33268H16.6667C16.8877 8.33268 17.0996 8.42048 17.2559 8.57676C17.4122 8.73304 17.5 8.945 17.5 9.16602V17.4993C17.5 17.7204 17.4122 17.9323 17.2559 18.0886C17.0996 18.2449 16.8877 18.3327 16.6667 18.3327H3.33333C3.11232 18.3327 2.90036 18.2449 2.74408 18.0886C2.5878 17.9323 2.5 17.7204 2.5 17.4993V9.16602C2.5 8.945 2.5878 8.73304 2.74408 8.57676C2.90036 8.42048 3.11232 8.33268 3.33333 8.33268H4.16667V7.49935C4.16667 6.7333 4.31755 5.97476 4.6107 5.26703C4.90386 4.5593 5.33354 3.91623 5.87521 3.37456C6.41689 2.83288 7.05995 2.4032 7.76768 2.11005C8.47541 1.8169 9.23396 1.66602 10 1.66602C10.766 1.66602 11.5246 1.8169 12.2323 2.11005C12.9401 2.4032 13.5831 2.83288 14.1248 3.37456C14.6665 3.91623 15.0961 4.5593 15.3893 5.26703C15.6825 5.97476 15.8333 6.7333 15.8333 7.49935V8.33268ZM14.1667 8.33268V7.49935C14.1667 6.39428 13.7277 5.33447 12.9463 4.55307C12.1649 3.77167 11.1051 3.33268 10 3.33268C8.89493 3.33268 7.83512 3.77167 7.05372 4.55307C6.27232 5.33447 5.83333 6.39428 5.83333 7.49935V8.33268H14.1667ZM9.16667 11.666V14.9993H10.8333V11.666H9.16667Z" fill="var(--el-color-primary)"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_570_61969">
                      <rect width="20" height="20" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
                <el-input :placeholder="$t('ui.login.passwordPlaceholder')" v-model="login.password" autocomplete="off" show-password class="customInput"></el-input>
              </div>
              <el-button type="primary" size="small" :disabled="state.loading" @click="onLogin" class="frame_but">
                <!-- {{ $t("ui.login.loginBtn") }} -->
                  立即登录<el-icon class="el-icon--right"><Right /></el-icon>
              </el-button>
            </div>
            <div class="records">
              <div class="record">{{ sttingInfo.info.pcIcp }}</div>
              <div class="record">{{ sttingInfo.info.pcPoliceIcp }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="contact">
      <div class="Info">技术支持：恒星团队</div>
      <div class="Info" style="margin-left: 16px;">联系我们：17866616173</div>
    </div>
  </div>
  
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";
import { CacheTenantCode, CacheToken } from "@/constants/cacheKey";
import Lang from "@/components/base/lang/index";
import baseService from "@/service/baseService";
import { getCache, setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import SvgIcon from "@/components/base/svg-icon/index";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import loginBg1 from "@/assets/images/login_bg1.png";
import loginBg2 from "@/assets/images/login_bg2.png";
import logo from "@/assets/images/logo.png";
import { useSettingStore } from "@/store/setting";

const store = useAppStore();
const sttingInfo = useSettingStore();

console.log(sttingInfo.info,'==== sttingInfo.info ====')

const router = useRouter();
const { t } = useI18n();

const state = reactive({
  captchaUrl: "",
  loading: false,
  tenantList: [] as any[],
  tenantCode: "",
  tenantMode: "",
  year: new Date().getFullYear(),
  logoImg: "",
  title1: "",
  title2: "",
  title3: ""
});

const login = reactive({ username: "", password: "", captcha: "1", uuid: getUuid() });

const videoHeight = ref('');
const pageWidth = ref('');

onMounted(async () => {
  videoHeight.value = window.innerHeight + 'px';
  pageWidth.value = window.innerWidth + 'px';
  //清理数据
  // store.logout();
  // getCaptchaUrl();
  state.tenantMode = app.tenantMode;

  if (app.tenantMode === "code") {
    await getTenantList();
  }
  state.tenantCode = getCache(CacheTenantCode, { isParse: false }) || "10000";

   // 监听登录成功
  const logChannel = new BroadcastChannel('login')
  logChannel.onmessage = (e) => {
      if (e.data.name === 'login') {
        router.push(e.data.path);
      }
  }
});

watch(
  () => sttingInfo.info.backendLogo,
  (val) => {
    state.logoImg = sttingInfo.info.backendLogo;
    if (sttingInfo.info.backendTitle && sttingInfo.info.backendTitle.indexOf("商家管理系统") > -1) {
      let ars = sttingInfo.info.backendTitle.split("商家管理系统");
      state.title1 = ars ? ars[0].slice(0, ars[0].length - 1) : "";
      state.title2 = ars ? ars[0].slice(-1) : "";
      state.title3 = "商家管理系统" + ars[1];
    } else {
      let ars = sttingInfo.info.backendTitle;
      state.title1 = ars?.slice(0, ars.length - 1);
      state.title2 = ars?.slice(-1);
    }
  },
  { immediate: true, deep: true }
);

const formRef = ref();
const onLogin = () => {
  if(!login.username){
    ElMessage.warning('请输入用户名');
    return;
  }
  if(!login.password){
    ElMessage.warning('请输入密码');
    return
  }
  state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            setCache(CacheToken, res.data, false);
            ElMessage.success(t("ui.login.loginOk"));

            baseService.get("/sys/user/info").then((ele) => {

              const logOut = new BroadcastChannel('login')
              logOut.postMessage({
                name: 'login',
                path: ele.data.tenantCode == "10000" ? '/' : '/tenantMenu/shopCenter/shop/shop'
              })

              if (ele.data.tenantCode == "10000") {
                router.push("/");
              } else {
                router.push("/tenantMenu/shopCenter/shop/shop");
              }
            });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          // onRefreshCode();
        });
  // formRef.value.validate((valid: boolean) => {
  //   if (valid) {
  //     state.loading = true;
  //     baseService
  //       .post("/login", login)
  //       .then((res) => {
  //         state.loading = false;
  //         if (res.code === 0) {
  //           setCache(CacheToken, res.data, false);
  //           ElMessage.success(t("ui.login.loginOk"));

  //           baseService.get("/sys/user/info").then((ele) => {

  //             const logOut = new BroadcastChannel('login')
  //             logOut.postMessage({
  //               name: 'login',
  //               path: ele.data.tenantCode == "10000" ? '/' : '/tenantMenu/shopCenter/shop/shop'
  //             })

  //             if (ele.data.tenantCode == "10000") {
  //               router.push("/");
  //             } else {
  //               router.push("/tenantMenu/shopCenter/shop/shop");
  //             }
  //           });
  //         } else {
  //           ElMessage.error(res.msg);
  //         }
  //       })
  //       .catch(() => {
  //         state.loading = false;
  //         // onRefreshCode();
  //       });
  //   }
  // });
};
const getTenantList = async () => {
  const res = await baseService.get("/sys/tenant/list");
  state.tenantList = res.data;
};
const onChange = (value: string) => {
  setCache(CacheTenantCode, value + "");
};

// const getCaptchaUrl = () => {
//   login.uuid = getUuid();
//   state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
// };

// const onRefreshCode = () => {
//   getCaptchaUrl();
// };

const rules = ref({
  username: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  password: [{ required: true, message: t("validate.required"), trigger: "blur" }]
  // captcha: [{ required: true, message: t("validate.required"), trigger: "blur" }]
});
</script>

<style lang="less" scoped>
@import url("@/assets/theme/base.less");
.header_video{
  height: v-bind(videoHeight);
  background-color: #000000;
  position: relative;
  .video_bg{
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 0;
    object-fit: cover;
  }
  .login_center{
    position: absolute;
    top: 0;
    left: 0;
    width: v-bind(pageWidth);
    height: v-bind(videoHeight);
    display: grid;
    .ad_center{
      // width: 1200px;
      margin: auto;
      // margin-top: 120px;
      // border: 1px solid red;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .lefts{
        width: 680px;
        height: 600px;
        display: flex;
        flex-direction: column;
        .slogan{
          margin-top: 108px;
        }
        .slogan_info{
          font-size: 16px;
          color: #909399;
          line-height: 21px;
          margin-top: 16px;
        }
        .tabs{
          display: flex;
          margin-top: 24px;
          .tabItem{
            display: flex;
            align-items: center;
            padding: 6.5px 12px;
            border: 1px solid var(--el-color-primary);
            border-radius: 4px;
            span{
              font-size: 14px;
              line-height: 14px;
              color: var(--el-color-primary);
              margin-left: 4px;
            }
          }
          .tabItem + .tabItem {
            margin-left: 10px;
          }
        }
      }
      .rights{
        width: 400px;
        height: 600px;
        // border: 1px solid transparent;
        border-radius: 16px;
        // background-clip: padding-box, border-box;
        // background-origin: padding-box, border-box;
        // background-image: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,1)), linear-gradient(to bottom , var(--el-color-primary) 40%, #FFFFFF 60%);
        // border: 1px solid red;
        background: linear-gradient(to bottom, var(--el-color-primary) 40%, rgba(255,255,255,0) 60%);
        position: relative;
        .rights_cent{
          border-radius: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-top: 68px;
          width: 398px;
          height: 598px;
          position: absolute;
          top: 1px;
          left: 1px;
          background: linear-gradient(to bottom, #FFF 70%, rgba(255,255,255,0));
          .logo-img{
            height: 56px;
          }
          .frame{
            margin-top: 40px;
            .frameItem{
              width: 320px;
              display: flex;
              align-items: center;
              padding: 8px 12px;
              border: 1px solid #EBEEF5;
              border-radius: 4px;
              background: #fafafa;
            }
            .frame_but{
              width: 320px;
              height: 50px;
              border-radius: 8px;
              margin-top: 40px;
            }
          }
        }
      }
    }

  }
}
.loginTit {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  span {
    // padding: 70px 0 20px;
    display: inline-block;
    font-size: 24px;
    font-weight: 400;
    line-height: 1;
    letter-spacing: 3px;
    color: #303133;
    font-weight: 700;
  }
}
/* 标题文字抖动 -s */
.loader_animation_name {
  animation: dou 1s infinite linear;
}

@keyframes dou {
  0% {
    transform: rotate(0);
  }

  11% {
    transform: rotate(7.61deg);
  }

  23% {
    transform: rotate(-5.8deg);
  }

  36% {
    transform: rotate(3.35deg);
  }

  49% {
    transform: rotate(-1.9deg);
  }

  62% {
    transform: rotate(1.12deg);
  }

  75% {
    transform: rotate(-0.64deg);
  }

  88% {
    transform: rotate(0.37deg);
  }

  100% {
    transform: rotate(-0.28deg);
  }
}

/* 标题文字抖动 -e */

:deep(.el-input__wrapper){
  background-color: #fafafa;
  box-shadow: none;
}
:deep(.el-input__wrapper:hover){
  background-color: #fafafa;
  box-shadow: none;
}

.records{
  margin-top: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .record{
    font-size: 12px;
    color: #909399;
    line-height: 18px;
  }
}

.contact{
  position: absolute;
  bottom: 48px;
  left: 0;
  right: 0;
  // border: 1px solid red;
  display: flex;
  align-items: center;
  justify-content: center;
  .Info{
    font-size: 16px;
    line-height: 21px;
    color: #909399;
  }
}

@media (max-width: 1920px) {
  .ad_center{
    width: 1200px;
    margin-top: 120px;
  }
  .contact{
    bottom: 48px;
  }
}

@media (max-width: 1440px) {
  .ad_center{
    width: 1100px ;
    margin-top: 60px;
  }
  .contact{
    bottom: 28px;
  }
}
</style>
