<template>
  <div class="container bargain-wrap">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ul class="headCardUl clearSty">
        <template v-for="(cardItem, index) in cardInfo">
          <li v-if="cardItem.isShow" class="headCardLi clearSty" :key="index">
            <!-- 头部 -->
            <div class="head flex">
              <div class="flex">
                <el-icon style="margin-right: 4px">
                  <WalletFilled color="#F44A29" />
                </el-icon>
                <span>{{ cardItem.top.label }}</span>
              </div>
              <div>
                <el-button style="margin-left: 12px" @click="btnItem.handle" :type="btnItem.type" v-for="(btnItem, btnI) in cardItem.top.bottomList" :key="btnI">{{ btnItem.text }}</el-button>
              </div>
            </div>
            <!-- 数值 -->
            <div class="middle flex">
              <div style="width: 50%" v-for="(midelItem, mI) in cardItem.middle" :key="mI">
                <span :style="midelItem.tip.length == 0 ? { color: '#fff' } : ''">{{ midelItem.tip || "金额" }}</span>
                <el-statistic :value="midelItem.data" :precision="2" />
              </div>
            </div>
            <!-- 尾部 -->
            <div class="bottom flex" v-if="currentTypeIndex != 2">
              <el-link type="primary" :underline="false" @click="cardItem.bottom.handle"
                >{{ cardItem.bottom.label }}<el-icon :size="16" style="margin-left: 5px"><Postcard /></el-icon
              ></el-link>
            </div></li
        ></template>
      </ul>
      <ny-flod-tab
        class="newTabSty"
        :list="[
          { label: '充值账单', value: '1' },
          { label: '提现明细', value: '2' }
        ]"
        v-model="currentTypeIndex"
        value="value"
        label="label"
        @change="tabsTypeChange"
      ></ny-flod-tab>

      <template v-for="tableIndex in [1, 2]">
        <ny-table
          :key="tableIndex"
          v-if="currentTypeIndex == tableIndex"
          :state="state"
          :columns="tableColums[tableIndex - 1]"
          :show-summary="true"
          :summary-method="getSummaries"
          @pageSizeChange="state.pageSizeChangeHandle"
          @pageCurrentChange="state.pageCurrentChangeHandle"
          @selectionChange="state.dataListSelectionChangeHandle"
          @sortableChange="sortableChange"
        >
          <template #header-right>
            <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item>
                <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
              </el-form-item>

              <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
              <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-form>
          </template>

          <template #header>
            <el-button v-if="state.hasPermission('finance:billingInformation:export')" type="primary" @click="getExport">{{ $t("export") }}</el-button>
          </template>
          <!-- 订单编号 -->
          <template #orderCode="{ row }">
            <div class="linkSty">
              <el-link :underline="false" v-copy="row.orderCode"
                >{{ row.orderCode }}<el-icon class="copyIcon"><DocumentCopy /></el-icon>
              </el-link>
            </div>
          </template>
          <template #real_amount="{ row }">
            {{ row.realAmount }}
          </template>
          <!-- 类型 -->
          <template #type="{ row }">
            <el-text v-if="row.type == 8">保证金提现</el-text>
            <el-text v-if="row.type == 7">保证金充值</el-text>
            <el-text v-if="row.type == 6">违约金支付</el-text>
            <el-text v-if="row.type == 5">账户余额提现</el-text>
            <el-text v-if="row.type == 4">账户充值</el-text>
            <el-text v-if="row.type == 3">取消交易退款</el-text>
          </template>
          <!-- 提现状态 -->
          <template #statusName="{ row }">
            <el-tag v-if="row.status == 1" type="success">已支付</el-tag> 
            <el-tag v-else-if="row.status == 0" type="primary">待支付</el-tag>
            <el-tag v-else-if="row.status == 3" type="danger">支付失败</el-tag>
            <el-tag v-else-if="row.status == 4" type="danger">已驳回</el-tag>
          </template>
          <template #createDate="{ row }">
            <span>{{ formatTimeStamp(row.createDate) }}</span>
          </template>
          <template #updateDate="{ row }">
            <span>{{ formatTimeStamp(row.updateDate) }}</span>
          </template>
        </ny-table>
      </template>
    </el-card>
    <!-- 充值 -->
     <!-- 订单号查询充值 -->
    <billingInformationRecharge @refresh="refreshDataList" ref="billingInformationRechargeRef"></billingInformationRecharge>
    <!-- 充值金额 -->
    <billingInformationRechargePay @refresh="refreshDataList" ref="billingInformationRechargePayRef"></billingInformationRechargePay>
    <!-- 提现 -->
    <billingInformationWthdrawal @refreshDataList="refreshDataList" ref="billingInformationWthdrawalcRef"></billingInformationWthdrawal>
    <!-- 保证金明细 -->
    <shopDetail ref="shopDetailRef"></shopDetail>
    <!-- 账户管理 -->
    <accountMana ref="accountManaRef"></accountMana>
  </div>
</template>

<script lang="ts" setup>
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { Search, DocumentCopy, Postcard } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import { BigNumber } from "bignumber.js";
import { useAppStore } from "@/store";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import billingInformationRecharge from "./billingInformation-recharge.vue";
import billingInformationRechargePay from "./billingInfomation-rechargePay.vue";
import billingInformationWthdrawal from "./billingInformation-wthdrawal.vue";
import shopDetail from "./shopDetail.vue";
import accountMana from "./accountMana.vue";

const view = reactive({
  getDataListURL: "/finance/tenantfinance/page",
  getDataListIsPage: true,
  createdIsNeed: false,
  exportURL: "/finance/tenantfinance/export",
  dataForm: {
    tenantCode: "",
    tab: "3",
    type: "",
    start: "",
    end: ""
  }
});
const store = useAppStore();
const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.tab = "";
  state.dataForm.start = "";
  state.dataForm.type = "";
  state.dataForm.end = "";
  createDate.value = [];
  state.getDataList();
};

// 切换Tab
const currentTypeIndex = ref("1");
const tabsTypeChange = async () => {
  state.dataForm.tab = currentTypeIndex.value == 1 ? 3 : 4;
  // state.dataForm.type = currentTypeIndex.value == 1 ? 7 : 8;
  state.page = 1;
  state.dataForm.order = "";
  state.dataForm.orderField = "";
  state.getDataList();
};

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
  state.page = 1;
  state.getDataList();
};

// 充值
const billingInformationRechargeRef = ref();
const billingInformationRechargePayRef = ref();
// 提现
const billingInformationWthdrawalcRef = ref();
const withdrawalForm = reactive({
  count: 0, //剩余次数
  amountTotal: 0 //余额
});
// 保证金明细
const shopDetailRef = ref();
// 账户管理
const accountManaRef = ref();

// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop == "createDate" ? "create_date" : prop == "updateDate" ? "update_date" : prop;
  state.getDataList();
};
// 导出
const getExport = () => {
  baseService.get("/finance/tenantfinance/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, ["充值账单", "提现明细"][+currentTableIndex.value]);
    }
  });
};
// 顶部数据查询
const getStatistics = (type: Number | String, date?: any) => {
  baseService
    .get("/finance/tenantfinance/report", {
      type
    })
    .then((res) => {
      cardInfo[0].middle[0].data = res.data.amountTotal || 0;
      // 总应缴金额
      cardInfo[0].middle[1].data = res.data.realAmountTotal || 0;
      // 提现操作
      withdrawalForm.amountTotal = res.data.amountTotal || 0;
      withdrawalForm.count = res.data.count; //null无限制
    })
    .catch((res) => {
      cardInfo[0].middle[0].data = "0";
      cardInfo[0].middle[1].data = "0";
    });
};

const refreshDataList = () => {
  getStatistics(2);
  state.getDataList();
};
// 合计行计算函数
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    // 第一列 显示文字
    if (index === 0) {
      return (sums[index] = "合计:");
    } else if (column.property == "amount") {
      let total: any = 0;
      state.dataList.map((item: any) => {
        if (item.amount) total = BigNumber(total).plus(BigNumber(item.amount));
      });
      return (sums[index] = total + "");
    }
  });
  return sums;
};
// 表格配置项
const tableColums = reactive({
  0: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "orderCode",
      label: "交易订单号",
      minWidth: 378
    },
    {
      prop: "type",
      label: "类型",
      minWidth: 138
    },
    {
      prop: "real_amount",
      label: "金额(元)",
      minWidth: 184,
      sortable: "custom"
    },
    {
      prop: "amount",
      label: "当前余额(元)",
      minWidth: 181
    },
    {
      prop: "createDate",
      label: "充值时间",
      minWidth: 181,
      sortable: "custom"
    }
  ],
  1: [
    {
      type: "selection",
      width: 50
    },
    {
      prop: "orderCode",
      label: "提现单号",
      minWidth: 181
    },
    {
      prop: "amount",
      label: "提现金额(元)",
      minWidth: 184,
      sortable: "custom"
    },
    {
      prop: "createDate",
      label: "申请时间",
      minWidth: 184,
      sortable: "custom"
    },
    {
      prop: "statusName",
      label: "提现状态",
      minWidth: 181
    },
    {
      prop: "updateDate",
      label: "到账时间",
      minWidth: 181,
      sortable: "custom"
    },
    {
      prop: "accountName",
      label: "收款账户",
      minWidth: 181
    },
    {
      prop: "remark",
      label: "备注",
      minWidth: 181
    }
  ]
});
const cardInfo = reactive([
  {
    top: {
      img: "",
      label: "商城保证金",
      hasButton: true,
      bottomList: [
        {
          type: "default",
          text: "提现",
          handle: async () => {
            await nextTick();
            billingInformationWthdrawalcRef.value.init(withdrawalForm);
          }
        },
        {
          type: "primary",
          text: "充值",
          handle: async (id: number | string) => {
            await nextTick();
            // billingInformationRechargeRef.value.init(id, "1");
            billingInformationRechargePayRef.value.init(id, "1");
          }
        }
      ],
      hasSearch: false,
      type: "" //时间检索类型
    },
    middle: [
      {
        tip: "保证金余额(元)",
        data: "0"
      },
      {
        tip: "总应缴金额(元)",
        data: "0"
      }
    ],
    bottom: {
      hasBottom: true,
      label: "账户管理",
      img: "",
      tip: "",
      data: "",
      color: "",
      handle: async (id: number | string) => {
        await nextTick();
        accountManaRef.value.init(id, "1");
      }
    },
    isShow: true
  }
]);
onMounted(() => {
  state.dataForm.tenantCode = store.state.user.tenantCode;
  refreshDataList();
});
</script>

<style lang="scss" scoped>
.el-tag {
  border: 1px solid;
}
.clearSty {
  padding: 0;
  list-style: none;
}
.flex {
  display: flex;
  align-items: cenetr;
}

.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}

.headCardUl {
  display: flex;
  align-items: cenetr;
  flex-wrap: nowrap;

  .headCardLi {
    min-width: 20%;
    margin-right: 20px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;

    &:last-child {
      margin-right: 0;
    }

    .head {
      justify-content: space-between;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
      :deep(.el-input) {
        height: 24px;
        .el-input__wrapper {
          height: 24px;
          line-height: 24px;
          .el-input__inner {
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 20px;
          }
        }
      }
    }

    .middle {
      justify-content: space-between;
      span {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      :deep(.el-statistic__content) {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bottom {
      margin-top: 16px;
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      :deep(.el-link__inner) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 14px;
        height: 11px;
        margin-left: 5px;
      }
    }
  }
}
.linkSty {
  display: flex;
  align-items: center;
  text-align: center;
  width: fit-content;
  margin: auto;
  .copyIcon {
    display: none;
    width: 1px;
  }
  &:hover {
    .copyIcon {
      display: inline-block;
      margin-left: 8px;
    }
  }
}
:deep(.el-table__footer) {
  font-weight: bold;
  .el-table-column--selection {
    font-weight: normal;
  }
}
</style>
