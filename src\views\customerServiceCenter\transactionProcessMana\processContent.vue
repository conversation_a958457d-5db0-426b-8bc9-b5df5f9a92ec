<template>
  <div>
    <ny-table cellHeight="ch-56" :showColSetting="false" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <!-- 操作 -->
      <template #operation="{ row }">
        <el-link v-if="state.hasPermission('sys:ipBlackList:update')" :underline="false" style="margin-right: 12px" @click="addOrUpdateHandle(row.id)" type="primary">编辑</el-link>
      </template>
    </ny-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./add-or-update.vue";

const view = reactive({
  getDataListURL: "/im/imflow/page",
  getDataListIsPage: true,
  dataForm: {
    type: 0
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const tableColums = reactive([
  {
    prop: "name",
    label: "流程名称",
    width: 320
  },
  {
    prop: "content",
    label: "流程内容",
    showOverflowTooltip: false
  },
  {
    prop: "operation",
    label: "操作",
    width: 240
  }
]);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: any) => {
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
