<template>
  <el-dialog class="chatDialog" :footer="null" v-model="visible" width="600" title="聊天记录" :close-on-click-modal="false" :close-on-press-escape="false">
    <div style="padding: 0 16px; display: flex; align-items: center; justify-content: space-between">
      <ny-button-group
        label="label"
        value="value"
        :list="stateList"
        v-model="params.objectName"
        @change="
          () => {
            search();
          }
        "
      ></ny-button-group>

      <el-date-picker style="width: 180px" v-model="params.selectTime" type="date" placeholder="请选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="search" />
    </div>
    <div v-loading="dataLoading">
      <el-scrollbar style="padding: 12px 16px; height: 70vh" ref="scrollbarRef" @scroll="handleReacBottom">
        <div class="chatSty" v-for="(item, index) in dataList" :key="index">
          <el-avatar shape="square" :size="36" :src="item.fromUserHeadUrl || settingStore.info.userDefaultAvatar" />
          <div class="middle">
            <span class="title">{{ item.fromUserName }}</span>

            <!-- 文本 -->
            <div class="msg-text" v-if="item.objectName == 'RC:TxtMsg'">
              <span>{{ item.content.content }}</span>
            </div>

            <!-- 图片 -->
            <div v-else-if="item.objectName == 'RC:ImgMsg'" class="image-msg">
              <el-image :src="item.content.imageUri" :preview-src-list="[item.content.imageUri]" :preview-teleported="true"></el-image>
            </div>

            <!-- 视频 -->
            <div v-else-if="item.objectName == 'RC:FileMsg'" class="image-msg">
              <video :src="item.content.fileUrl" :id="item.messageUId"></video>
              <div class="video-mask flx-center" @click="playVideo(item.content.fileUrl)">
                <el-icon><VideoPlay /></el-icon>
              </div>
            </div>

            <!-- 订单流程 -->
            <div class="msg-text order-flow" v-if="item.objectName == 'RC:IWNormalMsg' && item.content.msgType == 'CU:orderFlow'">
              <div class="flow-title">{{ item.content.msgFields.name }}</div>
              <div>{{ item.content.msgFields.content }}</div>
            </div>

            <!-- 商品 -->
            <div class="shop" v-else-if="item.objectName == 'RC:IWNormalMsg' && item.content.msgType == 'CU:shop'" @click="goToShop(item.content.msgFields)">
              <div class="title">
                <span class="text">商品编号：{{ item.content.msgFields.code }}</span>
                <el-text type="primary" class="pointer" @click.stop="" v-copy="item.content.msgFields.code">复制</el-text>
              </div>
              <div class="shop-info flx">
                <el-image class="shop-img" :src="item.content.msgFields.log" fit="cover"></el-image>
                <div class="shop-info-right">
                  <div class="mle shop-title">{{ item.content.msgFields.title }}</div>
                  <div class="price-wrap flx-justify-between">
                    <div class="price">￥{{ item.content.msgFields.price }}</div>
                    <el-tag type="primary" size="small">{{ item.content.msgFields.compensation == 0 ? "不可包赔" : item.content.compensation == 1 ? "可买包赔" : "永久包赔" }}</el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 图片 -->
            <div class="shop" v-else-if="item.messageType == 'RC:IWNormalMsg' && item.content.msgType == 'CU:image'">
              <el-image class="image" :src="item.content.msgFields.url" :preview-src-list="item.content.msgFields.url" />
            </div>

            <!-- 订单 -->
            <div class="shop order" v-else-if="item.objectName == 'RC:IWNormalMsg' && item.content.msgType == 'CU:order'">
              <div class="title flx">
                <span class="text sle flx-1">订单编号：{{ item.content.msgFields.sn }}</span>
                <el-text type="primary" class="pointer" v-copy="item.content.msgFields.sn">复制</el-text>
              </div>
              <div class="shop-info flx">
                <el-image v-if="item.content.msgFields.shopVo && item.content.msgFields.shopVo.log" class="shop-img" :src="item.content.msgFields.shopVo.log" fit="cover"></el-image>
                <div class="shop-info-right">
                  <div class="mle shop-title" v-if="item.content.msgFields.shopVo && item.content.msgFields.shopVo.title">{{ item.content.msgFields.shopVo.title }}</div>
                  <div class="price-wrap flx-justify-between">
                    <div class="price">￥{{ item.content.msgFields.dealAmount }}</div>
                    <el-tag v-if="item.content.msgFields.shopVo" type="primary" size="small">{{ item.content.msgFields.shopVo.compensation == 0 ? "不可包赔" : item.content.msgFields.shopVo.compensation == 1 ? "可买包赔" : "永久包赔" }}</el-tag>
                  </div>
                  <div class="order-bottom">
                    <div class="order-text">
                      <div class="text">创建时间：</div>
                      <div class="text-success">{{ item.content.msgFields.createDate }}</div>
                    </div>
                    <div class="order-text">
                      <div class="text">游戏名称：</div>
                      <div class="text-success">{{ item.content.msgFields.gameName }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 收集包赔消息 -->
            <div class="shop prohibited order" v-else-if="item.objectName == 'RC:IWNormalMsg' && item.content.msgType == 'CU:collect'">
              <div class="title flx">{{ imStore.currentConversation.conversationType == 3 ? "请卖家填写" : "" }}收集包赔信息</div>
              <div class="desc mle">{{ item.content.msgFields.content }}</div>
            </div>

            <!-- 签署合同消息 -->
            <div class="shop prohibited order" v-else-if="item.objectName == 'RC:IWNormalMsg' && item.content.msgType == 'CU:contract'">
              <div class="title flx">{{ imStore.currentConversation.conversationType == 3 ? "请卖家填写" : "" }}签署合同</div>
              <div class="desc mle">{{ item.content.msgFields.content }}</div>
            </div>
          </div>
          <span class="timeSty">{{ item.time }}</span>
        </div>
        <ny-no-data type="3" v-if="!dataList || !dataList.length" />
      </el-scrollbar>

      <!-- 视频预览 -->
      <el-dialog width="50%" v-model="showVideoPreview" @close="closeVideoPreview">
        <video style="width: 100%" :src="videoSrc" controls ref="videoRef"></video>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, nextTick } from "vue";
import { VideoPlay } from "@element-plus/icons-vue";
import { useSettingStore } from "@/store/setting";
import { useImStore } from "@/store/im";
import baseService from "@/service/baseService";

const visible = ref(false);
const dataFormRef = ref();
const settingStore = useSettingStore();
const imStore = useImStore();

const stateList = ref([
  { value: "", label: "全部" },
  { value: "RC:TxtMsg", label: "文本" },
  { value: "RC:ImgMsg", label: "图片" }
]);

const init = (id?: any) => {
  visible.value = true;
  params.imChannel = id;
  params.page = 1;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  if (id) {
    getInfo(id);
  }
};

const params = reactive({
  page: 1,
  size: 50,
  imChannel: null,
  objectName: "",
  selectTime: ""
});
const dataTotal = ref(0);

const filterDataList = ["RC:RRReqMsg", "RC:RRRspMsg", "RC:SRSMsg", "RC:ReadNtf"];
// 获取信息
const dataList = ref(<any>[]);
const hasMore = ref(true);
const dataLoading = ref(false);
const getInfo = () => {
  if (dataLoading.value) return;
  dataLoading.value = true;
  let formParams = JSON.parse(JSON.stringify(params));
  if (!formParams.objectName) delete formParams.objectName;
  if (!formParams.selectTime) delete formParams.selectTime;
  baseService
    .post("/im/login/historyAll", formParams)
    .then((res: any) => {
      let { list, total } = res.data;
      list = list.filter((item: any) => {
        item.content = JSON.parse(item.content);
        return !filterDataList.includes(item.objectName);
      });
      dataList.value = [...dataList.value, ...list];
      dataTotal.value = total;
      console.log(res.data.list.length, total);
      if (res.data.list.length < formParams.size) hasMore.value = false;
    })
    .finally(() => {
      dataLoading.value = false;
    });
};

// 滚动到底部
const scrollbarRef = ref();
const handleReacBottom = (e: any) => {
  // 滚动条位置
  const scrollPosition = scrollbarRef.value.wrapRef.scrollHeight - scrollbarRef.value.wrapRef.clientHeight;
  if (e.scrollTop == scrollPosition && hasMore.value && dataList.value.length) {
    params.page++;
    getInfo();
  }
};

const search = () => {
  dataList.value = [];
  params.page = 1;
  getInfo();
};

// 播放视频
const videoSrc = ref("");
const showVideoPreview = ref(false);
const videoRef = ref();
const playVideo = async (src: string) => {
  videoSrc.value = src;
  showVideoPreview.value = true;
  await nextTick();
  videoRef.value.play();
};

const closeVideoPreview = () => {
  videoSrc.value = "";
  showVideoPreview.value = false;
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.chatSty {
  display: flex;
  margin-top: 12px;
  .middle {
    flex: 1;
    display: flex;
    margin: 12px;
    margin-top: 0;
    flex-direction: column;
    font-family: OPPOSans, OPPOSans;
    font-weight: 400;
    font-size: 16px;
    color: #303133;
    line-height: 24px;

    .title {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 14px;
      margin-bottom: 12px;
    }

    .image-msg {
      width: auto;
      max-width: 300px;
      padding: 8px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      position: relative;

      .el-image,
      video {
        width: 100%;
        border-radius: 8px;
      }

      .video-mask {
        color: #fff;
        font-size: 50px;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background: rgba($color: #000000, $alpha: 0);
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.2);
        }
      }
    }

    .msg-text {
      line-height: 24px;
      font-size: 14px;
      overflow: hidden;

      .confirmed-btn {
        text-align: right;

        .el-button {
          margin-top: 8px;
        }
      }

      span {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }

    .shop {
      width: 306px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      color: #000;
      font-size: 14px;
      line-height: 1;

      &.prohibited {
        pointer-events: none;
        user-select: none; /* 禁止选择文本 */
        -webkit-user-select: none; /* Safari 和 Chrome 的兼容性 */
        -moz-user-select: none; /* Firefox 的兼容性 */
        -ms-user-select: none; /* IE 和 Edge 的兼容性 */
      }

      &.lifted {
        pointer-events: all;
        user-select: auto;
      }

      .title {
        line-height: 24px;
        margin: 0;

        .text {
          padding-right: 8px;
        }
      }

      .desc {
        color: #999;
        font-size: 12px;
        padding-top: 8px;
        word-break: break-all;
      }

      .shop-info {
        padding-top: 16px;

        .shop-img {
          width: 80px;
          height: 80px;
          border-radius: 4px;
          margin-right: 10px;
        }

        .shop-info-right {
          flex: 1;

          .shop-title {
            line-height: 24px;
            height: 48px;
          }

          .price-wrap {
            padding-top: 10px;
            height: 20px;
            .price {
              color: #f44a29;
            }
          }

          .order-bottom {
            .order-text {
              padding-top: 10px;
            }

            .text {
              font-size: 12px;
              color: #909399;
              padding-bottom: 4px;
            }
          }
        }
      }
    }
  }
  .timeSty {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 14px;
  }
}
</style>
<style lang="scss">
.chatDialog {
  padding: 0;
  .el-dialog__header {
    padding: 16px;
  }
}
</style>
