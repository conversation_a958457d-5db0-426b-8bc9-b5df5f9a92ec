.ny-im-wrap{
    height: 100%;
    min-width: 1408px;
    overflow-x: auto;
    font-size: 14px;
    div,p,span{
        font-size: 14px;
    }

    .im-container{
        background: #000;
        display: flex;
        width: auto;
        min-width: 1408px;
        margin: 16px 256px 0;
        background: #F4F4F4;
        height: calc(100% - 104px);
        box-shadow: 0px 6px 18px 0px rgba(38,38,38,0.14);
        position: relative;
    }
}
.no-conversation{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    img{
        width: 200px;
    }
}
@media screen and (max-width: 1920px) {
    .ny-im-wrap .im-container{
        width: 1408px;
        margin: 16px auto;
    }
}