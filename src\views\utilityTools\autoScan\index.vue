<template>
  <el-card shadow="never" class="rr-view-ctx-card">
    <ny-flod-tab
      class="newTabSty"
      :list="[
        { value: 0, label: '扫描任务' },
        { value: 1, label: '执行日志' }
      ]"
      v-model="tabActiveId"
      value="value"
      label="label"
    >
    </ny-flod-tab>
    <div class="mainBox" v-if="tabActiveId == 0">
      <div class="leftPart">
        <div class="title">选择合作商</div>
        <el-select v-model="state.dataForm.partnerCode" @change="handleselectPartner" clearable style="width: 186px; margin-bottom: 10px">
          <el-option v-for="item in stateList.curAllPartnerList" :key="item.code" :label="item.value" :value="item.code" />
        </el-select>
        <div class="title">选择游戏</div>
        <div class="scrollWrap lower">
          <ul class="menuUl">
            <li :class="'menuLi ' + (state.dataForm.gameId == item.gameId ? 'active' : '')" v-for="(item, itemIndex) in stateList.allSelectArr" :key="itemIndex" @click="handleselectGame(item.gameId)" :index="itemIndex">
              <el-tooltip effect="dark" :content="item.gameName" placement="top-start">
                <span>{{ item.gameName }}</span>
              </el-tooltip>
            </li>
          </ul>
        </div>
      </div>
      <div class="rightPart TableXScrollSty" v-if="currentType == 1">
        <ny-table :showColSetting="false" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @sortableChange="sortableChange">
          <template #header>
            <div class="flx-justify-between">
              <ny-button-group
                :list="[
                  { dictLabel: '扫描结果', dictValue: 1 },
                  { dictLabel: '规则配置', dictValue: 2 }
                ]"
                v-model="currentType"
              ></ny-button-group>
            </div>
          </template>
          <template #header-right>
            <el-input :prefix-icon="Search" style="width: 280px !important" v-model="state.dataForm.keywords" placeholder="请输入商品标题/商品编码" clearable></el-input>
            <el-date-picker
              v-model="timeInterval"
              @change="
                () => {
                  state.dataForm.start = timeInterval ? timeInterval[0] + ' 00:00:00' : '';
                  state.dataForm.end = timeInterval ? timeInterval[1] + ' 23:59:59' : '';
                }
              "
              type="daterange"
              range-separator="到"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              unlink-panels
              style="width: 220px; margin-left: 12px"
            />
            <el-select v-model="state.dataForm.configId" placeholder="请选择规则" clearable style="width: 186px">
              <el-option v-for="item in stateList.curAllRuleList" :key="item.id" :label="item.configName" :value="item.id" />
            </el-select>
            <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
            <el-button @click="getResetting()">重置</el-button>
          </template>
          <!-- 支付链接 -->
          <template #payUrl="{ row }">
            <a :href="row.payUrl" target="_blank">去支付</a>
          </template>
          <!-- <template #shopTitle="{ row }">
            <a :href="row.payUrl" target="_blank" style="color: #333">
              <div v-html="row.shopTitle"></div>
            </a>
          </template> -->
          <template #shopTitle="{ row }">
            <div class="shoping">
              <el-image style="height: 68px; width: 120px" :src="row.imgUrl" :preview-src-list="[row.imgUrl]" preview-teleported fit="cover" />
              <div class="info">
                <div class="title mle" v-html="row.shopTitle"></div>
                <div class="sle" style="width: 185px; text-align: left">
                  {{ `${row.gameName} / ${row.server}` }}
                </div>
              </div>
            </div>
          </template>
          <!-- 零售价(元) -->
          <template #price="{ row }">
            <span style="color: #f56c6c">{{ formatCurrency(row.price) }}</span>
          </template>
          <template #info="{ row }">
            <el-tooltip placement="top" title="" :width="200" trigger="hover" :content="row.info">
              <el-button link type="primary">查看</el-button>
            </el-tooltip>
          </template>
          <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span style="font-weight: bold; color: #1d2129">零售价</span>
            <span>手机号数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
        </ny-table>
      </div>
      <div class="rightPart" v-if="currentType == 2">
        <div class="flx-justify-between" style="margin-bottom: 12px">
          <ny-button-group
            :list="[
              { dictLabel: '扫描结果', dictValue: 1 },
              { dictLabel: '规则配置', dictValue: 2 }
            ]"
            v-model="currentType"
          ></ny-button-group>
        </div>
        <rulePage :propsData="propsData" />
      </div>
    </div>
    <tasklog v-if="tabActiveId == 1" :partnerList="stateList.curAllPartnerList" :gameList="stateList.allSelectArr" />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs, reactive, watch, provide, computed } from "vue";
import useView from "@/hooks/useView";
import { ElMessage, ElNotification } from "element-plus";
import { useSettingStore } from "@/store/setting";
import { formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
import { Search } from "@element-plus/icons-vue";
import tasklog from "./tasklog.vue";
import rulePage from "./rulePage.vue";
const timeInterval = ref(); // 时间区间
const currentType = ref(1);
const tabActiveId = ref(0);
// 表格配置项
const columns = reactive([
  {
    prop: "shopCode",
    label: "商品编码",
    minWidth: 120
  },
  {
    prop: "shopTitle",
    label: "商品信息",
    minWidth: 340
  },
  {
    prop: "price",
    label: "零售价(元)",
    minWidth: 120,
    sortable: true
  },
  {
    prop: "releaseTime",
    label: "创建时间",
    width: 180
  },
  {
    prop: "payUrl",
    label: "操作",
    width: 180
  }
]);

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/scan/autoScanResult/page",
  getDataListIsPage: true,
  deleteURL: "",
  deleteIsBatch: true,
  dataForm: {
    order: "",
    orderField: "",
    partnerCode: undefined,
    gameId: undefined,
    keywords: undefined,
    configId: undefined,
    start: "",
    end: ""
  }
});
const propsData = ref({
  gameName: "",
  gameId: "",
  partnerName: "",
  partnerCode: ""
});
const state = reactive({ ...useView(view), ...toRefs(view) });
const stateList = reactive({
  curAllPartnerList: <any>[],
  propGameList: <any>[], // 用来给子组件传参
  allSelectArr: <any>[],
  curAllRuleList: <any>[]
});
// 排序
const sortableChange = ({ order, prop }: any) => {
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleselectGame = (id: any) => {
  state.dataForm.gameId = id;
  let obj = stateList.allSelectArr.find((ele: any) => ele.id === id);
  propsData.value.gameName = id == null ? undefined : obj.gameName;
  propsData.value.gameId = id || "";
  state.getDataList();
  getRuleList();
};
// 切换平台获取游戏+更新table数据
const handleselectPartner = (id: any) => {
  state.dataForm.partnerCode = id || undefined;
  let obj = stateList.curAllPartnerList.find((ele: any) => ele.code === id);
  propsData.value.partnerName = obj.value || "";
  propsData.value.partnerCode = id || "";
  state.getDataList();
  getRuleList();
};
const getGameList = async () => {
  let res_ = await baseService.get("/game/sysgame/allGames");
  if (res_.code == 0) {
    let allSelectArr = (res_.data || []).map((ele: any) => {
      return {
        ...ele,
        gameId: ele.id,
        gameName: ele.title
      };
    });
    stateList.propGameList = allSelectArr;
    stateList.allSelectArr = [{ gameId: null, gameName: "全部游戏" }, ...allSelectArr];
    if (res_.data.length > -1) {
      // 获取列表
      state.getDataList();
      // 获取规则下拉
      getRuleList();
    }
  }
};
provide(
  "propGamrList",
  computed(() => stateList.propGameList)
);
// 获取合作商数据
const getSelectInfo = () => {
  baseService.get("/scan/autoScanConfig/getPartners").then((res) => {
    if (res.code == 0) {
      stateList.curAllPartnerList = res.data || [];
      if (stateList.curAllPartnerList.length > 0) {
        state.dataForm.partnerCode = stateList.curAllPartnerList[0].code;
        propsData.value.partnerName = stateList.curAllPartnerList[0].value;
        propsData.value.partnerCode = stateList.curAllPartnerList[0].code;
        getGameList();
      }
    }
  });
};
// 获取规则
const getRuleList = () => {
  baseService
    .get("/scan/autoScanConfig/page", {
      gameId: state.dataForm.gameId,
      partnerCode: state.dataForm.partnerCode,
      limit: 100
    })
    .then((res) => {
      if (res.code == 0) {
        stateList.curAllRuleList = res.data.list || [];
      }
    });
};
const getResetting = () => {
  state.dataForm.start = "";
  state.dataForm.end = "";
  timeInterval.value = undefined;
  state.dataForm.keywords = undefined;
  state.dataForm.configId = undefined;
  state.getDataList();
};
getSelectInfo();

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.price) total += Number(item?.price) || 0;
  });
  return total.toFixed(2);
};

</script>
<style lang="scss" row>
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;

  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}

.TableXScrollSty .NYpagination{
  right: 44px;
}

.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rr-view-ctx-card {
  min-height: calc(100vh - 154px);

  :deep(.el-card__body) {
    padding: 0;
  }

  .cards {
    display: flex;
    margin-bottom: 16px;

    .el-card {
      margin-left: 12px;

      :deep(.el-card__header) {
        padding: 7px 12px;
        background: #f5f7fa;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 22px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      :deep(.el-card__body) {
        padding: 12px;
        padding-bottom: 0;
        max-height: 100px;
        overflow-y: scroll;
      }

      :deep(.el-tag__content) {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .mainBox {
    display: flex;

    .leftPart {
      width: 186px;
      margin-right: 12px;

      .title {
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 22px;
        margin-bottom: 2px;
      }

      display: flex;
      flex-direction: column;

      .scrollWrap {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        height: calc(100vh - 260px);
        overflow: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .menuUl {
          .menuLi {
            cursor: pointer;
            padding: 20px;
            word-break: keep-all;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Inter, Inter;
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            line-height: 14px;
            &.active {
              background-color: var(--color-primary-light);
              color: var(--color-primary);
            }
          }
        }

        &.lower {
          height: calc(100vh - 330px);
        }
      }
    }

    .rightPart {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
