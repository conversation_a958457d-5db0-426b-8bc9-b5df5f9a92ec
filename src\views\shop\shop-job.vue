<template>
  <el-dialog v-model="visible" title="自动降价" :close-on-click-modal="false" :close-on-press-escape="false" width="65%">
    <div style="margin-bottom: 16px">
      <el-button type="info" icon="Refresh" @click="state.getDataList"></el-button>
      <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
    </div>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border style="width: 100%">
      <!-- <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column> -->
      <el-table-column prop="gameName" label="游戏名称" width="180" align="center" />
      <el-table-column prop="serverName" label="系统区服" width="180" align="center" />
      <el-table-column prop="period" label="降价周期" align="center" />
      <el-table-column prop="percent" label="降价比例" align="center" />
      <el-table-column prop="createDate" label="生效日期" width="180" align="center">
        <template #default="scope">
          {{ formatTimeStamp(scope.row.createDate) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="200">
        <template v-slot="scope">
          <el-button type="primary" text bg @click="addOrUpdateHandle(scope.row.id, scope.row.server)">{{ $t("update") }}</el-button>
          <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
  </el-dialog>

  <!-- 弹窗, 新增 / 修改 -->
  <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import { ref, reactive, toRefs, nextTick } from "vue";
import AddOrUpdate from "./shop-job-add-or-update.vue";

const visible = ref(false); // 对话框显隐åå

const view = reactive({
  getDataListURL: "/shop/shopjob/page",
  getDataListIsPage: true,
  exportURL: "/shop/shopjob/export",
  deleteURL: "/shop/shopjob",
  deleteIsBatch: true,
  dataForm: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number, server?: string) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id, server);
  });
};

// 弹窗初始化
const init = (shopId: any) => {
  visible.value = true;
  state.getDataList;
};
defineExpose({
  init
});
</script>

<style lang="less" scoped></style>
