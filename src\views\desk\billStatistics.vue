<template>
  <div class="billStatistics">
    <div class="above">
      <div class="left">
        <span class="title">账单统计</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
      </div>
    </div>
    <el-row :gutter="12" style="margin-top: 12px">
      <el-col :span="12">
        <div class="stock_card">
          <div class="stock_top">
            <div class="stock_top_item">
              <el-icon size="15px" color="#F56C6C"><Histogram /></el-icon>
              <span>月收入统计(元)</span>
            </div>
            <div class="stock_top_item">
              <el-date-picker v-model="cardInfo[0].date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="getStatistics(0, cardInfo[0].date)" />
            </div>
          </div>
          <div class="stock_button_num">
            <div class="stock_button_num_item">
              <div class="number">{{ formatCurrency(cardInfo[0].data[0]) }}</div>
              <div class="all">
                <span class="label">本年度总收入金额：</span>
                <span class="value" style="color: #f56c6c">{{ formatCurrency(cardInfo[0].data[1]) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="stock_card">
          <div class="stock_top">
            <div class="stock_top_item">
              <el-icon size="15px" color="#67C23A"><Histogram /></el-icon>
              <span>月支出统计(元)</span>
            </div>
            <div class="stock_top_item">
              <el-date-picker v-model="cardInfo[1].date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="getStatistics(1, cardInfo[1].date)" />
            </div>
          </div>
          <div class="stock_button_num">
            <div class="stock_button_num_item">
              <div class="number">{{ formatCurrency(cardInfo[1].data[0]) }}</div>
              <div class="all">
                <span class="label">本年度总支出金额：</span>
                <span class="value" style="color: #67c23a">{{ formatCurrency(cardInfo[1].data[1]) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div style="display: inline-block; margin-top: 8px; margin-bottom: 6px">
      <ny-button-group
        :list="[
          { dictLabel: '商城在线支付', dictValue: '1' },
          { dictLabel: '打款/退款流水', dictValue: '2' },
          { dictLabel: '分销商充值', dictValue: '3' },
          { dictLabel: '提现', dictValue: '4' }
        ]"
        v-model="state.params.type"
        @change="getData"
      ></ny-button-group>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <template v-if="state.params.type == '1'">
        <el-table-column prop="sn" show-overflow-tooltip label="订单编号" align="center" />
        <el-table-column prop="gameName" show-overflow-tooltip label="游戏名称" align="center" />
        <el-table-column prop="totalAmount" show-overflow-tooltip label="付款金额(元)" align="center" />
        <el-table-column prop="dealTime" show-overflow-tooltip label="支付时间" align="center" />/
      </template>
      <template v-if="state.params.type == '2'">
        <el-table-column prop="sn" show-overflow-tooltip label="账单编码" align="center" />
        <el-table-column prop="orderType" show-overflow-tooltip label="款项类型" align="center">
          <template v-slot="{ row }">
            {{ ["回收订单", "销售订单", "售后订单", "其他"][+row.orderType] }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" show-overflow-tooltip label="打/退款金额(元)" align="center" />
        <el-table-column prop="dealTime" show-overflow-tooltip label="打/退款时间" align="center">
          <template v-slot="{ row }">{{ formatTimeStamp(+row.dealTime) }}</template>
        </el-table-column>
      </template>
      <template v-if="state.params.type == '3'">
        <el-table-column prop="orderCode" show-overflow-tooltip label="订单编号" align="center" />
        <el-table-column prop="amount" show-overflow-tooltip label="充值金额(元)" align="center" />
        <el-table-column prop="accountName" show-overflow-tooltip label="支付商家" align="center" />
        <el-table-column prop="createDate" show-overflow-tooltip label="充值时间" align="center">
          <template v-slot="{ row }">{{ formatTimeStamp(+row.createDate) }}</template>
        </el-table-column>
      </template>
      <template v-if="state.params.type == '4'">
        <el-table-column prop="orderCode" show-overflow-tooltip label="提现单号" align="center" />
        <el-table-column prop="amount" show-overflow-tooltip label="提现金额(元)" align="center" />
        <el-table-column prop="accountName" show-overflow-tooltip label="提现商家" align="center" />
        <el-table-column prop="createDate" show-overflow-tooltip label="提现时间" align="center">
          <template v-slot="{ row }">{{ formatTimeStamp(+row.createDate) }}</template>
        </el-table-column>
      </template>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <div class="flx-center">
      <el-pagination v-model:current-page="state.params.page" @change="getData" layout="prev, pager, next" :total="state.totalCount" hide-on-single-page />
    </div>
  </div>
</template>

<script lang="ts" setup>
import commonData from "./index.ts";
import { ref, reactive, onMounted } from "vue";
import { formatDate, formatCurrency, formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
const publicTime = ref(""); // 时间筛选变量
const status = ref(1);
const cardInfo = ref([
  {
    data: <any>[],
    date: ""
  },
  {
    data: <any>[],
    date: ""
  }
]);
const state = reactive({
  totalCount: 0,
  params: {
    type: "1",
    page: 1,
    limit: 10
  }
});
const defaultDate = ref();
const tableData = ref([]);
const getData = () => {
  tableData.value = [];
  if (state.params.type == "1") {
    baseService
      .get("/flowable/capitalrecord/page", {
        ...state.params,
        billType: 0
      })
      .then((res) => {
        tableData.value = res.data.list || [];
        state.totalCount = res.data?.total || 0;
      });
    return;
  }

  baseService
    .get("/console/capitalRecord", {
      ...state.params
    })
    .then((res) => {
      tableData.value = res.data.list || [];
      state.totalCount = res.data?.total || 0;
    });
};
const getStatistics = (index: any, date: any) => {
  // NOTE: 空值的时候置默认值
  if (!date) {
    cardInfo.value[index].date = defaultDate.value;
    date = defaultDate.value;
  }

  let url = ["/flowable/capitalrecord/getMonthlyIncome", "/flowable/capitalrecord/getMonthlyExpense"];
  let param = { month: date };
  baseService.get(url[index], param).then((res) => {
    if (res) {
      if (index == 0) {
        cardInfo.value[0].data = [res.data?.monthIncome || 0, res.data?.yearIncome || 0];
      } else if (index == 1) {
        cardInfo.value[1].data = [res.data?.monthOutcome || 0, res.data?.yearOutcome || 0];
      }
    }
  });
};
onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  let month = now.getMonth() + 1; // 月份从0开始，需要加1
  let day = now.getDate();
  month = +month > 9 ? month : "0" + month;
  defaultDate.value = year + "-" + month;
  console.log("defaultDate", defaultDate.value);
  for (let index = 0; index < 2; index++) {
    cardInfo.value[index].date = defaultDate.value;
    getStatistics(+index, cardInfo.value[index].date);
  }
  getData();
});
</script>

<style lang="less" scoped>
.billStatistics {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.stock_card {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ebeef5;
  .stock_top {
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .stock_top_item {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
      span {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .but {
        display: flex;
        span {
          font-weight: 400;
          font-size: 14px;
          color: #909399;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-left: 12px;
        }
        .active {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .stock_button_num {
    padding: 4px 16px 12px 16px;
    display: flex;
    align-items: center;
    .stock_button_num_item {
      flex: 1;
      .number {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .all {
        margin-top: 8px;
      }
    }
  }
}
</style>
