<template>
  <div class="distributor">
    <div class="above">
      <div class="left">
        <span class="title">分销商统计(帮我卖)</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
      </div>
    </div>
    <div class="stock_card" style="margin-top: 12px; display: flex">
      <div style="flex: 1">
        <div class="stock_top">
          <div class="stock_top_item">
            <img src="../../assets/images/shop_stat8.png" />
            <span>总销售额(元)</span>
          </div>
        </div>
        <div class="stock_button">
          <div class="stock_button_count">
            <div class="number">{{ formatCurrency(apiData.totalAmount || 0) }}</div>
          </div>
        </div>
      </div>
      <div style="flex: 1">
        <div class="stock_top">
          <div class="stock_top_item">
            <img src="../../assets/images/shop_stat9.png" />
            <span>提号数量(个)</span>
          </div>
        </div>
        <div class="stock_button">
          <div class="stock_button_count">
            <div class="number">{{ formatCurrency(apiData.totalCount || 0) }}</div>
          </div>
        </div>
      </div>
      <div class="stock_top_item">
        <div class="but">
          <span
            @click="
              saleParams = item.value;
              handleData();
            "
            style="cursor: pointer"
            :class="{ active: saleParams == item.value }"
            :key="item.label"
            v-for="item in [
              { label: '本周', value: 'week' },
              { label: '本月', value: 'month' }
            ]"
            >{{ item.label }}</span
          >
        </div>
      </div>
    </div>
    <div v-show="apiData.xdata && apiData.xdata.length > 0" class="distributor_chart">
      <div style="width: 100%; height: 100%" ref="distributorChartRef"></div>
    </div>
    <div v-if="apiData.xdata && apiData.xdata.length > 0" class="ranking">
      <div class="ranking_title">分销商排名</div>
      <div class="ranking_list">
        <div class="ranking_item" v-for="(item, index) in apiData.rank" :key="index">
          <img v-if="index == 0" src="@/assets/images/ranking1.png" class="icon" />
          <img v-else-if="index == 1" src="@/assets/images/ranking2.png" class="icon" />
          <img v-else-if="index == 2" src="@/assets/images/ranking3.png" class="icon" />
          <span v-else>{{ index + 1 }}</span>
          <span class="name">{{ item }}</span>
        </div>
      </div>
    </div>
    <ny-no-data type="3" v-if="!apiData.xdata || apiData.xdata.length < 1" />
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from "vue";
import commonData from "./index.ts";
import { formatDate, formatCurrency } from "@/utils/method";
import * as echarts from "echarts";

const distributorChartRef = ref();
const charts = ref(<any>[]);
const apiData = ref({
  totalAmount: 0,
  totalCount: 0,
  xdata: [],
  ydata: [],
  rank: []
});
const saleParams = ref("week");
const stockChart = () => {
  const userGrowthChart = echarts.init(distributorChartRef.value);
  const option = {
    legend: {
      top: "bottom", // 标注位置
      x: "center",
      textStyle: {
        // 标注样式
        color: "#000",
        fontSize: 12
      },
      icon: "circle", // 设置图例图标为圆形
      itemWidth: 10, // 显示顶部标注左侧图片的宽度
      itemHeight: 10, // 显示顶部标注左侧图片的高度
      itemGap: 30 // 标注间距
    },
    grid: {
      top: "8%", // 距离边框距离
      left: "3%",
      right: "3%",
      bottom: "12%",
      containLabel: true
    },
    color: ["#5498FD", "#FD5454", "#6F54FD", "#FDA354"],
    tooltip: {
      // 提示框
      trigger: "axis",
      axisPointer: {
        // 坐标轴指示器的样式
        type: "shadow" // 显示阴影
      }
    },
    xAxis: {
      data: apiData.value.xdata
    },
    yAxis: {
      axisLabel: {
        color: "#000", // y轴字体颜色
        fontSize: 12
      }
    },
    series: [
      {
        name: "销售额",
        type: "bar",
        stack: "total",
        label: {
          show: false,
          color: "#000"
        },
        itemStyle: {
          borderRadius: [0, 0, 0, 0],
          color: "#4165D7"
        },
        barWidth: 15, // 柱状图宽度
        emphasis: {
          focus: "series"
        },
        data: apiData.value.ydata && apiData.value.ydata.length > 0 ? apiData.value.ydata[0].data : []
      },
      {
        name: "销售数量",
        type: "bar",
        stack: "total",
        label: {
          show: false,
          color: "#FFFFFF",
          formatter: "{a}"
        },
        itemStyle: {
          borderRadius: [0, 0, 0, 0],
          color: "#E6A23C"
        },
        emphasis: {
          focus: "series"
        },
        data: apiData.value.ydata && apiData.value.ydata.length > 1 ? apiData.value.ydata[1].data : []
      }
    ],
    dataZoom: [
      {
        type: "slider",
        start: 0,
        end: 100,
        bottom: "8%",
        height: 15
      }
    ]
  };

  userGrowthChart.setOption(option);
  charts.value.push(userGrowthChart);
  try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')

        let str1 = sliderZoom._displayables.handleLabels[0].style.text
        var patt1 = /[\u4e00-\u9fa5]+/g;
        //利用join将结果转换成字符串
        var letters = str1.match(patt1).join("");
        let width1 = letters.length * 10 + (str1.length - letters.length) * 13

        // let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 13
        // let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 13

        let str2 = sliderZoom._displayables.handleLabels[1].style.text
        var patt1 = /[\u4e00-\u9fa5]+/g;
        //利用join将结果转换成字符串
        var letters = str2.match(patt1).join("");
        let width2 = letters.length * 10 + (str2.length - letters.length) * 13
     
        // sliderZoom._displayables.handleLabels[0].x = option.dataZoom.start < 10 ? leftP : 0
        // sliderZoom._displayables.handleLabels[1].x = option.dataZoom.start > 90 ? rightP : 0
        sliderZoom._displayables.handleLabels[0].x = width1
        sliderZoom._displayables.handleLabels[1].x =  -width2
        
        userGrowthChart.on('datazoom', function (e: any) {
          let leftP, rightP
            if (e.start < 10) {
                leftP = width1
            } else {
                leftP = 0
            }
            if (e.end > 90) {
                rightP = -width2
            } else {
                rightP = 0
            }
            sliderZoom._displayables.handleLabels[0].x = leftP
            sliderZoom._displayables.handleLabels[1].x = rightP
        })
        
    } catch (error) {
        console.log(error);
    }
};
const handleData = async () => {
  apiData.value = await commonData.getSaleData(saleParams.value);
  setTimeout(() => {
    stockChart();
  }, 500);
};
onMounted(async () => {
  handleData();
});
</script>

<style lang='less' scoped>
.distributor {
  background: #ffffff;
  border-radius: 8px;
  height: 648px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.stock_card {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ebeef5;
  padding: 16px 12px;
  .stock_top {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .stock_top_item {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
      span {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .stock_button {
    padding: 4px 16px 12px 16px;
    display: flex;
    align-items: center;
    .stock_button_count {
      width: 40%;
      .number {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.but {
  display: flex;
  span {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 12px;
  }
  .active {
    color: var(--el-color-primary);
  }
}
.distributor_chart {
  width: 100%;
  height: 400px;
  margin-bottom: 16px;
  margin-top: 12px;
}
.ranking {
  .ranking_title {
    font-weight: bold;
    font-size: 14px;
    color: #000000;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 4px;
  }
  .ranking_list {
    display: flex;
    .ranking_item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ebeef5;
      padding: 4px 0px;
      .icon {
        width: 18px;
        height: 32px;
        margin-right: 8px;
      }
      .rank {
        font-weight: 400;
        font-size: 14px;
        color: var(--el-color-primary);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-right: 8px;
      }
      .name {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .ranking_item + .ranking_item {
      border-left: none;
    }
  }
}
</style>