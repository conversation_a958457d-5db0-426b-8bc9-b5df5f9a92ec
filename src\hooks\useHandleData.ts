import { ElMessageBox, ElMessage } from "element-plus";
import { initI18n } from "@/i18n";
import baseService from "@/service/baseService";

/**
 * @description 操作单条数据信息 (二次确认【删除、禁用、启用、重置密码】)
 * @param {String} api 操作数据接口的api (必传)
 * @param {Object} params 携带的操作数据参数 {id,params} (必传)
 * @param {String} message 提示信息 (必传)
 * @param {String} method 请求方式 (不必传,默认为 delete)
 * @param {String} confirmType icon类型 (不必传,默认为 warning)
 * @returns {Promise}
 */

type MethodType = "delete" | "put" | "post" | "get";
type MessageType = "" | "success" | "warning" | "info" | "error";

export const useHandleData = (
  api: string,
  params: any = {},
  message: string,
  method: MethodType = "delete",
  confirmType: MessageType = "warning"
) => {
  const { t } = initI18n.global;
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`${message}?`, t("prompt.title"), {
      confirmButtonText: t("confirm"),
      cancelButtonText: t("cancel"),
      type: confirmType,
      draggable: true
    })
      .then(async () => {

        const res = await baseService[method](api, params);
        if (!res) return reject(false);
        ElMessage({
          type: "success",
          message: `操作成功!`
        });
        resolve(true);
      })
      .catch(() => {
        // cancel operation
      });
  });
};
