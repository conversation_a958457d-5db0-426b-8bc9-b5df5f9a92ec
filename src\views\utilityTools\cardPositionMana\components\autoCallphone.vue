<template>
  <div style="width: 100%">
    <div class="title">手机互拨记录</div>
    <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <div style="display: flex; gap: 8px">
          <el-button type="primary" @click="mutualDialing">互拨配置</el-button>
        </div>
      </template>
      <template #header-right>
        <div style="display: flex; gap: 8px">
          <el-input :prefix-icon="Search" style="width: 320px" v-model="state.dataForm.info" placeholder="请输入手机号" clearable></el-input>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </template>
      <!-- <template #status="{ row }">
        <el-button link type="danger" v-if="row.cardStatus == 0">禁用</el-button>
        <el-button link type="success" v-if="row.status == 1">正常</el-button>
      </template>
      <template #remark="{ row }">
        <template v-if="row.remark">{{ row.remark }}</template>
        <div v-else>
          <span>-</span>
          <el-button @click="editHandle(row)" link type="primary"
            ><el-icon><Edit /></el-icon
          ></el-button>
        </div>
      </template> -->
      <template #createDate="{ row }">
        <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
      </template>
    </ny-table>
    <!-- 备注 -->
    <editRemark ref="editRemarkRef" @refreshDataList="state.getDataList()"></editRemark>
    <!-- 互拨配置 -->
    <mutualDialingPage ref="mutualDialingPageRef"></mutualDialingPage>
  </div>
</template>
        
<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import editRemark from "./edit-remark.vue";
import baseService from "@/service/baseService";
import mutualDialingPage from "./mutualDialing.vue";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/MobileMutualDialing/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/MobileMutualDialing",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    prop: "phone",
    label: "拨打手机号",
    minWidth: 100
  },
  {
    prop: "receivePhone",
    label: "接收手机号",
    minWidth: 100
  },
  {
    prop: "createDate",
    label: "拨打时间",
    minWidth: 100
  }
]);

const mutualDialingPageRef = ref();

// 互拨电话配置
const mutualDialing = () =>{
  mutualDialingPageRef.value.init()
}

// 重置操作
const getResetting = () => {
  state.dataForm.info = "";
  state.page = 1;
  state.getDataList();
};

const editRemarkRef = ref();
const editHandle = (row: any) => {
  nextTick(() => {
    editRemarkRef.value.init(1, row.id);
  });
};
</script>
        
        <style lang='less' scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.el-button {
  margin-left: 0px;
}
</style>