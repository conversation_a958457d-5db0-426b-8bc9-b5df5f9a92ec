<template>
    <el-dialog v-model="visible" title="付款" class="order-confirm-dialog" :close-on-click-modal="false" :close-on-press-escape="false" @close="close" width="480px">
        <el-form label-position="top" :model="form" :rules="rules" ref="formRef">
            <el-form-item label="账户名称" prop="accountName">
                <el-input v-model="form.accountName" placeholder="请输入账户名称" />
            </el-form-item>
            <el-form-item label="收款账户" prop="account">
                <el-input v-model="form.account" placeholder="请输入收款账户" />
            </el-form-item>
            <el-form-item label="付款金额(元)" prop="amount">
                <el-input v-model="form.amount" placeholder="请输入付款金额" />
            </el-form-item>
            <el-form-item label="付款凭证" prop="picUrl">
                <ny-upload :limit="1" v-model:imageUrl="form.picUrl" accept="image/*"></ny-upload>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="submit">确定付款</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref, defineExpose, defineEmits } from "vue";
    import baseService from "@/service/baseService";
    import { ElMessage } from "element-plus";
    const visible = ref(false);
    const formRef = ref();
    const emit = defineEmits(['refresh']);


    const form = ref({
        accountName: '',
        amount: '',
        account: '',
        picUrl: ''
    });

    const rules = ref({
        accountName: [{ required: true, message: '请输入账户名称', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入付款金额', trigger: 'blur' }],
        account: [{ required: true, message: '请输入收款账户', trigger: 'blur' }],
        picUrl: [{ required: true, message: '请上传付款凭证', trigger: 'blur' }]
    });


    const open = (data: any) => {
        form.value.orderId = data.id;
        visible.value = true;
    }

    const close = () => {
        visible.value = false;
    }

    const submit = () => {
        formRef.value.validate((valid: boolean) => {
            if (valid) {
                baseService.post('/purchase/channelPay', form.value).then(res => {
                    if (res.code == 0) {
                        ElMessage.success('付款成功');
                        close();
                        emit('refresh');
                    }
                });
            }
        });
    }

    defineExpose({
        open
    });

</script>