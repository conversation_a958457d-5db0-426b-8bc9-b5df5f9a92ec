<template>
    <div>
        <el-dialog v-model="visible" title="选择推送合作商" width="800">
            
            <el-transfer
                v-model="partnersIds"
                filterable
                filter-placeholder="搜索合作商名称"
                :titles="['选择合作商', '已选合作商']"
                :button-texts="['从已选取消', '添加到已选']"
                :data="partnersList"
                :props="{
                    key: 'id',
                    label: 'companyName'
                }"
                class="select-partner"
            >
            </el-transfer>

            <template #footer>
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :loading="butLoading" @click="submit">{{ btnTxt }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
    import { ref, defineExpose, defineProps, defineEmits } from 'vue'
    import { ElMessage } from 'element-plus';
    import baseService from "@/service/baseService";

    defineProps({
        btnTxt: {
            type: String,
            default: '提交'
        }
    })

    const visible = ref(false)
    const butLoading = ref(false)

    const emit = defineEmits(['change'])

    const init = () => {
        visible.value = true
        getList();
        getpartnerApiList();
    }

    // 合作商列表
    const partnersList = ref([])
    const getList = () => {
        baseService.get("/partner/partner/synchronizeListingAPI").then((res) => {
            if (res.code == 0) {
                partnersList.value = res.data || [];
            }
        });
    }

    // 获取已开通的合作商 (注：接口查询条件，1.接口类型：进推送、互推；2.开通状态：已开通、永久；)
    const getpartnerApiList = () =>{
        baseService.get("/partner/partner/partnerApiList").then((res) => {
            partnersIds.value = res.data.list.map((item:any) => item.id);
        })
    }

    const partnersIds = ref([])

    // 提交
    const submit = () => {
        if(!partnersIds.value.length){
            return ElMessage.warning('请选择合作商');
        }
        emit('change', partnersIds.value);
        visible.value = false;
    }

    defineExpose({
        init
    })

</script>

<style lang="scss">
.select-partner{
    display: flex;
    align-items: center;

    .el-transfer-panel{
        flex: 1;
    }
    .el-transfer__buttons{
        .el-button{
            display: block;
            margin: 10px 0;
        }
    }
}
</style>