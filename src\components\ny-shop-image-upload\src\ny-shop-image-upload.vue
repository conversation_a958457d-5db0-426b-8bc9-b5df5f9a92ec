<template>
  <div>
    <el-upload
      :class="{ 'hide-upload': previousProps.fileList.length && previousProps.limit == 1 }"
      class="avatar_uploader"
      drag
      v-model:file-list="previousProps.fileList"
      :limit="previousProps.limit"
      :accept="previousProps.accept"
      :multiple="previousProps.multiple"
      :action="url"
      list-type="picture-card"
      :show-file-list="previousProps.showFileList"
      :before-upload="beforeAvatarUpload"
      :auto-upload="false"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleExceed"
      :on-change="handleChange"
    >
      <el-icon class="el-icon--upload" size="44" style="color: var(--el-color-primary); margin-bottom: 0px"><upload-filled /></el-icon>
      <div class="upload--tip">
        <span style="color: #909399; font-size: 12px">将图片拖到此处或</span>
        <span style="color: var(--el-color-primary); font-size: 12px">点击上传</span>
      </div>
      <template #tip>
        <div class="el-upload__tip">{{ props.tip }}</div>
      </template>
      <template #file="{ file }">
        <div style="width: 100%" draggable="true" @dragstart="handleDragStart($event, file)" @drop="handleDrop($event, file)" @dragover.prevent v-loading="file.loading" element-loading-text="上传中...">
          <div class="showPic">
            <el-image :src="file.url" class="upload-image" :fit="imgContain ? 'contain' : 'cover'"></el-image>
            <div class="upload-handle" @click.stop>
              <div class="handle-icon" @click="onElPreview(file)">
                <el-icon style="color: #fff"><ZoomIn /></el-icon>
              </div>
              <div class="handle-icon" @click="onElRemove(file)">
                <el-icon style="color: #fff"><Delete /></el-icon>
              </div>
            </div>
          </div>
          <p class="picIndex" v-if="previousProps.limit > 1">{{ getPicIndex(file) }}</p>
        </div>
      </template>
    </el-upload>
    <el-dialog v-model="state.preview.show">
      <div style="margin-top: 12px">
        <el-image :src="state.preview.url" />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, defineComponent, watch, PropType, defineEmits } from "vue";
import { ElMessage, UploadProps, UploadUserFile } from "element-plus";
import { ZoomIn, Delete } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { getToken } from "@/utils/cache";
import app from "@/constants/app";
import Log from "@/components/ny-flowable/package/Log";
const emits = defineEmits(["urlArr", "update:modelValue"]);
interface UploadFileExt extends UploadUserFile {
  serverUrl?: string;
  fileIndex?: string;
}
const props = defineProps({
  modelValue: {
    type: Array || string,
    required: true
  },
  multiple: {
    type: Boolean,
    default: false
  },
  dialogVisible: {
    type: Boolean,
    default: false
  },
  fileList: {
    type: Array,
    default: () => []
  },
  limit: {
    type: Number,
    default: 1
  },
  fileSize: {
    type: Number || String,
    default: 3
  },
  accept: {
    type: String,
    default: ".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PNG,.GIF"
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: "images"
  },
  tip: {
    type: String,
    default: "建议尺寸16:9,JPEG、PNG格式"
  },
  imgContain: {
    type: Boolean,
    default: false
  }
});

let fileUploadIndex = 0;

const previousProps = reactive({
  // 文件地址
  modelValue: <any>"",
  // 是否支持多选
  multiple: props.multiple,
  // 对话框文件地址
  dialogImageUrl: "",
  // 对话框是否可见
  dialogVisible: props.dialogVisible,
  // 文件列表
  fileList: props.fileList,
  // 限制上传的文件数量
  limit: props.limit,
  // 限制上传的文件大小,传参时以参数为准。不传时默认20M
  fileSize: props.fileSize,
  // 允许上传的文件类型
  accept: props.accept,
  // 是否显示文件列表
  showFileList: props.showFileList,
  //   图片填充方式
  imgContain: props.imgContain
});

const hasBeenUploaded = ref(false);

// 监听文件地址进行列表回显
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length) {
      if (hasBeenUploaded.value) return;
      if (props.type == "image") {
        previousProps.modelValue = newVal.split(",");
      } else {
        previousProps.modelValue = newVal;
      }
      previousProps.fileList = [];
      previousProps.modelValue.forEach((item) => {
        previousProps.fileList.push({ url: item });
      });
      fileUploadIndex = previousProps.fileList.length;
    } else {
      previousProps.fileList = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const state: {
  // 预览弹窗
  preview: {
    show: boolean;
    url: string;
  };
  events: any;
} = reactive({
  preview: {
    show: false,
    url: ""
  },
  events: []
});

// 文件上传后端地址
const url = `${app.api}/sys/oss/uploadList?token=${getToken()}`;

// 处理头像上传前的函数
const beforeAvatarUpload = (file: any, uploadFiles: any) => {
  const fileSize = previousProps.fileSize * 1024 * 1024;
  console.log(fileSize, file.size);
  if (file.size > fileSize) {
    ElMessage.warning(`上传文件大小在${previousProps.fileSize}M内，请重新选择文件上传`);
    return false;
  }
};
// 点击文件列表中已上传的文件时的钩子
const handlePictureCardPreview: UploadProps["onPreview"] = (file: any) => {
  previousProps.dialogImageUrl = file.url;
  previousProps.dialogVisible = true;
};

// 当超出限制时，执行的钩子函数
const handleExceed: UploadProps["onExceed"] = (files, fileList) => {
  ElMessage.warning(`当前限制选择${previousProps.limit}个文件`);
};

// 图片下标
const getPicIndex = (file: UploadUserFile) => {
  let index = previousProps.fileList.findIndex((item: any) => item.uid === file.uid);
  return index + 1;
};

// 拖拽排序开始
const handleDragStart = (event: any, file: any) => {
  // 输出被拖动的文件对象
  // 在当前fileList中查找被拖动文件的索引
  const index = previousProps.fileList.findIndex((element) => element === file);
  // 将被拖动文件的索引设置到dataTransfer对象中，以便在拖放时使用
  event.dataTransfer.setData("index", index.toString());
};
// 拖拽排序结束
const handleDrop = (event: any, file: any) => {
  // 在当前fileList中查找被释放文件的索引
  const index = previousProps.fileList.findIndex((element) => element === file);
  // 阻止默认的拖放行为（例如打开链接等）
  event.preventDefault();
  // 从dataTransfer对象中获取被拖动项的索引
  const draggedIndex = Number(event.dataTransfer.getData("index"));
  // 从原始fileList中获取被拖动的项
  const draggedItem = previousProps.fileList[draggedIndex];
  // 创建当前fileList的副本以进行修改
  const updatedList = [...previousProps.fileList];
  // 从原始位置删除被拖动的项
  updatedList.splice(draggedIndex, 1);
  // 将被拖动的项插入到列表的新位置
  updatedList.splice(index, 0, draggedItem);
  // 使用修改后的列表更新fileList
  previousProps.fileList = updatedList;
  emits("update:modelValue", getAllUrls());
};

// 图片预览
const onElPreview = (file: UploadFileExt) => {
  typeof state.events["onPreview"] == "function" && state.events["onPreview"](file);
  if (!file || !file.url) {
    return;
  }
  state.preview.show = true;
  state.preview.url = file.url;
};

// 图片删除
const onElRemove = (file: UploadUserFile) => {
  let index = previousProps.fileList.findIndex((item: any) => item.uid === file.uid);
  previousProps.fileList.splice(index, 1);
  emits("update:modelValue", getAllUrls());
  fileUploadIndex = previousProps.fileList.length;
};

const getAllUrls = () => {
  console.log(previousProps.fileList);
  const srcArray = previousProps.fileList.map((item: any) => (item.ossurl ? item.ossurl : item.url));
  if (props.type == "image") {
    return srcArray.join("");
  } else {
    return srcArray;
  }
};

let fileLists = reactive([]);
let timer: any = undefined;
let list = reactive(<any>[]);

const handleChange = (file: any, fileList: any) => {
  if (file.status !== "ready") return;
  const fileSize = previousProps.fileSize * 1024 * 1024;
  if (file.size > fileSize) {
    ElMessage.warning(`上传文件大小在${previousProps.fileSize}M内，请重新选择文件上传`);
    // 2025年5月20日10:21:45 把文件过大的筛除
    fileList = fileList.filter((ele: any) => ele.uid != file.uid);
    // return;
  }
  fileLists = fileList;
  //  2025年5月20日10:21:41 回显控制
  previousProps.fileList = fileList;

  // previousProps.fileList = fileList.map((m) => m.raw);

  clearTimeout(timer);
  timer = setTimeout(() => {
    // console.log('fileUploadIndex', fileUploadIndex, fileLists, fileLists.length)
    list = fileLists.slice(fileUploadIndex, fileLists.length);

    fileLists.map((item: any, index: number) => {
      if (index >= fileUploadIndex) {
        item.loading = true;
      }
    });
    console.log(list, "====== jsdhfjksdhf =====");
    updateIndex.value = 0;
    queueUpdate();

    // ofdsBtnSure(list, fileUploadIndex, fileLists.length)
    fileUploadIndex = fileLists.length;
  }, 200);
};

// 图片排队上传
const updateIndex = ref(0);
const successImgs = ref(<any>[]);
const queueUpdate = () => {
  console.log(list.length - 1, updateIndex.value, updateIndex.value <= list.length - 1, "====== sdjfsldkjflsdjfsldkjf ======z");
  if (updateIndex.value <= list.length - 1) {
    const formData = new FormData();
    formData.append("files", list[updateIndex.value].raw, list[updateIndex.value].name);
    baseService
      .post("/sys/oss/uploadList", formData, { "Content-Type": "multipart/form-data" })
      .then((res) => {
        if (res.code == 0 && res.data.length) {
          hasBeenUploaded.value = true;
          // successImgs.value.push(res.data[0].src);
          list[updateIndex.value].loading = false;
          list[updateIndex.value].ossurl = res.data[0].src;
          updateIndex.value++;
          queueUpdate();
        } else {
          ElMessage.error("文件上传失败");
          list[updateIndex.value].loading = false;
        }
      })
      .catch((error) => {
        console.log(list[updateIndex.value], "====== list[updateIndex.value] =====");
      })
      .finally(() => {
        list[updateIndex.value].loading = false;
      });
  } else {
    list.map((item: any, index: number) => {
      item.loading = false;
    });
    let imgs = list.map((item: any) => {
      return item.ossurl;
    });
    console.log(props.modelValue, "===== props.modelValue ======");

    imgs = [...props.modelValue, ...imgs];
    if (props.type == "image") {
      emits("update:modelValue", imgs.split(","));
    } else {
      emits("update:modelValue", imgs);
    }
    console.log(imgs, "====== successImgs.value ======");
  }
};

// 批量上传
const ofdsBtnSure = (list, currentIndex, length) => {
  const formData = new FormData();
  list.forEach((file) => {
    formData.append("files", file.raw, file.name);
  });

  baseService.post("/sys/oss/uploadList", formData, { "Content-Type": "multipart/form-data" }).then((res) => {
    if (res.code == 0) {
      hasBeenUploaded.value = true;
      let imgs = res.data.map((item: any) => {
        return item.src;
      });

      fileLists.map((item: any, index: number) => {
        if (index >= currentIndex && index <= length) {
          item.loading = false;
        }
      });

      imgs = [...props.modelValue, ...imgs];
      if (props.type == "image") {
        emits("update:modelValue", imgs.split(","));
      } else {
        emits("update:modelValue", imgs);
      }
    }
  });
};
</script>

<style lang="less" scoped>
.avatar_uploader {
  &.hide-upload {
    :deep(.el-upload),
    :deep(.el-upload__tip) {
      font-size: 12px;
      display: none;
    }
  }
  :deep(.el-upload--picture-card) {
    width: 196px;
    height: 110px;
    border: none;
    .el-upload-dragger {
      width: 100%;
      height: 100%;
      border: none;
      background: var(--el-color-primary-light-9);
      box-shadow: 0px 2px 5px 0px rgba(87, 128, 239, 0.1);
      border-radius: 6px 6px 6px 6px;
      padding: 14px;
    }
  }
  // :deep(.el-upload-list--picture-card .el-upload-list__item){
  //     width: 294px;
  //     height: 165px;
  //     border: none;
  // }
  :deep(.el-loading-spinner) {
    top: 30%;
  }
  :deep(.el-upload-list) {
    width: 100%;

    li {
      width: 196px;
      height: 110px;
      border: none;
      border-radius: 0px;
      // border: 1px solid red;
      position: relative;

      .showPic {
        width: 196px;
        height: 110px;
        position: relative;
        border-radius: 6px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        .upload-image {
          height: 100%;
        }

        img {
          width: 100%;
          height: 100%;
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          object-fit: contain;
        }

        .upload-handle {
          display: none;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          background-color: rgba(0, 0, 0, 0.3);
          align-items: center;
          justify-content: center;
          border-radius: 6px;

          .el-icon {
            margin: 0 5px;
            font-size: 25px !important;
          }
        }

        &:hover {
          .upload-handle {
            display: flex;
          }
        }
      }

      .picIndex {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 30px;
        height: 25px;
        font-size: 14px;
        text-align: center;
        line-height: 25px;
        font-weight: bold;
        margin: 0px;
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border-radius: 6px 0px 6px 0px;
      }
    }
  }
}
.upload--tip {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 10px;
  left: 0px;
  line-height: 20px;
  width: 100%;
}
// .showPic {
//     width: 294px;
//     height: 165px;
//     position: relative;
//     border-radius: 6px;
//     overflow: hidden;

//     img {
//         width: 100%;
//         height: 100%;
//         border: 1px solid var(--el-border-color);
//         border-radius: 6px;
//         object-fit: contain;
//     }

//     .upload-handle {
//         display: none;
//         position: absolute;
//         width: 100%;
//         height: 100%;
//         top: 0;
//         left: 0;
//         background-color: rgba(0, 0, 0, 0.3);
//         align-items: center;
//         justify-content: center;
//         border-radius: 6px;

//         .el-icon {
//             margin: 0 5px;
//             font-size: 25px !important;
//         }
//     }

//     &:hover {
//         .upload-handle {
//             display: flex;
//         }
//     }
// }
</style>
