<template>
    <div class="business_page">
        <div class="business_header flx-align-center">
            <el-radio-group v-model="dataForm.type" @change="radioChange">
                <el-radio value="1">售后原因</el-radio>
                <el-radio value="2">销售员工</el-radio>
                <el-radio value="3">销售价位</el-radio>
                <!-- <el-radio value="4">销售渠道</el-radio> -->
                <el-radio value="5">游戏</el-radio>
            </el-radio-group>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">销售订单售后柱状图</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="legend">
                        <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData"
                            :key="index" @change="changeShow"></el-checkbox>
                    </div>
                    <el-date-picker v-model="echartDate" type="daterange" start-placeholder="开始时间"
                        end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        style="width: 220px; border-radius: 20px" @change="getSaleOrderAfterSaleBarChart" />
                </div>
            </div>
            <div class="header_describe">销售订单售后明细数据</div>
            <div class="charts">
                <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
            </div>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">订单售后明细表</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <el-date-picker v-model="tableDate" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                        format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 220px; border-radius: 20px"
                        @change="getAfterSaleDetailTable" />
                </div>
            </div>
            <div style="padding: 0px 20px 20px 20px;">
                <el-table :data="tableData" style="width: 100%;" cell-class-name="ch-56" border class="business_table">
                    <template v-for="item in columns" :key="item">
                        <el-table-column :prop="item.prop" :label="item.label" :sortable="item.prop != 'name'"
                            :min-width="item.minWidth" align="center">
                            <template #header v-if="item.prop == 'name'">
                                <span v-if="dataForm.type == '1'">售后原因</span>
                                <span v-if="dataForm.type == '2'">销售员工</span>
                                <span v-if="dataForm.type == '3'">销售价位</span>
                                <span v-if="dataForm.type == '4'">销售渠道</span>
                                <span v-if="dataForm.type == '5'">游戏</span>
                            </template>
                            <template #default="{ row, $index }">
                                {{ row[item.prop] }}
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 空状态 -->
                    <template #empty>
                        <div style="padding: 68px 0">
                            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                        </div>
                    </template>
                </el-table>
            </div>

        </div>
    </div>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";
import { formatDate } from "@/utils/method";

const tableData = ref(<any>[]);

// 表格配置项
const columns = reactive([
    {
        prop: "name",
        label: "售后原因",
        minWidth: 112
    },
    {
        prop: "addAfterSaleOrder",
        label: "新增售后订单",
        minWidth: 80,
        sortable: "custom",
    },
    {
        prop: "waitProcessAfterSaleOrder",
        label: "待处理售后订单",
        minWidth: 80,
        sortable: "custom",
    },
    {
        prop: "afterSaleRefundOrder",
        label: "售后已退款订单",
        minWidth: 100,
        sortable: "custom",
    },
]);

const echartDate = ref();
const tableDate = ref();

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    salesChannelId: "",
    startTime: "",
    endTime: "",
    type: "1"
});

// 图例数据
const legendData = ref([
    { name: '新增售后订单', color: '#4165D7', show: true },
    { name: '待处理售后订单', color: '#00B42A', show: true },
    { name: '售后已退款订单', color: '#722ED1', show: true }
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '新增售后订单',
        type: 'bar',
        itemStyle: {
            color: "#4165D7"
        },
        barMaxWidth: 30,
        data: [],
    },
    {
        name: '待处理售后订单',
        type: 'bar',
        itemStyle: {
            color: "#00B42A"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '售后已退款订单',
        type: 'line',
        stack: 'Total',
        smooth: true,
        itemStyle: {
            color: "#722ED1"
        },
        lineStyle: {
            width: 3,
            color: "#722ED1"
        },
        showSymbol: false,
        emphasis: {
            focus: 'series'
        },
        data: []
    }
])

// 销售订单售后柱状图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '3%',
            top: '10%',
            bottom: '6%',
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '订单(个)',
                // min: 0,
                // max: 'dataMax',
                // interval: 50,
                // axisLabel: {
                //     formatter: '{value} 个'
                // }
            },
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 40,
            bottom: "2%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

// 销售订单售后柱状图
const getSaleOrderAfterSaleBarChart = () => {
    dataForm.value.startTime = echartDate.value ? echartDate.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = echartDate.value ? echartDate.value[1] + " 23:59:59" : ""
    legendData.value.map((item: any) => item.show = true);
    baseService.post("/dataAnalysis/saleOrderAfterSaleBarChart", dataForm.value).then(res => {
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

// 订单售后明细表
const getAfterSaleDetailTable = () => {
    dataForm.value.startTime = tableDate.value ? tableDate.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = tableDate.value ? tableDate.value[1] + " 23:59:59" : "";
    baseService.post("/dataAnalysis/afterSaleDetailTable", dataForm.value).then(res => {
        tableData.value = res.data
    })
}

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    echartDate.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    tableDate.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getSaleOrderAfterSaleBarChart();
    getAfterSaleDetailTable();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})

const radioChange = () => {
    getSaleOrderAfterSaleBarChart();
    getAfterSaleDetailTable();
}


const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getSaleOrderAfterSaleBarChart();
    getAfterSaleDetailTable();
};

defineExpose({
    init
});

</script>

<style lang='less' scoped>
.business_page {
    margin-top: 12px;
}

.business_header {}

.business_center {}

.business_table {
    :deep(th .cell) {
        background: none !important;
    }

    :deep(th:nth-child(n+2):nth-child(-n+2)) {
        background-color: rgba(65, 101, 215, 0.1) !important;

        .cell {
            color: #4165D7;
        }
    }

    :deep(th:nth-child(n+3):nth-child(-n+3)) {
        background-color: rgba(0, 180, 42, 0.1) !important;

        .cell {
            color: #00C568;
        }
    }

    :deep(th:nth-child(n+4):nth-child(-n+4)) {
        background-color: rgba(114, 46, 209, 0.1) !important;

        .cell {
            color: #722ED1;
        }
    }

    :deep(td:nth-child(n+2):nth-child(-n+2)) {
        background-color: rgba(65, 101, 215, 0.05) !important;
    }

    :deep(td:nth-child(n+3):nth-child(-n+3)) {
        background-color: rgba(0, 180, 42, 0.05) !important;
    }

    :deep(td:nth-child(n+4):nth-child(-n+4)) {
        background-color: rgba(114, 46, 209, 0.05) !important;
    }
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165D7;
                        border-color: #4165D7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #00B42A;
                        border-color: #00B42A;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ED1;
                        border-color: #722ED1;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
</style>