<template>
    <el-drawer v-model="visible"  size="40%" class="compensationServe_drawer">
        <template #header>
            <div class="drawer_title">{{ !dataForm.id ? $t('add') + '包赔服务' : $t('update') + '包赔服务' }}</div>
        </template>
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" @keyup.enter="">
            <el-row :gutter="12">
                <el-col :span="24">
                    <el-form-item label="选择游戏" prop="games">
                        <template #label>
                            <span>选择游戏</span>
                            <el-checkbox
                                v-model="checkAll"
                                :indeterminate="isIndeterminate"
                                @change="handleCheckAllChange"
                                style="float: right;"
                            >全部</el-checkbox>
                        </template>
                        <el-checkbox-group
                            v-model="dataForm.games"
                            :indeterminate="isIndeterminate"
                            @change="handleCheckedCitiesChange"
                        >
                            <el-checkbox v-for="(item,index) in gamesList" :key="index" :label="item.title" :value="item.id" />
                        </el-checkbox-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="包赔名称" prop="title">
                        <el-input v-model="dataForm.title" placeholder="包赔名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="包赔类型" prop="type">
                        <el-select v-model="dataForm.type" placeholder="包赔类型">
                            <el-option label="包赔权益" :value="1" />
                            <el-option label="增值包赔" :value="2" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="包赔说明" prop="info">
                        <el-input v-model="dataForm.info" placeholder="包赔说明"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="费率(%)" prop="rate">
                        <el-input-number class="input-number" v-model="dataForm.rate" placeholder="费率(%)" :controls="false" :precision="2"></el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="最高赔付(%)" prop="maxIndemnityRate">
                        <el-input-number class="input-number" v-model="dataForm.maxIndemnityRate" placeholder="最高赔付(%)" :controls="false" :precision="2"></el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="dataForm.sort" :min="0" style="width: 100%;"/>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const visible = ref(false);  // 对话框显隐
const dataFormRef = ref();  // 表单ref

const dataForm = reactive({  // 表单变量
    id: null,
    title: '',
    type: '',
    info: '',
    rate: '',
    maxIndemnityRate: '',
    sort: '',
    games: <any>[],
});
const rules = ref({  // 表单必填项
    games: [{ required: true, message: '请选择游戏', trigger: 'change', },],
    title: [{ required: true, message: '请输入包赔名称', trigger: 'blur', },],
    type: [{ required: true, message: '请选择包赔类型', trigger: 'change', },],
    info: [{ required: true, message: '请输入包赔说明', trigger: 'blur', },],
    rate: [{ required: true, message: '请输入费率', trigger: 'blur', },],
    maxIndemnityRate: [{ required: true, message: '请输入最高赔付', trigger: 'blur', },],
    sort: [{ required: true, message: '请输入排序', trigger: 'blur', },],
});

// 表单初始化
const init = (id?: any) => {
    visible.value = true;
    dataForm.id = id ? id : null;
    getGamesList();

    // 获取表单详情
    if (id) {
        getInfo(id);
    }
    // 重置表单数据
    if (dataFormRef.value) {
        dataFormRef.value.resetFields();
    }
};

const getInfo = (id: number) => {
    baseService.get("/shop/guarantee/" + id).then((res) => {
        const games = res.data.games.map((item: any) => item.id);
        const data = JSON.parse(JSON.stringify(res.data));
        data.games = games;
        Object.assign(dataForm, data);
    });
}

// 获取游戏列表
const gamesList = ref(<any>[]);  // 游戏列表
const getGamesList = () => {
    baseService.get("/game/sysgame/listGames").then((res) => {
        gamesList.value = res.data
    });
};

const checkAll = ref(false);
const isIndeterminate = ref(false);
const handleCheckAllChange = (val: boolean) => {
    const ids = gamesList.value.map((item: any) => item.id);
    dataForm.games = val ? ids : [];
    isIndeterminate.value = false
}
const handleCheckedCitiesChange = (value: string[]) => {
    const checkedCount = dataForm.games.length
    checkAll.value = checkedCount == gamesList.value.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < gamesList.value.length
}

// 表单提交
const btnLoading = ref(false);
const emit = defineEmits(["refreshDataList"]);
const submitForm = () => {
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        const games:any = [];
        const params = JSON.parse(JSON.stringify(dataForm));
        params.games.map((it:any) => {
            gamesList.value.map((el: any) => {
                if (it == el.id) {
                    games.push(el)
                }
            })
        })
        params.games = games;
        btnLoading.value = true;
        (!dataForm.id ? baseService.post : baseService.put)("/shop/guarantee", params).then((res) => {
            ElMessage.success({
                message: t("prompt.success"),
                duration: 500,
                onClose: () => {
                    visible.value = false;
                    emit("refreshDataList");
                }
            });
        }).finally(() => {
            btnLoading.value = false;
        })
    });
}

defineExpose({
    init
});
</script>

<style lang='less' scoped>

</style>
<style lang='less'>
.compensationServe_drawer{
    .input-number{
        width: 100%;
        input{
            text-align: left;
        }
    }
    .el-drawer__header{
        margin-bottom: 0px;
    }
    .drawer_title{
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>