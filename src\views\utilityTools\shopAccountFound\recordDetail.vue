<template>
  <el-drawer v-model="visible" size="944" :footer="null" class="infoDrawerConfig">
    <template #header>
      <div class="drawer_title">执行记录详情</div>
    </template>
    <div class="shop_page">
      <el-form ref="formRef" :model="dataForm" label-suffix="：">
        <div class="shop_page_basic cardDescriptions">
          <div class="titleSty">基本信息</div>
          <el-descriptions title="" :column="2" size="default" border>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>排查开始时间</div>
              </template>
              {{ formatTimeStamp(dataForm.startTime) }}
            </el-descriptions-item>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>排查结束时间</div>
              </template>
              {{ formatTimeStamp(dataForm.endTime) }}
            </el-descriptions-item>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>被找回</div>
              </template>
              {{ dataForm.recoveredNumber }}
            </el-descriptions-item>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>被转手</div>
              </template>
              {{ dataForm.soldNumber }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" label-class-name="title">
              <template #label>
                <div>掉绑</div>
              </template>
              {{ dataForm.dropNumber }}
            </el-descriptions-item>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>更新时间</div>
              </template>
              {{ formatTimeStamp(dataForm.updateDate) }}
            </el-descriptions-item>
            <el-descriptions-item :span="1" label-class-name="title">
              <template #label>
                <div>执行状态</div>
              </template>
              <el-tag type="success" v-if="dataForm.executionStatus == '2'">执行成功</el-tag>
              <el-tag type="warning" v-if="dataForm.executionStatus == '1'">执行中</el-tag>
              <el-tag type="danger" v-if="dataForm.executionStatus == '3'">执行失败</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="shop_page_basic cardDescriptions" style="margin-top: 12px">
          <div class="titleSty">排查结果详情</div>
          <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-change="getTableData">
            <el-tab-pane label="被找回" name="1"></el-tab-pane>
            <el-tab-pane label="被转手" name="2"></el-tab-pane>
            <el-tab-pane label="掉绑" name="3"></el-tab-pane>
          </el-tabs>
          <el-table border :data="tableData" style="width: 100%">
            <el-table-column prop="shopCode" align="center" label="商品编码" width="180" />
            <el-table-column prop="gameName" align="center" label="游戏名称" width="180" />
            <el-table-column prop="address" align="center" label="商品信息">
              <!-- 商品标题 -->
              <template #default="{ row }">
                <div class="shoping">
                  <el-image style="height: 68px; width: 120px" :src="row.img" :preview-src-list="[row.img]" preview-teleported fit="cover" />
                  <div class="info">
                    <div class="title mle" v-html="row.shopTitle" @click="jumpGoodsDetails(row)"></div>
                    <div class="sle">
                      {{ `${row.gameName} / ${row.serverName}` }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="address" align="center" label="商品状态" width="180">
              <!-- 状态 -->
              <template #default="{ row }">
                <el-tag type="warning" v-if="row.shopStatus == '1'">待上架</el-tag>
                <el-tag type="primary" v-if="row.shopStatus == '2'">已上架</el-tag>
                <el-tag v-if="row.shopStatus == '3'" type="danger">已下架</el-tag>
                <el-tag v-if="row.shopStatus == '4'" type="success">已出售</el-tag>
                <el-tag type="warning" v-if="row.shopStatus == '7'">交易中</el-tag>
              </template>
            </el-table-column>
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 120px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
          <el-pagination
            :current-page="state.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :page-size="state.pageSize"
            :total="state.total"
            background
            :pager-count="5"
            layout="total, sizes, prev, pager, next, jumper"
            :hide-on-single-page="true"
            @size-change="pageSizeChangeHandle"
            @current-change="pageCurrentChangeHandle"
          ></el-pagination>
        </div>
      </el-form>
    </div>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef"></shop-info>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive, nextTick } from "vue";
import { ElMessage, FormItemRule } from "element-plus";
import { formatTimeStamp } from "@/utils/method";
import baseService from "@/service/baseService";
import ShopInfo from "@/views/shop/shop-info.vue";
const formRef = ref();
const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);
const dataForm = reactive(<any>{});
const activeName = ref("1");
const state = reactive({
  total: 0,
  page: 1,
  pageSize: 10
});
// 当前显示类型
const init = (row: any) => {
  visible.value = true;
  getInfo(row);
};

// 获取详情
const getInfo = (row: any) => {
  baseService.get("/shop/sysAccountFoundParent/" + row.id).then((res) => {
    if (res.code == 0 && res.data) {
      Object.assign(dataForm, res.data);
      getTableData();
    }
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("close");
};

// 获取游戏下拉
const tableData = ref(<any>[]);
const getTableData = () => {
  baseService.get("/shop/sysaccountfound/page", { parentAccountFoundId: dataForm.id, type: activeName.value, page: state.page, limit: state.pageSize }).then((res) => {
    tableData.value = res.data.list || [];
  });
};

const pageSizeChangeHandle = (val: any) => {
  state.pageSize = val;
  getTableData();
};
const pageCurrentChangeHandle = (val: any) => {
  state.page = val;
  getTableData();
};
// 商品详情
const shopInfoRef = ref();
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};
defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
.el-form {
  margin-bottom: 12px;
  display: block;
  .el-form-item {
    display: block;

    :deep(.el-input-group__append) {
      background: #fff !important;
    }
  }
}
:deep(.infoSty.el-alert--info) {
  color: var(--el-color-primary);
  background-color: transparent;
  display: flex;
  align-items: flex-start;
  padding-left: 0;

  .el-icon {
    margin-top: 4px;
  }
}
.accountTabsscriptpush {
  background: #fff;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
}
.shop_page {
  margin: 12px;
}
.shop_page_basic {
  border-radius: 8px;
}
</style>
<style lang="scss">
.infoDrawerConfig {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
    background: #f0f2f5;
  }
  .el-tabs__item {
    background-color: #f7f8fa;

    &.is-active {
      background-color: #fff;
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
