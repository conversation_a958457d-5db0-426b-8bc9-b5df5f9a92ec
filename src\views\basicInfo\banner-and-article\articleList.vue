<template>
  <div class="container article-list-wrap">
    <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <ny-button-group label="name" value="value" :list="statusList" v-model="state.dataForm.status" @change="state.getDataList"></ny-button-group>
      </template>

      <template #header-right>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-form-item>
            <el-input v-model="state.dataForm.title" placeholder="请输入标题名称" clearable :prefix-icon="Search"></el-input>
          </el-form-item>
          <el-form-item>
            <ny-select v-model="state.dataForm.articleTypeText" v-model:id="state.dataForm.articleType" dict-type="article_type" placeholder="请选择分类"></ny-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker class="input-240" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" />
          </el-form-item>

          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
        </el-form>
      </template>

      <template #header-custom>
        <div class="mb-12">
          <el-button v-if="state.hasPermission('basics:article:save')" type="primary" @click="addOrUpdateHandle()">新增文章</el-button>
          <el-button v-if="state.hasPermission('basics:article:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">删除</el-button>
        </div>
      </template>

      <!-- 分类 -->
      <template #articleType="{ row }">
        {{ state.getDictLabel("article_type", row.articleType, row.articleType) }}
      </template>

      <!-- 图片 -->
      <template #image="{ row }">
        <el-image style="width: 54px; height: 30px" :src="row.image" :preview-src-list="[row.image]" :preview-teleported="true" fit="cover" />
      </template>

      <!-- 排序 -->
      <template #sort="{ row }">
        <div class="flx-center sort-wrap">
          {{ row.sort }}
          <el-popover placement="top" :width="150" trigger="click" :visible="row.visible">
            <div class="sort-input">
              <el-input v-model="row.sort" type="number" size="small" placeholder="请输入排序"></el-input>
              <div class="btns flx-justify-between pt-8">
                <el-button size="small" :loading="row.loading" @click="row.visible = false">取消</el-button>
                <el-button size="small" :loading="row.loading" type="primary" @click="updateSort(row)">确定</el-button>
              </div>
            </div>
            <template #reference>
              <el-icon class="edit" @click="row.visible = true"><Edit /></el-icon>
            </template>
          </el-popover>
        </div>
      </template>

      <!-- 状态 0待发布1已发布2已下架-->
      <template #status="{ row }">
        <el-tag v-if="row.status == 1" type="success">已发布</el-tag>
        <el-tag v-else type="info">未发布</el-tag>
      </template>

      <!-- 创建日期 -->
      <template #createDate="{ row }">
        {{ timeFormat(row.createDate) }}
      </template>

      <!-- 更新日期 -->
      <template #updateDate="{ row }">
        {{ timeFormat(row.updateDate) }}
      </template>

      <template #operation="{ row }">
        <el-button v-if="state.hasPermission('basics:article:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">
          {{ $t("update") }}
        </el-button>
        <el-button v-if="state.hasPermission('basics:article:update') && row.status == 1" type="warning" text bg @click="updateStatus(row, 0)"> 取消发布 </el-button>
        <el-button v-if="state.hasPermission('basics:article:update') && row.status != 1" type="success" text bg @click="updateStatus(row, 1)"> 立即发布 </el-button>
        <el-button v-if="state.hasPermission('basics:article:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">
          {{ $t("delete") }}
        </el-button>
      </template>
    </ny-table>

    <!-- 新增/编辑 -->
    <article-add-or-update ref="articleAddOrUpdateRef" :key="articleAddOrUpdateKey" @refresh="state.getDataList"></article-add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { Edit, Search } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { timeFormat } from "@/utils/utils";
import ArticleAddOrUpdate from "./article-add-or-update.vue";

const { t } = useI18n();

// 状态列表
const statusList = ref([
  { name: "全部", value: "" },
  { name: "已发布", value: "1" },
  { name: "未发布", value: "0" }
]);

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 410
  },
  {
    prop: "articleType",
    label: "分类",
    minWidth: 198
  },
  {
    prop: "image",
    label: "封面图",
    minWidth: 100
  },
  {
    prop: "sort",
    label: "排序",
    minWidth: 200
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 200
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 200
  },
  {
    prop: "updateDate",
    label: "更新时间",
    minWidth: 200
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 280
  }
]);

const view = reactive({
  getDataListURL: "/basics/article/page",
  getDataListIsPage: true,
  deleteURL: "/basics/article",
  deleteIsBatch: true,
  dataForm: {
    status: "",
    title: "",
    articleType: "",
    startTime: "",
    endTime: "",
    articleTypeText: "",
    order: "asc",
    orderField: "sort"
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.status = "";
  state.dataForm.title = "";
  state.dataForm.articleType = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.dataForm.articleTypeText = "";
  state.getDataList();
};

// 新增  编辑
const articleAddOrUpdateRef = ref();
const articleAddOrUpdateKey = ref(0);
const addOrUpdateHandle = (id?: number) => {
  articleAddOrUpdateKey.value++;
  nextTick(() => {
    articleAddOrUpdateRef.value.init(id);
  });
};

// 修改状态
const updateStatus = (row: any, status: any) => {
  if (!row.id) return;
  row.status = status;
  row.loading = true;
  baseService
    .put("/basics/article", row)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.getDataList();
    })
    .finally(() => {
      row.loading = false;
    });
};

// 修改 排序
const updateSort = (row: any) => {
  if (!row.id) return;
  row.loading = true;
  baseService
    .put("/basics/article", row)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.getDataList();
      row.visible = false;
    })
    .finally(() => {
      row.loading = false;
    });
};

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
</script>

<style lang="scss" scoped>
.article-list-wrap {
  :deep(.input-240) {
    width: 220px;
  }
}
.sort-wrap {
  // position: relative;
  &:hover .edit {
    opacity: 1;
  }
  .edit {
    color: var(--el-color-primary);
    opacity: 0;
    cursor: pointer;
  }
}
</style>
