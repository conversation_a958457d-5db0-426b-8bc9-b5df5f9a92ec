import baseService from "@/service/baseService";
import { formatDate, formatCurrency } from "@/utils/method";
const methods = {
  // 工具
  // ----全部游戏
  getGameList: async () => {
    let res = await baseService.get("/game/sysgame/listGames");
    return res.data ? [{ id: "", title: "全部" }, ...res.data] : [];
  },
  currentDateTime: () => {
    let currentDate = new Date();
    let dateStr = currentDate.toLocaleString(); // 获取当前时间
    let regex = /\//g;
    return dateStr.replace(regex, "-");
  },
  currentWeekDate: () => {
    let alldate = [];
    for (let index = 0; index < 7; index++) {
      alldate.push(
        formatDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000 * index), "YYYY-MM-DD")
      );
    }
    return alldate;
  },
  //   实时数据
  realTimeData: async () => {
    let res = await baseService.get("/console/getRealTimeData");
    return res.data || {};
  },
  // 个人数据
  personData: async () => {
    let res = await baseService.get("/wallet/bill/dateTotal");
    return res.data || {};
  },
  // 系统消息
  sysMsgData: async () => {
    let res = await baseService.get("/im/message/page", { limit: 5, status: 0 });
    return res.data?.list || [];
  },
  // 工作台 - 商城概述
  getDeskShopdata: async () => {
    let res = await baseService.get("/console/getShopData");
    return res.data;
  },
  // 工作台 - 热门搜索
  getDeskPopulardata: async (params: any) => {
    let res = await baseService.get("/console/searchHistory", { ...params });
    return res || {};
  },
  // 工作台 - 代办
  getTodoData: async () => {
    let res = await baseService.get("/console/getTodo");
    return res.data;
  },
  // 工作台 -帮我卖
  getSaleData: async (weekOrMonth: any) => {
    let res = await baseService.get("/console/saleForMe", { weekOrMonth });
    return res.data;
  },
  // 工作台 -我帮卖
  getSaleOtherData: async (weekOrMonth: any, page: number) => {
    let res = await baseService.get("/console/saleFromMe", { weekOrMonth, limit: 10, page });
    return res.data;
  },
  //主页 - api数据
  apiData: async () => {
    let res = await baseService.get("/paid/function/page", {
      limit: 6,
      platform: import.meta.env.VITE_PLATFORM_MODE
    });
    return res.data?.list || [];
  }
};

export default methods;
