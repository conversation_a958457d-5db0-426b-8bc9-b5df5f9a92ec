<template>
  <div class="TableXScrollSty">
    <el-card :shadow="false" class="rr-view-ctx-card">
      <ny-flod-tab
        :list="[
          { label: '流程内容', value: '1' },
          { label: '交易行为', value: '2' }
        ]"
        v-model="state.tabType"
        value="value"
        label="label"
      ></ny-flod-tab>
      <processContent v-if="state.tabType == '1'"></processContent>
      <transaction v-else></transaction>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import processContent from "./processContent.vue";
import transaction from "./transaction.vue";

const state = reactive({
  tabType: "1"
});
</script>
