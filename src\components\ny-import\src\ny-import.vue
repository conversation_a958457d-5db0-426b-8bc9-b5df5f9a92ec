<template>
  <el-dialog v-model="visible" title="Excel导入" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs" style="margin: 20px 0">
      <template #default>
        <Icon name="iconfont icon-a236" color="#ccc" size="45" />
        <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
      </template>
      <template #file="{ file }">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
          <div style="height: 36px; display: flex">
            <Icon name="iconfont icon-excel" color="green" size="36" />
            <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
          </div>
          <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
        </div>
      </template>
    </el-upload>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          :loading="submitLoading"
          @click="
            visible = false;
            fileList = [];
          "
          >取消</el-button
        >
        <el-button type="primary" :loading="submitLoading" @click="uploadExcel">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import baseService from "@/service/baseService";
import { ElMessage, UploadUserFile } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const importUrl = ref("");
const fileList = ref(<any>[]);

const init = (url: any) => {
  visible.value = true;
  importUrl.value = url;
  fileList.value = [];
};

// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: UploadUserFile) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};

// 文件上传   确认导入按钮
const submitLoading = ref(false);
const uploadExcel = (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  submitLoading.value = true;
  baseService
    .post(importUrl.value, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      submitLoading.value = false;
      visible.value = false;
    });
};

defineExpose({
  init
});
</script>
<script lang="ts">
export default {
    name: 'NyImport'
}
</script>
