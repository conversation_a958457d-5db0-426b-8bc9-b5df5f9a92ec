<template>
  <div>
    <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
    <ny-table :showColSetting="false" :state="state" :columns="columns" routePath="/game/mapping/attribute" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <el-button type="primary" :loading="syncLoading" @click="handlesync()">自动同步</el-button>
        <el-button type="primary" @click="handleBatchSync()">批量同步</el-button>
        <el-button type="warning" @click="importShow = true">导入</el-button>
        <el-button type="primary" @click="exportHandle">导出</el-button>
        <el-button v-if="state.hasPermission('game:sysgame:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </template>
      <template #header-right>
        <el-input style="width: 280px !important" v-model="view.dataForm.attributeName" @change="state.getDataList()"  placeholder="请输入属性搜索" clearable :prefix-icon="Search"></el-input>
        <el-select style="width: 200px" @change="state.getDataList()" v-model="view.dataForm.outPname" clearable filterable>
          <el-option v-for="(item, index) in attributeList" :key="index" :label="item.name + (item.require ? ' (必填)' : '')" :value="item.name" />
        </el-select>
      </template>

      <!-- 当前平台 -->
      <template #currentPlatformName> {{ settingStore.info.seoPlatformName }} </template>

      <!-- 平台属性 -->
      <template #platformName="{ row, $index }">
        <el-select :remote-method="(query: any)=>{remoteMethod(query, row)}" @change="partnerAttributeNameFn(row)" v-model="row.platformName" clearable remote reserve-keyword filterable>
          <el-option v-for="(item, index) in externalAttribute" :key="index" :label="item.platformName" :value="item.platformName" />
        </el-select>
      </template>
      <!-- 是否默认 -->
      <template #defaultThis="{ row, $index }">
        <el-switch v-model="row.defaultThis" :active-value="true" :inactive-value="false" :loading="row.loading" @click="switchChange(row)" />
      </template>
    </ny-table>
    <!-- </el-card> -->

    <!-- 导入弹窗 -->
    <el-dialog v-model="importShow" class="ba-upload-preview" title="" @close="closeMoneyPreview" width="35%">
      <template #header>导入文件</template>
      <div>
        <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs">
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              importShow = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量同步 -->
    <batch-sync ref="batchSyncRef" :key="batchSyncKey" @change="handleBatchSyncChange"></batch-sync>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, defineExpose, defineProps, nextTick } from "vue";
import { Search } from "@element-plus/icons-vue";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import BatchSync from "./components/BatchSync.vue";
import { ElMessage } from "element-plus";
import { IObject } from "@/types/interface";
import { useSettingStore } from "@/store/setting";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const settingStore = useSettingStore();

const props = defineProps({
  gamesList: {
    type: Array,
    default: () => []
  }
});

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "partnerName",
    label: "合作商",
    minWidth: 100
  },
  {
    prop: "outPname",
    label: "合作商上级名称",
    minWidth: 120
  },
  {
    prop: "outName",
    label: "合作商游戏属性名称",
    minWidth: 120
  },
  {
    prop: "currentPlatformName",
    label: "当前平台",
    minWidth: 120
  },
  {
    prop: "platformPname",
    label: "平台游戏上级名称",
    minWidth: 120
  },
  {
    prop: "similar",
    label: "预览",
    minWidth: 120
  },
  {
    prop: "platformName",
    label: "平台属性",
    minWidth: 120
  },
  {
    prop: "defaultThis",
    label: "是否默认",
    minWidth: 100
  }
]);

const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/mapping/sysgameinfomapping/page",
  getDataListIsPage: true,
  deleteURL: "/mapping/sysgameinfomapping",
  deleteIsBatch: true,
  dataForm: {
    type: 4,
    partners: "",
    partnerId: "",
    gameId: "",
    outPname: "",
    attributeName: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const getList = (params: IObject) => {
  state.dataForm = {
    ...state.dataForm,
    ...params
  };
  state.getDataList();
};

const externalAttribute = ref(<any>[]); // 外部平台属性
// 搜索关联平台属性
const remoteMethod = (query: string, row: any) => {
  externalAttribute.value = [];
  if (query) {
    baseService
      .post("/mapping/sysgameinfomapping/getPlatformInfo", {
        mappingId: row.id,
        searchParam: query == null ? "" : query
      })
      .then((res) => {
        externalAttribute.value = res.data;
      });
  }
};

// 选择关联平台属性
const partnerAttributeNameFn = (row: any) => {
  let data = row;
  let itemdata: any = externalAttribute.value.find((item: any) => item.platformName == row.platformName);
  data.platformId = itemdata ? itemdata.platformId : "";
  baseService
    .put("/mapping/sysgameinfomapping", {
      id: row.id,
      platformId: data.platformId
    })
    .then((res) => {
      ElMessage.success("提交成功");
      state.query();
    });
};

const importShow = ref(false);
const fileList = ref([] as IObject);

// 关闭对话框
const closeMoneyPreview = () => {
  importShow.value = false;
};
// 数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};

// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: any) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};

// 文件上传   确认导入按钮
const uploadExcel = async (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  console.log(fileList.value, file);
  const url = `/mapping/sysgameinfomapping/import?type=${4}&gameId=${state.dataForm.gameId}&partnerId=${state.dataForm.partnerId}`;
  await baseService
    .post(url, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res: any) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      fileList.value = [];
      importShow.value = false;
      state.getDataList();
    });
};
const handlesync = () => {
  syncLoading.value = true;
  baseService
    .post("/mapping/sysgameinfomapping/sync", {
      type: 4,
      partnerId: state.dataForm.partnerId,
      gameId: state.dataForm.gameId
    })
    .then((res) => {
      ElMessage.success("自动同步成功！");
      state.getDataList();
      getAttributeData();
    })
    .finally(() => {
      syncLoading.value = false;
    });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  params.page = -1;
  params.type = 4;
  baseService.get("/mapping/sysgameinfomapping/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `属性映射`);
  });
};

// 同步
const syncLoading = ref(false);

// 自动同步总数量
const syncTotal = ref(0);

// 已同步数量
const synchronizedCount = ref(0);

const resetHandle = () => {
  view.dataForm.attribute = "";
  state.getDataList();
};
const changeDataForm = (partners: any, gameCode?: any) => {
  state.dataForm.partnerId = partners;
  state.dataForm.gameId = gameCode;
  getAttributeData();
  getList({});
};

const attributeList = ref();
const getAttributeData = () => {
  attributeList.value = [];
  baseService.get("/mapping/sysgameinfomapping/getPartnerAttrClass", { limit: 9999, partnerId: state.dataForm.partnerId, gameId: state.dataForm.gameId }).then((res) => {
    attributeList.value = res.data || [];
  });
};

// 点击批量同步
const batchSyncRef = ref();
const batchSyncKey = ref(0);
const handleBatchSync = () => {
  batchSyncKey.value++;
  nextTick(() => {
    batchSyncRef.value.init(props.gamesList);
  });
};

// 批量同步
const handleBatchSyncChange = () => {
  state.getDataList();
};

// 是否默认开关
const switchChange = (row: any) => {
  if (row.id) {
    row.loading = true;
    baseService
      .put("/mapping/sysgameinfomapping/setDefault", { id: row.id })
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
        state.query();
      })
      .finally(() => {
        row.loading = false;
      });
  }
};

defineExpose({
  getList,
  changeDataForm
});
</script>
