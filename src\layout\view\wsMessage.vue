<template>
    <div class="message-notification-dialog-content">
        <div class="message-list" v-loading="listLoading">
            <div class="top flx-justify-between">
                <div class="tabbar">
                    <div class="tabbar-item" v-for="(item, index) in msgStatusList" :key="index"
                        :class="{ active: item.value == currentStatus }" @click="currentStatusHandle(item.value)">
                        {{ item.label }}
                    </div>
                </div>

                <el-text v-show="currentStatus == 0 && messageListAll.length" class="pointer" type="primary"
                    @click="allReadHandle">全部已读</el-text>
                <el-text v-show="currentStatus == 1 && messageListAll.length" class="pointer" type="primary"
                    @click="emptiedHandle">清空</el-text>
            </div>
            <div style="height: calc(100% - 56px);overflow-y: auto;" ref="scrollbarRef" @scroll="handleScroll">
                <ny-no-data v-if="!messageListAll || !messageListAll.length" text="暂无数据"></ny-no-data>
                <div class="message-list-item flx" v-for="(item, index) in messageListAll" :key="index"
                    :class="{ active: item.id === currentMessageId }" @click="msgItemHandle(item.id)">
                    <el-image class="image" :src="msgNotificationRetrieve"></el-image>
                    <div class="item-right">
                        <el-badge is-dot :hidden="item.isRead == 1 ? true : false">
                            <div class="titlte">{{ item.typeName }}</div>
                        </el-badge>

                        <div class="desc sle">{{ filterHtml(item.messageInfo) }}</div>
                        <div class="time">{{ dayjs(item.createDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                    </div>
                </div>
            </div>

        </div>

        <div class="message-content" v-loading="detailLoading">
            <ny-no-data v-if="!msgDetail.id" type="2" text="请点击消息列表查看"></ny-no-data>

            <template v-else>
                <div class="content-top">
                    <div class="title">{{ msgDetail.typeName }}</div>
                    <div class="time">{{ dayjs(msgDetail.createDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>

                <el-scrollbar style="height: calc(100% - 124px)" scroll-y class="scrollbar">
                    <div class="message-text" v-html="msgDetail.messageInfo"></div>
                    <!-- <div class="message-button">
                        <el-button type="primary" @click="pzdsShow = true" v-if="msgDetail.type == 7">输入验证码</el-button>
                    </div> -->
                </el-scrollbar>

            </template>

        </div>
    </div>

    <!-- 消息全部已读确认 -->
    <secondary-confirmation :show="readConfirmationVisible" title="消息全部已读确认" confirmText="确认已读" type="primary"
        :showModal="false" :loading="btnLoading" @close="readConfirmationVisible = false" @confirm="readConfirmHandle">
        <template #default>
            <div class="text">
                <p>您是否确认要将所有未读消息都标记为已读？这个操作会将您的未读消息列表中所有的未读消息，一次性更新为已读状态。</p>
            </div>
        </template>
    </secondary-confirmation>

    <!-- 消息清空确认 -->
    <secondary-confirmation :show="emptiedVisible" title="消息清空确认" confirmText="确认清空" :showModal="false"
        :loading="btnLoading" @close="emptiedVisible = false" @confirm="emptiedConfirmHandle">
        <template #default>
            <div class="text">
                <p>您是否确认要执行删除所有消息的操作？这个操作将会永久移除您的所有消息记录，一旦删除将无法恢复。</p>
            </div>
        </template>
    </secondary-confirmation>

</template>

<script lang='ts' setup>
import { ref, reactive, computed, onUnmounted } from 'vue';
import baseService from '@/service/baseService';
import { messageNotificationType, filterHtml } from '@/utils/imTool';
import msgNotificationRetrieve from "@/assets/images/msg_notification_retrieve.png";
import SecondaryConfirmation from "@/views/order/components/SecondaryConfirmation.vue";
import { ElMessage } from 'element-plus';
import dayjs from "dayjs";

const messageList = ref(<any>[]);
const messageListAll = ref(<any>[]);
const listLoading = ref(false);
const currentStatus = ref(0);
const currentMessageId = ref();
const total = ref(0);
const queryParams = ref({
    page: 1,
    limit: 50,
    isRead: currentStatus.value
})

const msgStatusList = computed(() => {
    let unreadCount = messageListAll.value.filter((item: any) => item.isRead == 0).length;

    return [
        { label: '未读', value: 0, number: unreadCount },
        { label: '已读', value: 1, number: messageListAll.value.length - unreadCount },
    ]
});

const currentStatusHandle = (val: any) => {
    currentStatus.value = val;
    queryParams.value.page = 1;
    queryParams.value.isRead = val;
    messageListAll.value = [];
    getWebSocketMessageList();
}

// WS 消息内容
const getWebSocketMessageList = () => {
    baseService.get("/webSocket/message/page", queryParams.value).then(res => {
        if (res.code == 0) {
            messageListAll.value = messageListAll.value.concat(JSON.parse(JSON.stringify(res.data.list)));
            total.value = res.data.total
        }
    }).finally(() => {
        listLoading.value = false;
    })
}

// 查看详情
const msgDetail = ref(<any>{});
const detailLoading = ref(false);
const msgItemHandle = (id: number) => {
    currentMessageId.value = id;
    detailLoading.value = true;
    baseService.get('/webSocket/message/' + id).then(res => {
        if (res.code == 0) {
            msgDetail.value = res.data;
            if(msgDetail.value.type == 7){
                pzdsForm.value = JSON.parse(msgDetail.value.other)
            }
            readHandle();
        }
    }).finally(() => {
        detailLoading.value = false;
    })
}


// 消息已读
const readHandle = () => {
    baseService.put("/webSocket/message/read/" + currentMessageId.value).then(res => {
        if (res.code == 0) {
            let index = messageListAll.value.findIndex((item: any) => item.id == currentMessageId.value);
            if (!messageListAll.value[index].isRead) {
                messageListAll.value[index].isRead = 1;
            }
        }
    })
}

// 滚动事件处理
const scrollbarRef = ref()
const handleScroll = () => {
    if (!scrollbarRef.value) return
    const container = scrollbarRef.value;
    // 滚动条距离底部小于等于1px时认为触底
    if (container.scrollHeight - container.scrollTop <= container.clientHeight + 1) {
        // console.log('触底了');

        // 在这里加载更多数据或执行其他操作
        if (total.value > messageListAll.value.length) {
            queryParams.value.page++;
            // console.log(messageListAll.value,'===== console.log(messageListAll.value.length); ======');
            getWebSocketMessageList();
        }
    }
}

getWebSocketMessageList();

// 消息全部已读
const readConfirmationVisible = ref(false);
const btnLoading = ref(false);
const readConfirmHandle = () => {
    btnLoading.value = true;
    baseService.put('/webSocket/message/allRead').then(res => {
        if (res.code == 0) {
            queryParams.value.page = 1;
            messageListAll.value = [];
            getWebSocketMessageList();
            ElMessage.success('全部已读操作成功');
            readConfirmationVisible.value = false;
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}

const allReadHandle = () => {
    readConfirmationVisible.value = true;
}

// 清空
const emptiedVisible = ref(false);
const emptiedHandle = () => {
    emptiedVisible.value = true;
}
const emptiedConfirmHandle = () => {
    btnLoading.value = true;
    baseService.delete('/webSocket/message/clean').then(res => {
        if (res.code == 0) {
            queryParams.value.page = 1;
            messageListAll.value = [];
            getWebSocketMessageList();
            currentMessageId.value = '';
            msgDetail.value = {};
            emptiedVisible.value = false;
            ElMessage.success('清空操作成功');
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}


</script>

<style lang='less' scoped>
.message-notification-dialog-content {
    height: 100%;
    background: #F2F4F7;
    padding: 12px;
    display: flex;

    .message-list {
        height: 100%;
        width: 372px;
        background: #fff;
        border-radius: 4px;

        .top {
            height: 56px;
            padding: 0 20px;
            border-bottom: 1px solid #E5E6EB;

            .tabbar {
                display: flex;

                .tabbar-item {
                    height: 32px;
                    line-height: 32px;
                    border-radius: 100px;
                    color: #4E5969;
                    padding: 0 16px;
                    margin-right: 10px;
                    cursor: pointer;

                    &.active {
                        background: #F0F2F5;
                        color: var(--el-color-primary);
                    }
                }
            }
        }

        .message-list-item {
            padding: 14px 20px;
            border-bottom: 1px solid #E5E6EB;
            cursor: pointer;

            &:hover,
            &.active {
                background-color: #f9f9f9;
            }

            &:last-child {
                border-bottom: none;
            }

            .image {
                width: 40px;
                height: 40px;
            }

            .item-right {
                width: calc(100% - 56px);
                margin-left: 16px;

                .el-badge {
                    width: 100%;
                }

                .titlte {
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 22px;
                }

                .desc {
                    line-height: 22px;
                    color: #606266;
                    padding-top: 2px;
                }

                .time {
                    color: #909399;
                    line-height: 22px;
                    padding-top: 6px;
                }
            }
        }

        .scrollbar,
        .el-scrollbar__view {
            height: 100%;
        }
    }

    .message-content {
        flex: 1;
        height: 100%;
        background: #fff;
        margin-left: 12px;
        border-radius: 4px;
        padding: 20px;

        .message-text {
            line-height: 22px;
        }
        .message-button{
            width: 100%;
            margin-top: 40px;
        }

        .content-top {
            height: 72px;
            border-bottom: 1px solid #EBEEF5;

            .title {
                font-size: 18px;
                line-height: 26px;
            }

            .time {
                color: #909399;
                line-height: 22px;
                padding-top: 4px;
            }
        }

        .scrollbar {
            margin: 12px 0;
        }
    }
}
</style>