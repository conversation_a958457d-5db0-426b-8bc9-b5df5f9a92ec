<template>
    <div class="container">
        <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
            <!-- 表单布局 -->
            <ny-form-slot>
                <template v-slot:content>
                    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
                        <el-form-item>
                            <ny-select-search v-model="state.dataForm.gameCode"
                                labelKey="name"
                                valueKey="code"
                                url="/game/queryUserList"
                                :param="{limit:9999}"
                                placeholder="选择游戏"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="state.dataForm.companyName" placeholder="商户名称" clearable></el-input>
                        </el-form-item>
                    </el-form>
                </template>
                <template v-slot:button>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>
            </ny-form-slot>
        </el-card>

        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                :showColSetting="false"
                @selectionChange="state.dataListSelectionChangeHandle"
            >   
                <template #header>
                    <el-button v-if="state.hasPermission('customized:property:save')" type="primary" @click="productPush">商品推送</el-button>
                    <el-button v-if="state.hasPermission('customized:property:delete')" type="warning" @click="selectedWebsite">选号网址</el-button>
                </template>

                <!-- 账号排序 -->
                <template #shopSortHeader>
                    <div class="flx-center">
                        <span class="mr-5">账号排序</span>
                        <el-tooltip effect="dark" content="已勾选的数字越小，在此列表中的展示越靠前。如果数字相同，按照上架时间进行排序展示。未勾选的输入数字无效。"
                            placement="top-start">
                            <el-icon><Warning /></el-icon>
                        </el-tooltip>
                    </div>
                </template>
                <template #shopSort="{row}">
                    <el-input-number
                        :controls="false"
                        v-model="row.shopSort"
                        :show-input="true"
                        class="w-100"
                        placeholder="请输入序号"
                        type="number"
                        :min="1"
                    >
                    </el-input-number>
                </template>

                <!-- 平台类型 -->
                <template #companyType="{row}">
                    {{ state.getDictLabel("api_platform_type", row.companyType) }}
                </template>
                
                <!-- 接收加价倍率 -->
                <template #increaseRatio="{row}">
                    <div class="flx-center">
                        <el-input-number :controls="false" v-model="row.increaseRatio" :show-input="true" class="flx-1"
                            placeholder="请输入接受加价倍率" type="number" :min="0">
                        </el-input-number>
                        <span class="ml-10">%</span>
                    </div>
                </template>

                <!-- 过期时间 -->
                <template #expireTerm="{row}">
                    <div class="flx-center">
                        <el-input-number :controls="false" v-model="row.expireTerm" :show-input="true" class="flx-1"
                            placeholder="请输入过期时间（天）" type="number" :min="0">
                        </el-input-number>
                        <span class="ml-10">天</span>
                    </div>
                </template>

                <!-- 是否在商城展示 -->
                <template #showed="{row}">
                    <el-switch v-model="row.showed" />
                </template>

                <template #footer v-if="state.dataListSelections && state.dataListSelections.length">
                    <el-button type="primary" @click="batchSave">保存</el-button>
                </template>
                
                
            </ny-table>
        </el-card>

        <!-- 推送 -->
        <partnergame-push ref="partnergamePushRef" :key="partnergameKey"></partnergame-push>

        <!-- 选号网址 -->
        <partnergame-selected-website ref="partnergameSelectedWebsiteRef" :key="partnergameSelectedWebsiteKey"></partnergame-selected-website>

    </div>
</template>

<script lang="ts" setup>
    import { ref, reactive, toRefs, nextTick } from 'vue';
    import { Warning } from  '@element-plus/icons-vue';
    import useView from "@/hooks/useView";
    import PartnergamePush from './partnergame-push.vue';
    import PartnergameSelectedWebsite from './partnergame-selected-website.vue';
    import baseService from '@/service/baseService';
    import { ElMessage } from 'element-plus';

    const columns = reactive([
        {
            type: "selection",
            width: 50
        },
        {
            prop: "shopSort",
            label: "账号排序",
            minWidth: 180
        },
        {
            prop: "companyType",
            label: "平台类型",
            minWidth: 100
        },
        {
            prop: "companyName",
            label: "商户名",
            minWidth: 100
        },
        {
            prop: "increaseRatio",
            label: "接受加价倍率",
            minWidth: 180
        },
        {
            prop: "expireTerm",
            label: "过期时间",
            minWidth: 180
        },
        {
            prop: "shopCount",
            label: "商品数量",
            minWidth: 80
        },
        {
            prop: "showed",
            label: "是否在商城展示",
            minWidth: 130
        },
        {
            prop: "checkTime",
            label: "勾选时间",
            minWidth: 100
        }
    ])

    const view = reactive({
        getDataListURL: "/paid/partnergame/page",
        getDataListIsPage: true,
        dataForm: {
            // 游戏code
            gameCode: "",
            // 商户名称
            companyName: ""
        },
    });

    const state = reactive({ ...useView(view,), ...toRefs(view) });

    // 重置
    const getResetting = () => {
        state.dataForm.gameCode = "";
        state.dataForm.companyName = "";
        state.getDataList();
    }

    // 商品推送
    const partnergamePushRef = ref();
    const partnergameKey = ref(0);
    const productPush = () => {
        partnergameKey.value++;
        nextTick(() => {
            partnergamePushRef.value.init();
        })
    }

    // 选号网址
    const partnergameSelectedWebsiteRef = ref();
    const partnergameSelectedWebsiteKey = ref(0);
    const selectedWebsite = () => {
        partnergameSelectedWebsiteKey.value++;
        nextTick(() => {
            partnergameSelectedWebsiteRef.value.init();
        })
    }

    // 保存
    const batchSave = async () => {
        let jsons = JSON.parse(JSON.stringify(state.dataListSelections));
        let jsonRes: any = [];
        jsons.forEach((ele: any) => {
            jsonRes.push({
                companyName: ele.companyName,
                gameCode: ele.gameCode,
                id: ele.id,
                increaseRatio: Number(ele.increaseRatio / 100).toFixed(2),
                //
                pushMagnification: Number(ele.pushMagnification/ 100).toFixed(2),
                pullMagnification: Number(ele.pullMagnification / 100).toFixed(2),
                expireTerm: ele.expireTerm,
                shopSort: ele.shopSort,
                userId: ele.userId,
                isPush:ele.isPush
            })
        })
    
        const res = await baseService.post("/paid/partnergame/batchSave", state.dataList);
        if(res.code == 0){
            ElMessage.success("保存成功");
            state.getDataList();
        }
    }
    
</script>