<template>
  <el-drawer v-model="visible" :title="'商品售出分期审核'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="994" class="ny-drawer">
    <el-card class="cardDescriptions" style="padding: 0">
      <div class="titleSty">商品信息</div>
      <el-descriptions :column="2" border class="descriptions">
        <el-descriptions-item>
          <template #label>
            <span>游戏名称</span>
          </template>
          <div>{{ dataInfo.gameName }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>游戏账号</span>
          </template>
          <div>{{ dataInfo.gameAccount }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>商品编码</span>
          </template>
          <div>{{ dataInfo.shopCode }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>零售价#(元)</span>
          </template>
          <div>{{ dataInfo.transactionPrice }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>出售人</span>
          </template>
          <div>{{ dataInfo.sellerName }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>出售渠道</span>
          </template>
          <div>{{ dataInfo.sellerChannelName }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>买方手机号</span>
          </template>
          <div>{{ dataInfo.buyerPhone }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>成交价(元)</span>
          </template>
          <div>{{ dataInfo.transactionPrice }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>包赔费(元)</span>
          </template>
          <div>{{ dataInfo.compensationFee }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>手续费#(元)</span>
          </template>
          <div>{{ dataInfo.monthRepaymentCommission }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card class="cardDescriptions" style="padding: 0; margin-top: 12px">
      <div class="titleSty">分期信息</div>
      <el-descriptions :column="2" border class="descriptions">
        <el-descriptions-item>
          <template #label>
            <span>首付款(元)</span>
          </template>
          <div>{{ dataInfo.downPayments }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>分期金额</span>
          </template>
          <div>{{ dataInfo.byStagesMoney }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>分期期数</span>
          </template>
          <div>{{ dataInfo.byStagesPeriods }}个月</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>费率</span>
          </template>
          <div>{{ dataInfo.byStagesRate }}%</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>每月应还金额(元)</span>
          </template>
          <div>{{ dataInfo.monthRepaymentAmount }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>手续费总额(元)</span>
          </template>
          <div>{{ dataInfo.commissionTotal }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>还款日</span>
          </template>
          <div>每月{{ dataInfo.repaymentDay }}号</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>第三方订单编号</span>
          </template>
          <div>{{ dataInfo.thirdOrderNumber }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>付款方式</span>
          </template>
          <div>
            {{ ["支付宝转账", "微信", "银行卡", "淘宝"][dataInfo.payType - 1] }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="dataInfo.payType == 1">
          <template #label>
            <span>支付宝订单号</span>
          </template>
          <div>{{ dataInfo.alipayOrderNo }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <span>首付款凭证</span>
          </template>
          <div>
            <el-image style="height: 68px; width: 68px;border-radius: 4px;" :src="dataInfo.payCredentials" :preview-src-list="[dataInfo.payCredentials]" preview-teleported fit="cover" v-if="dataInfo.payCredentials"/>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin-top: 12px; display: flex; justify-content: flex-end" v-if="dataInfo.autoReconciliationStatus">
        <el-tag :type="dataInfo.autoReconciliationStatus == 1 ? 'success' : 'danger'" size="large">
          <div style="display: flex; align-items: center; gap: 9px">
            <el-icon v-if="dataInfo.autoReconciliationStatus == 1"><CircleCheckFilled /></el-icon>
            <el-icon v-else><CircleCloseFilled /></el-icon>
            <span style="font-weight: 500; font-size: 14px">{{ dataInfo.autoReconciliationStatus == 1 ? "对账成功" : "对账异常" }}</span>
          </div>
        </el-tag>
      </div>
    </el-card>
    <el-card class="cardDescriptions" style="padding: 0; margin-top: 12px">
      <div class="titleSty">审核操作</div>
      <div class="shop_page_basic">
        <!-- 商品信息 -->
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top" style="padding: 0px">
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="审核操作" prop="state">
                <el-radio-group v-model="dataForm.state">
                  <el-radio value="1" :disabled="dataInfo.autoReconciliationStatus == 2">审核通过</el-radio>
                  <el-radio value="2" :disabled="dataInfo.autoReconciliationStatus == 1">审核拒绝</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="dataForm.state == '2'">
              <el-form-item label="拒绝原因" prop="auditRemark">
                <el-input maxlength="100" show-word-limit type="textarea" v-model="dataForm.auditRemark" placeholder="请输入拒绝原因" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    
    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
const emits = defineEmits(["refresh"]);
const visible = ref(false);
const dataFormRef = ref(); // 表单ref
const dataInfo = ref(<any>{});
const dataForm = reactive({
  id:'',
  state: "",
  auditRemark: ""
});
const rules = ref({
  // 表单必填项
  state: [{ required: true, message: "请选择审核操作", trigger: "change" }],
  auditRemark: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }]
});

// 总表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    baseService.put("/sale/saleOrderByStages/audit",dataForm).then(res=>{
      if(res.code == 0){
        ElMessage.success("审核操作成功");
        visible.value = false;
        emits("refresh");
      }
    }).finally(()=>{
      btnLoading.value = false;
    })
  });
};

const init = (row:any) => {
  visible.value = true;
  // console.log(row);
  
  dataInfo.value = row;
  dataForm.id = row.id
  dataForm.state = "";
  dataForm.auditRemark = "";
};

defineExpose({
  init
});
</script>

<style lang="less" scoped></style>
