<template>
    <div class="form_solt" :class="{ 'align-right': alignRight }">
        <div class="form_solt_content" :style="expandStart ? '' :'height:50px'" ref="formSolt">
            <slot name="content"></slot>
        </div>
        <div class="form_solt_switch" v-if="isExpand">
            <el-text class="mx-1" type="primary" @click="expandStart = !expandStart">{{ expandStart ? '收起' :'展开'}}</el-text>
        </div>
        <div class="form_solt_right">
            <slot name="button"></slot>
        </div>

    </div>
</template>
<script lang="ts">
import { onMounted, defineComponent,ref, watch } from "vue";
export default defineComponent({
    name: "NyFormSlot",
    props: {
        isExpand: Boolean,  // 是否显示展开按钮
        alignRight: Boolean  // 是否对齐右侧
    },
    setup(props) {
        const expandStart = ref(true);

        watch(() => props.isExpand,
            (newValue) => {
                if (newValue) {
                    expandStart.value = false
                }
            }
        )
        return {expandStart}
    }
});
</script>
<style lang="less" scoped>
.form_solt{
    display: flex;
    align-items: flex-start;

    &.align-right{
        justify-content: flex-end;
    }
    
    .form_solt_content{
        max-width: calc(100% - 200px);
        overflow: hidden;

        :deep(.el-input--suffix){
            width: 200px !important;
        }
    }
    .form_solt_switch{
        padding: 8px 14px;
        cursor: pointer;
    }
    .form_solt_right{
        margin-bottom: 18px;
    }
    
}
</style>