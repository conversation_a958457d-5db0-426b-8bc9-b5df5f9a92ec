// src/utils/websocket.ts
import { ref, onUnmounted } from 'vue'

interface WebSocketOptions {
  url: string
  reconnectLimit?: number
  reconnectInterval?: number
  heartInterval?: number
  heartMessage?: string
}

export default function useWebSocket(options: WebSocketOptions) {
  const {
    url,
    reconnectLimit = 5,
    reconnectInterval = 5000,
    heartInterval = 20000, // 心跳间隔20秒
    heartMessage = 'ping'
  } = options

  // 响应式数据
  const message = ref<any>(null)
  const status = ref<'connecting' | 'open' | 'closed' | 'error'>('connecting')
  const reconnectCount = ref(0)
  
  // WebSocket实例和定时器
  let ws: WebSocket | null = null
  let heartTimer: NodeJS.Timeout | null = null
  let serverTimer: NodeJS.Timeout | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // 心跳检测
  const startHeartBeat = () => {
    // console.log('[WebSocket] 开始心跳检测')
    clearHeartBeat() // 先清理旧定时器
    
    heartTimer = setInterval(() => {
      if (ws?.readyState === WebSocket.OPEN) {
        // console.log('[WebSocket] 发送心跳:', heartMessage)
        send(heartMessage)
        
        // 服务器响应超时检测（30秒未响应则重连）
        serverTimer = setTimeout(() => {
          // console.error('[WebSocket] 服务器心跳响应超时，触发重连')
          reconnect()
        }, 30000) // 比心跳间隔长10秒
      }
    }, heartInterval)
  }

  // 清除心跳和超时检测
  const clearHeartBeat = () => {
    // console.log('[WebSocket] 清除心跳定时器')
    heartTimer && clearInterval(heartTimer)
    serverTimer && clearTimeout(serverTimer)
    heartTimer = null
    serverTimer = null
  }

  // 重连机制
  const reconnect = () => {
    if (reconnectCount.value >= reconnectLimit) {
      // console.error(`[WebSocket] 重连次数已达上限 (${reconnectLimit})，停止重连`)
      close()
      return
    }

    reconnectCount.value++
    // console.log(`[WebSocket] 尝试重连 (${reconnectCount.value}/${reconnectLimit})`)
    
    status.value = 'connecting'
    reconnectTimer = setTimeout(() => {
      createWebSocket()
    }, reconnectInterval)
  }

  // 发送消息
  const send = (data: string | ArrayBuffer | Blob) => {
    if (ws?.readyState === WebSocket.OPEN) {
      ws.send(data)
    } else {
      console.error('[WebSocket] 发送失败：连接未就绪')
    }
  }

  // 创建连接
  const createWebSocket = () => {
    if (ws) {
      ws.close() // 关闭旧连接
      ws = null
    }

    // console.log('[WebSocket] 创建连接:', url)
    try {
      ws = new WebSocket(url)
      
      ws.onopen = () => {
        console.log('[WebSocket] 连接已建立')
        status.value = 'open'
        reconnectCount.value = 0
        startHeartBeat()
      }

      ws.onmessage = (e: MessageEvent) => {
        // console.log('[WebSocket] 收到消息:', JSON.parse(e.data))
        // 任何消息都会重置服务器超时检测
        if (serverTimer) {
          clearTimeout(serverTimer)
          serverTimer = null
        }

        if(JSON.parse(e.data).code === 200){
          // console.log('[WebSocket] 连接成功');
          return
        }
        
        if (JSON.parse(e.data).type === 'pong') {
          // console.log('[WebSocket] 收到心跳响应');
          return
        }
        
        message.value = e.data
      }

      ws.onclose = (e) => {
        console.log(`[WebSocket] 连接关闭，代码: ${e.code}`)
        status.value = 'closed'
        clearHeartBeat()
        
        // 非手动关闭且需要重连时触发
        if (e.code !== 1000 && reconnectCount.value < reconnectLimit) {
          reconnect()
        }
      }

      ws.onerror = (e: Event) => {
        // console.error('[WebSocket] 连接错误:', e)
        status.value = 'error'
        ws?.close() // 触发onclose
      }

    } catch (error) {
      // console.error('[WebSocket] 创建连接失败:', error)
      reconnect()
    }
  }

  // 主动关闭
  const close = () => {
    console.log('[WebSocket] 手动关闭连接')
    clearHeartBeat()
    reconnectTimer && clearTimeout(reconnectTimer)
    reconnectCount.value = reconnectLimit // 停止自动重连
    ws?.close(1000, 'manual close')
    ws = null
  }

  // 组件卸载时关闭
  onUnmounted(() => {
    close()
  })

  // 初始化连接
  createWebSocket()

  return {
    message,
    status,
    send,
    close
  }
}