<template>
    <el-dialog v-model="visible"  width="80%" class="dialog_page" style="padding: 0px;" top="5vh" @close="dialogClost">
        <template #header="{ close, titleId, titleClass }">
            <div class="dialog_header">图表</div>
        </template>
        <div class="chart_page">
            <div class="chart">
                <div style="width: 100%; height: 100%" ref="PreviewChartRefs"></div>
            </div>
            <div class="config_page">
                <div style="border-bottom: 2px solid #E4E7ED;margin-bottom: -2px;">
                    <el-tabs v-model="activeName" class="demo-tabs">
                        <el-tab-pane label="类型与数据" name="1"></el-tab-pane>
                        <el-tab-pane label="自定义样式" name="2"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="config_form" v-if="activeName == '1'">
                    <el-form :model="dataForm" label-position="top" :rules="rules" ref="dataFormRef">
                        <el-form-item label="统计图名称" prop="name">
                            <el-input v-model="dataForm.name" placeholder="请输入统计图名称"/>
                        </el-form-item>
                        <el-form-item label="图表类型" prop="type">
                            <el-popover placement="bottom" :width="292" trigger="click">
                                <template #reference>
                                    <div class="selectFrame">
                                        <div class="typeName" v-if="dataForm.type == 'option1'">基础柱状图</div>
                                        <div class="typeName" v-if="dataForm.type == 'option2'">堆积柱状图</div>
                                        <div class="typeName" v-if="dataForm.type == 'option3'">基础折线图</div>
                                        <div class="typeName" v-if="dataForm.type == 'option4'">平滑折线图</div>
                                        <div class="typeName" v-if="dataForm.type == 'option5'">饼图</div>
                                        <div class="typeName" v-if="dataForm.type == 'option6'">环形图</div>
                                    </div>
                                </template>
                                <div class="chartsPage">
                                    <div class="chartsCard">
                                        <div class="title">柱状图</div>
                                        <div class="chartsLine">
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option1'}" @click="SwitchChart('option1')">
                                                <img src="/src/assets/images/stat1.png">
                                                <span>基础柱状图</span>
                                            </div>
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option2'}" @click="SwitchChart('option2')">
                                                <img src="/src/assets/images/stat2.png">
                                                <span>堆积柱状图</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chartsCard">
                                        <div class="title">折线图</div>
                                        <div class="chartsLine">
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option3'}" @click="SwitchChart('option3')">
                                                <img src="/src/assets/images/stat3.png">
                                                <span>基础折线图</span>
                                            </div>
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option4'}" @click="SwitchChart('option4')">
                                                <img src="/src/assets/images/stat4.png">
                                                <span>平滑折线图</span>
                                            </div>
                                        </div>
                                        
                                    </div>
                                    <div class="chartsCard">
                                        <div class="title">饼图</div>
                                        <div class="chartsLine">
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option5'}" @click="SwitchChart('option5')">
                                                <img src="/src/assets/images/stat5.png">
                                                <span>饼图</span>
                                            </div>
                                            <div class="chartsInfo" :class="{active:dataForm.type == 'option6'}" @click="SwitchChart('option6')">
                                                <img src="/src/assets/images/stat6.png">
                                                <span>环形图</span>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                            </el-popover>
                        </el-form-item>
                        <el-form-item label="主题色" prop="themeColor">
                            <el-popover placement="bottom" :width="292" trigger="click">
                                <template #reference>
                                    <div class="selectFrame">
                                        <div style="display: flex;">
                                            <div v-for="(e,i) in ThemeColor.find((item:any) => item.name == dataForm.ThemeColor)?.value" class="colorItem" :style="`background-color:${e.color}`"></div>
                                        </div>
                                    </div>
                                </template>
                                <template v-for="item in ThemeColor">
                                    <div class="colorLine" style="padding: 8px;" @click="ThemeColorChange(item.name,item.value)">
                                        <div v-for="(e,i) in item.value" class="colorItem" :style="`background-color:${e.color}`"></div>
                                    </div>
                                </template>
                            </el-popover>
                        </el-form-item>
                        <el-form-item label="图表选项">
                            <el-checkbox label="图例" v-model="dataForm.legendShow" @change="legendChange"/>
                            <el-checkbox label="数据标签" v-model="dataForm.labelShow" @change="labelChange"/>
                        </el-form-item>
                        <el-form-item label="分组（Y轴）" prop="yzColumns" v-if="dataForm.type != 'option5' && dataForm.type != 'option6'">
                            <el-select v-model="dataForm.yzColumns" placeholder="请选择字段（可多选）" multiple @change="getEchartsData" @remove-tag="getEchartsData">
                                <el-option
                                    v-for="(ele,ind) in yAxisList" 
                                    :key="ind" 
                                    :label="`${ele.name}`" 
                                    :value="ele.dynamicKey"
                                    :disabled="dataForm.yzColumns.length == 1 && ele.dynamicKey == dataForm.yzColumns[0]"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分组（Y轴）" prop="yzColumnsSingle" v-else>
                            <el-select v-model="dataForm.yzColumnsSingle" placeholder="请选择字段" @change="getEchartsData" @remove-tag="getEchartsData">
                                <el-option
                                    v-for="(ele,ind) in yAxisList" 
                                    :key="ind" 
                                    :label="`${ele.name}`" 
                                    :value="ele.dynamicKey"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="排序依据" prop="sortType">
                            <ny-button-group
                                :list="[
                                    { dictLabel: '横轴值', dictValue: 1 },
                                    { dictLabel: '纵轴值', dictValue: 2 },
                                ]"
                                v-model="dataForm.sortType"
                                @change="getEchartsData"
                                style="margin: 8px 0px"
                            ></ny-button-group>
                        </el-form-item>
                        <el-form-item label="排序规则" prop="isAsc">
                            <ny-button-group
                                :list="[
                                    { dictLabel: '正序', dictValue: true },
                                    { dictLabel: '倒序', dictValue: false },
                                ]"
                                v-model="dataForm.isAsc"
                                @change="getEchartsData"
                                style="margin: 8px 0px"
                            ></ny-button-group>
                        </el-form-item>
                        <el-form-item label="数据（X轴）" prop="xzColumn">
                            <!-- <el-tree-select
                                v-model="dataForm.xzColumn"
                                :data="xAxisList"
                                :props="{ label: 'name', value: 'dynamicKey' }"
                            /> -->
                            <el-select v-model="dataForm.xzColumn" placeholder="请选择字段"  @change="getEchartsData">
                                <el-option
                                    v-for="(ele,ind) in xAxisList" 
                                    :key="ind" 
                                    :label="`${ele.name}`" 
                                    :value="ele.dynamicKey"
                                />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="config_form" v-if="activeName == '2'">
                    <el-form :model="dataForm" label-position="top" :rules="rules" ref="dataFormRef">
                        <el-form-item label="图表宽度" prop="chartWitch">
                            <el-select v-model="dataForm.chartWitch" placeholder="请选择">
                                <el-option label="1/3" value="1" />
                                <el-option label="2/3" value="2" />
                                <el-option label="3/3" value="3" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="图列位置" prop="position">
                            <el-select v-model="dataForm.position" placeholder="请选择" @change="positionChange">
                                <el-option label="顶部" value="top" />
                                <el-option label="底部" value="bottom" />
                                <!-- <el-option label="左边" value="left" />
                                <el-option label="右边" value="right" /> -->
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="config_but">
                    <el-button type="primary" @click="submit">确定</el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue';
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import * as echarts from "echarts";
import { ThemeColor, option1, option2, option3, option4, option5, option6 } from './chartConfig';

const emit = defineEmits(["refreshDataList"]);
interface Props {
  menuInfo: any
}
const props = withDefaults(defineProps<Props>(), {
  menuInfo:''
})

const PreviewChartRefs = ref(null); // 图表预览
const charts = ref(<any>[]);
const dataFormRef = ref();
const visible = ref(false);
const activeName = ref('1');
const colorValue = ref(<any>[]);
const dataForm = ref({
    id: '', // 数据id
    indexName: '',  // 报表索引名称
    name: '',   // 统计图名称
    type: '',   // 统计图类型
    setting: <any>'',    // 统计图表样式
    themeColor: '', // 主题色
    chartType: '',  
    yzColumnsSingle: '', // Y轴单选(饼图)
    yzColumns: <any>[],  // Y轴
    xzColumn: '',   // X轴
    sortType: 1,    // 排序依据
    isAsc: true,    // 排序规则
    chartWitch: '1',
    position: 'top',   // 图例位置
    labelShow: false, // 数据标签
    legendShow: true, // 图例
    ThemeColor: '主题1', // 主题色
})

const rules = ref({
    name: [{ required: true, message: '请输入统计图名称', trigger: 'blur' },],
});

// 切换图表
const SwitchChart = (value:any) =>{
    charts.value = [];
    dataForm.value.type = value;
    colorValue.value = ThemeColor[0].value;
    if(value == 'option5' || value == 'option6'){
        dataForm.value.yzColumns = dataForm.value.yzColumns.slice(0,1);
        dataForm.value.yzColumnsSingle = dataForm.value.yzColumns[0];
    }else{
        dataForm.value.yzColumns = Array.isArray(dataForm.value.yzColumns) ? dataForm.value.yzColumns : [dataForm.value.yzColumnsSingle]
    }
    
    getEchartsData();
}

// 配置图表
const PreviewChart = (optionValue:any) =>{
    const userGrowthChart = echarts.init(PreviewChartRefs.value);
    const option = optionValue;
    userGrowthChart.setOption(option,{ notMerge: true });
    charts.value.push(userGrowthChart);
}

// 对话框关闭回调
const dialogClost = () =>{
    // 销毁echarts实例
    // charts.value[0].dispose();
    dataFormRef.value.resetFields();
    dataForm.value.id = '';
}

// 获取表格字段
const xAxisList = ref(<any>[]);
const yAxisList = ref(<any>[]);
const getfieldList = () =>{
    dataForm.value.indexName = props.menuInfo.indexName;
    baseService.get('/report/reportcolumn/all',{indexName:props.menuInfo.indexName}).then(res=>{
        yAxisList.value = res.data.filter((item:any) => item.type == 2 );
        xAxisList.value = res.data.filter((item:any) => (item.type == 1 || item.type == 3 || item.type == 4) );
        // 临时调试树桩选择框数据
        xAxisList.value.map((item:any)=>{
            if(item.type == 3){
                item.children = [];
                item.options.split(',').map((e:any)=>{
                    item.children.push({
                        dynamicKey: e,
                        name: e
                    })
                })
            }
        })
        if(xAxisList.value.length && yAxisList.value.length){
            if(!dataForm.value.id){
                dataForm.value.xzColumn = xAxisList.value[0].dynamicKey;
                dataForm.value.yzColumns.push(yAxisList.value[0].dynamicKey);
                dataForm.value.yzColumnsSingle = yAxisList.value[0].dynamicKey;
            }
            
        }else{
            ElMessage.warning('当前表格字段，不满足图表数据分析！请完善后在进行图表分析；');
            return
        }
        getEchartsData();
    })
}

// 获取图表数据
const getEchartsData = () =>{
    if(dataForm.value.type == 'option5' || dataForm.value.type == 'option6'){
        dataForm.value.yzColumns = dataForm.value.yzColumnsSingle;
    }
    const params = JSON.parse(JSON.stringify(dataForm.value));
    params.yzColumns = (params.type == 'option5' || params.type == 'option6') ? params.yzColumnsSingle : Array.isArray(params.yzColumns) ? params.yzColumns.join(',') : params.yzColumns;
    baseService.post('/report/reportchart/preview',params).then(res=>{
        if(res.code == 0){
            handleData(res.data)
        }
    })
}

// 切换主题色
const ThemeColorChange = (name:string,item:any) =>{
    dataForm.value.ThemeColor = name;
    colorValue.value = item;
    switch (dataForm.value.type) {
        case "option1":
            option1.color = item.map((e:any) => e.color)
            PreviewChart(option1);
            break;
        case "option2":
            option2.color = item.map((e:any) => e.color)
            PreviewChart(option2);
            break;
        case "option3":
            option3.color = item.map((e:any) => e.color)
            PreviewChart(option3);
            break;
        case "option4":
            option4.color = item.map((e:any) => e.color)
            PreviewChart(option4);
            break;
        case "option5":
            option5.color = item.map((e:any) => e.color)
            PreviewChart(option5);
            break;
        case "option6":
            option6.color = item.map((e:any) => e.color)
            PreviewChart(option6);
            break;
    }
}


// 处理图表数据结构
const handleData = (data:any) =>{
    switch (dataForm.value.type) {
        case "option1":
            option1.xAxis.data = data.other.xdata;
            option1.yAxis.axisLabel = {
                formatter: function(value:any) {
                    // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
                    if (value >= 10000) {
                        return (value / 10000).toFixed(1) + '万';
                    }
                    return value;
                }
            }
            option1.series = [];
            data.other.ydata.map((e:any)=>{
                option1.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'bar',
                    label:{
                        show:dataForm.value.labelShow,
                        position: 'top',
                    }
                })
            })
            PreviewChart(option1);
            break;
        case "option2":
            option2.xAxis.data = data.other.xdata;
            option2.yAxis.axisLabel = {
                formatter: function(value:any) {
                    // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
                    if (value >= 10000) {
                        return (value / 10000).toFixed(1) + '万';
                    }
                    return value;
                }
            }
            option2.series = [];
            data.other.ydata.map((e:any)=>{
                option2.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'bar',
                    stack: 'Ad', // 堆叠
                    label:{
                        show:dataForm.value.labelShow,
                        position: 'top',
                    }
                })
            })
            PreviewChart(option2);
            break;
        case "option3":
            option3.xAxis.data = data.other.xdata;
            option3.yAxis.axisLabel = {
                formatter: function(value:any) {
                    // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
                    if (value >= 10000) {
                        return (value / 10000).toFixed(1) + '万';
                    }
                    return value;
                }
            }
            option3.series = [];
            option3.legend.data = [];
            data.other.ydata.map((e:any)=>{
                option3.legend.data.push(e.name)
                option3.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'line',
                    stack: 'Total',
                    label: {
                        show: dataForm.value.labelShow,
                        position: 'top'
                    },
                })
            })
            PreviewChart(option3);
            break;
        case "option4":
            option4.xAxis.data = data.other.xdata;
            option4.yAxis.axisLabel = {
                formatter: function(value:any) {
                    // 如果数值大于等于 10000 则除以 10000，并在数值后面加上 '万'
                    if (value >= 10000) {
                        return (value / 10000).toFixed(1) + '万';
                    }
                    return value;
                }
            }
            option4.series = [];
            option4.legend.data = [];
            data.other.ydata.map((e:any)=>{
                option4.legend.data.push(e.name)
                option4.series.push({
                    data: e.data,
                    name: e.name,
                    type: 'line',
                    stack: 'Total',
                    smooth: true,
                    label: {
                        show: dataForm.value.labelShow,
                        position: 'top'
                    },
                })
            })
            PreviewChart(option4);
            break;
        case "option5":
            option5.series[0].data = data.pie;
            PreviewChart(option5);
            break;
        case "option6":
            option6.series[0].data = data.pie;
            PreviewChart(option6);
            break;
    }
}

// 图例显隐
const legendChange = (value:any) =>{
    switch (dataForm.value.type) {
        case "option1":
            option1.legend.show = value
            PreviewChart(option1);
            break;
        case "option2":
            option2.legend.show = value
            PreviewChart(option2);
            break;
        case "option3":
            option3.legend.show = value
            PreviewChart(option3);
            break;
        case "option4":
            option4.legend.show = value
            PreviewChart(option4);
            break;
        case "option5":
            option5.legend.show = value
            PreviewChart(option5);
            break;
        case "option6":
            option6.legend.show = value
            PreviewChart(option6);
            break;
    }
}

// 数据标签显隐
const labelChange = (value:any) =>{
    switch (dataForm.value.type) {
        case "option1":
            option1.series.map((e:any)=>{
                e.label.show = value
            })
            PreviewChart(option1);
            break;
        case "option2":
            option2.series.map((e:any)=>{
                e.label.show = value
            })
            PreviewChart(option2);
            break;
        case "option3":
            option3.series.map((e:any)=>{
                e.label.show = value
            })
            PreviewChart(option3);
            break;
        case "option4":
            option4.series.map((e:any)=>{
                e.label.show = value
            })
            PreviewChart(option4);
            break;
        case "option5":
            option5.series.map((e:any)=>{
                e.label.normal.show = value
            })
            PreviewChart(option5);
            break;
        case "option6":
            option6.series.map((e:any)=>{
                e.label.normal.show = value
            })
            PreviewChart(option6);
            break;
    }
}

// 图例位置
const positionChange = (value:any) =>{
    let legend:any = {
        show: dataForm.value.legendShow,
        orient: 'horizontal'
    };
    if(value == 'top'){
        legend.top = 'top'
    }else if(value == 'bottom'){
        legend.bottom = 'bottom'
    }
    switch (dataForm.value.type) {
        case "option1":
            option1.legend = legend
            PreviewChart(option1);
            break;
        case "option2":
            option2.legend = legend
            PreviewChart(option2);
            break;
        case "option3":
            option3.legend = legend
            PreviewChart(option3);
            break;
        case "option4":
            option4.legend = legend
            PreviewChart(option4);
            break;
        case "option5":
            option5.legend = legend
            PreviewChart(option5);
            break;
        case "option6":
            option6.legend = legend
            PreviewChart(option6);
            break;
    }
}

// 提交
const submit = () =>{
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        if(!dataForm.value.name){
            activeName.value = '1';
            ElMessage.warning('请输入统计图名称');
            return;
        }
        switch (dataForm.value.type) {
            case "option1":
                option1.xAxis.data = [];
                option1.series = [];
                dataForm.value.setting = option1;
                break;
            case "option2":
                option2.xAxis.data = [];
                option2.series = [];
                dataForm.value.setting = option2;
                break;
            case "option3":
                option3.xAxis.data = [];
                option3.series = [];
                option3.legend.data = [];
                dataForm.value.setting = option3;
                break;
            case "option4":
                option4.xAxis.data = [];
                option4.series = [];
                option4.legend.data = [];
                dataForm.value.setting = option4;
                break;
            case "option5":
                option5.series[0].data = [];
                dataForm.value.setting = option5;
                break;
            case "option6":
                option6.series[0].data = [];
                dataForm.value.setting = option6;
                break;
        }
        const params = JSON.parse(JSON.stringify(dataForm.value));
        params.yzColumns = Array.isArray(params.yzColumns) ? params.yzColumns.join(',') : params.yzColumns;
        params.otherSetting = JSON.stringify({
            labelShow: params.labelShow,
            legendShow: params.legendShow,
            position: params.position,
            chartWitch: params.chartWitch,
            ThemeColor: params.ThemeColor
        })
        params.setting = JSON.stringify(params.setting);
        (!dataForm.value.id ? baseService.post : baseService.put)('/report/reportchart',params).then(res=>{
            console.log(res);
            if(res.code == 0){
                ElMessage.success({
                    message: "操作成功！",
                    duration: 500,
                    onClose: () => {
                        visible.value = false;
                        emit("refreshDataList");
                    }
                });
            }
        })
    })
}

const init = (type:string,id?:number) => {
    visible.value = true;
    colorValue.value = ThemeColor[0].value;
    
    // 重置表单数据
    resetData();
    if (dataFormRef.value) {
        dataFormRef.value.resetFields();
    }
    
    if(id){
        nextTick(()=>{
            getInfo(id);
        })
    }else{
        dataForm.value.type = type;
        nextTick(()=>{
            getfieldList();
        })
        
    }
};

// 充值表格数据
const resetData = () =>{
    charts.value = [];
    dataForm.value = {
        id: '', // 数据id
        indexName: '',  // 报表索引名称
        name: '',   // 统计图名称
        type: '',   // 统计图类型
        setting: <any>'',    // 统计图表样式
        themeColor: '', // 主题色
        chartType: '',  
        yzColumnsSingle: '', // Y轴单选(饼图)
        yzColumns: <any>[],  // Y轴
        xzColumn: '',   // X轴
        sortType: 1,    // 排序依据
        isAsc: true,    // 排序规则
        chartWitch: '1',
        position: 'top',   // 图例位置
        labelShow: false, // 数据标签
        legendShow: true, // 图例
        ThemeColor: '主题1', // 主题色
    }
    
}


// 获取详情信息
const getInfo = (id:number) =>{
    baseService.get('/report/reportchart/' + id).then(res=>{
        const data = res.data;
        if(data.type == 'option5' || data.type == 'option6'){
            data.yzColumnsSingle = data.yzColumns;
        }else{
            data.yzColumns = data.yzColumns.split(',');
        }
        
        if(res.code == 0){
            switch (data.type) {
                case "option1":
                    Object.assign(option1,JSON.parse(data.setting));
                    break;
                case "option2":
                    Object.assign(option2,JSON.parse(data.setting));
                    break;
                case "option3":
                    Object.assign(option3,JSON.parse(data.setting));
                    break;
                case "option4":
                    Object.assign(option4,JSON.parse(data.setting));
                    break;
                case "option5":
                    Object.assign(option5,JSON.parse(data.setting));
                    break;
                case "option6":
                    Object.assign(option6,JSON.parse(data.setting));
                    break;
            }
            Object.assign(dataForm.value,data);
            Object.assign(dataForm.value,JSON.parse(data.otherSetting));
            getfieldList();
            
        }
        
    })
    
}

watch(
  ()=> props.menuInfo.id,
  (newValue)=>{
    if(newValue){
      nextTick(()=>{
        if(visible.value){
            getfieldList();
        }
        
      })
    }
  },{
    immediate: true
  }
)

defineExpose({
  init
});

</script>

<style lang='less' scoped>
.dialog_header{
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    padding-top: 15px;
    padding-left: 16px;
}
.demo-tabs{
    display: flex;
    justify-content: center;
    :deep(.el-tabs__header){
        margin: 0px;
    }
}
.chart_page{
    border-top: 1px solid #E4E7ED;
    height: 700px;
    display: flex;
    .chart{
        flex: 1;
        height: 100%;
        padding: 24px;
        border-right: 1px solid #E4E7ED;
    }
    .config_page{
        width: 340px;
    }
}
.config_form{
    padding: 16px 24px;
    height: 604px;
    overflow: auto;
}
.items{
    padding: 11px 12px;
    border: 1px solid #E4E7ED;
    border-radius: 5px 5px 5px 5px;
    display: flex;
    align-items: center;
    img{
        width: 42px;
        height: 42px;
    }
    span{
        font-weight: 400;
        font-size: 12px;
        color: #4165D7;
        line-height: 22px;
        margin-left: 6px;
    }
}

.colorLine{
    display: flex;
    align-items: center;
    border-radius: 6px;
    &:hover{
        background-color: #ECF0FB;
    }
}
.colorItem{
    width: 22px;
    height: 22px;
    &:first-of-type{
        border-radius: 4px 0px 0px 4px;
    }
    &:last-child{
        border-radius: 0px 4px 4px 0px;
    }
}

.selectFrame{
    height: 32px;
    width: 100%;
    border: 1px solid #DCDFE6;
    border-radius: 4px 4px 4px 4px;
    padding: 5px 8px;
    .typeName{
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
    }
}

.config_but{
    display: flex;
    justify-content: flex-end;
    height: 56px;
    padding: 12px 24px;
    border-top: 1px solid #E4E7ED;
}

.chartsPage{
    .chartsCard{
        .title{
            font-weight: 400;
            font-size: 14px;
            color: #303133;
            margin: 8px 0;
        }
        .chartsLine{
            display: flex;
            align-items: center;
            .chartsInfo{
                flex: 1;
                display: flex;
                align-items: center;
                border-radius: 5px 5px 5px 5px;
                border: 1px solid #E4E7ED;
                padding: 11px 13px;
                cursor: pointer;
                img{
                    width: 30px;
                    height: 30px;
                }
                span{
                    font-weight: 400;
                    font-size: 12px;
                    color: #4165D7;
                    line-height: 22px;
                    margin-left: 8px;
                }
                &:hover{
                    background-color: #ECF0FB;
                    border: 1px solid #4165D7;
                }
            }
            .chartsInfo + .chartsInfo{
                margin-left: 8px;
            }
            .active{
                background-color: #ECF0FB;
                border: 1px solid #4165D7;
            }
        }
        
    }
}
</style>