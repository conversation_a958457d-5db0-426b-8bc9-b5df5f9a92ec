<template>
  <el-card shadow="never" class="rr-view-ctx-card cardTop">
    <div class="mainBox">
      <div class="leftPart">
        <ul class="menuUl">
          <li :class="'menuLi ' + (activeName == item ? 'active' : '')" v-for="(item, itemIndex) in ['提现充值设置', '账户管理', '审核流程', '保证金设置', '支付设置']" :key="itemIndex" @click="changeTab(item)" :index="itemIndex">
            <!-- <el-tooltip effect="dark" :content="item" placement="top-start"> -->
            <span>{{ item }}</span>
            <!-- </el-tooltip> -->
          </li>
        </ul>
      </div>
      <div ref="rightPart" style="flex: 1">
        <div class="pagetitle" v-if="activeName != '审核流程'">
          <div style="font-weight: bold; font-size: 16px; color: #303133">{{ activeName }}</div>
          <div class="button" v-if="state.hasPermission('flowable:withdraw:save') && ['提现充值设置', '保证金设置'].includes(activeName)">
            <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle">保存更改</el-button>
          </div>
          <div class="button" v-if="state.hasPermission('wallet:tenantaccount:save') && activeName == '账户管理'">
            <el-button type="primary" @click="addHandle">添加账户</el-button>
          </div>
          <div class="button" v-if="state.hasPermission('flowable:withdraw:save') && activeName == '支付设置'">
            <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle2">保存更改</el-button>
          </div>
        </div>
        <template v-if="activeName == '提现充值设置'">
          <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top" style="margin: auto">
            <div class="cardDescriptions" style="padding: 0">
              <div class="titleSty">提现设置</div>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item :span="2" label="提现信息匹配验证">
                  <el-form-item label="提现信息匹配验证" prop="isNormal">
                    <el-switch active-text="启用" inactive-text="禁用" v-model="dataForm.isNormal"></el-switch>
                    <el-alert class="alertSty" title="验证用户的姓名和支付宝账号是否汇配，需要开通支付宝的转账到支付宝账户产品" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="提现手续费(%)">
                  <el-form-item label="提现手续费(%)" prop="charge">
                    <el-input v-model="dataForm.charge" type="number" placeholder="请输入提现手续费"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="免费提现额度(元)">
                  <el-form-item label="免费提现额度(元)" prop="freeAmount">
                    <el-input v-model="dataForm.freeAmount" type="number" placeholder="请输入免费提现额度"></el-input>
                    <el-alert class="alertSty" title="用户初始提现免费额度" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="提现最小金额(元)">
                  <el-form-item label="提现最小金额(元)" prop="minAmount">
                    <el-input v-model="dataForm.minAmount" type="number" placeholder="请输入提现最小金额"></el-input>
                    <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="提现最大金额(元)">
                  <el-form-item label="提现最大金额(元)" prop="maxAmount">
                    <el-input v-model="dataForm.maxAmount" type="number" placeholder="请输入提现最大金额"></el-input>
                    <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfRight" label="每天提现次数">
                  <el-form-item label="每天提现次数" prop="maxCount">
                    <el-input v-model="dataForm.maxCount" type="number" placeholder="请输入每天提现次数"></el-input>
                    <el-alert class="alertSty" title="0为不限制" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label=""> </el-descriptions-item>
              </el-descriptions>
              <el-col :span="24">
                <div class="titleSty">充值设置</div>
              </el-col>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border>
                <el-descriptions-item :span="2" label="充值提示" class-name="noneSelfRight">
                  <el-form-item label="充值提示" prop="rechargeTips">
                    <el-input :rows="1" type="textarea" v-model="dataForm.rechargeTips" placeholder="请输入充值提示" />
                    <el-alert class="alertSty" title="资金充值页面" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label=""> </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-form>
        </template>
        <template v-if="activeName == '账户管理'">
          <ny-table style="width: 100%; margin: auto" :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
            <!-- 账户类型 -->
            <template #type="{ row }">
              <span v-if="!row.isOperate">{{ getTypeText(row.type) }}</span>
              <el-select v-else v-model="row.type" filterable clearable placeholder="请输入账户类型">
                <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
            <!-- 账户名称 -->
            <template #name="{ row }">
              <span v-if="!row.isOperate">{{ row.name }}</span>
              <el-input v-else v-model="row.name" placeholder="请输入账户名称" />
            </template>
            <!-- 账号 -->
            <template #account="{ row }">
              <span v-if="!row.isOperate">{{ row.account }}</span>
              <el-input v-else v-model="row.account" placeholder="请输入账号" />
            </template>
            <!-- 开户行 -->
            <template #bankName="{ row }">
              <span v-if="!row.isOperate">{{ row.bankName || "-" }}</span>
              <el-input v-else v-model="row.bankName" placeholder="请输入开户行(银行卡必填)" />
            </template>
            <!-- 操作 -->
            <template #operation="{ row, $index }">
              <el-link v-if="state.hasPermission('wallet:tenantaccount:update')" type="primary" @click="handleEdit(row, $index)">{{ row.isOperate ? "保存" : "编辑" }}</el-link>
              <el-link v-if="state.hasPermission('wallet:tenantaccount:delete')" type="danger" @click="row.id ? state.deleteHandle(row.id) : delhandle($index)" style="margin-left: 12px">删除</el-link>
            </template>
          </ny-table>
        </template>
        <template v-if="activeName == '审核流程'">
          <auditFlowSet v-if="activeName == '审核流程'" ref="auditFlowSetRef"></auditFlowSet>
        </template>
        <template v-if="activeName == '保证金设置'">
          <el-form :model="dataForm" :rules="rules" ref="dataFormMoneyRef" label-position="top" style="width: 100%; margin: auto">
            <div class="cardDescriptions">
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item class-name="noneSelfRight" label="最低缴费(元)">
                  <el-form-item label="最低缴费(元)" prop="bondMin">
                    <el-input v-model="dataForm.bondMin" type="number" placeholder="请输入商城保证金最低缴费额度"></el-input>
                    <el-alert class="alertSty" title="合作商入驻平台保证金缴费最低额度" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item class-name="noneSelfLeft" label-class-name="noneSelfLabel" label="">
                  <el-form-item label="" prop="" style="visibility: hidden">
                    <el-input placeholder="请输入商城保证金最低缴费额度"></el-input>
                    <el-alert title="合作商入驻平台保证金缴费最低额度" type="info" show-icon :closable="false" />
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item :span="2" label="违规处罚扣除">
                  <el-form-item style="margin-bottom: 8px; padding-right: 145px" :key="i_" v-for="(ele, i_) in dataForm.punishList" prop="punishList">
                    <div style="display: flex; justify-content: space-between; width: 100%">
                      <span style="color: #606266">违规行为{{ i_ + 1 }}</span>
                      <div>
                        <el-button style="width: 14px; height: 14px" :icon="Plus" @click="addArrHandle" type="primary" circle />
                        <el-button v-if="i_ != 0" style="margin-left: 12px; width: 14px; height: 14px" type="danger" @click="delArrHandle(i_)" :icon="Delete" circle />
                      </div>
                    </div>
                    <div style="display: flex; width: 100%; align-items: center">
                      <el-select style="width: 50%" v-model="ele.name" type="number" placeholder="请选择违规行为">
                        <el-option></el-option>
                      </el-select>
                      <el-input style="width: 50%; margin-left: 12px" v-model="ele.bondPunish" type="number" placeholder="请输入违规处罚扣除金额"></el-input>
                      <span style="font-family: Inter, Inter; font-weight: 400; font-size: 13px; color: #a8abb2; line-height: 22px; margin-left: 2px">元</span>
                    </div>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-form>
        </template>
        <template v-if="activeName == '支付设置'">
          <el-form :model="dataFormPay" :rules="rules" ref="dataFormRef" label-position="top" style="width: 100%; margin: auto">
            <div class="cardDescriptions">
              <div class="titleSty">线下支付设置</div>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item :span="2" label="用户线下支付金额设定">
                  <el-form-item label="最低缴保证金是否开启支付宝扫码支付费(元)" prop="startAmount">
                    <el-input style="width: 320px; margin-bottom: 8px" v-model="dataFormPay.startAmount" type="number" placeholder="请输入金额">
                      <template #append>元</template>
                    </el-input>
                    <el-alert class="alertSty" title="" type="info" :closable="false">
                      <template #title>
                        <div style="display: flex; justify-content: flex-start">
                          <el-icon style="font-size: 16px"><InfoFilled /></el-icon>
                          <div style="margin-left: 4px">
                            <div style="line-height: 16px">当用户在商城选购商品时，若商品金额超过预设阈值，则系统将自动引导用户至客服聊天界面进行进一步沟通；</div>
                            <div>若输入0，则表示不启用此功能。</div>
                          </div>
                        </div>
                      </template>
                    </el-alert>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
              <el-col :span="24">
                <div class="titleSty">支付宝功能设置</div>
              </el-col>
              <el-descriptions style="width: 100%; margin-bottom: 10px" border :column="2">
                <el-descriptions-item :span="2" label="">
                  <template #label>
                    <span>保证金是否开启<br />支付宝扫码支付</span>
                  </template>
                  <el-form-item label="用户线下支付金额设定" prop="bondByQrCode">
                    <el-switch active-text="启用" inactive-text="禁用" v-model="dataFormPay.bondByQrCode"></el-switch>
                    <el-alert class="alertSty" title="" type="info" :closable="false">
                      <template #title>
                        <div style="display: flex; justify-content: flex-start">
                          <el-icon style="font-size: 16px"><InfoFilled /></el-icon>
                          <div style="margin-left: 4px">
                            <div style="line-height: 16px">启用支付宝扫码支付功能后，合作商将能够在平台内直接通过扫描二维码完成保证金的充值。</div>
                            <div>与此同时，平台需向支付宝支付相应的交易手续费。</div>
                          </div>
                        </div>
                      </template>
                    </el-alert>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item :span="2" label="应用公钥证书">
                  <el-form-item label="" prop="appCertPath">
                    <ny-upload-file ossUrl="/sys/oss/uploadLocal" :isSelfSty="false" :isDrag="false" :isSimple="true" tip="" v-model:fileSrc="dataFormPay.appCertPath">
                      <template #content>
                        <el-button>上传文件</el-button>
                      </template>
                    </ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item :span="2" label="应用私钥">
                  <el-form-item label="" prop="privateKey">
                    <el-input type="textarea" :rows="3" placeholder="请输入应用私钥" v-model="dataFormPay.privateKey"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item :span="2" label="支付宝公钥证书">
                  <el-form-item label="" prop="aliPayCertPath">
                    <ny-upload-file ossUrl="/sys/oss/uploadLocal" :isSelfSty="false" uploadPreText="" :isDrag="false" :isSimple="true" tip="" v-model:fileSrc="dataFormPay.aliPayCertPath">
                      <template #content>
                        <el-button>上传文件</el-button>
                      </template>
                    </ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item :span="2" label="支付宝根证书">
                  <el-form-item label="" prop="aliPayRootCertPath">
                    <ny-upload-file ossUrl="/sys/oss/uploadLocal" :isSelfSty="false" uploadPreText="" :isDrag="false" :isSimple="true" tip="" v-model:fileSrc="dataFormPay.aliPayRootCertPath">
                      <template #content>
                        <el-button>上传文件</el-button>
                      </template>
                    </ny-upload-file>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="APPID">
                  <el-form-item label="" prop="appId">
                    <el-input placeholder="请输入APPID" v-model="dataFormPay.appId"></el-input>
                  </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="绑定的商家账号（PID）">
                  <el-form-item label="" prop="aliPayAccount">
                    <el-input placeholder="请输入商家账号" v-model="dataFormPay.aliPayAccount"></el-input>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-form>
        </template>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs, onUnmounted } from "vue";
import WangEditor from "@/components/wang-editor/index.vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import useView from "@/hooks/useView";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import auditFlowSet from "./auditFlowSet.vue";

const { t } = useI18n();
const activeName = ref("提现充值设置");
const dataFormRef = ref();
const dataFormMoneyRef = ref();
const dataForm = reactive({
  //提现信息匹配验证
  isNormal: false,
  // 提现手续费
  charge: undefined,
  // 免费提现额度
  freeAmount: undefined,
  // 提现最小额度
  minAmount: undefined,
  // 提现最大额度
  maxAmount: undefined,
  // 每日提现次数
  maxCount: undefined,
  // 充值提示
  rechargeTips: undefined,
  // 保证金
  bondMin: undefined,
  // 违规行为
  punishList: <any>[]
});
const dataFormPay = reactive({
  startAmount: undefined,
  bondByQrCode: undefined,
  appCertPath: undefined,
  privateKey: undefined,
  aliPayCertPath: undefined,
  aliPayRootCertPath: undefined,
  appId: undefined,
  aliPayAccount: undefined
});
const btnLoading = ref(false);
const typeList = [
  {
    label: "支付宝",
    value: "1"
  },
  {
    label: "微信",
    value: "2"
  },
  {
    label: "银行卡",
    value: "3"
  }
];
const view = reactive({
  getDataListURL: "/wallet/tenantaccount/page",
  getDataListIsPage: true,
  deleteURL: "/wallet/tenantaccount",
  deleteIsBatch: true
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// 单行数据
const rowForm = reactive({
  data: {
    id: undefined,
    type: undefined,
    name: undefined,
    account: undefined
  },
  index: -1
});

// 获取信息
const getInfo = () => {
  if (activeName.value == "支付设置") {
    baseService.get("/pay/alipay/query/conf").then((res) => {
      Object.assign(dataFormPay, res.data);
      // dataForm.isNormal = res.data.isNormal == 1 ? true : false;
    });
    return;
  }
  // 提现设置
  baseService.get("/flowable/withdraw").then((res) => {
    Object.assign(dataForm, res.data);
    dataForm.isNormal = res.data.isNormal == 1 ? true : false;
    dataForm.punishList =
      dataForm.punishList?.length > 0
        ? dataForm.punishList
        : [
            {
              bondPunish: undefined,
              name: ""
            }
          ];
  });
};
// ------------------操作

// 切换tab
const changeTab = (name?: any) => {
  activeName.value = name;
  if (activeName.value == "提现充值设置" || activeName.value == "保证金设置" || activeName.value == "支付设置") {
    getInfo();
  } else if (activeName.value == "账户管理") {
    state.getDataList();
  }
};

// 账号编辑和保存操作
const handleEdit = (form: any, index: any) => {
  if (rowForm.index != -1 && form.isOperate) {
    if (form.type == 3 && !form.bankName) {
      ElMessage.warning("请填写开户行！");
      return;
    }
    if (!form.account || !form.type || !form.name) {
      ElMessage.warning("请填写账户信息！");
      return;
    }
    // 保存操作
    submitForm(form);
  } else {
    rowForm.index = index;
    state.dataList[index].isOperate = true;
  }
};
const addHandle = () => {
  state.dataList.push({
    type: undefined,
    name: undefined,
    account: undefined,
    isOperate: true
  });
  rowForm.index = state.dataList.length;
};
const delhandle = (index?: any) => {
  state.dataList?.splice(index, 1);
  rowForm.index = -1;
};
const submitForm = (form: any) => {
  baseService[form.id ? "put" : "post"]("/wallet/tenantaccount", form)
    .then((res) => {
      if (res.code === 0) {
        state.getDataList();
        rowForm.index = -1;
        ElMessage.success(res.msg);
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
const getTypeText = (type?: any) => {
  if (type) {
    let obj = typeList.find((ele) => ele.value == type);
    return obj.label;
  } else {
    return "";
  }
};
// 添加违规行为
const addArrHandle = () => {
  dataForm.punishList.push({
    bondPunish: undefined,
    name: ""
  });
};
// 删除流程违规行为
const delArrHandle = (index?: any) => {
  dataForm.punishList.splice(index, 1);
};

// 表单提交
const dataFormSubmitHandle = () => {
  if (activeName.value == "提现充值设置" || activeName.value == "保证金设置") {
    let formRef = activeName.value == "提现充值设置" ? dataFormRef.value : dataFormMoneyRef.value;
    formRef.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      let form = { ...dataForm };
      btnLoading.value = true;
      form.isNormal = form.isNormal ? 1 : 0;
      baseService
        .post("/flowable/withdraw", form)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });
    });
  }
};
const dataFormSubmitHandle2 = () => {
  if (activeName.value == "支付设置") {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      let form = { ...dataFormPay };
      btnLoading.value = true;
      baseService
        .post("/pay/alipay/save/conf", form)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500
          });
          getInfo();
        })
        .finally(() => {
          btnLoading.value = false;
        });
    });
  }
};
// 自定义验证
const validatorMax = (frule: any, value: any, callback: any) => {
  if (+dataForm.maxAmount < +dataForm.minAmount && dataForm.minAmount != 0 && dataForm.maxAmount != 0) {
    callback(new Error("提现最大金额不能小于提现最小金额"));
  } else {
    callback();
  }
};
// 表格配置项
const columns = reactive([
  {
    prop: "type",
    label: "账户类型",
    width: 240
  },
  {
    prop: "name",
    label: "账户名称",
    width: 240
  },
  {
    prop: "bankName",
    label: "开户行",
    width: 240
  },
  {
    prop: "account",
    label: "账号"
  },
  {
    prop: "operation",
    label: "操作",
    width: 100
  }
]);
const rules = ref({
  isNormal: [{ required: false, message: "请选择提现信息匹配验证", trigger: "blur" }],
  charge: [{ required: false, message: "请输入提现手续费", trigger: "blur" }],
  freeAmount: [{ required: false, message: "请输入免费提现额度", trigger: "blur" }],
  minAmount: [{ required: false, message: "请输入提现最小金额", trigger: "blur" }],
  maxAmount: [
    { required: false, message: "请输入提现最大金额", trigger: "blur" },
    { trigger: "blur", validator: validatorMax }
  ],
  maxCount: [{ required: false, message: "请输入每天提现次数", trigger: "blur" }],
  rechargeTips: [{ required: false, message: "请输入充值提示", trigger: "blur" }]
});
const rightPart = ref();
const getRightWidth = () => {
  let fatherBox = document.querySelector(".mainBox");
  rightPart.value.style.width = `${+fatherBox.offsetWidth - 212}px`;
};
onMounted(() => {
  getInfo();
  getRightWidth();
  window.addEventListener("resize", getRightWidth); // 监听窗口大小变化
});
onUnmounted(() => {
  window.removeEventListener("resize", getRightWidth);
});
</script>

<style scoped lang="less">
.pagetitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  .label {
    font-weight: bold;
    font-size: 20px;
    color: #303133;
    line-height: 28px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .button {
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
.el-alert {
  padding: 0;
  background: none;
}
:deep(.el-form-item__label) {
  padding-right: 0;
}
:deep(.el-tabs__item) {
  padding: 0 !important;

  &.is-active {
    font-weight: bold;
  }
}
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}
.mainBox {
  display: flex;
  margin-top: 12px;
  .leftPart {
    margin-right: 24px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .menuUl {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      .menuLi {
        width: 186px;
        cursor: pointer;
        padding: 20px;
        word-break: keep-all;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: Inter, Inter;
        font-weight: 400;
        font-size: 12px;
        color: #303133;
        line-height: 14px;
        &.active {
          background-color: var(--color-primary-light);
          color: var(--color-primary);
        }
      }
    }
  }
  .el-descriptions {
    :deep(.el-form-item__label) {
      display: none;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
    :deep(.el-descriptions__label) {
      font-weight: normal;
      width: 144px;
    }
    :deep(.el-descriptions__content) {
      height: 80px;
    }
    :deep(.noneSelfRight) {
      border-right: 0 !important;
    }
    :deep(.noneSelfLeft) {
      border-left: 0 !important;
    }
    :deep(.noneSelfLabel) {
      background: none;
      border-left: 0 !important;
      border-right: 0 !important;
    }
  }
}

.uploadFileStyle {
  :deep(.file-uploader) {
    display: flex;

    .el-upload-list {
      width: 100%;
      margin-left: 12px;
    }
  }
}
</style>
