<template>
    <div class="ny-header flx-justify-between">
        <div class="left-logo flx-center">
            <img :src="sttingInfo.info.backendLogo" class="header-logo-img" />   
            <span class="header-logo-name">{{ sttingInfo.info.backendTitle }}</span>
        </div>
        <div class="right-tool flx-center">
            <div class="automatic-reply-set" @click="autoReplyHandle" v-if="store.state.user.tenantCode == 10000">
                自动回复设置<el-icon size="16"><ChatLineRound /></el-icon>
            </div>
            <div class="online">
                <el-dropdown popper-class="online-dropdown" @command="(command: string) => handleCommand(command)">
                    <div class="el-dropdown-link" :class="{ active:  imStore.isOnline }">
                        <span >
                            {{ imStore.isOnline ? '在线中' : '已离线' }}
                        </span>
                        <el-icon><CaretBottom /></el-icon>
                    </div>
                    <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item :command="true">上线</el-dropdown-item>
                        <el-dropdown-item :command="false">下线</el-dropdown-item>
                    </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div class="user-info flx-center">
                <el-image class="user-avatar" :src="(store.state.user.headUrl != '' && store.state.user.headUrl != null) ? store.state.user.headUrl : sttingInfo.info.backendDefaultAvatar">
                    <template #error>
                        <div class="image-slot">
                            <el-image class="avatar" :src="sttingInfo.info.backendDefaultAvatar"></el-image>
                        </div>
                    </template>
                </el-image>
                <div class="user-name">{{ store.state.user.nickname || store.state.user.realName || store.state.user.username }}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useAppStore } from "@/store";
import { useImStore } from '@/store/im';
import { useSettingStore } from '@/store/setting';
import { ChatLineRound } from '@element-plus/icons-vue';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";


const store = useAppStore();
const imStore = useImStore();
const sttingInfo = useSettingStore();

const autoReplyHandle = () => {
    imStore.showAutoReplyDialog = true;
}

// 在线状态
const handleCommand = (status: boolean) => {
    baseService.get("/im/login/online", { online: status }).then((res: any) => {
        if(res.code == 0){
            imStore.isOnline = status;
            ElMessage.success(status ? '已上线' : '已下线');
        }
    }).catch((err: any) => {
        console.log(err)
    });
}


</script>

<style lang="scss" scoped>
    .ny-header{
        height: 72px;
        padding: 0 22px 0 16px;
        background: #fff;
        box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.06);

        .header-logo-img{
            width: 56px;
            height: 56px;
            display: inline-block;
            flex-shrink: 0;
        }

        .header-logo-name{
            margin-left: 10px;
            font-weight: bold;
            font-size: 24px;
            color: #303133;
            line-height: 24px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }

        .right-tool{
            font-size: 14px;
            
            .automatic-reply-set{
                font-weight: 500px;
                line-height: 18px;
                padding-right: 12px;
                cursor: pointer;
                
                .el-icon{
                    margin-left: 5px;
                    vertical-align: middle;
                    margin-top: -3px;
                }
            }

            .online{
                border-right: 1px solid var(--el-color-primary);
                padding-right: 12px;
                margin-right: 12px;
                cursor: pointer;

                .el-dropdown-link{
                    width: 86px;
                    height: 32px;
                    line-height: 32px;
                    border-radius: 4px;
                    text-align: center;
                    background: #909399;
                    color: #fff;
                }
                

                .active{
                    background: #00C568;
                }
            }
            
            .user-info{
                
                .user-avatar{
                    width: 32px;
                    height: 32px;
                    border-radius: 4px;
                    margin-right: 8px;
                }
            }
        }
    }

    .online-dropdown .el-dropdown-menu{
        width: 113px;
    }
</style>