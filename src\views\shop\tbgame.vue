<template>
  <div class="mod-shop__tbgame">
    <el-card shadow="never" class="shopCards ny_form_card">
      <ny-form-slot>
        <template #content>
          <el-tabs v-model="state.dataForm.status" @tab-click="handleClick" class="shopTabs">
            <el-tab-pane label="全部" name=""></el-tab-pane>
            <el-tab-pane label="上架" name="1"></el-tab-pane>
            <el-tab-pane label="下架" name="2"></el-tab-pane>
          </el-tabs>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.name" placeholder="游戏名称" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-select v-model="state.dataForm.type" placeholder="运行设备" clearable>
                <el-option label="手游" value="1"></el-option>
                <el-option label="端游" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </template>
        <template #button>
          <div style="padding-top: 56px">
            <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
            <el-button
              @click="
                state.dataForm = { status: '', name: '', type: '', order: '', orderField: '' };
                state.getDataList();
              "
              type="info"
              >重置</el-button
            >
          </div>
        </template>
      </ny-form-slot>
    </el-card>

    <el-card shadow="never" class="shopCards">
      <div style="padding-bottom: 20px">
        <el-button v-if="state.hasPermission('shop:tbgame:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button v-if="state.hasPermission('shop:tbgame:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
        <el-button type="warning" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        <el-button type="success" @click="importHandle()">{{ $t("excel.import") }}</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border class="draggable-table" ref="draggableRef" row-key="id" @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" @sort-change="handleSortChange">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="游戏名称" header-align="center" align="center" width="260" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.name }}({{ scope.row.code }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="运行设备" header-align="center" align="center" width="150">
          <template #default="scope">
            <span>{{ scope.row.type == 1 ? "手游" : scope.row.type == 2 ? "端游" : "" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="gameAreaList" label="系统区服" header-align="center" align="center" width="260" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ getList(scope.row.gameAreaList) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="image" label="图标" header-align="center" align="center" width="100">
          <template #default="scope">
            <el-image style="width: 36px; height: 36px" :src="scope.row.image" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[scope.row.image]" :initial-index="4" fit="cover" />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" header-align="center" align="center" width="120">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" @change="changeSwitch(scope.row.id, scope.row.status)" />
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center" width="180" sortable="custom" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatTimeStamp(scope.row.createDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="" label="设置" header-align="center" align="center" width="220">
          <template #default="scope">
            <el-button type="primary" @click="setInfosClick(scope.row)">设置商品信息字段</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建者ID" header-align="center" align="center" width="200"> </el-table-column>
        <el-table-column prop="updater" label="更新者ID" header-align="center" align="center" width="200"> </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="200">
          <template v-slot="scope">
            <el-button v-if="state.hasPermission('shop:tbgame:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('shop:tbgame:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
            <el-button class="dragBtn" type="primary" text bg>
              <el-icon style="color: var(--el-color-primary)">
                <Rank />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>

    <!-- 导入 -->
    <import ref="importRef" @refreshDataList="state.getDataList"></import>

    <!-- 弹窗, 设置商品信息字段 -->
    <set-infos ref="setInfosRef" @refreshDataList="state.getDataList"></set-infos>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import AddOrUpdate from "./tbgame-add-or-update.vue";
import SetInfos from "./tbgame-set-infos.vue";
import { formatTimeStamp } from "@/utils/method";
import Import from "./tbgame-excel-import.vue";
import { ElMessage, TabsPaneContext } from "element-plus";
import baseService from "@/service/baseService";
import Sortable from "sortablejs";

const view = reactive({
  getDataListURL: "/shop/tbgame/page",
  getDataListIsPage: true,
  exportURL: "/shop/tbgame/export",
  deleteURL: "/shop/tbgame",
  deleteIsBatch: true,
  dataForm: { status: "", name: "", type: "", orderField: "", order: "" },
  dataList: []
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 创建时间排序
const handleSortChange = ({ prop, order }) => {
  state.dataForm.orderField = prop === "createDate" ? "create_date" : prop;
  state.dataForm.order = order === "ascending" ? "asc" : order === "descending" ? "desc" : "";
  state.getDataList();
};

// 上下架
const loading1 = ref(false);
const changeSwitch = (id?: number, status?: number) => {
  if (!state.hasPermission("shop:tbgame:update")) return;
  loading1.value = true;
  baseService
    .get("/shop/tbgame/status", { id: id, status: status })
    .then((res) => {
      ElMessage.success("操作成功！");
      state.getDataList();
    })
    .catch((err) => {
      ElMessage.error("操作失败！");
    })
    .finally(() => {
      loading1.value = false;
    });
};

// 设置商品信息字段
const setInfosRef = ref();
const setInfosClick = (row?: any) => {
  nextTick(() => {
    setInfosRef.value.init(row.id, row.code);
  });
};

// 切换状态tabs
const handleClick = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.name) {
    state.dataForm.status = tab.props.name.toString();
  } else {
    state.dataForm.status = "";
  }
  state.getDataList();
};

// 系统区服
const getList = (arrs: any) => {
  let datas: any = [];
  if (arrs.length > 0) {
    arrs.forEach((element: any) => {
      datas.push(element.name);
    });
  }
  return datas.join(",");
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

const draggableRef = ref();

const initDragSortTableRow = () => {
  let el: any = document.querySelector(".draggable-table .el-table__body-wrapper tbody");
  Sortable.create(el, {
    handle: ".dragBtn",
    ghostClass: "",
    setData: (dataTransfer) => {},
    onEnd: ({ newIndex, oldIndex }) => {
      const newId = state.dataList[newIndex].id;
      const oldId = state.dataList[oldIndex].id;
      baseService
        .get("/shop/tbgame/sort", { sourceId: oldId, targetId: newId })
        .then((res) => {
          if (res.code == 0) {
            ElMessage.success("操作成功！");
            state.getDataList();
          } else {
            ElMessage.error("操作失败！");
          }
        })
        .catch((err) => {
          ElMessage.error("操作失败！");
        });
    }
  });
};

onMounted(() => {
  state.dataForm.status = "";
  initDragSortTableRow(); //拖拽表格行排序
});
</script>

<style lang="less" scoped>
.shopCards {
  border: none;

  &:nth-child(2) {
    margin-top: 20px;
  }
}

.shopTabs {
  :deep(.el-tabs__nav-wrap:after) {
    height: 0px;
    background-color: transparent;
  }
}

:deep th.el-table__cell,
:deep td.el-table__cell {
  position: static;
}

:deep(.el-button) {
  &.is-has-bg {
    min-width: 48px;
    padding: 5px 10px;
  }
}
</style>
