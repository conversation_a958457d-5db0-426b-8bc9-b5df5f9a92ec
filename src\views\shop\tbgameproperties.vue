<template>
  <div class="mod-shop__tbgameproperties">
    <div class="accountTabsConpy">
      <ul class="accountTabsUL" :style="{ height: stateTab.foldTabs ? 'auto' : '49px' }">
        <li :class="state.dataForm.gameCode == item.code ? 'codeActive' : ''" @click="handleNameClick(item)" v-for="(item, index) in stateTab.allGameList" :key="index">
          <span>{{ item.name }}</span>
        </li>
      </ul>
      <div class="icons" v-if="stateTab.moreTabs" @click="stateTab.foldTabs = !stateTab.foldTabs">
        <el-icon v-show="!stateTab.foldTabs"><ArrowDown /></el-icon>
        <el-icon v-show="stateTab.foldTabs"><ArrowUp /></el-icon>
      </div>
    </div>
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card" style="margin-bottom: 15px">
      <ny-form-slot>
        <template v-slot:content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.name" placeholder="属性名称" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <ny-select v-model="state.dataForm.type" dict-type="game_properties_type" placeholder="属性类型"></ny-select>
            </el-form-item>
            <el-form-item>
              <ny-select v-model="state.dataForm.status" dict-type="game_properties_status" placeholder="属性状态"></ny-select>
            </el-form-item>
          </el-form>
        </template>
        <template v-slot:button>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        <el-button v-if="state.hasPermission('shop:tbgameproperties:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button v-if="state.hasPermission('shop:tbgameproperties:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
        <el-button type="success" @click="moneyPreview = true">导入</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" class="elTable">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="gameCode" label="游戏编码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="code" label="编码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="name" label="属性" header-align="center" align="center"></el-table-column>
        <el-table-column prop="propertyField" label="映射字段" header-align="center" align="center"></el-table-column>
        <el-table-column prop="status" label="属性状态" header-align="center" align="center">
          <template v-slot="scope">
            {{ state.getDictLabel("game_properties_status", scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="属性类型" header-align="center" align="center">
          <template v-slot="scope">
            {{ state.getDictLabel("game_properties_type", scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建者ID" header-align="center" align="center" width="200"> </el-table-column>
        <el-table-column prop="updater" label="更新者ID" header-align="center" align="center" width="200"> </el-table-column>
        <el-table-column prop="sort" label="排序" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="210">
          <template v-slot="scope">
            <el-button v-if="state.hasPermission('shop:tbgameproperties:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('shop:tbgameproperties:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
            <el-button text bg class="drag"
              ><el-icon><Rank /></el-icon
            ></el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入 -->
    <el-dialog v-model="moneyPreview" class="ba-upload-preview" title="导入文件" @close="closeMoneyPreview" width="35%">
      <div style="margin-left: 20px">
        <p>
          为了保证数据导入顺利，请先
          <el-button v-blur @click="downloadExport" plain style="margin: 0; padding: 0; color: var(--color-primary); border: none">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
          ，并按照规范示例导入数据
        </p>
        <el-upload
          ref="uploadRefs"
          drag
          :limit="1"
          :auto-upload="false"
          action=""
          accept=".xlsx, .xls"
          :on-exceed="exceedFile"
          :on-error="handleError"
          :on-success="handleSuccess"
          :before-upload="beforeUPload"
          :show-file-list="true"
          v-model:file-list="fileList"
          class="uploadRefs"
          style="margin: 20px 0"
        >
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              moneyPreview = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, onMounted, reactive, ref, toRefs, onUnmounted, watch } from "vue";
import AddOrUpdate from "./tbgameproperties-add-or-update.vue";
import baseService from "@/service/baseService";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import { ElMessage, UploadUserFile } from "element-plus";
import Sortable from "sortablejs";
import { fileExport } from "@/utils/method";
import app from "@/constants/app";
import { getToken } from "@/utils/cache";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const view = reactive({
  getDataListURL: "/shop/tbgameproperties/page",
  getDataListIsPage: true,
  exportURL: "/shop/tbgameproperties/export",
  deleteURL: "/shop/tbgameproperties",
  deleteIsBatch: true,
  dataForm: {
    name: "",
    type: "",
    status: "",
    gameCode: "",
    gameId: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const stateTab: {
  gameList: any;
  allGameList: any[];
  moreTabs: boolean;
  foldTabs: boolean;
} = reactive({
  gameList: {},
  allGameList: [],
  moreTabs: false,
  foldTabs: false
});

const moneyPreview = ref(false); // 导航栏弹窗显隐
const fileList = ref([]); //
const fileUrl = ref("");

watch(
  () => state.dataForm.gameId,
  (newValue) => {
    fileUrl.value = `${app.api}/shop/tbgame/importExcel?token=${getToken()}?gameId=${state.dataForm.gameId}`;
    console.log(newValue, fileUrl.value);
  }
);

// 导入操作 ---- 开始 ----
// 关闭对话框
const closeMoneyPreview = () => {
  moneyPreview.value = false;
};
// 下载模板
const downloadExport = () => {
  baseService
    .get("/shop/tbgameproperties/exportMode")
    .then((res) => {
      if (res) {
        fileExport(res, "游戏列表模板");
      } else {
        ElMessage.error("下载失败");
      }
    })
    .catch((err) => {
      ElMessage.error("操作失败");
    });
};
// 数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};
// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: UploadUserFile) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 文件上传   确认导入按钮
const uploadExcel = (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  baseService
    .post("/shop/tbgameproperties/importExcel", { file: multipartFile, gameId: state.dataForm.gameId }, { "Content-Type": "multipart/form-data" })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      moneyPreview.value = false;
    });
};

// 导入操作 ---- 结束 ----

onMounted(() => {
  getGameList();
  initSort();
});

// 重置操作
const getResetting = () => {
  state.dataForm.name = "";
  state.dataForm.type = "";
  state.dataForm.status = "";
  state.getDataList();
};

// 获取游戏列表
const getGameList = () => {
  baseService.get("/shop/tbgame/gameList").then((res) => {
    state.dataForm.gameCode = res.data[0].code;
    state.dataForm.gameId = res.data[0].id;
    stateTab.allGameList = res.data;
    handleResize();
    window.addEventListener("resize", handleResize); // 监听窗口大小变化
    state.getDataList();
  });
};
// 计算游戏列表大小
const handleResize = () => {
  const sxWidth2: number = (document.querySelector(".accountTabsConpy") as HTMLElement).offsetWidth;
  let allFontLen = 0;
  stateTab.allGameList.forEach((item: any) => {
    allFontLen += Number(item.name.length * 13 + 48);
  });
  if (Number((sxWidth2 / allFontLen).toFixed(2)) < 1) {
    stateTab.moreTabs = true;
    stateTab.foldTabs = false;
  } else {
    stateTab.moreTabs = false;
    stateTab.foldTabs = true;
  }
};
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 顶部切换游戏名称
const handleNameClick = (item: any) => {
  console.log(item);
  state.dataForm.gameCode = item.code;
  state.dataForm.gameId = item.id;
  state.dataForm.name = "";
  state.dataForm.type = "";
  state.dataForm.status = "";
  state.getDataList();
};

// 初始化拖拽
const initSort = () => {
  const table: any = document.querySelector(".elTable .el-table__body-wrapper tbody");
  Sortable.create(table, {
    animation: 150,
    handle: ".drag",
    ghostClass: "",
    onStart: (item: any) => {},
    // 结束拖动事件
    onEnd: (item: any) => {
      setNodeSort(item.oldIndex, item.newIndex);
    }
  });
};

// 排序操作
const setNodeSort = (oldIndex: any, newIndex: any) => {
  const oldID = state.dataList[oldIndex].id;
  const newID = state.dataList[newIndex].id;
  baseService
    .get("/shop/tbgameproperties/sort", { sourceId: oldID, targetId: newID, gameId: state.dataForm.gameId })
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("操作成功");
        state.getDataList();
      } else {
        ElMessage.error("操作失败");
      }
    })
    .catch((err) => {
      ElMessage.error("操作失败");
    });
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
<style lang="less" scoped>
@import "/src/assets/theme/base.less";
.accountTabsConpy {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 4px 4px 0px 0px;
  background-color: #fff;
  // margin-bottom: 15px;

  .accountTabsUL {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0px;
    padding-left: 0px;

    li {
      padding: 0 20px 15px;
      margin: 15px 0 0;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      box-sizing: border-box;
      transition: all 0.1s;
    }

    .codeActive {
      color: @--color-primary;
      border-bottom: 2px solid @--color-primary;
    }
  }

  .icons {
    padding: 0 15px;
    cursor: pointer;

    .el-icon {
      font-size: 18px;
      margin-top: 18px;
      color: #606266 !important;
    }
  }
}
</style>
