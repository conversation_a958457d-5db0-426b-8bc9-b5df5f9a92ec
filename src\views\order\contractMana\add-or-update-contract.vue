<template>
  <el-drawer v-model="visible"  size="768" class="infoDrawer">
    <template #header>
      <div class="drawer_title">{{ dataForm.id ? "编辑" : "创建" }}合同</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
        <div class="basicInfoSty">
          <div class="title">基本信息</div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="合同标题名称" prop="name1">
                <el-input v-model="dataForm.name1" placeholder="请输入合同标题名称" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签约截止时间" prop="name2">
                <el-date-picker :prefix-icon="Calendar" v-model="dataForm.name2" type="datetime" placeholder="请选择截止时间" format="YYYY-MM-DD HH:mm:ss" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否使用模板" prop="name3">
                <el-radio-group v-model="dataForm.name3">
                  <el-radio :value="1">使用</el-radio>
                  <el-radio :value="2">不使用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="dataForm.name3 == 1">
              <el-form-item label="选择模板" prop="name4">
                <el-select v-model="dataForm.name4" placeholder="请选择模板" clearable>
                  <el-option v-for="item in []" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <template v-else>
              <el-col :span="12">
                <el-form-item label="上传合同文件" prop="name7">
                  <ny-upload-file :limit="1" v-model:fileSrc="dataForm.name7" widthUpload="354px" heightUpload="185px" tip="" :isOneLine="true" accept="*"></ny-upload-file>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </template>
            <el-col :span="12">
              <el-form-item label="选择签章" prop="name5">
                <el-select v-model="dataForm.name5" placeholder="请选择签章" clearable>
                  <el-option v-for="item in []" :key="item.id" :label="item.name" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="basicInfoSty">
          <div class="title">
            <span>添加签约方</span>
            <div>
              <el-button type="primary" plain @click="addPersonalorEnterprise(1)">添加签约个人</el-button>
              <el-button type="primary" plain @click="addPersonalorEnterprise(2)">添加签约企业</el-button>
            </div>
          </div>
          <div class="collapseItemSty" v-for="(item, itemIndex) in dataForm.name6" :key="itemIndex">
            <div class="itemtitle">
              <span>
                {{ item.type == 1 ? "签约个人" : "签约企业" }}
              </span>
              <div style="cursor: pointer" @click="item.isActive = !item.isActive" class="icon-ele">
                <el-dropdown
                  :disabled="item.type == 1"
                  @command="
                    (e) => {
                      item.signtype = e;
                    }
                  "
                >
                  <span class="el-dropdown-link">
                    <span>{{ item.signtype == 1 ? "签字" : "盖章" }}</span>
                    <el-icon class="el-icon--right">
                      <CaretBottom />
                    </el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="1">签字</el-dropdown-item>
                      <el-dropdown-item command="2">盖章</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-icon style="margin-left: 10px; color: #f56c6c" @click.stop="delPersonalorEnterprise(itemIndex)"><DeleteFilled /></el-icon>
              </div>
            </div>
            <div class="itemContent">
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item :label="'签约主体：' + (item.type == 1 ? '个人' : '企业')" :prop="`name6.${itemIndex}.name_1`" required :rules="rules.inputValite">
                    <el-input v-model="item.name_1" :placeholder="item.type == 1 ? '请输入姓名' : '请输入企业名称'" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="item.type == 1 ? '身份证号' : '经办人姓名'" :prop="`name6.${itemIndex}.name_2`" required :rules="rules.inputValite">
                    <el-input v-model="item.name_1" :placeholder="item.type == 1 ? '用于签约身份核对' : '请输入经办人姓名'" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="接收手机/邮箱" :prop="`name6.${itemIndex}.name_3`" required :rules="rules.inputValite">
                    <el-input v-model="item.name_1" placeholder="请输入手机/邮箱" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDrawer">取消</el-button>
      <el-button style="margin-left: 8px" :loading="replyLoading" @click="dataFormSubmitHandle" type="primary">生成合同</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
import { Calendar } from "@element-plus/icons-vue";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const replyLoading = ref(false);
const requestLoading = ref(false);
const dataFormRef = ref();
const gameList = ref([]);
const dataForm = reactive({
  name1: undefined,
  name2: undefined,
  name3: 1,
  name4: undefined,
  name5: undefined,
  name6: <any>[],
  name7: undefined
});

const rules = ref(<any>{
  name1: [{ required: true, message: "请输入合同标题名称", trigger: "blur" }],
  name2: [{ required: true, message: "请选择截止时间", trigger: "change" }],
  name3: [{ required: true, message: "请选择", trigger: "change" }],
  name4: [{ required: true, message: "请选择模板", trigger: "change" }],
  name5: [{ required: true, message: "请选择签章", trigger: "change" }],
  name7: [{ required: true, message: "请选择合同文件", trigger: "change" }],
  inputValite: [{ required: true, message: "请输入", trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  delete dataForm.id;
  if (id) {
    dataForm.id = id;
    getInfo(id);
    return;
  }
  dataForm.name3 = 1;
  dataForm.name7 = "https://oss.nyyyds.com/upload/20241114/18404560db754b7795afc30b3a7f1104.png";
};

// 获取游戏下拉
const getgameList = () => {
  // baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
  //   gameList.value = res.data;
  // });
};
getgameList();
// 获取信息
const getInfo = (id: number) => {
  requestLoading.value = true;
  baseService.get("/group/sysqqgroup/" + id).then((res) => {
    requestLoading.value = false;
    Object.assign(dataForm, res.data);
  });
};
// 添加签约form
const addPersonalorEnterprise = (type: Number) => {
  dataForm.name6.push({
    type,
    signtype: type,
    isActive: true,
    name_1: undefined,
    name_2: undefined,
    name_3: undefined
  });
};
// 删除签约项
const delPersonalorEnterprise = (index: Number) => {
  dataForm.name6.splice(index, 1);
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let gameObj = gameList.value.find((ele: any) => ele.id == dataForm.gameId);
    dataForm.gameName = gameObj.title;
    (!dataForm.id ? baseService.post : baseService.put)("/group/sysqqgroup", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

const closeDrawer = () => {
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  visible.value = false;
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: block;
}
.setUptable {
  width: 100%;
  :deep(.el-table) {
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .title {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-button {
      height: 20px;
    }
  }

  .el-form-item {
    display: block;
    margin-bottom: 12px;
  }
}
.collapseItemSty {
  background: #f1f6fa;
  margin-bottom: 12px;
  border-radius: 4px;

  .itemtitle {
    background: #ebf3f9;
    padding: 7px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 400;
    font-size: 14px;
    color: #303133;

    > span {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
    }
  }

  .itemContent {
    padding: 10px 12px;
    padding-bottom: 0;
  }
}
</style>
<style lang="scss">
.infoDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
