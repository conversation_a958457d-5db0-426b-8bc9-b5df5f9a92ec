<template>
  <el-drawer v-model="visible" :footer="null" :title="props.gameInfo?.title + '-估价公式'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="944" class="ny-drawer">
    <div class="drawer-card">
      <ny-table cellHeight="ch-40" :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header-custom>
          <div class="mb-12">
            <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
          </div>
        </template>
        <template #createDate="{ row }">
          {{ formatDate(row.createDate) }}
        </template>
        <template #state="{ row }">
          <el-switch @click="submitStatus(row)" v-model="row.state" :active-value="0" :inactive-value="1" />
        </template>
        <!-- 操作 -->
        <template #operation="{ row, $index }">
          <el-link type="primary" @click="addOrUpdateHandle(row)">{{ row.isOperate ? "保存" : "编辑" }}</el-link>
          <el-link type="danger" @click="state.deleteHandle(row.id)" style="margin-left: 12px">删除</el-link>
        </template>
      </ny-table>
    </div>
    <editFormula ref="addOrUpdateRef" @formulaChange="submitForm" />
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, reactive, toRefs, nextTick } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { formatDate } from "@/utils/method";
import editFormula from "./editFormula.vue";
const props = defineProps({
  gameInfo: {},
  attributeList: []
});
const visible = ref(false);
const view = reactive({
  getDataListURL: "/appraise/newFormula/page",
  getDataListIsPage: true,
  deleteURL: "/appraise/newFormula/delete",
  deleteIsBatch: true,
  dataForm: {
    appraiseAttributeId: "",
    gameId: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
  {
    prop: "formulaName",
    label: "公式名称",
    minWidth: 140
  },
  {
    prop: "formulaContent",
    label: "公式内容",
    minWidth: 140
  },
  {
    prop: "createDate",
    label: "创建时间",
    width: 160
  },
  {
    prop: "state",
    label: "是否启用",
    width: 138
  },
  {
    prop: "operation",
    label: "操作",
    width: 100
  }
]);

const init = (gid?: any) => {
  visible.value = true;
  state.dataForm.gameId = gid;
  state.getDataList();
};
const btnLoading = ref(false);
// 更新状态
const submitStatus = (form: any) => {
  baseService
    .put("/appraise/newFormula/updateState", { id: form.id })
    .then((res) => {
      if (res.code === 0) {
        state.getDataList();
        ElMessage.success(res.msg);
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
const submitForm = (form: any) => {
  baseService[form.id ? "put" : "post"]("/appraise/newFormula", form)
    .then((res) => {
      if (res.code === 0) {
        state.getDataList();
        ElMessage.success(res.msg);
        nextTick(() => {
          addOrUpdateRef.value.close();
        });
      }
    })
    .finally(() => {
      btnLoading.value = false;
      nextTick(() => {
        addOrUpdateRef.value.closeLoading();
      });
    });
};
// 新增  编辑
const addOrUpdateRef = ref();
const addOrUpdateKey = ref(0);
const addOrUpdateHandle = (row?: any) => {
  addOrUpdateKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(row, state.dataForm.gameId);
  });
};
defineExpose({
  init
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
.el-drawer__body {
  padding: 12px;
}
</style>
