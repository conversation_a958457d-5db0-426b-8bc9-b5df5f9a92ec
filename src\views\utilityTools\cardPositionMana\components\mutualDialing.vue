<template>
    <el-dialog :footer="null" v-model="visibleShow" title="互拨配置" :close-on-click-modal="false" width="480">
        <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-position="top" label-width="80px">
            <el-descriptions class="descriptions descriptions-label-140" style="width: 100%" border :column="1">
                <el-descriptions-item>
                    <template #label>
                        <span>任务类型<span style="color: red">*</span></span>
                    </template>
                    互拨电话
                </el-descriptions-item>
                <el-descriptions-item>
                    <template #label>
                        <span>间隔周期(天)<span style="color: red">*</span></span>
                    </template>
                    <el-form-item label="间隔周期(天)" prop="intervalDay">
                        <el-input-number v-model="dataForm.intervalDay" :min="0" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template #label>
                        <span>执行时间<span style="color: red">*</span></span>
                    </template>
                    <el-form-item label="执行时间" prop="value16">
                        <el-date-picker :prefix-icon="Calendar" v-model="dataForm.executeTime" type="date" placeholder="请选择执行时间" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template #label>
                        <span>启用<span style="color: red">*</span></span>
                    </template>
                    <el-form-item label="启用" prop="status">
                        <el-switch inline-prompt v-model="dataForm.status" :active-value="1" :inactive-value="0"  active-text="是" inactive-text="否" />
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>

        <div style="height: 60px"></div>
        <template #footer>
            <el-button :loading="submitLoading" @click="visibleShow = false">{{ $t("cancel") }}</el-button>
            <el-button :loading="submitLoading" type="primary" @click="submitThreshold()">确定</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { Calendar } from "@element-plus/icons-vue";

const emit = defineEmits(["refresh"]);

// 设置
const visibleShow = ref(false);
const dataForm = ref(<any>{});
const rules = {
  value1: [{ required: true, message: "请选择任务类型", trigger: "blur" }],
  value2: [{ required: true, message: "请输入", trigger: "blur" }],
  value3: [{ required: true, message: "请选择", trigger: "blur" }]
};

const dataFormRef = ref();
const submitLoading = ref(false);
const submitThreshold = () => {
    dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      baseService[dataForm.value.id ? "put" : "post"]("/mobile/mobileTaskConfig", dataForm.value)
        .then((res) => {
          if (res.code === 0) {
            visibleShow.value = false;
            ElMessage.success(res.msg);
            emit("refresh");
          }
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
};

const init = () => {
    visibleShow.value = true;
    baseService.get("/mobile/mobileTaskConfig/getMutualDialingConfig").then(res=>{
        console.log(res);
        if(res.code == 0){
            dataForm.value = res.data;
        }
    })
};

defineExpose({
    init
});
</script>

<style lang="less" scoped>
.basicInfoSty {
    padding: 12px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 12px;

    .title {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
        padding-left: 8px;
        border-left: 2px solid var(--el-color-primary);
        margin-bottom: 12px;
    }

    .tipinfo {
        :deep(.el-descriptions__label) {
            width: 144px;
            background: #f5f7fa;
            font-family: Inter, Inter;
            font-weight: 500;
            font-size: 14px;
            color: #606266;
            padding: 9px 12px;
            border: 1px solid #ebeef5;
        }
    }
}

:deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;

    tbody {
        display: flex;
        flex-direction: column;

        tr {
            display: flex;
            flex: 1;

            .el-descriptions__label {
                display: flex;
                align-items: flex-start !important;
                font-weight: normal;
                width: 144px;
            }

            .el-descriptions__content {
                display: flex;
                align-items: center;
                min-height: 48px;
                flex: 1;

                >div {
                    width: 100%;
                }

                .el-form-item__label {
                    display: none;
                }

                .el-form-item {
                    margin-bottom: 0;
                }
            }

            .noneSelfRight {
                border-right: 0 !important;
            }

            .noneSelfLeft {
                border-left: 0 !important;
            }

            .noneSelfLabel {
                background: none;
                border-left: 0 !important;
                border-right: 0 !important;
            }
        }
    }
}
</style>
