<template>
  <el-drawer v-model="visible" title="商品详情" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%">
    <div v-loading="requestLoading">
      <el-descriptions :column="1" border class="descriptions descriptions-label-140">
        <el-descriptions-item>
          <template #label>商品编码</template>
          {{ dataForm.code }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>游戏名称</template>
          {{ dataForm.gameName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>系统区服</template>
          {{ dataForm.serverName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>商品标题</template>
          {{ dataForm.title }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>零售价</template>
          ￥{{ dataForm.price }}
        </el-descriptions-item>
        <el-descriptions-item v-for="(item, index) in dataForm.attributesList" :key="index">
          <template #label>{{ item.name }}</template>
          <span v-if="item.type == 3">{{ item.attributeText }}</span>
          <span v-else>{{ idQueryName(item.attributeIds, item.children) }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>游戏主图</template>
          <el-image style="height: 100px" :src="dataForm.log" :preview-src-list="[dataForm.log]" preview-teleported />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>详情图片</template>
          <el-image style="height: 100px" v-for="(item, index) in dataForm.imagesList" :src="item" :preview-src-list="dataForm.imagesList" preview-teleported />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>商品描述</template>
          <span style="white-space: pre-wrap">{{ dataForm.info }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref } from "vue";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import arrowIcon from "@/assets/images/tagicon.png";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const visible = ref(false); // 对话框显隐
const dataForm = reactive({
  // 表单变量
  id: null,
  code: "",
  gameId: "",
  gameName: "",
  gameCode: "",
  highlights: "",
  title: "",
  price: 0,
  acquisitionPrice: 0,
  phone: "",
  gameAccount: "",
  gamePassword: "",
  log: [],
  imagesList: [],
  info: "",
  gameAre: "",
  server: "",
  serverName: "",
  compensation: "",
  bargain: "",
  topped: "",
  attributesList: <any>[]
});

// 表单初始化
const init = (row: any) => {
  visible.value = true;
  dataForm.id = row.shopId || row.id;
  dataForm.attributesList = [];

  getInfo();
};

// 加载详情
const requestLoading = ref(false); // 详情加载
const getInfo = () => {
  requestLoading.value = true;
  baseService
    .get("/shop/shop/" + dataForm.id)
    .then((res) => {
      const data = res.data;
      dataForm.gameId = data.gameId;
      dataForm.code = data.code;
      dataForm.gameName = data.gameName;
      dataForm.serverName = data.serverName;
      dataForm.title = data.title;
      dataForm.price = data.price;
      dataForm.imagesList = data.images ? JSON.parse(data.images) || [] : [];
      dataForm.info = data.info;
      dataForm.log = data.log;

      // 处理属性回显
      baseService.get("/game/attribute/page", { gameId: dataForm.gameId }).then((res) => {
        dataForm.attributesList = [];
        res.data.map((item: any) => {
          dataForm.attributesList.push({
            typeId: item.id,
            attributeIds: [],
            attributeNames: "",
            attributeText: "",
            children: item.children,
            isTitle: item.isTitle,
            type: item.type,
            name: item.name
          });
        });
        data.attributesList.forEach((ele: any) => {
          dataForm.attributesList.forEach((item: any) => {
            if (item.typeId == ele.typeId) {
              if (item.type == 1 || item.type == 2) {
                item.attributeIds = ele.attributeIds;
              }
              if (item.type == 3) {
                item.attributeText = ele.attributeText;
              }
              Object.assign(ele, item);
            }
          });
        });
      });

      console.log(dataForm);
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

// 属性ID查询属性名称
const idQueryName = (value: any, data: any[]) => {
  const names: any = [];
  value.forEach((id: string) => {
    data.forEach((item: any) => {
      if (item.id == id) {
        names.push(item.name);
      }
    });
  });
  return names.join("，");
};

defineExpose({
  init
});
</script>