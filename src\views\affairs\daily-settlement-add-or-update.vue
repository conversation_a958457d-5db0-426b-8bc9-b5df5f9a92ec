<template>
  <el-dialog v-model="visible" :title="getDialogTitle()" :close-on-click-modal="false" :close-on-press-escape="false" width="900px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="140px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资金账户" prop="xzzjzh">
            <el-input v-model="dataForm.xzzjzh" placeholder="请输入资金账户" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账户用途" prop="zhyt">
            <el-select v-model="dataForm.zhyt" placeholder="请选择账户用途" style="width: 100%" :disabled="isView">
              <el-option label="店铺银行卡" value="店铺银行卡"></el-option>
              <el-option label="收款支付宝" value="收款支付宝"></el-option>
              <el-option label="店铺支付宝" value="店铺支付宝"></el-option>
              <el-option label="备用金账户" value="备用金账户"></el-option>
              <el-option label="公户银行卡" value="公户银行卡"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账户户名" prop="zhhm">
            <el-input v-model="dataForm.zhhm" placeholder="请输入账户户名" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="khyh">
            <el-input v-model="dataForm.khyh" placeholder="请输入开户银行" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="联动资金余额" prop="ldzjye">
            <el-input-number v-model="dataForm.ldzjye" placeholder="请输入联动资金余额" :precision="2" :min="0" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昨日收入总额" prop="zrsrze">
            <el-input-number v-model="dataForm.zrsrze" placeholder="请输入昨日收入总额" :precision="2" :min="0" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="昨日支出总额" prop="zrzcze">
            <el-input-number v-model="dataForm.zrzcze" placeholder="请输入昨日支出总额" :precision="2" :min="0" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昨日期初余额" prop="zrqcye">
            <el-input-number v-model="dataForm.zrqcye" placeholder="请输入昨日期初余额" :precision="2" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="昨日期末余额" prop="zrqmye">
            <el-input-number v-model="dataForm.zrqmye" placeholder="昨日期末余额" :precision="2" style="width: 100%" :disabled="true"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昨日期末待收货金额" prop="zrqmdshje">
            <el-input-number v-model="dataForm.zrqmdshje" placeholder="请输入昨日期末待收货金额" :precision="2" :min="0" style="width: 100%" :disabled="isView"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="审批状态" prop="zt">
            <el-select v-model="dataForm.zt" placeholder="请选择审批状态" style="width: 100%" :disabled="isView">
              <el-option label="待审批" value="待审批"></el-option>
              <el-option label="同意" value="同意"></el-option>
              <el-option label="拒绝" value="拒绝"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图片上传" prop="tpsc">
            <el-upload
              v-model:file-list="fileList"
              :action="uploadAction"
              :headers="uploadHeaders"
              :on-success="handleUploadSuccess"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              :disabled="isView"
              list-type="picture-card"
              :limit="5">
              <el-icon v-if="!isView"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button v-if="!isView" type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { getToken } from "@/utils/cache";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const isView = ref(false);
const fileList = ref([]);

const dataForm = reactive({
  id: "",
  xzzjzh: "",
  zhyt: "",
  zhhm: "",
  khyh: "",
  ldzjye: 0,
  zrsrze: 0,
  zrzcze: 0,
  zrqcye: 0,
  zrqmye: 0,
  zrqmdshje: 0,
  zt: "待审批",
  tpsc: []
});

const rules = ref({
  xzzjzh: [{ required: true, message: "资金账户不能为空", trigger: "blur" }],
  zhyt: [{ required: true, message: "账户用途不能为空", trigger: "change" }],
  zhhm: [{ required: true, message: "账户户名不能为空", trigger: "blur" }],
  khyh: [{ required: true, message: "开户银行不能为空", trigger: "blur" }]
});

// 上传配置
const uploadAction = `${import.meta.env.VITE_API_URL}/api/file/upload`;
const uploadHeaders = {
  Authorization: `Bearer ${getToken()}`
};

// 计算对话框标题
const getDialogTitle = () => {
  if (isView.value) return "日清日结详情";
  return dataForm.id ? "编辑日清日结" : "新增日清日结";
};

// 监听收入和支出变化，自动计算期末余额
watch([() => dataForm.zrqcye, () => dataForm.zrsrze, () => dataForm.zrzcze], () => {
  dataForm.zrqmye = (dataForm.zrqcye || 0) + (dataForm.zrsrze || 0) - (dataForm.zrzcze || 0);
});

const init = (id?: number, viewMode?: boolean) => {
  visible.value = true;
  isView.value = !!viewMode;
  fileList.value = [];

  // 重置表单数据
  Object.assign(dataForm, {
    id: "",
    xzzjzh: "",
    zhyt: "",
    zhhm: "",
    khyh: "",
    ldzjye: 0,
    zrsrze: 0,
    zrzcze: 0,
    zrqcye: 0,
    zrqmye: 0,
    zrqmdshje: 0,
    zt: "待审批",
    tpsc: []
  });

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get(`/finance/daily-settlement/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
    
    // 处理图片数据
    if (res.data.tpsc && Array.isArray(res.data.tpsc)) {
      fileList.value = res.data.tpsc.map((item: any, index: number) => ({
        uid: index,
        name: item.name || `图片${index + 1}`,
        url: item.url,
        thumbUrl: item.thumbUrl
      }));
    }
  });
};

// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 上传成功回调
const handleUploadSuccess = (response: any, file: any) => {
  if (response.code === 0) {
    const uploadedFile = {
      name: file.name,
      url: response.data.url,
      thumbUrl: response.data.thumbUrl || response.data.url,
      fileId: response.data.fileId
    };
    dataForm.tpsc.push(uploadedFile);
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 移除文件
const handleRemove = (file: any) => {
  const index = dataForm.tpsc.findIndex((item: any) => item.url === file.url);
  if (index > -1) {
    dataForm.tpsc.splice(index, 1);
  }
};

const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    const request = !dataForm.id ?
      baseService.post("/finance/daily-settlement", dataForm) :
      baseService.put("/finance/daily-settlement", dataForm);

    request.then((res) => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 80px;
  height: 80px;
}
</style>
