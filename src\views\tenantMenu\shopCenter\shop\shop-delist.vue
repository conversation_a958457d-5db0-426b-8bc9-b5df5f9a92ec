<template>
  <el-dialog v-model="visible" :title="delistForm.sold ? '商品售出确认' : '下架'" :close-on-click-modal="false" :close-on-press-escape="false" width="30%">
    <el-form ref="delistFormRef" :model="delistForm" :rules="delistRules" label-width="120px" status-icon v-if="delistForm.sold">
      <el-row>
        <el-col :span="22">
          <el-form-item label="成交价" prop="transactionPrice">
            <el-input v-model="delistForm.transactionPrice" placeholder="成交价" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form ref="delistFormRef" :model="delistForm" :rules="delistRules" label-width="120px" status-icon v-else>
      <el-row>
        <el-col :span="22">
          <el-form-item label="下架原因：" prop="types">
            <el-select v-model="delistForm.types" placeholder="请选择下架原因" @change="delistForm.remark = ''">
              <el-option label="用户下架" value="用户下架" />
              <el-option label="平台下架" value="平台下架" />
              <el-option label="账号问题" value="账号问题" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="原因" prop="remark" v-if="delistForm.types == '账号问题'">
            <el-input v-model="delistForm.remark" placeholder="请输入原因" />
          </el-form-item>
          <el-form-item label="备注" prop="remark" v-if="delistForm.types == '其他'">
            <el-input v-model="delistForm.remark" placeholder="请输入下架原因" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" type="primary" @click="SubmitEvent()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false); // 对话框显隐

const delistFormRef = ref();
const delistForm = reactive({
  types: "",
  remark: "",
  status: 2,
  id: null,
  sold: "",
  transactionPrice: ""
});
const delistRules = ref({
  types: [{ required: true, message: "请选择下架原因", trigger: "change" }],
  remark: [{ required: true, message: "请输入下架原因" }],
  transactionPrice: [{ required: true, message: "请输入成交价" }]
});

// 弹窗初始化
const init = (id: any, sold?: string) => {
  visible.value = true;
  delistForm.id = id;
  delistForm.sold = sold ? sold : "";
};

// 提交
const btnLoading = ref(false);
const SubmitEvent = () => {
  delistFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (delistForm.sold) {
      const params = {
        id: delistForm.id,
        status: 4,
        transactionPrice: delistForm.transactionPrice,
        // 创建订单，商品列表点击出售时传true
        createOrder: true
      };
      btnLoading.value = true;
      baseService
        .post("/shop/shop/status", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });
    } else {
      const params = {
        id: delistForm.id,
        status: 3,
        delistingRemark: delistForm.types
      };
      if (delistForm.types == "账号问题" || delistForm.types == "其他") {
        params.delistingRemark = delistForm.remark;
      }
      btnLoading.value = true;
      baseService
        .post("/shop/shop/status", params)
        .then((res) => {
          ElMessage.success({
            message: t("prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

defineExpose({
  init
});
</script>

<style lang='less' scoped>
</style>