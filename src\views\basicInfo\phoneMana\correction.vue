<template>
  <el-drawer @close="visible = false" :footer="null" v-model="visible" size="944" class="infoDrawer">
    <template #header>
      <div class="drawer_title">手机号详情</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="basicInfoSty">
        <div class="title">基本信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>手机号</div> </template>
            {{ state.propForm.phone }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>绑定数量</div> </template>
            {{ state.propForm.total }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>状态</div> </template>
            <el-tag v-if="state.propForm.status == 0" type="danger">废弃</el-tag>
            <el-tag v-if="state.propForm.status == 1" type="success">正常</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty" style="min-height: 73vh">
        <div class="title" style="display: flex; align-items: center; justify-content: space-between">
          <span>绑定商品详情</span>
          <div v-show="state.showOperation">
            <el-button style="height: 20px" @click="changeBtn(false)">取消</el-button>
            <el-button style="height: 20px" @click="handleSave" plain type="primary">保存</el-button>
          </div>
          <div v-show="state.hasPermission('phone:sysphone:correct') && !state.showOperation">
            <el-button style="height: 20px" @click="changeBtn(true)" plain type="primary">纠偏</el-button>
          </div>
        </div>
        <div style="display: flex; align-items: center; justify-content: flex-end; margin-bottom: 12px">
          <el-form :inline="true">
            <el-form-item style="margin-bottom: 0px">
              <el-input style="width: 280px !important" v-model="state.dataForm.shopTitleKeywords" placeholder="请输入商品名称" clearable :prefix-icon="Search"></el-input>
            </el-form-item>
            <el-form-item style="margin-bottom: 0px">
              <el-select style="width: 160px" clearable v-model="state.dataForm.operatorsId" placeholder="请选择所属系别"> <el-option v-for="item in operatorsGameList" :key="item.id" :label="item.operatorsName" :value="item.id" /> </el-select
            ></el-form-item>
            <el-button type="primary" @click="state.getDataList()">查询</el-button>
            <el-button class="ml-8" @click="getResetting">重置</el-button>
          </el-form>
        </div>
        <div class="setUptable">
          <el-table border ref="tableRef" :data="state.dataList" style="width: 100%">
            <el-table-column show-overflow-tooltip prop="shopTitle" label="商品名称" align="center">
              <template #default="scope">
                <el-text @click="jumpGoodsDetails(scope.row)" style="cursor: pointer" type="primary">{{ scope.row.shopTitle }}</el-text>
              </template>
            </el-table-column>
            <el-table-column width="184" show-overflow-tooltip prop="gameName" label="游戏名称" align="center" />
            <el-table-column width="110" prop="operatorsName" label="所属系别" align="center"> </el-table-column>
            <el-table-column width="110" prop="shopStatus" label="商品状态" align="center">
              <template #default="scope">
                <template v-if="scope.row.shopStatus">
                  <el-tag :type="shopStatus[+scope.row.shopStatus].type">{{ shopStatus[+scope.row.shopStatus].name }}</el-tag>
                </template>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column v-if="state.showOperation" width="146" prop="type" label="操作" align="center">
              <template #default="scope">
                <!-- 状态 -->
                <el-link v-if="state.hasPermission('phone:sysphonegame:update')" :underline="false" style="margin-right: 12px" @click="editHandle(scope.row, true)" type="primary">更换</el-link>
                <el-link v-if="state.hasPermission('phone:sysphonegame:delete')" :underline="false" @click="delData(scope.row.id)" type="danger">删除</el-link>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <div style="padding: 68px 0">
                <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
              </div>
            </template>
          </el-table>
          <el-pagination
            :current-page="state.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :page-size="state.limit"
            :total="state.total"
            layout="total, sizes, prev, pager, next, jumper"
            :hide-on-single-page="true"
            @size-change="state.pageSizeChangeHandle"
            @current-change="state.pageCurrentChangeHandle"
          ></el-pagination>
        </div>
      </div>
    </el-scrollbar>
    <changeBind @refreshDataList="refreshdata" ref="changeBindRef"></changeBind>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
  </el-drawer>
</template>

<script lang="ts" setup>
import { Edit, Search } from "@element-plus/icons-vue";
import { nextTick, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { TableInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import ShopInfo from "@/views/shop/shop-info.vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
// 组件引入
import changeBind from "./change-bind-goods.vue";
const changeBindRef = ref();

const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const tableRef = ref<TableInstance>();
const visible = ref(false); // 对话框显隐
const shopStatus = ref([
  {
    name: "",
    type: "info"
  },
  {
    name: "待上架",
    type: "warning"
  },
  {
    name: "已上架",
    type: "primary"
  },
  {
    name: "已下架",
    type: "danger"
  },
  {
    name: "已出售",
    type: "success"
  },
  {
    name: "问题账号",
    type: "warning"
  },
  {
    name: "作废账号",
    type: "danger"
  },
  {
    name: "交易中",
    type: "primary"
  }
]);
const view = reactive({
  createdIsNeed: false,
  getDataListURL: "/phone/sysphonegame/listInfo",
  getDataListIsPage: true,
  deleteURL: "/phone/sysphonegame",
  deleteIsBatch: true,
  // 传参
  dataForm: {
    order: null,
    orderField: null,
    shopTitleKeywords: null, //状态
    operatorsId: null //系别
  },
  showOperation: false,
  propForm: {} //传递过来的数据
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const operatorsGameList = ref([]);
// 表单初始化
const init = async (data?: any, showBtn?: boolean) => {
  visible.value = true;
  state.propForm = Object.assign({}, data);
  state.showOperation = showBtn;
  state.dataForm.phone = data.phone;
  //   传参处理
  state.dataList = [];
  state.getDataList();
  //   获取运营商下拉数据
  let res = await baseService.get("/operators/sysoperators/page", { limit: 9999 });
  if (res.code == 0) {
    operatorsGameList.value = res.data.list || [];
  }
};
const getResetting = () => {
  state.dataForm.shopTitleKeywords = undefined;
  state.dataForm.operatorsId = undefined;
  state.getDataList();
};
const handleSave = () => {
  ElMessage.success("保存成功！");
  state.showOperation = false;
};
const refreshdata = () => {
  emit("refreshDataList");
  state.getDataList();
};
const delData = (id: any) => {
  ElMessageBox.confirm(t("prompt.info", { handle: t("delete") }), t("prompt.title"), {
    confirmButtonText: t("confirm"),
    cancelButtonText: t("cancel"),
    type: "warning"
  }).then(() => {
    baseService.delete(`${state.deleteURL}${state.deleteIsBatch ? "" : "/" + id}`, [id]).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          refreshdata();
        }
      });
    });
  });
};
// 获取表单详情信息
const requestLoading = ref(false); // 详情加载
// 更换
const editHandle = async (id: any) => {
  await nextTick();
  changeBindRef.value.init(id);
};
// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  if (res.data) {
    shopInfoKey.value++;
    await nextTick();
    shopInfoRef.value.init(res?.data || {});
  }
};
const changeBtn = (show: boolean) => {
  state.showOperation = show;
};
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.button-group {
  width: fit-content;
  background: #f2f2f2;
  border-radius: 6px;
  margin-bottom: 12px;
}
.button-group {
  display: flex;
  padding: 4px;
  height: 40px;

  .button-item {
    flex-shrink: 0;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    &.active {
      background: #fff;
      color: var(--el-color-primary);
    }
  }
}
.setUptable {
  :deep(.el-table) {
    overflow: visible !important;
    th.el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .title {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}
</style>
<style lang="scss">
.infoDrawer {
  .el-drawer__header {
    margin-bottom: 0px;
    padding-bottom: 12px !important;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 12px;
    background: #f0f2f5;
  }
  .el-tag {
    border: 1px solid;
  }
}
</style>
