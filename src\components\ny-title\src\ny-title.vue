<template>
    <div class="ny_title">
        <div class="ny_title_icon"></div>
        <span class="ny_title_text">{{ props.title }}</span>
    </div>
</template>
<script lang="ts">
import { onMounted, defineComponent,ref, watch } from "vue";
export default defineComponent({
    name: "NyTitle",
    props: {
        title: String  // 标题名称
    },
    setup(props) {
        return { props }
    }
});
</script>
<style lang="less" scoped>
@import "/src/assets/theme/base.less";
.ny_title{
    width: 100%;
    display: flex;
    align-items: center;
    .ny_title_icon{
        width: 4px;
        height: 20px;
        border-radius: 10px;
        background-color: @--color-primary;
        margin-right: 10px;
    }
    .ny_title_text{
        font-size: 14px;
        font-weight: bold;
        // color: @--color-primary;
    }
}
</style>