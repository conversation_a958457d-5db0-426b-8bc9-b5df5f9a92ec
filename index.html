<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="revised" content="1.0.8"/>
  <title>
    <%= title %>
  </title>
  <script>
    //全局钩子
    window.SITE_CONFIG = {
      //api
      apiURL: "<%=apiURL%>"
    };
    // function getScreenWidth() {
    //   var screenWidth = window.innerWidth;
    //   let scarl = screenWidth / 1920
    //   document.getElementById('app').style.zoom = scarl
    // }

    // window.onload = function () {
    //   getScreenWidth(); // 页面加载时获取屏幕宽度
    // };
    // window.addEventListener('resize', () => {
    //   getScreenWidth(); // 页面加载时获取屏幕宽度
    // })
  </script>
</head>

<body>
  <div id="app" style="height: 100%;"></div>
  <script src="https://cdn.ronghub.com/RongEmoji-2.2.11.js"></script>
  <script type="module" src="./src/main.ts"></script>
</body>

</html>