<template>
  <div>
    <ny-table cellHeight="ch-56" :showColSetting="false" :state="state" :columns="tableColums" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @sortableChange="sortableChange">
      <!-- 操作 -->
      <template #operation="{ row }">
        <el-link v-if="state.hasPermission('sys:ipBlackList:update')" :underline="false" style="margin-right: 12px" @click="addOrUpdateHandle(row.id)" type="primary">编辑</el-link>
      </template>
    </ny-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./add-or-update.vue";

const view = reactive({
  getDataListURL: "/im/imflow/page",
  getDataListIsPage: true,
  exportURL: "/sys/ipBlackList/export",
  deleteURL: "/sys/ipBlackList",
  deleteIsBatch: true,
  dataForm: {
    type: 1
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const tableColums = reactive([
  {
    prop: "name",
    label: "行为名称",
    width: 320
  },
  {
    prop: "content",
    label: "行为内容",
    showOverflowTooltip: false
  },
  {
    prop: "operation",
    label: "操作",
    width: 240
  }
]);
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
