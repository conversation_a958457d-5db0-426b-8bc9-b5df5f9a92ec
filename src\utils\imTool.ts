import * as RongIMLib from "@rongcloud/imlib-next";
import { ElMessage, ElNotification } from "element-plus";
import { useImStore } from "@/store/im";
import { useSettingStore } from "@/store/setting";
import { useAppStore } from "@/store/index";
import { showJudge, browserTabTitle } from "@/utils/notification";
import router from '@/router';
import { h } from 'vue';
import baseService from "@/service/baseService";
import dayjs from "dayjs";

import msgNotificationApi from "@/assets/images/msg_notification_api.png";
import msgNotificationMoney from "@/assets/images/msg_notification_money.png";
import msgNotificationAnnouncement from "@/assets/images/msg_notification_announcement.png";
import msgNotificationOpen from "@/assets/images/msg_notification_open.png";
import msgNotificationSale from "@/assets/images/msg_notification_sale.png";
import msgNotificationBargain from "@/assets/images/msg_notification_bargain.png";
import msgNotificationContract from "@/assets/images/msg_notification_contract.png";
import msgNotificationRetrieve from "@/assets/images/msg_notification_retrieve.png";


// 声明全局变量
declare global {
    interface Window {
        RongIMLib: typeof RongIMLib;
    }
}

const Events = RongIMLib.Events

// token 过期 重新获取token  @@@
export const reacquireToken = () => {
    return new Promise((resolve, reject) => {
        // 模拟异步请求
        setTimeout(() => {
            resolve('token')
        }, 1000)
    })
}


// 获取会话列表
export const getConversationList = () => {
    const imStore = useImStore();
    
    return new Promise((resolve, reject) => {
        // 如果分页获取会话，第二次应传入返回结果数组中最后一条数据的 IAReceivedConversation 的 latestMessage 的 sendTime
        const startTime = 0;
        // 会话列表数量，默认值 300，最大值 1000。
        const count = 20;
        //0获取最新的会话列表   1获取服务端最早的会话
        const order = 0;

        RongIMLib.getConversationList({
            count: count,
            startTime: startTime,
            order: order
        }).then(async (res: any) => {
            if (res.code === 0) {
                console.log('获取会话', res.code, res.data);
                let list = res.data.filter((item: any) => item.targetId != imStore.systemMessageTargetId)
                // unreadMessageCount 当前会话的未读消息数
                // 置顶状态排在最前面
                list.sort((a, b) => {
                    return b.isTop - a.isTop;
                });
                let data = await getUserInfoByConversationId(list);
                resolve(data);
                // this.subscribeOnlineStatus(['199497']);
            } else {
                resolve([]);
                console.log(res.code, res.msg);
            }
        })
    })
}



// 发送消息
interface IMessage {
    // 会话类型
    conversationType: string | number,
    // 消息类型
    msgType: string,
    // 接收人id
    targetId: string,
    // 消息内容
    content?: string,
    // 附加内容
    extra?: any
    // @类型
    MentionedType?: string,
    // 群聊@消息
    mentionedInfo?: any
}

// 发送消息
export const sendMessage = (obj: IMessage) => {
    return new Promise((resolve, reject) => {
        // PRIVATE: 单聊   1
        // DISCUSSION: 讨论组 2
        // GROUP: 群组  3
        // CHATROOM  聊天室 4 
        // CUSTOMER_SERVICE 客服 5
        // SYSTEM 系统消息 6
        console.log(obj);
        const { conversationType, msgType, targetId, content, extra, mentionedInfo } = obj;

        const conversation = {
            conversationType: conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE,
            targetId: targetId
        }

        // options 定义发送行为中的一些可选配置
        let option = {
            // 如果需要发送 @ 消息，isMentioned 需设置为 true
            isMentioned: false
        };

        let imMessage: any = undefined;

        // 文本消息
        if (msgType === 'TextMessage') {
            if (mentionedInfo && mentionedInfo.type) {
                option.isMentioned = true;
            }
            console.log(mentionedInfo, option)

            imMessage = new RongIMLib.TextMessage({
                // 文本内容
                content: content || '',
                // （可选）消息中附加信息，透传到对端
                extra: extra,

                // 群组消息中，如果需要发送 @ 消息，可添加 mentionedInfo 字段
                // mentionedInfo: {
                //     // @ 类型：ALL 全部、 SINGAL个人 
                //     type: RongIMLib.MentionedType.SINGAL,
                //     // @ 用户列表
                //     // userIdList: [zhangsan],
                //     // @ 内容
                //     mentionedContent: ''
                // }

                mentionedInfo: mentionedInfo || {}
            })
        }

        // 图片消息
        if (msgType === 'ImageMessage') {
            imMessage = new RongIMLib.ImageMessage({
                content: '', // 图片缩略图，应为 Base64 字符串，且不可超过 80KB
                imageUri: content // 图片的远程访问地址
            })
        }

        // 文件消息
        if (msgType === 'FileMessage') {
            imMessage = new RongIMLib.FileMessage({
                // 文件名称
                name: '',
                // 文件尺寸
                size: '',
                // 文件类型
                type: 'MP4',
                fileUrl: content
            })
        }

        // 订单消息
        if (msgType === 'OrderMessage') {
            const PersonMessage = RongIMLib.registerMessageType('RC:IWNormalMsg', true, true, [], false)
            imMessage = new PersonMessage({
                msgType: 'CU:order',
                msgFields: content
            })
        }

        // 合同链接消息
        if (msgType === 'ContractMessage') {
            const PersonMessage = RongIMLib.registerMessageType('RC:IWNormalMsg', true, true, [], false)
            imMessage = new PersonMessage({
                msgType: 'CU:contract',
                msgFields: {
                    content
                }
            })
        }

        // 收集包赔链接消息
        if (msgType === 'CollectMessage') {
            const PersonMessage = RongIMLib.registerMessageType('RC:IWNormalMsg', true, true, [], false)
            imMessage = new PersonMessage({
                msgType: 'CU:collect',
                msgFields: {
                    content
                }
            })
        }
        // 入群链接消息
        if (msgType === 'qqMessage') {
            const PersonMessage = RongIMLib.registerMessageType('RC:IWNormalMsg', true, true, [], false)
            imMessage = new PersonMessage({
                msgType: 'CU:qq',
                msgFields: {
                    content
                }
            })
        }

        // 发送消息
        RongIMLib.sendMessage(conversation, imMessage, option).then((res: any) => {
            switch (res.code) {
                // 消息发送成功
                case RongIMLib.ErrorCode.SUCCESS:
                    // 消息发送成功，可以根据返回结果中的 messageId 字段将列表中的该消息状态改为发送成功。
                    resolve(res.data);
                    console.log('消息发送成功', res.data)

                    // 群聊发起已读回执请求
                    if (res.data.conversationType == 3) sendReadReceiptRequest(targetId, res.data.messageUId);
                    break;
                // 发送消息频率过高，1 秒钟最多只允许发送 5 条消
                case RongIMLib.ErrorCode.SEND_FREQUENCY_TOO_FAST:
                    ElMessage.warning('发送消息频率过高')
                    break;
                default:
                    ElMessage.error('消息发送失败')
                    console.log('消息发送失败', res.code, res.msg)
                    break;
            }
        })

    })
}

// 单聊 发送已读回执消息
// messageUId 最新一条消息的id
// timestamp 最新一条消息的消息发送时间
export const sendReadReceiptMessage = (targetId: string, messageUId: string, timestamp: number) => {
    RongIMLib.sendReadReceiptMessage(targetId, messageUId, timestamp).then(res => {
        if (res.code === 0) {
            // 发送成功
            console.log('发送已读回执消息成功', targetId, messageUId, timestamp)
        } else {
            console.log(res.code, res.msg)
        }
    }).catch(error => {
        console.log('发送已读回执消息失败', error)
    })
}


// 群聊 发起已读回执请求
export const sendReadReceiptRequest = (targetId: string, messageUId: string) => {
    RongIMLib.sendReadReceiptRequest(targetId, messageUId)
        .then((res) => {
            if (res.code === 0) {
                // 发送成功
                console.log('群聊发起已读回执请求', targetId, messageUId)
            } else {
                console.log(res.code, res.msg)
            }
        })
        .catch((error) => {
            console.log('发送群聊已读回执消息失败', error)
        })
}


// 群聊  响应已读回执请求  通知对方（消息发送者）已阅读此消息
export const sendReadReceiptResponse = (targetId: string) => {
    RongIMLib.sendReadReceiptResponseV2(targetId).then(res => {
        if (res.code === 0) {
            console.log('通知对方（消息发送者）已阅读此消息', res.code, res.data)
        } else {
            console.log(res.code, res.msg)
        }
    }).catch(error => {
        console.log('通知对方（消息发送者）已阅读此消息失败', error)
    })
}

// 获取历史消息
export const getHistoryMessages = (targetId: string, timestamp?: number, conversationType?: any) => {
    return new Promise((resolve, reject) => {
        const conversation = {
            conversationType: conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE,
            targetId: targetId
        }

        // 从当前时间开始向前查询
        const option: any = {
            // 传 0 表示从当前时间开始获取
            timestamp: timestamp || 0,
            // 范围为 [1-100]。默认值 20
            count: 50,
            // 0 表示降序，获取发送时间早于 timestamp 的消息
            order: 0
        }

        RongIMLib.getHistoryMessages(conversation, option).then((res: any) => {
            if (res.code === 0) {
                // receivedTime  接收时间
                resolve(res.data);
            } else {
                resolve([]);
                console.log('获取历史消息失败', res.code, res.msg)
            }
        })
    })
}


// 获取第一条未读消息信息
export const getFirstUnreadMessage = (targetId: string, conversationType: any) => {
    return new Promise((resolve, reject) => {
        const conversation = {
            conversationType: conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE,
            targetId: targetId
        }

        RongIMLib.getFirstUnreadMessageInfo(conversation).then(res => {
            if (res.code === 0) {
                resolve(res.data);
            } else {
                console.log('获取第一条未读消息失败', res.code, res.msg)
            }
        })

    })
}


// 存储当前会话最后一条已读会话的时间
export const setConversationReadMessageTime = (targetId: string, sentTime: number) => {
    let data = localStorage.getItem('conversationReadMessageTime') ? JSON.parse(localStorage.getItem('conversationReadMessageTime')) : {}
    data[targetId] = sentTime;
    localStorage.setItem('conversationReadMessageTime', JSON.stringify(data))
}

// 获取当前会话最后一条已读会话的时间
export const getConversationReadMessageTime = (targetId: string) => {
    let data = localStorage.getItem('conversationReadMessageTime') ? JSON.parse(localStorage.getItem('conversationReadMessageTime')) : {}
    return data[targetId]
}




// 清除指定会话未读数
export const clearUnreadCount = (targetId: string, _conversationType: any) => {
    return new Promise((resolve, reject) => {
        const conversationType = _conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE;

        RongIMLib.clearMessagesUnreadStatus({ conversationType, targetId }).then(res => {
            if (res.code === 0) {
                console.log('清除未读会话成功', targetId, conversationType)
                // 发送多端同步未读数消息 
                RongIMLib.sendSyncReadStatusMessage({ conversationType, targetId }, Date.now()).then(() => { })
                resolve(res);
            } else {
                console.log('清除指定会话未读数失败', res.code, res.msg)
            }
        })
    })
}

// 清除全部会话未读数 @@@
export const clearAllUnreadCount = () => {
    return new Promise((resolve, reject) => {
        RongIMLib.clearAllMessagesUnreadStatus().then(res => {
            if (res.code === 0) {
                resolve(res);
            } else {
                console.log('清除全部会话未读数失败', res.code, res.msg)
            }
        })
    })
}



// 设置免打扰   不接收通知，即使为 @ 消息也不推送通知    @@@
export const setUpDoNotDisturb = (targetId: string) => {
    return new Promise((resolve, reject) => {
        const conversationType = RongIMLib.ConversationType.GROUP;
        const notificationLevel = RongIMLib.NotificationLevel.NOT_MESSAGE_NOTIFICATION

        RongIMLib.setConversationNotificationLevel({
            conversationType,
            targetId,
        }, notificationLevel).then(({ code }) => {
            // 设置免打扰状态成功
            resolve(true);
        })
    })
}

// 获取免打扰会话列表  @@@
export const getDoNotDisturbList = () => {
    return new Promise((resolve, reject) => {
        RongIMLib.getBlockedConversationList().then(({ code, data }) => {
            // 获取免打扰会话成功
            if (code === RongIMLib.ErrorCode.SUCCESS) {
                resolve(data);
            } else {
                resolve([]);
                console.log('获取免打扰会话失败', code, data)
            }
        })
    })
}




// EMOJI
export const imEmoji = {
    // 获取列表
    getList: () => {
        // Emoji 初始化
        window.RongIMLib.RongIMEmoji.init();

        // 获取emoji 列表
        return window.RongIMLib.RongIMEmoji.list;
    },

    // Emoji 转 文字
    emoji2Text: (message: string) => {
        return window.RongIMLib.RongIMEmoji.emojiToSymbol(message);
    },

    // 文字 转 Emoji
    text2Emoji: (message: string) => {
        return window.RongIMLib.RongIMEmoji.symbolToEmoji(message);
    },

    // Emoji 转 HTML
    emoji2Html: (message: string) => {
        return window.RongIMLib.RongIMEmoji.emojiToHTML(message);
    },

    // 文字 转 HTML
    text2Html: (message: string) => {
        return window.RongIMLib.RongIMEmoji.symbolToHTML(message);
    }
}



/**
* 对Date的扩展，将 Date 转化为指定格式的String。
*
*  月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
*  年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)。
*
*  【示例】：
*  common.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss.S') ==> 2006-07-02 08:09:04.423
*  common.formatDate(new Date(), 'yyyy-M-d h:m:s.S')      ==> 2006-7-2 8:9:4.18
*  common.formatDate(new Date(), 'hh:mm:ss.S')            ==> 08:09:04.423
* 
*/
var _formatDate = function (date: any, fmt: string) {
    var o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
};

/**
* 仿照微信中的消息时间显示逻辑，将时间戳（单位：毫秒）转换为友好的显示格式.
*
* 1）7天之内的日期显示逻辑是：今天、昨天(-1d)、前天(-2d)、星期？（只显示总计7天之内的星期数，即<=-4d）；
* 2）7天之外（即>7天）的逻辑：直接显示完整日期时间。
*
* @param  {[long]} timestamp 时间戳（单位：毫秒），形如：1550789954260
* @param {boolean} mustIncludeTime true表示输出的格式里一定会包含“时间:分钟”
* ，否则不包含（参考微信，不包含时分的情况，用于首页“消息”中显示时）
*
* @return {string} 输出格式形如：“刚刚”、“10:30”、“昨天 12:04”、“前天 20:51”、“星期二”、“2019/2/21 12:09”等形式
* @since 1.1
*/
export const timeStringAutoShort = (timestamp: number, mustIncludeTime: boolean) => {

    // 当前时间
    var currentDate = new Date();
    // 目标判断时间
    var srcDate = new Date(timestamp);

    var currentYear = currentDate.getFullYear();
    var currentMonth = (currentDate.getMonth() + 1);
    var currentDateD = currentDate.getDate();

    var srcYear = srcDate.getFullYear();
    var srcMonth = (srcDate.getMonth() + 1);
    var srcDateD = srcDate.getDate();

    var ret = "";

    // 要额外显示的时间分钟
    var timeExtraStr = (mustIncludeTime ? " " + _formatDate(srcDate, "hh:mm") : "");

    // 当年
    if (currentYear == srcYear) {
        var currentTimestamp = currentDate.getTime();
        var srcTimestamp = timestamp;
        // 相差时间（单位：毫秒）
        var deltaTime = (currentTimestamp - srcTimestamp);

        // 当天（月份和日期一致才是）
        if (currentMonth == srcMonth && currentDateD == srcDateD) {
            // 时间相差3分钟以内
            if (deltaTime < 60 * 1000 * 3) {
                ret = "刚刚";

            } else if (deltaTime < 60 * 1000 * 60) {
                ret = dayjs(currentTimestamp).diff(dayjs(timestamp), 'minutes') + "分钟前";
            } else {
                // 否则当天其它时间段的，直接显示“时:分”的形式
                ret = _formatDate(srcDate, "hh:mm");
            }
        }
        // 当年 && 当天之外的时间（即昨天及以前的时间）
        else {
            // 昨天（以“现在”的时候为基准-1天）
            var yesterdayDate = new Date();
            yesterdayDate.setDate(yesterdayDate.getDate() - 1);

            // 前天（以“现在”的时候为基准-2天）
            var beforeYesterdayDate = new Date();
            beforeYesterdayDate.setDate(beforeYesterdayDate.getDate() - 2);

            // 用目标日期的“月”和“天”跟上方计算出来的“昨天”进行比较，是最为准确的（如果用时间戳差值
            // 的形式，是不准确的，比如：现在时刻是2019年02月22日1:00、而srcDate是2019年02月21日23:00，
            // 这两者间只相差2小时，直接用“deltaTime/(3600 * 1000)” > 24小时来判断是否昨天，就完全是扯蛋的逻辑了）
            if (srcMonth == (yesterdayDate.getMonth() + 1) && srcDateD == yesterdayDate.getDate())
                ret = "昨天" + timeExtraStr;// -1d
            // “前天”判断逻辑同上
            else if (srcMonth == (beforeYesterdayDate.getMonth() + 1) && srcDateD == beforeYesterdayDate.getDate())
                ret = "前天" + timeExtraStr;// -2d
            else {
                ret = _formatDate(srcDate, "M-d") + timeExtraStr;
            }
        }
    }
    // 往年
    else {
        ret = _formatDate(srcDate, "M-d") + timeExtraStr;
    }

    return ret;
};

// // 用于首页“消息”界面时
// timeStringAutoShort(1550789954260, false);
// // 用于聊天内容界面时
// timeStringAutoShort(1550789954260, true);


// 会话置顶
export const setConversationTop = (conversation: any, isTop: boolean) => {
    return new Promise((resolve, reject) => {
        const conversationType = conversation.conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE;
        const targetId = conversation.targetId;

        RongIMLib.setConversationToTop({
            conversationType,
            targetId,
        }, isTop).then(({ code }) => {
            // 设置会话置顶成功
            if (!code) {
                console.log('设置会话置顶成功')
                resolve(true);
            }
        })
    })
}

// 设置免打扰
export const setConversationDisturb = (targetId: string, level: number) => {
    return new Promise((resolve, reject) => {
        const conversationType = RongIMLib.ConversationType.GROUP;

        // NotificationLevel.NOT_SET 未设置 0
        // NotificationLevel.NOT_MESSAGE_NOTIFICATION 不接收通知，即使为 @ 消息也不推送通知 5
        const notificationLevel = level == 0 ? RongIMLib.NotificationLevel.NOT_SET : RongIMLib.NotificationLevel.NOT_MESSAGE_NOTIFICATION

        RongIMLib.setConversationNotificationLevel({
            conversationType,
            targetId: targetId,
        }, notificationLevel).then((res) => {
            console.log(res)
            // 设置免打扰状态成功
            console.log('设置免打扰状态成功');
            resolve(true);
        })
    })
}


// 获取指定会话
export const getConversation = (targetId: string | number) => {
    return new Promise((resolve, reject) => {
        const conversationType = RongIMLib.ConversationType.PRIVATE;

        RongIMLib.getConversation({
            conversationType,
            targetId,
        }).then(res => {
            if (res.code === 0) {
                console.log(res.code, res.data)
                resolve(res.data)
            } else {
                console.log(res.code, res.msg)
            }
        })
    })
}


// 根据 会话id 查询用户信息
export const getUserInfoByConversationId = (list: any) => {

    return new Promise((resolve, reject) => {
        let imUserId: string[] = [];
        let groupId: string[] = [];

        if (!list || !list.length) {
            return resolve(list);
        }

        list.map((item: any) => {
            if (item.conversationType == 3 && !item.sessionData) {
                groupId.push(item.targetId)
            } else if (!item.sessionData) {
                imUserId.push(item.targetId)
            }
        })

        if (!groupId.length && !imUserId.length) return resolve(list);

        baseService.post('/api/im/info/list', {
            imUserId,
            groupId
        }).then(res => {
            if (res.code == 0) {
                console.log(list)
                list.map((item: any, index) => {
                    if (res.data.imUsers && res.data.imUsers.length) {
                        res.data.imUsers.map((user: any) => {
                            if (item.targetId == user.imUid) {
                                item.sessionData = user
                            }
                        })
                    }

                    if (res.data.imGroups && res.data.imGroups.length) {
                        res.data.imGroups.map((group: any) => {
                            if (item.targetId == group.groupId) {
                                item.sessionData = group
                            }
                        })
                    }
                    if (item.sessionData) {
                        return item
                    } else {
                        deleteConversation(item.targetId, item.conversationType);
                    }
                })


                resolve(list)
            } else {
                resolve(list)
            }
        })
    })
}


// 发起私聊会话  让服务端知道你现在正和谁聊天
// export const startPrivateConversation = (fromImUid: string, targetId: string) => {
//     return new Promise((resolve, reject) => {
//         baseService.post('/api/im/create/private', {
//             fromImUid,
//             targetId
//         }).then(res => {
//             if(res.code == 0){
//                 resolve(res.data)
//             }
//         })
//     })
// }


// 当前用户在线状态
export const getOnlineStatus = (imUid: string) => {
    return new Promise((resolve, reject) => {
        baseService.get("/api/im/isOnline?imUid=" + imUid).then((res: any) => {
            if (res.code == 0) {
                resolve(res.data)
            }
        })
    })
}


// 群组详情
const groupInfo: any = {};
const groupStateImg = [
    // 取消
    'https://oss.nyyyds.com/upload/20241202/9cd01528ea9c425b846e8f7bf7f750ea.png',
    // 完成
    'https://oss.nyyyds.com/upload/20241202/484cfe48d6db40fb9c1f2a6b03ebd378.png',
    // 待换绑
    'https://oss.nyyyds.com/upload/20241202/e779ffcb557f4f089e0072b6f04d5098.png'
]

// 收到消息  显示通知提醒  
const notification: any = {};
export const showNotificationReminders = async (message: any, visibilityState: boolean) => {
    const imStore = useImStore();
    const settingStore = useSettingStore();

    // 免打扰群聊不通知
    if (imStore.imDoNotDisturbList.includes(message.targetId)) return;

    // 自己发的消息不通知
    if (message.senderUserId == imStore.imUid) return;

    // 成员列表
    let memberList: any = [];

    // 群组
    if (message.conversationType == 3) {
        // 获取群组成员信息
        let res: any = await getGroupMemberList(message.targetId);
        if (!res || !res.data) return;
        memberList = res.data;
        let groupDetail: any = await getGroupDetail(message.targetId);
        groupInfo[message.targetId + '-info'] = groupDetail;
    } else if(message.conversationType == 1){
        let res: any = await RongIMLib.getUserProfiles([message.targetId]);
        memberList = res.data;
    }

    let content = message.content.content;
    switch (message.messageType) {
        case 'RC:TxtMsg':
            content = message.content.content;
            break;
        case 'RC:ImgMsg':
            content = '[图片]';
            break;
        case 'RC:FileMsg':
            content = '[视频]';
            break;
        case 'RC:IWNormalMsg':
            switch (message.content.msgType) {
                case 'CU:shop':
                    content = '[商品]';
                    break;
                case 'CU:image':
                    content = '[图片]';
                    break;
                case 'CU:order':
                    content = '[订单]';
                    break;
                case 'CU:createGroup':
                    content = '[创建群聊]';
                    break;
                case 'CU:uploadGroup':
                    content = '[更新群]';
                    break;
                case 'CU:orderFlow':
                    content = '[订单流程]';
                    break;
                case 'CU:flow':
                    content = '[订单流程更新]';
                    break;
                case 'CU:collect':
                    content = '[收集包赔信息]';
                    break;
                case 'CU:contract':
                    content = '[签署合同]';
                    break;
                case 'CU:qq':
                    content = '[QQ入群邀请链接]';
                    break;
            }
            break;
    }

    if(message.targetId == imStore.systemMessageTargetId){
        const appStore = useAppStore();
        content = message.content.msgFields.message.replace('nickname', appStore.state.user.realName)
    }

    // 当前已打开聊天界面不通知
    if (location.hash == '#/im' && visibilityState) return;

    if (memberList && memberList.length && message.conversationType == 1 && message.targetId != imStore.systemMessageTargetId) {
        showJudge(memberList[0], content, message.targetId, message.sentTime);
    } else if (memberList && memberList.length && groupInfo[message.targetId + '-info'] && message.targetId != imStore.systemMessageTargetId) {
        showJudge({
            name: groupInfo[message.targetId + '-info'].groupName,
            portraitUri: message.conversationType == 3 ? '' : groupInfo[message.targetId + '-info'].type == 7 ? groupStateImg[0] : groupInfo[message.targetId + '-info'].type == 6 ? groupStateImg[1] : groupStateImg[2]
        }, content, message.targetId, message.sentTime);

    }

    let img = new Image()
    img.src = memberList && memberList.length ? memberList[0].portraitUri : ''
    let imgOnload = true;
    imgOnload = await imgLoad(memberList && memberList.length ? memberList[0].portraitUri : '');


    notification[message.sentTime] = ElNotification({
        message: h('div', { class: 'notification-wrap' }, [
            // 单聊
            message.conversationType == 1
                ? memberList[0].portraitUri ? h('img', { class: 'notification-img', src: imgOnload ? memberList[0].portraitUri : settingStore.info.backendDefaultAvatar })
                    : message.targetId == imStore.systemMessageTargetId  ? h('img', { class: 'notification-img', src: messageNotificationType[message.content.msgType] }) : h('img', { class: 'notification-img', src: settingStore.info.userDefaultAvatar })
                // 交易群 
                : message.conversationType == 4
                    ?
                    groupInfo[message.targetId + '-info'].type == 7 ?
                        h('img', { class: 'notification-img', src: groupStateImg[0] })
                        : groupInfo[message.targetId + '-info'].type == 6 ?
                            h('img', { class: 'notification-img', src: groupStateImg[1] })
                            : h('img', { class: 'notification-img', src: groupStateImg[2] })

                    // 普通群
                    : h('ul', { class: 'group' }, [
                        memberList.map(item => h('li', [
                            h('img', { class: 'avatar', src: item.portraitUri ? item.portraitUri : settingStore.info.userDefaultAvatar })
                        ])),
                    ]),

                    
                    h('div', { class: 'notification-content' }, [
                        // 单聊
                        message.conversationType == 1 ?
                        h('div', { class: 'notification-title sle' }, message.targetId == imStore.systemMessageTargetId ? message.content.msgFields.title : memberList[0].name)
                        : h('div', { class: 'notification-title sle' }, groupInfo[message.targetId + '-info'].groupName),
                        h('div', { class: 'notification-desc sle' }, filterHtml(content))
                    ]),
        ]),
        duration: 5000,
        onClick: () => {
            browserTabTitle.clear();
            notification[message.sentTime].close();
            if(message.targetId == imStore.systemMessageTargetId){
                const imStore = useImStore();
                imStore.showMessageNotification = true;
                let obj = JSON.parse(message.content.content);
                imStore.messageNotificationInfo = {
                    id: obj.msgFields.code,
                    status: message.content.msgFields.status
                };
                return
            }

            let url = router.resolve({
                path: '/im',
                query: {
                    targetId: message.targetId,
                }
            })
            window.open(url.href, imStore.imUid)
            imStore.showContactInfo = false;
            imStore.currentConversationType = '';
        }
    })
}

// 图片是否加载成功
const imgLoad = (src: string) => {
    return new Promise((resolve, reject) => {
        let img = new Image()
        img.src = src
        img.onload = function () {
            resolve(true)
        }
        img.onerror = function () {
            resolve(false)
        }
    }).catch(() => {
        return true;
    })
}


// 截取首字符
const getFirstChar = (str: string) => {
    console.log(str, 'charAt')
    return str.charAt(0).toUpperCase();
}

// 群查询 获取群组资料
export const getGroupInfo = (groupId: string) => {
    return new Promise((resolve, reject) => {
        // 必填项，群 ID 数组，单次查询最多支持 20 个群组。
        const groupIds = [groupId]
        RongIMLib.getGroupsInfo(groupIds).then(res => {
            console.log(res, '获取群组资料')
            resolve(res)
        })
    })
}

// 获取群成员信息  @@@
export const getGroupMemberList = (groupId: string) => {
    if (groupInfo[groupId]) return groupInfo[groupId];
    return new Promise((resolve, reject) => {
        const role = RongIMLib.GroupMemberRole.UNDEF;
        RongIMLib.getGroupMembersByRole(groupId, role, { count: 50 }).then(res => {
            resolve(res.data);
        });
    })
}


// 断开连接
export const disconnect = () => {
    try {
        RongIMLib.disconnect().then(() => {
            console.log('im成功断开')
        })
    } catch (error) {

    }
}

// 根据用户imUid 查询用户信息
export const getUserInfoByImUid = (data: any, imUid: string) => {
    let obj =  data.find(item => item.imUid == imUid)
    return obj ? obj : {}
}


// 查询群详情
let timer = 0;
export const getGroupDetail = (groupId: string) => {
    if (groupInfo[groupId + '-info']) return groupInfo[groupId + '-info'];
    return new Promise((resolve, reject) => {
        clearTimeout(timer);
        timer = setTimeout(() => {
            baseService.get('/api/im/group/info?groupId=' + groupId).then(res => {
                resolve(res.data);
            })
        }, 500);
    }).catch((error) => {
        console.log(error)
    })
}


// 查询用户详情
export const getUserInfo = (imUid: string) => {
   return new Promise((resolve, reject) => {
       baseService.get('/api/im/user/info?imUid=' + imUid).then(res => {
           resolve(res.data);
       })
   }).catch((error) => {
       console.log(error)
   })
}



// 三分钟之内 只显示一个消息时间
export const timeDiff = (data: any) => {
    let msgTime = 0;
    data.map((item: any) => {
        if (item.content.msgType == 'CU:orderFlow') {
            try {
                item.content.content = item.content.content ? JSON.parse(item.content.content) : '';
            } catch (error) {

            }
        }

        // 文本消息
        if (item.messageType == 'RC:TxtMsg') {
            item.content.content = htmlContent(item.content.content);
        }


        if (!msgTime) {
            msgTime = item.sentTime;
        } else {
            let end = dayjs(msgTime).format('YYYY-MM-DD HH:mm:ss');
            let start = dayjs(item.sentTime).format('YYYY-MM-DD HH:mm:ss');
            if (dayjs(start).diff(dayjs(end), 'minutes') <= 3) {
                item.hideTime = true;
            } else {
                msgTime = item.sentTime;
            }
        }
    })

    // 订单流程更新消息不展示
    return data
}

// 字符串中URL的检测并转换为链接
const htmlContent = ((text: string) => {
    const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
    let content = text.replace(urlRegex, (url) => {
        return `<a href=${url} target="_blank">${url}</a>`;
    })
    return content
})




// 删除指定会话
export const deleteConversation = (targetId: string, _conversationType: string | number) => {
    return new Promise((resolve, reject) => {
        RongIMLib.removeConversation({
            conversationType: _conversationType == 3 ? RongIMLib.ConversationType.GROUP : RongIMLib.ConversationType.PRIVATE,
            targetId: targetId,
        }).then(res => {
            // 删除指定会话成功
            if (res.code === 0) {
                console.log(res.code);
            } else {
                console.log('删除指定会话失败');
            }
        })
    })
}


// 根据关键词查询会话
export const searchConversation = (uid: string, keyword: string) => {
    return new Promise((resolve, reject) => {
        baseService.post('/api/im/search/info', {
            fromImUid: uid,
            search: keyword
        }).then(res => {
            if (res.code == 0) {
                resolve(res.data);
            }
        })
    })

}


// 租户 平台客服列表
export const getTenantCustomerServiceList = () => {
    return new Promise((resolve, reject) => {
        baseService.post('/im/login/partner/contacts').then(res => {
            if (res.code == 0) {
                resolve(res.data);
            }
        })
    })
}


// base64 前缀
export const base64Prefix = (data, type) => {
    if(data.imageUri){
        return data.imageUri;
    }else if(data){
        let prefix = '';
        switch (type) {
            case 'image':
                prefix = 'data:image/*;base64,';
                break;
            case 'video':
                prefix = 'data:video/mp4;base64,';
                break;
        }
        return prefix + data.content;
    }

}


// 消息通知 类型
export const messageNotificationType = {
    // 付费功能开通
    'CU:cmdPaidOpen': msgNotificationApi,
    // api到期
    'CU:cmdAPIExpire': msgNotificationApi,
    // 付费功能到期
    'CU:cmdPaidExpire': msgNotificationOpen,
    // 账户余额不足提醒
    'CU:cmdBalance': msgNotificationMoney,
    // 账户余额充值成功
    'CU:cmdRecharge': msgNotificationAnnouncement,
    // 商品售出通知
    'CU:cmdSale': msgNotificationSale,
    // 商品议价提醒
    'CU:cmdBargain': msgNotificationBargain,
    // 销售合同待签署提醒
    'CU:cmdContractSale': msgNotificationContract,
    // 收购合同待签署提醒
    'CU:cmdContractPurchase': msgNotificationContract,
    // 账号被找回提醒
    'CU:cmdRetrieve': msgNotificationRetrieve,
    // 系统公告
    'CU:cmdNotice': msgNotificationAnnouncement,
}


// 过滤html标签
export const filterHtml = (str: string) => {
    return str.replace(/<[^>]+>/g, '');
}