<template>
  <el-drawer v-model="visible" size="944" class="infoDrawerConfig">
    <template #header>
      <div class="drawer_title">规则配置</div>
    </template>
    <ny-flod-tab
      :list="[
        { label: '被找回', value: '1' },
        { label: '被转手', value: '2' }
      ]"
      value="value"
      label="label"
      v-model="dataForm.type"
      @change="handleStatusClick"
    ></ny-flod-tab>
    <div class="shop_page">
      <el-form :rules="rules" ref="formRef" :model="dataForm" label-suffix="：">
        <div class="shop_page_basic cardDescriptions">
          <div class="titleSty">基本配置</div>
          <el-descriptions title="" :column="1" size="default" border>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>检查间隔时长<span style="color: red">*</span></div>
              </template>
              <el-form-item style="margin-bottom: 12px" label="检查间隔时长(天)" prop="days">
                <el-input style="width: 300px" type="number" v-model.number="dataForm.days" clearable placeholder="请输入时长">
                  <template #append>天</template>
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>{{ dataForm.type == "1" ? "上架时间" : "出售时间" }}<span style="color: red">*</span></div>
              </template>
              <el-form-item style="margin-bottom: 12px" :label="dataForm.type == '1' ? '上架时间' : '出售时间'" prop="sectionStart">
                <div style="display: flex; align-items: center; gap: 8px">
                  <el-date-picker style="width: 150px" v-model="dataForm.sectionStart" value-format="YYYY-MM-DD" type="date" placeholder="开始时间" />
                  <span>至</span>
                  <el-select v-model="dataForm.selectNode" placeholder="请选择游戏" style="width: 110px" @change="selectNodeChange">
                    <el-option label="当前时间" value="1" />
                    <el-option label="时间点" value="2" />
                  </el-select>
                  <el-date-picker style="width: 150px" v-model="dataForm.sectionEnd" value-format="YYYY-MM-DD" type="date" placeholder="结束时间" v-if="dataForm.selectNode == '2'" />
                </div>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>执行时间<span style="color: red">*</span></div>
              </template>
              <el-form-item style="margin-bottom: 12px" label="执行时间" prop="startTime">
                <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
                  <el-time-select v-model="dataForm.startTime" style="width: 300px" :max-time="dataForm.endTime" placeholder="开始时间" start="00:00" step="01:00" end="24:00" />
                  <!-- <span>~</span>
          <el-time-select v-model="dataForm.endTime" style="width: 210px" :min-time="dataForm.startTime" placeholder="结束时间" start="00:00" step="01:00" end="24:00" /> -->
                </div>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>是否开启</div>
              </template>
              <el-form-item label="是否开启" prop="isStart">
                <el-switch :active-value="1" :inactive-value="0" active-text="启用" inactive-text="关闭" v-model="dataForm.isStart"></el-switch>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>提示</div>
              </template>
              <el-alert style="width: 530px" title="查找回可使用次数不足3次时会进行提示，连续三次提示后查找回开关将自动关闭，平台购买完成后需再次手动打开查找回开关并提交才可执行任务。 " class="infoSty" type="info" show-icon :closable="false" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="shop_page_basic cardDescriptions" style="margin-top: 12px">
          <div class="titleSty">找回游戏配置</div>
          <el-descriptions title="" :column="1" size="default" border>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>选择查找回的游戏</div>
              </template>
              <el-form-item style="margin-bottom: 12px" label="选择游戏" prop="gameIds">
                <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
                <el-checkbox-group v-model="dataForm.gameIds" @change="handleCheckedChange">
                  <el-checkbox style="width: 20%" v-for="item in gameList" :key="item.id" :label="item.title" :value="item.id">
                    {{ item.title }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle(formRef)">{{ "确定" }}</el-button>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive } from "vue";
import { ElMessage, FormItemRule } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
const formRef = ref();
const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);
const validateDate = (rule: any, value: string, callback: (e?: Error) => any): any => {
  // || !dataForm.endTime
  if (!dataForm.startTime) {
    return callback(new Error("请选择更新时间"));
  }
  callback();
};
const rules = ref({
  days: [{ required: true, message: "请输入时长", trigger: "change" }],
  startTime: [{ required: true, trigger: "blur", message: "请选择更新时间", validator: validateDate }],
  isStart: [{ required: true, type: "number", trigger: "change", message: "请选择" }]
});
const dataForm = reactive(<any>{
  days: undefined,
  startTime: undefined,
  endTime: undefined,
  isStart: undefined,
  type: "1",
  sectionStart: "",
  sectionEnd: "",
  selectNode: "1",
  gameIds: []
});

const checkAll = ref(false);
const isIndeterminate = ref(false);
const handleCheckAllChange = (val: any) => {
  dataForm.gameIds = val ? gameList.value.map((ele) => ele.id) : [];
  isIndeterminate.value = false;
};
const handleCheckedChange = (value: any) => {
  const checkedCount = value.length;
  checkAll.value = checkedCount === gameList.value.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < gameList.value.length;
};
// 状态
const groupList = ref([
  { dictLabel: "被找回", dictValue: "1" },
  { dictLabel: "被转手", dictValue: "2" }
]);

// 切换状态
const handleStatusClick = (tab: any) => {
  dataForm.status = tab;
  getInfo();
};

// 当前显示类型
const init = () => {
  visible.value = true;
  getInfo();
};

// 获取详情
const getInfo = () => {
  baseService.get("/shop/sysaccountfoundsetting/querySetting", { type: dataForm.type }).then((res) => {
    if (res.code == 0 && res.data) {
      res.data.gameIds = res.data.gameIds ? res.data.gameIds.split(",") : [];
      res.data.sectionEnd ? (dataForm.selectNode = "2") : (dataForm.selectNode = "1");
      Object.assign(dataForm, res.data);
      baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
        gameList.value = res.data;
        handleCheckedChange(dataForm.gameIds);
      });
    } else {
      baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
        gameList.value = res.data;
      });
      (dataForm.days = undefined), (dataForm.startTime = undefined), (dataForm.endTime = undefined), (dataForm.isStart = 0), (dataForm.sectionStart = ""), (dataForm.sectionEnd = ""), (dataForm.selectNode = "1"), (dataForm.gameIds = []);
    }
  });
};

// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = () => {
  //编辑
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    if (valid) {
      let form = { ...dataForm };
      form.startTime = form.startTime.length > 5 ? form.startTime : form.startTime + ":00";
      form.gameIds = form.gameIds.join(",");
      // form.endTime = form.endTime.length > 5 ? form.endTime : form.endTime + ":00";
      (dataForm.id ? baseService.put : baseService.post)("/shop/sysaccountfoundsetting", { ...form }).then((res) => {
        if (res.code == 0) {
          ElMessage.success("修改成功");
          emits("refresh");
          closeDialog();
        }
      });
    }
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("close");
};

// 获取游戏下拉
const gameList = ref(<any>[]);
const getgameList = () => {
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    gameList.value = res.data;
  });
};

// 节点切换
const selectNodeChange = (value: any) => {
  if (value == "1") {
    dataForm.sectionEnd = "";
  }
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
.el-form {
  margin-bottom: 12px;
  display: block;
  .el-form-item {
    display: block;

    :deep(.el-input-group__append) {
      background: #fff !important;
    }
  }
}
:deep(.infoSty.el-alert--info) {
  color: var(--el-color-primary);
  background-color: transparent;
  display: flex;
  align-items: flex-start;
  padding-left: 0;

  .el-icon {
    margin-top: 4px;
  }
}
.accountTabsscriptpush {
  background: #fff;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }
}
.shop_page {
  margin: 0 12px;
}
.shop_page_basic {
  border-radius: 8px;
}
</style>
<style lang="scss">
.infoDrawerConfig {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
    background: #f0f2f5;
  }
  .flod_tab_li {
    font-weight: 400 !important;
    font-size: 14px !important;
  }
  .checkBox {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ebeef5;
    padding: 12px 0px 0px 12px;
    max-height: 100px;
    min-height: 40px;
    width: 100%;
    overflow-y: scroll;
  }
}

.radius {
  .el-checkbox {
    margin-right: 24px;
  }
  .el-checkbox__inner {
    border-radius: 4px;
  }
}
</style>
