<template>
  <div class="container TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle" @dragSort="dragSortHandle">
        <!-- <template #header>
          <el-button v-if="state.hasPermission('game:sysgame:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button v-if="state.hasPermission('game:sysgame:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
        </template> -->

        <template #header-right>
          <ny-select v-model="state.dataForm.state" dict-type="game_status" style="width: 200px" placeholder="选择状态" />
          <el-input v-model="state.dataForm.title" placeholder="游戏名称" clearable></el-input>
          <ny-select v-model="state.dataForm.gtype" dict-type="game_type" style="width: 200px" placeholder="运行设备" />
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>

        <!-- 游戏名称 -->
        <template #name="{ row }"> {{ row.name }}({{ row.code }}) </template>

        <!-- 运行设备 -->
        <template #gtype="{ row }">
          {{ state.getDictLabel("game_type", row.gtype) }}
        </template>

        <!-- 图标 -->
        <template #thumbnail="{ row }">
          <el-image style="width: 36px; height: 36px" :src="row.thumbnail" :preview-src-list="[row.thumbnail]" :preview-teleported="true" fit="cover" />
        </template>

        <!-- 状态 -->
        <template #state="{ row }">
          <el-switch v-if="state.hasPermission('game:sysgame:update')" v-model="row.state" :active-value="0" :inactive-value="1" :loading="row.loading" @change="(e: any) => {updateStatus(e, row)}"></el-switch>
          <el-tag v-else :type="row.state == 1 ? 'danger' : 'success'">{{ row.state == 1 ? "下架" : "上架" }}</el-tag>
        </template>
        <template #official="{ row }">
          <el-switch v-if="state.hasPermission('game:sysgame:update')" :model-value="row.official" :disabled="row.official === 1" :active-value="1" :inactive-value="0" :loading="row.loading" @click="(e: any) => {updateStatus2(row.official, row)}"></el-switch>
          <el-tag v-else :type="row.state == 1 ? 'danger' : 'success'">{{ row.official == 1 ? "启用" : "不启用" }}</el-tag>
        </template>

        <template #operation="{ row }">
          <el-button v-if="state.hasPermission('game:sysgame:listCustomField')" type="primary" text bg @click="setHandle(row.id)"> 设置 </el-button>
          <!-- <el-button v-if="state.hasPermission('game:sysgame:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">
            {{ $t("update") }}
          </el-button>
          <el-button v-if="state.hasPermission('game:sysgame:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">
            {{ $t("delete") }}
          </el-button>
          -->
          <el-button v-if="state.hasPermission('game:sysgame:changeSort')" class="move" type="primary" text bg>
            <el-icon class="text-primary">
              <Rank />
            </el-icon>
          </el-button>
        </template>
      </ny-table>
    </el-card>

    <!-- 新增/修改 -->
    <game-add-or-update ref="gameAddOrUpdateRef" :key="gameAddOrUpdateKey" @refreshDataList="state.query" />

    <!-- 设置 -->
    <game-set ref="gameSetRef" :key="gameSetKey"></game-set>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import GameAddOrUpdate from "./game-add-or-update.vue";
import GameSet from "./game-set.vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "title",
    label: "游戏名称",
    minWidth: 160
  },
  {
    prop: "operatorsStr",
    label: "游戏运营商",
    minWidth: 160
  },
  {
    prop: "gtype",
    label: "运行设备",
    minWidth: 130
  },
  {
    prop: "areaStr",
    label: "系统区服",
    minWidth: 150
  },
  {
    prop: "thumbnail",
    label: "图标",
    minWidth: 90
  },
  {
    prop: "state",
    label: "状态",
    minWidth: 90
  },
  {
    prop: "official",
    label: "启用模版",
    minWidth: 90
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 160
  },
  {
    prop: "sortNum",
    label: "排序",
    width: 80
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 100
  }
]);

const view = reactive({
  getDataListURL: "/game/sysgame/page",
  getDataListIsPage: true,
  deleteURL: "/game/sysgame/delete",
  deleteIsBatch: true,
  dataForm: {
    state: "",
    title: "",
    gtype: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.state = "";
  state.dataForm.title = "";
  state.dataForm.gtype = "";
  state.getDataList();
};

// 新增  编辑
const gameAddOrUpdateRef = ref();
const gameAddOrUpdateKey = ref(0);
const addOrUpdateHandle = (id?: number) => {
  gameAddOrUpdateKey.value++;
  nextTick(() => {
    gameAddOrUpdateRef.value.init(id);
  });
};

// 排序
const dragSortHandle = async ({ newIndex, oldIndex }: { newIndex: number; oldIndex: number }) => {
  // targetGameId 移动到目标游戏id
  // movedGameId 选择移动游戏id
  let res = await baseService.put("/game/sysgame/changeSort", { targetId: state.dataList[newIndex].id, movedId: state.dataList[oldIndex].id });
  if (res.code == 0) {
    ElMessage.success("排序成功");
  }
  state.getDataList();
};

// 上下架
const updateStatus = (status: any, row: any) => {
  if (!row.id) return;
  row.state = status;
  row.loading = true;
  baseService
    .put("/game/sysgame/update", row)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.query();
    })
    .finally(() => {
      row.loading = false;
    });
};
// 启用模板
const updateStatus2 = (status: any, row: any) => {
  if (row.official === 1) return;
  if (!row.id) return;
  ElMessageBox.confirm("启用模板后无法修改！", "提示", {
    type: "warning",
    confirmButtonText: "确认启用",
    cancelButtonText: "取消"
  }).then(() => {
    row.official = status == 1 ? 0 : 1;
    row.loading = true;
    baseService
      .put("/game/sysgame/update", row)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500
        });
        state.query();
      })
      .finally(() => {
        row.loading = false;
      });
  });
};

// 显示设置
const gameSetRef = ref();
const gameSetKey = ref(0);
const setHandle = (id: number) => {
  gameSetKey.value++;
  nextTick(() => {
    gameSetRef.value.init(id);
  });
};
onMounted(async () => {});
</script>
