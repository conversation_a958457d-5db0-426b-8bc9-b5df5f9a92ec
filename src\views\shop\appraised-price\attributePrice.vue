<template>
  <div class="mainBoxPrice">
    <div class="leftPart">
      <div class="title">选择游戏</div>
      <div class="scrollWrap">
        <ul class="menuUl">
          <li :class="'menuLi ' + (state.dataForm.gameId == item.id ? 'active' : '')" v-for="(item, itemIndex) in state.allSelectArr" :key="itemIndex" @click="handleselectGame(item.id)" :index="itemIndex">
            <span>{{ item.title }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="rightPart TableXScrollSty">
      <ny-table cellHeight="ch-40" max-height="calc(100vh - 340px)" :span-method="mergeRows" :showColSetting="false" :state="state" :columns="columns" @pageSizeChange="pageSizeChangeHandle" @pageCurrentChange="pageCurrentChangeHandle">
        <template #header>
          <template v-if="!editAll">
            <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
            <el-button type="warning" @click="importPriceHandle()">导入</el-button>
            <el-button type="info" @click="exportNewHandle()">导出</el-button>
            <el-button color="#8D5AAF" @click="editAll = true">编辑</el-button>
            <el-button color="#3491FA" style="color: #fff" @click="formulaSetRefHandle">公式</el-button>
          </template>
          <template v-else>
            <el-button type="primary" v-loading="subLoading" @click="saveChange">保存更改</el-button>
            <el-button
              @click="
                editAll = false;
                getResetting();
              "
              >取消</el-button
            >
          </template>
        </template>
        <template #header-right>
          <el-input v-model="state.dataForm.title" placeholder="请输入属性名称" :prefix-icon="Search" style="width: 240px !important" clearable></el-input>
          <el-button type="primary" @click="getDataListSearch">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
        <template #parentName="{ row }">
          <div v-if="row.isWarning" style="color: red">属性不匹配</div>
          <span v-else>{{ row.parentName }}</span>
        </template>
        <template #name="{ row }">
          <div v-if="row.isWarning" style="color: red">
            <el-select v-if="editAll" filterable remote :remote-method="remoteMethod" clearable v-model="row.name" placeholder="请输入关键字搜索">
              <el-option v-for="item in searchAttriArr" :key="item.name" :label="item.name" :value="item.name" />
            </el-select>
            <span v-else>{{ row.name }}</span>
          </div>
          <span v-else>{{ row.name }}</span>
        </template>
        <template #status="{ row }">
          <el-tag type="warning">待处理</el-tag>
        </template>
        <template #singlePrice="{ row }">
          <span v-if="!editAll">{{ row.singlePrice }}</span>
          <el-input-number style="width: 100%" v-else v-model="row.singlePrice" placeholder="请输入单属性价格" :controls="false"></el-input-number>
        </template>
        <template #multiPrice="{ row }">
          <span v-if="!editAll">{{ row.multiPrice }}</span>
          <el-input-number style="width: 100%" v-else v-model="row.multiPrice" placeholder="请输入多属性价格" :controls="false"></el-input-number>
        </template>
        <template #multiPriceHeader="{ row }">
          <el-tooltip
            effect="dark"
            raw-content
            content="
              ■ 若勾选属性时，该属性名称中的单属性明细前面没有勾选任意属性，使用该属性的单属性价格；<br />
              ■ 若勾选属性时，该属性名称中的单属性明细前面存在并勾选了任意属性，则使用该属性的多属性价格（前提是已在价格列表中设置了该属性的多属性价格）；<br />
              ■ 例： 比如神话棋盘：有勾选“云鹤仙观”，然后“黑默丁格的黄金实验室”就按“多属性价格”算  ，若未勾选“云鹤仙观”，然后“黑默丁格的黄金实验室”就按“单属性价格算”
          "
            placement="top"
          >
            <div class="flx-center">
              <div>多属性价格(元)</div>
              <el-icon size="16" style="margin-left: 4px">
                <QuestionFilled />
              </el-icon>
            </div>
          </el-tooltip>
        </template>
        <template #empty>
          <div style="line-height: 300px">
            <el-button type="primary" v-loading="importLoading" plain :icon="Plus" @click="handleImportData">导入默认属性</el-button>
          </div>
        </template>
      </ny-table>
    </div>
    <importPrice ref="importPriceRef" :game-info="currentGame" @refreshDataList="getResetting()" />
    <formulaSet ref="formulaSetRef" :game-info="currentGame" :attribute-list="currentAttributeData" />
    <addAttribute ref="addOrUpdateRef" :key="addOrUpdateKey" @refreshDataList="getResetting()" :game-info="currentGame" :attribute-list="currentAttributeData" />
  </div>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, onMounted, toRefs, reactive, watch, onUnmounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import { formatTimeStamp, findSimilarItems } from "@/utils/method";
import addAttribute from "./addAttribute.vue";
import importPrice from "./importPrice.vue";
import formulaSet from "./formula-set.vue";
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
const view = reactive({
  getDataListURL: "/appraise/attribute/listTree",
  createdIsNeed: false,
  getDataListIsPage: false,
  deleteURL: "",
  listRequestMethod: "post",
  deleteIsBatch: true,
  allSelectArr: [],
  dataForm: {
    gameId: "",
    title: ""
  },
  limit: 100
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    prop: "parentName",
    label: "属性名称",
    minWidth: 136
  },
  {
    prop: "name",
    label: "属性明细",
    minWidth: 136
  },
  {
    prop: "singlePrice",
    label: "单属性价格(元)",
    minWidth: 136,
    showOverflowTooltip: false
  },
  {
    prop: "multiPrice",
    label: "多属性价格(元)",
    minWidth: 136,
    showOverflowTooltip: false
  }
  // {
  //   prop: "opearate",
  //   label: "操作",
  //   minWidth: 136
  // }
]);
const searchAttriArr = ref(<any>[]);
const remoteMethod = (query: string) => {
  if (query) {
    let arr = [];
    currentAttributeData.value?.forEach((ele) => {
      if (ele.children && ele.children.length > 0) {
        arr.push(...ele.children);
      }
    });
    arr = arr.map((ele) => ele.name);
    searchAttriArr.value = findSimilarItems(arr, query, 0.4);
  } else {
    searchAttriArr.value = [];
  }
};
// 合并行逻辑
const mergeRows = ({ row, column, rowIndex }) => {
  if (column.property === "parentName") {
    const data = state.dataList;
    // 只在第一次出现时合并
    let rowspan = 1;
    for (let i = rowIndex + 1; i < data.length; i++) {
      if (data[i].parentName === row.parentName) {
        rowspan++;
      } else {
        break;
      }
    }
    // 如果不是第一次出现，隐藏单元格
    for (let i = 0; i < rowIndex; i++) {
      if (data[i].parentName === row.parentName) {
        return { rowspan: 0, colspan: 0 };
      }
    }
    return { rowspan, colspan: 1 };
  }
  return { rowspan: 1, colspan: 1 };
};
const editAll = ref(false);
// 新增  编辑
const addOrUpdateRef = ref();
const addOrUpdateKey = ref(0);
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
// 导入
const importPriceRef = ref();
const importPriceHandle = () => {
  nextTick(() => {
    importPriceRef.value.init();
  });
};
// 公式设置
const formulaSetRef = ref();
const formulaSetRefHandle = () => {
  nextTick(() => {
    formulaSetRef.value.init(state.dataForm.gameId);
  });
};
// 导出最新属性
const exportNewHandle = () => {
  baseService.get("/appraise/attribute/export", { gameId: view.dataForm.gameId }).then((res) => {
    if (res) {
      fileExport(res, currentGame.value.title + "属性价格列表");
    }
  });
};
const subLoading = ref(false);
// 保存更改
const saveChange = async () => {
  subLoading.value = true;
  let params = <any>[];
  let warnParams = <any>[];
  historyData.value?.forEach((ele: any) => {
    if (ele.isWarning) {
      warnParams.push({
        appraiseAttributeId: ele.id,
        singlePrice: ele.singlePrice,
        multiPrice: ele.multiPrice,
        notMacthName: ele.name
      });
    } else {
      params.push({
        appraiseAttributeId: ele.id,
        singlePrice: ele.singlePrice,
        multiPrice: ele.multiPrice
      });
    }
  });
  // 不匹配的编辑
  // if (warnParams.length > 0) {
  //   let res = await baseService.put("/appraise/attribute/edit", { gameId: view.dataForm.gameId, bacthEditParams: warnParams });
  // }
  // 匹配的编辑
  let res2 = await baseService.put("/appraise/attribute/batchEdit", { gameId: view.dataForm.gameId, bacthEditParams: [...warnParams, ...params] });
  if (res2.code == 0) {
    ElMessage.success("保存成功！");
    editAll.value = false;
    subLoading.value = false;
    getResetting();
  }
};
// 重置操作
const getResetting = () => {
  state.dataForm.title = "";
  state.page = 1;
  state.total = 0;
  getDataList();
};
const getDataList = () => {
  state.dataListLoading = true;
  if (state.total != 0) {
    state.dataList = historyData.value.slice((+state.page - 1) * +state.limit, +state.page * +state.limit);
    state.dataListLoading = false;
    return;
  }
  baseService
    .post(state.getDataListURL, {
      ...state.dataForm
    })
    .then((res) => {
      state.dataListLoading = false;
      state.dataList = [];
      if (res.data && res.data.length > 0) {
        let unmatchArr = <any>[];
        res.data.forEach((ele: any) => {
          if (ele.children && ele.children.length > 0) {
            // 属性
            let arr = ele.children.map((cEle: any) => {
              cEle.parentName = ele.name;

              return cEle;
            });
            if (ele.id == null) {
              // 不匹配的
              unmatchArr.push(...ele.children);
              unmatchArr = unmatchArr.map((cEle: any) => {
                cEle.isWarning = true;
                return cEle;
              });
            } else {
              state.dataList?.push(...arr);
            }
          }
        });
        state.dataList = [...unmatchArr, ...state.dataList];
      }
      historyData.value = JSON.parse(JSON.stringify(state.dataList));
      state.dataList = historyData.value.slice(0, state.limit);
      state.total = historyData.value.length;
    })
    .catch(() => {
      state.dataListLoading = false;
    });
};
const historyData = ref([]);
const getDataListSearch = () => {
  if (state.dataForm.title) {
    state.dataList = historyData.value.filter((ele: any) => ele.parentName.includes(state.dataForm.title) || ele.name.includes(state.dataForm.title));
    state.total = 0;
  } else {
    getResetting();
  }
};
const pageSizeChangeHandle = (val: any) => {
  state.limit = val;
  getDataList();
};
const pageCurrentChangeHandle = (val: any) => {
  state.page = val;
  getDataList();
};

// 导入默认属性
const importLoading = ref(false);
const handleImportData = () => {
  importLoading.value = true;
  baseService.get("/appraise/attribute/importDefaultAttr", { gameId: state.dataForm.gameId }).then((res) => {
    ElMessage.success("导入成功！");
    importLoading.value = false;
    getDataList();
  });
};
const currentGame = ref(<any>{});
// 切换合作商获取游戏+更新table数据
const currentAttributeData = ref();
const getSelectGame = () => {
  state.allSelectArr = [];
  baseService.get("/game/sysgame/listGames", { limit: null }).then((res) => {
    state.allSelectArr = res.data || [];
    state.dataForm.gameId = state.allSelectArr.length > 0 ? state.allSelectArr[0].id : "";
    currentGame.value = state.allSelectArr.length > 0 ? state.allSelectArr[0] : {};
    baseService.get("/game/attribute/page", { gameId: state.dataForm.gameId }).then((res) => {
      let attributesList = [];
      res.data.map((item: any) => {
        attributesList.push({
          typeId: item.id,
          attributeIds: item.type == 2 ? [] : "",
          attributeText: "",
          children: item.children,
          isTitle: item.isTitle,
          type: item.type,
          name: item.name,
          statisticsTag: item.statisticsTag,
          currentPage: 1,
          fold: false
        });
      });
      currentAttributeData.value = attributesList;
      getResetting();
    });
  });
};

const handleselectGame = (id: any) => {
  if (editAll.value) {
    ElMessageBox.confirm("当前游戏属性价格未保存，确定切换游戏?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(() => {
      handleWay();
    });
  } else {
    handleWay();
  }
  function handleWay() {
    currentGame.value = state.allSelectArr.find((ele) => ele.id == id);
    state.dataForm.gameId = id;
    currentAttributeData.value = [];
    baseService.get("/game/attribute/page", { gameId: state.dataForm.gameId }).then((res) => {
      let attributesList = [];
      res.data.map((item: any) => {
        attributesList.push({
          typeId: item.id,
          attributeIds: item.type == 2 ? [] : "",
          attributeText: "",
          children: item.children,
          isTitle: item.isTitle,
          type: item.type,
          name: item.name,
          statisticsTag: item.statisticsTag,
          currentPage: 1,
          fold: false
        });
      });
      currentAttributeData.value = attributesList;
      getResetting();
    });
  }
};

onMounted(() => {
  getSelectGame();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.menuUl,
.menuLi {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mainBoxPrice {
  display: flex;

  .leftPart {
    width: 186px;
    margin-right: 12px;

    .title {
      font-family: Inter, Inter;
      font-weight: 400;
      font-size: 13px;
      color: #606266;
      line-height: 22px;
      margin-bottom: 2px;
    }

    display: flex;
    flex-direction: column;

    .scrollWrap {
      border-radius: 4px;
      border: 1px solid #ebeef5;
      max-height: calc(100vh - 270px);
      overflow: auto;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .menuUl {
        .menuLi {
          cursor: pointer;
          padding: 20px;
          word-break: keep-all;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: Inter, Inter;
          font-weight: 400;
          font-size: 12px;
          color: #303133;
          line-height: 14px;
          &.active {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
          }
        }
      }
    }
  }

  .rightPart {
    flex: 1;
    overflow: hidden;
  }
}
.el-input-number .el-input__inne {
  text-align: left !important;
}
</style>
