<template>
  <!-- <el-dialog v-model="visible" width="480" :title="propForm.title + '支付'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog"> -->
  <el-drawer class="infoAuditDrawer" v-model="visible" size="944">
    <template #header>
      <div class="drawer_title">{{ propForm.title }}支付</div>
    </template>
    <el-scrollbar v-loading="requestLoading">
      <div class="basicInfoSty cardDescriptions">
        <div class="titleSty">基本信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>游戏名称</div> </template>
            {{ propForm.gameName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>游戏账号</div> </template>
            {{ propForm.gameAccount }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>商品编码</div> </template>
            {{ propForm.shopCode }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title" v-if="propForm.orderType == '8'">
            <template #label> <div>销售渠道</div> </template>
            {{ propForm.channelName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title" v-if="propForm.orderType == '8'">
            <template #label> <div>成交价(元)</div> </template>
            {{ propForm.dealAmount }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>销售类型</div> </template>
            <span v-if="propForm.orderType == 4">回收订单</span>
            <span v-if="propForm.orderType == 5">销售订单</span>
            <span v-if="propForm.orderType == 6">售后订单</span>
            <span v-if="propForm.orderType == 8">合作商订单</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>申请人</div> </template>
            {{ propForm.creatorName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>申请时间</div> </template>
            <span>{{ formatTimeStamp(Number(propForm.createDate)) }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty cardDescriptions">
        <div class="titleSty">收款信息</div>
        <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>账户名称</div> </template>
            {{ propForm.orderType == "8" ? propForm.paymentAccountName : propForm.accountName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>收款方账户类型</div> </template>
            {{ accountTypeList.find((item:any)=> item.value == (propForm.orderType == '8' ? propForm.paymentAccountType : propForm.accountType))?.name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>收款账户</div> </template>
            {{ propForm.orderType == "8" ? propForm.paymentAccount : propForm.account }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="title">
            <template #label> <div>备注</div> </template>
            {{ propForm.orderType == "8" ? propForm.paymentRemark : propForm.remarks }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="basicInfoSty cardDescriptions">
        <div class="titleSty">付款操作</div>
        <el-form ref="dataFormRef" :model="dataForm" :rules="rules" label-suffix="：">
          <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>付款方式<span style="color: red">*</span></div>
              </template>
              <el-form-item label="付款方式" prop="payType">
                <el-select v-model="dataForm.payType" placeholder="请选择付款方式" @change="accountTypeChange">
                  <el-option
                    v-for="item in [
                      { value: 1, name: '支付宝转账' },
                      { value: 2, name: '微信' },
                      { value: 3, name: '银行卡' }
                    ]"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title">
              <template #label>
                <div>应付金额(元)</div>
              </template>
              <span>{{ dataForm.fackAmount }}</span>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="title" :span="dataForm.payType == 2 || dataForm.payType == 3 ? 2 : 1">
              <template #label>
                <div>付款账户<span style="color: red">*</span></div>
              </template>
              <el-form-item label="付款账户" prop="payAccount">
                <el-select v-model="dataForm.payAccount" placeholder="请选择付款账户">
                  <el-option v-for="item in propForm.accountList" :key="item.account" :label="item.name" :value="item.account" />
                </el-select>
              </el-form-item>
            </el-descriptions-item>

            <template v-if="dataForm.payType == 3">
              <el-descriptions-item label-class-name="title">
                <template #label>
                  <div>账号名称<span style="color: red">*</span></div>
                </template>
                <el-form-item label="账号名称" prop="bankAccountName">
                  <el-input v-model="dataForm.bankAccountName" placeholder="请输入账号名称"></el-input>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label-class-name="title">
                <template #label>
                  <div>开户行名称<span style="color: red">*</span></div>
                </template>
                <el-form-item label="开户行名称" prop="bankName">
                  <el-input v-model="dataForm.bankName" placeholder="请输入开户行名称"></el-input>
                </el-form-item>
              </el-descriptions-item>
            </template>
            <el-descriptions-item label-class-name="title" v-if="dataForm.payType == 1">
              <template #label>
                <div>支付宝订单号</div>
              </template>
              <el-form-item prop="lendingAlipayNo">
                <el-input v-model="dataForm.lendingAlipayNo" type="number" placeholder="请输入"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2" label-class-name="title">
              <template #label>
                <div>付款凭证<span style="color: red">*</span></div>
              </template>
              <el-form-item label="付款凭证" prop="payImage">
                <ny-upload :limit="1" v-model:imageUrl="dataForm.payImage" accept="image/*"></ny-upload>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- <el-form-item label="付款方式" prop="type">
            <el-radio-group v-model="dataForm.type" @change="dataForm.payAccount = undefined">
              <el-radio value="0" v-if="propForm.accountType != '3'">自动付款</el-radio>
              <el-radio value="1">手动转款</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="dataForm.type == '0'" label="付款账户" prop="payAccount">
            <el-select style="width: 358px" v-model="dataForm.payAccount" placeholder="请选择付款账户"> <el-option v-for="item in propForm.accountList" :key="item.account" :label="item.name" :value="item.account" /> </el-select
          ></el-form-item>
          <template v-if="dataForm.type == '1'">
            <div style="display: flex">
              <el-form-item label="付款金额(元)" prop="amount">
                <el-input v-if="propForm.payType == 3 && defaultSet.charge" style="width: 210px" disabled type="number" v-model="dataForm.fackAmount" :min="0" :precision="0" placeholder="请输入付款金额" />
                <el-input v-else style="width: 210px" disabled type="number" v-model="dataForm.amount" :min="0" :precision="0" placeholder="请输入付款金额" />
              </el-form-item>
              <el-form-item style="margin-left: 12px" label="付款方式" prop="payType">
                <el-select style="width: 210px" v-model="dataForm.payType" placeholder="请输入付款方式">
                  <el-option
                    v-for="item in [
                      { value: 1, name: '支付宝' },
                      { value: 2, name: '微信' },
                      { value: 3, name: '银行卡' }
                    ]"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div v-if="propForm.payType == 3 && defaultSet.charge" style="font-size: 12px">
              <span>提现手续费：</span><span style="color: #e6a23c">原提现金额{{ dataForm.amount }}元，{{ defaultSet.charge }}%，{{ (+defaultSet.charge * dataForm.amount) / 100 }}元</span>
            </div>
            <template v-if="dataForm.payType == 3">
              <el-form-item label="账号名称" prop="bankAccountName">
                <el-input v-model="dataForm.bankAccountName" placeholder="请输入账号名称"></el-input>
              </el-form-item>
              <el-form-item label="开户行名称" prop="bankName">
                <el-input v-model="dataForm.bankName" placeholder="请输入开户行名称"></el-input>
              </el-form-item>
            </template>
            <el-form-item label="付款账号" prop="payAccount">
              <el-input style="width: 432px" v-model="dataForm.payAccount" placeholder="请输入付款账号" />
            </el-form-item>
            <el-form-item label="付款凭证" prop="payImage"> <ny-upload :limit="1" v-model:imageUrl="dataForm.payImage" accept="image/*"></ny-upload></el-form-item>
          </template>
          <el-form-item v-if="dataForm.type != '1'" label="付款金额(元)" prop="amount">
            <el-input style="width: 358px" type="number" disabled v-model="dataForm.amount" :min="0" :precision="0" placeholder="请输入付款金额" />
          </el-form-item> -->
        </el-form>
      </div>
    </el-scrollbar>

    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle()">{{ "确认付款" }}</el-button>
    </template>
  </el-drawer>
  <!-- </el-dialog> -->
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, defineExpose } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";

const emits = defineEmits(["close", "refreshDataList"]);
const visible = ref(false);
const dataForm = ref(<any>{
  payType: 1
});
let propForm = reactive({
  accountType: "",
  accountBank: "",
  payType: "1",
  title: "",
  accountName: "",
  account: "",
  id: "",
  accountList: []
});
// 详情id
const detailId = ref("");
let defaultSet = ref({});
const init = (id: string, type?: any) => {
  visible.value = true;
  detailId.value = id;
  baseService.get("/flowable/withdraw").then((res) => {
    defaultSet.value = res.data || {};
    getInfo(id);
  });
};
// 获取账户下拉
const getAccountList = () => {
  baseService.get("/wallet/tenantaccount/page", { limit: 9999, type: dataForm.value.payType }).then((res) => {
    propForm.accountList = res.data.list || [];
  });
};
getAccountList();
const rules = ref({
  // 表单必填项
  type: [{ required: true, message: "请选择付款方式", trigger: "change" }],
  payAccount: [{ required: true, message: "请选择付款账户", trigger: "change" }],
  amount: [{ required: true, message: "请输入付款金额", trigger: "blur" }],
  payType: [{ required: true, message: "请输入付款方式", trigger: "blur" }],
  bankAccountName: [{ required: true, message: "请输入账号名称", trigger: "blur" }],
  bankName: [{ required: true, message: "请输入开户行名称", trigger: "blur" }],
  payImage: [{ required: true, message: "请选择付款凭证", trigger: "blur" }],
  // lendingAlipayNo: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});

const accountTypeList = ref([
  { value: 1, name: "支付宝转账" },
  { value: 2, name: "微信转账" },
  { value: 3, name: "银行卡转账" }
]);
// 获取详情
const getInfo = async (id: string) => {
  let res = await baseService.get("/flowable/financialaudit/" + id);
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  propForm = Object.assign(propForm, res.data);
  dataForm.value.amount = propForm.amount;
  dataForm.value.type = propForm.accountType == 3 ? "1" : "0"; //赋一个初始值
  propForm.payType = +res.data.type;
  propForm.title = ["", "放款", "退款", "提现", "销售放款"][propForm.payType];
  // NOTE: 前端设置的假字段， 用于前台显示和提交赋值realAmount  提现&有手续费-需要计算 手动在提交时判断赋值
  dataForm.value.fackAmount = propForm.payType == "3" && defaultSet.value.charge ? propForm.amount - (+defaultSet.value.charge * propForm.amount) / 100 : propForm.amount;
};
const dataFormRef = ref(); // 表单ref

// 付款方式选择
const accountTypeChange = (e: any) => {
  dataForm.value.accountName = accountTypeList.value.find((item: any) => item.value == e)?.name;
  dataForm.value.payAccount = "";
  dataForm.value.lendingAlipayNo = "";
  dataForm.value.bankAccountName = "";
  dataForm.value.bankName = "";
  getAccountList();
};

// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    let form = { ...dataForm.value, id: propForm.id };
    form.realAmount = form.amount;
    form.type = "1"; // 逻辑修改默认改为1手动
    if (form.type == 0 || form.type == 2) {
      // 非手动转款删除多余内容
      delete form.payType;
      delete form.bankAccountName;
      delete form.bankName;
      delete form.payImage;
      if (form.type == 2) {
        delete form.payAccount;
      }
    }
    if (propForm.payType == "3" && form.type == 1) {
      // 提现&手动
      form.realAmount = form.fackAmount;
    }
    delete form.fackAmount;
    let res = await baseService.post("/flowable/financialaudit", form);
    if (res.code == 0) {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emits("refreshDataList");
        }
      });
    }
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("close");
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.basicInfoSty {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 12px;
  .titleSty {
    font-family: Inter, Inter;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    padding-left: 8px;
    border-left: 2px solid var(--el-color-primary);
    margin-bottom: 12px;
  }

  .tipinfo {
    :deep(.el-descriptions__label) {
      width: 144px;
      background: #f5f7fa;
      font-family: Inter, Inter;
      font-weight: 500;
      font-size: 14px;
      color: #606266;
      padding: 9px 12px;
      border: 1px solid #ebeef5;
    }
  }
}

:deep(.el-form-item--default) {
  margin-bottom: 12px;
  display: block;
}
</style>
