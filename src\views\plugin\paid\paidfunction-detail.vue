<template>
  <!-- 付费功能详情 -->
  <el-drawer 
    v-model="visible" 
    :title="resData.name" 
    :close-on-click-moformuladal="false" 
    :close-on-press-escape="false"
    size="40%" 
    class="ny-drawer"
  >
    <el-card class="paid-fun-detail mod-paid__tbpaidfunction">
      <el-scrollbar class="scrollbar">
        <div class="detail-content">
          <div class="tag-wrap flx-align-center method-tag">
            <!-- 收费方式 -->
            <span class="method" v-if="isConfigureTrial()">试用</span>
            <span class="method-1" v-else-if="resData.chargingMethod == 1">按次</span>
            <span class="method-2" v-else-if="resData.chargingMethod == 2">包月</span>
            <span class="method-3" v-else-if="resData.chargingMethod == 3">永久</span>

            <!-- 游戏范围 -->
            <el-tag class="ml-10" type="primary" effect="dark">全平台</el-tag>
          </div>

          <div class="p-title">介绍</div>
          <div class="desc">{{ resData.introduce }}</div>

          <div class="version">
            <el-tag type="info">版本号</el-tag>
            更新于 {{ timestampFormat(resData.updateTime) }}
          </div>

          <div
            class="described-image-wrap"
            v-if="resData.infoImgs && resData.infoImgs.length"
          >
            <el-image
              class="image"
              v-for="img in resData.infoImgs"
              :key="img"
              :src="img"
              fit="cover"
              :preview-src-list="[img]"
            />
          </div>
        </div>
      </el-scrollbar>
    </el-card>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, defineEmits, computed, defineExpose } from "vue";
import { checkPermission, timeFormat } from "@/utils/utils";
import { IObject } from "@/types/interface";

const emits = defineEmits(["close", "open"]);

const resData = ref({} as IObject);
const visible = ref(false);

const init = (data: any) => {
  visible.value = true;
  resData.value = data;
};

// 时间戳格式化
const timestampFormat = (dateTime: string | number | null = null) => {
  return timeFormat(dateTime);
};

// 是否配置试用
const isConfigureTrial = () => {
	const hasTrial = resData.value?.paidFunctionOptionsVo?.trialType === 0 || resData.value?.paidFunctionOptionsVo?.trialType === 1 ? true : false;
 	return hasTrial && resData.value?.paidFunctionOptionsVo?.trialOption && resData.value?.remainCount === null && resData.value?.expireTime === null;
};

defineExpose({
  init,
});
</script>

<style lang="less" scoped>
.paid-fun-detail {
  height: 100%;

  .back {
    display: flex;
    align-items: center;
    height: 22px;
    padding-bottom: 32px;
    cursor: pointer;

    i {
      padding-right: 3px;
    }
  }

  .scrollbar {

    .detail-content {

      .title {
        font-size: 36px;
        font-weight: 800;
        line-height: 52px;
      }

      .tag-wrap {

        .el-tag {
          margin-right: 16px;
        }
      }

      .p-title {
        font-size: 24px;
        font-weight: 600;
        line-height: 34px;
        padding-bottom: 12px;
      }

      .desc {
        font-size: 16px;
        line-height: 24px;
      }

      .version {
        padding: 20px 0;
        font-size: 12px;
      }

      .described-image-wrap {
        display: flex;
        flex-wrap: wrap;
        margin-left: -26px;

        .image {
          width: 100%;
          border-radius: 12px;
          margin-left: 26px;
        }
      }

      .btn {
        margin-top: 84px;
      }
    }
  }
}
</style>
