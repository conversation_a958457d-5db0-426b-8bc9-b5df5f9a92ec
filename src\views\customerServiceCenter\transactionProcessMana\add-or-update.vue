<template>
  <el-dialog v-model="visible" width="500" :title="dataForm.type == 0 ? '编辑流程' : '交易行为内容编辑'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item label="选择或输入流程" prop="name">
        <el-input disabled v-model="dataForm.name" filterable placeholder="请选择或输入流程"> </el-input>
      </el-form-item>
      <el-form-item :label="dataForm.type == 0 ? '流程内容' : '行为内容'" prop="content">
        <el-input :autosize="{ minRows: 6 }" placeholder="请输入流程内容" type="textarea" v-model="dataForm.content"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  name: "",
  content: "",
  type: 0
});

const rules = {
  name: [{ required: true, message: "请选择或输入流程", trigger: "blur" }],
  content: [{ required: true, message: "请输入内容", trigger: "change" }]
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/im/imflow/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.put("/im/imflow", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
.el-form-item {
  display: inherit;
  margin-bottom: 12px;
  .el-form-item__label {
    font-weight: 400;
    font-size: 13px;
    color: #606266;
    line-height: 22px;
  }
  .el-checkbox {
    margin-right: 24px;
  }
  :deep(.el-checkbox__inner) {
    border-radius: 4px;
  }
}
</style>
