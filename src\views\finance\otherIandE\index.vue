<template>
  <!-- 售后订单 -->
  <div class="container sell-order-wrap TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-flod-tab
        class="newTabSty"
        :list="[
          {
            id: '2',
            title: '其他支出'
          },
          {
            id: '1',
            title: '其他收入'
          }
        ]"
        @change="getResetting"
        v-model="dataForm.billType"
        value="id"
        label="title"
      ></ny-flod-tab>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <ny-button-group
          label="label"
          value="value"
          :list="[
            { label: '全部', value: '' },
            { label: '待审核', value: '0' },
            { label: '已通过', value: '1' },
            { label: '已拒绝', value: '2' }
          ]"
          v-model="dataForm.auditStatus"
          @change="getDataList"
        ></ny-button-group>
        <div style="display: flex; align-items: center; gap: 8px">
          <el-date-picker v-model="createDate" style="width: 240px !important" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="createDateChange" />
          <div style="width: 240px !important">
            <el-input v-model="dataForm.searchParam" placeholder="请输入费用名称/商品编码" clearable :prefix-icon="Search"></el-input>
          </div>
          <el-button type="primary" @click="getDataList()">{{ $t("query") }}</el-button>
          <el-button class="ml-8" @click="getResetting">{{ $t("resetting") }}</el-button>
        </div>
      </div>
      <div style="padding-top: 12px">
        <!-- pParams 所有参数都在，如需要特殊处理，去对应的组件内参数接收的地方处理 -->
        <!-- 其他支出 -->
        <otherOutTable v-if="dataForm.billType == '2'" :pParams="dataForm" ref="reconcileTableRef" />
        <!-- 其他收入 -->
        <otherInTable v-if="dataForm.billType == '1'" :pParams="dataForm" ref="reconcileTableRef" />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
import otherInTable from "./otherInTable.vue";
import otherOutTable from "./otherOutTable.vue";
import dayjs from "dayjs";

const reconcileTableRef = ref();
// 传参
const dataForm = reactive({
  billType: '2', // 顶部类型
  searchParam: "", //输入框
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  auditStatus: "" // 对账状态
});
const createDate = ref([]);

const createDateChange = () => {
  dataForm.startTime = createDate.value && createDate.value.length ? createDate.value[0] + " 00:00:00" : "";
  dataForm.endTime = createDate.value && createDate.value.length ? createDate.value[1] + " 23:59:59" : "";
};
const getDataList = () => {
  nextTick(() => {
    reconcileTableRef.value.handleUpDateData();
  });
};
const getResetting = () => {
  dataForm.searchParam = "";
  dataForm.startTime = "";
  dataForm.endTime = "";
  dataForm.auditStatus = "";
  createDate.value = [];
  nextTick(() => {
    reconcileTableRef.value.handleUpDateData();
  });
};

</script>

<style lang="scss" scoped>
.stat_card {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
  .stat_item {
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    height: 120px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: 1px solid #e5e6eb;
    .stat_top {
      padding: 12px;
      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }
    .stat_middle {
      padding: 4px 16px;
      font-weight: 500;
      font-size: 20px;
      color: #303133;
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .stat_below {
      display: flex;
      align-items: center;
      padding: 4px 16px 12px 16px;
      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
