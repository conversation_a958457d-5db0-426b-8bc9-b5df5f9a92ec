<template>
  <el-dialog :footer="null" v-model="visible" width="600" :title="`腾讯系-${dataForm.keywords}`" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-tabs v-model="activeName" style="min-height: 300px">
      <el-tab-pane :label="`${item.gameName}(${item.total})`" :name="index" v-for="(item, index) in dataForm.info" :key="index">
        <el-card shadow="never">
          <template #header>
            <el-text type="primary">绑定的账号</el-text>
          </template>
          <div style="padding: 20px; display: flex; align-items: center; flex-wrap: wrap">
            <span v-for="(item_, index_) in item.gameInfoBoList" :key="index_" style="margin-bottom: 8px; margin-right: 30px">{{ item_.shopCode }} &nbsp;{{ item_.areaTitle }}</span>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const activeName = ref(0);
let dataForm = reactive({});

const init = (obj?: any) => {
  visible.value = true;
  activeName.value = 0;
  dataForm = Object.assign(dataForm, obj);
  getInfo();
};

// 获取信息
const getInfo = () => {
  baseService
    .get("/phone/sysphonegame/listInfoNumber", {
      phone: dataForm.phone,
      keywords: dataForm.keywords,
      shopIds: dataForm.shopIds
    })
    .then((res) => {
      dataForm.info = (res.data || []).filter((ele: any) => ele.total != 0);
    });
};
defineExpose({
  init
});
</script>
<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 12px;
}
</style>
