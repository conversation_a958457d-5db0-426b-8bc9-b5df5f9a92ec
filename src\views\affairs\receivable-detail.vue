<template>
  <div class="mod-finance__receivable-detail">
    <el-card shadow="never" class="rr-view-ctx-card cardTop ny_form_card">
      <template #header>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
          <el-row :gutter="20" style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="state.dataForm.settlementUnit" placeholder="请输入往来单位" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="state.dataForm.documentType" placeholder="请选择单据类型" clearable>
                <el-option label="销售单" value="1"></el-option>
                <el-option label="服务单" value="2"></el-option>
                <el-option label="其他" value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.documentNumber" placeholder="请输入单据编号" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.businessType" placeholder="请输入业务类型" clearable></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="state.dataForm.department" placeholder="请输入所在部门" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="state.dataForm.gameName" placeholder="请输入游戏名称" clearable></el-input>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
                clearable
                style="width: 240px">
              </el-date-picker>
            </el-col>
            <el-col :span="6">
              <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
              <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <ny-table
        :state="state"
        :columns="columns"
        @pageSizeChange="state.pageSizeChangeHandle"
        @pageCurrentChange="state.pageCurrentChangeHandle"
        @selectionChange="state.dataListSelectionChangeHandle">

        <template #header>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          <el-button type="danger" @click="state.deleteHandle()" :disabled="!state.dataListSelections || !state.dataListSelections.length">{{ $t("deleteBatch") }}</el-button>
          <el-button type="info" @click="exportHandle()">{{ $t("export") }}</el-button>
          <el-button type="warning" @click="importHandle()">{{ $t("excel.import") }}</el-button>
        </template>

        <!-- 自定义列内容 -->
        <template #documentType="scope">
          <el-tag :type="getDocumentTypeTagType(scope.row.documentType)" size="small">
            {{ getDocumentTypeText(scope.row.documentType) }}
          </el-tag>
        </template>

        <template #documentDate="scope">
          <span>{{ formatTimeStamp(scope.row.documentDate, 'YYYY-MM-DD') }}</span>
        </template>

        <template #amount="scope">
          <span class="amount-text">{{ formatCurrency(scope.row.amount) }}</span>
        </template>

        <template #receivedAmount="scope">
          <span class="amount-text received">{{ formatCurrency(scope.row.receivedAmount) }}</span>
        </template>

        <template #unreceivedAmount="scope">
          <span class="amount-text unreceived">{{ formatCurrency(scope.row.unreceivedAmount) }}</span>
        </template>

        <template #status="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="small">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>

        <template #operation="scope">
          <el-button type="info" link size="small" @click="viewDetailHandle(scope.row)">详情</el-button>
          <el-button type="success" link size="small" @click="receiveRecordHandle(scope.row)">收款</el-button>
          <el-button v-if="state.hasPermission('finance:receivable:update')" type="primary" link size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
        </template>
      </ny-table>

      <el-pagination
        :current-page="state.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="state.limit"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="state.pageSizeChangeHandle"
        @current-change="state.pageCurrentChangeHandle">
      </el-pagination>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <ExcelImport ref="importRef" @refreshDataList="state.getDataList"></ExcelImport>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, onMounted } from "vue";
import AddOrUpdate from "./receivable-detail-add-or-update.vue";
import { formatTimeStamp } from "@/utils/method";
import { registerDynamicToRouterAndNext } from "@/router";
import { IObject } from "@/types/interface";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    type: "index",
    label: "序号",
    width: 60
  },
  {
    prop: "settlementUnit",
    label: "结算往来单位",
    width: 150
  },
  {
    prop: "documentType",
    label: "单据类型",
    width: 100
  },
  {
    prop: "documentNumber",
    label: "单据编号",
    width: 150
  },
  {
    prop: "businessType",
    label: "业务类型",
    width: 100
  },
  {
    prop: "documentDate",
    label: "本期日付",
    width: 120
  },
  {
    prop: "amount",
    label: "本期金额",
    width: 120
  },
  {
    prop: "receivedAmount",
    label: "已收金额",
    width: 120
  },
  {
    prop: "unreceivedAmount",
    label: "未收金额",
    width: 120
  },
  {
    prop: "businessCode",
    label: "业务员",
    width: 100
  },
  {
    prop: "department",
    label: "所在部门",
    width: 120
  },
  {
    prop: "gameName",
    label: "游戏名称",
    width: 150
  },
  {
    prop: "gameNumber",
    label: "游戏编号",
    width: 120
  },
  {
    prop: "status",
    label: "状态",
    width: 100
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right"
  }
]);

const view = reactive({
  getDataListURL: "/finance/receivable/page",
  getDataListIsPage: true,
  exportURL: "/finance/receivable/export",
  deleteURL: "/finance/receivable",
  deleteIsBatch: true,
  dataForm: {
    settlementUnit: "",
    documentType: "",
    documentNumber: "",
    businessType: "",
    department: "",
    gameName: "",
    gameNumber: "",
    startCreateDate: "",
    endCreateDate: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const createTimeRange = ref();

// 新增/修改处理
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 时间选择变化
const handleDateChange = () => {
  if (createTimeRange.value) {
    state.dataForm.startCreateDate = createTimeRange.value[0] + " 00:00:00";
    state.dataForm.endCreateDate = createTimeRange.value[1] + " 23:59:59";
  } else {
    state.dataForm.startCreateDate = "";
    state.dataForm.endCreateDate = "";
  }
};

// 获取单据类型标签类型
const getDocumentTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    "1": "primary", // 销售单
    "2": "success", // 服务单
    "3": "warning"  // 其他
  };
  return typeMap[type] || "info";
};

// 获取单据类型文本
const getDocumentTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    "1": "销售单",
    "2": "服务单",
    "3": "其他"
  };
  return typeMap[type] || "未知";
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    "1": "success", // 已收齐
    "2": "warning", // 部分收款
    "3": "danger",  // 逾期
    "4": "info"     // 未收款
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    "1": "已收齐",
    "2": "部分收款",
    "3": "逾期",
    "4": "未收款"
  };
  return statusMap[status] || "未知";
};

// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 查看详情
const viewDetailHandle = (row: IObject) => {
  const routeParams = {
    path: `/finance/receivable-detail-view`,
    query: {
      id: row.id,
      _mt: `应收详情 - ${row.settlementUnit}`
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 收款记录
const receiveRecordHandle = (row: IObject) => {
  const routeParams = {
    path: `/finance/receive-record`,
    query: {
      receivableId: row.id,
      settlementUnit: row.settlementUnit,
      _mt: `收款记录 - ${row.settlementUnit}`
    }
  };
  registerDynamicToRouterAndNext(routeParams);
};

// 重置操作
const getResetting = () => {
  createTimeRange.value = undefined;
  state.dataForm.settlementUnit = "";
  state.dataForm.documentType = "";
  state.dataForm.documentNumber = "";
  state.dataForm.businessType = "";
  state.dataForm.department = "";
  state.dataForm.gameName = "";
  state.dataForm.gameNumber = "";
  state.dataForm.startCreateDate = "";
  state.dataForm.endCreateDate = "";
  state.getDataList();
};

// 导出
const exportHandle = () => {
  baseService.get("/finance/receivable/export", view.dataForm).then((res) => {
    if (res) {
      fileExport(res, "应收明细账列表");
    }
  });
};

// 导入
const importRef = ref();
const importHandle = () => {
  importRef.value.init();
};

onMounted(() => {
  state.getDataList();
});
</script>

<style lang="less" scoped>
.cardTop {
  margin-bottom: 20px;
}

.amount-text {
  color: #e6a23c;
  font-weight: 500;

  &.received {
    color: #67c23a;
  }

  &.unreceived {
    color: #f56c6c;
  }
}
</style>
