<template>
  <el-drawer v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" size="40%" class="ny-drawer">
    <el-card>
      <el-form label-position="top" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="游戏名称" prop="gameId">
              <el-select v-model="dataForm.gameId" placeholder="请选择游戏" @change="changeGame" :disabled="currentGame.official == 1">
                <el-option :label="item.title" :value="item.id" v-for="(item, index) in gamesList" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="属性名称" prop="name">
              <el-input @blur="validateName" v-model="dataForm.name" placeholder="属性名称" :disabled="currentGame.official == 1"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="类型" prop="type">
              <ny-radio-group v-model="dataForm.type" :disabled="dataForm.id || currentGame.official == 1" dict-type="game_properties_type" @change="radioGroupChage"></ny-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="启用区间搜索" prop="isSection">
              <!-- 1：启用 0：禁用 -->
              <el-switch v-model="dataForm.isSection" :active-value="1" :inactive-value="0" :disabled="dataForm.type != '3'" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否商城显示" prop="isShow">
              <!-- 2025年4月21日17:38:24 改为正常数值判断 【jzj、ys】 -->
              <el-switch v-model="dataForm.isShow" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否标题显示" prop="isTitle">
              <!-- 0：启用 1：禁用 -->
              <el-switch v-model="dataForm.isTitle" :active-value="0" :inactive-value="1" />
            </el-form-item>
          </el-col>

          <!-- 0：否 1：是 ；只在测试或演示环境使用-->
          <!-- <el-col :span="6">
            <el-form-item label="固定文本属性" prop="isFixedAttr">
              <el-switch v-model="dataForm.isFixedAttr" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col> -->
          <el-col :span="24" v-if="dataForm.type != '3'">
            <el-form-item label="属性粘贴填充" prop="attributePasteFill">
              <el-input :disabled="currentGame.official == 1" v-model="attributePasteFill" type="textarea" placeholder="属性之间以逗号隔开。如：XXX，XXX" :autosize="{ minRows: 2, maxRows: 4 }" @input="inputChange" />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="dataForm.type != '3'">
            <el-form-item label="属性明细">
              <el-table size="small" :data="dataForm.children" border style="width: 100%" margin-bottom="20px" row-key="id">
                <el-table-column label="游戏属性" prop="name" min-width="130" align="center">
                  <template #default="scope">
                    <el-input :disabled="currentGame.official == 1" size="small" v-model="scope.row.name"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="商城显示" prop="name" min-width="40" align="center">
                  <template #default="scope">
                    <!-- 0：启用 1：禁用 -->
                    <el-switch v-model="scope.row.isShow" :active-value="1" :inactive-value="0" />
                  </template>
                </el-table-column>
                <!-- <el-table-column label="排序" prop="name" min-width="40" align="center" v-if="dataForm.id">
                  <template #default="scope">
                    <el-input-number size="small" style="width: 100%;" v-model="scope.row.sort"></el-input-number>
                  </template>
                </el-table-column> -->
                <el-table-column label="操作" align="center">
                  <template #header>
                    <div style="display: flex; justify-content: center">
                      <el-button v-if="currentGame.official !== 1" type="primary" size="small" @click="addColumn()">新增</el-button>
                      <el-button type="info" size="small" @click="exportColumn()" v-if="dataForm.id">导出</el-button>
                      <el-upload :multiple="false" :limit="1" :show-file-list="false" :auto-upload="true" accept=".xls, .xlsx" style="margin-left: 12px" :http-request="importColumn">
                        <template #trigger>
                          <el-button type="warning" size="small" v-if="dataForm.id">导入</el-button>
                        </template>
                      </el-upload>
                    </div>
                  </template>
                  <template #default="scope">
                    <el-button v-if="currentGame.official !== 1" type="danger" size="small" text @click="DelColumn(scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
                <!-- 空状态 -->
                <template #empty>
                  <div style="padding: 68px 0">
                    <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                  </div>
                </template>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template v-slot:footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { fileExport } from "@/utils/utils";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: null,
  gameId: "",
  pid: "",
  name: "",
  type: "1",
  isSection: 0,
  isShow: 0,
  isTitle: 0,
  isFixedAttr: 0,
  sort: "",
  deptId: "",
  tenantCode: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: "",
  isDelete: "",
  images: "",
  imagesSource: "",
  children: <any>[]
});
const validateName = async () => {
  if (resData.value.name == dataForm.name) return;
  if (dataForm.name && dataForm.name.length > 0) {
    let res = await baseService.get("/game/attribute/getSameName", { gameId: dataForm.gameId, name: dataForm.name });
    if (res.data) {
      ElMessage.warning("属性名称已存在！");
    }
    return res.data;
  }
};
const rules = ref({
  gameId: [{ required: true, message: "游戏名称不能为空", trigger: "change" }],
  name: [{ required: true, message: "属性名称不能为空", trigger: "blur" }]
});
const resData = ref();
const gamesList = ref(<any>[]); // 游戏列表
const time = ref<any>(); // 定时器
const attributePasteFill = ref(""); // 属性粘贴填充

const init = (id?: any, gameId?: string) => {
  visible.value = true;
  resData.value = null;
  dataForm.id = id ? id : null;
  dataForm.gameId = gameId ? gameId : "";
  getGamesList();
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};
const currentGame = ref({});
// 游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
    currentGame.value = dataForm.gameId ? res.data.find((ele) => ele.id == dataForm.gameId) : {};
  });
};
const changeGame = () => {
  currentGame.value = dataForm.gameId ? gamesList.value.find((ele) => ele.id == dataForm.gameId) : {};
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/game/attribute/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    resData.value = { ...res.data };
  });
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (resData.value && resData.value.name !== dataForm.name) {
      let same = await validateName();
      if (same) return;
    }
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/game/attribute", dataForm)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 属性拆分
const inputChange = (res: any) => {
  if (res != "") {
    clearTimeout(time.value);
    time.value = setTimeout(() => {
      let array = [];
      array = res.split(/,|，|\n|" "|\s/);
      array.forEach((v: any) => {
        if (v != undefined && v != "" && Object.keys(v).length > 0) {
          let index = dataForm.children.findIndex((v1: any) => v1.name == v);
          if (index < 0) {
            // 哈哈哈h，哈哈哈，哈哈，哈哈哈hh，呃呃呃呃，V6，V7，V8，V9，V10，无
            dataForm.children.push({ name: v });
          }
        }
      });
      attributePasteFill.value = "";
    }, 1200);
  }
};
// 属性新增
const addColumn = () => {
  dataForm.children.push({ name: "" });
};
// 属性删除
const DelColumn = (index: number, row: any) => {
  dataForm.children.splice(index, 1);
};
// 导出
const exportColumn = () => {
  baseService.get("game/attributeChildren/export", { pId: dataForm.id }).then((res) => {
    ElMessage.success("导出成功！");
    if (res) {
      fileExport(res, "筛选属性导出");
    }
  });
};

// 导入
const importColumn = (option: any) => {
  console.log(option.file, dataForm);
  baseService.post("game/attribute/childrenImport", { file: option.file, gameId: dataForm.gameId, pId: dataForm.id }, { "Content-Type": "multipart/form-data" }).then((res) => {
    if (res.code == 0) {
      ElMessage.success("导入成功！");
      getInfo(dataForm.id);
    } else {
      ElMessage.error("导入失败！");
    }
  });
};

// 属性类型回调
const radioGroupChage = (value: any) => {
  if (!dataForm.name) {
    ElMessage.warning("请填写属性名称");
    dataForm.type = "1";
    return;
  }

  if (value == 3) {
    baseService.get("/game/attribute/page", { gameId: dataForm.gameId }).then((res) => {
      res.data.forEach((item: any) => {
        if (item.name == dataForm.name && item.type == 1) {
          dataForm.name = `${dataForm.name}【文本】`;
        }
        if (item.name == dataForm.name && item.type == 2) {
          dataForm.name = `${dataForm.name}【合计】`;
        }
      });
    });
  } else {
    if (dataForm.name.includes("【文本】") || dataForm.name.includes("【合计】")) {
      dataForm.name = dataForm.name.slice(0, -4);
    }
  }
};

defineExpose({
  init
});
</script>
