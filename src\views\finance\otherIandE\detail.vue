<template>
  <el-dialog v-model="dialogVisible" title="收款信息" width="480" :footer="null" @close="close">
    <div class="cardDescriptions" style="padding: 0;" v-loading="btnLoading">
      <el-descriptions :column="1" border class="descriptions" style="margin-bottom: 12px">
        <el-descriptions-item>
          <template #label>收款金额(元)</template>
          {{ dataForm.amount || "-" }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>账户类型</template>
          <span v-if="dataForm.accountType == '1'">支付宝</span>
          <span v-else-if="dataForm.accountType == '2'">微信</span>
          <span v-else-if="dataForm.accountType == '3'">银行卡</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>收款账户</template>
          {{ dataForm.accountNumber || "-" }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>姓名</template>
          {{ dataForm.accountName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item v-if="dataForm.accountType == '3'">
          <template #label>开户银行</template>
          {{ dataForm.bankName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { ref, reactive, onMounted } from "vue";
const emits = defineEmits(["refresh"]);
const dialogVisible = ref(false);
const dataForm = ref({} as any);
const btnLoading = ref(false);
const init = (row: any) => {
  dialogVisible.value = true;
  btnLoading.value = true;
  console.log(row,'===sdfksd fsd;lfk sdjlfdslkfj ');
  baseService.get("/automaticReconciliation/othercost/" + (row.otherCostId ? row.otherCostId : row.id) ).then(res=>{
    dataForm.value = res.data;
  }).finally(()=>{
    btnLoading.value = false;
  })
};
const close = () => {
  dialogVisible.value = false;
};

defineExpose({
  init
});
</script>
<style scoped lang="less"></style>
