<template>
    <el-dialog v-model="dialogVisible" title="自动推送设置" width="1160" :before-close="handleClose" >
    <div class="settingPage">
        <div class="leftCenter">
            <div class="titleSty">选择合作商</div>
            <div class="menuList">
                <div class="menuItem"
                    :class="{active: dataForm.partnerId == item.id}"
                    v-for="(item,index) in curAllPartnerList" 
                    :key="index"
                    @click="partenerClick(item.id)"
                    v-if="functionType == 1"
                >{{ item.name }}</div>
                <div class="menuItem"
                    :class="{active: dataForm.partnerId == item.id}"
                    v-for="(item,index1) in curAllPartnerList" 
                    :key="index1"
                    @click="partenerClick(item.id)"
                    v-else
                >{{ item.companyName }}</div>
            </div>
        </div>
        <div class="rightCenter">
            <div class="titleSty">自动推送设置</div>
            <div class="center">
                <div class="line" style="height: 80px;" v-if="functionType == 1">
                    <div class="labelDiv">推送店铺<span style="color: red;">*</span> </div>
                    <div class="valueDiv">
                        <el-select v-model="dataForm.scriptUserId" >
                        <el-option
                            v-for="item in shopList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                    </div>
                </div>
                <div class="line" style="height: 120px;">
                    <div class="labelDiv">自动推送</div>
                    <div class="valueDiv">
                        <div>
                            <el-radio-group v-model="dataForm.status">
                                <el-radio :value="1" border>开启</el-radio>
                                <el-radio :value="0" border>关闭</el-radio>
                            </el-radio-group>
                            <div style="margin-top: 8px;"><el-icon color="#909399"><WarningFilled /></el-icon><el-text class="mx-1" type="info">开启自动上架后，默认所有游戏自动上架；可在下方选择取消部分游戏自动上架</el-text></div>
                        </div>
                    </div>
                </div>
                <div class="line" style="height: 342px;">
                    <div class="labelDiv">取消自动上架的游戏</div>
                    <div class="valueDiv">
                        <div>
                            <el-checkbox-group v-model="dataForm.excludeGameIds">
                                <el-checkbox :label="item.gameName" :value="item.gameId" v-for="(item, index) in allSelectArr" :key="index"/>
                            </el-checkbox-group>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submit()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const shopList = ref(<any>[]);

const dataForm = reactive({
    id: null,
    partnerId: '',
    status: 0,
    excludeGameIds: [],
    type: 1,
    scriptUserId: null
})

// NOTE: 合作商列表
const curAllPartnerList = ref(<any>[]);
// NOTE: 游戏列表
const allSelectArr = ref(<any>[]);

// 获取合作商数据
const getSelectInfo = () => {
  baseService.get(functionType.value == 1 ? "/script/sysscriptpartnerinfo/allList" : "/partner/partner/partnerList").then((res) => {
    if (res.code == 0) {
      curAllPartnerList.value = res.data || [];
      if (curAllPartnerList.value.length > 0) {
        dataForm.partnerId = curAllPartnerList.value[0].id;
        getGameList(dataForm.partnerId);
        getShopList(dataForm.partnerId);
      }
    }
  });
};

// 获取游戏列表
const getGameList = (partnersid: any) => {
  baseService.get("/partner/partner/partnerMappingGameList", { partnerId: partnersid }).then((res_) => {
    if (res_.code == 0) {
        allSelectArr.value = res_.data;
        getConfigDetail();
    }
  });
};

// 获取设置详情
const getConfigDetail = () =>{
    baseService.get('/shop/autoUpConfig/page',{ partnerId: dataForm.partnerId, type: functionType.value} ).then(res=>{
        if(res.code == 0 && res.data.list.length){
            const info = res.data.list[0]
            info.excludeGameIds = info.excludeGameIds.split(',');
            Object.assign(dataForm,info);
        }else{
            dataForm.id = null;
            dataForm.status = 0;
            dataForm.excludeGameIds = [];
            dataForm.scriptUserId = null
        }
    })
}

// 合作商点击回调
const partenerClick = (id:any) =>{
    dataForm.partnerId = id;
    getGameList(id);
    getShopList(id);
}

// 店铺搜索
const getShopList = (partnerId:any) =>{
    baseService.get("/script/sysscriptuser/page",{ scriptPartnerId: partnerId }).then(res=>{
        if(res.data){
            shopList.value = res.data.list;
        }
    })
}

// 保存配置
const submitLoading = ref(false);
const submit = () =>{
    if(functionType.value == 1 && !dataForm.scriptUserId){
        ElMessage.warning('请选择推送店铺');
        return;
    }
    const params = JSON.parse(JSON.stringify(dataForm));
    params.excludeGameIds = params.excludeGameIds.join(',');
    params.type = functionType.value;
    submitLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/shop/autoUpConfig", params).then(res=>{
        if(res.code == 0){
            ElMessage.success('保存成功');
            getConfigDetail();
        }
    }).finally(()=>{
        submitLoading.value = false;
    })
}
// 类型type(1:同步上架,2:API上架)
const functionType = ref(1)
const init = (type:any) =>{
    functionType.value = type;
    dialogVisible.value = true;
    getSelectInfo();
}

// 弹窗关闭回调
const handleClose = () =>{
    dialogVisible.value = false;
}

defineExpose({
  init
});
</script>

<style lang='less' scoped>
.settingPage{
    width: 1128px;
    height: 500px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .leftCenter{
        width: 186px;
        .menuList{
            height: 462px;
            border: 1px solid #EBEEF5;
            border-radius: 4px;
            overflow: auto;
            .menuItem{
                padding: 18px 20px;
                font-weight: 400;
                font-size: 12px;
                color: #303133;
                line-height: 14px;
                cursor: pointer;
                &:hover{
                    background-color: var(--color-primary-light);
                    color: var(--color-primary);
                }
            }

            .active{
                background-color: var(--color-primary-light);
                color: var(--color-primary);
            }
        }
    }
    .rightCenter{
        width: 930px;
        .center{
            width: 100%;
            height: 462px;
            border: 1px solid #EBEEF5;
            border-radius: 4px;
            overflow: auto;
            .line{
                display: flex;
                .labelDiv{
                    width: 144px;
                    background: #F5F7FA;
                    border: 1px solid #EBEEF5;
                    padding: 12px;
                    display: flex;
                    align-items: center;
                }
                .valueDiv{
                    width: 786px;
                    border: 1px solid #E4E7ED;
                    padding: 24px;
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}
</style>