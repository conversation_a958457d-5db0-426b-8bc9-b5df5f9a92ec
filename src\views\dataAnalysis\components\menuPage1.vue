<template>
    <div>
        <div class="menu_title">运营效能洞察</div>
        <div class="top">
            <ny-button-group :list="[
                { dictLabel: '数据概况', dictValue: '0' },
                { dictLabel: '经营明细', dictValue: '1' },
                { dictLabel: '利润分布', dictValue: '2' },
                { dictLabel: '转化周期', dictValue: '3' }
            ]" v-model="typeValue" @change="buttonGroupChange"></ny-button-group>
            <div class="flx-align-center">
                <NyDropdownMenu v-model="dataForm.gameId" :list="gameList" labelKey="gameName" valueKey="gameId"
                    placeholder="选择游戏" clearable></NyDropdownMenu>
                <!-- <NyDropdownMenu v-model="dataForm.recyclingChannelId" :list="recyclingChanneList" labelKey="channelName"
                    valueKey="channelId" placeholder="回收渠道"
                    v-if="typeValue == '1' || typeValue == '2' || typeValue == '3'" clearable></NyDropdownMenu>
                <NyDropdownMenu v-model="dataForm.salesChannelId" :list="salesChannelList" labelKey="channelName"
                    valueKey="channelId" placeholder="销售渠道"
                    v-if="typeValue == '1' || typeValue == '2' || typeValue == '3'" clearable></NyDropdownMenu> -->
                <NyDropdownMenu v-model="dataForm.purchaseEmployeeId" :list="employeeList" labelKey="employeeName"
                    valueKey="employeeId" placeholder="回收员工"
                    v-if="typeValue == '1' || typeValue == '2' || typeValue == '3'" clearable></NyDropdownMenu>
                <el-button type="primary" @click="queryChagne" style="margin-left: 12px">{{ $t("query") }}</el-button>
                <el-button @click="resettingChange">{{ $t("resetting") }}</el-button>
            </div>
        </div>
        <!-- <div class="refreshDate">更新时间：{{ formatTimeStamp(refreshTime) }} <span @click="refreshTime = new Date().getTime(); queryChagne();">刷新</span> </div> -->
        <!-- 选择游戏 -->
        <selectGame ref="selectGameRef" v-if="typeValue == '0'"></selectGame>
        <!-- 经营明细 -->
        <BusinessDetails ref="BusinessDetailsRef" v-if="typeValue == '1'"></BusinessDetails>
        <!-- 利润分布 -->
        <ProfitDistribution ref="ProfitDistributionRef" v-if="typeValue == '2'"></ProfitDistribution>
        <!-- 转化周期 -->
        <ConversionCycle ref="ConversionCycleRef" v-if="typeValue == '3'"></ConversionCycle>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { formatTimeStamp } from "@/utils/method";
import selectGame from "./menuPageList/selectGame.vue";
import BusinessDetails from "./menuPageList/BusinessDetails.vue";
import ProfitDistribution from "./menuPageList/ProfitDistribution.vue";
import ConversionCycle from "./menuPageList/ConversionCycle.vue";
import baseService from "@/service/baseService";

const typeValue = ref("0");
const refreshTime = ref();
const selectGameRef = ref();
const BusinessDetailsRef = ref();
const ProfitDistributionRef = ref();
const ConversionCycleRef = ref();

const dataForm = ref({
    gameId: null,
    recyclingChannelId: null,
    salesChannelId: null,
    purchaseEmployeeId: null
});

const gameList = ref(); // 游戏列表
const recyclingChanneList = ref(); // 回收渠道列表
const salesChannelList = ref(); // 销售渠道列表
const employeeList = ref(); // 选择员工列表

// 游戏列表
const getGameList = () => {
    baseService.get("/dataAnalysis/gameSearchList").then((res) => {
        gameList.value = res.data;
    });
};

// 回收渠道列表
// 渠道类型 0、出售 1、收购 2、售后 3、合作商出售
const getRecyclingChanneList = (channelType: number) => {
    baseService.get("/dataAnalysis/channelSearchList", { channelType }).then((res) => {
        // 销售渠道
        if (channelType == 0) {
            salesChannelList.value = res.data;
            if (selectGameRef.value) {
                selectGameRef.value.getRecyclingChanneList(0, res.data);
            }
        }
        // 回收渠道
        if (channelType == 1) {
            recyclingChanneList.value = res.data;
            if (selectGameRef.value) {
                selectGameRef.value.getRecyclingChanneList(1, res.data);
            }
        }
    });
};

// 员工列表
const getEmployeeList = () => {
    baseService.get("/dataAnalysis/employeeSearchList").then((res) => {
        employeeList.value = res.data;
        if (selectGameRef.value) {
            selectGameRef.value.getEmployeeList(res.data);
        }
    });
};

// 查询
const queryChagne = () => {
    if (selectGameRef.value) {
        selectGameRef.value.init({ gameId: dataForm.value.gameId });
    }
    if (BusinessDetailsRef.value) {
        BusinessDetailsRef.value.init(dataForm.value);
    }
    if (ProfitDistributionRef.value) {
        ProfitDistributionRef.value.init(dataForm.value);
    }
    if (ConversionCycleRef.value) {
        ConversionCycleRef.value.init(dataForm.value);
    }
};

// 重置
const resettingChange = () => {
    dataForm.value.gameId = null;
    dataForm.value.recyclingChannelId = null;
    dataForm.value.salesChannelId = null;
    dataForm.value.employeeId = null;

    if (selectGameRef.value) {
        selectGameRef.value.init({ gameId: dataForm.value.gameId });
    }
    if (BusinessDetailsRef.value) {
        BusinessDetailsRef.value.init(dataForm.value);
    }
    if (ProfitDistributionRef.value) {
        ProfitDistributionRef.value.init(dataForm.value);
    }
    if (ConversionCycleRef.value) {
        ConversionCycleRef.value.init(dataForm.value);
    }
};

// tab切换
const buttonGroupChange = () => {
    nextTick(() => {
        if (selectGameRef.value) {
            selectGameRef.value.getRecyclingChanneList(0, salesChannelList.value);
        }
        if (selectGameRef.value) {
            selectGameRef.value.getRecyclingChanneList(1, recyclingChanneList.value);
        }
        if (selectGameRef.value) {
            selectGameRef.value.getEmployeeList(employeeList.value);
        }
    })
};

onMounted(() => {
    refreshTime.value = new Date().getTime();
    getGameList();
    getRecyclingChanneList(0);
    getRecyclingChanneList(1);
    getEmployeeList();
});
</script>

<style lang="less" scoped>
.menu_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
}

.top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.refreshDate {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #4e5969;

    span {
        color: #4165d7;
        cursor: pointer;
    }
}
</style>
