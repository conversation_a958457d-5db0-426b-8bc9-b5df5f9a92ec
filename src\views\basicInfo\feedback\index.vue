<template>
  <div class="mod-basicInfo__tbannouncement">
    <el-card shadow="never" class="rr-view-ctx-card mb-20 ny_form_card">
      <ny-form-slot>
        <template #content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-select v-model="state.dataForm.status" placeholder="请选择状态" clearable>
                <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="state.dataForm.type" placeholder="请选择反馈类型" clearable>
                <el-option v-for="(item, index) in feedbackType" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker v-model="state.dataForm.createTime" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
          </el-form>
        </template>
        <template #button>
          <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
          <el-button @click="resetForm()" type="info">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>

    <el-card shadow="never" class="rr-view-ctx-card">
      <ny-table :state="state" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('basicInfo:feedback:delete')" @click="state.deleteHandle()" type="danger">{{ $t("delete") }}</el-button>
        </template>

        <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :showOverflowTooltip="true">
          <el-table-column prop="id" label="编号" minWidth="100" header-align="center" align="center"></el-table-column>
          <el-table-column prop="type" label="分组" minWidth="100" header-align="center" align="center"></el-table-column>
          <el-table-column prop="imageUrl" label="图片" minWidth="100" header-align="center" align="center">
            <template #default="{ row }">
              <img :src="row.imageUrl" alt="" style="width: 50px; height: 50px" :preview-src-list="[row.imageUrl]" :preview-teleported="true" />
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" minWidth="100" header-align="center" align="center"></el-table-column>
          <el-table-column prop="detailsName" label="绑定文章" minWidth="100" header-align="center" align="center"></el-table-column>

          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="240">
            <template v-slot="scope">
              <el-button v-if="state.hasPermission('basicInfo:feedback:info')" type="success" text bg @click="getInfo(scope.row, 'view')">{{ $t("view") }}</el-button>
              <el-button v-if="state.hasPermission('basicInfo:feedback:reply') && scope.row.status == 0" type="primary" text bg @click="getInfo(scope.row, 'reply')">{{ $t("reply") }}</el-button>
              <el-button v-if="state.hasPermission('basicInfo:feedback:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
            </template>
          </el-table-column>
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </ny-table>
    </el-card>

    <!-- 查看和回复 -->
    <feedback-info-or-reply ref="feedbackInfoOrReplyRef" :key="feedbackInfoOrReplyKey"></feedback-info-or-reply>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import feedbackInfoOrReply from "./feedback-info-or-reply.vue";
import { nextTick, reactive, ref, toRefs } from "vue";

// 状态
const stateList = ref([
  { label: "全部", value: "" },
  { label: "未回复", value: "0" },
  { label: "已回复", value: "1" }
]);

// 反馈类型
const feedbackType = ref([
  { label: "投诉", value: "0" },
  { label: "建议", value: "1" }
]);

const view = reactive({
  getDataListURL: "/basicInfo/banner/page",
  getDataListIsPage: true,
  deleteURL: "/basicInfo/banner",
  deleteIsBatch: true,
  dataForm: {
    status: "",
    type: "",
    createTime: ""
  }
});

// 重置
const resetForm = () => {
  state.dataForm.status = "";
  state.dataForm.type = "";
  state.dataForm.createTime = "";
};

const state = reactive({ ...useView(view), ...toRefs(view) });

// 查看详情
const feedbackInfoOrReplyKey = ref(0);
const feedbackInfoOrReplyRef = ref();

const getInfo = (data: any = {}, type: string) => {
  feedbackInfoOrReplyKey.value++;
  nextTick(() => {
    feedbackInfoOrReplyRef.value.init(data.id, type);
  });
};
</script>
