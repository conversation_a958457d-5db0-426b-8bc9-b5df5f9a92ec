<template>
  <el-dialog width="800px" v-model="dialogVisible" class="im-custom-dialog" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #header="{ close, titleId, titleClass }">
      <div class="im-custom-dialog-header">
        <p class="dialog-title">创建回收订单</p>
      </div>
    </template>

    <div class="create-order-dialog-content flx">
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules" class="w-100">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="回收渠道" prop="channelId">
              <!-- <el-select v-model="dataForm.channelId" :disabled="!userStore.state.isPlatform" filterable placeholder="选择回收渠道">
                                <el-option v-for="(item, index) in channelList" :key="index" :label="item.title" :value="item.id"></el-option>
                            </el-select> -->
              <el-cascader style="width: 100%" :show-all-levels="false" v-model="selectedOptions" :options="ChannelTreeList" :props="{ label: 'title', value: 'id' }" placeholder="请选择" @change="handleChange" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="游戏名称" prop="gameId">
              <el-select v-model="dataForm.gameId" filterable placeholder="请选择游戏名" @change="gameChange">
                <el-option v-for="(item, index) in gamesList" :key="index" :label="item.title" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.gameId">
            <el-form-item label="选择大区" prop="district">
              <el-select v-model="dataForm.district" placeholder="选择大区" @change="(dataForm.server = ''), getServerList()">
                <el-option v-for="item in districtList" :key="item.id" :label="item.title" :value="item.id + '-' + item.title"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serverList && serverList.length">
            <el-form-item label="选择服务器" prop="server">
              <el-select v-model="dataForm.server" placeholder="选择服务器">
                <el-option v-for="item in serverList" :key="item.id" :label="item.title" :value="item.id + '-' + item.title"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="游戏账号" prop="gameAccount">
              <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回收价格(元)" prop="purchasePrice">
              <el-input v-model="dataForm.purchasePrice" placeholder="请输入回收价格"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卖方手机号" prop="customerPhone">
              <el-input disabled v-model="dataForm.customerPhone" placeholder="请输入卖方手机号"></el-input>
            </el-form-item>
          </el-col>
          <!-- 租户才可以选择客服 -->
          <el-col :span="12">
            <el-form-item label="平台客服">
              <el-select v-if="!userStore.state.isPlatform" @change="selectService" v-model="dataForm.appraiseUserId" placeholder="请选择平台客服">
                <el-option v-for="(item, index) in platformCustomerServiceList" :key="index" :label="item.nickname" :value="item.id"></el-option>
              </el-select>
              <el-input v-else disabled :value="userStore.state.user.nickname || userStore.state.user.realName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择包赔权益">
              <el-select v-model="bp1">
                <el-option v-for="(item, index) in bpSingleList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择增值包赔">
              <el-select v-model="bp2" multiple>
                <el-option v-for="(item, index) in bpMulList" :key="index" :value="item.id" :label="item.title">{{ item.title }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="flx-justify-end automatic-reply-footer">
      <el-button :loading="btnLoading" @click="handleCancel">取消</el-button>
      <!-- 租户端可以创建群聊  平台只创建回收订单 -->
      <el-button v-if="!userStore.state.isPlatform" :loading="btnLoading" type="primary" @click="handleSubmit(true)">确定并创建群聊</el-button>
      <el-button v-else :loading="btnLoading" type="primary" @click="handleSubmit(false)">确定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits } from "vue";
import { useImStore } from "@/store/im";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const imStore = useImStore();
const userStore = useAppStore();
const emits = defineEmits(["sendOrder"]);

// 卖家imUid
const sellersImUid = ref("");

const dialogVisible = ref(false);
const init = (user: any) => {
  dialogVisible.value = true;
  dataForm.value.customerPhone = user.sessionData ? user.sessionData.mobile : "";
  sellersImUid.value = user.sessionData.imUid;
  if (userStore.state.isPlatform) {
    dataForm.value.appraiseUserId = userStore.state.user.id;
  }
  getGamesList();
  getPompensationList();
  getPlatformCustomerService();
  getChannelList();
  getChannelTree();
};

// 回收渠道
const channelList = ref(<any>[]);
const getChannelList = () => {
  baseService
    .get("/channel/channel/page", {
      limit: 9999,
      state: 0,
      channelType: 1
    })
    .then((res) => {
      channelList.value = res.data.list;
      if (!userStore.state.isPlatform) {
        let data = res.data.list.find((item: any) => item.title == "合作商回收");
        dataForm.value.channelId = data.id;
        dataForm.value.channelName = data.title;
      }
    });
};

// 获取新版回收渠道
const ChannelTreeList = ref(<any>[]);
const getChannelTree = () => {
  baseService.get("/channel/channel/getChannelTree", { type: "1" }).then((res) => {
    res.data.map((item: any) => {
      if (item.children.length == 0) {
        item.disabled = true;
      }
    });
    ChannelTreeList.value = res.data;
  });
};

const selectedOptions = ref(<any>[]);
const handleChange = (selectedData: any) => {
  if (selectedData && selectedData.length > 0) {
    dataForm.value.channelId = selectedData[selectedData.length - 1];
    baseService.get("/channel/channel/" + dataForm.value.channelId).then((res) => {
      if (res.data) {
        dataForm.value.channelName = res.data.title;
      }
    });
  } else {
    dataForm.value.channelId = "";
    dataForm.value.channelName = "";
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

const dataForm = ref(<any>{});

const rules = {
  channelId: [{ required: true, message: "请选择渠道", trigger: "blur" }],
  gameId: [{ required: true, message: "请输入游戏名称", trigger: "blur" }],
  district: [{ required: true, message: "请选择大区", trigger: "change" }],
  server: [{ required: true, message: "请选择服务器", trigger: "change" }],
  gameAccount: [{ required: true, message: "请输入游戏账号", trigger: "blur" }],
  purchasePrice: [{ required: true, message: "请输入回收价格", trigger: "blur" }],
  customerPhone: [{ required: true, message: "请输入卖方手机号", trigger: "blur" }]
};

// 游戏列表
const gamesList = ref(<any>[]);
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
  });
};

// 选择游戏
const gameChange = () => {
  dataForm.value.district = "";
  dataForm.value.server = "";
  getdistrictList();
};

// 大区列表
const districtList = ref(<any>[]);
const getdistrictList = () => {
  baseService.get("/game/sysgame/get/" + dataForm.value.gameId).then((res: any) => {
    districtList.value = res.data?.areaDtoList;
  });
};

// 服务列表
const serverList = ref(<any>[]);
const getServerList = () => {
  serverList.value = districtList.value.find((item: any) => item.id == dataForm.value.district.split("-")[0]).children;
};

// 平台客服
const platformCustomerServiceList = ref([]);
const getPlatformCustomerService = () => {
  baseService.post("/im/login/partner/contacts").then((res) => {
    if (res.code == 0) {
      platformCustomerServiceList.value = res.data;
    }
  });
};

// 回收渠道名称
const getChannelName = (data: any) => {
  dataForm.value.channelName = data.title;
};

// 客服imUid
const serviceImUid = ref("");

// 选择客服
const selectService = (val: any) => {
  // serviceImUid.value = val.imUid;
  // console.log(serviceImUid.value, platformCustomerServiceList.value)
};

// 提交
const btnLoading = ref(false);
const formRef = ref();
const handleSubmit = (createGroup: boolean) => {
  console.log(serviceImUid.value, "客服id");
  console.log(imStore.imUid, "当前用户 imUid");
  console.log(sellersImUid.value, "卖家imUid");

  formRef.value.validate((valid: any) => {
    if (!valid) return;
    btnLoading.value = true;
    let params = JSON.parse(JSON.stringify(dataForm.value));
    params.gameName = gamesList.value.find((item: any) => item.id == dataForm.value.gameId)?.title;

    let guaranteeIds: any = bp2.value ? JSON.parse(JSON.stringify(bp2.value)) : [];
    if (bp1.value) guaranteeIds.push(bp1.value);
    params.baopeiIds = guaranteeIds;

    // let channel = channelList.value.find((item: any) => item.id == dataForm.value.channelId);
    // params.channelName = channel.title;

    if (params.server) {
      params.serverId = params.server.split("-")[0];
      params.serverName = params.district.split("-")[1] + "-" + params.server.split("-")[1];
    } else {
      params.serverId = params.district.split("-")[0];
      params.serverName = params.district.split("-")[1];
    }
    delete params.server;
    delete params.district;

    // 回收类型
    params.acquisitionType = userStore.state.isPlatform ? 1 : 2;

    // 现在创建商品 如果又回收人  会自动创建一个 回收订单挺威09:47所有要区分是从哪创建的
    params.isFromShop = false;

    baseService
      .post("/purchase/add", params)
      .then((res) => {
        if (res.code == 0) {
          if (createGroup) {
            btnLoading.value = true;
            return createGroupHandle(res.data.orderId);
          }
          ElMessage.success("提交成功");
          let data = res.data;
          emits("sendOrder", {
            sn: data.sn,
            id: data.id,
            title: data.title,
            // 价格
            dealAmount: data.amount,
            // 创建时间
            createDate: data.createDate,
            // 游戏名称
            gameName: data.gameName,
            // 包赔费
            guaranteePrice: data.guaranteePrice,
            // 收购订单类型
            orderType: "purchase"
          });
          dialogVisible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

// 创建群聊
const createGroupHandle = (orderId: string) => {
  if (!dataForm.value.appraiseUserId) {
    return ElMessage.warning("请选择客服");
  }
  serviceImUid.value = platformCustomerServiceList.value.find((item) => item.id == dataForm.value.appraiseUserId).imUid;

  let params = {
    // 群主id 当前选择的客服id
    waiterId: dataForm.value.appraiseUserId,
    imUids: [serviceImUid.value, sellersImUid.value, imStore.imUid],
    // 群类型 1交易群，2中介群3普通群,4回收群
    type: 4,
    orderId: orderId
  };
  btnLoading.value = true;
  baseService
    .post("/im/login/group/create", params)
    .then((res) => {
      if (res.code == 0) {
        ElMessage.success("创建成功！");
        dialogVisible.value = false;
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const bpSingleList = ref(<any>[]);
const bpMulList = ref(<any>[]);
const bp1 = ref("");
const bp2 = ref([]);

const getPompensationList = () => {
  baseService.get("/shop/guarantee/page").then((res) => {
    // 包赔
    bpSingleList.value = res.data.list.filter((item: any) => item.type == "1");
    // 增值
    bpMulList.value = res.data.list.filter((item: any) => item.type == "2");
  });
};

defineExpose({
  init
});
</script>

<style lang="scss">
.create-order-dialog-content {
  padding: 16px;
}

.automatic-reply-footer {
  padding: 0 16px 16px 0;
}
</style>
