<!-- 设置商品信息字段 -->
<template>
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-width="100px">
        <el-card class="card-item" v-for="(item, index) in dataForm.list" :key="index">
            <template #header>
                <div class="card-header flx-justify-between">
                    <b class="flx-1 sle">字段{{ index + 1 }}</b>
                    <el-button type="danger" text bg :icon="Delete" @click="deleteHandle(index)" />
                </div>
            </template>
            
            <el-form-item label="名称" :prop="'list[' + index + '].keyName'" :rules="rules.keyName">
                <el-input v-model="item.keyName" placeholder="请输入名称"></el-input>
            </el-form-item>
            
            <div class="flx">
                <el-form-item label="类型" :prop="'list[' + index + '].keyType'" :rules="rules.keyType">
                    <ny-radio v-model="item.keyType" :datas="radioData"  @change="formTypeChange(index)"></ny-radio>
                </el-form-item>
    
                <el-form-item label="必填项">
                    <el-switch v-model="item.required"></el-switch>
                </el-form-item>
            </div>

            <el-form-item label="字段类型">
                <el-select v-model="item.keySubType" clearable placeholder="请选择字段类型">
                    <el-option v-for="(item, index) in fieldTypeData" :key="index" :value="item.value" :label="item.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="填写类型" v-if="item.keyType == '2'" >
                <el-select v-model="item.textType" clearable placeholder="请选择填写类型">
                    <el-option v-for="(item, index) in fillingTypeData" :key="index" :value="item.value" :label="item.label" />
                </el-select>
            </el-form-item>

            <template v-if="item.keyType == '0' || item.keyType == '1'">
                <el-form-item 
                    v-for="(item2, index2) in item.fieldValue" 
                    :key="index2 + '_formSon'" 
                    class="dynamicFormSon w-100"
                    :label="index2 == 0 ? '选项' : ''" 
                    :prop="`list[${index}].fieldValue[${index2}]`"
                    :rules="rules.detailsNames"
                >
                    <div class="flx-center">
                        <el-input v-model="item.fieldValue[index2]" placeholder="请输入" class="flx-1" />
                        <el-button circle type="danger" size="small" :icon="Minus" v-if="item.fieldValue.length > 1" @click="removeSon(item, item2)" class="dels ml-20"></el-button>
                        <el-button circle type="primary" size="small" :icon="Plus" v-if="index2 == item.fieldValue.length - 1" @click="addSon(item)" class="adds ml-20"></el-button>
                    </div>
                </el-form-item>
            </template>

            <el-form-item label="排序" :prop="`list[${index}].keySort`" :rules="rules.keySort">
                <el-input v-model="item.keySort" placeholder="请输入" clearable type="number" />
            </el-form-item>
                
        </el-card>
    </el-form>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineProps, defineEmits } from "vue";
import { Delete, Plus, Minus } from "@element-plus/icons-vue";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
import { useHandleData } from '@/hooks/useHandleData';

const emit = defineEmits(["setInfoSave"])

const dataForm = ref({
    list: [{
        fieldValue: [''],
        required: false
    }]
    
} as IObject);

const rules = ref({
    keyName: [
        { required: true, message: "请输入名称", trigger: "blur" }
    ],
    keyType: [
        { required: true, message: "请选择类型", trigger: "change" }
    ],
    detailsNames: [
        { required: true, message: "请输入选项", trigger: "blur" }
    ],
    keySort: [
        { required: true, message: "请输入排序", trigger: "blur" }
    ]
})

// 表单类型
const radioData = [
    { label: "文本", value: "2" }, 
    { label: "单选", value: "0" }, 
    { label: "多选", value: "1" }
]

// 字段类型
const fieldTypeData = [
    { label: "营地号", value: 4 },
    { label: "实名情况", value: 5 },
    { label: "包赔", value: 6 },
    { label: "是否顶级账号", value: 7 }
]

// 填写类型
const fillingTypeData = [
    { label: "字符", value: 0 },
    { label: "数字", value: 1 }
]

// 选择表单类型
const formTypeChange = (index: number) => {
    dataForm.value.list[index] = {
        ...dataForm.value.list[index],
        required: false,
        keySubType: "",
        fieldValue: (dataForm.value.list[index].keyType == '0'|| dataForm.value.list[index].keyType == '1') ? [''] :  []
    }
}

// 初始化数据
const init = (data: any) => {
    if(data && data.length){
        dataForm.value.list = data;
    }
}

// 新增
const addData = () => {
    dataForm.value.list.push({
        fieldValue: ['']
    })
}

// 删除
const deleteHandle = async (index: number) => {
    let id = dataForm.value.list[index].fieldId
    if(!id) return dataForm.value.list.splice(index, 1);

    await useHandleData('/game/sysgame/deleteCustomField', [id], '是否确认删除');
    dataForm.value.list.splice(index, 1);
}

// 选项字段
const addSon = (item: any) => {
  var index = dataForm.value.list.indexOf(item)
  if (index !== -1) {
    dataForm.value.list[index].fieldValue.push('');
  }
}
const removeSon = (item: any, item2: any) => {
  var index = dataForm.value.list.indexOf(item)
  if (index !== -1) {
    var index2 = dataForm.value.list[index].fieldValue.indexOf(item2)
    if (index2 !== -1) {
        dataForm.value.list[index].fieldValue.splice(index2, 1)
    }
  }
}

// 验证表单
const dataFormRef = ref();
const validateForm = () => {
    if (dataForm.value.list.length == 0) {
        ElMessage({
            type: 'error',
            message: '请至少添加一条数据！',
        })
    }
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }

        emit('setInfoSave', dataForm.value.list);
   })
}

defineExpose({
    init,
    addData,
    validateForm
})
</script>