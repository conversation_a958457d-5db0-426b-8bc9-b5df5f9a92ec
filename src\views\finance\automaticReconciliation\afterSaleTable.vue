<template>
  <div class="container sell-order-wrap TableXScrollSty">
    <ny-table
      :showColSetting="false"
      noDataType="1"
      cellHeight="ch-96"
      class="nyTableSearchFormFitable"
      :state="state"
      :columns="props.pParams.billType == '1' ? columns : columns2"
      @pageSizeChange="state.pageSizeChangeHandle"
      @pageCurrentChange="state.pageCurrentChangeHandle"
      @selectionChange="state.dataListSelectionChangeHandle"
      @sortableChange="sortableChange"
      :key="tabPageKey"
    >
      <template #header> <el-button type="primary" @click="exportHandle">导出</el-button> </template>
      <!-- 商品标题 -->
      <template #shopTitle="{ row }">
        <div class="shoping">
          <el-image style="height: 68px; width: 120px" :src="row.shopLog" :preview-src-list="[row.shopLog]" preview-teleported fit="cover" />
          <div class="info">
            <div class="title mle" v-html="row.shopTitle" @click="jumpGoodsDetails(row)"></div>
            <div class="sle" style="width: 185px;text-align: left;">
              {{ `${row.gameName || '-'} / ${row.areaName || '-'}` }}
            </div>
          </div>
        </div>
      </template>

      <!-- 状态 -->
      <template #reconciliationStatus="{ row }">
        <el-tag v-if="row.reconciliationStatus == '1'" type="success">对账成功</el-tag>
        <el-tag v-if="row.reconciliationStatus == '2'" type="danger">异常订单</el-tag>
      </template>
      <!-- 实收金额 -->
      <template #receivedCompensation="{ row }">
        <el-text type="danger" text v-if="+row.receivedCompensation == 0">{{ row.receivedCompensation }}</el-text>
        <span v-else>{{ row.receivedCompensation }}</span>
      </template>
      <template #operation="{ row }">
        <el-button link type="primary" v-if="row.reconciliationStatus == '2'" @click="handleError(row)">处理</el-button>
        <span v-else>-</span>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort">
            <NyDropdownMenu
              :isBorder="false"
              v-model="SummariesParams"
              :list="[
                { label: '实收赔付', value: 1 },
                { label: '应收赔付', value: 2 }
              ]"
              labelKey="label"
              valueKey="value"
              isTop
            ></NyDropdownMenu>
          </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
    <!-- 处理异常 -->
    <errorOrder ref="errorOrderRef" :key="errorOrderKey" @refresh="state.getDataList()" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import useView from "@/hooks/useView";
import { fileExport } from "@/utils/utils";
import baseService from "@/service/baseService";
import ShopInfo from "@/views/shop/shop-info.vue";
import errorOrder from "./errorOrder.vue";

// 收入
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNo",
    label: "订单编号",
    minWidth: 180
  },
  {
    prop: "orderType",
    label: "订单类型",
    width: 120
  },
  {
    prop: "shopTitle",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "receivableAmount",
    label: "应收赔付(元)",
    minWidth: 180
  },
  {
    prop: "receivedAmount",
    label: "实收赔付(元)",
    width: 180
  },
  {
    prop: "transactionPrice",
    label: "成交价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "submitter",
    label: "提交人",
    width: 120
  },
  {
    prop: "createDate",
    label: "对账时间",
    width: 160
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    fixed: "right",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 200
  }
]);
// 支出
const columns2 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNo",
    label: "订单编号",
    minWidth: 180
  },
  {
    prop: "orderType",
    label: "订单类型",
    width: 120
  },
  {
    prop: "shopTitle",
    label: "商品信息",
    minWidth: "340"
  },
  {
    prop: "payableAmount",
    label: "应付赔付(元)",
    minWidth: 180
  },
  {
    prop: "paidAmount",
    label: "实付赔付(元)",
    width: 180
  },
  {
    prop: "transactionPrice",
    label: "成交价(元)",
    width: 136,
    sortable: "custom"
  },
  {
    prop: "submitter",
    label: "提交人",
    width: 120
  },
  {
    prop: "createDate",
    label: "对账时间",
    width: 160
  },
  {
    prop: "reconciliationStatus",
    label: "对账状态",
    fixed: "right",
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 200
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/autoreconciliation/page",
  getDataListIsPage: true,
  exportURL: "/automaticReconciliation/autoreconciliation/export",
  dataForm: {
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const props = defineProps({
  pParams: {}
});
const tabPageKey = ref(0);
watch(
  () => props.pParams?.billType || props.pParams?.type,
  () => {
    state.dataForm = Object.assign(state.dataForm, props.pParams);
    tabPageKey.value++;
  },
  {
    immediate: true,
    deep: true
  }
);
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleUpDateData = (dataForm:any) => {
  state.dataForm = Object.assign(state.dataForm, dataForm);
  state.getDataList();
};
// 异常处理
const errorOrderRef = ref();
const errorOrderKey = ref(0);
const handleError = (row: any) => {
  errorOrderKey.value++;
  nextTick(() => {
    errorOrderRef.value.init({ ...row },props.pParams.billType);
  });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/autoreconciliation/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `售后订单`);
  });
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  if (SummariesParams.value == 2) {
    state.dataList.map((item: any) => {
      if (item.receivableCompensation) if (item.receivableCompensation) total = total + (item.receivableCompensation || 0);
    });
  } else if (SummariesParams.value == 1) {
    state.dataList.map((item: any) => {
      if (item.receivedCompensation) if (item.receivedCompensation) total = total + (item.receivedCompensation || 0);
    });
  }
  return total.toFixed(2);
};
defineExpose({
  handleUpDateData
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
