<template>
  <div class="container sell-order-wrap TableXScrollSty">
    <ny-table
      :showColSetting="false"
      noDataType="1"
      cellHeight="ch-96"
      class="nyTableSearchFormFitable"
      :state="state"
      :columns="columns"
      @pageSizeChange="state.pageSizeChangeHandle"
      @pageCurrentChange="state.pageCurrentChangeHandle"
      @selectionChange="state.dataListSelectionChangeHandle"
      @sortableChange="sortableChange"
    >
      <template #header>
        <el-button v-if="state.hasPermission('game:attribute:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button type="info" @click="exportHandle">导出</el-button>
      </template>

      <!-- 是否需要发票 -->
      <template #whetherHasInvoice="{ row }">
        {{ row.whetherHasInvoice ? '是' : '否' }}
      </template>

      <!-- 关联商品 -->
      <template #shopCode="{ row }">
        <el-text type="primary" text class="pointer" @click="jumpGoodsDetails(row)" v-if="row.shopCode">{{ row.shopCode }}</el-text>
        <span v-else>-</span>
      </template>

      <!-- 状态 -->
      <template #auditStatus="{ row }">
        <el-tag v-if="row.auditStatus == '0'" type="warning">待审核</el-tag>
        <el-tag v-if="row.auditStatus == '1'" type="success">已通过</el-tag>
        <el-tag v-if="row.auditStatus == '2'" type="danger">已拒绝</el-tag>
      </template>

      <!-- 收入凭证 -->
      <template #voucher="{ row }">
        <span v-if="!row.voucher || row.voucher.length < 1">-</span>
        <div style="display: flex; flex-wrap: wrap; gap: 10px" v-else>
          <el-image style="width: 64px; height: 64px" :src="item" v-for="item in row.voucher.split(',')" alt=""  preview-teleported :preview-src-list="row.voucher.split(',')" />
        </div>
      </template>

      <!-- 收款信息 -->
      <template #collectionAccount="{ row }">
        <div v-if="row.collectionAccount" style="display: flex; align-items: center; justify-content: center; gap: 8px">
          <span>{{ row.collectionAccount }}</span>
          <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M21.9224 15.3576C18.0929 14.2048 15.8667 13.5176 15.244 13.296C15.812 12.32 16.268 11.2 16.564 9.976H13.3V8.872H17.3V8.192H13.3V6.344H11.764C11.484 6.344 11.452 6.592 11.452 6.592V8.184H7.7V8.864H11.452V9.968H8.38V10.584H14.604C14.38 11.36 14.076 12.096 13.716 12.76C12.308 12.296 11.524 11.976 9.804 11.816C6.548 11.504 5.796 13.296 5.676 14.392C5.5 16.064 6.98 17.424 9.188 17.424C11.396 17.424 12.868 16.4 14.268 14.704C15.4346 15.2619 17.6059 16.2293 20.7819 17.6062C18.9835 20.2577 15.9452 22 12.5 22C6.97715 22 2.5 17.5228 2.5 12C2.5 6.47715 6.97715 2 12.5 2C18.0228 2 22.5 6.47715 22.5 12C22.5 13.1778 22.2964 14.3081 21.9224 15.3576ZM8.932 16.368C6.596 16.368 6.228 14.888 6.348 14.272C6.468 13.656 7.148 12.856 8.452 12.856C9.948 12.856 11.284 13.24 12.892 14.016C11.756 15.496 10.372 16.368 8.932 16.368Z"
              fill="#165DFF"
            />
          </svg>
        </div>
      </template>

      <template #operation="{ row }">
        <el-button link type="primary" v-if="row.auditStatus == '0'" @click="addOrUpdateHandle(row)">编辑</el-button>
        <el-button link type="primary" @click="addOrUpdateHandle({ ...row, isCheck: true })">详情</el-button>
        <el-button link type="danger" v-if="row.auditStatus == '2'" @click="state.deleteHandle(row.id)">删除</el-button>
      </template>

      <template #footer>
        <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
          <span class="tableSort"> 收入金额 </span>
          <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
          <span>合计={{ getSummaries() }}</span>
        </div>
      </template>
    </ny-table>
    <!-- 商品详情 -->
    <shop-info ref="shopInfoRef" :key="shopInfoKey"></shop-info>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refresh="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { fileExport } from "@/utils/utils";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import ShopInfo from "@/views/shop/shop-info.vue";
import addOrUpdate from "./otherInTable-add-or-update.vue";
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "name",
    label: "费用名称",
    minWidth: 160
  },
  {
    prop: "amount",
    label: "收入金额(元)",
    minWidth: 160
  },
  {
    prop: "depName",
    label: "申请部门",
    minWidth: 160
  },
  {
    prop: "submitter",
    label: "提交人",
    minWidth: 160
  },
  {
    prop: "submitTime",
    label: "提交时间",
    minWidth: 160
  },
  {
    prop: "voucher",
    label: "收入凭证",
    minWidth: 160
  },
  {
    prop: "whetherHasInvoice",
    label: "是否需要发票",
    minWidth: 160
  },
  {
    prop: "shopCode",
    label: "关联商品",
    width: 160
  },
  {
    prop: "accountNumber",
    label: "收款账号",
    minWidth: 160
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "remark",
    label: "备注",
    width: 200
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 160
  }
]);

const view = reactive({
  getDataListURL: "/automaticReconciliation/othercost/page",
  getDataListIsPage: true,
  deleteURL: "/automaticReconciliation/othercost",
  deleteIsBatch: true,
  dataForm: {
    order: "",
    orderField: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (row?: any) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(view.dataForm.billType,{ ...row });
  });
};
const props = defineProps({
  pParams: {}
});
watch(
  () => props.pParams,
  () => {
    state.dataForm = Object.assign(state.dataForm, props.pParams);
  },
  {
    immediate: true,
    deep: true
  }
);
// 排序
const sortableChange = ({ order, prop }: any) => {
  console.log(order, prop);
  state.dataForm.order = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  state.dataForm.orderField = prop;
  state.getDataList();
};
const handleUpDateData = () => {
  state.getDataList();
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/automaticReconciliation/othercost/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `其他收入`);
  });
};

// 商品详情
const shopInfoRef = ref();
const shopInfoKey = ref(0);
const jumpGoodsDetails = async (row: any) => {
  let res = await baseService.get("/shop/shop/" + row.shopId);
  shopInfoKey.value++;
  await nextTick();
  shopInfoRef.value.init(res?.data || {});
};

// 合计行计算函数
const SummariesParams = ref(1);
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item.amount) if (item.amount) total = total + (item.amount || 0);
  });

  return total.toFixed(2);
};
defineExpose({
  handleUpDateData
});
</script>

<style lang="scss" scoped>
.bargain-wrap {
  .input-280 {
    width: 280px;
  }
  :deep(.input-240) {
    width: 240px;
  }
}
.contract-icon {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.shoping {
  display: flex;
  align-items: center;
  cursor: pointer;
  .el-image {
    border-radius: 4px;
    margin-right: 8px;
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .title {
      color: var(--el-color-primary);
      white-space: pre-wrap;
      text-align: left;
    }
  }
}
</style>
