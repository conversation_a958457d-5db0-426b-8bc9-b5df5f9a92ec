<template>
    <div class="mod-demo__sysnotice">
        <el-card shadow="never" class="rr-view-ctx-card">
            <ny-table 
                :state="state" 
                :columns="columns"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                    <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
                </template>

                <template #header-right>
                    <ny-select v-model="state.dataForm.noticeType" dict-type="notice_type" style="width: 200px;" :placeholder="$t('notice.type')"></ny-select>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <!-- 类型 -->
                <template #noticeType="{row}">
                    {{ state.getDictLabel("notice_type", row.noticeType) }}
                </template>
                
                <!-- 状态 -->
                <template #status="{row}">
                    <el-tag v-if="row.status === 0" type="danger">{{ $t("notice.status0") }}</el-tag>
                    <el-tag v-else type="success">{{ $t("notice.status1") }}</el-tag>
                </template>

                <!-- 操作 -->
                <template #operation="{row}">
                    <el-button v-if="row.status === 0" type="warning" text bg  @click="addOrUpdateHandle(row.id)">{{ $t("update") }}</el-button>
                    <el-button v-if="row.status === 1" type="primary" text bg @click="viewHandle(row)">{{ $t("notice.view") }}</el-button>
                    <el-button type="primary" text bg @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
                </template>

            </ny-table>
        </el-card>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./notice-add-or-update.vue";
import { IObject } from "@/types/interface";
import { registerDynamicToRouterAndNext } from "@/router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const view = reactive({
    getDataListURL: "/sys/notice/page",
    getDataListIsPage: true,
    deleteURL: "/sys/notice",
    deleteIsBatch: true,
    dataForm: {
        noticeType: ""
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "title",
        label: "标题",
        minWidth: 150,
    },
    {
        prop: "noticeType",
        label: "类型",
        minWidth: 100
    },
    {
        prop: "senderName",
        label: "发送者",
        minWidth: 100
    },
    {
        prop: "senderDate",
        label: "发送时间",
        minWidth: 150
    },
    {
        prop: "status",
        label: "状态",
        minWidth: 100
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.noticeType = "";
    state.getDataList();
}

const viewHandle = (row: IObject) => {
    // 路由参数
    const routeParams = {
        path: "/notice/notice-view",
        query: {
            id: row.id,
            _mt: t("notice.view1")
        }
    };
    // 动态路由
    registerDynamicToRouterAndNext(routeParams);
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};
</script>
