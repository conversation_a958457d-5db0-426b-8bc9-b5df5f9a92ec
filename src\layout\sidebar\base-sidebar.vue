<script lang="ts">
import { themeSetting } from "@/constants/config";
import { EMitt, EThemeSetting } from "@/constants/enum";
import { IObject } from "@/types/interface";
import Layout from "@/layout/layout.vue";
import emits from "@/utils/emits";
import { toValidRoutes } from "@/utils/router";
import { getThemeConfigCacheByKey } from "@/utils/theme";
import { useWindowSize } from "@vueuse/core";
import { defineComponent, nextTick, onMounted, reactive, ref, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import { RouteRecordRaw, useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/store";
import SidebarMenusItems from "./sidebar-menus-items.vue";
import { getValueByKeys } from "@/utils/utils";
import logo from "../header/logo.vue";
import defaultMenu from "./defaultMenu.vue";
import "./tenantMenu.less";
import app from "@/constants/app";
import classNames from "classnames";
import baseService from "@/service/baseService";
import { getToken } from "@/utils/cache";

/**
 * 侧边栏导航菜单
 */
export default defineComponent({
  name: "BaseSidebar",
  components: { SidebarMenusItems, logo, defaultMenu },
  props: {
    mode: { type: String, default: "vertical" },
    menus: Array,
    currRoute: String,
    router: Boolean,
    onSelect: Function,
    isMobile: Boolean
  },
  setup(props) {
    const route = useRoute();
    const router = useRouter();
    const win = useWindowSize();
    const store = useAppStore();
    const { t } = useI18n();
    const defaultMenus = toValidRoutes((props.menus ?? store.state.routes) as RouteRecordRaw[]);
    const getPopClassName = () => {
      const sidebarCache = getThemeConfigCacheByKey(EThemeSetting.Sidebar);
      return `rr-sidebar-menu-pop-${props.mode === "vertical" && sidebarCache === "dark" ? "dark" : "light"}`;
    };
    const state = reactive({
      collapseSidebar: getThemeConfigCacheByKey(EThemeSetting.SidebarCollapse),
      uniqueOpened: themeSetting.sidebarUniOpened,
      windowWidth: win.width || 800,
      hiddenIndex: -1,
      rawMenus: defaultMenus,
      menus: defaultMenus,
      popClassName: getPopClassName(),
      currRoute: props.currRoute ?? route.path,
      defaultOpenedsList: <any>[],
      menusDedault: <any>[{ meta: { title: "测试" }, path: "/order/sell/index" }]
    });
    const elm = ref({} as IObject);
    const li = ref({
      widths: [] as number[]
    });
    const initComputeSidebarLayout = (width: number) => {
      if (props.mode === "horizontal") {
        //存储水平布局元素信息
        const el = elm.value.$el;
        const lis = el.querySelectorAll("li");
        li.value.widths = [];
        lis.forEach((x: Element) => {
          li.value.widths.push(x.getBoundingClientRect().width);
        });
        computeSidebarLayout(width);
      }
    };

    //
    onMounted(() => {
      if(getToken()){
        getDefaultMenuData();
      }
      // initComputeSidebarLayout(state.windowWidth);
      // const list = state.menus.map((item:any) => item.path);
    });

    // 默认展开全部菜单
    const openeds = computed(() => {
      const set = new Set<string>();
      state.menus.map((menuItem: any) => {
        set.add(menuItem.path);
      });
      set.add("index-1");
      return [...set];
    });

    const getDefaultMenuData = () => {
      baseService.get("/sys/commonFunctions/nav").then((res) => {
        state.menusDedault = toValidMenuData(res.data || []);
      });
    };
    const toValidMenuData = (cMenu: any) => {
      let menuList = <any>[];
      cMenu.forEach((cItem: any) => {
        // 判断是否有权限
        state.menus.forEach((ele: any) => {
          if (ele.children && ele.children.length > 0 && ele.children.some((e: any) => e.meta?.id == cItem?.id)) {
            menuList.push(cItem);
          }
        });
      });
      return menuList;
    };

    watch(
      () => props.menus,
      (vl) => {
        const ms = toValidRoutes((vl ? vl : store.state.routes) as RouteRecordRaw[]);
        state.menus = ms;
        state.rawMenus = ms;
      }
    );
    watch(
      () => store.state.routes,
      (vl) => {
        const ms = toValidRoutes(vl as RouteRecordRaw[]);
        state.rawMenus = ms;
        state.menus = ms;
      }
    );

    emits.on(EMitt.OnSwitchLeftSidebar, () => {
      state.collapseSidebar = !state.collapseSidebar;
    });
    emits.on(EMitt.OnSetThemeNotUniqueOpened, (vl) => {
      state.uniqueOpened = vl;
    });
    emits.on(EMitt.OnSetTheme, ([vl]) => {
      if (vl === EThemeSetting.Sidebar) {
        state.popClassName = getPopClassName();
      }
    });
    watch(
      () => route.path,
      (vl) => {
        const matchedRoute = getValueByKeys(getValueByKeys(router.currentRoute.value.meta, "matched", [])[0], "path", "");
        if (!route.query.pop && matchedRoute) {
          setTimeout(() => {
            state.currRoute = vl;
          }, 10);
        }
      }
    );
    watch(
      () => state.windowWidth,
      (vl) => {
        computeSidebarLayout(vl);
      }
    );

    const computeSidebarLayout = (windowWidth: number) => {
      // if (props.mode === "horizontal" && windowWidth > 768 && elm.value.$el) {
      //   //菜单水平方向菜单过长，采用折叠效果
      //   const width = elm.value.$el.parentNode.getBoundingClientRect().width;
      //   let liWidth = 0;
      //   let index = -1;
      //   for (let i = 0; i < li.value.widths.length; i++) {
      //     liWidth += li.value.widths[i];
      //     if (liWidth > width) {
      //       index = i - 1;
      //       break;
      //     }
      //   }
      //   state.hiddenIndex = index;
      //   state.menus =
      //     index > -1
      //       ? state.rawMenus.slice(0, index).concat({
      //           path: "/__more",
      //           component: Layout,
      //           meta: { title: t("ui.router.moreMenus"), icon: false, isMore: true },
      //           children: state.rawMenus.slice(index)
      //         })
      //       : state.rawMenus;
      // }
    };
    const url = app.api;
    const showScanUrls = ["http://www.mimaker.com/children", "http://192.168.110.11/children", "http://demo2.nyyyds.com:18080/children"];
    const isdev = import.meta.env.DEV;
    const getStyle = (index: number): string => {
      const styles: Array<any> = [];
      const isHidden = state.hiddenIndex ? state.hiddenIndex > -1 && index > state.hiddenIndex : false;
      styles.push("display:" + (isHidden ? "none" : "block"));
      return styles.join(";");
    };
    const defaultMenuRef = ref();
    const handleDefault = () => {
      nextTick(() => {
        defaultMenuRef.value.init(state.menusDedault, state.menus);
      });
    };
    return { elm, props, state, t, store, openeds, showScanUrls, url, isdev, getStyle, classNames, handleDefault, defaultMenuRef, getDefaultMenuData };
  }
});
</script>

<template>
  <!-- <div class="rr-header-ctx-logo hidden-xs-only">
    <logo logoUrl="https://www.nyyyds.com/pic/img/202407/202407251c64b9.png" :logoName="t('ui.login.systemName')"></logo>
  </div>  -->
  <!-- ref="elm"
  :default-active="props.currRoute ?? state.currRoute"
  :mode="props.mode"
  :collapse="props.isMobile ? false : props.mode === 'vertical' && state.collapseSidebar"
  :router="props.router"
  :unique-opened="state.uniqueOpened"
  :onSelect="props.onSelect"
  :collapse-transition="false"
  @open="handleOpen"
  @close="handleClose"
  class="rr-sidebar-menu" -->
  <div class="base_sidebar_list">
    <!-- 主户菜单 -->
    <el-menu
      ref="elm"
      :default-active="props.currRoute ?? state.currRoute"
      :mode="props.mode"
      :collapse="props.isMobile ? false : props.mode === 'vertical' && state.collapseSidebar"
      :router="props.router"
      :default-openeds="openeds"
      :onSelect="props.onSelect"
      class="rr-sidebar-menu"
      v-if="store.state.isPlatform"
    >
      <el-sub-menu :popper-class="state.popClassName" :index="'index-1'">
        <template #title>
          <div @click.stop="" style="display: flex; align-items: center; flex: 1">
            <el-icon><Reading /></el-icon>
            <span>
              <a>常用功能</a>
            </span>
          </div>
          <div @click.stop="handleDefault" style="background-color: #fff; position: absolute; right: 14px; z-index: 99; font-size: 12px; color: #909399"><a>管理</a></div>
        </template>
        <template v-for="(x, index) in state.menusDedault || []" :key="x.url">
          <el-menu-item v-if="x.url != 'utilityTools/autoScan/index' || showScanUrls.includes(url) || isdev" :index="x.url && x?.url.startsWith('/') ? x.url : '/' + x.url" :style="getStyle(index)">
            <template #title>
              <a>{{ x.name }}</a>
            </template>
          </el-menu-item>
        </template>
      </el-sub-menu>
      <sidebar-menus-items :className="state.popClassName" :menus="state.menus" :hiddenIndex="state.hiddenIndex"></sidebar-menus-items>
    </el-menu>

    <!-- 租户菜单 -->
    <el-menu ref="elm" :default-active="props.currRoute ?? state.currRoute" :mode="props.mode" :collapse="props.isMobile ? false : props.mode === 'vertical' && state.collapseSidebar" :router="props.router" :default-openeds="openeds" :onSelect="props.onSelect" class="tenant-menu" v-else>
      <sidebar-menus-items :className="state.popClassName" :menus="state.menus" :hiddenIndex="state.hiddenIndex"></sidebar-menus-items>
    </el-menu>

    <defaultMenu ref="defaultMenuRef" @refresh-data-list="getDefaultMenuData" />
  </div>
</template>
<style lang="less" scoped>
.base_sidebar_list {
  height: 100%;
  overflow: auto;
  scrollbar-width: none;
  background-color: #fff;
  &::-webkit-scrollbar {
    display: none;
  }
  border-radius: 8px;
}
</style>
