<template>
    <!-- 盼之推送验证码 -->
    <el-dialog v-model="pzdsShow" title="同步上架短信验证码" width="480" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="1">
            <el-descriptions-item label="游戏名称">{{ pzdsForm.gameName }}</el-descriptions-item>
            <el-descriptions-item label="商品编码">{{ pzdsForm.shopCode }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ pzdsForm.phone }}</el-descriptions-item>
            <el-descriptions-item>
                <template #label><span>验证码<span style="color: red">*</span></span></template>
                <el-input type="number" v-model="pzdsForm.code" placeholder="验证码" style="width: 100%;" clearable ></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <div style="display: flex;align-items: center;gap: 4px;margin-top: 4px;margin-bottom: 28px;">
            <el-icon color="var(--color-primary)"><InfoFilled /></el-icon>
            <el-text type="primary">请在24小时内完成验证，否则推送任务将失效！</el-text>
        </div>
        <template #footer>
            <el-button style="color: var(--color-primary);border: 1px solid var(--color-primary);" :disabled="isCounting" @click="handleSendSms">{{ buttonText }}</el-button>
            <el-button type="primary" v-loading="dialogSubmitLoading" @click="pzdsSubmit">立即验证</el-button>
        </template>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onUnmounted } from 'vue';
import baseService from '@/service/baseService';
import { ElMessage } from 'element-plus';

const pzdsShow = ref(false);
const pzdsForm = ref();
const dialogSubmitLoading = ref(false);
// 倒计时时间
const remainingTime = ref(0);
// 是否正在倒计时
const isCounting = ref(false);
// 是否正在发送请求
const isSending = ref(false);
// 定时器引用
const timer = ref<number | null>(null);

// 按钮文字计算属性
const buttonText = computed(() => {
  return isCounting.value 
    ? `剩余${remainingTime.value}秒`
    : remainingTime.value === 0 
      ? '重新获取' 
      : '重新发送';
});

// 开始倒计时
const startCountdown = () => {
  isCounting.value = true;
  remainingTime.value = 60;
  timer.value = window.setInterval(() => {
    remainingTime.value -= 1;
    if (remainingTime.value <= 0) {
      stopCountdown();
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
  isCounting.value = false;
  remainingTime.value = 0;
};

// 处理发送验证码
const handleSendSms = async () => {
  if (isCounting.value || isSending.value) return;

  isSending.value = true;
  try {
    // 这里替换成真实的短信接口调用
    await mockApiCall();
    ElMessage.success('操作成功');
    startCountdown();
  } catch (error) {
    console.error('短信发送失败:', error);
  } finally {
    isSending.value = false;
    stopCountdown();
  }
};


// 发送验证码
const mockApiCall = () => {
  baseService.post("/script/sysscriptpushtaskdetails/sendPhoneCode",pzdsForm.value).then(res=>{
    if(res.code == 0){
        pzdsShow.value = true;
        stopCountdown();
    }
  }).finally(()=>{
    stopCountdown();
  })
};

const pzdsSubmit = () =>{
    if(!pzdsForm.value.code){
        ElMessage.warning('请输入验证码');
        return
    }
    dialogSubmitLoading.value = true;
    baseService.post("/script/sysscriptpushtaskdetails/submitPhoneCode",pzdsForm.value).then(res=>{
        if(res.code == 0){
            ElMessage.success('提交成功');
        }
    }).finally(()=>{
        dialogSubmitLoading.value = false;
        pzdsShow.value = false;
    })
}

// 组件卸载时清除定时器
onUnmounted(() => {
  stopCountdown();
});

const init = (data?: any) => {
  pzdsShow.value = true;
  pzdsForm.value = data
  console.log(data,'==== sdjflsdjf ');
  
};

defineExpose({
  init
});
</script>

<style lang='less' scoped>

</style>