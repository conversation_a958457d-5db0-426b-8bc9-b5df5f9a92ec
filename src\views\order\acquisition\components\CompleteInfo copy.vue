<template>
    <el-drawer
        v-model="visible"
        title="完善信息"
        :close-on-click-moformuladal="false"
        :close-on-press-escape="false"
        size="50%"
        class="ny-drawer acquisition-complete-info"
    >   
        <template #header="{ close, titleId, titleClass }">
            <div class="el-drawer__title">完善信息</div>
        </template>
        <div class="flx-column">
            <el-scrollbar class="contract-real-name flx-1" v-loading="dataLoading">
                <el-form label-position="top" :model="dataForm" :rules="rules" ref="formRef">

                        <el-card>
                            <div class="p-title mt-0">账号信息</div>
                            <el-row :gutter="12">
                                <el-col :span="12">
                                <!-- 大区  卖方手机号  包赔费 -->
                                    <el-form-item label="区服" prop="district">   
                                        <el-col :span="12">
                                            <el-select v-model="dataForm.district" placeholder="选择大区" @change="dataForm.server = '',getServerList()">
                                                <el-option v-for="item in districtList" :key="item.id" :label="item.title" :value="item.id"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="12" v-if="serverList && serverList.length">
                                            <el-select v-model="dataForm.server" placeholder="选择服务器">
                                                <el-option v-for="item in serverList" :key="item.id" :label="item.title" :value="item.id"></el-option>
                                            </el-select>
                                        </el-col>
                                    </el-form-item>
                                </el-col>
                               
                                <el-col :span="12"></el-col>
                                <el-col :span="12">
                                    <el-form-item label="游戏账号" prop="gameAccount">
                                        <el-input v-model="dataForm.gameAccount" placeholder="请输入游戏账号"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="游戏密码" prop="gamePassword">
                                        <el-input v-model="dataForm.gamePassword" type="password" show-password placeholder="请输入游戏密码"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="买方手机号" prop="buyerPhone">
                                        <el-input v-model="dataForm.buyerPhone" placeholder="请输入买方手机号"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="卖方手机号" prop="sellerPhone">
                                        <el-input v-model="dataForm.sellerPhone" placeholder="请输入卖方手机号"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="回收价(元)" prop="acquisitionPrice">
                                        <el-input v-model="dataForm.acquisitionPrice" placeholder="请输入回收价"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="包赔费(元)">
                                        <el-input v-model="dataForm.guaranteeAmount" placeholder="请输入包赔费"></el-input>
                                    </el-form-item>
                                </el-col>

                            </el-row>
                        </el-card>

                        <!-- 商品描述 -->
                        <el-card class="mt-12" v-if="currentType == 1">
                            <div class="p-title">商品描述</div>
                            <el-form-item label="标题" prop="title">
                                <el-input v-model="dataForm.title" placeholder="请输入商品标题"></el-input>
                            </el-form-item>
                            
                            <el-form-item label="商品描述" prop="info">
                                <el-input v-model="dataForm.info" :rows="12" type="textarea" placeholder="请输入商品描述"></el-input>
                            </el-form-item>

                            <el-form-item>
                                <el-button type="primary" @click="analysisFn">一键解析</el-button>
                            </el-form-item>
                            
                            <div class="pb-20">
                                <el-text type="warning">tip：估价网站文本内容可在此进行解析。勾选底部属性信息后，会自动更新商品“描述信息”以及“标题”内容；</el-text>
                            </div>

                            <el-col :span="24">
                                <el-form-item label="商品主图" prop="log">
                                    <ny-shop-image-upload type="image" v-model="dataForm.log"></ny-shop-image-upload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="商品详情图" prop="images">
                                    <ny-shop-image-upload type="images" v-model="dataForm.images" :limit="100"></ny-shop-image-upload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item prop="highlights">
                                    <template #label>
                                        <div class="flx-justify-between">
                                            <div>商品亮点</div>
                                            <div class="flx-align-center" >
                                                <span>亮点展示示例：</span>
                                                <span class="mytag" style="max-width: 400px;">
                                                    <el-image class="topImg" :src="arrowIcon" />
                                                    {{ dataForm.highlights }}
                                                </span>
                                            </div>
                                        </div>
                                    </template>
                                    <el-input v-model="dataForm.highlights" placeholder="商品亮点"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-card>

                        <!-- 商品属性 -->
                        <el-card class="mt-12">
                            <div class="p-title">商品属性</div>
                            
                            <el-row>
                                <el-col :span="24">
                                    <div v-for="(item,index) in dataForm.attributesList" :key="index">
                                        <el-form-item :label="item.name" v-if="item.type == 1">
                                            <div class="flod_tab">
                                                <div class="flod_tab_cont">
                                                    <el-radio-group v-model="item.attributeIds" size="small" @change="feedBackInfo()">
                                                        <el-radio :value="it.id" border v-for="it in item.children">{{ it.name }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                                <div class="oper" style="width: 60px;">
                                                    <el-text type="primary" v-if="item.attributeIds" @click="item.attributeIds = ''" class="resetFont" >重置</el-text>
                                                </div>
                                            </div>
                                        </el-form-item>
                                        <el-form-item :label="item.name" v-if="item.type == 2">
                                            <div class="flod_tab">
                                                <div class="flod_tab_cont" :style="{ 'height': !item.fold ? 'auto' : '40px' }">
                                                    <el-checkbox-group v-model="item.attributeIds" @change="feedBackInfo()">
                                                        <el-checkbox :label="it.name" :value="it.id" v-for="it in item.children"/>
                                                    </el-checkbox-group>
                                                </div>
                                                <div class="oper">
                                                    <el-text type="primary" v-if="item.attributeIds.length > 0" @click="item.attributeIds = []" class="resetFont" >重置</el-text>
                                                    <el-button v-if="item.isMult" type="primary" plain size="small"
                                                        @click="item.fold = !item.fold">{{ item.fold ? '展开列表' : '收起列表' }}</el-button>
                                                </div>
                                            </div>
                                        </el-form-item>
                                        <el-form-item :label="item.name" v-if="item.type == 3">
                                            <div class="flod_tab">
                                                <el-input v-model="item.attributeText" :placeholder="item.name" style="width: 25%;" clearable @change="feedBackInfo()"></el-input>
                                            </div>
                                        </el-form-item>
                                    </div>
                                </el-col>
                            </el-row>

                        </el-card>


                </el-form>

            </el-scrollbar>
           
        </div>

        <template #footer v-if="currentType!=2">
            <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
        </template>

    </el-drawer>
</template>  

<script lang="ts" setup>
    import { ref, reactive, defineExpose, defineEmits } from "vue";
    import { ElMessage } from "element-plus";
    import { useAppStore } from "@/store";
    import { checkPermission } from "@/utils/utils";
    import baseService from "@/service/baseService";
    import arrowIcon from "@/assets/images/tagicon.png";

    const emits = defineEmits(["refresh"]);
    const store = useAppStore();

    const visible = ref(false);
    const init = (data: any) => {
        visible.value = true;
        dataForm.orderId = data.id;
        dataForm.gameId = data.gameId;
        dataForm.gameAccount = data.gameAccount;
        dataForm.acquisitionPrice = data.amount;
        dataForm.sellerPhone = data.customerPhone;
        dataForm.gamePassword = data.changeInfo ? data.changeInfo.ourPassword : ''
        dataForm.guaranteeAmount = data.guaranteeAmount || data.guaranteePrice;
        dataForm.server = data.serverId;
        // // 基本信息
        // if(data.orderInfo && data.orderInfo.gameAccount){
        //     setBaseInfo(data.orderInfo);
        // }
        


        getDetail();

        getdistrictList();
        getAttribute();
    }

    const currentType = ref(1);

    const dataForm = reactive(<any>{
        // 游戏id
        gameId: "",

        // 商品描述
        info: "",

        // 属性列表
        attributesList: <any>[]
    });

    const hasPermission = (key: string) => {
      return checkPermission(store.state.permissions as string[], key);
    }


    // 获取详情
    const dataLoading = ref(false);
    const getDetail = () => {
        dataLoading.value = true;
        baseService.get("/purchase/getOrderInfo/" + dataForm.orderId).then((res: any) => {
            if(res.code == 0 && res.data){
                let data = res.data;
                setBaseInfo(data);
                if(dataForm.attributesList && dataForm.attributesList.length){
                    analysisFn('getInfo');
                }
            }
        }).finally(() => {
            dataLoading.value = false;
        })
    }

    // 选择服务器校验
    const validator = (rule: any, value: string, callback: any) => {
        if(!dataForm.server && serverList.value.length){
            callback(new Error('请选择服务器'));
        }else{
            callback();
        }
    };
    
    const rules = reactive({    
        district: [
            { required: true, message: '请选择区服', trigger: 'change' },
            { required: true, validator: validator, trigger: 'change'  }
        ],
        gameAccount: [{ required: true, message: '请输入游戏账号', trigger: 'blur' }],
        gamePassword: [{ required: true, message: '请输入游戏密码', trigger: 'blur' }],
        buyerPhone: [
            { required: true, message: '请输入买方手机号', trigger: 'blur' }, 
            { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        sellerPhone: [
            { required: true, message: '请输入卖方手机号', trigger: 'blur' }, 
            { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        acquisitionPrice: [{ required: true, message: '请输入回收价', trigger: 'blur' }],
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        info: [{ required: true, message: '请输入商品描述', trigger: 'blur' }],

        customerBindPhone: [
            { required: true, message: '请输入对方绑定手机号', trigger: 'blur' },
            { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        ourBindPhone: [
            { required: true, message: '请输入我方绑定手机号', trigger: 'blur' },
            { pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        ourPassword: [{ required: true, message: '请输入我方密码', trigger: 'blur' }],
        changePicture: [{ required: true, message: '请上传换绑凭证', trigger: 'change' }],
    })
    


    // 获取游戏属性信息
    const requestLoading = ref(false);
    const getAttribute = () => {
        requestLoading.value = true;
        baseService.get("/game/attribute/page", { gameId: dataForm.gameId }).then((res) => { 
            if (res.data.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '没有查询到当前游戏属性信息！',
                })
            } else {
                dataForm.attributesList = [];
                const sxWidth: number = Number(((document.querySelector('.flod_tab') as HTMLElement).offsetWidth * 0.5).toFixed(2));
                res.data.map((item: any) => {
                    let allFontLen = 0;
                    item.children.forEach((item: any) => {
                        allFontLen += Number((item.name.length) * 14 + 52);
                    })
                    
                    if (Number((sxWidth / allFontLen).toFixed(2)) < 1) { 
                        dataForm.attributesList.push({
                            typeId: item.id,
                            attributeIds: item.type == 2 ? [] : null,
                            attributeText: '',
                            children: item.children,
                            isTitle: item.isTitle,
                            type: item.type,
                            name: item.name,
                            isMult: true,
                            fold: true
                        })
                    } else {
                        dataForm.attributesList.push({
                            typeId: item.id,
                            attributeIds: item.type == 2 ? [] : null,
                            attributeText: '',
                            children: item.children,
                            isTitle: item.isTitle,
                            type: item.type,
                            name: item.name,
                            isMult: false,
                            fold: false
                        })
                    }
                    
                })

                if(dataForm.info) analysisFn('getInfo');
                
            }
        }).finally(() => {
            requestLoading.value = false;
        })
    }

    // 大区列表
    const districtList = ref(<any>[]);
    const getdistrictList = () => {
        baseService.get('/game/sysgame/get/' + dataForm.gameId).then((res: any) => {
            districtList.value = res.data?.areaDtoList;
            if(dataForm.server){
                districtList.value.map(item => {
                    if(item.id == dataForm.server){
                        dataForm.district = item.id;
                    }else {
                        item.children.map(item2 => {
                            if(item2.id == dataForm.server){
                                dataForm.district = item.id;
                            }
                        })
                    }
                })

                if(!dataForm.district) dataForm.district = dataForm.server;

                getServerList();
            }
        })
    }

    // 服务列表
    const serverList = ref(<any>[]);
    const getServerList = () => {
        if(dataForm.district) serverList.value = districtList.value.find((item: any) => item.id == dataForm.district).children
    }

    // 一键解析
    const analysisFn = (type?: string) => {
        if (!dataForm.info) {
            ElMessage.warning('请输入商品简介');
            return
        }
        const strToArr = dataForm.info.split('\n');
        const infoArr = arrayToKeyValuePairs(strToArr);
        if (typeof infoArr == 'string') {
            return
        }
        infoArr.forEach((element:any) => {
            if (element.key.indexOf('服务器') > -1 && element.value) {
                const gameAreItem = districtList.value.filter((item: any) => item.title == element.value)[0]
                if (gameAreItem.children.length > 0) {
                    dataForm.gameAre = gameAreItem.id;
                    serverList.value = gameAreItem.children;
                } else {
                    dataForm.gameAre = gameAreItem.id;
                    dataForm.server = gameAreItem.id;
                }
            } else if (element.key.indexOf('系统区服') > -1) {
                const serverItem = serverList.value.filter((item: any) => item.title == element.value)[0];
                dataForm.server = serverItem.id;
            } else if (element.key.indexOf('您自己认为是亮点的地方') > -1) {
                dataForm.highlights = element.value
            } else {
                
                dataForm.attributesList.forEach((item:any) => {
                    if (item.name == element.key) {
                        if ( item.type == 1 ) {  // 单选
                            const ids = nameQueryId(element.value, item.children);
                            item.attributeIds = ids ? ids.join('') : null;
                        }
                        if ( item.type == 2 ) {  // 多选
                            const ids = nameQueryId(element.value, item.children);
                            item.attributeIds = ids ? ids : null;
                        }
                        if ( item.type == 3 ) {  // 文本
                            item.attributeText = element.value
                        }
                    }
                });
            }
        });
        if(!type) ElMessage.success('解析成功！')
    }

    // 文本内容转换成数组
    const arrayToKeyValuePairs = (strArray: any) => {
        try {
            let arr: { key: any, value: any }[] = [];
            strArray.forEach((item: any) => {
                const [key, value] = item.split('：');
                if (value == undefined) {
                    throw '解析失败，商品简介文本内容结构错误。请检查后重新解析；'
                }
                arr.push({ key: key, value: value })
            })
            return arr
        } catch (err: any) {
            ElMessage.warning(err)
            return err;
        }
    }

    // 属性名称查询属性ID
    const nameQueryId = (value: string, data: any[]) => {
        const valToArr = value.split('，')
        const ids:any = [];
        valToArr.forEach((name: string) => {
            data.forEach((item: any) => {
                if (item.name == name) {
                    ids.push(item.id)
                }
            })
        })
        return ids
    }


    // 点击属性回显商品简介
    const feedBackInfo = () => {
        const infoArr: any = [];
        const titleArr: any = [];
        // 区服
        const gameAreItem = districtList.value.filter((item: any) => item.id == dataForm.gameAre)[0];
        infoArr.push(`服务器：${gameAreItem ? gameAreItem.title : ''}`);
        if (serverList.value.length > 0) {
            const serverItem = serverList.value.filter((item: any) => item.id == dataForm.server)[0];
            infoArr.push(`系统区服：${serverItem ? serverItem.title : ''}`);
        } 
        // 属性
        dataForm.attributesList.forEach((item: any) => {
            if (item.type == 1) {
                const names = idQueryName([item.attributeIds], item.children).join('');
                infoArr.push(`${item.name}：${names}`);
            }
            if (item.type == 2) {
                const names = idQueryName(item.attributeIds, item.children).join('，');
                infoArr.push(`${item.name}：${names}`);
            }
            if (item.type == 3) {
                infoArr.push(`${item.name}：${item.attributeText}`);
            }
        })
        dataForm.info = infoArr.join('\n');

        // 拼接商品标题
        // 获取文本类型属性
        const textArr = dataForm.attributesList.filter((item: any) => item.type == 3 && item.isTitle == 0)
        if (textArr.length > 0) {
            textArr.forEach((item: any) => { 
                if (item.attributeText) {
                    titleArr.push(`${item.name}${item.attributeText}`);
                }
            })
        }
        
        // 获取单选类型属性
        const radioArr = dataForm.attributesList.filter((item: any) => item.type == 1 && item.isTitle == 0)
        if (radioArr.length > 0) { 
            radioArr.forEach((item: any) => { 
                const names = idQueryName([item.attributeIds], item.children).join('');
                if (names) {
                    titleArr.push(`【${names}】`);
                }
            })
        }
        
        // 获取多选类型属性
        const checkboxArr = dataForm.attributesList.filter((item: any) => item.type == 2 && item.isTitle == 0)
        if (radioArr.length > 0) { 
            checkboxArr.forEach((item: any) => {
                if (item.attributeIds.length > 0) {
                    titleArr.push(`${item.name}${item.attributeIds.length}`);
                }
            })
        }
        dataForm.title = titleArr.join(' ');
    }


    // 属性ID查询属性名称
    const idQueryName = (value: any, data: any[]) => {
        const names:any = [];
        value.forEach((id: string) => {
            data.forEach((item: any) => {
                if (item.id == id) {
                    names.push(item.name)
                }
            })
        })
        return names
    }



    // 基本信息回显
    const setBaseInfo = (data: any) => {
        dataForm.server = data.server;
        if(data.gameAreaTree && data.gameAreaTree) serverIdFindData(data.gameAreaTree);

        dataForm.gameAccount = data.gameAccount;
        dataForm.gamePassword = data.gamePassword;
        dataForm.buyerPhone = data.buyerPhone;
        dataForm.sellerPhone = data.sellerPhone;
        dataForm.acquisitionPrice = data.acquisitionPrice;
        dataForm.title = data.title;
        dataForm.info = data.info;
        dataForm.log = data.log;
        dataForm.images = data.images ? JSON.parse(data.images) : [];

        if(dataForm.server){
            getServerList();
        }
    }
    


    // 按照serverId 回显
    const serverIdFindData = (gameAreaTree: any) => {
        gameAreaTree.map(item => {
            if(item.children && item.children.length){
                item.children.map(sub => {
                    if(sub.id == dataForm.server){
                        dataForm.district = item.id;
                    }
                })
            }else if(item.id == dataForm.server){
                console.log(item.id, dataForm.server)
                dataForm.district = item.id;
            }
        })
    }


    // 提交
    const formRef = ref();
    const btnLoading = ref(false);
    const submitForm = () => {
        formRef.value.validate(async (valid: boolean) => {
            if (!valid) return;

            btnLoading.value = true;

            if(currentType.value == 1){
                // 基本信息 
                submitBaseInfo();
            }

        })
    }

    // 提交基本信息
    const submitBaseInfo = () => {
        const params = JSON.parse(JSON.stringify(dataForm));
        let attributesList = JSON.parse(JSON.stringify(dataForm.attributesList));
        attributesList.map((item: any) => {
            if(item.type == 1) item.attributeIds = [item.attributeIds];
            delete item.children
        })
        baseService.post("/purchase/completeInfo", {
            orderId: params.orderId,
            server: params.server || params.district,
            gameAccount: params.gameAccount,
            gameId: params.gameId,
            gamePassword: params.gamePassword,
            buyerPhone: params.buyerPhone,
            sellerPhone: params.sellerPhone,
            acquisitionPrice: params.acquisitionPrice,
            guaranteeAmount: params.guaranteeAmount,
            title: params.title,
            info: params.info,
            attributesList: attributesList,
            log: params.log,
            images: params.images,
            highlights: params.highlights
        }).then(res => {
            if (res.code == 0) {
                ElMessage.success('基本信息提交成功！');
                visible.value = false;
                emits('refresh');
            }
        }).finally(() => {
            btnLoading.value = false;
        })
    }

    
    defineExpose({
        init
    })

</script>

<style lang="scss">
    .acquisition-complete-info{
        .image{
            width: 148px;
            height: 148px;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #E4E7ED;
        }

        .el-drawer__body{
            padding: 0;
        }

        .contract-real-name{
            padding: 0 12px 12px 12px;

            .p-title{
                margin-top: 0;
            }
        }

        .flod_tab{
            display: flex;
            align-items: flex-start;
            width: 100%;
            .flod_tab_cont{
                flex: 1;
                overflow: hidden;
                .el-radio{
                    margin-right: 20px;
                }
                .el-radio.is-bordered.el-radio--small{
                    margin-bottom: 10px;
                }
            }
        }
        .oper {
            width: 120px;
            height: 28px;
            box-sizing: border-box;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .resetFont {
                cursor: pointer;
                margin-right: 6px
            }

            .foledBtn {
                float: right;
                padding: 12px 10px;
                margin-right: 0;
            }
        }

        .mytag {
            background: #fcf2bb;
            border-radius: 20px;
            color: #f6930a;
            display: inline-block;
            font-size: 12px;
            height: 25px;
            line-height: 25px;
            max-width: 100%;
            overflow: hidden;
            padding: 0 15px 0 30px;
            position: relative;
            text-overflow: ellipsis;
            white-space: nowrap;

            .topImg {
                width: 25px;
                height: 25px;
                position: absolute;
                top: 0;
                left: 3px;
            }
        }
    }
</style>  
