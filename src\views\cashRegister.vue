<template>
  <!-- 需要接口获取当前订单是否为关闭状态 -->
  <el-drawer v-model="visible" :footer="null" :show-close="false" :modal="false" size="100%" style="background: #f2f2f2" class="cashRegisterDrawer">
    <template #header>
      <div style="width: 1200px; display: flex; align-items: center; flex: inherit; margin: auto">
        <img style="width: 26px; height: 26px" :src="adminLogo" alt="" />
        <span>恒星游戏交易管理系统</span>
        <span style="margin-left: 10px">收银台</span>
      </div>
    </template>
    <div style="width: 1200px; margin: auto">
      <el-alert title="充值后长时间不到账，请及时联系客服，客服电话：400-888-6688" type="info" show-icon close-text="我已了解" />
      <el-card shadow="never">
        <template #header>
          <span
            >账户名称：<b style="margin-left: 12px; font-weight: bold; font-size: 14px; color: #303133; line-height: 20px">{{ store.state.user.realName }}</b></span
          >
        </template>
        <div>
          <div style="margin-bottom: 12px">充值金额</div>
          <el-statistic :value="dataForm.amount" prefix="￥" :precision="2" />
        </div>
      </el-card>
      <div class="payCard">
        <div style="font-weight: bold; font-size: 14px; color: #303133; line-height: 20px; margin-bottom: 12px">支付方式</div>
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 44px">
          <el-button>
            <img style="width: 24px; margin-right: 10px" src="@/assets/images/zhbPay.png" alt="" />
            支付宝支付
          </el-button>
          <el-statistic title="实付金额" suffix="元" :value="dataForm.payAmount" />
        </div>
        <div class="codeCard">
          <div style="font-size: 14px; color: #606266; display: flex" v-if="orderStatus">
            请在
            <span style="margin: 0 2px"><el-countdown style="line-height: 16px" value-style="color: #e6a23c;font-weight: 400;font-size: 14px;" format="mm[分]ss[秒]" :value="timedate"> </el-countdown></span>
            内完成支付，超时订单将自动关闭
          </div>
          <div style="font-size: 14px; color: #606266; display: flex" v-else>
            订单
            <span style="margin: 0 2px; color: #f56c6c">超时</span>
            已自动关闭，请返回重新发起支付
          </div>
          <div style="padding: 12px; margin: 12px 0; border-radius: 8px; border: 1px solid #ebeef5">
            <iframe v-if="orderStatus && !resultVisible" style="width: 205px; height: 208px" :src="codeUrl"></iframe>
            <img v-else style="width: 184px; height: 184px" src="@/assets/images/codeBroken.png" alt="" srcset="" />
          </div>
          <div style="font-size: 14px; color: #303133; margin-bottom: 12px">{{ !orderStatus ? "订单关闭" : "打开支付宝扫一扫，继续付款" }}</div>
          <div style="font-size: 14px; color: #909399; line-height: 20px">订单号：{{ dataForm.orderId }}</div>
          <div style="font-size: 14px; color: #909399; line-height: 20px">收款方：恒星游戏交易账号管理游戏公司</div>
        </div>
      </div>
    </div>
    <el-dialog v-model="resultVisible" title="支付结果" :close-on-click-modal="false" :close-on-press-escape="false" :width="480">
      <div style="display: flex; flex-direction: column; align-items: center">
        <img style="width: 63px; height: 63px" src="@/assets/images/payOk.png" alt="" />
        <div style="font-weight: 400; font-size: 16px; color: #303133; line-height: 24px; margin-bottom: 4px">支付成功!</div>
        <!-- 支付成功后三秒倒计时 -->
        <div style="font-weight: 400; font-size: 14px; color: #909399; line-height: 22px">{{ 3 - +timeNum }}秒后自动返回商家管理系统页面</div>
        <el-button style="margin-top: 32px; margin-bottom: 50px" type="primary" @click="backPage">立即返回</el-button>
        <!-- 返回系统首页 -->
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script lang="ts" setup>
import { IObject } from "@/types/interface";
import { ref, defineExpose, defineEmits, reactive, toRefs, onMounted, onUnmounted } from "vue";
import { useAppStore } from "@/store";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { useSettingStore } from "@/store/setting";
import QrcodeVue from "qrcode.vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const emits = defineEmits(["refresh"]);
const dataForm = ref({} as IObject);
const sttingInfo = useSettingStore();
const store = useAppStore();
const visible = ref(true);
const adminLogo = sttingInfo.info.backendLogo;
const resultVisible = ref(false);
const codeUrl = ref("");
const orderStatus = ref(true); //订单状态false:关闭
const timer = ref();
const timeNum = ref(0);
const weChatHandle = () => {
  baseService
    .get(`/pay/alipay/webPay?orderId=${dataForm.value.orderId}`)
    .then((res) => {
      if (res.code == 0) {
        codeUrl.value = res.msg;
        timedate.value = Date.now() + 1000 * 60 * 5;
        timer.value = setInterval(() => {
          timeNum.value++;
          submitSucess();
          // 如果时间到5分钟还在执行则付款码展示失效状态，清除定时器------
          if (timeNum.value > 60 * 5) {
            orderStatus.value = false;
            clearInterval(timer.value);
          }
        }, 5000);
      } else {
        // 错误状态默认为过期
        orderStatus.value = false;
      }
    })
    .catch((err) => {
      // 错误状态默认为过期
      orderStatus.value = false;
    });
};
const submitSucess = () => {
  baseService.get(`/pay/alipay/payEnd?orderId=${dataForm.value.orderId}`).then((res) => {
    if (res.code == 0 && res.msg == "success") {
      // 成功：清除定时器，打开成功弹窗，倒计时3秒返回
      clearInterval(timer.value);
      resultVisible.value = true;
      timeNum.value = 0;
      timer.value = setInterval(() => {
        timeNum.value++;
        if (timeNum.value > 2) {
          clearInterval(timer.value);
          backPage();
        }
      }, 1000);
    }
  });
};
const backPage = () => {
  router.push(dataForm.value.backUrl);
};
const timedate = ref();
onMounted(() => {
  console.log(sttingInfo);
  dataForm.value = route.query;
  weChatHandle();
});
onUnmounted(() => {
  if (timer.value) clearInterval(timer.value);
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
.cashRegisterDrawer {
  .el-drawer__header {
    padding: 0 !important;
    height: 50px;
    background: #ffffff;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.06);
    font-family: OPPOSans, OPPOSans;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    .el-alert--info {
      margin-bottom: 20px;
      &.is-light {
        color: var(--el-color-primary);
        background-color: var(--color-primary-light);
      }
      .el-alert__close-btn,
      .is-customed {
        color: var(--el-color-primary);
      }
    }
    .el-card {
      border: none;
      margin-bottom: 20px;
      padding: 24px;

      .el-card__header {
        padding: 0;
        padding-bottom: 12px;
      }

      .el-card__body {
        padding: 0;
        padding-top: 12px;
      }

      .el-statistic__content {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 24px;
        color: #f44a29;
        line-height: 28px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .payCard {
      background: #fff;
      padding: 24px;
      border-radius: 4px;

      .el-button {
        width: 168px;
        height: 48px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #4165d7;
      }

      .el-statistic__head {
        text-align: right;
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        line-height: 20px;
      }

      .codeCard {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
</style>
