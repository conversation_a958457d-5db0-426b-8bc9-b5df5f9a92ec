<template>
  <div class="mod-official__tbofficialvideo">
    <el-card shadow="never" class="rr-view-ctx-card ny_form_card">
      <ny-form-slot>
        <template #content>
          <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
            <el-form-item>
              <el-input v-model="state.dataForm.title" placeholder="视频标题" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <ny-select v-model="state.dataForm.type" dict-type="official_video_type" placeholder="视频类型"></ny-select>
            </el-form-item>
          </el-form>
        </template>
        <template #button>
          <el-button @click="state.getDataList()" type="primary">{{ $t("query") }}</el-button>
          <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
        </template>
      </ny-form-slot>
    </el-card>
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="ny-table-button-list">
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
        <el-button v-if="state.hasPermission('official:tbofficialvideo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        <el-button v-if="state.hasPermission('official:tbofficialvideo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </div>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="title" label="视频标题" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cover" label="视频封面" header-align="center" align="center">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px" :src="scope.row.cover" :preview-src-list="[scope.row.cover]" preview-teleported fit="cover" />
          </template>
        </el-table-column>
        <el-table-column prop="type" label="视频类型" header-align="center" align="center">
          <template v-slot="scope">
            {{ state.getDictLabel("official_video_type", scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者ID" header-align="center" align="center"> </el-table-column>
        <el-table-column prop="updater" label="更新者ID" header-align="center" align="center"> </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
          <template v-slot="scope">
            <el-button v-if="state.hasPermission('official:tbofficialvideo:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
            <el-button v-if="state.hasPermission('official:tbofficialvideo:delete')" type="primary" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          </template>
        </el-table-column>
        <!-- 空状态 -->
        <template #empty>
          <div style="padding: 68px 0">
            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
          </div>
        </template>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./tbofficialvideo-add-or-update.vue";

const view = reactive({
  getDataListURL: "/official/tbofficialvideo/page",
  getDataListIsPage: true,
  exportURL: "/official/tbofficialvideo/export",
  deleteURL: "/official/tbofficialvideo",
  deleteIsBatch: true,
  dataForm: {
    title: "",
    type: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.title = "";
  state.dataForm.type = "";
  state.getDataList();
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
