<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px" style="width: 90%">
      <el-form-item label="合作商名称" prop="partnerId">
        <el-select v-model="dataForm.partnerId" placeholder="请选择" @change="partnerChange" style="width: 60%">
          <el-option v-for="(item, index) in partneruserListArr" :key="index" :label="item.companyName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="游戏名称" prop="gameId">
        <el-select v-model="dataForm.gameId" placeholder="请选择" @change="gameChange" style="width: 60%">
          <el-option v-for="(item, index) in gameListArr" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="属性名称" prop="attributeId">
        <el-select v-model="dataForm.attributeId" placeholder="请选择" @change="attributeChange" style="width: 60%">
          <el-option v-for="(item, index) in gamePropertiesListArr" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="属性关联">
        <el-table v-loading="AttributeCorrelation" :data="attributeCorrelationTable" border style="width: 100%">
          <el-table-column prop="attributeName" label="平台属性" />
          <el-table-column prop="propertiesName" label="平台上级名称" />
          <el-table-column prop="partnerAttributeName" label="外部平台属性">
            <template #default="scope">
              <el-select @visible-change="(visible: boolean) => { visibleChange(visible,scope.row.partnerAttributeName) }" @change="partnerAttributeNameFn(scope.row, scope.$index)" v-model="scope.row.partnerAttributeName" placeholder="请选择关联属性" filterable>
                <el-option v-for="(item, index) in externalAttribute" :key="index" :label="item.attribute" :value="item.attribute" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="partnerSuperiorName" label="外部平台上级名称" />
          <!-- 空状态 -->
          <template #empty>
            <div style="padding: 68px 0">
              <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
            </div>
          </template>
        </el-table>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  gameCode: "",
  gameId: "",
  attributeId: "",
  attributeCode: "",
  attributeName: "",
  partnerId: "",
  partnerName: "",
  partnerAttributeId: "",
  partnerAttributeName: "",
  partnerSuperiorName: "",
  remark: "",
  type: "",
  partnerSuperiorId: "",
  field1: "",
  field2: "",
  isDelete: "",
  creator: "",
  createDate: "",
  updater: "",
  updateDate: ""
});

const rules = ref({});

const init = (id?: number, gameId?: number, gameCode?: string) => {
  visible.value = true;
  dataForm.id = "";
  dataForm.gameId = gameId;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  } else {
    dataForm.gameCode = gameCode;
  }

  getGameList();
  getGamePropertiesList();
  getPartneruserList();
};

const gameListArr = ref([]); // 游戏列表数组
const gamePropertiesListArr = ref([]); // 属性列表数组
const partneruserListArr = ref([]); // 合作商列表数组
const AttributeCorrelation = ref(false); // 属性关联加载
const attributeCorrelationTable = ref(<any>[]); // 属性关联列表
const externalAttribute = ref([]); // 外部平台属性
const attributeItem = ref(<any>""); // 当前关联属性

// 获取游戏列表
const getGameList = () => {
  baseService.get("/shop/tbgame/gameList").then((res) => {
    gameListArr.value = res.data;
  });
};

// 获取属性列表
const getGamePropertiesList = () => {
  baseService.get("/shop/tbgameproperties/list", { pid: dataForm.gameId }).then((res) => {
    gamePropertiesListArr.value = res.data;
  });
};

// 获取合作商列表
const getPartneruserList = () => {
  baseService.get("/partner/tbpartneruser/list").then((res) => {
    partneruserListArr.value = res.data;
  });
};

// 合作商选择事件
const partnerChange = (value: any) => {
  dataForm.partnerName = partneruserListArr.value.filter((item: any) => item.id == value)[0].companyName;
  if (attributeItem.value) {
    getAttributeCorrelation();
  }
};

// 游戏选择事件
const gameChange = (value: any) => {
  dataForm.gameId = value;
  getGamePropertiesList();
  // console.log(" ===== game ===== ",value)
};

// 属性选择事件
const attributeChange = (value: any) => {
  attributeItem.value = "";
  if (!dataForm.partnerId) {
    dataForm.attributeId = "";
    ElMessage.warning("请选择合作商");
    return;
  }

  attributeItem.value = gamePropertiesListArr.value.filter((item: any) => item.id == value)[0];
  getAttributeCorrelation();
};

// 提取关联属性
const getAttributeCorrelation = () => {
  AttributeCorrelation.value = true;
  baseService.get("/shop/tbpartnerattributemapping/query", { partnerId: dataForm.partnerId, gameId: dataForm.gameId, propertiesId: dataForm.attributeId }).then((res) => {
    AttributeCorrelation.value = false;
    attributeCorrelationTable.value = res.data;
  });

  // attributeCorrelationTable.value = [];
  // attributeItem.value.detailsList.forEach((item: any, index: number) => {
  //   attributeCorrelationTable.value.push({
  //     ...item,
  //     attributeId: item.id,
  //     attributeCode: item.gadKey,
  //     attributeName: item.gadValue,
  //     partnerId: dataForm.partnerId,
  //     partnerName: dataForm.partnerName,
  //     propertiesName: attributeItem.value.name,
  //   })
  // })
  // console.log('______ ', attributeCorrelationTable.value)
};

// 获取外部平台属性
const visibleChange = (visible: boolean, name: string) => {
  console.log(visible);
  if (visible) {
    externalAttribute.value = [];
    baseService.get("/shop/tbpartnerattributemapping/search", { partnerId: dataForm.partnerId, gameId: dataForm.gameId, attribute: name }).then((res) => {
      externalAttribute.value = res.data;
    });
  }
};

// 选择关联平台属性
const partnerAttributeNameFn = (data: any, index: number) => {
  let itemdata: any = externalAttribute.value.filter((item: any) => {
    if (attributeCorrelationTable.value[index].partnerAttributeName == item.attribute) {
      return item;
    }
  });
  attributeCorrelationTable.value[index].partnerAttributeId = itemdata[0].attributeId;
  attributeCorrelationTable.value[index].partnerAttributeName = itemdata[0].attribute;
  attributeCorrelationTable.value[index].partnerSuperiorName = data.partnerSuperiorName;
  console.log(attributeCorrelationTable.value, "表格内关联");
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/tbpartnerattributemapping/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.post("/shop/tbpartnerattributemapping/update", attributeCorrelationTable.value).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
    // (!dataForm.id ? baseService.post : baseService.put)("/shop/tbpartnerattributemapping", dataForm).then((res) => {
    //   ElMessage.success({
    //     message: t("prompt.success"),
    //     duration: 500,
    //     onClose: () => {
    //       visible.value = false;
    //       emit("refreshDataList");
    //     }
    //   });
    // });
  });
};

defineExpose({
  init
});
</script>
