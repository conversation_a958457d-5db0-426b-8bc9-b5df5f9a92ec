<template>
    <div class="thirdParty_page" >
        <!-- <div class="shop_page_basic" style="margin-bottom: 12px;border-radius: 0px 8px 8px 8px;">
            <div class="titleSty">平台状态</div>
            <el-descriptions style="width: 100%" class="descriptions descriptions-label-140" border :column="2">
                <el-descriptions-item label="商品编码">{{ props.dataForm.code }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag type="primary" v-if="props.dataForm.status == 2">已上架</el-tag>
                    <el-tag type="danger" v-if="props.dataForm.status == 3">已下架</el-tag>
                </el-descriptions-item>
            </el-descriptions>
        </div> -->
        <div class="shop_page_basic">
            <div class="titleSty">推送信息</div>
            <ny-button-group
                :list="[
                { dictLabel: '同步上架', dictValue: '1' },
                { dictLabel: 'API上架', dictValue: '2' }
                ]"
                v-model="listingShow"
                @change="handleClick"
                style="display: inline-block;"
            ></ny-button-group>
            <ny-table 
                ref="nyTableRef"
                :state="state" 
                :columns="listingShow == '1' ? columns : columnsApi"
                :showColSetting="false"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle" 
                @selectionChange="state.dataListSelectionChangeHandle"
            >
            
                <template #scriptUserName="{row}">
                    <el-select
                        v-if="row.scriptUserName"
                        v-model="row.scriptUserName"
                        size="small"
                        @visible-change="(visible: boolean)=>{getShopList(visible,row)}"
                        @change="(value: any)=>{ scriptUserNameChange(value,row)}"
                        
                    >
                        <el-option
                            v-for="item in shopList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </template>
                <!-- <template #taskType="{row}">
                    <template v-if="listingShow == '1'">
                        <el-tag type="primary" v-if="row.taskType == '1'">寄售</el-tag>
                        <el-tag type="danger" v-if="row.taskType == '2'">上架</el-tag>
                        <el-tag type="primary" v-if="row.taskType == '3'">擦亮</el-tag>
                        <el-tag type="primary" v-if="row.taskType == '4'">编辑</el-tag>
                        <el-tag type="primary" v-if="row.taskType == '5'">下架</el-tag>
                    </template>
                    <template v-else>
                        <el-tag type="primary" v-if="row.taskType == '1'">上架</el-tag>
                        <el-tag type="danger" v-if="row.taskType == '2'">下架</el-tag>
                    </template>
                    
                </template> -->
                <template #status="{row}">
                    <el-tag type="info" v-if="row.status == 0">未推送</el-tag>
                    <el-tag type="success" v-if="row.status == 1">上架</el-tag>
                    <el-tag type="warning" v-if="row.status == 2">下架</el-tag>
                </template>
                <!-- <template #channelPrice="{row}">
                    <el-input v-model="row.channelPrice" type="number" size="small" placeholder="请输入" @blur="inputBlur(row)"/>
                </template> -->
            </ny-table>
        </div>
      </div>
</template>

<script lang='ts' setup>
import useView from "@/hooks/useView";
import { ElMessage } from "element-plus";
import baseService from '@/service/baseService';
import { ref,reactive, onMounted, toRefs } from 'vue';

const emit = defineEmits(["thridChange"]);
const listingShow = ref("1");
const shopList = ref(<any>[]);
const nyTableRef = ref();

const props = defineProps({
    dataForm:{
        type: Object,
    }
})
const view = reactive({
    getDataListURL: "/shop/shopPushInfo/page",
    getDataListIsPage: true,
    deleteURL: "",
    deleteIsBatch: true,
    dataForm: {
        shopId:props.dataForm.shopId,
        type: '1'
    }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 同步上架
const columns = reactive([
    // {
    //     type: "selection",
    //     width: 50
    // },
    {
        prop: "partnerName",
        label: "合作商名称",
        minWidth: "150"
    },
    {
        prop: "scriptUserName",
        label: "店铺名称",
        minWidth: "150"
    },
    {
        prop: "partnerShopCode",
        label: "合作商商品编码",
        minWidth: "150",
    },
    // {
    //     prop: "taskType",
    //     label: "推送操作",
    //     minWidth: "100"
    // },
    {
        prop: "status",
        label: "状态",
        minWidth: "100",
    },
    // {
    //     prop: "channelPrice",
    //     label: "渠道价格",
    //     minWidth: "150"
    // }
])
// API
const columnsApi = reactive([
    // {
    //     type: "selection",
    //     width: 50
    // },
    {
        prop: "partnerName",
        label: "合作商名称",
        minWidth: "150"
    },
    {
        prop: "partnerShopCode",
        label: "合作商商品编码",
        minWidth: "150",
    },
    // {
    //     prop: "taskType",
    //     label: "推送操作",
    //     minWidth: "100"
    // },
    {
        prop: "status",
        label: "状态",
        minWidth: "100",
    },
    // {
    //     prop: "channelPrice",
    //     label: "渠道价格",
    //     minWidth: "150"
    // }
])

// 推送分类切换
const handleClick = (value:any) =>{
    view.dataForm.type = value;
    nyTableRef.value.clearSelection();
    state.getDataList();
    emit("thridChange",value);
}

// 店铺搜索
const getShopList = (visible: boolean,row:any) =>{
    if(visible){
        baseService.get("/script/sysscriptuser/page",{ scriptPartnerId:row.partnerId }).then(res=>{
            if(res.data){
            shopList.value = res.data.list;
            }
        })
    }
}

// 选择回调
const scriptUserNameChange = (value:any, row:any) =>{
    const info = shopList.value.find((item:any) => item.id == value);
    row.scriptUserId = info.id;
    row.scriptUserName = info.name;
    inputBlur(row);
}

// 渠道价格回调
const inputBlur = (row:any) =>{
    baseService.put("/shop/shopPushInfo",row).then(res=>{
        if(res.code == 0){
            // ElMessage.success({
            //     message: res.msg
            // });
            state.getDataList();
        }
    })
}


// 推送获取
const pushSubmit = (type:any) =>{
    if(state.dataListSelections?.length == 0){
        ElMessage.warning('请选择推送的合作商');
        return;
    }

    let ids = state.dataListSelections?.map(item => item.id).join(',');
    console.log(state.dataListSelections,'====== state.dataListSelections ======');
    
    // state.dataListSelections?.map(item=>{
    //     item.taskType = type
    // })
    baseService.post("/shop/shopPushInfo/sync",{taskType: type, ids}).then(res=>{
        if(res.code == 0){
            ElMessage.success({
                message: res.msg
            });
        }
    }).finally(()=>{
        state.getDataList();
    })
}

defineExpose({
    pushSubmit,
});

</script>

<style lang='less' scoped>
.thirdParty_page {
  background-color: #f0f2f5;
  // border-radius: 8px;
  padding: 0px;
}
.shop_page_basic {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;

  :deep(.NYpagination){
    width: auto !important;
  }
}
.titleSty {
  font-family: Inter, Inter;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  line-height: 20px;
  padding-left: 8px;
  border-left: 2px solid var(--el-color-primary);
  margin-bottom: 12px;
}

:deep(.table-body){
    padding-bottom: 0px;
}

:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;
  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;
      .el-descriptions__label {
        display: flex;
        align-items: center;
        font-weight: normal;
        width: 144px;
      }
      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;
        > div {
          width: 100%;
        }
        .el-form-item__label {
          display: none;
        }
        .el-form-item {
          margin-bottom: 0;
        }
      }
      .noneSelfRight {
        border-right: 0 !important;
      }
      .noneSelfLeft {
        border-left: 0 !important;
      }
      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>