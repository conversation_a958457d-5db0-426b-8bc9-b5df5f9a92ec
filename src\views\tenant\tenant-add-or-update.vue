<template>
  <el-drawer v-model="visible" size="50%" class="drawer_shop">
    <template #header>
      <div class="drawer_title">{{ !dataForm.id ? $t("add") : $t("update") }}</div>
    </template>
    <div class="shop_page">
      <div class="shop_page_card">
        <ny-title title="基本信息" style="padding: 0px 0px 12px 0px" />
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-position="top">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="tenantName" label="合作商名称">
                <el-input v-model="dataForm.tenantName" placeholder="请输入合作商名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="username" label="手机号">
                <el-input v-model="dataForm.username" placeholder="请输入手机号作为登录账号" :disabled="!!dataForm.id"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="idCard" label="身份证号">
                <el-input v-model="dataForm.idCard" placeholder="请输入身份证号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="realName" label="注册人">
                <el-input v-model="dataForm.realName" placeholder="请输入注册人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="tenantDomain" label="合作商域名">
                <el-input v-model="dataForm.tenantDomain" placeholder="请输入合作商域名"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item v-if="dataForm.tenantMode === 0" prop="roleIdList" label="合作商角色" class="role-list">
                <el-select v-model="dataForm.roleIdList" multiple placeholder="合作商角色">
                  <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="tenantDomain" label="关联合作商">
                <el-select v-model="dataForm.partnerId" placeholder="请选择关联合作商">
                  <el-option v-for="item in PartnerList" :key="item.id" :label="item.companyName" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="status" :label="$t('tenant.status')">
                <el-radio-group v-model="dataForm.status">
                  <el-radio :label="0" border>{{ $t("tenant.status0") }}</el-radio>
                  <el-radio :label="1" border>{{ $t("tenant.status1") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="shop_page_card" style="margin-top: 12px">
        <div class="flx-justify-between">
          <ny-title title="游戏分配" style="padding: 0px 0px 12px 0px" />
          <el-button type="primary" plain size="small" @click="GameDialog = true" style="margin-bottom: 12px">选择游戏</el-button>
        </div>
        <div v-if="dataForm.gameIdList.length">
          <el-descriptions :column="1" border class="descriptions descriptions-label-140">
            <el-descriptions-item>
              <template #label>已分配游戏</template>
              {{ gameIdByName(dataForm.gameIdList) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <ny-no-data type="3" description="暂未分配游戏" :image-size="80" v-else />
      </div>
      <div class="shop_page_card" style="margin-top: 12px">
        <div class="flx-justify-between">
          <ny-title title="API分配" style="padding: 0px 0px 12px 0px" />
          <el-button type="primary" plain size="small" @click="APIDialog = true" style="margin-bottom: 12px">选择API</el-button>
        </div>
        <div v-if="dataForm.partnerList.length">
          <el-descriptions :column="1" border class="descriptions descriptions-label-140">
            <el-descriptions-item>
              <template #label>已分配API</template>
              {{ apiIdByName(dataForm.partnerList) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <ny-no-data type="3" description="暂未分配游戏" :image-size="80" v-else />
      </div>
    </div>

    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-drawer>
  <!-- 分配游戏 -->
  <el-dialog v-model="GameDialog" title="分配游戏" width="734px">
    <el-transfer
      v-model="gameIdList"
      filterable
      filter-placeholder="搜索游戏名称"
      :data="gamesList"
      :titles="['选择游戏', '已选游戏']"
      :button-texts="['从已选取消', '添加到已选']"
      :props="{
        key: 'id',
        label: 'title'
      }"
      class="select-partner"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="GameDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="
            dataForm.gameIdList = gameIdList;
            GameDialog = false;
          "
          >保存</el-button
        >
      </div>
    </template>
  </el-dialog>
  <!-- API分配 -->
  <el-dialog v-model="APIDialog" title="分配API" width="734px">
    <el-transfer
      v-model="ApiIdLIst"
      filterable
      filter-placeholder="搜索API名称"
      :data="ApiList"
      :titles="['选择API', '已选API']"
      :button-texts="['从已选取消', '添加到已选']"
      :props="{
        key: 'id',
        label: 'companyName'
      }"
      class="select-partner"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="APIDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="
            dataForm.partnerList = ApiIdLIst;
            APIDialog = false;
          "
          >保存</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { isCardNo } from "@/utils/method";
import baseService from "@/service/baseService";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";

const { t } = useI18n();

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const roleList = ref<any[]>([]);
const datasourceList = ref<any[]>([]);

// 分配游戏
const GameDialog = ref(false);
const gamesList = ref(<any>[]); // 游戏列表
const gameIdList = ref([]);

// 分配API
const APIDialog = ref(false);
const ApiList = ref(<any>[]); // API列表
const ApiIdLIst = ref([]);

// 合作商列表
const PartnerList = ref(<any>[]);

const dataForm = reactive({
  id: "",
  tenantMode: 0,
  tenantDomain: "",
  tenantName: "",
  username: "",
  idCard: "",
  realName: "",
  status: 1,
  partnerId: "",
  gameIdList: [],
  partnerList: [],
  roleIdList: []
});

const validatePassword = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (!dataForm.id && !/\S/.test(value)) {
    return callback(new Error(t("validate.required")));
  }
  callback();
};
// 自定义身份证校验规则
const idCardValidator = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入正确身份证号"));
  } else {
    if (!isCardNo(value)) {
      callback(new Error("身份证号格式错误"));
    } else {
      callback();
    }
  }
};
const rules = ref({
  tenantMode: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  username: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  idCard: [
    { required: true, message: t("validate.required"), trigger: "blur" },
    { validator: idCardValidator, trigger: "blur" }
  ],
  realName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  datasourceId: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  roleIdList: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  tenantName: [{ required: true, message: t("validate.required"), trigger: "change" }],
  password: [{ validator: validatePassword, trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  Promise.all([getDataSourceList(), getRoleList(), getGamesList(), getApiList(), getPartnerList()]).then(() => {
    if (id) {
      getInfo(id);
    }
  });
};

// 获取租户数据源列表
const getDataSourceList = async () => {
  const res = await baseService.get("/sys/tenant/datasource/list");
  datasourceList.value = res.data;
};

// 获取角色列表
const getRoleList = async () => {
  const res = await baseService.get("/sys/tenant/role/list");
  roleList.value = res.data;
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/tenant/${id}`).then((res) => {
    console.log(res.data);
    res.data.gameIdList = res.data.gameIdList ? res.data.gameIdList : [];
    res.data.partnerList = res.data.partnerList ? res.data.partnerList : [];
    Object.assign(dataForm, res.data);
  });
};

// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
  });
};

// 获取已开通API列表
const getApiList = () => {
  baseService.get("/partner/partner/page", { openState: "1", page: 1, limit: 99999 }).then((res) => {
    ApiList.value = res.data.list;
  });
};

// 获取所有合作商
const getPartnerList = () => {
  baseService.get("/partner/partner/page", { page: 1, limit: 99999 }).then((res) => {
    PartnerList.value = res.data.list;
  });
};

// 根据游戏id获取名称
const gameIdByName = (idList: any) => {
  const nameList: any = [];
  gamesList.value.map((item: any) => {
    idList.map((ele: any) => {
      if (item.id == ele) {
        nameList.push(item.title);
      }
    });
  });
  return nameList.join("、");
};

// 根据APIid获取名称
const apiIdByName = (idList: any) => {
  const nameList: any = [];
  ApiList.value.map((item: any) => {
    idList.map((ele: any) => {
      if (item.id == ele) {
        nameList.push(item.companyName);
      }
    });
  });
  return nameList.join("、");
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    if (!dataForm.roleIdList) {
      dataForm.roleIdList = [];
    }
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/sys/tenant", {
      ...dataForm,
      roleIdList: [...dataForm.roleIdList]
    })
      .then(() => {
        baseService.post("partner/partner/bindKeyUrl", {
          id: dataForm.partnerId
        });
        visible.value = false;
        ElMessage({
          message: t("prompt.success"),
          type: "success",
          duration: 500,
          onClose: () => {
            emit("refreshDataList");
            btnLoading.value = false;
          }
        });
      })
      .catch(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
.shop_page {
  background-color: #f0f2f5;
  padding: 12px;
  // height: 100%;
}
.shop_page_card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}
</style>
<style lang="less">
.select-partner {
  display: flex;
  align-items: center;

  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    .el-button {
      display: block;
      margin: 10px 0;
    }
  }
}
.drawer_shop {
  .el-drawer__header {
    margin-bottom: 0px;
  }
  .drawer_title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
    line-height: 26px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .el-drawer__body {
    padding: 0px;
  }
}
</style>
