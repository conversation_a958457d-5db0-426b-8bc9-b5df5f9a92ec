<template>
  <el-drawer v-model="visible" title="商品详情" @close="visible = false" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%">
    <div v-loading="requestLoading">
      <el-descriptions :column="1" border class="descriptions descriptions-label-140">
        <el-descriptions-item>
          <template #label>商品编码</template>
          {{ dataForm.code }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>游戏名称</template>
          {{ dataForm.gameName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>系统区服</template>
          {{ dataForm.serverName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>商品标题</template>
          {{ dataForm.title }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>零售价</template>
          ￥{{ dataForm.price }}
        </el-descriptions-item>
        <el-descriptions-item v-for="(item, index) in dataForm.attributesList" :key="index">
          <template #label>{{ item.name }}</template>
          <span v-if="item.type == 3">{{ item.attributeText }}</span>
          <span v-else>{{ idQueryName(item.attributeIds, item.children) }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>游戏主图</template>
          <el-image style="height: 100px" v-if="dataForm.log" :src="dataForm.log" :preview-src-list="[dataForm.log]" preview-teleported fit="scale-down"/>
          <div class="noImg" v-else>
              <el-icon color="#a8abb2" size="24"><PictureFilled /></el-icon>
              <text>图片生成中</text>
            </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <el-tooltip effect="dark" content="点击下载图片">
            <div class="pointer flx-align-center" @click="downloadAll">详情图片 <el-icon class="ml-5 text-primary"><Download /></el-icon></div>
            </el-tooltip>
          </template>
          <div style="display: flex;flex-wrap: wrap; gap: 10px;">
            <el-image style="height: 100px" v-for="(item, index) in dataForm.imagesList" :src="item" :preview-src-list="dataForm.imagesList" :initial-index="index" preview-teleported fit="scale-down"/>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>商品描述</template>
          <div v-html="dataForm.info" v-if="/<[^>]+>/.test(dataForm.info)"></div>
          <div style="white-space: pre-wrap" v-else>{{ dataForm.info }}</div>
          <!-- <span style="white-space: pre-wrap">{{ dataForm.info }}</span> -->
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref } from "vue";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import arrowIcon from "@/assets/images/tagicon.png";
import baseService from "@/service/baseService";
import { Shop, Pointer, Back, Download } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import app from "@/constants/app";
import { getToken } from "@/utils/cache";
import qs from "qs";
const { t } = useI18n();

const visible = ref(false); // 对话框显隐
const dataForm = reactive({
  // 表单变量
  id: null,
  code: "",
  gameId: "",
  gameName: "",
  gameCode: "",
  highlights: "",
  title: "",
  price: 0,
  acquisitionPrice: 0,
  phone: "",
  gameAccount: "",
  gamePassword: "",
  log: [],
  imagesList: [],
  info: "",
  gameAre: "",
  server: "",
  serverName: "",
  compensation: "",
  bargain: "",
  topped: "",
  attributesList: <any>[]
});

// 表单初始化
const init = (row: any) => {
  visible.value = true;
  dataForm.id = row.shopId || row.id;
  dataForm.gameId = row.gameId;
  dataForm.code = row.code;
  dataForm.gameName = row.gameName;
  dataForm.serverName = row.serverName;
  dataForm.title = row.title1 || row.title;
  dataForm.price = row.price;
  dataForm.log = row.log;
  dataForm.imagesList = row.images ? JSON.parse(row.images) : [];
  dataForm.info = row.info;
  dataForm.attributesList = [];
  getAttribute();
};

// 加载详情
const requestLoading = ref(false); // 详情加载
const getInfo = () => {
  requestLoading.value = true;
  baseService
    .get("/shop/shop/" + dataForm.id)
    .then((res) => {
      const data = res.data;
      dataForm.imagesList = data.images ? JSON.parse(data.images) || [] : [];
      dataForm.info = data.info;
      dataForm.log = data.log;
      dataForm.serverName = data.serverName;

      // 处理属性回显
      data.attributesList.forEach((ele: any) => {
        dataForm.attributesList.forEach((item: any) => {
          if (item.typeId == ele.typeId) {
            if (item.type == 1 || item.type == 2) {
              item.attributeIds = ele.attributeIds;
            }
            if (item.type == 3) {
              item.attributeText = ele.attributeText;
            }
            Object.assign(ele, item);
          }
        });
      });
      // console.log(dataForm);
    })
    .finally(() => {
      requestLoading.value = false;
    });
};
// 获取游戏属性信息
const getAttribute = () => {
  requestLoading.value = true;
  baseService
    .get("/game/attribute/page", { gameId: dataForm.gameId })
    .then((res) => {
      dataForm.attributesList = [];
      res.data.map((item: any) => {
        dataForm.attributesList.push({
          typeId: item.id,
          attributeIds: [],
          attributeNames: "",
          attributeText: "",
          children: item.children,
          isTitle: item.isTitle,
          type: item.type,
          name: item.name
        });
      });
      getInfo();
    })
    .finally(() => {
      requestLoading.value = false;
    });
};

// 属性ID查询属性名称
const idQueryName = (value: any, data: any[]) => {
  const names: any = [];
  value.forEach((id: string) => {
    data.forEach((item: any) => {
      if (item.id == id) {
        names.push(item.name);
      }
    });
  });
  return names.join("，");
};

// 详情图下载
// const downLoading = ref(false);
const downloadAll = async () => {
  window.location.href = `${app.api}/shop/shop/exportImages/${dataForm.id}?${qs.stringify({token: getToken()})}`;
}

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.noImg{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 8px;
  text{
    color: #a8abb2;
    font-size: 12px;
  }
  
}
</style>