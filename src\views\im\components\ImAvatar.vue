<template>
    <div>
        <el-image class="avatar" v-if="user && user.headUrl" :src="user.headUrl">
            <template #error>
                <div class="image-slot">
                <!-- imRole 0 商城用户  1 客服  2商户 -->
                    <el-image class="avatar" :src="user.imRole == 0 ? settingStore.info.userDefaultAvatar : settingStore.info.backendDefaultAvatar"></el-image>
                </div>
            </template>
        </el-image>
        <el-image class="avatar" v-else-if="user && user.imRole == 0" :src="settingStore.info.userDefaultAvatar"></el-image>
        <el-image class="avatar" v-else-if="!user || user.imRole == 1 || user.imRole == 2 || !user.imRole" :src="settingStore.info.backendDefaultAvatar"></el-image>
        <div v-else class="avatar text-avatar"><span>{{ getFirstChar(user ? (user.nickname  || user.username) : '-') }}</span></div>
    </div>
</template>

<script lang="ts" setup>
    import { defineProps } from "vue";
    import { useSettingStore } from "@/store/setting";

    const settingStore = useSettingStore();
    const props = defineProps({
        user: Object
    })

    // 截取首字符
    const getFirstChar = (str: string) => {
        return str.charAt(0).toUpperCase();
    }

</script>

<style lang="scss" scoped>
    .text-avatar{
        background: #eee;
        color: #999;
        font-size: 20px;

        span{
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>