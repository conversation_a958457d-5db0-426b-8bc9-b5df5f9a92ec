<template>
  <!-- 卖家详情 -->
  <el-drawer v-model="visible" title="卖家详情" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%">
    <el-descriptions class="descriptions-label-140" :column="1" border v-if="dataType != '平台代售'">
      <el-descriptions-item label="姓名">{{ resData.realName }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ resData.mobile || "-" }}</el-descriptions-item>
      <el-descriptions-item label="员工ID">{{ resData.id }}</el-descriptions-item>
      <el-descriptions-item label="头像">
        <el-image :src="resData.headUrl" style="width: 96px; height: 96px"></el-image>
      </el-descriptions-item>
      <el-descriptions-item label="角色名称">
        <el-tag v-for="item in resData.roleName" :key="item" type="info">{{ item }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="游戏分配">{{ gameIdByName(resData.gameIdList) }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag v-if="resData.status == '1'" type="success">正常</el-tag>
        <el-tag v-else type="danger">禁用</el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions class="descriptions-label-140" :column="1" border v-else>
      <el-descriptions-item label="昵称">{{ resData.nickname }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ resData.realMobile || "-" }}</el-descriptions-item>
      <el-descriptions-item label="用户ID">{{ resData.id }}</el-descriptions-item>
      <el-descriptions-item label="用户账号">{{ resData.username }}</el-descriptions-item>
      <el-descriptions-item label="头像">
        <el-image :src="resData.avatar" style="width: 96px; height: 96px"></el-image>
      </el-descriptions-item>
      <el-descriptions-item label="微信">{{ resData.wechatName || "-" }}</el-descriptions-item>
      <el-descriptions-item label="邮箱">{{ resData.email || "-" }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ resData.sexual == 2 ? '女' : '男' }}</el-descriptions-item>
      <el-descriptions-item label="生日">{{ resData.birthday || "-" }}</el-descriptions-item>
      <el-descriptions-item label="身份证">{{ resData.idNo || "-" }}</el-descriptions-item>
      <el-descriptions-item label="实名状态">{{ resData.isRealname == 1 ? "已实名" : "未实名" }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag v-if="resData.state != '1'" type="success">正常</el-tag>
        <el-tag v-else type="danger">禁用</el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup lang="ts">
import baseService from "@/service/baseService";
import { ref, defineExpose } from "vue";

const visible = ref(false);
const dataType = ref();
const resData = ref({
  realName: "",
  mobile: "",
  id: "",
  headUrl: "",
  roleName: "",
  gameIdList: [],
  wechat: "",
  status: ""
});
const roleList = ref();
const init = (id: any, dataType_: any) => {
  visible.value = true;
  dataType.value = dataType_;
  if (dataType.value == "平台代售") {
    baseService.get("/tb/user/info", { id }).then((res) => {
      resData.value = res?.data;
    });
    return;
  }
  baseService.get("/sys/user/" + id).then((res) => {
    resData.value = res?.data;
    let nameList = [];
    baseService.get("/sys/role/list").then((res) => {
      roleList.value = res.data;
      resData.value.roleIdList.forEach((element: any) => {
        let obj = roleList.value.find((ele: any) => ele.id == element);
        obj ? nameList.push(obj.name) : "";
      });
      resData.value.roleName = nameList;
    });
  });
};
const gamesList = ref(<any>[]); // 游戏列表
const gameIdList = ref([]);
// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
  });
};
getGamesList();
// 根据游戏id获取名称
const gameIdByName = (idList: any) => {
  const nameList: any = [];
  gamesList.value.map((item: any) => {
    idList.map((ele: any) => {
      if (item.id == ele) {
        nameList.push(item.title);
      }
    });
  });
  return nameList.join("、");
};
defineExpose({
  init
});
</script>