<template>
  <div class="supplier">
    <div class="above">
      <div class="left">
        <span class="title">供货商统计(我帮卖)</span>
      </div>
      <div class="right">
        <div class="deadline">截止至 {{ commonData.currentDateTime() }}</div>
      </div>
    </div>
    <div class="stock_card" style="margin-top: 12px; display: flex">
      <div style="flex: 1">
        <div class="stock_top">
          <div class="stock_top_item">
            <img src="../../assets/images/shop_stat8.png" />
            <span>总销售额(元)</span>
          </div>
        </div>
        <div class="stock_button">
          <div class="stock_button_count">
            <div class="number">{{ formatCurrency(form.totalAmounts) }}</div>
          </div>
        </div>
      </div>
      <div style="flex: 1">
        <div class="stock_top">
          <div class="stock_top_item">
            <img src="../../assets/images/shop_stat10.png" />
            <span>销售数量(个)</span>
          </div>
        </div>
        <div class="stock_button">
          <div class="stock_button_count">
            <div class="number">{{ formatCurrency(form.totalNums) }}</div>
          </div>
        </div>
      </div>
      <div class="stock_top_item">
        <div class="but">
          <span
            @click="
              form.saleParams = item.value;
              handleData();
            "
            style="cursor: pointer"
            :class="{ active: form.saleParams == item.value }"
            :key="item.label"
            v-for="item in [
              { label: '本周', value: 'week' },
              { label: '本月', value: 'month' }
            ]"
            >{{ item.label }}</span
          >
        </div>
      </div>
    </div>
    <el-table :data="tableData" border style="width: 100%; margin-top: 12px">
      <el-table-column prop="rank" label="排名" align="center" width="60">
        <template #default="{ row, $index }">
          <img src="../../assets/images/ranking1.png" class="img" v-if="$index == 0 && form.page == 1" />
          <img src="../../assets/images/ranking2.png" class="img" v-else-if="$index == 1 && form.page == 1" />
          <img src="../../assets/images/ranking3.png" class="img" v-else-if="$index == 2 && form.page == 1" />
          <span v-else>{{ form.limit * (form.page - 1) + 1 + $index }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="acquisitionName" label="供货商名称" align="center" />
      <el-table-column prop="saleNums" label="销售数量" align="center" />
      <el-table-column prop="saleAmounts" label="销售额" align="center" />
      <el-table-column prop="saleRate" label="销售占比" align="center">
        <template #default="{ row }">
          <el-progress :percentage="row.saleRate" />
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <div class="flx-center">
      <el-pagination layout="prev, pager, next" v-model:current-page="form.page" :page-size="form.limit" hide-on-single-page @current-change="handleData" :total="form.totalPage" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import commonData from "./index.ts";
import { formatDate, formatCurrency } from "@/utils/method";
const form = reactive({
  totalAmounts: 0,
  totalNums: 0,
  totalPage: 0,
  page: 1,
  limit: 10,
  saleParams: "week"
});
const tableData = ref([]);
const handleData = async () => {
  let allData = await commonData.getSaleOtherData(form.saleParams, form.page);
  tableData.value = allData.pageData?.list || [];
  form.totalAmounts = allData.totalAmounts;
  form.totalNums = allData.totalNums;
  form.totalPage = allData.pageData?.total || 0;
};
onMounted(() => {
  handleData();
});
</script>

<style lang="less" scoped>
.supplier {
  background: #ffffff;
  border-radius: 8px;
  height: 648px;
  padding: 12px;
  margin-top: 12px;
}
.above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 8px;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "";
        width: 2px;
        height: 22px;
        background-color: var(--el-color-primary);
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    .deadline {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8px;
    }
    .toPage {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: var(--el-color-primary);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .el-icon {
      margin-left: 4px;
    }
  }
}
.stock_card {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ebeef5;
  padding: 16px 12px;
  .stock_top {
    display: flex;
    padding-bottom: 16px;
    align-items: center;
    justify-content: space-between;
    .stock_top_item {
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
      span {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .stock_button {
    padding: 4px 16px 12px 16px;
    display: flex;
    align-items: center;
    .stock_button_count {
      width: 40%;
      .number {
        font-weight: bold;
        font-size: 16px;
        color: #303133;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.but {
  display: flex;
  span {
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 12px;
  }
  .active {
    color: var(--el-color-primary);
  }
}
.img {
  height: 30px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
