<template>
  <el-drawer class="pb12 ny-drawer article-add-or-update" v-model="visible" :footer="null" title="订单详情" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="40%">
    <div style="padding: 12px; background: #fff; border-radius: 4px">
      <div class="p-title mt-0">基本信息</div>
      <el-descriptions class="descriptions-label-140" :column="2" border>
        <el-descriptions-item label="订单编号">{{ resData.sn || "-" }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ resData.state || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏名称">{{ resData.gameName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏区服">{{ resData.serverName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏密码">
          <div class="flx-justify-between" v-if="resData.gamePassword">
            <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
            <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
              <View v-if="!isShowGamePassword" />
              <Hide v-if="isShowGamePassword" />
            </el-icon>
          </div>
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="账号金额(元)"> <el-statistic value-style="font-weight: 400;font-size: 14px;color: #F44A29;" :value="resData.amount" :precision="2" /></el-descriptions-item>
        <el-descriptions-item label="包赔费(元)" :span="2"><el-statistic value-style="font-weight: 400;font-size: 14px;" :value="resData.guaranteePrice" :precision="2" /></el-descriptions-item>
        <el-descriptions-item label="成交价(元)" :span="2"><el-statistic value-style="font-weight: 400;font-size: 14px;" :value="resData.dealAmount" :precision="2" /></el-descriptions-item>
        <el-descriptions-item label="打款时间" :span="2">{{ resData.payTime || "-" }}</el-descriptions-item> 
        <el-descriptions-item label="卖方手机号" :span="2">{{ resData.customerPhone || "-" }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";

const emit = defineEmits(["refresh"]);

let resData = ref({});

const visible = ref(false);

const init = (obj: any) => {
  visible.value = true;
  resData.value = Object.assign({}, obj);
};

const dataLoading = ref(false);

// 是否显示游戏密码
const isShowGamePassword = ref(false);

defineExpose({
  init
});
</script>
<style lang="scss">
.pb12 {
  .el-drawer__header {
    padding-bottom: 12px !important;
  }
}
</style>
