<template>
  <div class="stat_card_finance">
    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 244, 254, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">销售订单收款待审核</span>
        </div>
        <div class="stat_top_time">
          <el-date-picker v-model="dataForm.date1" type="date" format="MM/DD" value-format="YYYY-MM-DD" style="width: 70px" :clearable="false" @change="getShopReport" />
        </div>
      </div>
      <div class="stat_middle">{{ topData.m1 || 0 }}</div>
      <div class="stat_below">
        <div>
          <span class="sum_label">待审核金额：</span>
          <span class="sum_value" style="color: #4165d7">{{ formatCurrency(topData.b1) || 0 }}</span>
        </div>
        <div style="color: #4165d7; display: flex; align-items: center; cursor: pointer" @click="toExamine">
          <span style="line-height: 15px">去审核</span><el-icon size="15"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(135deg, rgba(254, 241, 230, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">回收订单收款待审核</span>
        </div>
        <div class="stat_top_time">
          <el-date-picker v-model="dataForm.date2" type="date" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getShopReport" />
        </div>
      </div>
      <div class="stat_middle">{{ topData.m2 || 0 }}</div>
      <div class="stat_below flx-align-center">
        <div style="margin-right: 12px">
          <span class="sum_label">待审核金额：</span>
          <span class="sum_value" style="color: #ff9a2e">{{ formatCurrency(topData.b2) || 0 }}</span>
        </div>
        <div style="color: #ff9a2e; display: flex; align-items: center; cursor: pointer" @click="toExamine">
          <span style="line-height: 15px">去审核</span><el-icon size="15"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 238, 254, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between">
        <div class="stat_top_title flx-align-center">
          <span class="name">售后订单收到赔付待审核</span>
        </div>
        <div class="stat_top_time">
          <el-date-picker v-model="dataForm.date3" type="date" format="YYYY/MM" value-format="YYYY-MM" style="width: 85px" :clearable="false" @change="getShopReport" />
        </div>
      </div>
      <div class="stat_middle">{{ topData.m3 || 0 }}</div>
      <div class="stat_below">
        <div>
          <span class="sum_label">待审核金额：</span>
          <span class="sum_value" style="color: #165dff">{{ formatCurrency(topData.b3) || 0 }}</span>
        </div>
        <div style="color: #165dff; display: flex; align-items: center; cursor: pointer" @click="toExamine">
          <span style="line-height: 15px">去审核</span><el-icon size="15"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <div class="stat_item" style="background: linear-gradient(135deg, rgba(230, 254, 234, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #fff">
      <div class="stat_top flx-justify-between" style="padding: 12px">
        <div class="stat_top_title flx-align-center">
          <span class="name">今日审核通过</span>
        </div>
        <div class="stat_top_time">
          <el-date-picker v-model="dataForm.date4" type="date" format="MM/DD" value-format="YYYY-MM-DD" style="width: 70px" :clearable="false" @change="getShopReport" />
        </div>
      </div>
      <div class="stat_middle">{{ topData.m4 || 0 }}</div>
      <div class="stat_below">
        <div style="flex: 1">
          <span class="sum_label">商品售出审核：</span>
          <span class="sum_value" style="color: #00b42a">{{ topData.b41 || 0 }}</span>
        </div>
        <div style="flex: 1">
          <span class="sum_label">售后收到赔付：</span>
          <span class="sum_value" style="color: #00b42a">{{ topData.b42 || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="stat_item" style="background: linear-gradient(160deg, rgba(255, 226, 226, 0.84) 0%, rgba(217, 224, 247, 0.1) 50%), #ffffff">
      <div class="stat_top flx-justify-between">
        <div class="stat_top_title flx-align-center">
          <span class="name">今日审核拒绝</span>
        </div>
        <div class="stat_top_time">
          <el-date-picker v-model="dataForm.date5" type="date" format="MM/DD" value-format="YYYY-MM-DD" style="width: 70px" :clearable="false" @change="getShopReport" />
        </div>
      </div>
      <div class="stat_middle" style="color: #f53f3f">{{ topData.m5 || 0 }}</div>
      <div class="stat_below flx-align-center">
        <div style="flex: 1">
          <span class="sum_label">商品售出审核：</span>
          <span class="sum_value" style="color: #f53f3f">{{ topData.b51 || 0 }}</span>
        </div>
        <div style="flex: 1">
          <span class="sum_label">售后收到赔付：</span>
          <span class="sum_value" style="color: #f53f3f">{{ topData.b52 || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import dayjs from "dayjs";
const emit = defineEmits(["Examine"]);

const getAll = () => {
  getShopReport();
};

const dataForm = reactive({
  date1: dayjs().format("YYYY-MM-DD"),
  date2: dayjs().format("YYYY-MM"),
  date3: dayjs().format("YYYY-MM"),
  date4: dayjs().format("YYYY-MM-DD"),
  date5: dayjs().format("YYYY-MM-DD")
});
const topData = reactive({
  m1: "",
  b1: "",
  m2: "",
  b2: "",
  m3: "",
  b3: "",
  m4: "",
  b41: "",
  b42: "",
  m5: "",
  b51: "",
  b52: ""
});
const getShopReport = () => {
  // 销售订单收款待审核
  baseService.get("/flowable/financialaudit/payOutReviewReport",{ queryType: 0, type: 5, date: dataForm.date1, orderType: 8 }).then((res) => {
    topData.m1 = res.data.count;
    topData.b1 = res.data.totalAmount;
  });
  // 回收订单收款待审核
  baseService.get("/flowable/financialaudit/payOutReviewReport",{ queryType: 0, type: 5, date: dataForm.date2, orderType: 4 }).then((res) => {
    topData.m2 = res.data.count;
    topData.b2 = res.data.totalAmount;
  });
  // 售后订单收到赔付待审核
  baseService.get("/flowable/financialaudit/payOutReviewReport",{ queryType: 0, type: 5, date: dataForm.date3, orderType: 6 }).then((res) => {
    topData.m3 = res.data.count;
    topData.b3 = res.data.totalAmount;
  });
  // 今日审核通过
  baseService.get("/flowable/financialaudit/todayPayOutReviewReport",{ queryType: 1, type: 5, date: dataForm.date4 }).then((res) => {
    topData.m4 = res.data.find((item:any) => item.orderType == null).count;
    topData.b41 = res.data.find((item:any) => item.orderType == 8).count;
    topData.b42 = res.data.find((item:any) => item.orderType == 6).count;
  });
  // 今日审核拒绝
  baseService.get("/flowable/financialaudit/todayPayOutReviewReport",{ queryType: 2, type: 5, date: dataForm.date5 }).then((res) => {
    topData.m5 = res.data.find((item:any) => item.orderType == null).count;
    topData.b51 = res.data.find((item:any) => item.orderType == 8).count;
    topData.b52 = res.data.find((item:any) => item.orderType == 6).count;
  });
};

// 金额格式化
const formatCurrency = (number: number) => {
  if (isNaN(number) || number === null) return;
  const numStr = number.toString();
  const decimalIndex = numStr.indexOf(".");
  const integerNum = decimalIndex >= 0 ? numStr.substring(0, decimalIndex) : numStr;
  const integerStr = integerNum.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return decimalIndex >= 0 ? integerStr + numStr.substring(decimalIndex) : integerStr;
};

// 去审核
const toExamine = () =>{
  emit('Examine');
}

onMounted(()=>{
  getShopReport();
})

defineExpose({
  getAll
});
</script>

<style lang="less" scoped>
.stat_card_finance {
  display: flex;
  align-content: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;

  .stat_item {
    // width: 19.5%;
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    .stat_top {
      padding: 12px;

      .stat_top_title {
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        .name {
          font-weight: 500;
          font-size: 14px;
          color: #303133;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .stat_top_time {
        :deep(.el-text) {
          cursor: pointer;
        }
        :deep(.ny_dropdown_menu) {
          padding: 0;
        }
        :deep(.clickValue),
        :deep(.placeholder) {
          line-height: normal;
          cursor: pointer;
        }

        :deep(.el-date-editor) {
          line-height: 20px;
          height: 20px;
          .el-input__prefix {
            position: absolute;
            right: 4px;

            .el-input__prefix-inner {
              color: #303133;

              .el-input__icon {
                margin-right: 0;
              }
            }
          }
          .el-input__wrapper {
            box-shadow: none;
            background-color: transparent;

            .el-input__inner {
              cursor: pointer;
            }
          }
        }
      }
    }

    .stat_middle {
      padding: 4px 16px;
      font-weight: bold;
      font-size: 20px;
      color: #303133;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .stat_below {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 16px 12px 16px;

      .sum_label {
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .sum_value {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
