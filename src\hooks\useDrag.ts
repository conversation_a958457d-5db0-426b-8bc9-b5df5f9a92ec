// 可拖拽组件
import { localGet, localSet } from "@/utils/cache";
const moveObject = {
  isMove: false,
  useMove: (el: any, defaultOffset?: any, fullScreen?: Boolean) => {
    el.style.position = "fixed";
    if (fullScreen) {
      el.style.left = defaultOffset.left + "px";
    } else {
      el.style.right = "0px";
    }
    el.style.top = defaultOffset.top + "px";
    let offsetX: number, offsetY: number, oL: number, oT: number, oLeft: number, oTop: number;

    if (el != null) {
      el.addEventListener("mousedown", function (event: any) {
        console.log("down");

        if (event.button == 0 && el != null) {
          const lexObj: any = getComputedStyle(el);
          offsetX = event.pageX - el.offsetLeft + parseInt(lexObj["margin-left"]);
          offsetY = event.pageY - el.offsetTop + parseInt(lexObj["margin-right"]);
          const move = function (event: any) {
            console.log("move");
            moveObject.isMove = true;
            if (el != null) {
              let x = event.pageX - offsetX;
              let y = event.pageY - offsetY;
              if (x < 0) {
                x = 0;
              } else if (x > document.documentElement.clientWidth - el.offsetWidth) {
                x = document.documentElement.clientWidth - el.offsetWidth;
              }
              x = document.documentElement.clientWidth - el.offsetWidth;
              if (y < 0) {
                y = 0;
              } else if (y > document.documentElement.clientHeight - el.offsetHeight) {
                y = document.documentElement.clientHeight - el.offsetHeight;
              }
              if (fullScreen) {
                el.style.left = x + "px";
              }
              el.style.top = y + "px";
              localSet("offsetX&Y", { left: x, top: y });
            }
          };
          const stop = function () {
            console.log("stop");

            document.removeEventListener("mousemove", move);
            document.removeEventListener("mouseup", stop);
            setTimeout(() => {
              moveObject.isMove = false;
            }, 100);
          };
          document.addEventListener("mousemove", move);
          document.addEventListener("mouseup", stop);
        }
      });
    }
  }
};

export default moveObject;
