<template>
    <operation-log 
        ref="operationRef" 
        :show="show" 
        @close="show=false" 
        title="订单入库确认" 
        confirmText="确认入库"
        type="primary"
        :loading="btnLoading"
        @confirm="confirm">   
        <template #default>
            <div class="text">
                <p>您即将对以下订单执行入库操作</p>
                <ul>
                    <li><b>订单编号：</b><el-text type="primary">{{ orderInfo.sn }}</el-text></li>
                    <li><b>游戏名称：</b>{{ orderInfo.gameName }}</li>
                    <li><b>游戏账号：</b><el-text type="primary">{{ orderInfo.gameAccount }}</el-text></li>
                </ul>
                <p>请仔细核对上述信息，并确保商品信息正确无误。入库操作将更新库存数量，并影响后续的库存管理和财务核算。</p>
            </div>
        </template>
    </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import OperationLog from "../../components/SecondaryConfirmation.vue";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);

const show = ref(false);
const orderInfo = ref(<any>{});
const init = (row: any) => {
    show.value = true;
    orderInfo.value = row;
}

// 确认
const btnLoading = ref(false);
const confirm = () => {
    btnLoading.value = true;
    baseService.get('/purchase/inbound/' + orderInfo.value.orderId).then(res =>{
        if(res.code == 0){
            ElMessage.success("入库成功");
            show.value = false;
            emits('refresh');
        }
    }).finally(() => {
        btnLoading.value = false;
    })
}

defineExpose({
    init
})

</script>