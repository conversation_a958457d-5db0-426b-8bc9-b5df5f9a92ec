<template>
    <RenderTableColumn v-bind="column" />
</template>

<script setup lang="tsx" name="TableColumn">
import { ref, useSlots, defineProps } from "vue";


import { ColumnProps, RenderScope, HeaderRenderScope, FieldNamesProps } from "@/components/ny-table/interface";
import { isArray } from "@/utils/is";

const props = defineProps<{ column: ColumnProps }>();

const slots = useSlots();

// 渲染表格数据
const renderCellData = (item: ColumnProps, scope: RenderScope<any>) => {
    return formatValue(handleRowAccordingToProp(scope.row, item.prop!));
};

const enumMap = ref(new Map<string, { [key: string]: any }[]>());

// 获取 tag 类型
const getTagType = (item: ColumnProps, scope: RenderScope<any>) => {
    return (
        filterEnum(handleRowAccordingToProp(scope.row, item.prop!), enumMap.value.get(item.prop), item.fieldNames, "tag") || "primary"
    );
};


/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {Array} fieldNames label && value && children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 * */
function filterEnum(callValue: any, enumData?: any, fieldNames?: FieldNamesProps, type?: "tag") {
    const value = fieldNames?.value ?? "value";
    const label = fieldNames?.label ?? "label";
    const children = fieldNames?.children ?? "children";
    let filterData: { [key: string]: any } = {};
    // 判断 enumData 是否为数组
    if (Array.isArray(enumData)) filterData = findItemNested(enumData, callValue, value, children);
    // 判断是否输出的结果为 tag 类型
    if (type == "tag") {
        return filterData?.tagType ? filterData.tagType : "";
    } else {
        return filterData ? filterData[label] : "-";
    }
}

// 递归查找 callValue 对应的 enum 值
function findItemNested(enumData: any, callValue: any, value: string, children: string) {
    return enumData.reduce((accumulator: any, current: any) => {
        if (accumulator) return accumulator;
        if (current[value] === callValue) return current;
        if (current[children]) return findItemNested(current[children], callValue, value, children);
    }, null);
}

/**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */

function formatValue(callValue: any) {
    // 如果当前值为数组，使用 / 拼接（根据需求自定义）
    if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "-";
    return callValue ?? "-";
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
function handleProp(prop: string) {
    const propArr = prop.split(".");
    if (propArr.length == 1) return prop;
    return propArr[propArr.length - 1];
}

/**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
    if(!prop) return row;
    if (!prop.includes(".")) return row[prop] ?? "-";
    prop.split(".").forEach(item => (row = row[item] ?? "-"));
    return row;
}

const RenderTableColumn = (item: ColumnProps) => {
    return (
        <>
            {item.isShow && (
                <el-table-column
                    {...item}
                    align={item.align ?? "center"}
                    showOverflowTooltip={item.showOverflowTooltip ?? item.prop !== "operation"}
                >
                    {{
                        default: (scope: RenderScope<any>) => {
                            if (item._children) return item._children.map(child => RenderTableColumn(child));
                            if (item.render) return item.render(scope);
                            if (item.prop && slots[handleProp(item.prop)]) return slots[handleProp(item.prop)]!(scope);
                            if (item.tag) return <el-tag type={getTagType(item, scope)}>{renderCellData(item, scope)}</el-tag>;
                            return renderCellData(item, scope);
                        },
                        header: (scope: HeaderRenderScope<any>) => {
                            if (item.headerRender) return item.headerRender(scope);
                            if (item.prop && slots[`${handleProp(item.prop)}Header`]) return slots[`${handleProp(item.prop)}Header`]!(scope);
                            return item.label;
                        }
                    }}
                </el-table-column>
            )}
        </>
    );
};
</script>