<template>
  <div class="container">
    <ny-table cellHeight="ch-40" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header>
        <ny-button-group label="name" value="value" :list="statusList" v-model="state.dataForm.status" @change="state.getDataList"></ny-button-group>
      </template>

      <template #header-right>
        <ny-select style="width: 200px" v-model="state.dataForm.type" dict-type="banner_type" placeholder="请选择分组" @change="state.getDataList()"></ny-select>

        <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
        <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
      </template>

      <template #header-custom>
        <div class="mb-12">
          <el-button v-if="state.hasPermission('basics:slideshow:save')" type="primary" @click="addOrUpdateHandle()">新增轮播图</el-button>
          <el-button v-if="state.hasPermission('basics:slideshow:delete')" type="danger" :disabled="!state.dataListSelections || !state.dataListSelections.length" @click="state.deleteHandle()">删除</el-button>
        </div>
      </template>

      <!-- 分组 -->
      <template #type="{ row }">
        {{ state.getDictLabel("banner_type", row.type) }}
      </template>

      <!-- 图片 -->
      <template #image="{ row }">
        <el-image style="width: 54px; height: 30px" :src="row.image" :preview-src-list="[row.image]" :preview-teleported="true" fit="cover" />
      </template>

      <!-- 排序 -->
      <template #sort="{ row }">
        <div class="flx-center sort-wrap">
          {{ row.sort }}
          <el-popover placement="top" :width="150" trigger="click" :visible="row.visible">
            <div class="sort-input">
              <el-input v-model="row.sort" type="number" size="small" placeholder="请输入排序"></el-input>
              <div class="btns flx-justify-between pt-8">
                <el-button size="small" :loading="row.loading" @click="row.visible = false">取消</el-button>
                <el-button size="small" :loading="row.loading" type="primary" @click="updateSort(row)">确定</el-button>
              </div>
            </div>
            <template #reference>
              <el-icon class="edit" @click="row.visible = true"><Edit /></el-icon>
            </template>
          </el-popover>
        </div>
      </template>

      <!-- 状态 0未启用1启用 -->
      <template #status="{ row }">
        <el-switch v-if="state.hasPermission('basics:slideshow:update')" v-model="row.status" active-value="1" inactive-value="0" active-text="启用" inactive-text="关闭" :loading="row.loading" @change="(e: any) => {updateStatus(e, row)}"></el-switch>
      </template>

      <template #operation="{ row }">
        <el-button v-if="state.hasPermission('basics:slideshow:update')" type="primary" text bg @click="addOrUpdateHandle(row.id)">
          {{ $t("update") }}
        </el-button>
        <el-button v-if="state.hasPermission('basics:slideshow:delete')" type="danger" text bg @click="state.deleteHandle(row.id)">
          {{ $t("delete") }}
        </el-button>
      </template>
    </ny-table>

    <!-- 新增/编辑 -->
    <banner-add-or-update ref="addOrUpdateRef" :key="addOrUpdateKey" @refresh="state.getDataList"></banner-add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import BannerAddOrUpdate from "./banner-add-or-update.vue";
import { ElMessage } from "element-plus";
import { Edit } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 状态列表
const statusList = ref([
  { name: "全部", value: "" },
  { name: "启用", value: "1" },
  { name: "关闭", value: "0" }
]);

// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "type",
    label: "分组",
    minWidth: 200
  },
  {
    prop: "image",
    label: "图片",
    minWidth: 100
  },
  {
    prop: "sort",
    label: "排序",
    minWidth: 200
  },
  {
    prop: "title",
    label: "绑定文章",
    minWidth: 410
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 200
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 140
  }
]);

const view = reactive({
  getDataListURL: "/basics/slideshow/page",
  getDataListIsPage: true,
  deleteURL: "/basics/slideshow",
  deleteIsBatch: true,
  dataForm: {
    status: "",
    type: "",
    order: "asc",
    orderField: "sort"
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 重置操作
const getResetting = () => {
  state.dataForm.status = "";
  state.dataForm.type = "";
  state.getDataList();
};

// 新增  编辑
const addOrUpdateRef = ref();
const addOrUpdateKey = ref(0);
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// 修改状态
const updateStatus = (status: any, row: any) => {
  if (!row.id) return;
  row.status = status;
  row.loading = true;
  baseService
    .put("/basics/slideshow", row)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.getDataList();
    })
    .finally(() => {
      row.loading = false;
    });
};

// 修改 排序
const updateSort = (row: any) => {
  if (!row.id) return;
  row.loading = true;
  baseService
    .put("/basics/slideshow", row)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.getDataList();
      row.visible = false;
    })
    .finally(() => {
      row.loading = false;
    });
};
</script>

<style lang="scss" scoped>
.sort-wrap {
  // position: relative;
  &:hover .edit {
    opacity: 1;
  }
  .edit {
    color: var(--el-color-primary);
    opacity: 0;
    cursor: pointer;
  }
}
</style>
