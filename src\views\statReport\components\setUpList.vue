<template>
  <div style="margin-top: 10px">
    <el-button color="#73767A" :icon="RefreshRight" @click="getList()"></el-button>
    <el-button type="primary" @click="setUpListAddOrUpdateHandle()">新增列</el-button>
  </div>
  <el-table :data="tableData" style="width: 100%; margin-top: 10px" border class="elTable">
    <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="80" />
    <el-table-column prop="name" label="名称" align="center" />
    <el-table-column prop="type" label="类型" align="center">
      <template #default="{ row }">
        <span v-if="row.type == 1">文本</span>
        <span v-if="row.type == 2">数值</span>
        <span v-if="row.type == 3">下拉引用</span>
        <span v-if="row.type == 4">日期</span>
        <span v-if="row.type == 5">图片上传</span>
      </template>
    </el-table-column>
    <el-table-column prop="isMust" label="必填" align="center">
      <template #default="srow">
        <div v-if="srow.row.isMust">是</div>
        <div v-else>否</div>
      </template>
    </el-table-column>
    <el-table-column prop="isSum" label="聚合" align="center">
      <template #default="srow">
        <div v-if="srow.row.isSum">开启</div>
        <div v-else>关闭</div>
      </template>
    </el-table-column>
    <el-table-column prop="isSelect" label="筛选" align="center">
      <template #default="srow">
        <div v-if="srow.row.isSelect">是</div>
        <div v-else>否</div>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button link type="primary" size="small" @click="setUpListAddOrUpdateHandle(row.id)">编辑</el-button>
        <el-popconfirm width="200" confirm-button-text="确定" cancel-button-text="取消" title="您确定删除该目录吗？" @confirm="setUpDelete(row.id)">
          <template #reference>
            <el-button link type="danger" size="small">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="排序" width="80" align="center">
      <template #default>
        <el-button bg text class="move">
          <el-icon> <Rank /></el-icon>
        </el-button>
      </template>
    </el-table-column>
    <!-- 空状态 -->
    <template #empty>
      <div style="padding: 68px 0">
        <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
      </div>
    </template>
  </el-table>
  <el-pagination :current-page="requestParams.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="requestParams.limit" :total="total" layout="total, sizes, prev, pager, next, jumper" :hide-on-single-page="true" @size-change="sizeChange" @current-change="currentChange"></el-pagination>
  <!-- 新增列 -->
  <setUpListAddOrUpdate ref="setUpListAddOrUpdateRef" @refreshDataList="getList"></setUpListAddOrUpdate>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, watch } from "vue";
import { RefreshRight } from "@element-plus/icons-vue";
import setUpListAddOrUpdate from "./setUpList-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import Sortable from "sortablejs";

interface Props {
  menuInfo: any;
}
const props = withDefaults(defineProps<Props>(), {
  menuInfo: ""
});

// 请求参数
const requestParams = {
  page: 1,
  limit: 10,
  id: "",
  indexName: ""
};
const total = ref(0);

// 序号
const indexMethod = (index: number) => {
  return index + 1;
};

const tableData = ref([]);

// 表格拖拽排序
const dragSort = () => {
  const tbody = document.querySelector(`.elTable .el-table__body-wrapper tbody`) as HTMLElement;
  Sortable.create(tbody, {
    handle: ".move",
    animation: 300,
    onEnd({ newIndex, oldIndex }) {
      baseService
        .get("/report/reportcolumn/sort", {
          targetId: tableData.value[oldIndex].id,
          sourceId: tableData.value[newIndex].id
        })
        .then((res) => {
          if (res.code == 0) {
            getList();
          }
        });
    }
  });
};

watch(
  () => props.menuInfo.id,
  (newValue) => {
    if (newValue) {
      requestParams.id = newValue;
      requestParams.indexName = props.menuInfo.indexName;
      console.log(requestParams.id, "===== requestParams.id =====");
      nextTick(() => {
        getList();
      });
    }
  },
  {
    immediate: true
  }
);

const sizeChange = (number: any) => {
  requestParams.page = number;
  getList();
};

const currentChange = (number: any) => {
  requestParams.page = number;
  getList();
};

// 请求列表数据
const getList = () => {
  tableData.value = [];
  baseService.get("/report/reportcolumn/page", requestParams).then((res) => {
    tableData.value = res.data.list;
    total.value = res.data.total;
  });
};

// 删除
const setUpDelete = (id: any) => {
  baseService.delete("/report/reportcolumn", [id]).then((res) => {
    if (res.code == 0) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};

onMounted(() => {
  dragSort();
});

const addKey = ref(0);
const setUpListAddOrUpdateRef = ref();
const setUpListAddOrUpdateHandle = (id?: any) => {
  addKey.value++;
  nextTick(() => {
    setUpListAddOrUpdateRef.value.init(props.menuInfo.indexName, id);
  });
};
</script>

<style lang="less" scoped></style>
