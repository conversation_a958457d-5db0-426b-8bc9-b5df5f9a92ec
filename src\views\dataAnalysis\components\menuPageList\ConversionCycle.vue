<template>
    <div class="business_page">
        <div class="business_header flx-align-center">
            <el-button type="info" :loading="exportclickLoading" @click="exportclick">导出数据</el-button>
            <el-radio-group v-model="dataForm.type" style="margin-left: 24px;" @change="radioChange">
                <el-radio value="1">游戏</el-radio>
                <el-radio value="2">回收员工</el-radio>
                <!-- <el-radio value="3">回收渠道</el-radio> -->
                <el-radio value="4">回收价位</el-radio>
            </el-radio-group>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">转化周期柱状图</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="legend">
                        <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData"
                            :key="index" @change="changeShow"></el-checkbox>
                    </div>
                    <el-date-picker v-model="conversionTime" type="daterange" start-placeholder="开始时间"
                        end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        style="width: 220px; border-radius: 20px" @change="getConversionCycle"/>
                </div>
            </div>
            <div class="header_describe">销售订单数据</div>
            <div class="charts">
                <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
            </div>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">转化周期明细表</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <el-date-picker v-model="detailTime" type="daterange" start-placeholder="开始时间"
                        end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        style="width: 220px; border-radius: 20px" @change="getConversionCycleDetail"/>
                </div>
            </div>
            <div style="padding: 0px 20px 10px 20px;">
                <el-table :data="paginatedData" style="width: 100%;" cell-class-name="ch-56"
                    border class="business_table" @sort-change="sortChange">
                    <template v-for="item in columns" :key="item">
                        <el-table-column :prop="item.prop" :label="item.label" :sortable="item.prop != 'name' ? 'custom' : false"
                            :min-width="item.minWidth" align="center">
                            <template #default="{ row, $index }">
                                {{ row[item.prop] == null ? '-' : row[item.prop] }}
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 空状态 -->
                    <template #empty>
                        <div style="padding: 68px 0">
                            <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
                        </div>
                    </template>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pagination.size" :total="total" layout="total, sizes, prev, pager, next, jumper"
                    :hide-on-single-page="true" @size-change="sizeChange"
                    @current-change="currentChange"></el-pagination>
            </div>

        </div>
    </div>
</template>

<script lang='ts' setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, computed, onMounted } from "vue";
import * as echarts from "echarts";
import { fileExport } from "@/utils/utils";
import { formatDate } from "@/utils/method";
import baseService from "@/service/baseService";
import { usePagination, useSortList } from "@/views/dataAnalysis/pagination"

const tableData = ref(<any>[]);

// 分页
const pagination = ref({
    page: 1,
    size: 10,
})
const total = ref();

// 计算分页数据
const paginatedData = computed(() =>
    usePagination({
        currentPage: pagination.value.page,
        pageSize: pagination.value.size,
        data: tableData.value
    })
);

// 表格配置项
const columns = reactive([
    {
        prop: "name",
        label: "游戏",
        minWidth: 112
    },
    {
        prop: "averageConversionCycle",
        label: "平均转化周期(天)",
        minWidth: 80
    },
    {
        prop: "shortestConversionCycle",
        label: "最短转化周期(天)",
        minWidth: 80
    },
    {
        prop: "salesOrderCount",
        label: "销售订单数",
        minWidth: 100
    },
]);

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    salesChannelId: "",
    employeeId: "",
    purchaseEmployeeId: "",
    saleEmployeeId: "",
    type: "1"
});

// 图例数据
const legendData = ref([
    { name: '平均转化周期', color: '#F77234', show: true },
    { name: '最短转化周期', color: '#0FC6C2', show: true },
    { name: '销售订单数', color: '#F7BA1E', show: true }
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '平均转化周期',
        type: 'bar',
        itemStyle: {
            color: "#F77234"
        },
        barMaxWidth: 30,
        data: [],
    },
    {
        name: '最短转化周期',
        type: 'bar',
        itemStyle: {
            color: "#0FC6C2"
        },
        barMaxWidth: 30,
        data: []
    },
    {
        name: '销售订单数',
        type: 'line',
        stack: 'Total',
        smooth: true,
        itemStyle: {
            color: "#F7BA1E"
        },
        lineStyle: {
            width: 3,
            color: "#F7BA1E"
        },
        showSymbol: false,
        emphasis: {
            focus: 'series'
        },
        data: []
    }
])

// 游戏库存折线图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '3%',
            top: '4%',
            bottom: '14%',
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 40,
            bottom: "2%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 6
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 9
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

const radioChange = () => {
    getConversionCycle();
    getConversionCycleDetail();
}

// 转化周期柱状图 - 请求
const conversionTime = ref();
const getConversionCycle = () => {
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/conversionCycle", {
        ...dataForm.value,
        startTime: conversionTime.value ? conversionTime.value[0] + " 00:00:00" : "",
        endTime: conversionTime.value ? conversionTime.value[1] + " 23:59:59" : ""
    }).then(res => {
        optionX.value = res.data.x;
        seriesList.value.map((i) => {
            res.data.y.map((j) => {
                if (i.name == j.name) {
                    i.data = j.data;
                }
            });
        });
        GameSalesStatisticsChart(seriesList.value);
    })
}

// 转化周期明细表
const detailTime = ref();
const getConversionCycleDetail = () =>{
    baseService.post("/dataAnalysis/conversionCycleDetail",{
        ...dataForm.value,
        startTime : detailTime.value ? detailTime.value[0] + " 00:00:00" : "",
        endTime : detailTime.value ? detailTime.value[1] + " 23:59:59" : ""
    }).then(res=>{
        tableData.value = res.data;
        total.value = tableData.value.length;
    })
}

const exportclickLoading = ref(false);
const exportclick = () => {
    exportclickLoading.value = true;
    baseService.get("/dataAnalysis/conversionCycleDetailExport", dataForm.value).then(res => {
        if (res) {
            fileExport(res, "转化周期明细表");
        }
    }).finally(()=>{
        exportclickLoading.value = false;
    })
}

// 分页方法
const sizeChange = (number: any) => {
    pagination.value.size = number;
    pagination.value.page = 1;
};

const currentChange = (number: any) => {
    pagination.value.page = number;
};

// 排序事件
const sortChange = (column: any) => {
    if(column.order != null){
        tableData.value = useSortList({
            prop: column.prop,
            order: column.order,
            data: tableData.value
        })
    }
};

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    conversionTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    detailTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getConversionCycle();
    getConversionCycleDetail();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})


const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getConversionCycle();
    getConversionCycleDetail();
}

defineExpose({
    init
});

</script>

<style lang='less' scoped>
.business_page {
    margin-top: 12px;
}

.business_header {}

.business_center {}

.business_table {
    :deep(th .cell) {
        background: none !important;
    }

    :deep(th:nth-child(n+2):nth-child(-n+2)) {
        background-color: rgba(247, 114, 52, 0.1) !important;

        .cell {
            color: #F77234;
        }
    }

    :deep(th:nth-child(n+3):nth-child(-n+3)) {
        background-color: rgba(15, 198, 194, 0.1) !important;

        .cell {
            color: #0FC6C2;
        }
    }

    :deep(th:nth-child(n+4):nth-child(-n+4)) {
        background-color: rgba(247, 186, 30, 0.1) !important;

        .cell {
            color: #F7BA1E;
        }
    }

    :deep(td:nth-child(n+2):nth-child(-n+3)) {
        background-color: rgba(247, 114, 52, 0.1) !important;
    }

    :deep(td:nth-child(n+3):nth-child(-n+4)) {
        background-color: rgba(15, 198, 194, 0.1) !important;
    }

    :deep(td:nth-child(n+4):nth-child(-n+5)) {
        background-color: rgba(247, 186, 30, 0.1) !important;
    }
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F77234;
                        border-color: #F77234;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #0FC6C2;
                        border-color: #0FC6C2;
                    }
                }

                .el-checkbox:nth-child(3) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F7BA1E;
                        border-color: #F7BA1E;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
</style>