//定义基础色

//主色

body {
  --color-primary: #4165d7;
  --color-primary-light: rgb(64 158 255 / 8%);
  --el-text-color-regular: #303133;
}

@--color-primary: ~ 'var(--color-primary)';
@--color-primary-light: ~ 'var(--color-primary-light)';

@text: #595959;
@text-2: #606266;

//导航菜单
@dark-text: #fff;
@dark-text-active: #fff;
@dark-bg: #2f3447;
@dark-bg-active: @--color-primary;

@light-text: @text;
@light-text-active: @--color-primary;
@light-bg: #fff;
@light-bg-active: @--color-primary-light;

@primary-text: rgb(255 255 255 / 66%);
@primary-text-2: rgb(255 255 255 / 65%);
@primary-text-active: #fff;
@primary-bg: @--color-primary;
@primary-bg-light: @--color-primary-light;
@primary-bg-active: @--color-primary-light;

// 字体
@font-face {
  font-family: 'MyCustomFont';
  src: url('../fonts/MyCustomFont.woff2') format('woff2');
  font-display: swap;
}
@font-face {
  font-family: 'ZenDots-Regular';
  src: url('../fonts/ZenDots-Regular.ttf') format('truetype');
  font-display: swap;
}