<template>
  <div style="width: 100%">
    <div class="flx" style="justify-content: space-between">
      <div class="title">短信发送记录</div>
    </div>
    <div class="TableXScrollSty">
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header-right>
          <div style="display: flex; gap: 8px; height: 32px">
            <el-input :prefix-icon="Search" style="width: 270px !important; height: 32px" v-model="state.dataForm.info" placeholder="请输入发送手机号/目标手机号/内容" clearable></el-input>
            <!-- <el-date-picker style="width: 240px" v-model="createDate" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="createDateChange" /> -->
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            <el-button @click="filterHandle">高级筛选</el-button>
          </div>
        </template>
        <template #status="{ row }">
          <el-tag type="danger" v-if="row.status == 0">未发送</el-tag>
          <el-tag type="success" v-if="row.status == 1">发送成功</el-tag>
          <el-tag type="warning" v-if="row.status == 2">设备不在线</el-tag>
          <el-tag type="primary" v-if="row.status == 3">设备已确认</el-tag>
        </template>
        <template #createDate="{ row }">
          <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
        </template>
      </ny-table>
    </div>

    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import filterCom from "./filter.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
const view = reactive({
  getDataListURL: "/mobile/mobileSendMessage/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileSendMessage",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  // {
  //   prop: "id",
  //   label: "ID",
  //   minWidth: 80
  // },
  {
    prop: "sendPhone",
    label: "发送手机号",
    minWidth: 140
  },
  {
    prop: "receivePhone",
    label: "目标手机号",
    minWidth: 140
  },
  {
    prop: "type",
    label: "目标手机号类型",
    minWidth: 160
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 100
  },
  {
    prop: "content",
    label: "内容",
    minWidth: 140
  },
  {
    prop: "deptName",
    label: "部门",
    minWidth: 80
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 140
  },
  {
    prop: "operator",
    label: "操作人",
    minWidth: 120
  }
  // {
  //   prop: "remark",
  //   label: "备注",
  //   minWidth: 140
  // }
]);

// 时间区间筛选
const createDate = ref([]);
const createDateChange = () => {
  state.dataForm.start = createDate.value && createDate.value.length ? createDate.value[0] : "";
  state.dataForm.end = createDate.value && createDate.value.length ? createDate.value[1] : "";
};
// 重置操作
const getResetting = () => {
  state.dataForm = { info: "" };
  createDate.value = [];
  filterRef.value.reset();
  state.page = 1;
  state.getDataList();
};

const getParamsInfo = (params: any) => {
  delete params.createDate;
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(5);
  });
};
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 32px;
}

.el-button {
  margin-left: 0px;
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 503px;
}
</style>
