<template>
  <operation-log ref="operationRef" :show="show" @close="show = false" title="收款确认" confirmText="提交" type="primary" @confirm="confirm">
    <template #default>
      <el-form :model="dataForm" label-position="top" :rules="rules" ref="formRef">
        <el-descriptions class="descriptions descriptions-label-140" :column="1" size="default" border>
          <template v-if="!sourceFromShopList">
            <el-descriptions-item>
              <template #label>
                <div>收款金额(元)<span style="color: red">*</span></div>
              </template>
              <el-form-item label="收款金额(元)" prop="dealAmount" :key="1">
                <el-input type="number" v-model="dataForm.dealAmount" placeholder="请输入收款金额"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div>支付宝订单号<span style="color: red">*</span></div>
              </template>
              <el-form-item label="" prop="alipayNo" :key="2">
                <!-- <template #label>
                  <span style="margin-right: 10px">支付宝订单号</span>
                  <el-text type="primary" class="pointer" @click="showImagePreview = true">如何查看订单号？</el-text>
                </template> -->
                <el-input v-model="dataForm.alipayNo" placeholder="请输入支付宝订单号"></el-input>
              </el-form-item>
            </el-descriptions-item>
          </template>
          <template v-else>
            <el-descriptions-item>
              <template #label>
                <div>游戏名称</div>
              </template>
              {{ dataForm.gameName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div>商品编码</div>
              </template>
              {{ dataForm.shopCode }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div>成交价</div>
              </template>
              {{ dataForm.saleAmount }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div>付款方式<span style="color: red">*</span></div>
              </template>
              <el-form-item label="支付宝订单号" prop="payType" :key="3">
                <el-select v-model="dataForm.payType" clearable placeholder="请选择支付方式">
                  <el-option v-for="(item, index) in ['支付宝转账', '微信', '银行卡']" :label="item" :value="index + 1" :key="index"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item v-if="dataForm.payType == 1">
              <template #label><span>支付宝订单号<span style="color: red">*</span></span></template>
              <el-form-item label="支付宝订单号" prop="alipayOrderNo" :key="4">
                <el-input v-model="dataForm.alipayOrderNo" placeholder="请输入支付宝订单号" ></el-input>
              </el-form-item>
            </el-descriptions-item>
          </template>

          <el-descriptions-item>
            <template #label>
              <div>打款凭证<span style="color: red">*</span></div>
            </template>
            <el-form-item label="打款凭证" prop="payCredentials" :key="5">
              <ny-upload v-model:imageUrl="dataForm.payCredentials" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <el-image-viewer v-if="showImagePreview" :url-list="['https://www.nyyyds.com:9443/pic/bill.png']" hide-on-click-modal teleported @close="showImagePreview = false" />
    </template>
    <template #footer>
      <el-button :loading="btnLoading" @click="show = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="confirm">提交</el-button>
    </template>
  </operation-log>
</template>

<script lang="ts" setup>
import { ref, defineExpose, reactive, defineEmits } from "vue";
import OperationLog from "../../components/SecondaryConfirmation.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emits = defineEmits(["refresh"]);
const show = ref(false);
const sourceFromShopList = ref(false); // 商品列表售出

const init = (row: any, shop = false) => {
  dataForm.value = JSON.parse(JSON.stringify(row));
  show.value = true;
  sourceFromShopList.value = shop;
};

const showImagePreview = ref(false);

const dataForm = ref(<any>{
  dealAmount: "",
  alipayNo: "",
  payCredentials: "",
});

const rules = reactive({
  dealAmount: [{ required: true, message: "收款金额不能为空", trigger: "blur" }],
  alipayNo: [{ required: true, message: "支付宝订单号不能为空", trigger: "blur" }],
  payCredentials: [{ required: true, message: "请上传打款凭证", trigger: "change" }],
  alipayOrderNo: [{ required: true, message: "支付宝订单号不能为空", trigger: "blur" }],
  payType: [{ required: true, message: "请选择付款方式", trigger: "blur" }],
});

// 确认
const formRef = ref();
const btnLoading = ref(false);
const confirm = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      btnLoading.value = true;
      if (sourceFromShopList.value) {
        baseService
          .post("/sale/updatePlatform", dataForm.value)
          .then((res) => {
            if (res.code == 0) {
              ElMessage.success("交易成功！");
              show.value = false;
              emits("refresh");
            }
          })
          .finally(() => {
            btnLoading.value = false;
          });
      } else {
        baseService
          .post("/sale/update", dataForm.value)
          .then((res) => {
            if (res.code == 0) {
              ElMessage.success("收款确认成功");
              show.value = false;
              emits("refresh");
            }
          })
          .finally(() => {
            btnLoading.value = false;
          });
      }
    }
  });
};

defineExpose({
  init
});
</script>
<style lang="less" scoped>
:deep(.el-descriptions__body) {
  display: flex;
  justify-content: space-between;

  tbody {
    display: flex;
    flex-direction: column;

    tr {
      display: flex;
      flex: 1;

      .el-descriptions__label {
        display: flex;
        align-items: flex-start !important;
        font-weight: normal;
        width: 144px;
      }

      .el-descriptions__content {
        display: flex;
        align-items: center;
        min-height: 48px;
        flex: 1;

        > div {
          width: 100%;
        }

        .el-form-item__label {
          display: none;
        }

        .el-form-item {
          margin-bottom: 0;
        }
      }

      .noneSelfRight {
        border-right: 0 !important;
      }

      .noneSelfLeft {
        border-left: 0 !important;
      }

      .noneSelfLabel {
        background: none;
        border-left: 0 !important;
        border-right: 0 !important;
      }
    }
  }
}
</style>
