<template>
    <el-dialog v-model="show" class="order-confirm-dialog" :close-on-click-modal="false" :close-on-press-escape="false"
        :append-to-body="true" title="提交打款申请" width="480px" align-center>
        <el-form :model="dataForm" label-position="top" :rules="rules" ref="formRef">
            <el-descriptions class="descriptions descriptions-label-140" :column="1" size="default" border>
                <el-descriptions-item label-class-name="title">
                    <template #label>
                        <div>收款账户类型</div>
                    </template>
                    <el-form-item prop="paymentAccountType">
                        <el-select v-model="dataForm.paymentAccountType" placeholder="请选择">
                            <el-option v-for="item in accountTypeList" :key="item.value" :label="item.name"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name="title">
                    <template #label>
                        <div>账户名称<span style="color: red;">*</span></div>
                    </template>
                    <el-form-item prop="paymentAccountName">
                        <el-input v-model="dataForm.paymentAccountName" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name="title">
                    <template #label>
                        <div>账号<span style="color: red;">*</span></div>
                    </template>
                    <el-form-item prop="paymentAccount">
                        <el-input v-model="dataForm.paymentAccount" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name="title">
                    <template #label>
                        <div>打款金额(元)<span style="color: red;">*</span></div>
                    </template>
                    <el-form-item prop="paymentMoney">
                        <el-input v-model="dataForm.paymentMoney" type="number" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name="title">
                    <template #label>
                        <div>备注</div>
                    </template>
                    <el-form-item prop="paymentRemark">
                        <el-input v-model="dataForm.paymentRemark" type="textarea" placeholder="请输入"
                            :rows="4"></el-input>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <template #footer>
            <el-button :loading="btnLoading" @click="show = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="confirm">确认提交</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineExpose, reactive, defineEmits } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emits = defineEmits(["refresh"]);
const show = ref(false);
const dataForm = ref(<any>{
    paymentAccountType: 1
});
const rules = reactive({
    paymentAccountName: [{ required: true, message: "请输入账户名称", trigger: "blur" }],
    paymentAccount: [{ required: true, message: "请输入账号名称", trigger: "blur" }],
    paymentMoney: [{ required: true, message: "请输入打款金额", trigger: "blur" }]
});

const accountTypeList = ref([
    { value: 1, name: '支付宝转账' },
    { value: 2, name: '微信转账' },
    { value: 3, name: '银行卡转账' }
])

const init = (id:any) => {
    dataForm.value.id = id;
    show.value = true;
};


// 确认
const formRef = ref();
const btnLoading = ref(false);
const confirm = () => {
    formRef.value.validate((valid: boolean) => {
        if (valid) {
            btnLoading.value = true;
            baseService
                .post("/partnerSaleOrder/applyPay", dataForm.value)
                .then((res) => {
                    if (res.code == 0) {
                        ElMessage.success("提交成功");
                        show.value = false;
                        emits("refresh");
                    }
                })
                .finally(() => {
                    btnLoading.value = false;
                });
        }
    });
};

defineExpose({
    init
});
</script>
<style lang="less" scoped>
:deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;

    tbody {
        display: flex;
        flex-direction: column;

        tr {
            display: flex;
            flex: 1;

            .el-descriptions__label {
                display: flex;
                align-items: flex-start !important;
                font-weight: normal;
                width: 144px;
            }

            .el-descriptions__content {
                display: flex;
                align-items: center;
                min-height: 48px;
                flex: 1;

                >div {
                    width: 100%;
                }

                .el-form-item__label {
                    display: none;
                }

                .el-form-item {
                    margin-bottom: 0;
                }
            }

            .noneSelfRight {
                border-right: 0 !important;
            }

            .noneSelfLeft {
                border-left: 0 !important;
            }

            .noneSelfLabel {
                background: none;
                border-left: 0 !important;
                border-right: 0 !important;
            }
        }
    }
}

:deep(.el-form-item) {
    &.is-error {
        .el-input__inner {
            &::placeholder {
                color: var(--el-color-danger);
            }
        }

        .el-select__placeholder {
            color: var(--el-color-danger);
        }

        .el-form-item__error {
            display: none !important;
            opacity: 0 !important;
        }
    }

    &.is-success {
        .el-form-item__error {
            transition: none !important;
            opacity: 0 !important;
        }
    }
}
</style>