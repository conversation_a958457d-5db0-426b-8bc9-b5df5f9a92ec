<template>
  <div class="TableXScrollSty">
    <el-card shadow="never" class="rr-view-ctx-card">
      <div class="flx-align-center" style="margin-bottom: 12px">
        <div class="wallet_card">
          <div class="above flx-align-center">
            <span class="title flx-1">账户余额</span>
          </div>
          <div class="middle flx-align-center">
            <div class="middle_item">
              <AnimatedNumber :value="balance" :duration="1500" easing="easeOutCubic" />
            </div>
          </div>
          <div class="below">
            <!-- <div class="recharge" @click=" rechargedrawer = true; dataForm.orderNum = ''; ">立即充值</div> -->
            <div class="recharge" @click="rechargeCoinChange">立即充值</div>
          </div>
        </div>
        <div class="statistics" style="background: linear-gradient(135deg, rgba(255, 226, 226, 0.47) 24.79%, rgba(217, 224, 247, 0.1) 58.47%), #fff">
          <div class="above flx-align-center" style="padding-right: 2px;">
            <span class="title flx-1">月消费统计(星球币)</span>
            <el-date-picker v-model="topTotal.params.consumptionParams.date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="consumption" />
          </div>
          <div class="middle">{{ formatCurrency(topTotal.data.consumptionData.amount) }}</div>
          <div class="below flx">
            <span style="flex: 1; height: 22px">
              <!-- <span class="sum_label">总消费：</span>
              <span class="sum_value" style="color: #f56c6c">{{ formatCurrency(topTotal.data.consumptionData.total) }}</span> -->
            </span>
          </div>
        </div>
        <div class="statistics" style="background: linear-gradient(135deg, rgba(230, 254, 234, 0.47) 24.79%, rgba(217, 224, 247, 0.1) 58.47%), #fff">
          <div class="above flx-align-center" style="padding-right: 2px;">
            <span class="title flx-1">月充值统计(星球币)</span>
            <el-date-picker v-model="topTotal.params.rechargeParams.date" type="month" format="YYYY-MM" value-format="YYYY-MM" size="small" style="width: 80px" :clearable="false" @change="recharge" />
          </div>
          <div class="middle">{{ formatCurrency(topTotal.data.rechargeData.amount) }}</div>
          <div class="below flx">
            <span style="flex: 1; height: 22px">
              <!-- <span class="sum_label">总充值：</span>
              <span class="sum_value" style="color: #67c23a">{{ formatCurrency(topTotal.data.rechargeData.total) }}</span> -->
            </span>
          </div>
        </div>
      </div>
      <ny-button-group :list="[
          { dictLabel: '消费账单', dictValue: 0 },
          { dictLabel: '充值账单', dictValue: 1 },
          // { dictLabel: '提现明细', dictValue: 2 },
          { dictLabel: '开票记录', dictValue: 3 },
          { dictLabel: '线下汇款记录', dictValue: 4 }
        ]" v-model="currentType" @change="handleClick" style="display: inline-block;margin-bottom: 10px;"></ny-button-group>
      <ny-table ref="tableRef" v-if="currentType == 0" :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('wallet:bill:export')" type="info" @click="getExport">{{ $t("export") }}</el-button>
        </template>
        <template #header-right>
          <div>
            <el-date-picker v-model="publicTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="dateChange" />
            <el-date-picker style="width: 120px !important; margin-left: 8px" v-model="state.dataForm.month" @change="changeMonth" type="month" format="YYYY-MM" value-format="YYYY-MM" placeholder="选择月份"> </el-date-picker>
          </div>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #applyStatus="{ row }">
         <el-tag type="info" v-if="row.applyStatus == 0">未开票</el-tag>
          <el-tag type="warning" v-if="row.applyStatus == 1">开票中</el-tag>
          <el-tag type="success" v-if="row.applyStatus == 2">已开票</el-tag>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
            <span style="font-weight: bold; color: #1d2129">消费金额</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
      <ny-table v-else-if="currentType == 1" :state="state" :columns="columns2" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button :disabled="state.dataListSelections && state.dataListSelections.length < 1" type="primary" @click="applyInvoicingMultHandle()">申请开票</el-button>
          <el-button type="warning" @click="headerManaHandle">抬头管理</el-button>
          <el-button v-if="state.hasPermission('wallet:bill:export')" type="info" @click="getExport">{{ $t("export") }}</el-button>
        </template>
        <template #header-right>
          <el-date-picker v-model="publicTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="dateChange" />
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #applyStatus="{ row }">
          <el-tag type="info" v-if="row.applyStatus == 0">未开票</el-tag>
          <el-tag type="warning" v-if="row.applyStatus == 1">开票中</el-tag>
          <el-tag type="success" v-if="row.applyStatus == 2">已开票</el-tag>
        </template>
        <template #operate="{ row }">
          <el-button v-if="row.applyStatus == 0" type="primary" @click="applyInvoicingHandle(row)" link>申请开票</el-button>
          <span v-else>-</span>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
            <span style="font-weight: bold; color: #1d2129">充值金额</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
      <ny-table v-else-if="currentType == 2" :state="state" :show-summary="true" :columns="columns3" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('wallet:bill:export')" type="info" @click="getExport">{{ $t("export") }}</el-button>
        </template>
        <template #header-right>
          <div>
            <el-date-picker v-model="publicTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="dateChange" />
          </div>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #accountType="{ row }">
          <span>{{ row.accountType == 1 ? "支付宝" : row.accountType == 2 ? "银行卡" : "" }}</span>
        </template>
        <template #operate="{ row }">
          <el-button @click="billWthdrawalHandle(row)" v-if="row.state == 2" type="success" link>重新申请</el-button>
          <el-button type="primary" @click="auditWthdrawalDrawerHandle(row)" link>详情</el-button>
        </template>
        <template #state="{ row }">
          <el-tag type="primary" v-if="row.state == 0">待审核</el-tag>
          <template v-else-if="row.state == 2">
            <div class="flx-center">
              <el-tag style="margin-right: 8px" type="danger">已拒绝</el-tag>
              <el-tooltip class="box-item" effect="dark" :content="row.approvalOpinion" placement="top-start">
                <el-icon color="#F56C6C">
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-tag type="success" v-else-if="row.state == 1">审核通过</el-tag>
          <el-tag type="success" v-else-if="row.state == 3">已付款</el-tag>
          <span v-else>-</span>
        </template>
      </ny-table>
      <ny-table v-else-if="currentType == 3" :state="state" :columns="columns4" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button v-if="state.hasPermission('wallet:bill:export')" type="info" @click="getExport">{{ $t("export") }}</el-button>
        </template>
        <template #header-right>
          <el-date-picker v-model="publicTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="dateChange" />
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #invoiceType="{ row }">
          <span>{{ row.invoiceType == 1 ? "普票" : "-" }}</span>
        </template>
        <template #applyStatus="{ row }">
          <el-tag :type="row.applyStatus == 1 ? 'warning' : 'success'">{{ row.applyStatus == 1 ? "开票中" : "已开票" }}</el-tag>
        </template>
        <template #operate="{ row }">
          <el-button type="primary" @click="applyInvoicingMultHandle(row)" link>查看明细</el-button>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
            <span style="font-weight: bold; color: #1d2129">发票金额</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
      <ny-table v-else-if="currentType == 4" :state="state" :columns="columns5" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <el-button type="primary" @click="transferdrawerHandle()">新增汇款</el-button>
          <el-button v-if="state.hasPermission('wallet:bill:export')" type="info" @click="getExport">{{ $t("export") }}</el-button>
        </template>
        <template #header-right>
          <div>
            <el-date-picker v-model="publicTime" type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" unlink-panels style="width: 220px" @change="dateChange" />
          </div>
          <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
          <el-button @click="resetFn">{{ $t("resetting") }}</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ formatTimeStamp(row.createDate) }}</span>
        </template>
        <template #paymentVoucher="{ row }">
          <el-image v-if="row.paymentVoucher" style="width: 54px" :src="row.paymentVoucher" :preview-src-list="[row.paymentVoucher]" :preview-teleported="true" fit="cover"></el-image>
        </template>
        <template #operate="{ row }">
          <el-button @click="billWthdrawalHandle(row)" v-if="row.auditStatus == 2" type="success" link>重新申请</el-button>
          <el-button type="primary" @click="audittransferDrawerHandle(row)" link>详情</el-button>
        </template>
        <template #state="{ row }">
          <el-tag type="primary" v-if="row.state == 0">待审核</el-tag>
          <el-tag type="success" v-if="row.state == 1">审核通过</el-tag>
          <template v-else-if="row.state == 2">
            <div class="flx-center">
              <el-tag style="margin-right: 8px" type="danger">已拒绝</el-tag>
              <el-tooltip class="box-item" effect="dark" :content="row.approvalOpinion" placement="top-start">
                <el-icon color="#F56C6C">
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px">
            <span style="font-weight: bold; color: #1d2129">提交金额</span>
            <span>商品数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </el-card>
  </div>
  <!-- 充值 -->
  <el-dialog v-model="rechargedrawer" width="480" title="充值" :close-on-click-modal="false" :close-on-press-escape="false" @close="rechargedrawer = false">
    <el-descriptions class="descriptions descriptions-label-140" :column="1" border>
      <el-descriptions-item label-class-name="title">
        <template #label>
          <div>支付宝账号</div>
        </template>
        <EMAIL>
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label>
          <div>店铺名称</div>
        </template>
        枣庄努运企业管理有限公司
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label>
          <div>转账金额</div>
        </template>
        {{ rechargeAmount }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="title">
        <template #label>
          <div>支付宝订单号<span style="color: red">*</span><br /><el-text type="primary" @click="dataForm.showImagePreview = true">如何获取？</el-text></div>
        </template>
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-position="top">
          <el-form-item prop="orderNum">
            <el-input v-model="dataForm.orderNum" placeholder="请输入支付宝订单号"></el-input>
          </el-form-item>
        </el-form>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="rechargedrawer = false">取消</el-button>
        <el-button :loading="requestLoading" type="primary" @click="rechargeSubmit">提交</el-button>
      </div>
    </template>
    <el-image-viewer v-if="dataForm.showImagePreview" :url-list="['https://oss.nyyyds.com/upload/20250509/c2737ad597474204b6b78b255d63fc25.png']" hide-on-click-modal teleported @close="closePreview" />
  </el-dialog>
  <!-- 充值星球币 -->
  <rechargeCoin ref="rechargeCoinRef" @change="rechargeChange"></rechargeCoin>
  <!-- 抬头管理 -->
  <headerMana ref="headerManaRef" />
  <!-- 开票申请 -->
  <applyInvoicing ref="applyInvoicingRef" @refreshDataList="state.getDataList()" />
  <!-- 批量开票申请 -->
  <applyInvoicingMult ref="applyInvoicingMultRef" @refreshDataList="state.getDataList()" />
  <!-- 提现 -->
  <billWthdrawal ref="billWthdrawalRef" @refreshDataList="state.getDataList()" />
  <!-- 提现审核详情 -->
  <auditWthdrawalDrawer ref="auditWthdrawalDrawerRef" />
  <!-- 汇款 -->
  <transferdrawer ref="transferdrawerRef" @refreshDataList="state.getDataList()" />
  <!-- 汇款审核详情 -->
  <audittransferDrawer ref="audittransferDrawerRef" />
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatTimeStamp, formatDate, formatCurrency } from "@/utils/method";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/utils";
import { useAppStore } from "@/store";
import headerMana from "./headerMana.vue";
import applyInvoicing from "./applyInvoicing.vue";
import applyInvoicingMult from "./applyInvoicingMult.vue";
import billWthdrawal from "./billWthdrawal.vue";
import auditWthdrawalDrawer from "./auditWthdrawalDrawer.vue";
import transferdrawer from "./transferdrawer.vue";
import audittransferDrawer from "./audittransferDrawer.vue";
import AnimatedNumber from "./AnimatedNumber.vue";
import rechargeCoin from "./recharge.vue";
import { useRoute } from "vue-router";

const route = useRoute();
const store = useAppStore();
const publicTime = ref(""); // 时间筛选变量
const balance = ref(0); // 账户余额
const rechargedrawer = ref(false); // 充值

const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "remark",
    label: "消费功能",
    minWidth: "400",
    align: "center"
  },
  {
    prop: "spendingAmount",
    label: "消费星球币",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "afterMoney",
    label: "剩余星球币",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "createName",
    label: "支付人",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "createDate",
    label: "消费时间",
    minWidth: "200",
    align: "center"
  }
]);
const columns2 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "orderNum",
    label: "订单编号",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "recharge",
    label: "充值金额（元）",
    minWidth: "100",
    align: "center"
  },
  {
    prop: "afterMoney",
    label: "余额（星球币）",
    minWidth: "100",
    align: "center"
  },
  {
    prop: "createName",
    label: "支付人",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "createDate",
    label: "充值时间",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "applyStatus",
    label: "开票状态",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "operate",
    label: "操作",
    minWidth: "160",
    align: "center"
  }
]);
const columns3 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "amount",
    label: "提现金额(元)",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "accountType",
    label: "账户类型",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "account",
    label: "收款账户",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "applicant",
    label: "申请人",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "createDate",
    label: "申请时间",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "300",
    align: "center"
  },
  {
    prop: "state",
    label: "审核状态",
    minWidth: "160",
    align: "center"
  },
  {
    prop: "operate",
    label: "操作",
    minWidth: "160",
    align: "center"
  }
]);
const columns4 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "headerName",
    label: "发票抬头",
    minWidth: "200",
    align: "center"
  },
  {
    prop: "invoiceType",
    label: "发票类型",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "invoiceAmount",
    label: "发票金额(元)",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "creatorName",
    label: "申请人",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "createDate",
    label: "申请时间",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "email",
    label: "接收邮箱",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "applyStatus",
    label: "开票状态",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "operate",
    label: "操作",
    minWidth: "140",
    align: "center"
  }
]);
const columns5 = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "amount",
    label: "提交金额(元)",
    minWidth: "100",
    align: "center"
  },
  {
    prop: "receiveAccount",
    label: "支付宝账户主体",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "receiveNumber",
    label: "支付宝账号",
    minWidth: "140",
    align: "center"
  },
  {
    prop: "name",
    label: "提交人",
    minWidth: "100",
    align: "center"
  },
  {
    prop: "createDate",
    label: "提交时间",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "paymentVoucher",
    label: "汇款凭证",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "state",
    label: "审核状态",
    minWidth: "120",
    align: "center"
  },
  {
    prop: "operate",
    label: "操作",
    minWidth: "120",
    align: "center"
  }
]);
const currentType = ref(0);
const view = reactive({
  getDataListURL: "/wallet/bill/page",
  getDataListIsPage: true,
  dataForm: {
    type: "0", // 0：消费 1：充值
    start: "",
    month: "", 
    end: "",
    startDate: "",
    endDate: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// tabs切换
const handleClick = (name: any) => {
  view.getDataListURL = ["/wallet/bill/page", "/wallet/bill/page", "/wallet/bill/withdraw/page", "/invoice/apply/page", "/wallet/bill/offline/page"][+name];
  view.dataForm.type = currentType.value < 2 ? name : "";
  resetFn();
};
// 重置
const resetFn = () => {
  publicTime.value = undefined;
  tableRef.value && tableRef.value.clearSelection();
  view.dataForm.start = "";
  view.dataForm.end = "";
  view.dataForm.endDate = "";
  view.dataForm.startDate = "";
  view.dataForm.month = "";
  state.limit = 10;
  state.getDataList();
};
// 选择月份
const tableRef = ref();
const changeMonth = () => {
  publicTime.value = undefined;
  tableRef.value && tableRef.value.clearSelection();
  if (state.dataForm.month) {
    state.limit = 9999;
    state.page = 1;
    let days = new Date(state.dataForm.month.split("-")[0], state.dataForm.month.split("-")[1], 0).getDate();
    view.dataForm.start = state.dataForm.month + "-01 00:00:00";
    view.dataForm.end = state.dataForm.month + "-" + days + " 23:59:59";
    baseService
      .get(view.getDataListURL, {
        page: 1,
        limit: 9999,
        ...state.dataForm
      })
      .then((res) => {
        state.dataList = [];
        state.total = 0;
        state.dataList = res.data.list;
        state.total = res.data.total;
        tableRef.value.selectedAll();
      });
  } else {
    state.limit = 10;
    state.page = 1;
    state.getDataList();
  }
};
// 表格 - 时间选择
const dateChange = () => {
  state.dataForm.month = undefined;
  tableRef.value && tableRef.value.clearSelection();
  if (currentType.value == 3 || currentType.value == 2 || currentType.value == 4) {
    view.dataForm.startDate = publicTime.value ? publicTime.value[0] + " 00:00:00" : "";
    view.dataForm.endDate = publicTime.value ? publicTime.value[1] + " 23:59:59" : "";
    return;
  }
  view.dataForm.start = publicTime.value ? publicTime.value[0] : "";
  view.dataForm.end = publicTime.value ? publicTime.value[1] : "";
};

// --------------顶部数据
const topTotal = reactive({
  params: {
    consumptionParams: {
      type: 0,
      date: ""
    },
    rechargeParams: {
      type: 1,
      date: ""
    }
  },
  data: {
    consumptionData: {
      amount: 0,
      total: 0,
      year: 0
    },
    rechargeData: {
      amount: 0,
      total: 0,
      year: 0
    }
  }
});
// 总数据 - 消费
const consumption = () => {
  baseService.get("/wallet/bill/date", topTotal.params.consumptionParams).then((res) => {
    topTotal.data.consumptionData.amount = res.data.amount;
    topTotal.data.consumptionData.total = res.data.total;
    topTotal.data.consumptionData.year = res.data.year;
  });
};
// 总数据 - 充值
const recharge = () => {
  baseService.get("/wallet/bill/date", topTotal.params.rechargeParams).then((res) => {
    topTotal.data.rechargeData.amount = res.data.amount;
    topTotal.data.rechargeData.total = res.data.total;
    topTotal.data.rechargeData.year = res.data.year;
  });
};
// -----------------

// 导出
const getExport = () => {
  let url = ["/wallet/bill/export?type=0", "/wallet/bill/export?type=1", "/wallet/bill/withdraw/export", "/invoice/apply/export", "/wallet/bill/offline/export"][currentType.value];
  baseService.get(url, view.dataForm).then((res) => {
    if (res) {
      fileExport(res, ["消费账单", "充值账单", "提现明细", "开票记录", "线下汇款记录"][currentType.value]);
    }
  });
};

// 合计行计算函数
const getSummaries = () => {
  if (currentType.value == 0) {
    let total: any = 0;
    state.dataList.map((item: any) => {
      if (item.spendingAmount) total = total + +item.spendingAmount;
    });
    return total.toFixed(2);
  } else if (currentType.value == 1) {
    let total: any = 0;
    state.dataList.map((item: any) => {
      if (item.spendingAmount) total = total + +item.spendingAmount;
    });
    return total.toFixed(2);
  } else if (currentType.value == 2) {
    let total: any = 0;
    state.dataList.map((item: any) => {
      if (item.amount) total = total + +item.amount;
    });
    return total.toFixed(2);
  } else if (currentType.value == 3) {
    let total: any = 0;
    state.dataList.map((item: any) => {
      if (item.invoiceAmount) total = total + +item.invoiceAmount;
    });
    return total.toFixed(2);
  } else if (currentType.value == 4) {
    let total: any = 0;
    state.dataList.map((item: any) => {
      if (item.amount) total = total + +item.amount;
    });
    return total.toFixed(2);
  }
};
// 合计行计算函数
// const getSummaries = (param: any) => {
//   const { columns } = param;
//   const sums: string[] = [];
//   columns.forEach((column, index) => {
//     // 第一列 显示文字
//     if (index === 0) {
//       return (sums[index] = "合计:");
//     } else if (column.property == "spendingAmount" && currentType.value == 0) {
//       let total: any = 0;
//       state.dataList.map((item: any) => {
//         if (item.spendingAmount) total = total + +item.spendingAmount;
//       });
//       return (sums[index] = total.toFixed(2) + "");
//     } else if (column.property == "spendingAmount" && currentType.value == 1) {
//       let total: any = 0;
//       state.dataList.map((item: any) => {
//         if (item.spendingAmount) total = total + +item.spendingAmount;
//       });
//       return (sums[index] = total.toFixed(2) + "");
//     } else if (column.property == "amount" && currentType.value == 2) {
//       let total: any = 0;
//       state.dataList.map((item: any) => {
//         if (item.amount) total = total + +item.amount;
//       });
//       return (sums[index] = total.toFixed(2) + "");
//     } else if (column.property == "invoiceAmount" && currentType.value == 3) {
//       let total: any = 0;
//       state.dataList.map((item: any) => {
//         if (item.invoiceAmount) total = total + +item.invoiceAmount;
//       });
//       return (sums[index] = total.toFixed(2) + "");
//     } else if (column.property == "amount" && currentType.value == 4) {
//       let total: any = 0;
//       state.dataList.map((item: any) => {
//         if (item.amount) total = total + +item.amount;
//       });
//       return (sums[index] = total.toFixed(2) + "");
//     }
//   });
//   return sums;
// };

// ----------------充值-余额表单变量
const dataForm = reactive({
  orderNum: "",
  showImagePreview: false
});
const rules = ref({
  orderNum: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }]
});
// 充值提交
const dataFormRef = ref(); // 表单ref
const requestLoading = ref(false); // 详情加载
const rechargeSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    requestLoading.value = true;
    baseService
      .get("/wallet/bill/recharge", { orderNum: dataForm.orderNum })
      .then((res) => {
        ElMessage.success("充值成功！");
        rechargedrawer.value = false;
        state.getDataList();
        store.checkRecharge();
      })
      .finally(() => {
        requestLoading.value = false;
      });
  });
};
const closePreview = () => {
  dataForm.showImagePreview = false;
};
// -------------
onMounted(() => {
  baseService.get("/wallet/bill/balance").then((res) => {
    balance.value = res.data;
    if (route.query.open == "1") {
      rechargeCoinRef.value.init(balance.value);
    }
  });
  topTotal.params.consumptionParams.date = formatDate(new Date(), "YYYY-MM");
  topTotal.params.rechargeParams.date = formatDate(new Date(), "YYYY-MM");
  consumption();
  recharge();
});

// ------------------组件调用
// 抬头管理
const headerManaRef = ref();
const headerManaHandle = () => {
  nextTick(() => {
    headerManaRef.value.init();
  });
};
// 开票申请
const applyInvoicingRef = ref();
const applyInvoicingHandle = (row: any) => {
  nextTick(() => {
    applyInvoicingRef.value.init(row);
  });
};
// 批量开票申请
const applyInvoicingMultRef = ref();
const applyInvoicingMultHandle = (row?: any) => {
  nextTick(() => {
    if (row) {
      applyInvoicingMultRef.value.init({ title: "开票明细", apply: false, id: row.id, purchases: [{ ...row }] });
      return;
    }
    applyInvoicingMultRef.value.init({ title: "申请开票", apply: true, purchases: state.dataListSelections });
  });
};
// 提现
const billWthdrawalRef = ref();
const billWthdrawalHandle = (row?: any) => {
  nextTick(() => {
    billWthdrawalRef.value.init({ ...row, allAmount: balance.value });
  });
};
// 提现详情
const auditWthdrawalDrawerRef = ref();
const auditWthdrawalDrawerHandle = (row?: any) => {
  nextTick(() => {
    auditWthdrawalDrawerRef.value.init(row);
  });
};
// 提现
const transferdrawerRef = ref();
const transferdrawerHandle = (row?: any) => {
  nextTick(() => {
    transferdrawerRef.value.init(row);
  });
};
// 提现审核详情
const audittransferDrawerRef = ref();
const audittransferDrawerHandle = (row?: any) => {
  nextTick(() => {
    audittransferDrawerRef.value.init(row);
  });
};

// 充值星球币
const rechargeCoinRef = ref();
const rechargeCoinChange = () => {
  rechargeCoinRef.value.init(balance.value);
};

// 选择充值金额
const rechargeAmount = ref();
const rechargeChange = (num: number) => {
  rechargeAmount.value = num;
  rechargedrawer.value = true;
  dataForm.orderNum = "";
};
</script>

<style lang="less" scoped>
.TableXScrollSty {
  :deep(.NYpagination) {
    padding: 16px 0px;
  }
}

.statistics {
  width: 24%;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  margin-left: 20px;
  height: 143px;

  .above {
    padding: 12px;

    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 500;
      font-size: 14px;
      color: #1d252f;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 3px;
      height: 24px;
    }

    :deep(.el-date-editor) {
      line-height: 20px;
      height: 20px;

      .el-input__prefix {
        position: absolute;
        right: 4px;

        .el-input__prefix-inner {
          color: #303133;
        }
      }

      .el-input__wrapper {
        box-shadow: none;
        background-color: transparent;
        .el-input__inner {
          cursor: pointer;
        }
      }
    }
  }

  .middle {
    // height: 32px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 20px;
    color: #1d2129;
    line-height: 32px;
    display: flex;
    align-items: center;
  }

  .below {
    padding: 9px 16px 17px 16px;

    .sum_label {
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .sum_value {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}

.wallet_card {
  width: 48%;
  height: 143px;
  background-image: url("/src/assets/images/coinBg.png");
  background-size: 100% 100%;
  border-radius: 4px 4px 4px 4px;

  // border: 1px solid #ebeef5;
  .above {
    padding: 12px;

    .title {
      font-weight: 400;
      font-size: 14px;
      color: #1d252f;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 3px;
    }
  }

  .middle {
    padding: 4px 16px 12px 16px;

    // height: 32px;
    .middle_item {
      width: 225px;
      height: 32px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .middle_item_value {
        font-weight: 400;
        font-size: 32px;
        line-height: 32px;
        font-family: ZenDots-Regular;
      }
    }
  }

  .below {
    padding: 4px 16px 12px 16px;

    .recharge {
      width: 88px;
      height: 32px;
      background: linear-gradient(90deg, #63451e 0%, #060503 100%);
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      font-size: 14px;
      color: #ffd7af;
      line-height: 22px;
      padding: 5px 16px;
      cursor: pointer;
    }
  }
}

.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}

:deep(.el-form-item--default) {
  margin-bottom: 12px;
  display: block;
}

:deep(tfoot) {
  tr td:not(:first-child) .cell {
    font-weight: bold;
  }
}

.el-tag {
  border: 1px solid;
}
</style>
