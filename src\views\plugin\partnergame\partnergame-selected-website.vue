<template>
  <el-dialog v-model="visible" title="复制选号网址" :close-on-click-modal="false" :close-on-press-escape="false" width="700">
    <div class="flx-justify-between mb-20">
      <el-text type="danger">提示：折扣9折（输入：90）；原价（输入：100）；加价10%（输入：110）</el-text>
      <el-button type="warning" v-copy="link">复制选号网址</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" align="center" label="游戏名称"></el-table-column>
      <el-table-column prop="priceRatio" align="center" label="价格比率">
        <template #default="{ row }">
          <div class="flx-center">
            <el-input-number :controls="false" v-model="row.priceRatio" :show-input="true" class="flx-1" placeholder="请输入价格比率" type="number" :min="0" />
            <span class="ml-10">%</span>
          </div>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>

    <template #footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits } from "vue";
import { IObject } from "@/types/interface";
import { ElMessage } from "element-plus";
import { useSettingStore } from "@/store/setting";
import baseService from "@/service/baseService";

const emits = defineEmits(["refresh"]);
const sttingInfo = useSettingStore();
const visible = ref(false);

const link = sttingInfo.info.h5Url + "/pages/gameShare/gameShare";

// 表格数据
const tableData = ref<IObject[]>([]);

const init = () => {
  visible.value = true;
};

defineExpose({
  init
});
</script>
