<template>
    <shop-add-or-update 
        ref="shopAddOrUpdateRef" 
        :title="'商品信息审核'"
        source="audit"
        getDetialApi="/shop/shopaudit/"
        @refreshDataList="emit('refreshDataList')"
    >
    
        <template #header="scope">
            <div style="padding: 12px">
                <ny-title title="基本信息" style="padding: 0px 0px 12px 0px"/>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                    <el-descriptions-item>
                        <template #label>游戏名称</template>
                        {{ scope?.data?.gameName }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>零售价(元)</template>
                        <el-text type="danger">{{ scope?.data?.price }}</el-text>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </template>


        <template #info="scope">
            <div class="shop_page_basic" style="margin: 12px 0px;">
                <div style="padding: 12px">
                    <ny-title title="用户信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                        <el-descriptions-item>
                            <template #label>用户ID</template>
                            {{ scope.data.creator }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>用户名</template>
                            {{ scope.data.nickname }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系手机</template>
                            {{ scope.data.phone }}
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template #label>联系微信</template>
                            {{ scope.data.wx }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
            <div class="shop_page_basic" style="margin: 12px 0px; padding: 12px">
                <ny-title title="自动降价" style="padding: 0px 0px 12px 0px"/>
                <el-descriptions :column="2" border class="descriptions descriptions-label-140 proportion">
                    <el-descriptions-item>
                        <template #label>自动降价周期(天)</template>
                            {{ scope.data.priceReductionCycle || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>自动降价比例(%)</template>
                        {{ scope.data.priceReductionPercentage || '-'  }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>自动降价底价(元)</template>
                        {{ scope.data.minimumPrice || '-'  }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="shop_page_basic" style="margin: 12px 0px;" v-if="scope?.data && scope?.data?.basicInfo && scope?.data?.basicInfo.length">
                <div style="padding: 12px">
                    <ny-title title="商品信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                        <el-descriptions-item v-for="(item,index) in scope.data.basicInfo">
                            <template #label>{{ item.key }}</template>
                            {{ item.values }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
            <div class="shop_page_basic" style="margin: 12px 0px;" v-if="scope?.data && scope?.data?.basicInfo && scope?.data?.basicInfo.length">
                <div style="padding: 12px">
                    <ny-title title="账号信息" style="padding: 0px 0px 12px 0px"/>
                    <el-descriptions :column="1" border class="descriptions descriptions-label-140">
                        <el-descriptions-item v-for="(item,index) in scope.data.accountInfo">
                            <template #label>{{ item.key }}</template>
                            {{ item.values }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
        </template> 


    </shop-add-or-update>
</template>

<script lang="ts" setup>
    import { ref, reactive, defineExpose, defineEmits } from 'vue';
    import shopAddOrUpdate from './shop-add-or-update.vue';

    const shopAddOrUpdateRef = ref();

    const init = (gameId: string, id: any) => {
        shopAddOrUpdateRef.value.init(gameId, id);
    }

    const emit = defineEmits(['refreshDataList']);

    defineExpose({
        init
    })

</script>