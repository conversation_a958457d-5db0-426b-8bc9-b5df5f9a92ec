<template>
  <el-dialog v-model="visible" append-to-body :width="480" title="修改密码" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form style="min-height: 380px" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">
      <el-form-item prop="password" :label="$t('updatePassword.password')">
        <el-input v-model="dataForm.password" type="password" :placeholder="$t('updatePassword.password')"></el-input>
      </el-form-item>
      <el-form-item prop="newPassword" :label="$t('updatePassword.newPassword')">
        <el-input v-model="dataForm.newPassword" type="password" :placeholder="$t('updatePassword.newPassword')"></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword" :label="$t('updatePassword.confirmPassword')">
        <el-input v-model="dataForm.confirmPassword" type="password" :placeholder="$t('updatePassword.confirmPassword')"></el-input>
      </el-form-item>
      <el-form-item v-if="store.state.user.tenantCode != 10000" prop="smsCode" label="验证码">
        <div style="display: flex; align-items: center">
          <el-input style="width: 336px" clearable v-model="dataForm.smsCode" placeholder="请输入验证码"></el-input>
          <el-button @click="sendCode" style="margin-left: 10px">获取验证码</el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from "vue";
import { IObject } from "@/types/interface";
import baseService from "@/service/baseService";
import { useAppStore } from "@/store";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const router = useRouter();
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  password: "",
  newPassword: "",
  confirmPassword: ""
});

const store = useAppStore();
const user = computed(() => store.state.user);
const init = () => {
  visible.value = true;
};
const validateConfirmPassword = (rule: IObject, value: string, callback: (e?: Error) => any) => {
  if (dataForm.newPassword !== value) {
    return callback(new Error(t("updatePassword.validate.confirmPassword")));
  }
  callback();
};

const rules = ref({
  password: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  newPassword: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  confirmPassword: [
    { required: true, message: t("validate.required"), trigger: "blur" },
    { validator: validateConfirmPassword, trigger: "blur" }
  ]
});
const disCountNum = ref(60);
const timer = ref();
const sendCode = () => {
  baseService
    .get("/sms/sendUpdateCode", {
      phones: store.state.user.mobile
    })
    .then((res) => {
      timer.value = setInterval(() => {
        if (disCountNum.value == 1) {
          clearInterval(timer.value);
        }
        disCountNum.value--;
      }, 1000);
    });
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (store.state.user.tenantCode != 10000) {
      dataForm.mobile = store.state.user.mobile;
    }
    baseService.put("/sys/user/password", dataForm).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          router.replace("/login");
        }
      });
    });
  });
};
defineExpose({
  init
});
</script>
<style lang="less" scoped>
.el-form-item {
  display: block;
  margin-bottom: 12px;
}
</style>
