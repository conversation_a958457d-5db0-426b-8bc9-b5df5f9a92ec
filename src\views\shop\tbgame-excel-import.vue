<template>
  <el-dialog v-model="visible" title="Excel导入" :close-on-click-modal="false" :close-on-press-escape="false">

    <p>为了保证数据导入顺利，请先
      <el-button v-blur @click="getExportMode()" plain
        style="margin: 0; padding: 0; color: var(--el-color-primary); border: none;">
        下载导入模板
      </el-button>
      ，并按照规范示例导入数据
    </p>


    <el-upload name="file" :action="url" :file-list="fileList" drag :on-success="successHandle" class="text-center">
      <el-icon>
        <upload />
      </el-icon>
      <div class="el-upload__text" v-html="$t('upload.text')"></div>
    </el-upload>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import app from "@/constants/app";
import { getToken } from "@/utils/cache";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { fileExport } from "@/utils/method";
import qs from "qs";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const url = ref("");
const fileList = ref([]);

const init = () => {
  visible.value = true;
  url.value = `${app.api}/shop/tbgame/importExcel?token=${getToken()}`;
  fileList.value = [];
};

// 上传成功
const successHandle = (res: IObject) => {
  if (res.code !== 0) {
    return ElMessage.error(res.msg);
  }
  ElMessage.success({
    message: t("prompt.success"),
    duration: 500,
    onClose: () => {
      visible.value = false;
      emit("refreshDataList");
    }
  });
};

// 下载模板
const getExportMode = () => {
  baseService.get("/shop/tbgame/exportMode").then((res) => {
    if (res) {
      let blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8'
      })
      let a = document.createElement('a');
      let url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = '游戏列表模板';
      document.body.appendChild(a);
      a.style.display = 'none';
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } else {
      ElMessage.error('下载失败！');
    }
  }).catch((err) => {
    ElMessage.error('操作失败！');
  });
}

defineExpose({
  init
});
</script>
