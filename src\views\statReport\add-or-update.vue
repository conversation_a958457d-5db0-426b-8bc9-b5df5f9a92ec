<template>
    <el-dialog
        v-model="visible"
        :title="addType == '1' ? (operateType == 'add' ? '新增分类' : '编辑分类') : (operateType == 'add' ? '新增报表' : '编辑报表')"
        width="560"
    >
        <el-form :model="dataForm" label-position="top" :rules="rules" ref="dataFormRef">
            <el-form-item :label="addType == '1' ? '分类名称' : '报表名称'" prop="title">
                <el-input v-model="dataForm.title" />
            </el-form-item>
            <template v-if="addType == '2'">
                <el-form-item label="权限成员" prop="visibleType">
                    <el-radio-group v-model="dataForm.visibleType" >
                        <el-radio :value="1" border>公开</el-radio>
                        <el-radio :value="2" border>指定可见人员</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="可见人员" required v-if="dataForm.visibleType == 2">
                    <div class="addUser">
                        <el-tag type="info" closable @close="visiblesClose(item)" v-for="(item,index) in dataForm.visibles">{{ item.realName }}</el-tag>
                        <el-button size="small" style="margin-bottom: 8px;"  @click="SelectMemberChange('visibles')">添加人员 +</el-button>
                    </div>
                </el-form-item>
                <el-form-item label="数据范围" prop="dataRadius">
                    <el-radio-group v-model="dataForm.dataRadius" >
                        <el-radio :value="1" border>公开</el-radio>
                        <el-radio :value="2" border>本人提交</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="是否审批" prop="isAudit">
                    <el-switch 
                        v-model="dataForm.isAudit" 
                        inline-prompt
                        active-text="是"
                        inactive-text="否"
                    />
                </el-form-item>
                <el-form-item label="审批流程" required v-if="dataForm.isAudit">
                    <div class="process">
                        <div class="list">
                            <div class="items" :class="{active : auditsChange == index}" draggable="true" @dragstart="handleDragStart($event,index)" @click="auditsChange = index"
                            @drop="handleDrop($event,index)" @dragover.prevent v-for="(item,index) in dataForm.audits" :key="index">
                                <div class="order">{{ index + 1 }}</div>
                                <span v-if="item.realName">审批：{{ item.realName }}</span>
                                <span v-else>角色：审批人</span>
                                
                            </div>
                            <el-icon 
                                color="var(--color-primary)" size="28" 
                                style="margin-left: 12px;cursor: pointer;"
                                @click="addAudits()"
                            ><CirclePlusFilled /></el-icon>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="审批人员"  v-if="dataForm.isAudit">
                    <template #label>
                        <div class="flx-justify-between">
                            <span><span style="color: #F56C6C;">*</span> 审批人员</span>
                            <el-button link @click="auditsDelete">删除流程</el-button>
                        </div>
                    </template>
                    <el-tag type="info" closable @close="auditsClose" v-if="dataForm.audits[auditsChange].realName && dataForm.audits[auditsChange] && auditsChange">{{ dataForm.audits[auditsChange].realName }}</el-tag>
                    <el-button size="small" @click="SelectMemberChange('audits')" v-else>设置人员 +</el-button>
                </el-form-item>
            </template>
        </el-form>
        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" :loading="butLoading" @click="submit">提交</el-button>
        </template>
    </el-dialog>
    <!-- 选择人员 -->
    <SelectMember ref="SelectMemberRef" @refreshDataList="callback"></SelectMember>
</template>

<script lang='ts' setup>
import { ref, reactive, nextTick } from 'vue';
import SelectMember from './SelectMember.vue';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);

const dataFormRef = ref();
const dataForm = ref({
    id: '',
    pid: '0',
    title: '',
    visibleType: 1,
    visibles: <any>[],
    dataRadius: 1,
    isAudit: false,
    audits: <any>[{
        type: 2,
        userId: '',
        realName: '',
    }],
    isShow:true,
    indexName: '',
})

const rules = ref({
    title: [{ required: true, message: '请输入报表名称', trigger: "blur" }],
    visibleType: [{ required: true, message: '请输选择权限成员', trigger: "change" }],
});

const addType = ref('');
const operateType = ref('');


const init = (type:any,operate:string,data?:any) => {
  visible.value = true;
  addType.value = type;
  operateType.value = operate;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  if(type == '2' && operate == 'add'){
    dataForm.value.pid = data.id
  }
  if(operate == 'edit'){
    getInfo(data);
  }
};

const getInfo = (data:any) =>{
    baseService.get('/report/report/'+data.id).then(res=>{
        Object.assign(dataForm.value,res.data);
        dataForm.value.audits = res.data.audits ? res.data.audits : [{
            type: 2,
            userId: '',
            realName: '',
        }]
        
        console.log(dataForm.value,'===== dataForm.value ======');
    })
}

// 可见人员删除
const visiblesClose = (e:any) =>{
    let index = dataForm.value.visibles.findIndex((item:any) => item.id === e.id);
    // 如果元素存在于数组中，则删除它
    if (index !== -1) {
        dataForm.value.visibles.splice(index, 1);
    }
}

// 选择人员回调
const callback = (e:any) =>{
    if(e.type == 'visibles'){
        dataForm.value.visibles = [];
        JSON.parse(JSON.stringify(e.value)).map((item:any)=>{
            dataForm.value.visibles.push({
                type: 1,
                userId: item.id,
                realName: item.realName
            })
        })
    }
    if(e.type == 'audits'){
        dataForm.value.audits[auditsChange.value] = {
            userId: e.value[0].id,
            realName: e.value[0].realName
        }
    }
    console.log(dataForm.value.audits)
}

// 审批流程新增
const addAudits = () =>{
    if(!dataForm.value.audits) dataForm.value.audits = [];
    dataForm.value.audits.push({
        type: 2,
        userId: '',
        realName: ''
    })
}

// 选中审批人员
const auditsChange = ref(<any>0)
// 删除审批人员
const auditsClose = () => {
    dataForm.value.audits[auditsChange.value].id = '';
    dataForm.value.audits[auditsChange.value].realName = '';
}

// 删除流程
const auditsDelete = () => {
    dataForm.value.audits.splice(auditsChange.value,1);
}

// 保存
const butLoading = ref(false);
const submit = () =>{
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }

        butLoading.value = true;
        if(addType.value == '1'){
            (!dataForm.value.id ? baseService.post : baseService.put)('/report/report', dataForm.value ).then(res=>{
                if(res.code == 0){
                    ElMessage.success({
                        message: "新增成功",
                        duration: 500,
                        onClose: () => {
                            visible.value = false;
                            emit("refreshDataList");
                        }
                    });
                }
            }).finally(()=>{
                butLoading.value = false;
            })
        }else{
            // 判断可见人员是否选择
            if(dataForm.value.visibleType == 2 && !dataForm.value.visibles.length){
                ElMessage.warning('可见人员不能为空');
                return ;
            }
            if(dataForm.value.isAudit){
                try{
                    dataForm.value.audits.map((item:any,index:number)=>{
                        if(item.userId == ''){
                            throw `第${index+1}个审核人不能为空`
                        }
                    })

                    dataForm.value.audits.map((item:any,index:number)=>{
                        item.auditFlow = index + 1
                    })
                }catch(err:any){
                    ElMessage.warning(err);
                    return
                }
            }

            console.log(dataForm.value,'====== dataForm.value =====');
            (!dataForm.value.id ? baseService.post : baseService.put)('/report/report', dataForm.value ).then(res=>{
                if(res.code == 0){
                    ElMessage.success({
                        message: "编辑成功",
                        duration: 500,
                        onClose: () => {
                            visible.value = false;
                            emit("refreshDataList");
                        }
                    });
                }
            }).finally(()=>{
                butLoading.value = false;
            })
        }
        
    });
}

defineExpose({
  init
});

// 拖拽排序开始
const handleDragStart = (event: any,index:number) => {
    // 将被拖动的索引设置到dataTransfer对象中，以便在拖放时使用
    event.dataTransfer.setData('index', index.toString());
};
// 拖拽排序结束
const handleDrop = (event: any,index:number) => {
    // 阻止默认的拖放行为（例如打开链接等）
    event.preventDefault();
    // 从dataTransfer对象中获取被拖动项的索引
    const draggedIndex = Number(event.dataTransfer.getData('index'));
    // 从原始数组中获取被拖动的项
    const draggedItem = dataForm.value.audits[draggedIndex];
    // 创建当前数组的副本以进行修改
    const updatedList = [...dataForm.value.audits];
    // 从原始位置删除被拖动的项
    updatedList.splice(draggedIndex, 1);
    // 将被拖动的项插入到列表的新位置
    updatedList.splice(index, 0, draggedItem);
    // 使用修改后的列表更新数组
    dataForm.value.audits = updatedList;
};


const addKey = ref(0);
const SelectMemberRef = ref();
const SelectMemberChange = (name:string) => {
    addKey.value++;
    let list:any = [];
    if(name == 'visibles'){
        list = dataForm.value.visibles || []
    }
    // if(name == 'audits'){
    //     list = dataForm.value.audits
    // }
    nextTick(() => {
        console.log(list,  dataForm.value.visibles)
        SelectMemberRef.value.init(name,list);
    });
};

</script>

<style lang='less' scoped>
.addUser{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .el-tag{
        margin-right: 8px;
        margin-bottom: 8px;
    }
}

.process{
    background: #F8F9FA;
    border-radius: 4px;
    padding: 18px 12px;
    width: 584px;
    overflow-x: auto;
    

    .list{
        display: inline-flex;
        align-items: center;
        .items{
            width: 152px;
            display: inline-flex;
            align-items: center;
            padding: 10px 12px;
            border: 1px solid #EBEEF5;
            background-color: #FFFFFF;
            border-radius: 4px;
            margin-right: 10px;
            .order{
                width: 24px;
                height: 24px;
                background: #909399;
                border-radius: 50px;
                border: 2px solid #909399;
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            span{
                font-weight: 400;
                font-size: 14px;
                color: #909399;
                padding: 0px 8px;
            }
        }
        .active{
            border: 1px solid var(--color-primary);
            .order{
                background-color: var(--color-primary);
                border: 2px solid var(--color-primary);
            }
            span{
                color: var(--color-primary);
            }
        }
    }
    
}

</style>