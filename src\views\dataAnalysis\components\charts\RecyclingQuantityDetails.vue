<template>
    <div class="card_analysis mt-12" style="background: #F7F8FA;">
        <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
            <div class="header_analysis_left flx-align-center">
                <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">回收量明细柱状图</div>
            </div>
            <div class="header_analysis_right flx-align-center">
                <div class="legend">
                    <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData" :key="index"
                        @change="changeShow"></el-checkbox>
                </div>
                <el-date-picker v-model="echartTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 220px; border-radius: 20px"
                    @change="getSalesRankingBarChart" />
            </div>
        </div>
        <div class="header_describe">回收明细数据</div>
        <div class="charts">
            <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue'
import * as echarts from "echarts";
import baseService from '@/service/baseService';
import { formatDate } from "@/utils/method";

const echartTime = ref();

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    startTime: "",
    endTime: "",
    type: "1"
});
const charts = ref(<any>[]);
const analysisChartRef = ref(null);
const seriesList = ref([
    {
        name: '订单量',
        type: 'bar',
        itemStyle: {
            color: "#F77234"
        },
        barMaxWidth: 30,
        yAxisIndex: 0,
        data: [],
    },
    {
        name: '回收额',
        type: 'bar',
        itemStyle: {
            color: "#0FC6C2"
        },
        barMaxWidth: 30,
        yAxisIndex: 1,
        data: []
    }
])


// 图例数据
const legendData = ref([
    { name: '订单量', color: '#F77234', show: true },
    { name: '回收额', color: '#0FC6C2', show: true },
])

// 销量明细柱状图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '6%',
            top: '10%',
            bottom: '16%',
        },
        xAxis: [
            {
                type: 'category',
                axisPointer: {
                    type: 'shadow'
                },
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '订单(个)',
                position: 'left',
                alignTicks: true,
                axisLabel: {
                    formatter: '{value} 个'
                }
            },
            {
                type: 'value',
                name: '金额(元)',
                position: 'right',
                alignTicks: true,
                axisLabel: {
                    formatter: function (value: any) {
                        if (value >= 10000) {
                            return (value / 10000).toFixed(1) + "万";
                        }
                        return value;
                    }
                }
            }
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 50,
            bottom: "2%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);

    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 9
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 9
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};
const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    echartTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getSalesRankingBarChart();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})


// 销量明细柱状图
const getSalesRankingBarChart = () => {
    dataForm.value.startTime = echartTime.value ? echartTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = echartTime.value ? echartTime.value[1] + " 23:59:59" : "";
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/recycleRankingBarChart", dataForm.value).then(res => {
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getSalesRankingBarChart();
};

defineExpose({
    init
});
</script>

<style lang='less' scoped>
.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F77234;
                        border-color: #F77234;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #0FC6C2;
                        border-color: #0FC6C2;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
</style>