<template>
  <el-dialog class="pushGooddialog" v-model="visible" width="480" :title="'账号密码设置'" :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog">
    <el-descriptions class="tipinfo" title="" :column="1" size="default" border>
      <el-descriptions-item label-class-name="title">
        <template #label> <div>合作商名称</div> </template>
        {{ dataForm.name }}
      </el-descriptions-item>
    </el-descriptions>

    <el-form :rules="rules" ref="formRef" :model="dataForm" label-suffix="：">

      <!-- 转转和闲鱼只显示cookie -->
      <el-form-item label="cookie" prop="cookie" v-if="dataForm.name == '转转' || dataForm.name == '闲鱼'">
        <el-input :prefix-icon="Lock" show-password v-model="dataForm.cookie" placeholder="请输入cookie" />
      </el-form-item>

      <template v-else>
        <el-form-item label="登录账号" prop="loginAccount">
          <el-input :prefix-icon="User" v-model="dataForm.loginAccount" clearable placeholder="请输入账号" />
        </el-form-item>

        <el-form-item label="登录密码" prop="loginPassword">
          <el-input :prefix-icon="Lock" type="password" show-password v-model="dataForm.loginPassword" placeholder="请输入密码" />
        </el-form-item>
      </template>
    </el-form>
    <template v-slot:footer>
      <el-button :loading="replyLoading" @click="closeDialog">{{ "取消" }}</el-button>
      <el-button :loading="replyLoading" type="primary" @click="dataFormSubmitHandle(formRef)">{{ "保存" }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineExpose, reactive } from "vue";
import { ElMessage, FormItemRule, FormInstance } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
const formRef = ref<FormInstance>();
const emits = defineEmits(["close", "refresh"]);
const visible = ref(false);
const rules: Partial<Record<string, FormItemRule[]>> = reactive({
  cookie: [
    {
      name: "required",
      trigger: "blur",
      title: "请输入cookie"
    }
  ],
  loginAccount: [
    {
      name: "required",
      title: "请输入账号",
      trigger: "blur"
    }
  ],
  loginPassword: [
    {
      name: "required",
      trigger: "blur",
      title: "请输入密码"
    }
  ]
});
const dataForm = ref(<any>{});
// 当前显示类型

const init = (data: any) => {
  visible.value = true;
  dataForm.value = { ...data };
};

// 提交回复
const replyLoading = ref(false);
const dataFormSubmitHandle = async (formEl: FormInstance | undefined) => {
  //编辑
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      baseService.put('/script/sysscriptpartnerinfo',{ ...dataForm.value }).then((res) => {
        if (res.code == 0) {
          ElMessage.success("修改成功");
          emits("refresh");
          closeDialog();
        }
      });
    }
  });
};

// 关闭
const closeDialog = () => {
  visible.value = false;
  emits("close");
};

defineExpose({
  init
});
</script>
<style scoped lang="scss">
.tipinfo {
  margin-bottom: 12px;

  :deep(.el-descriptions__label) {
    width: 144px;
    background: #f5f7fa;
    font-family: Inter, Inter;
    font-weight: 500;
    font-size: 14px;
    color: #606266;
    padding: 9px 12px;
    border: 1px solid #ebeef5;
  }
}
.el-form {
  margin-bottom: 12px;
  display: block;
  .el-form-item {
    display: block;
  }
}
</style>
<style lang="scss">
.pushGooddialog {
  .el-dialog__header {
    padding-bottom: 16px;
  }
  .el-dialog__title {
    font-weight: bold;
    font-size: 18px;
    color: #303133;
  }
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
