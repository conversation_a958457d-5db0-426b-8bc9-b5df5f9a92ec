<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="40%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row>
        <el-col :span="11">
          <el-form-item label="游戏名称" prop="gameId">
            <el-select v-model="dataForm.gameId" @change="getSysgame">
              <el-option v-for="item in gamesList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="系统区服" prop="server">
            <template #label>
              <div class="flx-center">
                <span style="margin-right: 4px">系统区服</span>
                <el-tooltip effect="dark" content="若未选择系统区服，默认提交当前游戏的所有区服。" placement="top">
                  <el-icon size="16"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-select v-model="dataForm.server" placeholder="系统区服" :disabled="!dataForm.gameId">
              <el-option v-for="item in sysgameList" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="降价周期" prop="period">
            <el-input v-model="dataForm.period" :controls="false" type="number" placeholder="降价周期">
              <template #append>天</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="降价比例" prop="percent">
            <el-input v-model="dataForm.percent" :controls="false" type="number" placeholder="降价比例">
              <template #append>%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :loading="btnLoading" @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button :loading="btnLoading" type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const gamesList = ref(<any>[]); // 游戏列表
const sysgameList = ref(<any>[]); // 游戏区服信息 - 服务器

const dataForm = reactive({
  id: null,
  gameId: "",
  server: null,
  period: "",
  percent: ""
});

const rules = ref({
  gameId: [{ required: true, message: "游戏名称不能为空", trigger: "change" }],
  period: [{ required: true, message: "属性名称不能为空", trigger: "blur" }],
  percent: [{ required: true, message: "属性名称不能为空", trigger: "blur" }]
});

const init = (id?: any) => {
  visible.value = true;
  dataForm.id = id ? id : null;
  getGamesList();
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/shop/shopjob/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    getSysgame();
  });
};

// 获取游戏列表
const getGamesList = () => {
  baseService.get("/game/sysgame/listGames").then((res) => {
    gamesList.value = res.data;
  });
};

// 获取游戏区服
const getSysgame = () => {
  baseService.get("/game/sysgame/get/" + dataForm.gameId).then((res) => {
    sysgameList.value = res.data.areaDtoList;
  });
};

// 表单提交
const btnLoading = ref(false);
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    btnLoading.value = true;
    (!dataForm.id ? baseService.post : baseService.put)("/shop/shopjob", dataForm)
      .then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};

defineExpose({
  init
});
</script>

<style lang='less' scoped>
</style>