<template>
  <div>
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px">
      <div style="font-weight: bold; font-size: 16px; color: #303133">审批流程</div>
      <el-button v-if="state.hasPermission('flowable:flowable:save')" type="primary" @click="addOrUpdateHandle()">新增审核流程</el-button>
    </div>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column prop="name" label="流程名称" header-align="center" align="center"> </el-table-column>
      <el-table-column prop="type" label="流程类型" header-align="center" align="center">
        <template #default="scope">
          {{ scope.row.type == 1 ? "放款" : scope.row.type == 2 ? "退款" : scope.row.type == 3 ? "提现" : scope.row.type == 4 ? "销售放款" : scope.row.type == 5 ? "线下收款确认" : scope.row.type == 9 ? "其他收支" : "" }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="ipAdd2ress" label="阶段负责人" header-align="center" align="center">
        <!-- stage中stage为0的数据   -->
        <template #default="scope">
          {{ getStageuser(scope.row, 1) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="ipA3ddress" label="审批人类型" header-align="center" align="center">
        <!-- stage中stage不为0的数据的type集合字符串   -->
        <template #default="scope">
          {{ getStageuser(scope.row, 2) }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="ipAd5dress" label="阶段审批人" header-align="center" align="center">
        <!-- stage中stage不为0的数据的tlink-name集合字符串   -->
        <template #default="scope">
          {{ getStageuser(scope.row, 3) }}
        </template>
      </el-table-column>
      <el-table-column prop="isRemind" label="是否提醒" header-align="center" align="center">
        <template #default="scope">
          {{ scope.row.isRemind ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column width="160" prop="status" label="状态" header-align="center" align="center">
        <template v-slot="scope">
          <el-switch v-if="state.hasPermission('flowable:flowable:update')" active-text="启用" inactive-text="禁用" v-model="scope.row.status" :active-value="1" :inactive-value="0" :loading="scope.row.loading" @change="(e: any) => {updateStatus(e, scope.row)}"></el-switch>
          <span v-else>{{ scope.row.status == 0 ? "禁用" : "启用" }}</span></template
        >
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="fields" label="要求填写字段" header-align="center" width="240" align="center">
        <template #default="scope">
          {{ getStageuser(scope.row, 4) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="180">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('flowable:flowable:update')" type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ "编辑" }}</el-button>
          <el-button v-if="state.hasPermission('flowable:flowable:delete')" type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
      <!-- 空状态 -->
      <template #empty>
        <div style="padding: 68px 0">
          <img style="width: 170px" src="@/components/ny-table/src/components//noResult.png" alt="" />
        </div>
      </template>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import baseService from "@/service/baseService";
import AddOrUpdate from "./auditFlowSet-add-or-update.vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const view = reactive({
  getDataListURL: "/flowable/flowable/page",
  getDataListIsPage: true,
  deleteURL: "/flowable/flowable",
  deleteIsBatch: true,
  dataForm: {}
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 禁用&启用
const updateStatus = (status: any, row: any) => {
  if (!row.id) return;
  let form = { ...row };
  form.status = status;
  // 处理数据

  form.stage.forEach((element) => {
    // 处理用户或角色数据
    element.link = element.link.map((ele) => ele.linkId);
  });
  row.loading = true;
  baseService
    .put("/flowable/flowable", form)
    .then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500
      });
      state.getDataList();
    })
    .finally(() => {
      row.loading = false;
    });
};
const getStageuser = (data: any, type: Number) => {
  let nameArr: any = [];
  if (type == 1) {
    let obj = data.stage?.find((ele) => ele.stage == 0);
    nameArr = obj.link?obj.link.map((ele) => ele.name):[];
  } else if (type == 2) {
    let typeArr = ["", "默认审批人", "指定审批人"];
    let objArr = data.stage?.filter((ele) => ele.stage != 0);
    nameArr = objArr.map((ele) => typeArr[+ele.type]);
  } else if (type == 3) {
    let objArr = data.stage?.filter((ele) => ele.stage != 0);
    objArr.forEach((ele) => {
      let linkArr = ele.link?ele.link.map((e) => e.name):[];
      nameArr = [...nameArr, ...linkArr];
    });
  } else if (type == 4) {
    let typeArr = ["", "金额", "收款账户", "原因", "备注"];
    nameArr = data.fields?.map((ele) => typeArr[+ele]);
  }
  nameArr = nameArr.filter((ele) => ele);
  return nameArr.join("，");
};
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
