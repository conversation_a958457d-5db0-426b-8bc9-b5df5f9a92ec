<template>
    <el-dialog v-model="visible" title="数据联动"  width="758" @close="closeFn">
        <el-form :model="dataForm" label-position="top" :rules="rules" ref="dataFormRef">
            <el-form-item label="联动表单" prop="sourceIndex">
                <el-tree-select
                    v-model="dataForm.sourceIndex"
                    :data="menuList"
                    :props="{label:'title', value: 'indexName'}"
                    :render-after-expand="false"
                    placeholder="请选择联动表单"
                    style="width: 100%"
                    @node-click="menuChange"
                />
            </el-form-item>
            <div class="card">
                <div class="header">当条件规则满足时</div>
                <div class="cont">
                    <div class="lines" v-for="(item,index) in ruleList">
                        <el-select v-model="item.sourceColumn" placeholder="请选择字段" style="width: 240px;" @focus="sourceFocus" @change="(value:any)=>{sourceChange(value,index)}">
                            <el-option
                                v-for="(ele,ind) in sourceList" 
                                :key="ind" 
                                :label="`${ele.name}(${ele.type == 1 ? '文本' : '数值'})`" 
                                :value="ele.dynamicKey"
                            />
                        </el-select>
                        <span>等于</span>
                        <el-select v-model="item.targetColumn" placeholder="请选择字段" style="width: 240px;" @change="(value:any)=>{targetChange(value,index)}">
                            <el-option
                                v-for="(ele,ind) in targetList" 
                                :key="ind" 
                                :label="`${ele.name}(${ele.type == 1 ? '文本' : '数值'})`" 
                                :value="ele.dynamicKey"
                            />
                        </el-select>
                        <el-icon color="#4165D7" size="20" style="margin-left: 12px;cursor: pointer;" 
                            @click="ruleAdd"><CirclePlusFilled />
                        </el-icon>
                        <el-popconfirm
                            width="220"
                            confirm-button-text="删除"
                            cancel-button-text="取消"
                            title="确定删除当前数据吗？"
                            @confirm="ruleDelete(index)"
                        >
                            <template #reference>
                                <el-icon color="#F56C6C" size="20" style="margin-left: 12px;cursor: pointer;" 
                                    v-if="index == (ruleList.length -1)&& index != 0"><DeleteFilled />
                                </el-icon>
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </div>
            </div>
            <div class="card" style="margin-top: 12px;">
                <div class="header">触发以下联动</div>
                <div class="cont">
                    <div class="lines">
                        <div>
                            <el-form-item label="当前表单字段" style="margin-bottom: 0px;">
                                <el-input v-model="fieldName" style="width: 240px" disabled />
                            </el-form-item>
                        </div>
                        
                        <div class="title" style="margin: 32px 12px 0px 12px;">联动显示为</div>
                        <div>
                            <el-form-item label="联动表单字段" prop="sourceColumn" style="margin-bottom: 0px;">
                                <el-select v-model="dataForm.sourceColumn" placeholder="请选择字段" style="width: 240px;" @change="sourceColumnChange" @focus="sourceFocus">
                                    <el-option
                                        v-for="(ele,ind) in sourceList" 
                                        :key="ind" 
                                        :label="`${ele.name}`" 
                                        :value="ele.dynamicKey"
                                    />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="title" style="margin: 20px 0px 0px 12px;">的对应值</div>
                    </div>
                </div>
            </div>
        </el-form>
        
        <template #footer>
            <el-button @click="visible = false;closeFn()">取消</el-button>
            <el-button type="primary" @click="submit">提交</el-button>
        </template>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";

const emit = defineEmits(["dataLinkageChange"]);

const visible = ref(false);
const dataFormRef = ref();
const dataForm = ref({
    sourceColumn: '',
    sourceIndex: null,
    targetIndex: '',
})

const rules = ref({
    sourceIndex: [{ required: true, message: '请选择联动表单', trigger: 'change' },],
    sourceColumn: [{ required: true, message: '请选择联动表单字段', trigger: 'change' },],
});

const ruleList = ref(<any>[]);

// 菜单点击
const menuChange = (e:any) =>{
    if(e.pid != '0'){
        getSourceName(e.indexName);
    }
}

// 获取来源字段
const sourceList = ref(<any>[]);
const getSourceName = (indexName:any) =>{
    baseService.get('/report/reportcolumn/all',{indexName:indexName}).then(res=>{
        sourceList.value = res.data.filter((item:any) => item.type == 1 || item.type == 2 );
    })
}

// 来源获取焦点时触发
const sourceFocus = () =>{
    if(!sourceList.value.length){
        ElMessage.warning('请选择联动表单!')
    }
}
// 来源字段选择
const sourceChange = (value:any,index:number) =>{
    const sourceInfo = sourceList.value.find((item:any) => item.dynamicKey == value);
    ruleList.value[index].sourceInfo = sourceInfo;
    if(ruleList.value[index].targetInfo){
        if(ruleList.value[index].sourceInfo.type != ruleList.value[index].targetInfo.type){
            ElMessage.warning('条件规则的字段类型必须一致!');
            ruleList.value[index].sourceColumn = ''
        }
    }
}

// 目标字段
const targetList = ref(<any>[]); // 目标（当前表）
const targetChange = (value:any,index:number) =>{
    const targetInfo = targetList.value.find((item:any) => item.dynamicKey == value);
    ruleList.value[index].targetInfo = targetInfo;
    if(ruleList.value[index].sourceInfo){
        if(ruleList.value[index].sourceInfo.type != ruleList.value[index].targetInfo.type){
            ElMessage.warning('条件规则的字段类型必须一致!');
            ruleList.value[index].targetColumn = ''
        }
    }
}

// 获取表格目录
const menuList = ref(<any>[]);
const getAllTables = () => {
    baseService.get('/report/report/all/table').then(res=>{
        menuList.value = res.data;
    })
}

// 联动表格类型判断
const sourceColumnChange = (value:any) =>{
    const sourceInfo = sourceList.value.find((item:any) => item.dynamicKey == value);
    if(sourceInfo.type != fieldType.value){
        ElMessage.warning('联动表单字段与当前表单字段必须一致!');
        dataForm.value.sourceColumn = '';
    }
}


// 新增 - 规则
const ruleAdd = () =>{
    ruleList.value.push({
        sourceColumn: '',
        sourceInfo: <any>'',
        targetColumn: '',
        targetInfo: <any>'',
    })
}
// 删除 - 规则
const ruleDelete = (index:number) =>{
    ruleList.value.splice(index,1);
}

// 提交
const submit = () =>{
    dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
            return false;
        }
        const rule = {
            ruleList: ruleList.value,
            sourceIndex: dataForm.value.sourceIndex,
            targetIndex: dataForm.value.targetIndex,
            sourceColumn: dataForm.value.sourceColumn
        }
        emit('dataLinkageChange',rule);
        visible.value = false;
        console.log(rule,'====== rule ======');
    })
}


// 弹窗关闭回调
const closeFn = () =>{
    ruleList.value = [];
}

// 字段名称
const fieldName = ref('');
const fieldType = ref(1);
const init = (indexName: any, name:any, type:any , rule:any) => {
    fieldType.value = type;

    // 重置表单数据
    if (dataFormRef.value) {
        dataFormRef.value.resetFields();
    }

    dataForm.value.targetIndex = indexName;
    fieldName.value = name;
    visible.value = true;
    // 获取目标字段
    baseService.get('/report/reportcolumn/all',{indexName:indexName}).then(res=>{
        targetList.value = res.data.filter((item:any) => item.type == 1 || item.type == 2 );
    })
    getAllTables();
    if(rule && Object.keys(rule).length){
        Promise.all([getSourceName(rule.sourceIndex)]).then(() => {
            ruleList.value = rule.ruleList;
            dataForm.value.sourceIndex = rule.sourceIndex;
            dataForm.value.targetIndex = rule.targetIndex;
            dataForm.value.sourceColumn = rule.sourceColumn;
        });
        
    }else{
        ruleAdd();
    }
};

defineExpose({
    init
});
</script>

<style lang='less' scoped>
.card{
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    .header{
        background: #F0F2F5;
        font-weight: 500;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
        padding: 12px;
    }
    .cont{
        padding: 12px;
        .lines{
            display: flex;
            align-items: center;
            span{
                font-weight: 400;
                font-size: 14px;
                color: #A8ABB2;
                line-height: 22px;
                margin: 0px 10px;
            }
        }
        .lines + .lines{
            margin-top: 12px;
        }
    }
}
.title{
    font-weight: 400;
    font-size: 13px;
    color: #606266;
    line-height: 22px;
}
</style>