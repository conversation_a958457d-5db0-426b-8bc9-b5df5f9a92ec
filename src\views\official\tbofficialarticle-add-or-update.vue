<template>
    <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-form-item label="标题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="副标题" prop="subtitle">
                        <el-input v-model="dataForm.subtitle" placeholder="副标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="内容" prop="content" style="height: 400px">
                        <WangEditor v-model="dataForm.content"></WangEditor>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="封面" prop="cover">
                        <ny-upload v-model:imageUrl="dataForm.cover" :limit="1"></ny-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="类型" prop="type">
                        <ny-select v-model="dataForm.type" dict-type="official_article_type" placeholder="类型"></ny-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="发布时间" prop="releaseTime">
                        <el-date-picker type="datetime" placeholder="发布时间" v-model="dataForm.releaseTime" format="YYYY-MM-DD hh:mm:ss" value-format="YYYY-MM-DD hh:mm:ss"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="排序" prop="sort">
                        <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template v-slot:footer>
            <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import WangEditor from "@/components/wang-editor/index.vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  //id
  id:"",
  //标题
  title: "",
  //副标题
  subtitle: "",
  //内容
  content: "",
  //封面
  cover: "",
  //类型 1：企业方案 2：最新公告 3：新闻中心 【字典：official_article_type】
  type: "",
  //发布时间
  releaseTime: "",
  //排序
  sort: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
      dataFormRef.value.resetFields();
  }

  if (id) {
      getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/official/tbofficialarticle/" + id).then((res) => {
      Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
          return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/official/tbofficialarticle", dataForm).then((res) => {
          ElMessage.success({
              message: t("prompt.success"),
              duration: 500,
              onClose: () => {
                  visible.value = false;
                  emit("refreshDataList");
              }
          });
      });
  });
};

defineExpose({
  init
});
</script>
