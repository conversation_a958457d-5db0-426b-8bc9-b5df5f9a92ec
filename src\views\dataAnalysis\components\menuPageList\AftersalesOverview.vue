<template>
    <div class="business_page">
        <div class="business_header flx-align-center">
            <el-radio-group v-model="dataForm.type" @change="radioChange">
                <el-radio value="1">时间</el-radio>
                <el-radio value="2">游戏</el-radio>
            </el-radio-group>
        </div>
        <div class="card_list" style="margin-top: 12px;">
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>回收售后：新增售后订单</span>
                        <el-tooltip effect="dark" content="商品列表新生成的问题账号数量" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ AfterSalesOverview.newAfterSaleProblemAccount }}</div>
                <div class="item_footer">
                    <div class="box">退款订单：{{ AfterSalesOverview.refundOrder }}</div>
                    <div class="box">亏损订单：{{ AfterSalesOverview.lossOrder }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,254,234,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>回收售后：新增问题账号</span>
                        <el-tooltip effect="dark" content="新生成的售后原因包含【账号找回】的所有售后订单数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ AfterSalesOverview.newAfterSaleRetrieveAccount }}</div>
                <div class="item_footer">
                    <div class="box">作废账号：{{ AfterSalesOverview.voidAccount }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(230,244,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>销售售后：新增售后订单</span>
                        <el-tooltip effect="dark" content="销售订单新生成的售后订单数量" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ AfterSalesOverview.newSalesAfterSaleOrder }}</div>
                <div class="item_footer">
                    <div class="box">退款订单：{{ AfterSalesOverview.salesRefundOrder }}</div>
                    <div class="box">亏损订单：{{ AfterSalesOverview.salesLossOrder }}</div>
                </div>
            </div>
            <div class="card_list_item"
                style="background: linear-gradient( 174deg, rgba(232,230,254,0.84) 0%, rgba(217,224,247,0.17) 100%), #FFFFFF;">
                <div class="item_header">
                    <div class="flx-align-center">
                        <span>销售售后：新增找回账号</span>
                        <el-tooltip effect="dark" content="新生成的【找回账号】+【疑似找回】售后订单数据" placement="top-end">
                            <img src="/src/assets/icons/svg/question-line.svg"
                                style="width: 14px;height: 14px;margin-left: 4px;">
                        </el-tooltip>
                    </div>
                </div>
                <div class="item_center">{{ AfterSalesOverview.newSalesRetrieveAccount }}</div>
                <div class="item_footer">
                    <div class="box">作废账号：{{ AfterSalesOverview.salesVoidAccount }}</div>
                </div>
            </div>
        </div>
        <div class="card_analysis mt-12" style="background: #F7F8FA;">
            <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
                <div class="header_analysis_left flx-align-center">
                    <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">新增售后</div>
                </div>
                <div class="header_analysis_right flx-align-center">
                    <div class="legend">
                        <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData"
                            :key="index" @change="changeShow"></el-checkbox>
                    </div>
                    <el-date-picker v-model="dateTime" type="daterange" start-placeholder="开始时间"
                        end-placeholder="结束时间" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        style="width: 220px; border-radius: 20px" @change="getAddAfterSalesStatisticalChart"/>
                </div>
            </div>
            <div class="header_describe">新增回收售后与销售售后数据</div>
            <div class="charts">
                <div :style="`width: 100%; height: 100%;zoom:${1/echartsZoom};transform:scale(${1});transform-origin:0 0;`" ref="analysisChartRef"></div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref, watch, onMounted } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";
import { formatDate } from "@/utils/method";

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    salesChannelId: "",
    startTime: "",
    endTime: "",
    type: "1"
});

const dateTime = ref()

// 图例数据
const legendData = ref([
    { name: '回收售后订单', color: '#4165D7', show: true },
    { name: '销售售后订单', color: '#722ED1', show: true },
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '回收售后订单',
        type: 'line',
        itemStyle: {
            color: "#4165D7"
        },
        smooth: true,
        lineStyle: {
            width: 3,
            color: "#4165D7"
        },
        areaStyle: {
            opacity: 0.5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#FFFFFF" },
                { offset: 0, color: "#64A2FF " }
            ])
        },
        
        data: [],
    },
    {
        name: '销售售后订单',
        type: 'line',
        itemStyle: {
            color: "#722ED1"
        },
        smooth: true,
        lineStyle: {
            width: 3,
            color: "#722ED1"
        },
        areaStyle: {
            opacity: 0.5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#FFFFFF" },
                { offset: 0, color: "#8364FF " }
            ])
        },
        data: []
    }
])

// 游戏库存折线图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '6%',
            right: '3%',
            top: '4%',
            bottom: '16%',
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: seriesList,
        dataZoom: [{
            type: 'slider',
            start: 0,
            end: 40,
            bottom: "2%",
            height: 15
        }]
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
    try {
        let sliderZoom = (userGrowthChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')
        let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 4
        let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 4
        sliderZoom._displayables.handleLabels[0].x = leftP
        sliderZoom._displayables.handleLabels[1].x =  rightP
        
    } catch (error) {
        
    }
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });
    GameSalesStatisticsChart(filteredSeries);
}

const echartsZoom = ref('1');

onMounted(() => {
    var now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    dateTime.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
    getAfterSaleDetail();
    getAddAfterSalesStatisticalChart();

    const elements = document.getElementsByClassName('rr-view');
    Array.from(elements).forEach(element => {
        const computedZoom = window.getComputedStyle(element).zoom;
        echartsZoom.value = computedZoom
    });
})

// 售后概况
const AfterSalesOverview = reactive({
    newAfterSaleProblemAccount: 0,
    refundOrder: 0,
    lossOrder: 0,
    newAfterSaleRetrieveAccount: 0,
    voidAccount: 0,
    newSalesAfterSaleOrder: 0,
    salesRefundOrder: 0,
    salesLossOrder: 0,
    newSalesRetrieveAccount: 0,
    salesVoidAccount: 0
})
const getAfterSaleDetail = () =>{
    baseService.post("/dataAnalysis/afterSaleDetail",dataForm.value).then(res=>{
        Object.assign(AfterSalesOverview,res.data);
    })
}

// 新增统计图
const getAddAfterSalesStatisticalChart = () =>{
    dataForm.value.startTime = dateTime.value ? dateTime.value[0] + " 00:00:00" : "";
    dataForm.value.endTime = dateTime.value ? dateTime.value[1] + " 23:59:59" : "";
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/addAfterSalesStatisticalChart",dataForm.value).then(res=>{
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

const radioChange = () => {
    getAfterSaleDetail();
    getAddAfterSalesStatisticalChart();
}

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    console.log(form);
    
    getAfterSaleDetail();
    getAddAfterSalesStatisticalChart();
};

defineExpose({
    init
});
</script>

<style lang='less' scoped>
.business_page {
    margin-top: 12px;
}

.page_title {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
    line-height: 28px;
    margin-top: 12px;
}

.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #4165D7;
                        border-color: #4165D7;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #722ED1;
                        border-color: #722ED1;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 360px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.analysis_type_item {
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    line-height: 22px;
    padding: 3px 12px;
    background: #FFFFFF;
    border-radius: 24px;
    border: 1px solid #D4D7DE;
}

.card_list {
    display: flex;
    align-items: center;
    gap: 12px;

    .card_list_item {
        flex: 1;
        padding: 16px 20px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;

        .item_header {
            font-weight: bold;
            font-size: 16px;
            color: #1C1C1C;
            line-height: 20px;
            
            display: flex;
            align-items: center;
            justify-content: space-between;
            .month{
                font-weight: 500;
                font-size: 14px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }

        .item_center {
            font-weight: bold;
            font-size: 24px;
            color: #1C1C1C;
            line-height: 36px;
            margin: 10px 0px;
        }

        .item_footer {
            display: flex;
            align-items: center;

            .box {
                flex: 1;
                font-weight: 400;
                font-size: 13px;
                color: #1C1C1C;
                line-height: 20px;
            }
        }
    }
}
</style>