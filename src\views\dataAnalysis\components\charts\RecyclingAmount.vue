<template>
    <div class="card_analysis mt-12" style="background: #F7F8FA;">
        <div class="header_analysis" style="padding: 22px 20px 6px 20px;">
            <div class="header_analysis_left flx-align-center">
                <div class="header_analysis_title" style="font-size: 20px;margin-left: 0px;">回收额当月环比</div>
            </div>
            <div class="header_analysis_right flx-align-center">
                <div class="legend">
                    <el-checkbox v-model="item.show" :label="item.name" v-for="(item, index) in legendData" :key="index"
                        @change="changeShow"></el-checkbox>
                </div>
            </div>
        </div>
        <div class="header_describe">回收明细数据</div>
        <div class="charts">
            <div style="width: 100%; height: 100%" ref="analysisChartRef"></div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { nextTick, reactive, ref, watch, onMounted } from "vue";
import * as echarts from "echarts";
import baseService from "@/service/baseService";

const dataForm = ref({
    gameId: "",
    recyclingChannelId: "",
    startTime: "",
    endTime: "",
    type: "1"
});

// 图例数据
const legendData = ref([
    { name: '当月', color: '#F77234', show: true },
    { name: '上月', color: '#0FC6C2', show: true },
])

const analysisChartRef = ref(null);
const charts = ref(<any>[]);
const seriesList = ref([
    {
        name: '当月',
        type: 'line',
        itemStyle: {
            color: "#F77234"
        },
        smooth: true,
        lineStyle: {
            width: 3,
            color: "#F77234"
        },
        data: [],
    },
    {
        name: '上月',
        type: 'line',
        itemStyle: {
            color: "#0FC6C2"
        },
        smooth: true,
        lineStyle: {
            width: 3,
            color: "#0FC6C2"
        },
        data: []
    }
])

// 游戏库存折线图
const optionX = ref();
const GameSalesStatisticsChart = (seriesList: any, color?: any) => {
    if (charts.value.length > 0) {
        charts.value[0].dispose();
        charts.value = [];
    }
    const userGrowthChart = echarts.init(analysisChartRef.value);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '10%',
            right: '6%',
            top: '4%',
            bottom: '12%',
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: optionX.value
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: seriesList
    };
    userGrowthChart.setOption(option);
    charts.value.push(userGrowthChart);
};

const changeShow = () => {
    const filteredSeries = seriesList.value.filter((_, index) => {
        return legendData.value[index].show;
    });

    const color = legendData.value.filter((item: any) => item.show).map((item: any) => item.color)

    GameSalesStatisticsChart(filteredSeries, color);
}


onMounted(() => {
    getSalesRankingMonthRatio();
})

// 订单量当月环比
const getSalesRankingMonthRatio = () =>{
    legendData.value.map((item:any)=> item.show = true);
    baseService.post("/dataAnalysis/monthOnMonthRecycle",dataForm.value).then(res=>{
        if (res.code == 0) {
            optionX.value = res.data.x;
            seriesList.value.map((i) => {
                res.data.y.map((j) => {
                    if (i.name == j.name) {
                        i.data = j.data;
                    }
                });
            });
            GameSalesStatisticsChart(seriesList.value);
        }
    })
}

const init = (form: any) => {
    Object.assign(dataForm.value, form);
    getSalesRankingMonthRatio();
};

defineExpose({
    init
});

</script>

<style lang='less' scoped>
.card_analysis {
    width: 100%;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;

    .header_analysis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 14px 20px;

        .header_analysis_left {
            .header_analysis_title {
                font-weight: 500;
                font-size: 16px;
                color: #1d252f;
                line-height: 24px;
                margin-left: 4px;
            }
        }

        .header_analysis_right {
            .legend {
                margin-right: 16px;

                :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
                    color: #1D2129;
                }

                .el-checkbox:nth-child(1) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #F77234;
                        border-color: #F77234;
                    }
                }

                .el-checkbox:nth-child(2) {
                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #0FC6C2;
                        border-color: #0FC6C2;
                    }
                }
            }
        }
    }

    .header_describe {
        font-weight: 400;
        font-size: 13px;
        color: #4E5969;
        line-height: 22px;
        padding: 0px 20px;
    }

    .charts {
        width: 100%;
        height: 200px;
        padding-bottom: 20px;
        margin-top: 16px;
    }

    .center_analysis {
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .listMap {
            width: 200px;

            .listMap_label {
                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 22px;
                    margin-right: 2px;
                }
            }

            .listMap_value {
                font-weight: 500;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
</style>