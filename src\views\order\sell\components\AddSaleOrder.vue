<template>
    <el-drawer @close="visible = false" :footer="null" v-model="visible" size="944" class="infoDrawer">
        <template #header>
            <div class="drawer_title">{{ !dataForm.id ? '新增' : '编辑' }}合作商订单</div>
        </template>
        <el-scrollbar v-loading="requestLoading">
            <el-form :model="dataForm" :rules="rules" ref="formRef">
                <div class="basicInfoSty">
                    <div class="title">商品信息</div>
                    <el-descriptions class="descriptions descriptions-label-140" :column="2" size="default" border>
                        <el-descriptions-item>
                            <template #label>
                                <div>游戏名称<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="gameId">
                                <el-select v-model="dataForm.gameId" filterable placeholder="请选择" @change="gameChange">
                                    <el-option v-for="(item, index) in gamesList" :key="index" :label="item.title"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>出售渠道<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="channelId">
                                <el-cascader style="width: 100%" :show-all-levels="false"
                                    v-model="dataForm.channelId" :options="ChannelTreeList"
                                    :props="{ label: 'title', value: 'id' }" placeholder="请选择" @change="handleChange"/>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>商品编码<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="shopCode">
                                <el-input v-model="dataForm.shopCode" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>游戏区服<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="serverId">
                                <el-cascader style="width: 100%" :show-all-levels="false"
                                    v-model="dataForm.serverId" :options="sysgameList"
                                    :props="{ label: 'title', value: 'id' }" placeholder="请选择" @change="serverChange"/>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>游戏账号<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="gameAccount">
                                <el-input v-model="dataForm.gameAccount" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>成交价(元)<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="dealAmount">
                                <el-input-number v-model="dataForm.dealAmount" controls-position="right" min="0" style="width: 100%;" placeholder="请输入"/>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>售出人<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="saleUserId">
                                <ny-select-search v-model="dataForm.saleUserId" labelKey="realName" valueKey="id"
                                    url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择"
                                    @changeReturnEntity="saleUserChange"
                                    style="width: 39%" />
                            </el-form-item>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions class="descriptions descriptions-label-140" :column="1" size="default" border>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>商品标题<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="shopTitle">
                                <el-input v-model="dataForm.shopTitle" type="textarea" placeholder="请输入"
                                    :rows="4"></el-input>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>商品主图<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="mainImg">
                                <ny-upload v-model:imageUrl="dataForm.mainImg" :limit="1" :fileSize="5"
                                    accept="image/*"></ny-upload>
                            </el-form-item>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="basicInfoSty">
                    <div class="title">付款信息</div>
                    <el-descriptions class="descriptions descriptions-label-140" :column="2" size="default" border>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>三方订单编号<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="thirdPartyOrderCode">
                                <el-input v-model="dataForm.thirdPartyOrderCode" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>应收金额(元)<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="receivableMoney">
                                <el-input-number v-model="dataForm.receivableMoney" controls-position="right" min="0" style="width: 100%;" placeholder="请输入"/>
                                <!-- <el-input v-model="dataForm.receivableMoney" type="number" onkeypress="return event.charCode >= 48" min="0" placeholder="请输入"></el-input> -->
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>付款方式<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="accountType">
                                <el-select v-model="dataForm.accountType" placeholder="请选择" @change="accountTypeChange">
                                    <el-option v-for="item in accountTypeList" :key="item.value" :label="item.name" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title" v-if="dataForm.accountType == 1">
                            <template #label>
                                <div>支付宝订单号</div>
                            </template>
                            <el-form-item prop="alipayNo">
                                <el-input v-model="dataForm.alipayNo" type="number" onkeypress="return event.charCode >= 48" min="0" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-descriptions-item>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>收款帐户<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="account">
                                <!-- <el-input v-model="dataForm.accountName" type="number" placeholder="请输入" :style="{ width: dataForm.accountType == 1 ? '39%' : '100%' }"></el-input> -->
                                <el-select :style="{ width: dataForm.accountType == 1 ? '39%' : '100%' }" v-model="dataForm.account" placeholder="请选择">
                                    <el-option v-for="item in accountList" :key="item.account"
                                        :label="item.name" :value="item.account" />
                                </el-select>
                            </el-form-item>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions class="descriptions descriptions-label-140" title="" :column="1" size="default"
                        border>
                        <el-descriptions-item label-class-name="title">
                            <template #label>
                                <div>付款凭证<span style="color: red;">*</span></div>
                            </template>
                            <el-form-item prop="payCredentials">
                                <ny-upload v-model:imageUrl="dataForm.payCredentials" :limit="1" :fileSize="5"
                                    accept="image/*"></ny-upload>
                            </el-form-item>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-form>
        </el-scrollbar>
        <template #footer>
            <el-button :loading="requestLoading" @click="visible = false">取消</el-button>
            <el-button :loading="requestLoading" type="primary" @click="submitForm()">确定</el-button>
        </template>
    </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
const emits = defineEmits(["refresh"]);

const visible = ref(false); // 弹窗变量
const requestLoading = ref(false); // 数据加载
const gamesList = ref(<any>[]);// 游戏列表

const dataForm = ref(<any>{
    accountType: '1',
    accountName: '支付宝转账'
});
const rules = reactive({
    gameId: [{ required: true, message: "请选择游戏名称", trigger: "blur" }],
    channelId: [{ required: true, message: "请选择回收渠道", trigger: "blur" }],
    shopCode: [{ required: true, message: "请输入商品编码", trigger: "change" }],
    serverId: [{ required: true, message: "请选择游戏区服", trigger: "blur" }],
    gameAccount: [{ required: true, message: "请输入游戏账号", trigger: "change" }],
    saleUserId: [{ required: true, message: "请选择出售人", trigger: "blur" }],
    shopTitle: [{ required: true, message: "请输入商品标题", trigger: "blur" }],
    mainImg: [{ required: true, message: "请上传商品主图", trigger: "blur" }],
    dealAmount: [{ required: true, message: "请输入成交价", trigger: "blur" }],
    thirdPartyOrderCode: [{ required: true, message: "请输入三方订单编号", trigger: "blur" }],
    receivableMoney: [{ required: true, message: "请输入应收金额", trigger: "blur" }],
    accountType: [{ required: true, message: "请选择付款方式", trigger: "change" }],
    // alipayNo: [{ required: true, message: "请输入支付宝订单号", trigger: "blur" }],
    account: [{ required: true, message: "请选择收款帐户", trigger: "change" }],
    payCredentials: [{ required: true, message: "请上传付款凭证", trigger: "change" }],
});

const accountTypeList = ref([
    { value: '1', name: '支付宝转账' },
    { value: '2', name: '微信转账' },
    { value: '3', name: '银行卡转账' }
])

// 表单初始化
const init = async (id?: any) => {
    visible.value = true;
    dataForm.value.id = id

    // 重置表单数据
    if (formRef.value) {
        formRef.value.resetFields();
    }
    getGamesList();
    getChannelTree();
    getAccountList();
};

// 获取表单详情
const getDetails = () =>{
    baseService.get("/partnerSaleOrder/getOrder/" + dataForm.value.id).then(res=>{
        dataForm.value = res.data
        dataForm.value.mainImg = res.data.log;
        const channelIds = findNodePath(ChannelTreeList.value,dataForm.value.channelId);
        dataForm.value.channelId = channelIds.map((item:any)=> item.id);
        getSysgame();
    })
}

// 获取游戏列表
const getGamesList = () => {
    baseService.get("/game/sysgame/listGames").then((res) => {
        gamesList.value = res.data;
        dataForm.value.gameId = res.data[0].id;
        dataForm.value.gameName = res.data[0].title;
        getSysgame();
    });
};

// 选择游戏
const gameChange = (e:any) => {
    dataForm.value.serverId = "";
    dataForm.value.gameName = gamesList.value.find((item:any)=> item.id == e).title;
    getSysgame();
};

// 获取出售渠道
const ChannelTreeList = ref(<any>[])
const getChannelTree = () => {
    baseService.get("/channel/channel/getChannelTree", { type: '0' }).then(res => {
        res.data.map((item: any) => {
            if (item.children.length == 0) {
                item.disabled = true;
            }
        })
        ChannelTreeList.value = res.data;

        if(dataForm.value.id){
            getDetails();
        }
    })
}
// 选择渠道
const handleChange = (selectedData: any) => {
  if (selectedData && selectedData.length > 0) {
    dataForm.value.channelId = selectedData[selectedData.length - 1];
    baseService.get("/channel/channel/" + dataForm.value.channelId).then(res=>{
      if(res.data){
        dataForm.value.channelName = res.data.title
      }
    })
  } else {
    dataForm.value.channelId = "";
    dataForm.value.channelName = "";
  }
};

// 获取游戏区服
const sysgameList = ref(<any>[]);
const getSysgame = () => {
    baseService.get("/game/sysgame/get/" + dataForm.value.gameId).then((res) => {
        sysgameList.value = res.data.areaDtoList || [];
        // 编辑时回显区服
        if(dataForm.value.id && !Array.isArray(dataForm.value.serverId)){
            const findList = findNodePath(sysgameList.value,dataForm.value.serverId);
            dataForm.value.serverId = findList.map((item:any)=> item.id);
        }
    });
};

// 选择区服
const serverChange = (selectedData: any) => {
    dataForm.value.serverId = selectedData[selectedData.length - 1];
    const findList = findNodePath(sysgameList.value,dataForm.value.serverId);
    dataForm.value.serverName = findList[findList.length - 1].title
  
};

const findNodePath = (tree:any, targetId:any, path = []) => {
  for (const node of tree) {
    const currentPath:any = [...path, node];
    if (node.id === targetId) return currentPath;
    if (node.children) {
      const found:any = findNodePath(node.children, targetId, currentPath);
      if (found) return found;
    }
  }
  return null;
};

// 付款方式选择
const accountTypeChange = (e:any) =>{
    dataForm.value.accountName = accountTypeList.value.find((item:any) => item.value == e)?.name
    dataForm.value.account = "";
    getAccountList();
}

// 获取账户
const accountList = ref(<any>[])
const getAccountList = () => {
  baseService.get("/wallet/tenantaccount/page", { limit: 9999, type: dataForm.value.accountType }).then((res) => {
    accountList.value = res.data.list || [];
  });
};

// 出售人回调
const saleUserChange = (e:any) =>{
    if(e){
        dataForm.value.saleUserName = e.realName
    }else{
        dataForm.value.saleUserName = ""
    }
}

const formRef = ref();
const submitForm = () => {
    formRef.value.validate((valid: boolean) => {
        if (!valid) return;
        dataForm.value.channelId = Array.isArray(dataForm.value.channelId) ? dataForm.value.channelId[dataForm.value.channelId.length - 1] : dataForm.value.channelId;
        dataForm.value.serverId = Array.isArray(dataForm.value.serverId) ? dataForm.value.serverId[dataForm.value.serverId.length - 1] : dataForm.value.serverId;
        requestLoading.value = true;
        baseService.post("/sale/createPartnerOrder", dataForm.value).then(res => {
            if (res.code == 0) {
                ElMessage.success("提交成功");
                emits("refresh");
                visible.value = false;
            }
        }).finally(() => {
            requestLoading.value = false
        })
    })
}

defineExpose({
    init
});
</script>

<style lang="less" scoped>
.basicInfoSty {
    padding: 12px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 12px;

    .title {
        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
        padding-left: 8px;
        border-left: 2px solid var(--el-color-primary);
        margin-bottom: 12px;
    }

    .tipinfo {
        :deep(.el-descriptions__label) {
            width: 144px;
            background: #f5f7fa;
            font-family: Inter, Inter;
            font-weight: 500;
            font-size: 14px;
            color: #606266;
            padding: 9px 12px;
            border: 1px solid #ebeef5;
        }
    }
}

:deep(.el-descriptions__body) {
    display: flex;
    justify-content: space-between;

    tbody {
        display: flex;
        flex-direction: column;

        tr {
            display: flex;
            flex: 1;

            .el-descriptions__label {
                display: flex;
                align-items: flex-start !important;
                font-weight: normal;
                width: 144px;
            }

            .el-descriptions__content {
                display: flex;
                align-items: center;
                min-height: 48px;
                flex: 1;

                >div {
                    width: 100%;
                }

                .el-form-item__label {
                    display: none;
                }

                .el-form-item {
                    margin-bottom: 0;
                }
            }

            .noneSelfRight {
                border-right: 0 !important;
            }

            .noneSelfLeft {
                border-left: 0 !important;
            }

            .noneSelfLabel {
                background: none;
                border-left: 0 !important;
                border-right: 0 !important;
            }
        }
    }
}

// :deep(.el-form-item) {
//     &.is-error {
//         .el-input__inner {
//             &::placeholder {
//                 color: var(--el-color-danger);
//             }
//         }

//         .el-select__placeholder {
//             color: var(--el-color-danger);
//         }

//         .el-form-item__error {
//             display: none !important;
//             opacity: 0 !important;
//         }
//     }

//     &.is-success {
//         .el-form-item__error {
//             transition: none !important;
//             opacity: 0 !important;
//         }
//     }
// }
</style>
<style lang="scss">
.infoDrawer {
    .el-drawer__header {
        margin-bottom: 0px;
        padding-bottom: 12px !important;
    }

    .drawer_title {
        font-weight: bold;
        font-size: 18px;
        color: #303133;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }

    .el-drawer__body {
        padding: 12px;
        background: #f0f2f5;
    }
}
</style>
