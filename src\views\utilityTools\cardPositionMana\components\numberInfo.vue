<template>
  <div style="width: 100%">
    <div class="title">手机号管理</div>
    <div class="TableXScrollSty">
      <ny-table :state="state" :columns="columns" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
        <template #header>
          <div style="display: flex; gap: 8px">
            <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
            <el-dropdown :disabled="!state.dataListSelections?.length">
              <el-button color="#409EFF" style="color: #fff" :loading="loading" :disabled="!state.dataListSelections?.length"
                >批量操作<el-icon class="el-icon--right"> <arrow-down /> </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOprate(1)">查询话费（短信）</el-dropdown-item>
                  <el-dropdown-item @click="handleOprate(2)">断线重启设备</el-dropdown-item>
                  <el-dropdown-item @click="handleOprate(3)">标记频繁</el-dropdown-item>
                  <el-dropdown-item @click="handleOprate(4)">Q绑查询</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <!-- <el-button type="success" @click="addOrUpdateHandle()">检测频繁</el-button> -->
            <el-button type="warning" @click="importShow = true">导入</el-button>
            <el-button type="info" @click="exportHandle()">导出</el-button>
            <el-button type="danger" :disabled="!state.dataListSelections || state.dataListSelections.length == 0" @click="deleteChange">{{ $t("deleteBatch") }}</el-button>
            <el-button @click="setUpChange">设置</el-button>
          </div>
        </template>
        <template #header-right>
          <div style="display: flex; gap: 8px">
            <el-input :prefix-icon="Search" style="width: 250px" v-model="state.dataForm.info" placeholder="请输入手机号/ICCID/主卡/卡位置" clearable></el-input>
            <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
            <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
            <el-button @click="filterHandle">高级筛选</el-button>
          </div>
        </template>
        <template #phone="{ row }">
          <span>{{ row.phone }}</span>
          <div class="CoolingDays" v-if="row.remainingFrequentlyDays > 0">剩余冷却{{ row.remainingFrequentlyDays }}天</div>
        </template>

        <template #deviceStatus="{ row }">
          <el-tag type="danger" v-if="row.deviceStatus == 0">未绑定</el-tag>
          <el-tag type="success" v-else>已绑定</el-tag>
        </template>
        <template #bindQq="{ row }">
          <el-tooltip :content="row.bindQq" placement="top" v-if="row.bindQq">
            <div style="display: flex; flex-direction: column; justify-content: center" v-if="row.bindQq">
              <div v-for="(item, index) in row.bindQq.split(',').splice(0, 3)" :key="index">{{ item }}</div>
            </div>
          </el-tooltip>
          <span v-else>-</span>
        </template>
        <template #bindMaxTime="{ row }">
          <template v-if="row.bindTime > row.bindMaxTime">
            <div style="display: flex; flex-direction: column; justify-content: center">
              <span style="color: red">{{ row.bindTime }}/{{ row.bindMaxTime }}</span>
              <span style="color: red">(存在超限风险)</span>
            </div>
          </template>
          <template v-else-if="row.bindTime <= row.bindMaxTime">
            <span>{{ row.bindTime || 0 }}/{{ row.bindMaxTime || 0 }}</span>
          </template>
          <span v-else>-</span>
        </template>
        <template #balance="{ row }">
          <span :style="{ color: Number(row.balance) < Number(threshold) ? 'red' : '' }">{{ row.balance }}</span>
        </template>
        <template #frequently="{ row }">
          <el-switch v-model="row.frequently" :active-value="1" :inactive-value="0" :loading="row.loading" @change="(e: any) => {updateStatus(e, row)}"></el-switch>
        </template>

        <!-- <template #frequently="{ row }">
        <span v-if="row.frequently == 0">否</span>
        <span v-if="row.frequently == 1">是</span>
      </template>
      <template #rent="{ row }">
        <span v-if="row.rent == 0">否</span>
        <span v-if="row.rent == 1">是</span>
      </template> -->
        <template #bindShopTotal="{ row }">
          <!-- <el-text type="primary" style="cursor: pointer" @click="phoneNumberShopChange(row.phone)">点击测试</el-text> -->
          <div v-if="row.bindShopTotal" style="cursor: pointer; width: 100%; height: 40px; display: flex; align-items: center; justify-content: center" @click="phoneNumberShopChange(row.phone)">
            <el-text type="primary">{{ row.bindShopTotal }}</el-text>
          </div>
          <span v-else>-</span>
        </template>
        <template #bindStatusName="{ row }">
          <el-text type="primary" v-if="row.bindStatusName == '空闲'">{{ row.bindStatusName }}</el-text>
          <el-text type="danger" v-else>{{ row.bindStatusName }}</el-text>
        </template>
        <template #onlineStatus="{ row }">
          <el-button link type="success" v-if="row.onlineStatus == 1">正常</el-button>
          <el-button link type="danger" v-if="row.onlineStatus == 2">异常</el-button>
          <el-button link type="warning" v-if="row.onlineStatus == 3">停用</el-button>
          <el-button link type="primary" v-if="row.onlineStatus == 4">沉默号</el-button>
          <el-button link type="info" v-if="row.onlineStatus == 5">空号</el-button>
          <el-button link type="danger" v-if="row.onlineStatus == 6">欠费</el-button>
        </template>
        <template #createDate="{ row }">
          <span>{{ row.createDate ? formatDate(row.createDate, undefined) : "-" }}</span>
        </template>
        <template #remark="{ row }">
          <el-dropdown>
            <el-button link type="primary">
              操作
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleOprate(1, row)">查询话费（短信）</el-dropdown-item>
                <el-dropdown-item @click="handleOprate(2, row)">断线重启设备</el-dropdown-item>
                <!-- <el-dropdown-item @click="handleOprate(3, row)">标记频繁</el-dropdown-item> -->
                <el-dropdown-item @click="handleOprate(4, row)">Q绑查询</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template #operation="{ row }">
          <el-button type="primary" link @click="addOrUpdateHandle(row.id)">编辑</el-button>
          <el-button type="danger" link :disabled="row.bindShopTotal > 0" @click="state.deleteHandle(row.id)">{{ $t("delete") }}</el-button>
        </template>
        <template #footer>
          <div class="flx-align-center" style="font-weight: 400; font-size: 16px; color: #86909c; gap: 20px; margin-left: -16px">
            <span style="font-weight: bold; color: #1d2129">余额</span>
            <span>手机号数量={{ state.dataList ? state.dataList.length : 0 }}</span>
            <span>合计={{ getSummaries() }}</span>
          </div>
        </template>
      </ny-table>
    </div>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importShow" title="导入文件" class="ba-upload-preview" @close="closeMoneyPreview" width="35%">
      <div>
        <p style="margin: 0 0 12px 0">
          为了保证数据导入顺利，请先
          <el-button v-blur @click="getExportMode()" plain style="margin: 0; padding: 0; color: var(--el-color-primary); border: none"> 下载导入模板 </el-button>
          ，并按照规范示例导入数据
        </p>
        <el-upload ref="uploadRefs" drag :limit="1" :auto-upload="false" action="" accept=".xlsx, .xls" :on-exceed="exceedFile" :on-error="handleError" :on-success="handleSuccess" :before-upload="beforeUPload" :show-file-list="true" v-model:file-list="fileList" class="uploadRefs">
          <template #default>
            <Icon name="iconfont icon-a236" color="#ccc" size="45" />
            <div class="el-upload__text" style="margin-top: 15px">将文件拖到此处，或<em> 点击上传</em></div>
          </template>
          <template #file="{ file }">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 15px">
              <div style="height: 36px; display: flex">
                <Icon name="iconfont icon-excel" color="green" size="36" />
                <span style="margin-left: 15px; line-height: 36px">{{ file.name }}</span>
              </div>
              <Icon color="#666" class="nav-menu-icon" name="el-icon-Close" size="18" @click="onElRemove(file)" />
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            style="margin-right: 8px"
            :loading="uploadExcelLoading"
            @click="
              importShow = false;
              fileList = [];
            "
            >取消</el-button
          >
          <el-button type="primary" :loading="uploadExcelLoading" @click="uploadExcel">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 设置 -->
    <numberInfoSetUp ref="numberInfoSetUpRef" @refresh="getThreshold"></numberInfoSetUp>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <!-- 高级筛选 -->
    <filterCom ref="filterRef" @paramsData="getParamsInfo"></filterCom>
    <!-- 手机号绑定商品数量 -->
    <phoneNumberShop ref="phoneNumberShopRef"></phoneNumberShop>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { IObject } from "@/types/interface";
import AddOrUpdate from "./add-or-update-numerInfo.vue";
import filterCom from "./filter.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { fileExport } from "@/utils/utils";
import { formatDate } from "@/utils/method";
import { Edit, Search } from "@element-plus/icons-vue";
import { color } from "echarts";
import phoneNumberShop from "./phoneNumber-shop.vue";
import numberInfoSetUp from "./numberInfo-setUp.vue";

const view = reactive({
  getDataListURL: "/mobile/mobileCard/page",
  getDataListIsPage: true,
  deleteURL: "/mobile/mobileCard",
  deleteIsBatch: true,
  dataForm: {
    info: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
// 表格配置项
const columns = reactive([
  {
    type: "selection",
    width: 50
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 140
  },
  {
    prop: "cardPosition",
    label: "卡位置",
    minWidth: 136
  },
  {
    prop: "bindShopTotal",
    label: "绑定商品数量",
    minWidth: 140
  },
  {
    prop: "bindStatusName",
    label: "绑定状态",
    minWidth: 100
  },
  {
    prop: "bindQq",
    label: "已绑QQ",
    minWidth: 100
  },
  {
    prop: "bindMaxTime",
    label: "绑定QQ数",
    minWidth: 150
  },
  {
    prop: "balance",
    label: "余额(元)",
    minWidth: 80
  },
  {
    prop: "operator",
    label: "运营商",
    minWidth: 100
  },
  {
    prop: "frequently",
    label: "是否频繁",
    minWidth: 120
  },
  {
    prop: "deptName",
    label: "部门",
    minWidth: 80
  },
  {
    prop: "createUser",
    label: "办卡人",
    minWidth: 100
  },
  {
    prop: "deviceStatus",
    label: "设备状态",
    minWidth: 100
  },
  {
    prop: "onlineStatus",
    label: "卡状态",
    minWidth: 100
  },
  {
    prop: "abnormalRemark",
    label: "异常备注",
    minWidth: 100
  },
  // {
  //   prop: "frequently",
  //   label: "是否频繁",
  //   minWidth: 120
  // },
  // {
  //   prop: "frequentlyTime",
  //   label: "频繁时间",
  //   minWidth: 160
  // },
  {
    prop: "monthlyRent",
    label: "月租(元)",
    minWidth: 80
  },
  // {
  //   prop: "rent",
  //   label: "租用",
  //   minWidth: 80
  // },
  // {
  //   prop: "mainPhone",
  //   label: "主卡",
  //   minWidth: 120
  // },
  {
    prop: "iccid",
    label: "ICCID",
    minWidth: 180
  },
  {
    prop: "createDate",
    label: "创建时间",
    minWidth: 160
  },
  {
    prop: "remark",
    label: "命令",
    fixed: "right",
    minWidth: 80
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    minWidth: 120
  }
]);

const loading = ref(false);
const handleOprate = (index: any, row?: any) => {
  loading.value = true;
  if (index == 1) {
    // 查询话费（短信）
    let phones: any = [];
    if (row) {
      phones = [row.phone];
    } else {
      phones = state.dataListSelections?.map((item: any) => item.phone);
    }

    baseService
      .post("/mobile/mobileDevice/queryPhoneBill", phones)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else if (index == 2) {
    // 短线重启
    let ids: any = [];
    if (row) {
      ids = [row.phone];
    } else {
      ids = state.dataListSelections?.map((item: any) => item.phone);
    }
    console.log(state.dataListSelections, "====== state.dataListSelections =====");

    baseService
      .post("/mobile/mobileDevice/restartByPhones", ids)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else if (index == 3) {
    if (CoolingDays.value == "0") {
      ElMessage.warning("请先设置绑定冷却天数");
      loading.value = false;
      return;
    }
    // 标记频繁
    let ids: any = [];
    if (row) {
      ids = [row.phone];
    } else {
      ids = state.dataListSelections?.map((item: any) => item.phone);
    }
    baseService
      .post("/mobile/mobileCard/frequently", ids)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(res.msg);
        }
      })
      .finally(() => {
        loading.value = false;
        state.getDataList();
      });
  } else if (index == 4) {
    // Q绑查询
    let ids: any = [];
    if (row) {
      ids = [row.phone];
    } else {
      ids = state.dataListSelections?.map((item: any) => item.phone);
    }
    baseService
      .post("/mobile/mobileCard/queryBindQq", ids)
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success("任务执行成功，请稍后刷新查看！");
        }
      })
      .finally(() => {
        loading.value = false;
        state.getDataList();
      });
  }
};

// 重置操作
const getResetting = () => {
  state.dataForm = { info: "" };
  filterRef.value.reset();
  state.page = 1;
  state.getDataList();
};

const getParamsInfo = (params: any) => {
  state.dataForm = { ...state.dataForm, ...params };
  state.getDataList();
};

// 导入
const importShow = ref(false);
const fileList = ref([] as IObject);

// 关闭对话框
const closeMoneyPreview = () => {
  importShow.value = false;
};
// -----------------------数据导入
const beforeUPload = (file: any) => {
  const isExcel = file.type === "application/vnd.ms-excel" || file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel)
    ElMessage({
      message: "上传文件只能是 xls / xlsx 格式！",
      type: "warning"
    });
  return isExcel;
};
// 文件数超出提示
const exceedFile = () => {
  ElMessage.warning("最多只能上传一个文件！");
};
// 上传错误提示
const handleError = () => {
  ElMessage.error("导入数据失败，请您重新上传！");
};
//上传成功提示
const handleSuccess = () => {
  ElMessage.success("导入数据成功！");
};
// 删除文件
const onElRemove = (file: any) => {
  let index = fileList.value.findIndex((ele: any) => ele.name === ele.name);
  fileList.value.splice(index, 1);
};
// 文件上传   确认导入按钮
const uploadExcelLoading = ref(false);
const uploadExcel = async (file: any) => {
  if (!fileList.value.length) {
    return ElMessage.error("请先上传文件！");
  }
  let multipartFile = fileList.value[0].raw;
  const url = `/mobile/mobileCard/import`;
  uploadExcelLoading.value = true;
  await baseService
    .post(url, { file: multipartFile }, { "Content-Type": "multipart/form-data" })
    .then((res: any) => {
      if (res.code == 0) {
        ElMessage.success("导入成功！");
        state.getDataList();
      } else {
        ElMessage.error("导入失败！");
      }
    })
    .finally(() => {
      fileList.value = [];
      importShow.value = false;
      state.getDataList();
      uploadExcelLoading.value = false;
    });
};
// 导出
const exportHandle = () => {
  let params = { ...state.dataForm };
  baseService.get("/mobile/mobileCard/export", { ...params }).then((res) => {
    ElMessage.success("导出成功");
    fileExport(res, `号码信息管理`);
  });
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const filterRef = ref();
const filterHandle = () => {
  nextTick(() => {
    filterRef.value.init(1);
  });
};

const phoneNumberShopRef = ref();
const phoneNumberShopChange = (phone: any) => {
  phoneNumberShopRef.value.init(phone);
};

// 获取话费阈值
const threshold = ref("");
const getThreshold = () => {
  baseService.get("/sys/params/getByCode/mobileThreshold").then((res) => {
    if (res.code == 0) {
      threshold.value = res.data.paramValue;
      getCoolingDays();
      state.getDataList();
    }
  });
};

// 获取冷却天数
const CoolingDays = ref("");
const getCoolingDays = () => {
  baseService.get("/sys/params/getByCode/mobileCoolingDays").then((res) => {
    if (res.code == 0) {
      CoolingDays.value = res.data.paramValue;
    }
  });
};

// 设置
const numberInfoSetUpRef = ref();
const setUpChange = () => {
  numberInfoSetUpRef.value.init();
};

// 合计行计算函数
const getSummaries = () => {
  let total: any = 0;
  state.dataList.map((item: any) => {
    if (item?.balance) total += Number(item?.balance) || 0;
  });
  return total.toFixed(2);
};

// 下载模板
const getExportMode = () => {
  baseService
    .get("/mobile/mobileCard/exportMode")
    .then((res) => {
      if (res) {
        let blob = new Blob([res], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8"
        });
        let a = document.createElement("a");
        let url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = "手机号管理导入模板";
        document.body.appendChild(a);
        a.style.display = "none";
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        ElMessage.error("下载失败！");
      }
    })
    .catch((err) => {
      ElMessage.error("操作失败！");
    });
};

// 批量删除
const deleteChange = () => {
  const isDelete = state.dataListSelections?.filter((item: any) => item.bindShopTotal > 0);
  if (isDelete?.length) {
    ElMessage.warning("已绑定商品的手机号不能删除呦！");
    return;
  }
  state.deleteHandle();
};

// 是否频繁
const updateStatus = (status: any, row: any) => {
  if (!row.id) return;
  if (!status) {
    row.frequently = status;
    row.loading = true;
    baseService
      .put("/mobile/mobileCard", row)
      .then((res) => {
        ElMessage.success({
          message: "操作成功",
          duration: 500
        });
        state.query();
      })
      .finally(() => {
        row.loading = false;
      });
  } else {
    handleOprate(3, row);
  }
};

onMounted(() => {
  getThreshold();
  getCoolingDays();
});
</script>

<style lang="less" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}

.el-button {
  margin-left: 0px;
}
:deep(.TableXScrollSty .el-table .el-scrollbar .el-scrollbar__bar.is-horizontal) {
  left: 503px;
}

.CoolingDays {
  font-weight: 500;
  font-size: 12px;
  color: #ffffff;
  line-height: 22px;
  padding: 1px 8px;
  background: #f53f3f;
  border-radius: 40px;
}
</style>
