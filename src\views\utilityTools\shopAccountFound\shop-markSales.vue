<template>
  <el-drawer v-model="visible" :title="isMark ? '问题商品处理' : '问题商品详情'" :close-on-click-moformuladal="false" :close-on-press-escape="false" size="40%" class="ny-drawer article-add-or-update">
    <div class="article-add-or-update-form cardDescriptions" v-loading="dataLoading">
      <div class="p-title mt-0">商品信息</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="商品编码" :span="2">{{ resData.code || "-" }}</el-descriptions-item>
        <el-descriptions-item label="商品名称" :span="2">
          {{ resData.title || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="商品类型">{{ resData.accountSourceName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏名称">{{ resData.gameName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏账号">{{ resData.gameAccount || "-" }}</el-descriptions-item>
        <el-descriptions-item label="游戏密码" :span="2">
          <div class="flx-justify-between" v-if="resData.gamePassword">
            <span>{{ isShowGamePassword ? resData.gamePassword : "******" }}</span>
            <el-icon class="pointer" @click="isShowGamePassword = !isShowGamePassword">
              <View v-if="!isShowGamePassword" />
              <Hide v-if="isShowGamePassword" />
            </el-icon>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="当前绑定手机号">{{ resData.phone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="回收人">{{ resData.acquisitionName || "-" }}</el-descriptions-item>
        <el-descriptions-item label="售价">
          <el-text type="danger">￥{{ resData.price || "-" }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="回收价">￥{{ resData.acquisitionPrice || "-" }}</el-descriptions-item>
        <el-descriptions-item label="成交价">
          <el-text type="danger">￥{{ resData.transactionPrice || "-" }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="买方手机号">{{ resData.buyerPhone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="卖方手机号" :span="2">{{ resData.phone || "-" }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ resData.createDate ? formatTimeStamp(resData.createDate) : "-" }}</el-descriptions-item>
        <el-descriptions-item label="上架时间" :span="2">{{ resData.listingTime ? formatTimeStamp(resData.listingTime) : "-" }}</el-descriptions-item>
        <el-descriptions-item label="商品状态">{{ resData.saleStatus ? ["未售", "已出售"][+resData.saleStatus] : "-" }}</el-descriptions-item>
        <el-descriptions-item label="问题类型">{{ resData.type ? ["", "被找回", "被转手", "掉绑"][+resData.type] : "-" }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <el-card class="mt-12" v-if="isMark">
      <div class="p-title mt-0">标记售后</div>
      <el-form label-position="top" :model="dataForm" ref="formRef" :rules="rules">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="售后类型" prop="delistingCause">
              <ny-select disabled v-model="dataForm.delistingCause" dict-type="after_sales_type" placeholder="请选择售后类型"></ny-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级原因" prop="subDelistingCause" v-if="dataForm.delistingCause == 'ACCOUNT_RETURN'">
              <ny-select v-model="dataForm.subDelistingCause" :dict-type="resData.saleStatus == '0' ? 'retrieve_type_unsold' : 'retrieve_type_sale'" placeholder="请选择二级原因"></ny-select>
            </el-form-item>
            <el-form-item label="其他原因备注" prop="saleAfterRemark" v-if="dataForm.delistingCause == 'OTHER'">
              <el-input v-model="dataForm.saleAfterRemark" placeholder="请输入其他原因备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="请选择处理人" prop="aftermarketProcessor">
              <ny-select-search v-model="dataForm.aftermarketProcessor" labelKey="realName" valueKey="id" url="/sys/user/page" :param="{ limit: 9999 }" placeholder="请选择处理人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="售后过期时间" prop="expirationTime">
              <el-date-picker popper-class="date_picke" :disabled-date="disabledDate" v-model="dataForm.expirationTime" type="datetime" placeholder="请选择售后过期时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题截图" prop="issueImg">
              <ny-upload v-model:imageUrl="dataForm.issueImg" :limit="1" :fileSize="2" accept="image/*"></ny-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template #footer v-if="isMark">
      <el-button :loading="btnLoading" @click="visible = false">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, defineExpose, defineEmits } from "vue";
import { View, Hide } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import { formatTimeStamp } from "@/utils/method";
import useView from "@/hooks/useView";

const emit = defineEmits(["refresh"]);

// 标记售后
const dataForm = ref(<any>{
  aftermarketProcessor: "",
  issueImg: ""
});
const visible = ref(false);

const isMark = ref(false);
const orderId = ref("");
const init = (data: any, isdetail: boolean) => {
  isMark.value = !isdetail;
  visible.value = true;
  baseService.get("/shop/shop/" + data.shopId).then((res) => {
    if (res.data) {
      resData.value = res.data;
      resData.value.type = data.type;
      orderId.value = res.data.id;
      dataForm.value = res.data;
      dataForm.value.issueImg = res.data.afterSalesPictures;
      if (isMark.value) {
        dataForm.value.delistingCause = "ACCOUNT_RETURN";
      }
    }
  });
};
const disabledDate = (time: any) => {
  // 禁用今天之前的日期
  return time.getTime() < new Date().getTime();
};
const resData = ref(<any>{});
const dataLoading = ref(false);
// 是否显示游戏密码
const isShowGamePassword = ref(false);

const rules = reactive({
  delistingCause: [{ required: true, message: "请选择售后类型", trigger: "change" }],
  subDelistingCause: [{ required: true, message: "请选择二级原因", trigger: "change" }],
  aftermarketProcessor: [{ required: true, message: "请选择售后处理人", trigger: "change" }],
  expirationTime: [{ required: true, message: "请选择过期时间", trigger: "change" }],
  issueImg: [{ required: true, message: "请上传问题截图", trigger: "change" }]
});

const formRef = ref();
const btnLoading = ref(false);
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    btnLoading.value = true;
    let data = JSON.parse(JSON.stringify(dataForm.value));
    data.status = 5;
    baseService
      .post("/shop/shop/signAfterSale", data)
      .then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          if (dataForm.value.delistingCause == "ACCOUNT_RETURN") {
            // 账号找回加黑名单
            addBlackData();
          }
          emit("refresh");
          visible.value = false;
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  });
};
const addBlackData = () => {
  baseService.post("/blacklist/blacklist", {
    id: null,
    account: resData.value.gameAccount,
    gameName: resData.value.gameName,
    idCard: "-",
    name: "-",
    phone: resData.value.phone,
    platform: "-",
    remarks: resultoptions.value[+dataForm.value.subDelistingCause - 1].label
  });
};
const resultoptions = ref([
  {
    label: "未售未放款被找回",
    value: "1"
  },
  {
    label: "未售已放款被找回",
    value: "2"
  },
  {
    label: "已售未放款被找回",
    value: "3"
  },
  {
    label: "已售已放款被找回",
    value: "4"
  },
  {
    label: "未售未放款疑似被找回",
    value: "5"
  },
  {
    label: "未售已放款疑似被找回",
    value: "6"
  },
  {
    label: "已售未放款疑似被找回",
    value: "7"
  },
  {
    label: "已售已放款疑似被找回",
    value: "8"
  }
]);
defineExpose({
  init
});
</script>
<style lang="scss">
.date_picke {
  .el-picker-panel__footer {
    display: none !important;
  }
}
</style>