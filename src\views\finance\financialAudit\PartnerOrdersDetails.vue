<template>
  <template v-if="props.title == '销售放款'">
    <div class="basicInfoSty cardDescriptions">
      <div class="titleSty">申请信息</div>
      <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label><div>游戏名称</div> </template>
          {{ props.orderInfo.gameName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>出售渠道</div> </template>
          {{ props.orderInfo.channelName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>商品编码</div> </template>
          {{ props.orderInfo.shopCode }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>游戏区服</div> </template>
          {{ props.orderInfo.serverName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>订单号</div> </template>
          {{ props.orderInfo.id }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>申请类型</div> </template>
          合作商销售订单
        </el-descriptions-item>
        <el-descriptions-item :span="2" label-class-name="title">
          <template #label><div>账户类型</div> </template>
          {{ accountTypeList.find((item:any)=> item.value == props.orderInfo.paymentAccountType)?.name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>账户名称</div> </template>
          {{ props.orderInfo.paymentAccountName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>收款账号</div> </template>
          {{ props.orderInfo.paymentAccount }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>申请金额(元)</div> </template>
          {{ props.orderInfo.paymentMoney }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>申请时间</div> </template>
          <span>{{ formatTimeStamp(Number(orderInfo.createDate)) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>申请人</div> </template>
          {{ props.orderInfo.creatorName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>成交价(元)</div> </template>
          {{ props.orderInfo.dealAmount }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>备注</div> </template>
          {{ props.orderInfo.paymentRemark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </template>
  <template v-else>
    <div class="basicInfoSty cardDescriptions">
      <div class="titleSty">商品信息</div>
      <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label><div>游戏名称</div> </template>
          {{ props.orderInfo.gameName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>出售渠道</div> </template>
          {{ props.orderInfo.channelName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>商品编码</div> </template>
          {{ props.orderInfo.shopCode }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>游戏区服</div> </template>
          {{ props.orderInfo.serverName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>出售人</div> </template>
          {{ props.orderInfo.saleUserName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>成交价(元)</div> </template>
          {{ props.orderInfo.dealAmount }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>订单来源</div> </template>
          合作商销售订单
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="basicInfoSty cardDescriptions">
      <div class="titleSty">收款信息</div>
      <el-descriptions class="tipinfo" title="" :column="2" size="default" border>
        <el-descriptions-item label-class-name="title">
          <template #label><div>三方订单编号</div> </template>
          {{ props.orderInfo.thirdPartyOrderCode }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>应收金额(元)</div> </template>
          {{ props.orderInfo.receivableMoney }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>收款账户</div> </template>
          {{ props.orderInfo.account }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title" v-if="props.orderInfo.accountType == '1'">
          <template #label><div>支付宝订单号</div> </template>
          {{ props.orderInfo.alipayNo }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="title">
          <template #label><div>付款凭证</div> </template>
          <el-image style="height: 4.25rem; width: 4.25rem" :src="props.orderInfo.payCredentials" :preview-src-list="[props.orderInfo.payCredentials]" preview-teleported fit="cover" />
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin-top: 12px; display: flex; justify-content: flex-end" v-if="props.orderInfo.autoReconciliationStatus">
        <el-tag :type="props.orderInfo.autoReconciliationStatus == 1 ? 'success' : 'danger'" size="large">
          <div style="display: flex; align-items: center; gap: 9px">
            <el-icon v-if="props.orderInfo.autoReconciliationStatus == 1"><CircleCheckFilled /></el-icon>
            <el-icon v-else><CircleCloseFilled /></el-icon>
            <span style="font-weight: 500; font-size: 14px">{{ props.orderInfo.autoReconciliationStatus == 1 ? "对账成功" : "对账异常" }}</span>
          </div>
        </el-tag>
      </div>
    </div>
  </template>
</template>

<script lang="ts" setup>
import baseService from "@/service/baseService";
import { ref, reactive, onMounted } from "vue";
import { formatTimeStamp, camelToUnderscore } from "@/utils/method";

interface Props {
  orderInfo: any;
  title: any;
}
const props = withDefaults(defineProps<Props>(), {
  orderInfo: "",
  title: "",
});
const accountTypeList = ref([
  { value: 1, name: "支付宝转账" },
  { value: 2, name: "微信转账" },
  { value: 3, name: "银行卡转账" }
]);
</script>

<style lang="less" scoped>
.basicInfoSty {
  margin-bottom: 12px;
}
</style>
