<template>
    <div class="mod-tenant__role">
        <!-- <el-card shadow="never" class="rr-view-ctx-card"> -->
            <ny-table
                :state="state"
                :columns="columns"
                routePath="/tenant/role"
                @pageSizeChange="state.pageSizeChangeHandle"
                @pageCurrentChange="state.pageCurrentChangeHandle"
                @selectionChange="state.dataListSelectionChangeHandle"
            >
                <template #header>
                    <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
                    <el-button type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
                </template>
                <template #header-right>
                    <el-input v-model="state.dataForm.name" :placeholder="$t('role.name')" clearable></el-input>
                    <el-button type="primary" @click="state.getDataList()">{{ $t("query") }}</el-button>
                    <el-button @click="getResetting">{{ $t("resetting") }}</el-button>
                </template>

                <!-- 操作 -->
                <template #operation="scope">
                    <el-button type="primary" text bg @click="addOrUpdateHandle(scope.row.id)">{{ $t("update")}}</el-button>
                    <el-button type="danger" text bg @click="state.deleteHandle(scope.row.id)">{{ $t("delete")}}</el-button>
                </template>

            </ny-table>

            <!-- <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="state.limit"
                :total="state.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="state.pageSizeChangeHandle"
                @current-change="state.pageCurrentChangeHandle"></el-pagination> -->
        <!-- </el-card> -->
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./tenant-role-add-or-update.vue";

const view = reactive({
    getDataListURL: "/sys/tenant/role/page",
    getDataListIsPage: true,
    deleteURL: "/sys/tenant/role",
    deleteIsBatch: true,
    dataForm: {
        name: ""
    }
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
};

const state = reactive({ ...useView(view), ...toRefs(view) });

// 表格配置项
const columns = reactive([
    {
        type: "selection",
        width: 50
    },
    {
        prop: "name",
        label: "名称",
        minWidth: 150
    },
    {
        prop: "remark",
        label: "备注",
        minWidth: 150
    },
    {
        prop: "createDate",
        label: "创建时间",
        minWidth: 160,
        sortable: true
    },
    {
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: 140
    }
])

// 重置操作
const getResetting = () => {
    view.dataForm.name = "";
    state.getDataList();
}
</script>
