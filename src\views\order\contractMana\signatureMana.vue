<template>
  <el-drawer v-model="visible" :footer="null" :title="'账户管理'" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="768px" class="">
    <ny-table :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header-custom>
        <div class="mb-12">
          <el-button type="primary" @click="addOrEditHandle()">添加</el-button>
        </div>
      </template>
      <!-- 类型 -->
      <template #type="{ row }">
        <span>{{ getTypeText(row.type) }}</span>
      </template>
      <template #file="{ row }">
        <el-avatar v-if="row.file" shape="square" :src="row.file" />
        <span v-else>-</span>
      </template>
      <!-- 操作 -->
      <template #operation="{ row }">
        <el-link type="primary" @click="addOrEditHandle(row)">编辑</el-link>
        <el-link type="danger" @click="state.deleteHandle(row.id)" style="margin-left: 12px">删除</el-link>
      </template>
    </ny-table>
    <addSignture @refreshDataList="state.getDataList" ref="addSigntureRef"></addSignture>
  </el-drawer>
</template>
  
  <script lang="ts" setup>
import { ref, defineExpose, defineEmits, reactive, toRefs, nextTick } from "vue";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import addSignture from "./add-signature.vue";
import useView from "@/hooks/useView";

const visible = ref(false);
const addSigntureRef = ref();
const typeList = [
  {
    label: "盖章",
    value: "1"
  },
  {
    label: "签名",
    value: "2"
  }
];
const view = reactive({
  getDataListURL: "/wallet/tenantaccount/page",
  getDataListIsPage: true,
  deleteURL: "/wallet/tenantaccount",
  deleteIsBatch: true
});
const state = reactive({ ...useView(view), ...toRefs(view) });

// 编辑和保存操作
const addOrEditHandle = async (form?: any) => {
  await nextTick();
  addSigntureRef.value.init(form?.id);
};

// 表格配置项
const columns = reactive([
  {
    prop: "type",
    label: "签章名称",
    minWidth: 170
  },
  {
    prop: "name",
    label: "签章类型",
    minWidth: 138
  },
  {
    prop: "file",
    label: "附件",
    width: 138
  },
  {
    prop: "account",
    label: "备注",
    width: 182
  },
  {
    prop: "operation",
    label: "操作",
    width: 99
  }
]);
const getTypeText = (type?: any) => {
  if (type) {
    let obj = typeList.find((ele) => ele.value == type);
    return obj?.label;
  } else {
    return "";
  }
};
const init = (id?: number) => {
  visible.value = true;
  state.getDataList();
};
const btnLoading = ref(false);

defineExpose({
  init
});
</script>
  
  <style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
</style>
  