<template>
  <el-drawer v-model="visible" :footer="null" :title="'账户管理'" :close-on-click-moformuladal="false" :close-on-press-escape="false"  size="934px" class="">
    <ny-table :state="state" :columns="columns" :showColSetting="false" @pageSizeChange="state.pageSizeChangeHandle" @pageCurrentChange="state.pageCurrentChangeHandle" @selectionChange="state.dataListSelectionChangeHandle">
      <template #header-custom>
        <div class="mb-12">
          <el-button type="primary" @click="addHandle">添加</el-button>
        </div>
      </template>
      <!-- 账户类型 -->
      <template #type="{ row }">
        <span v-if="!row.isOperate">{{ getTypeText(row.type) }}</span>
        <el-select v-else v-model="row.type" filterable clearable placeholder="请输入账户类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </template>
      <!-- 账户名称 -->
      <template #name="{ row }">
        <span v-if="!row.isOperate">{{ row.name }}</span>
        <el-input v-else v-model="row.name" placeholder="请输入账户名称" />
      </template>
      <!-- 账号 -->
      <template #account="{ row }">
        <span v-if="!row.isOperate">{{ row.account }}</span>
        <el-input v-else v-model="row.account" placeholder="请输入账号" />
      </template>
      <template #isDefault="{ row }">
        <el-switch active-value="1" inactive-value="0" @change="changeDefault(row)" v-if="!row.isOperate" :disabled="row.isOperate" v-model="row.isDefault" />
      </template>
      <!-- 操作 -->
      <template #operation="{ row, $index }">
        <el-link type="primary" @click="handleEdit(row, $index)">{{ row.isOperate ? "保存" : "编辑" }}</el-link>
        <el-link type="danger" @click="row.id ? state.deleteHandle(row.id) : delhandle($index)" style="margin-left: 12px">删除</el-link>
      </template>
    </ny-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { IObject } from "@/types/interface";
import { ref, defineExpose, defineEmits, reactive, toRefs } from "vue";
import { useAppStore } from "@/store";
import { ElMessage } from "element-plus";
import baseService from "@/service/baseService";
import useView from "@/hooks/useView";
import { Search } from "@element-plus/icons-vue";
const store =useAppStore()
const visible = ref(false);
const typeList = [
  {
    label: "支付宝",
    value: "1"
  },
  {
    label: "微信",
    value: "2"
  },
  {
    label: "银行卡",
    value: "3"
  }
];
const view = reactive({
  getDataListURL: "/wallet/tenantaccount/page",
  getDataListIsPage: true,
  deleteURL: "/wallet/tenantaccount",
  deleteIsBatch: true,
  dataForm: {
    tenantCode: undefined
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
// 单行数据
const rowForm = reactive({
  data: {
    id: undefined,
    type: undefined,
    name: undefined,
    account: undefined,
    isDefault: undefined
  },
  index: -1
});
// 编辑和保存操作
const handleEdit = (form: any, index: any) => {
  if (rowForm.index != -1 && form.isOperate) {
    // 保存操作
    submitForm(form);
  } else {
    rowForm.index = index;
    state.dataList[index].isOperate = true;
  }
};
const addHandle = () => {
  state.dataList.push({
    type: undefined,
    name: undefined,
    account: undefined,
    isDefault: '0',
    isOperate: true
  });
  rowForm.index = state.dataList.length;
};
const delhandle = (index?: any) => {
  state.dataList?.splice(index, 1);
  rowForm.index = -1;
};
const changeDefault = (row: any) => {
  if (!row.id) return;
  let url = row.isDefault === "1" ? "/wallet/tenantaccount/updateDefault/" : "/wallet/tenantaccount/updateUnDefault/";
  baseService.put(url + row.id).then((res) => {
    ElMessage.success(res.msg);
    state.getDataList();
    rowForm.index = -1;
  }).catch(err=> {
    state.getDataList();
    rowForm.index = -1;
  });
};
// 表格配置项
const columns = reactive([
  {
    prop: "type",
    label: "账户类型",
    minWidth: 170
  },
  {
    prop: "name",
    label: "账户名称",
    minWidth: 140
  },
  {
    prop: "account",
    label: "账号",
    width: 280
  },
  {
    prop: "isDefault",
    label: "设为默认",
    width: 158
  },
  {
    prop: "operation",
    label: "操作",
    width: 100
  }
]);
const getTypeText = (type?: any) => {
  if (type) {
    let obj = typeList.find((ele) => ele.value == type);
    return obj.label;
  } else {
    return "";
  }
};
const init = (id?: number) => {
  visible.value = true;
  state.dataForm.tenantCode = store.state.user.tenantCode
  state.getDataList();
};
const btnLoading = ref(false);
const submitForm = (form: any) => {
  baseService[form.id ? "put" : "post"]("/wallet/tenantaccount", form)
    .then((res) => {
      if (res.code === 0) {
        state.getDataList();
        rowForm.index = -1;
        ElMessage.success(res.msg);
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

defineExpose({
  init
});
</script>

<style lang="scss">
.article-add-or-update {
  .input-w-360 {
    width: 360px;
  }
}
</style>
