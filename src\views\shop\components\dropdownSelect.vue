<template>
    <el-dropdown>
        <div class="ds_button">
            <div class="ds_wap">
                <div class="ds_label">111</div>
                <el-icon color="#A8ABB2"><ArrowDown /></el-icon>
            </div>
        </div>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item>Action 1</el-dropdown-item>
                <el-dropdown-item>Action 2</el-dropdown-item>
                <el-dropdown-item>Action 3</el-dropdown-item>
                <el-dropdown-item disabled>Action 4</el-dropdown-item>
                <el-dropdown-item divided>Action 5</el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    
</template>

<script lang='ts' setup>
import { ref,reactive } from 'vue';
const emit = defineEmits(["change"]);

defineProps({
    modelValue: {
        type: String,
        required: true,
    },
})

</script>

<style lang='less' scoped>
.ds_button{
    width: 100%;
    height: 32px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 8px 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .ds_label{
        color: #303133;
        font-size: 13px;
        line-height: 22px;
        font-weight: 400;
    }
}
</style>